<app-loading *ngIf="isLoading" text="正在加载日程..."></app-loading>
<div class="exam-schedule" >
  <h2 class="title">日程</h2>
  @for (dateSchedule of groupedSchedules(); track dateSchedule.date) {
    <div class="date-group">
      <h2 class="date">{{ dateSchedule.date }}</h2>
    @for (schedule of dateSchedule.schedules; track schedule.id) {
      <div class="schedule-card {{schedule.status}}" (click)="goSignIn(schedule.id)">
        <div class="schedule-header">
          <span class="subject-name">{{ schedule.subjects.join(',') }}</span>
        <span class="status {{ schedule.status }}">{{ schedule.status | statusTrans: 'scheduleStatus' }}</span>
      </div>
      <div class="schedule-info">
        <div class="time-info">
          <span nz-icon nzType="clock-circle" nzTheme="outline"></span>
          <span>{{ schedule.start | date: 'HH:mm' }}-{{ schedule.end | date: 'HH:mm' }}</span>
        </div>
        <div class="attendance-rate">
          <span nz-icon nzType="pie-chart" nzTheme="outline"></span>
          <span><span class="label">到考率</span>：{{ schedule.attendanceRate }}%</span>
        </div>
        <div class="attendance-count">
          <span nz-icon nzType="user" nzTheme="outline"></span>
          <span><span class="label">已签到/总人数</span>：{{ schedule.attended }}/{{ schedule.total }}</span>
          </div>
        </div>
      </div>
    }
    </div>
  }
  <nz-empty nzNotFoundImage="simple" *ngIf="isEmpty"></nz-empty>
</div>