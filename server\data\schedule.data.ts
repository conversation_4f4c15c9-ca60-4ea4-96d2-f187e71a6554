import { DbConnection } from './types';
import globalWriteQueue from '../utils/writeQueue';

export interface IScheduleData {
  id: string;
  project_id: string;
  name: string;
  start: string;
  end: string;
  test: 0 | 1;
  form_published: 0 | 1;
  password_published: 0 | 1;
  // version: number;
  created_at: number;
}

export interface IScheduleDataAccess {
  getAllSchedules(): Promise<IScheduleData[]>;
  getSchedulesByProjectId(project_id: string): Promise<IScheduleData[]>;
  update(
    whereFields: { id: string },
    schedule: Partial<IScheduleData>,
  ): Promise<void>;
  get(id: string): Promise<IScheduleData | null>;
  create(schedule: Partial<IScheduleData>): Promise<void>;
  delete(schedule_id: string): Promise<void>;
}

export class ScheduleDataAccess implements IScheduleDataAccess {
  db: DbConnection;
  constructor(db: DbConnection) {
    this.db = db;
  }

  async getAllSchedules(): Promise<IScheduleData[]> {
    const schedules = await this.db.schedule.findAll({ raw: true });
    return schedules;
  }

  async getSchedulesByProjectId(project_id: string): Promise<IScheduleData[]> {
    const schedules = await this.db.schedule.findAll({
      where: { project_id },
      raw: true,
    });
    return schedules;
  }

  async get(id: string): Promise<IScheduleData | null> {
    return this.db.schedule.findByPk(id, { raw: true });
  }

  async create(schedule: Partial<IScheduleData>): Promise<void> {
    await globalWriteQueue.enqueue(() =>
      this.db.schedule.create(schedule as IScheduleData),
    );
  }

  async update(
    whereFields: { id: string },
    schedule: Partial<IScheduleData>,
  ): Promise<void> {
    await globalWriteQueue.enqueue(() =>
      this.db.schedule.update(schedule, {
        where: whereFields,
      }),
    );
  }

  async delete(schedule_id: string): Promise<void> {
    await globalWriteQueue.enqueue(() =>
      this.db.schedule.destroy({ where: { id: schedule_id } }),
    );
  }
}
