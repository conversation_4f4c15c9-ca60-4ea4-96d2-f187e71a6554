{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@angular+common@19.2.9_@angular+core@19.2.9_rxjs@7.8.1_zone.js@0.15.0__rxjs@7.8.1/node_modules/@angular/common/fesm2022/dom_tokens-rA0ACyx7.mjs", "../../../../../../node_modules/.pnpm/@angular+common@19.2.9_@angular+core@19.2.9_rxjs@7.8.1_zone.js@0.15.0__rxjs@7.8.1/node_modules/@angular/common/fesm2022/xhr-BfNfxNDv.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { InjectionToken } from '@angular/core';\n\n/**\n * A DI Token representing the main rendering context.\n * In a browser and SSR this is the DOM Document.\n * When using SSR, that document is created by [<PERSON><PERSON>](https://github.com/angular/domino).\n *\n * @publicApi\n */\nconst DOCUMENT = new InjectionToken(ngDevMode ? 'DocumentToken' : '');\nexport { DOCUMENT };\n", "/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nfunction parseCookieValue(cookieStr, name) {\n  name = encodeURIComponent(name);\n  for (const cookie of cookieStr.split(';')) {\n    const eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue] = eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n  return null;\n}\nconst PLATFORM_BROWSER_ID = 'browser';\nconst PLATFORM_SERVER_ID = 'server';\n/**\n * Returns whether a platform id represents a browser platform.\n * @publicApi\n */\nfunction isPlatformBrowser(platformId) {\n  return platformId === PLATFORM_BROWSER_ID;\n}\n/**\n * Returns whether a platform id represents a server platform.\n * @publicApi\n */\nfunction isPlatformServer(platformId) {\n  return platformId === PLATFORM_SERVER_ID;\n}\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n */\nclass XhrFactory {}\nexport { PLATFORM_BROWSER_ID, PLATFORM_SERVER_ID, XhrFactory, isPlatformBrowser, isPlatformServer, parseCookieValue };\n"], "mappings": ";;;;;AAeA,IAAM,WAAW,IAAI,eAAe,YAAY,kBAAkB,EAAE;;;ACTpE,SAAS,iBAAiB,WAAW,MAAM;AACzC,SAAO,mBAAmB,IAAI;AAC9B,aAAW,UAAU,UAAU,MAAM,GAAG,GAAG;AACzC,UAAM,UAAU,OAAO,QAAQ,GAAG;AAClC,UAAM,CAAC,YAAY,WAAW,IAAI,WAAW,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,MAAM,GAAG,OAAO,GAAG,OAAO,MAAM,UAAU,CAAC,CAAC;AACrH,QAAI,WAAW,KAAK,MAAM,MAAM;AAC9B,aAAO,mBAAmB,WAAW;AAAA,IACvC;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,sBAAsB;AAC5B,IAAM,qBAAqB;AAK3B,SAAS,kBAAkB,YAAY;AACrC,SAAO,eAAe;AACxB;AAKA,SAAS,iBAAiB,YAAY;AACpC,SAAO,eAAe;AACxB;AAOA,IAAM,aAAN,MAAiB;AAAC;", "names": []}