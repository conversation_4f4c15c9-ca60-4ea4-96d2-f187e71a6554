import { AuthEventType } from '@share-types/auth-event.types';
import { AuthEvent, EventDetail } from './event.model';
import serverTime from '@services/serverTime';
import { HMAC256, getRandomInt, randomString } from '@server/utils/utils';
import { CLOUD_SECRET_PASSWORD } from '@share-types/constants';
import { offlineEventQrcode, authEventStatus } from '@services/cloud/cloud.api';
import * as qs from 'querystring';
import { encryptText } from '@server/utils/crypt-aes';

type EventID = string;
const password = CLOUD_SECRET_PASSWORD;

export class AuthEventService {
  eventList = new Map<EventID, AuthEvent>();
  timer: any;

  createAuthEvent(type: AuthEventType, event: Partial<EventDetail>) {
    const auth_event = new AuthEvent(type, event);
    this.eventList.set(auth_event.id, auth_event);
    // this.timedQuery();
    return auth_event;
  }

  getAuthEventById(event_id: string) {
    return this.eventList.get(event_id);
  }

  /** 验证授权码是否正确 */
  authCode(event_id: EventID, code: number) {
    const event = this.eventList.get(event_id);
    if (event && code === event.code) {
      return true;
    }
    return false;
  }

  /** 创建二维码 */
  async getQRCode(event: AuthEvent, center_id: string) {
    event.code = getRandomInt(100000, 999999);
    const offline_data = event.offlineData();
    const { c, code, content, o, p, uuid, h_id, entries } = offline_data;
    const v_id = center_id;
    const ts = serverTime.now();
    const nonce = randomString(6);
    const token = encryptText(`${event.code}`, password);
    const sign = HMAC256(
      password,
      `c=${c}code=${code}content=${content}entries=${entries}h_id=${h_id}nonce=${nonce}o=${o}p=${p}ts=${ts}token=${token}uuid=${uuid}v_id=${v_id}`,
    );
    const qur_str = qs.stringify({
      ...offline_data,
      v_id,
      token,
      ts,
      nonce,
      sign,
    });
    const data_url = await offlineEventQrcode(qur_str);
    return data_url;
  }

  timedQuery() {
    if (!this.timer) {
      this.timer = setInterval(() => {
        void this.checkAuthEvent();
      }, 10000);
    }
  }

  async checkAuthEvent() {
    const auth_events = await getAuthEvent('111');
    for (const event of auth_events) {
      const auth_event = this.eventList.get(event.event_id);
      if (
        auth_event &&
        !auth_event.code &&
        auth_event.confirmed === 'waiting' &&
        event.status !== 'waiting'
      ) {
        auth_event.confirmed = event.status;
        let err;
        if (event.status === 'agreed') {
          try {
            await auth_event.excute();
          } catch (error: any) {
            err = error.message;
          }
        }
        // this.notifyManager(auth_event, err);
      }
    }

    if (this.checkAllEventAuth()) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  /** 检查事件是否有在等待状态 删除 超时事件 */
  checkAllEventAuth() {
    for (const [id, event] of this.eventList) {
      if (event.is_timeout) {
        console.log('delete auth event', id, event.type);
        this.eventList.delete(event.id);
        continue;
      }
      if (event.confirmed === 'waiting') {
        return false;
      }
    }
    return true;
  }
}

async function getAuthEvent(center_id: string) {
  const events = await authEventStatus(center_id).catch((err) => {
    console.error('AuthEvent: get status failed:', err);
  });
  if (Array.isArray(events) && events.length !== 0) {
    return events;
  }
  return [];
}
