import { execFile } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';

function _get7zPath(): string {
  const isWin = process.platform === 'win32';
  const isMac = process.platform === 'darwin';
  const _name = isWin ? '7za.exe' : isMac ? '7za' : '7zz';

  const paths: string[] = [];
  if (process.resourcesPath) {
    paths.push(path.join(process.resourcesPath, _name));
  }
  if (process.execPath) {
    paths.push(path.join(path.dirname(process.execPath), _name));
  }
  if (isWin) {
    paths.push(
      path.join(__dirname, './tools/7z/7za.exe'),
      path.join(process.env.JOY_DEV_SERVER_PATH || '', './tools/7z/7za.exe'),
    );
  } else if (isMac) {
    paths.push(path.join(__dirname, './tools/7z/darwin/7za'));
  }
  for (const p of paths) {
    if (fs.existsSync(p)) {
      return p;
    }
  }
  return '7z';
}

export const get7z = (() => {
  let _7z_path: string = '';
  return () => {
    if (!_7z_path) {
      _7z_path = _get7zPath();
    }
    return _7z_path;
  };
})();

export function unzip(path: string, target: string, pwd: string): Promise<any> {
  console.debug('Unzipping from ' + path + ' to ' + target);

  if (!fs.existsSync(target)) {
    fs.mkdirSync(target);
  }

  return new Promise((resolve, reject) => {
    const args = ['x', path, '-o' + target, '-r', '-y', '-aoa', '-sccUTF-8'];
    if (pwd) {
      args.push('-p' + pwd);
    }

    // 命令文档 https://sevenzip.osdn.jp/chm/cmdline/syntax.htm
    execFile(get7z(), args, (error, stdout, stderr) => {
      if (error) {
        console.error('解压文件失败:', error);
        console.error('解压文件失败:', stderr);
        reject(stderr);
        return;
      }
      resolve(stdout);
    });
  });
}

export function testUnzip(path: string, pwd?: string) {
  console.debug('Testing unzip from ' + path);
  if (!fs.existsSync(path)) {
    return Promise.reject('测试解压文件失败: 文件不存在');
  }

  return new Promise((resolve, reject) => {
    const args = ['t', path, '*', '-r', '-sccUTF-8'];
    if (pwd) {
      args.push('-p' + pwd);
    }

    // 命令文档 https://sevenzip.osdn.jp/chm/cmdline/syntax.htm
    execFile(get7z(), args, (error, stdout, stderr) => {
      if (error) {
        console.error('测试文件失败:', stderr);
        reject(stderr);
        return;
      }
      resolve(stdout);
    });
  });
}
