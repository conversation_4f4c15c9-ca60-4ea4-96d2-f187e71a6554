<div class="title">操作日志</div>
<main class="card">
  <div class="filter">
    <!-- <div class="select">
      <nz-select ngModel="1">
        <nz-option nzValue="1" nzLabel="考试管理"></nz-option>
        <nz-option nzValue="2" nzLabel="考场管理"></nz-option>
        <nz-option nzValue="3" nzLabel="试卷管理"></nz-option>
      </nz-select>
    </div> -->
    <div class="select">
      <nz-select [(ngModel)]="typeSelected" nzPlaceHolder="全部操作">
        <nz-option [nzValue]="null" nzLabel="全部操作"></nz-option>
        @for (type of logTypeArray; track $index) {
          <nz-option [nzValue]="type" [nzLabel]="logTypeMap[type]"></nz-option>
        }
      </nz-select>
    </div>
    <div class="time">
      <nz-range-picker
        nzValue="small"
        [nzShowTime]="false"
        [(ngModel)]="filter_date"
      ></nz-range-picker>
    </div>
    <div class="search-box">
      <nz-input-group [nzSuffix]="suffixIconSearch">
        <input
          type="text"
          nz-input
          placeholder="操作内容"
          [(ngModel)]="search"
        />
      </nz-input-group>
      <ng-template #suffixIconSearch>
        <span nz-icon nzType="search"></span>
      </ng-template>
    </div>
  </div>
  <hr class="divider" />
  <div class="content">
    <nz-table
      #basicTable
      [nzData]="
        logs
          | logFilter : search
          | dateFilter : filter_date
          | typeFilter : typeSelected
      "
      [nzFrontPagination]="false"
      nzSize="small"
    >
      <thead>
        <tr>
          <th nzWidth="30%">操作模块</th>
          <th>操作内容</th>
          <th nzWidth="160px">操作时间</th>
        </tr>
      </thead>
      <tbody>
        @for (log of basicTable.data; track $index) {
        <tr>
          <td>{{ log.module_name }}</td>
          <td>{{ log.content }}</td>
          <td>{{ log.time | customDate : "date" }}</td>
        </tr>
        }
      </tbody>
    </nz-table>
  </div>
</main>
