import * as path from "path";
import * as electron from "electron";
import { app, BrowserWindow, dialog } from "electron";
import * as config from "../config";

// import { t as $t } from "../i18n";

import * as powerSaveBlocker from "./power-save-blocker";
import { log } from "src/logger";

export let win: Electron.BrowserWindow;

export function loadFile(url: string) {
  win.loadFile(url);
}

export function init(): void {
  if (win) {
    return;
  }

  const workAreaSize = electron.screen.getPrimaryDisplay().workAreaSize;
  win = new BrowserWindow({
    width: 1200,
    height: Math.max(workAreaSize.height, 768),
    minWidth: 1024,
    minHeight: 768,
    backgroundColor: "#3c89f3",
    show: false,
    icon: getAppIcon(),
    autoHideMenuBar: true,
    center: true,
    fullscreen: false,
    fullscreenable: true,
    title: '考点服务器',
    webPreferences: {
      webSecurity: false,
      nodeIntegration: true,
      contextIsolation: true,
      allowRunningInsecureContent: true,
      backgroundThrottling: false,
      preload: path.join(__dirname, "preload.js"),
    },
  });

  win.removeMenu();
  win.once("ready-to-show", () => {
    if (win.removeMenu) {
      win.removeMenu();
    }

    win.on("close", async (e) => {
      e.preventDefault(); // Prevents the window from closing
      const r = await dialog.showMessageBox(win, {
        type: "warning",
        buttons: ["确定", "取消"],
        title: "确认",
        defaultId: 1,
        message: "确认退出？",
        cancelId: 1,
      });
      if (r.response === 0) {
        // powerSaveBlocker.disable();
        app.exit(0);
      }
    });
    // powerSaveBlocker.enable();
  });
  
  win.webContents.on("console-message", (_event, level, message, line, sourceId) => {
    switch (level) {
      case 0:
        console.debug(message, "(" + path.basename(sourceId) + ":" + line + ")");
        break;
      case 1:
        log.info(message);
        break;
      case 2: // warning
        if (message.startsWith("This site does not have a valid") || message.startsWith("The vm module")) {
          // no logging this message
          return;
        }
        log.warn(message);
        break;
      case 3:
        log.error(message, "(" + path.basename(sourceId) + ":" + line + ")");
        break;
      default:
        log.info(message);
    }
  });

  win.webContents.setBackgroundThrottling(false);


}

function getAppIcon(): string {
  return process.platform === "win32" ? config.APP_ICON + ".ico" : config.APP_ICON + ".png";
}
