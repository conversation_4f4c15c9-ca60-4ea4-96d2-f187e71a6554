#!/usr/bin/env node

/**
 * Human.js Models Download Script
 * 下载 Human.js 人脸检测模型到本地 assets 目录以支持离线使用
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// 模型文件配置
const models = [
  {
    name: 'BlazeFace Model',
    url: 'https://cdn.jsdelivr.net/npm/@vladmandic/human/models/blazeface.json',
    filename: 'blazeface.json'
  },
  {
    name: 'BlazeFace Weights',
    url: 'https://cdn.jsdelivr.net/npm/@vladmandic/human/models/blazeface.bin',
    filename: 'blazeface.bin'
  }
];

// 目标目录
const targetDir = path.join(__dirname, '..', 'src', 'assets', 'human-models');

/**
 * 确保目录存在
 */
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`Created directory: ${dirPath}`);
  }
}

/**
 * 下载文件
 */
function downloadFile(url, filepath) {
  return new Promise((resolve, reject) => {
    console.log(`Downloading: ${path.basename(filepath)}...`);
    
    const file = fs.createWriteStream(filepath);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      const totalSize = parseInt(response.headers['content-length'], 10);
      let downloadedSize = 0;
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        if (totalSize) {
          const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
          process.stdout.write(`\r  Progress: ${progress}%`);
        }
      });
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`\n  ✓ Downloaded: ${path.basename(filepath)} (${(downloadedSize / 1024 / 1024).toFixed(2)} MB)`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // 删除不完整的文件
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

/**
 * 检查文件是否已存在
 */
function fileExists(filepath) {
  return fs.existsSync(filepath);
}

/**
 * 获取文件大小
 */
function getFileSize(filepath) {
  const stats = fs.statSync(filepath);
  return (stats.size / 1024 / 1024).toFixed(2);
}

/**
 * 主函数
 */
async function main() {
  console.log('🤖 Human.js Models Download Script');
  console.log('=====================================\n');
  
  // 确保目标目录存在
  ensureDirectoryExists(targetDir);
  
  let totalDownloaded = 0;
  let totalSkipped = 0;
  
  for (const model of models) {
    const filepath = path.join(targetDir, model.filename);
    
    if (fileExists(filepath)) {
      const size = getFileSize(filepath);
      console.log(`⏭️  Skipped: ${model.filename} (already exists, ${size} MB)`);
      totalSkipped++;
      continue;
    }
    
    try {
      await downloadFile(model.url, filepath);
      totalDownloaded++;
    } catch (error) {
      console.error(`\n❌ Failed to download ${model.filename}:`, error.message);
      process.exit(1);
    }
  }
  
  console.log('\n=====================================');
  console.log('📊 Download Summary:');
  console.log(`   Downloaded: ${totalDownloaded} files`);
  console.log(`   Skipped: ${totalSkipped} files`);
  console.log(`   Target directory: ${targetDir}`);
  
  if (totalDownloaded > 0) {
    console.log('\n✅ Models downloaded successfully!');
    console.log('   Human.js engine now supports offline mode.');
  } else {
    console.log('\n✅ All models are already available.');
  }
  
  // 生成模型清单文件
  const manifest = {
    version: '1.0.0',
    generatedAt: new Date().toISOString(),
    models: models.map(model => ({
      name: model.name,
      filename: model.filename,
      path: `/assets/human-models/${model.filename}`,
      exists: fileExists(path.join(targetDir, model.filename)),
      size: fileExists(path.join(targetDir, model.filename)) ? 
             getFileSize(path.join(targetDir, model.filename)) + ' MB' : 'N/A'
    }))
  };
  
  const manifestPath = path.join(targetDir, 'manifest.json');
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  console.log(`\n📋 Generated manifest: ${manifestPath}`);
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('\n❌ Script failed:', error.message);
    process.exit(1);
  });
}

module.exports = { downloadFile, ensureDirectoryExists };
