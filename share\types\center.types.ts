
export interface SuccessRes<T> {
  status: 'success';
  data: T;
}

export interface ErrorRes<T> {
  status: 'error';
  error: {
    code: number;
    msg: string;
    data: T;
  };
}

export type Res<T = any> = SuccessRes<T> | ErrorRes<T>;

export interface CenterInfo {
  center_name: string;
  center_address: string;
  is_active: number;
  project_id: string;
  project_name: string;
  start: string;
  end: string;
  test: {
    project_id: string;
    name: string;
    start: string;
    end:string;
  }
}

export enum ElogType {
  // 系统操作
  CenterRegister = 0,
  CenterLogout = 1,
  TimeSynced = 2,

  // 考场管理
  RoomRegister = 100,
  RoomDelete = 101,

  // 考试管理
  PackageImport = 200,
  FormImport = 201,
  FormPublish = 202,
  PasswordPublish = 203,
  FormDelete = 204,
  ResultUpload = 205,
  ResultExport = 206,
}

export const logTypeMap: Record<ElogType, string> = {
  [ElogType.CenterRegister]: "注册考点",
  [ElogType.CenterLogout]: "注销考点",
  [ElogType.TimeSynced]: "时间同步",
  [ElogType.RoomRegister]: "注册考场",
  [ElogType.RoomDelete]: "删除考场",
  [ElogType.PackageImport]: "导入数据包",
  [ElogType.FormImport]: "导入试卷",
  [ElogType.FormPublish]: "发布试卷",
  [ElogType.PasswordPublish]: "发布密码",
  [ElogType.FormDelete]: "删除试卷",
  [ElogType.ResultUpload]: "上传结果",
  [ElogType.ResultExport]: "导出结果",
}

export const logTypeArray = Object.keys(logTypeMap).map(t => +t) as ElogType[];

export enum EntryAnomaly {
  flip_over = 20,
  inactive = 21,
  fast_answer = 22,
  shuffle_answer = 40, // 乱序答题
}

export const anomalyTypeArray = [
  EntryAnomaly.flip_over,
  EntryAnomaly.fast_answer,
  EntryAnomaly.inactive,
  EntryAnomaly.shuffle_answer,
];

export enum CloudErrorCode {
  InvalidServerId = 901, // 无效的server_id
  InvalidCode = 902, // 无效的验证码
}