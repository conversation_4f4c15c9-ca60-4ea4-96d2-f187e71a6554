import path from 'path';
import { Configuration, IgnorePlugin } from 'webpack';
import nodeExternals from 'webpack-node-externals';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import copy from 'copy-webpack-plugin';

const mode =
  (process.env.NODE_ENV as 'development' | 'production') || 'development';

const is_dev = mode === 'development';
const buildTargetDir = path.resolve(__dirname, "..", 'build', "app");

export const base_config: Configuration = {
  output: {
    path: buildTargetDir,
    filename: '[name].js',
  },
  node: {
    __dirname: false,
    __filename: false,
  },
  resolve: {
    extensions: ['.ts', '.js', '.json'],
    plugins: [
      new TsconfigPathsPlugin({
        configFile: path.resolve(__dirname, 'tsconfig.json'),
      }),
    ],
  },
  plugins: [
  ],
  module: {
    noParse: /native-require.ts$/,
    rules: [
      {
        test: /\.ts$/,
        use:  [
          {
            loader: 'ts-loader',
            options: {
              compilerOptions: {
                onlyCompileBundledFiles: true,
                sourceMap: is_dev,
              },
            },
          },
        ],
        exclude: /node_modules/,
      },
    ],
  },
  mode,
  devtool: is_dev ? 'source-map' : false,
  externalsPresets: { node: true },
  externals: [nodeExternals()],
};

