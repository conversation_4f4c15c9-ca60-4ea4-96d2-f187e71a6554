import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  OnDestroy,
  Output,
  type OnInit,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { Router } from '@angular/router';
import { ProjectDataService } from '../core/data';
import { Subscription, filter, fromEvent, throttleTime } from 'rxjs';
import { ModalService } from '../core/services/modal.service';
import { CloudErrorCode } from '@share-types/center.types';

@Component({
    selector: 'app-login-card',
    imports: [
        CommonModule,
        FormsModule,
        NzButtonModule,
        NzInputModule,
        NzIconModule,
    ],
    template: ` <div class="container">
    <div class="title">登录</div>
    <div class="user">
      <nz-input-group>
        <input
          class="server-code"
          type="text"
          nz-input
          autofocus
          [(ngModel)]="server_code"
          placeholder="请输入考点服务器登录码"
        />
      </nz-input-group>
      <!-- <ng-template #prefixTemplateUser
        ><span nz-icon nzType="user"></span
      ></ng-template> -->
    </div>
    <!-- <div class="password">
      <nz-input-group [nzPrefix]="prefixTemplate" [nzSuffix]="suffixTemplate">
        <input
          [type]="passwordVisible ? 'text' : 'password'"
          nz-input
          placeholder="请输入密码"
          [(ngModel)]="password"
        />
      </nz-input-group>
      <ng-template #suffixTemplate>
        <span
          nz-icon
          [nzType]="passwordVisible ? 'eye-invisible' : 'eye'"
          (click)="passwordVisible = !passwordVisible"
        ></span>
      </ng-template>
      <ng-template #prefixTemplate>
        <span nz-icon nzType="lock" nzTheme="outline"></span>
      </ng-template>
    </div> -->
    <button
      nz-button
      nzType="primary"
      nzBlock
      [nzLoading]="button_loading"
      (click)="login()"
    >
      登录
    </button>
  </div>`,
    styles: `
  :host {
    display: block;
    background-color: #fff;
    border-radius: 8px;
  }
  .container {
    padding: 40px;
  }
  .title {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 48px;
  }
  .user {
    margin-bottom: 24px;
  }
  .password {
    margin-bottom: 24px;
  }
  `
})
export class LoginCardComponent implements OnInit, AfterViewInit, OnDestroy {
  server_code = '';
  passwordVisible = false;
  button_loading = false;

  keyEnter$$: Subscription;

  @Output() loginSuccess = new EventEmitter<{
    center_name: string;
    center_address: string;
  }>();
  constructor(
    private router: Router,
    private projectData: ProjectDataService,
    private modalService: ModalService
  ) {}
  ngOnInit(): void {}

  ngAfterViewInit(): void {
    const input = <HTMLInputElement>document.querySelector('input.server-code');
    if (input) {
      input.focus();
    }
    this.keyEnter$$ = fromEvent<KeyboardEvent>(input, 'keyup')
      .pipe(
        filter((event) => event.key === 'Enter' && !!this.server_code),
        throttleTime(1000)
      )
      .subscribe(() => this.login());
  }
  ngOnDestroy(): void {
    this.keyEnter$$.unsubscribe();
  }

  login() {
    this.button_loading = true;
    this.projectData.reqCenterLogin(this.server_code).subscribe((res) => {
      if (res.status === 'success') {
        this.router.navigate(['/login/register']);
      } else {
        if (res.error?.data) {
          const {message, code} = res.error.data as any;
          console.error('Login error', code, message);
          const msg = code === CloudErrorCode.InvalidServerId ? '服务器登录码错误!' : message;
          this.modalService.error('登录失败', msg);
        } else {
          this.modalService.error('登录失败', res.error.msg);
        }
      }
      this.button_loading = false;
    });
  }
}
