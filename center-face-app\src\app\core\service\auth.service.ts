import { Injectable, effect, signal } from '@angular/core';
import { randomString } from '@app/utils/randomString';
import { BehaviorSubject, Observable } from 'rxjs';
import { PwaInstallService } from './pwa-install.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  serverCode = "";
  authToken = "";
  hardwareId = "";
  public serverCodeTimestamp: number | null = null;

  private loginStateChange!: BehaviorSubject<boolean>; 
  public loginStateChange$!: Observable<boolean>;

  constructor(private pwaService: PwaInstallService) {
    this.loginStateChange = new BehaviorSubject<boolean>(this.isLoginInitial());
    this.loginStateChange$ = this.loginStateChange.asObservable();
    
    let authToken = sessionStorage.getItem('authToken');
    
    if (!authToken && this.isPwaMode()) {
      authToken = localStorage.getItem('pwa_authToken');
      if (authToken) {
        sessionStorage.setItem('authToken', authToken);
      }
    }
    
    if (authToken) {
      this.authToken = authToken;
    }
    
    const hardwareId = localStorage.getItem('hardwareId');
    if (hardwareId) {
      this.hardwareId = hardwareId;
    }
    const serverCode = localStorage.getItem('serverCode');
    if (serverCode) {
      this.serverCode = serverCode;
    }
    const serverCodeTimestampString = localStorage.getItem('serverCodeTimestamp');
    if (serverCodeTimestampString) {
      this.serverCodeTimestamp = parseInt(serverCodeTimestampString, 10);
    }
    this.loginStateChange.next(this.isLogin);
  }

  private isPwaMode(): boolean {
    if (!this.pwaService) return false;
    return this.pwaService.isInStandaloneMode;
  }

  private isLoginInitial(): boolean {
    const isPwa = this.pwaService ? this.isPwaMode() : false;
    
    const authToken = sessionStorage.getItem('authToken') || 
                     (isPwa ? localStorage.getItem('pwa_authToken') : null);
    const hardwareId = localStorage.getItem('hardwareId');
    return !!authToken && !!hardwareId;
  }

  get isLogin() {
    return !!this.authToken && !!this.hardwareId;
  }

  logout() {
    this.authToken = '';
    sessionStorage.removeItem('authToken');
    localStorage.removeItem('pwa_authToken');
    this.hardwareId = '';
    localStorage.removeItem('hardwareId');
    this.serverCode = '';
    localStorage.removeItem('serverCode');
    this.serverCodeTimestamp = null;
    localStorage.removeItem('serverCodeTimestamp');
    this.loginStateChange.next(false);
  }

  setAuthToken(token: string) {
    this.authToken = token;
    sessionStorage.setItem('authToken', token);
    
    localStorage.setItem('pwa_authToken', token);
    
    this.loginStateChange.next(this.isLogin);
  }

  setHardwareId(id: string) {
    this.hardwareId = id;
    localStorage.setItem('hardwareId', id);
    this.loginStateChange.next(this.isLogin);
  }

  setServerCode(code: string) {
    this.serverCode = code;
    localStorage.setItem('serverCode', code);
    this.serverCodeTimestamp = Date.now();
    localStorage.setItem('serverCodeTimestamp', this.serverCodeTimestamp.toString());
  }

  createHardwareId() {
    return randomString(16);
  }
}
