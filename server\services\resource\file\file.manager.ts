import { Subject, Subscription, from, debounceTime, concatMap } from 'rxjs';
import * as path from 'path';
import * as fs from 'fs-extra';

import serverTime from '@services/serverTime';

interface FileConfig<T> {
  update_at: number;
  file_list: T[];
}
export interface IFile {
  id: string;
  path: string;
  deleted: boolean;

  toJSON(): object;
}

export class GeneralFile implements IFile {
  id: string;
  path: string;
  deleted: boolean;

  constructor(fileManager: IManagerFile) {
    return new Proxy(this, {
      set(target, p, value) {
        target[p] = value;
        fileManager.markWrite();
        return true;
      },
    });
  }

  get isExist() {
    const is_exist = fs.existsSync(this.path);
    return is_exist;
  }

  toJSON(): object {
    return {
      id: this.id,
      path: this.path,
      deleted: this.deleted,
    };
  }
}

export interface IManagerFile {
  file_base_dir: string;
  markWrite(): void;
  serverTime: ITime;
}

export interface ITime {
  now(): number;
}

export class FileManager<T extends IFile> {
  readonly file_base_dir: string;
  protected files: T[] = [];
  protected config: FileConfig<T> = { update_at: 0, file_list: [] };

  private writeConfig$ = new Subject();
  private subscription: Subscription;

  constructor(file_base_dir: string) {
    this.file_base_dir = file_base_dir;
  }

  pushFile(file: T) {
    this.files.push(file);
    this.markWrite();
  }

  markWrite() {
    if (!this.subscription) {
      this.subscription = this.writeConfig$
        .pipe(
          debounceTime(2000),
          concatMap(() => from(this.writeConfig())),
        )
        .subscribe();
    }
    this.writeConfig$.next(1);
  }

  async readConfig(): Promise<FileConfig<T> | null> {
    const config_path = path.join(this.file_base_dir, 'config');
    const config_temp_path = path.join(this.file_base_dir, 'config.temp');
    const is_config_exist = fs.existsSync(config_path);
    const is_temp_exist = fs.existsSync(config_temp_path);
    let temp_config: FileConfig<T> = {} as any,
      config: FileConfig<T>;

    if (!is_temp_exist && !is_config_exist) {
      console.warn('FileManager: no config file #', config_path);
      return null;
    }

    if (is_temp_exist) {
      console.warn('FileManager: config temp file exists', config_temp_path);
      temp_config = await fs
        .readJSON(config_temp_path)
        .catch((err) => console.error('read config temp file error:', err));
    }
    if (is_config_exist) {
      config = await fs
        .readJSON(config_path)
        .catch((err) => console.error('read config file error:', err));
      if (
        is_temp_exist &&
        config &&
        temp_config?.update_at > config?.update_at
      ) {
        console.warn(
          'FileManager: use temp config file',
          config_temp_path,
          temp_config?.update_at,
          config?.update_at,
        );
        return temp_config;
      }
      if (is_temp_exist && temp_config && !config) {
        console.warn('FileManager: use temp file');
        return temp_config;
      }
      if (config) {
        console.log(
          'FileManager: read config file',
          config_path,
          config.update_at,
        );
        return config;
      } else {
        console.error(
          'FileManager: read config file error',
          is_temp_exist,
          is_config_exist,
        );
        return null;
      }
    } else {
      console.log(`FileManager: read config file: temp ${!!temp_config}`);
      return temp_config || null;
    }
  }

  deleteFile(id: string, delete_record = false): boolean {
    const file = this.files.find((f) => f.id === id);
    if (delete_record) {
      this.deleteRecord(id);
    }
    if (file?.path) {
      if (file.deleted) {
        return true;
      }
      file.deleted = true;
      try {
        fs.unlinkSync(file.path);
      } catch (error) {
        console.error('FileManager: Delete: delete file failed', error);
      }
      return true;
    } else {
      console.error(
        `FileManager: Delete: file not found: ${this.file_base_dir}  ${id}(${file?.path})`,
      );
      return false;
    }
  }

  private deleteRecord(id: string) {
    const i = this.files.findIndex((f) => f.id === id);
    if (i > -1) {
      this.files.splice(i, 1);
      this.markWrite();
    }
  }

  private async writeConfig() {
    const config_path = path.join(this.file_base_dir, 'config');
    const config_temp_path = path.join(this.file_base_dir, 'config.temp');
    await fs.ensureFile(config_temp_path).catch((err) => console.error(err));
    this.config.update_at = serverTime.now();
    this.config.file_list = this.files;
    console.info(
      'FileManager: write config file #' + this.config.file_list.length,
    );
    try {
      await fs.writeJSON(config_temp_path, this.config);
      fs.removeSync(config_path);
      await fs.rename(config_temp_path, config_path);
    } catch (error) {
      console.error('FileManager: write config file error', error);
    }
  }
}
