{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-qr-code.mjs"], "sourcesContent": ["import { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, inject, PLATFORM_ID, booleanAttribute, numberAttribute, Output, Input, ViewChild, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i3 from 'ng-zorro-antd/button';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzStringTemplateOutletDirective } from 'ng-zorro-antd/core/outlet';\nimport * as i5 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from 'ng-zorro-antd/spin';\nimport { NzSpinModule } from 'ng-zorro-antd/spin';\nimport * as i1 from 'ng-zorro-antd/i18n';\nimport * as i4 from 'ng-zorro-antd/core/transition-patch';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * QR Code generator library (TypeScript)\n *\n * Copyright (c) Project Nayuki.\n * https://www.nayuki.io/page/qr-code-generator-library\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nconst _c0 = [\"canvas\"];\nfunction NzQRCodeComponent_Conditional_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzStatusRender);\n  }\n}\nfunction NzQRCodeComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, NzQRCodeComponent_Conditional_0_ng_container_1_Template, 2, 1, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzStatusRender);\n  }\n}\nfunction NzQRCodeComponent_Conditional_1_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-spin\");\n  }\n}\nfunction NzQRCodeComponent_Conditional_1_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function NzQRCodeComponent_Conditional_1_Case_2_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.reloadQRCode());\n    });\n    i0.ɵɵelement(4, \"nz-icon\", 5);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.locale.expired);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.locale.refresh);\n  }\n}\nfunction NzQRCodeComponent_Conditional_1_Case_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.locale.scanned);\n  }\n}\nfunction NzQRCodeComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, NzQRCodeComponent_Conditional_1_Case_1_Template, 1, 0, \"nz-spin\")(2, NzQRCodeComponent_Conditional_1_Case_2_Template, 7, 2, \"div\")(3, NzQRCodeComponent_Conditional_1_Case_3_Template, 3, 1, \"div\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional((tmp_1_0 = ctx_r0.nzStatus) === \"loading\" ? 1 : tmp_1_0 === \"expired\" ? 2 : tmp_1_0 === \"scanned\" ? 3 : -1);\n  }\n}\nfunction NzQRCodeComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", null, 0);\n  }\n}\n'use strict';\n// eslint-disable-next-line @typescript-eslint/no-namespace\nvar qrcodegen;\n(function (qrcodegen) {\n  /*---- QR Code symbol class ----*/\n  /*\n   * A QR Code symbol, which is a type of two-dimension barcode.\n   * Invented by Denso Wave and described in the ISO/IEC 18004 standard.\n   * Instances of this class represent an immutable square grid of dark and light cells.\n   * The class provides static factory functions to create a QR Code from text or binary data.\n   * The class covers the QR Code Model 2 specification, supporting all versions (sizes)\n   * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.\n   *\n   * Ways to create a QR Code object:\n   * - High level: Take the payload data and call QrCode.encodeText() or QrCode.encodeBinary().\n   * - Mid level: Custom-make the list of segments and call QrCode.encodeSegments().\n   * - Low level: Custom-make the array of data codeword bytes (including\n   *   segment headers and final padding, excluding error correction codewords),\n   *   supply the appropriate version number, and call the QrCode() constructor.\n   * (Note that all ways require supplying the desired error correction level.)\n   */\n  class QrCode {\n    version;\n    errorCorrectionLevel;\n    /*-- Static factory functions (high level) --*/\n    // Returns a QR Code representing the given Unicode text string at the given error correction level.\n    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n    // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n    // ecl argument if it can be done without increasing the version.\n    static encodeText(text, ecl) {\n      const segs = qrcodegen.QrSegment.makeSegments(text);\n      return QrCode.encodeSegments(segs, ecl);\n    }\n    // Returns a QR Code representing the given binary data at the given error correction level.\n    // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n    static encodeBinary(data, ecl) {\n      const seg = qrcodegen.QrSegment.makeBytes(data);\n      return QrCode.encodeSegments([seg], ecl);\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a QR Code representing the given segments with the given encoding parameters.\n    // The smallest possible QR Code version within the given range is automatically\n    // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n    // may be higher than the ecl argument if it can be done without increasing the\n    // version. The mask number is either between 0 to 7 (inclusive) to force that\n    // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n    // This function allows the user to create a custom sequence of segments that switches\n    // between modes (such as alphanumeric and byte) to encode text in less space.\n    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n    static encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {\n      if (!(QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= QrCode.MAX_VERSION) || mask < -1 || mask > 7) throw new RangeError('Invalid value');\n      // Find the minimal version number to use\n      let version;\n      let dataUsedBits;\n      for (version = minVersion;; version++) {\n        const dataCapacityBits = QrCode.getNumDataCodewords(version, ecl) * 8; // Number of data bits available\n        const usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits) {\n          dataUsedBits = usedBits;\n          break; // This version number is found to be suitable\n        }\n        if (version >= maxVersion)\n          // All versions in the range could not fit the given data\n          throw new RangeError('Data too long');\n      }\n      // Increase the error correction level while the data still fits in the current version number\n      for (const newEcl of [QrCode.Ecc.MEDIUM, QrCode.Ecc.QUARTILE, QrCode.Ecc.HIGH]) {\n        // From low to high\n        if (boostEcl && dataUsedBits <= QrCode.getNumDataCodewords(version, newEcl) * 8) ecl = newEcl;\n      }\n      // Concatenate all segments to create the data bit string\n      const bb = [];\n      for (const seg of segs) {\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (const b of seg.getData()) bb.push(b);\n      }\n      assert(bb.length == dataUsedBits);\n      // Add terminator and pad up to a byte if applicable\n      const dataCapacityBits = QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n      // Pad with alternating bytes until data capacity is reached\n      for (let padByte = 0xec; bb.length < dataCapacityBits; padByte ^= 0xec ^ 0x11) appendBits(padByte, 8, bb);\n      // Pack bits into bytes in big endian\n      const dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length) dataCodewords.push(0);\n      bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));\n      // Create the QR Code object\n      return new QrCode(version, ecl, dataCodewords, mask);\n    }\n    /*-- Fields --*/\n    // The width and height of this QR Code, measured in modules, between\n    // 21 and 177 (inclusive). This is equal to version * 4 + 17.\n    size;\n    // The index of the mask pattern used in this QR Code, which is between 0 and 7 (inclusive).\n    // Even if a QR Code is created with automatic masking requested (mask = -1),\n    // the resulting object still has a mask value between 0 and 7.\n    mask;\n    // The modules of this QR Code (false = light, true = dark).\n    // Immutable after constructor finishes. Accessed through getModule().\n    modules = [];\n    // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n    isFunction = [];\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code with the given version number,\n    // error correction level, data codeword bytes, and mask number.\n    // This is a low-level API that most users should not use directly.\n    // A mid-level API is the encodeSegments() function.\n    constructor(\n    // The version number of this QR Code, which is between 1 and 40 (inclusive).\n    // This determines the size of this barcode.\n    version,\n    // The error correction level used in this QR Code.\n    errorCorrectionLevel, dataCodewords, msk) {\n      this.version = version;\n      this.errorCorrectionLevel = errorCorrectionLevel;\n      // Check scalar arguments\n      if (version < QrCode.MIN_VERSION || version > QrCode.MAX_VERSION) throw new RangeError('Version value out of range');\n      if (msk < -1 || msk > 7) throw new RangeError('Mask value out of range');\n      this.size = version * 4 + 17;\n      // Initialize both grids to be size*size arrays of Boolean false\n      const row = [];\n      for (let i = 0; i < this.size; i++) row.push(false);\n      for (let i = 0; i < this.size; i++) {\n        this.modules.push(row.slice()); // Initially all light\n        this.isFunction.push(row.slice());\n      }\n      // Compute ECC, draw modules\n      this.drawFunctionPatterns();\n      const allCodewords = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n      // Do masking\n      if (msk == -1) {\n        // Automatically choose best mask\n        let minPenalty = 1000000000;\n        for (let i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          const penalty = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i); // Undoes the mask due to XOR\n        }\n      }\n      assert(msk >= 0 && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk); // Apply the final choice of mask\n      this.drawFormatBits(msk); // Overwrite old format bits\n      this.isFunction = [];\n    }\n    /*-- Accessor methods --*/\n    // Returns the color of the module (pixel) at the given coordinates, which is false\n    // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n    // If the given coordinates are out of bounds, then false (light) is returned.\n    getModule(x, y) {\n      return x >= 0 && x < this.size && y >= 0 && y < this.size && this.modules[y][x];\n    }\n    // Modified to expose modules for easy access\n    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n    getModules() {\n      return this.modules;\n    }\n    /*-- Private helper methods for constructor: Drawing function modules --*/\n    // Reads this object's version field, and draws and marks all function modules.\n    drawFunctionPatterns() {\n      // Draw horizontal and vertical timing patterns\n      for (let i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n      // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n      // Draw numerous alignment patterns\n      const alignPatPos = this.getAlignmentPatternPositions();\n      const numAlign = alignPatPos.length;\n      for (let i = 0; i < numAlign; i++) {\n        for (let j = 0; j < numAlign; j++) {\n          // Don't draw on the three finder corners\n          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0)) this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n      // Draw configuration data\n      this.drawFormatBits(0); // Dummy mask value; overwritten later in the constructor\n      this.drawVersion();\n    }\n    // Draws two copies of the format bits (with its own error correction code)\n    // based on the given mask and this object's error correction level field.\n    drawFormatBits(mask) {\n      // Calculate error correction code and pack bits\n      const data = this.errorCorrectionLevel.formatBits << 3 | mask; // errCorrLvl is uint2, mask is uint3\n      let rem = data;\n      for (let i = 0; i < 10; i++) rem = rem << 1 ^ (rem >>> 9) * 0x537;\n      const bits = (data << 10 | rem) ^ 0x5412; // uint15\n      assert(bits >>> 15 == 0);\n      // Draw first copy\n      for (let i = 0; i <= 5; i++) this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (let i = 9; i < 15; i++) this.setFunctionModule(14 - i, 8, getBit(bits, i));\n      // Draw second copy\n      for (let i = 0; i < 8; i++) this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (let i = 8; i < 15; i++) this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true); // Always dark\n    }\n    // Draws two copies of the version bits (with its own error correction code),\n    // based on this object's version field, iff 7 <= version <= 40.\n    drawVersion() {\n      if (this.version < 7) return;\n      // Calculate error correction code and pack bits\n      let rem = this.version; // version is uint6, in the range [7, 40]\n      for (let i = 0; i < 12; i++) rem = rem << 1 ^ (rem >>> 11) * 0x1f25;\n      const bits = this.version << 12 | rem; // uint18\n      assert(bits >>> 18 == 0);\n      // Draw two copies\n      for (let i = 0; i < 18; i++) {\n        const color = getBit(bits, i);\n        const a = this.size - 11 + i % 3;\n        const b = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n    // Draws a 9*9 finder pattern including the border separator,\n    // with the center module at (x, y). Modules can be out of bounds.\n    drawFinderPattern(x, y) {\n      for (let dy = -4; dy <= 4; dy++) {\n        for (let dx = -4; dx <= 4; dx++) {\n          const dist = Math.max(Math.abs(dx), Math.abs(dy)); // Chebyshev/infinity norm\n          const xx = x + dx;\n          const yy = y + dy;\n          if (xx >= 0 && xx < this.size && yy >= 0 && yy < this.size) this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n    // Draws a 5*5 alignment pattern, with the center module\n    // at (x, y). All modules must be in bounds.\n    drawAlignmentPattern(x, y) {\n      for (let dy = -2; dy <= 2; dy++) {\n        for (let dx = -2; dx <= 2; dx++) this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n    // Sets the color of a module and marks it as a function module.\n    // Only used by the constructor. Coordinates must be in bounds.\n    setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n    /*-- Private helper methods for constructor: Codewords and masking --*/\n    // Returns a new byte string representing the given data with the appropriate error correction\n    // codewords appended to it, based on this object's version and error correction level.\n    addEccAndInterleave(data) {\n      const ver = this.version;\n      const ecl = this.errorCorrectionLevel;\n      if (data.length != QrCode.getNumDataCodewords(ver, ecl)) throw new RangeError('Invalid argument');\n      // Calculate parameter numbers\n      const numBlocks = QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      const blockEccLen = QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      const rawCodewords = Math.floor(QrCode.getNumRawDataModules(ver) / 8);\n      const numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      const shortBlockLen = Math.floor(rawCodewords / numBlocks);\n      // Split data into blocks and append ECC to each block\n      const blocks = [];\n      const rsDiv = QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (let i = 0, k = 0; i < numBlocks; i++) {\n        const dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        const ecc = QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks) dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n      // Interleave (not concatenate) the bytes from every block into a single sequence\n      const result = [];\n      for (let i = 0; i < blocks[0].length; i++) {\n        blocks.forEach((block, j) => {\n          // Skip the padding byte in short blocks\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks) result.push(block[i]);\n        });\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n    // data area of this QR Code. Function modules need to be marked off before this is called.\n    drawCodewords(data) {\n      if (data.length != Math.floor(QrCode.getNumRawDataModules(this.version) / 8)) throw new RangeError('Invalid argument');\n      let i = 0; // Bit index into the data\n      // Do the funny zigzag scan\n      for (let right = this.size - 1; right >= 1; right -= 2) {\n        // Index of right column in each column pair\n        if (right == 6) right = 5;\n        for (let vert = 0; vert < this.size; vert++) {\n          // Vertical counter\n          for (let j = 0; j < 2; j++) {\n            const x = right - j; // Actual x coordinate\n            const upward = (right + 1 & 2) == 0;\n            const y = upward ? this.size - 1 - vert : vert; // Actual y coordinate\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n            // If this QR Code has any remainder bits (0 to 7), they were assigned as\n            // 0/false/light by the constructor and are left unchanged by this method\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n    // XORs the codeword modules in this QR Code with the given mask pattern.\n    // The function modules must be marked and the codeword bits must be drawn\n    // before masking. Due to the arithmetic of XOR, calling applyMask() with\n    // the same mask value a second time will undo the mask. A final well-formed\n    // QR Code needs exactly one (not zero, two, etc.) mask applied.\n    applyMask(mask) {\n      if (mask < 0 || mask > 7) throw new RangeError('Mask value out of range');\n      for (let y = 0; y < this.size; y++) {\n        for (let x = 0; x < this.size; x++) {\n          let invert;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error('Unreachable');\n          }\n          if (!this.isFunction[y][x] && invert) this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n    // Calculates and returns the penalty score based on state of this QR Code's current modules.\n    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n    getPenaltyScore() {\n      let result = 0;\n      // Adjacent modules in row having same color, and finder-like patterns\n      for (let y = 0; y < this.size; y++) {\n        let runColor = false;\n        let runX = 0;\n        const runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5) result += QrCode.PENALTY_N1;else if (runX > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * QrCode.PENALTY_N3;\n      }\n      // Adjacent modules in column having same color, and finder-like patterns\n      for (let x = 0; x < this.size; x++) {\n        let runColor = false;\n        let runY = 0;\n        const runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5) result += QrCode.PENALTY_N1;else if (runY > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * QrCode.PENALTY_N3;\n      }\n      // 2*2 blocks of modules having same color\n      for (let y = 0; y < this.size - 1; y++) {\n        for (let x = 0; x < this.size - 1; x++) {\n          const color = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1]) result += QrCode.PENALTY_N2;\n        }\n      }\n      // Balance of dark and light modules\n      let dark = 0;\n      for (const row of this.modules) dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n      const total = this.size * this.size; // Note that size is odd, so dark/total != 1/2\n      // Compute the smallest integer k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%\n      const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(k >= 0 && k <= 9);\n      result += k * QrCode.PENALTY_N4;\n      assert(result >= 0 && result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4\n      return result;\n    }\n    /*-- Private helper functions --*/\n    // Returns an ascending list of positions of alignment patterns for this version number.\n    // Each position is in the range [0,177), and are used on both the x and y axes.\n    // This could be implemented as lookup table of 40 variable-length lists of integers.\n    getAlignmentPatternPositions() {\n      if (this.version == 1) return [];else {\n        const numAlign = Math.floor(this.version / 7) + 2;\n        const step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        const result = [6];\n        for (let pos = this.size - 7; result.length < numAlign; pos -= step) result.splice(1, 0, pos);\n        return result;\n      }\n    }\n    // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n    static getNumRawDataModules(ver) {\n      if (ver < QrCode.MIN_VERSION || ver > QrCode.MAX_VERSION) throw new RangeError('Version number out of range');\n      let result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        const numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7) result -= 36;\n      }\n      assert(result >= 208 && result <= 29648);\n      return result;\n    }\n    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n    // QR Code of the given version number and error correction level, with remainder bits discarded.\n    // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n    static getNumDataCodewords(ver, ecl) {\n      return Math.floor(QrCode.getNumRawDataModules(ver) / 8) - QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n    static reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255) throw new RangeError('Degree out of range');\n      // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.\n      // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the uint8 array [255, 8, 93].\n      const result = [];\n      for (let i = 0; i < degree - 1; i++) result.push(0);\n      result.push(1); // Start off with the monomial x^0\n      // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),\n      // and drop the highest monomial term which is always 1x^degree.\n      // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).\n      let root = 1;\n      for (let i = 0; i < degree; i++) {\n        // Multiply the current product by (x - r^i)\n        for (let j = 0; j < result.length; j++) {\n          result[j] = QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length) result[j] ^= result[j + 1];\n        }\n        root = QrCode.reedSolomonMultiply(root, 0x02);\n      }\n      return result;\n    }\n    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n    static reedSolomonComputeRemainder(data, divisor) {\n      const result = divisor.map(_ => 0);\n      for (const b of data) {\n        // Polynomial division\n        const factor = b ^ result.shift();\n        result.push(0);\n        divisor.forEach((coef, i) => result[i] ^= QrCode.reedSolomonMultiply(coef, factor));\n      }\n      return result;\n    }\n    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n    // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n    static reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0) throw new RangeError('Byte out of range');\n      // Russian peasant multiplication\n      let z = 0;\n      for (let i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 0x11d;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n    // Can only be called immediately after a light run is added, and\n    // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n    finderPenaltyCountPatterns(runHistory) {\n      const n = runHistory[1];\n      assert(n <= this.size * 3);\n      const core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n    finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {\n      if (currentRunColor) {\n        // Terminate dark run\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size; // Add light border to final run\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n    finderPenaltyAddHistory(currentRunLength, runHistory) {\n      if (runHistory[0] == 0) currentRunLength += this.size; // Add light border to initial run\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n    /*-- Constants and tables --*/\n    // The minimum version number supported in the QR Code Model 2 standard.\n    static MIN_VERSION = 1;\n    // The maximum version number supported in the QR Code Model 2 standard.\n    static MAX_VERSION = 40;\n    // For use in getPenaltyScore(), when evaluating which mask is best.\n    static PENALTY_N1 = 3;\n    static PENALTY_N2 = 3;\n    static PENALTY_N3 = 40;\n    static PENALTY_N4 = 10;\n    static ECC_CODEWORDS_PER_BLOCK = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Low\n    [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    // Medium\n    [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Quartile\n    [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30] // High\n    ];\n    static NUM_ERROR_CORRECTION_BLOCKS = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    // Low\n    [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    // Medium\n    [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    // Quartile\n    [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81] // High\n    ];\n  }\n  qrcodegen.QrCode = QrCode;\n  // Appends the given number of low-order bits of the given value\n  // to the given buffer. Requires 0 <= len <= 31 and 0 <= val < 2^len.\n  function appendBits(val, len, bb) {\n    if (len < 0 || len > 31 || val >>> len != 0) throw new RangeError('Value out of range');\n    for (let i = len - 1; i >= 0; i-- // Append bit by bit\n    ) bb.push(val >>> i & 1);\n  }\n  // Returns true iff the i'th bit of x is set to 1.\n  function getBit(x, i) {\n    return (x >>> i & 1) != 0;\n  }\n  // Throws an exception if the given condition is false.\n  function assert(cond) {\n    if (!cond) throw new Error('Assertion error');\n  }\n  /*---- Data segment class ----*/\n  /*\n   * A segment of character/binary/control data in a QR Code symbol.\n   * Instances of this class are immutable.\n   * The mid-level way to create a segment is to take the payload data\n   * and call a static factory function such as QrSegment.makeNumeric().\n   * The low-level way to create a segment is to custom-make the bit buffer\n   * and call the QrSegment() constructor with appropriate values.\n   * This segment class imposes no length restrictions, but QR Codes have restrictions.\n   * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.\n   * Any segment longer than this is meaningless for the purpose of generating QR Codes.\n   */\n  class QrSegment {\n    mode;\n    numChars;\n    bitData;\n    /*-- Static factory functions (mid level) --*/\n    // Returns a segment representing the given binary data encoded in\n    // byte mode. All input byte arrays are acceptable. Any text string\n    // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n    static makeBytes(data) {\n      const bb = [];\n      for (const b of data) appendBits(b, 8, bb);\n      return new QrSegment(QrSegment.Mode.BYTE, data.length, bb);\n    }\n    // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n    static makeNumeric(digits) {\n      if (!QrSegment.isNumeric(digits)) throw new RangeError('String contains non-numeric characters');\n      const bb = [];\n      for (let i = 0; i < digits.length;) {\n        // Consume up to 3 digits per iteration\n        const n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new QrSegment(QrSegment.Mode.NUMERIC, digits.length, bb);\n    }\n    // Returns a segment representing the given text string encoded in alphanumeric mode.\n    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static makeAlphanumeric(text) {\n      if (!QrSegment.isAlphanumeric(text)) throw new RangeError('String contains unencodable characters in alphanumeric mode');\n      const bb = [];\n      let i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        // Process groups of 2\n        let temp = QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        // 1 character remaining\n        appendBits(QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new QrSegment(QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    }\n    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n    // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n    static makeSegments(text) {\n      // Select the most efficient segment encoding automatically\n      if (text == '') return [];else if (QrSegment.isNumeric(text)) return [QrSegment.makeNumeric(text)];else if (QrSegment.isAlphanumeric(text)) return [QrSegment.makeAlphanumeric(text)];else return [QrSegment.makeBytes(QrSegment.toUtf8ByteArray(text))];\n    }\n    // Returns a segment representing an Extended Channel Interpretation\n    // (ECI) designator with the given assignment value.\n    static makeEci(assignVal) {\n      const bb = [];\n      if (assignVal < 0) throw new RangeError('ECI assignment value out of range');else if (assignVal < 1 << 7) appendBits(assignVal, 8, bb);else if (assignVal < 1 << 14) {\n        appendBits(0b10, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1000000) {\n        appendBits(0b110, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else throw new RangeError('ECI assignment value out of range');\n      return new QrSegment(QrSegment.Mode.ECI, 0, bb);\n    }\n    // Tests whether the given string can be encoded as a segment in numeric mode.\n    // A string is encodable iff each character is in the range 0 to 9.\n    static isNumeric(text) {\n      return QrSegment.NUMERIC_REGEX.test(text);\n    }\n    // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n    // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static isAlphanumeric(text) {\n      return QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code segment with the given attributes and data.\n    // The character count (numChars) must agree with the mode and the bit buffer length,\n    // but the constraint isn't checked. The given bit buffer is cloned and stored.\n    constructor(\n    // The mode indicator of this segment.\n    mode,\n    // The length of this segment's unencoded data. Measured in characters for\n    // numeric/alphanumeric/kanji mode, bytes for byte mode, and 0 for ECI mode.\n    // Always zero or positive. Not the same as the data's bit length.\n    numChars,\n    // The data bits of this segment. Accessed through getData().\n    bitData) {\n      this.mode = mode;\n      this.numChars = numChars;\n      this.bitData = bitData;\n      if (numChars < 0) throw new RangeError('Invalid argument');\n      this.bitData = bitData.slice(); // Make defensive copy\n    }\n    /*-- Methods --*/\n    // Returns a new copy of the data bits of this segment.\n    getData() {\n      return this.bitData.slice(); // Make defensive copy\n    }\n    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n    // the given version. The result is infinity if a segment has too many characters to fit its length field.\n    static getTotalBits(segs, version) {\n      let result = 0;\n      for (const seg of segs) {\n        const ccbits = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits) return Infinity; // The segment's length doesn't fit the field's bit width\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    }\n    // Returns a new array of bytes representing the given string encoded in UTF-8.\n    static toUtf8ByteArray(str) {\n      str = encodeURI(str);\n      const result = [];\n      for (let i = 0; i < str.length; i++) {\n        if (str.charAt(i) != '%') result.push(str.charCodeAt(i));else {\n          result.push(parseInt(str.substring(i + 1, i + 3), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n    /*-- Constants --*/\n    // Describes precisely all strings that are encodable in numeric mode.\n    static NUMERIC_REGEX = /^[0-9]*$/;\n    // Describes precisely all strings that are encodable in alphanumeric mode.\n    static ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+./:-]*$/;\n    // The set of all legal characters in alphanumeric mode,\n    // where each character value maps to the index in the string.\n    static ALPHANUMERIC_CHARSET = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:';\n  }\n  qrcodegen.QrSegment = QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n/*---- Public helper enumeration ----*/\n// eslint-disable-next-line @typescript-eslint/no-namespace\n(function (qrcodegen) {\n  var QrCode;\n  (function (QrCode) {\n    /*\n     * The error correction level in a QR Code symbol. Immutable.\n     */\n    class Ecc {\n      ordinal;\n      formatBits;\n      /*-- Constants --*/\n      static LOW = new Ecc(0, 1); // The QR Code can tolerate about  7% erroneous codewords\n      static MEDIUM = new Ecc(1, 0); // The QR Code can tolerate about 15% erroneous codewords\n      static QUARTILE = new Ecc(2, 3); // The QR Code can tolerate about 25% erroneous codewords\n      static HIGH = new Ecc(3, 2); // The QR Code can tolerate about 30% erroneous codewords\n      /*-- Constructor and fields --*/\n      constructor(\n      // In the range 0 to 3 (unsigned 2-bit integer).\n      ordinal,\n      // (Package-private) In the range 0 to 3 (unsigned 2-bit integer).\n      formatBits) {\n        this.ordinal = ordinal;\n        this.formatBits = formatBits;\n      }\n    }\n    QrCode.Ecc = Ecc;\n  })(QrCode = qrcodegen.QrCode || (qrcodegen.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n/*---- Public helper enumeration ----*/\n// eslint-disable-next-line @typescript-eslint/no-namespace\n(function (qrcodegen) {\n  var QrSegment;\n  (function (QrSegment) {\n    /*\n     * Describes how a segment's data bits are interpreted. Immutable.\n     */\n    class Mode {\n      modeBits;\n      numBitsCharCount;\n      /*-- Constants --*/\n      static NUMERIC = new Mode(0x1, [10, 12, 14]);\n      static ALPHANUMERIC = new Mode(0x2, [9, 11, 13]);\n      static BYTE = new Mode(0x4, [8, 16, 16]);\n      static KANJI = new Mode(0x8, [8, 10, 12]);\n      static ECI = new Mode(0x7, [0, 0, 0]);\n      /*-- Constructor and fields --*/\n      constructor(\n      // The mode indicator bits, which is a uint4 value (range 0 to 15).\n      modeBits,\n      // Number of character count bits for three different version ranges.\n      numBitsCharCount) {\n        this.modeBits = modeBits;\n        this.numBitsCharCount = numBitsCharCount;\n      }\n      /*-- Method --*/\n      // (Package-private) Returns the bit width of the character count field for a segment in\n      // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n      numCharCountBits(ver) {\n        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n      }\n    }\n    QrSegment.Mode = Mode;\n  })(QrSegment = qrcodegen.QrSegment || (qrcodegen.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\n// Modification to export for actual use\nvar qrcodegen$1 = qrcodegen;\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst ERROR_LEVEL_MAP = {\n  L: qrcodegen$1.QrCode.Ecc.LOW,\n  M: qrcodegen$1.QrCode.Ecc.MEDIUM,\n  Q: qrcodegen$1.QrCode.Ecc.QUARTILE,\n  H: qrcodegen$1.QrCode.Ecc.HIGH\n};\nconst DEFAULT_SIZE = 160;\nconst DEFAULT_SCALE = 10;\nconst DEFAULT_PADDING = 10;\nconst DEFAULT_COLOR = '#000000';\nconst DEFAULT_BACKGROUND_COLOR = '#FFFFFF';\nconst DEFAULT_ICONSIZE = 40;\nconst DEFAULT_LEVEL = 'M';\nconst plotQRCodeData = (value, level = DEFAULT_LEVEL) => {\n  if (!value) {\n    return null;\n  }\n  return qrcodegen$1.QrCode.encodeText(value, ERROR_LEVEL_MAP[level]);\n};\nfunction drawCanvas(canvas, value, size = DEFAULT_SIZE, scale = DEFAULT_SCALE, padding = DEFAULT_PADDING, color = DEFAULT_COLOR, backgroundColor = DEFAULT_BACKGROUND_COLOR, iconSize = DEFAULT_ICONSIZE, icon) {\n  const ctx = canvas.getContext('2d');\n  const formattedPadding = formatPadding(padding);\n  canvas.style.width = `${size}px`;\n  canvas.style.height = `${size}px`;\n  if (!value) {\n    ctx.fillStyle = 'rgba(0, 0, 0, 0)';\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n    return;\n  }\n  canvas.width = value.size * scale + formattedPadding[1] + formattedPadding[3];\n  canvas.height = value.size * scale + formattedPadding[0] + formattedPadding[2];\n  if (!icon) {\n    drawCanvasBackground(ctx, canvas.width, canvas.height, scale, backgroundColor);\n    drawCanvasColor(ctx, value, scale, formattedPadding, backgroundColor, color);\n  } else {\n    const iconImg = new Image();\n    iconImg.src = icon;\n    iconImg.crossOrigin = 'anonymous';\n    iconImg.width = iconSize * (canvas.width / size);\n    iconImg.height = iconSize * (canvas.width / size);\n    const onLoad = () => {\n      cleanup();\n      drawCanvasBackground(ctx, canvas.width, canvas.height, scale, backgroundColor);\n      drawCanvasColor(ctx, value, scale, formattedPadding, backgroundColor, color);\n      const iconCoordinate = canvas.width / 2 - iconSize * (canvas.width / size) / 2;\n      ctx.fillRect(iconCoordinate, iconCoordinate, iconSize * (canvas.width / size), iconSize * (canvas.width / size));\n      ctx.drawImage(iconImg, iconCoordinate, iconCoordinate, iconSize * (canvas.width / size), iconSize * (canvas.width / size));\n    };\n    const onError = () => {\n      cleanup();\n      drawCanvasBackground(ctx, canvas.width, canvas.height, scale, backgroundColor);\n      drawCanvasColor(ctx, value, scale, formattedPadding, backgroundColor, color);\n    };\n    const cleanup = () => {\n      iconImg.removeEventListener('load', onLoad);\n      iconImg.removeEventListener('error', onError);\n    };\n    iconImg.addEventListener('load', onLoad);\n    iconImg.addEventListener('error', onError);\n  }\n}\nfunction drawCanvasColor(ctx, value, scale, padding, backgroundColor, color) {\n  for (let y = 0; y < value.size; y++) {\n    for (let x = 0; x < value.size; x++) {\n      ctx.fillStyle = value.getModule(x, y) ? color : backgroundColor;\n      ctx.fillRect(padding[3] + x * scale, padding[0] + y * scale, scale, scale);\n    }\n  }\n}\nfunction drawCanvasBackground(ctx, width, height, scale, backgroundColor) {\n  ctx.fillStyle = backgroundColor;\n  ctx.fillRect(0, 0, width * scale, height * scale);\n}\nfunction formatPadding(padding) {\n  if (Array.isArray(padding)) {\n    // Build an array of 4 elements and repeat values from padding as necessary to set the value of the array\n    return Array(4).fill(0).map((_, index) => padding[index % padding.length]);\n  } else {\n    return [padding, padding, padding, padding];\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzQRCodeComponent {\n  i18n;\n  el;\n  cdr;\n  canvas;\n  nzValue = '';\n  nzPadding = 0;\n  nzColor = '#000000';\n  nzBgColor = '#FFFFFF';\n  nzSize = 160;\n  nzIcon = '';\n  nzIconSize = 40;\n  nzBordered = true;\n  nzStatus = 'active';\n  nzLevel = 'M';\n  nzStatusRender = null;\n  nzRefresh = new EventEmitter();\n  locale;\n  // https://github.com/angular/universal-starter/issues/538#issuecomment-365518693\n  // canvas is not supported by the SSR DOM\n  isBrowser = true;\n  destroy$ = new Subject();\n  platformId = inject(PLATFORM_ID);\n  constructor(i18n, el, cdr) {\n    this.i18n = i18n;\n    this.el = el;\n    this.cdr = cdr;\n    this.isBrowser = isPlatformBrowser(this.platformId);\n    this.cdr.markForCheck();\n  }\n  ngOnInit() {\n    this.el.nativeElement.style.backgroundColor = this.nzBgColor;\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('QRCode');\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzValue,\n      nzIcon,\n      nzLevel,\n      nzSize,\n      nzIconSize,\n      nzColor,\n      nzPadding,\n      nzBgColor\n    } = changes;\n    if ((nzValue || nzIcon || nzLevel || nzSize || nzIconSize || nzColor || nzPadding || nzBgColor) && this.canvas) {\n      this.drawCanvasQRCode();\n    }\n    if (nzBgColor) {\n      this.el.nativeElement.style.backgroundColor = this.nzBgColor;\n    }\n  }\n  ngAfterViewInit() {\n    this.drawCanvasQRCode();\n  }\n  reloadQRCode() {\n    this.drawCanvasQRCode();\n    this.nzRefresh.emit('refresh');\n  }\n  drawCanvasQRCode() {\n    if (this.canvas) {\n      drawCanvas(this.canvas.nativeElement, plotQRCodeData(this.nzValue, this.nzLevel), this.nzSize, 10, this.nzPadding, this.nzColor, this.nzBgColor, this.nzIconSize, this.nzIcon);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzQRCodeComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzQRCodeComponent)(i0.ɵɵdirectiveInject(i1.NzI18nService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzQRCodeComponent,\n    selectors: [[\"nz-qrcode\"]],\n    viewQuery: function NzQRCodeComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.canvas = _t.first);\n      }\n    },\n    hostAttrs: [1, \"ant-qrcode\"],\n    hostVars: 2,\n    hostBindings: function NzQRCodeComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-qrcode-border\", ctx.nzBordered);\n      }\n    },\n    inputs: {\n      nzValue: \"nzValue\",\n      nzPadding: \"nzPadding\",\n      nzColor: \"nzColor\",\n      nzBgColor: \"nzBgColor\",\n      nzSize: [2, \"nzSize\", \"nzSize\", numberAttribute],\n      nzIcon: \"nzIcon\",\n      nzIconSize: [2, \"nzIconSize\", \"nzIconSize\", numberAttribute],\n      nzBordered: [2, \"nzBordered\", \"nzBordered\", booleanAttribute],\n      nzStatus: \"nzStatus\",\n      nzLevel: \"nzLevel\",\n      nzStatusRender: \"nzStatusRender\"\n    },\n    outputs: {\n      nzRefresh: \"nzRefresh\"\n    },\n    exportAs: [\"nzQRCode\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 3,\n    vars: 2,\n    consts: [[\"canvas\", \"\"], [1, \"ant-qrcode-mask\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-qrcode-expired\"], [\"nz-button\", \"\", \"nzType\", \"link\", 3, \"click\"], [\"nzType\", \"reload\", \"nzTheme\", \"outline\"]],\n    template: function NzQRCodeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzQRCodeComponent_Conditional_0_Template, 2, 1, \"div\", 1)(1, NzQRCodeComponent_Conditional_1_Template, 4, 1, \"div\", 1)(2, NzQRCodeComponent_Conditional_2_Template, 2, 0, \"canvas\");\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(!!ctx.nzStatusRender ? 0 : ctx.nzStatus !== \"active\" ? 1 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx.isBrowser ? 2 : -1);\n      }\n    },\n    dependencies: [NzSpinModule, i2.NzSpinComponent, NzButtonModule, i3.NzButtonComponent, i4.ɵNzTransitionPatchDirective, NzIconModule, i5.NzIconDirective, NzStringTemplateOutletDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzQRCodeComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'nz-qrcode',\n      exportAs: 'nzQRCode',\n      template: `\n    @if (!!nzStatusRender) {\n      <div class=\"ant-qrcode-mask\">\n        <ng-container *nzStringTemplateOutlet=\"nzStatusRender\">{{ nzStatusRender }}</ng-container>\n      </div>\n    } @else if (nzStatus !== 'active') {\n      <div class=\"ant-qrcode-mask\">\n        @switch (nzStatus) {\n          @case ('loading') {\n            <nz-spin />\n          }\n          @case ('expired') {\n            <div>\n              <p class=\"ant-qrcode-expired\">{{ locale.expired }}</p>\n              <button nz-button nzType=\"link\" (click)=\"reloadQRCode()\">\n                <nz-icon nzType=\"reload\" nzTheme=\"outline\" />\n                <span>{{ locale.refresh }}</span>\n              </button>\n            </div>\n          }\n          @case ('scanned') {\n            <div>\n              <p class=\"ant-qrcode-expired\">{{ locale.scanned }}</p>\n            </div>\n          }\n        }\n      </div>\n    }\n\n    @if (isBrowser) {\n      <canvas #canvas></canvas>\n    }\n  `,\n      host: {\n        class: 'ant-qrcode',\n        '[class.ant-qrcode-border]': `nzBordered`\n      },\n      imports: [NzSpinModule, NzButtonModule, NzIconModule, NzStringTemplateOutletDirective]\n    }]\n  }], () => [{\n    type: i1.NzI18nService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    canvas: [{\n      type: ViewChild,\n      args: ['canvas', {\n        static: false\n      }]\n    }],\n    nzValue: [{\n      type: Input\n    }],\n    nzPadding: [{\n      type: Input\n    }],\n    nzColor: [{\n      type: Input\n    }],\n    nzBgColor: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    nzIcon: [{\n      type: Input\n    }],\n    nzIconSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    nzBordered: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzLevel: [{\n      type: Input\n    }],\n    nzStatusRender: [{\n      type: Input\n    }],\n    nzRefresh: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzQRCodeModule {\n  static ɵfac = function NzQRCodeModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzQRCodeModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzQRCodeModule,\n    imports: [NzQRCodeComponent],\n    exports: [NzQRCodeComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzQRCodeComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzQRCodeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzQRCodeComponent],\n      exports: [NzQRCodeComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzQRCodeComponent, NzQRCodeModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA,IAAM,MAAM,CAAC,QAAQ;AACrB,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,cAAc;AAAA,EAC5C;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,CAAC;AACjG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,cAAc;AAAA,EAC/D;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,SAAS;AAAA,EAC3B;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC;AACrC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,0EAA0E;AACxG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,UAAU,GAAG,WAAW,CAAC;AAC5B,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,OAAO,OAAO;AAC1C,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,OAAO,OAAO;AAAA,EAC5C;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC;AACrC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,OAAO,OAAO;AAAA,EAC5C;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,SAAS,EAAE,GAAG,iDAAiD,GAAG,GAAG,KAAK,EAAE,GAAG,iDAAiD,GAAG,GAAG,KAAK;AACnN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,eAAe,UAAU,OAAO,cAAc,YAAY,IAAI,YAAY,YAAY,IAAI,YAAY,YAAY,IAAI,EAAE;AAAA,EAC7H;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,UAAU,MAAM,CAAC;AAAA,EACnC;AACF;AAGA,IAAI;AAAA,CACH,SAAUA,YAAW;AAAA,EAkBpB,MAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,OAAO,WAAW,MAAM,KAAK;AAC3B,YAAM,OAAOA,WAAU,UAAU,aAAa,IAAI;AAClD,aAAO,OAAO,eAAe,MAAM,GAAG;AAAA,IACxC;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO,aAAa,MAAM,KAAK;AAC7B,YAAM,MAAMA,WAAU,UAAU,UAAU,IAAI;AAC9C,aAAO,OAAO,eAAe,CAAC,GAAG,GAAG,GAAG;AAAA,IACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,OAAO,eAAe,MAAM,KAAK,aAAa,GAAG,aAAa,IAAI,OAAO,IAAI,WAAW,MAAM;AAC5F,UAAI,EAAE,OAAO,eAAe,cAAc,cAAc,cAAc,cAAc,OAAO,gBAAgB,OAAO,MAAM,OAAO,EAAG,OAAM,IAAI,WAAW,eAAe;AAEtK,UAAI;AACJ,UAAI;AACJ,WAAK,UAAU,cAAa,WAAW;AACrC,cAAMC,oBAAmB,OAAO,oBAAoB,SAAS,GAAG,IAAI;AACpE,cAAM,WAAW,UAAU,aAAa,MAAM,OAAO;AACrD,YAAI,YAAYA,mBAAkB;AAChC,yBAAe;AACf;AAAA,QACF;AACA,YAAI,WAAW;AAEb,gBAAM,IAAI,WAAW,eAAe;AAAA,MACxC;AAEA,iBAAW,UAAU,CAAC,OAAO,IAAI,QAAQ,OAAO,IAAI,UAAU,OAAO,IAAI,IAAI,GAAG;AAE9E,YAAI,YAAY,gBAAgB,OAAO,oBAAoB,SAAS,MAAM,IAAI,EAAG,OAAM;AAAA,MACzF;AAEA,YAAM,KAAK,CAAC;AACZ,iBAAW,OAAO,MAAM;AACtB,mBAAW,IAAI,KAAK,UAAU,GAAG,EAAE;AACnC,mBAAW,IAAI,UAAU,IAAI,KAAK,iBAAiB,OAAO,GAAG,EAAE;AAC/D,mBAAW,KAAK,IAAI,QAAQ,EAAG,IAAG,KAAK,CAAC;AAAA,MAC1C;AACA,aAAO,GAAG,UAAU,YAAY;AAEhC,YAAM,mBAAmB,OAAO,oBAAoB,SAAS,GAAG,IAAI;AACpE,aAAO,GAAG,UAAU,gBAAgB;AACpC,iBAAW,GAAG,KAAK,IAAI,GAAG,mBAAmB,GAAG,MAAM,GAAG,EAAE;AAC3D,iBAAW,IAAI,IAAI,GAAG,SAAS,KAAK,GAAG,EAAE;AACzC,aAAO,GAAG,SAAS,KAAK,CAAC;AAEzB,eAAS,UAAU,KAAM,GAAG,SAAS,kBAAkB,WAAW,MAAO,GAAM,YAAW,SAAS,GAAG,EAAE;AAExG,YAAM,gBAAgB,CAAC;AACvB,aAAO,cAAc,SAAS,IAAI,GAAG,OAAQ,eAAc,KAAK,CAAC;AACjE,SAAG,QAAQ,CAAC,GAAG,MAAM,cAAc,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI,EAAE;AAE/D,aAAO,IAAI,OAAO,SAAS,KAAK,eAAe,IAAI;AAAA,IACrD;AAAA;AAAA;AAAA;AAAA,IAIA;AAAA;AAAA;AAAA;AAAA,IAIA;AAAA;AAAA;AAAA,IAGA,UAAU,CAAC;AAAA;AAAA,IAEX,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMd,YAGA,SAEA,sBAAsB,eAAe,KAAK;AACxC,WAAK,UAAU;AACf,WAAK,uBAAuB;AAE5B,UAAI,UAAU,OAAO,eAAe,UAAU,OAAO,YAAa,OAAM,IAAI,WAAW,4BAA4B;AACnH,UAAI,MAAM,MAAM,MAAM,EAAG,OAAM,IAAI,WAAW,yBAAyB;AACvE,WAAK,OAAO,UAAU,IAAI;AAE1B,YAAM,MAAM,CAAC;AACb,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,IAAK,KAAI,KAAK,KAAK;AAClD,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,aAAK,QAAQ,KAAK,IAAI,MAAM,CAAC;AAC7B,aAAK,WAAW,KAAK,IAAI,MAAM,CAAC;AAAA,MAClC;AAEA,WAAK,qBAAqB;AAC1B,YAAM,eAAe,KAAK,oBAAoB,aAAa;AAC3D,WAAK,cAAc,YAAY;AAE/B,UAAI,OAAO,IAAI;AAEb,YAAI,aAAa;AACjB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAK,UAAU,CAAC;AAChB,eAAK,eAAe,CAAC;AACrB,gBAAM,UAAU,KAAK,gBAAgB;AACrC,cAAI,UAAU,YAAY;AACxB,kBAAM;AACN,yBAAa;AAAA,UACf;AACA,eAAK,UAAU,CAAC;AAAA,QAClB;AAAA,MACF;AACA,aAAO,OAAO,KAAK,OAAO,CAAC;AAC3B,WAAK,OAAO;AACZ,WAAK,UAAU,GAAG;AAClB,WAAK,eAAe,GAAG;AACvB,WAAK,aAAa,CAAC;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,UAAU,GAAG,GAAG;AACd,aAAO,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,CAAC,EAAE,CAAC;AAAA,IAChF;AAAA;AAAA;AAAA,IAGA,aAAa;AACX,aAAO,KAAK;AAAA,IACd;AAAA;AAAA;AAAA,IAGA,uBAAuB;AAErB,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,aAAK,kBAAkB,GAAG,GAAG,IAAI,KAAK,CAAC;AACvC,aAAK,kBAAkB,GAAG,GAAG,IAAI,KAAK,CAAC;AAAA,MACzC;AAEA,WAAK,kBAAkB,GAAG,CAAC;AAC3B,WAAK,kBAAkB,KAAK,OAAO,GAAG,CAAC;AACvC,WAAK,kBAAkB,GAAG,KAAK,OAAO,CAAC;AAEvC,YAAM,cAAc,KAAK,6BAA6B;AACtD,YAAM,WAAW,YAAY;AAC7B,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAEjC,cAAI,EAAE,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,WAAW,KAAK,KAAK,GAAI,MAAK,qBAAqB,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAAA,QACjJ;AAAA,MACF;AAEA,WAAK,eAAe,CAAC;AACrB,WAAK,YAAY;AAAA,IACnB;AAAA;AAAA;AAAA,IAGA,eAAe,MAAM;AAEnB,YAAM,OAAO,KAAK,qBAAqB,cAAc,IAAI;AACzD,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,IAAI,IAAK,OAAM,OAAO,KAAK,QAAQ,KAAK;AAC5D,YAAM,QAAQ,QAAQ,KAAK,OAAO;AAClC,aAAO,SAAS,MAAM,CAAC;AAEvB,eAAS,IAAI,GAAG,KAAK,GAAG,IAAK,MAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AACzE,WAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC5C,WAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC5C,WAAK,kBAAkB,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAC5C,eAAS,IAAI,GAAG,IAAI,IAAI,IAAK,MAAK,kBAAkB,KAAK,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAE9E,eAAS,IAAI,GAAG,IAAI,GAAG,IAAK,MAAK,kBAAkB,KAAK,OAAO,IAAI,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AACxF,eAAS,IAAI,GAAG,IAAI,IAAI,IAAK,MAAK,kBAAkB,GAAG,KAAK,OAAO,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC;AAC1F,WAAK,kBAAkB,GAAG,KAAK,OAAO,GAAG,IAAI;AAAA,IAC/C;AAAA;AAAA;AAAA,IAGA,cAAc;AACZ,UAAI,KAAK,UAAU,EAAG;AAEtB,UAAI,MAAM,KAAK;AACf,eAAS,IAAI,GAAG,IAAI,IAAI,IAAK,OAAM,OAAO,KAAK,QAAQ,MAAM;AAC7D,YAAM,OAAO,KAAK,WAAW,KAAK;AAClC,aAAO,SAAS,MAAM,CAAC;AAEvB,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,QAAQ,OAAO,MAAM,CAAC;AAC5B,cAAM,IAAI,KAAK,OAAO,KAAK,IAAI;AAC/B,cAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,aAAK,kBAAkB,GAAG,GAAG,KAAK;AAClC,aAAK,kBAAkB,GAAG,GAAG,KAAK;AAAA,MACpC;AAAA,IACF;AAAA;AAAA;AAAA,IAGA,kBAAkB,GAAG,GAAG;AACtB,eAAS,KAAK,IAAI,MAAM,GAAG,MAAM;AAC/B,iBAAS,KAAK,IAAI,MAAM,GAAG,MAAM;AAC/B,gBAAM,OAAO,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AAChD,gBAAM,KAAK,IAAI;AACf,gBAAM,KAAK,IAAI;AACf,cAAI,MAAM,KAAK,KAAK,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,KAAM,MAAK,kBAAkB,IAAI,IAAI,QAAQ,KAAK,QAAQ,CAAC;AAAA,QACnH;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA,IAGA,qBAAqB,GAAG,GAAG;AACzB,eAAS,KAAK,IAAI,MAAM,GAAG,MAAM;AAC/B,iBAAS,KAAK,IAAI,MAAM,GAAG,KAAM,MAAK,kBAAkB,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC;AAAA,MACnH;AAAA,IACF;AAAA;AAAA;AAAA,IAGA,kBAAkB,GAAG,GAAG,QAAQ;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,IAAI;AACrB,WAAK,WAAW,CAAC,EAAE,CAAC,IAAI;AAAA,IAC1B;AAAA;AAAA;AAAA;AAAA,IAIA,oBAAoB,MAAM;AACxB,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK;AACjB,UAAI,KAAK,UAAU,OAAO,oBAAoB,KAAK,GAAG,EAAG,OAAM,IAAI,WAAW,kBAAkB;AAEhG,YAAM,YAAY,OAAO,4BAA4B,IAAI,OAAO,EAAE,GAAG;AACrE,YAAM,cAAc,OAAO,wBAAwB,IAAI,OAAO,EAAE,GAAG;AACnE,YAAM,eAAe,KAAK,MAAM,OAAO,qBAAqB,GAAG,IAAI,CAAC;AACpE,YAAM,iBAAiB,YAAY,eAAe;AAClD,YAAM,gBAAgB,KAAK,MAAM,eAAe,SAAS;AAEzD,YAAM,SAAS,CAAC;AAChB,YAAM,QAAQ,OAAO,0BAA0B,WAAW;AAC1D,eAAS,IAAI,GAAG,IAAI,GAAG,IAAI,WAAW,KAAK;AACzC,cAAM,MAAM,KAAK,MAAM,GAAG,IAAI,gBAAgB,eAAe,IAAI,iBAAiB,IAAI,EAAE;AACxF,aAAK,IAAI;AACT,cAAM,MAAM,OAAO,4BAA4B,KAAK,KAAK;AACzD,YAAI,IAAI,eAAgB,KAAI,KAAK,CAAC;AAClC,eAAO,KAAK,IAAI,OAAO,GAAG,CAAC;AAAA,MAC7B;AAEA,YAAM,SAAS,CAAC;AAChB,eAAS,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK;AACzC,eAAO,QAAQ,CAAC,OAAO,MAAM;AAE3B,cAAI,KAAK,gBAAgB,eAAe,KAAK,eAAgB,QAAO,KAAK,MAAM,CAAC,CAAC;AAAA,QACnF,CAAC;AAAA,MACH;AACA,aAAO,OAAO,UAAU,YAAY;AACpC,aAAO;AAAA,IACT;AAAA;AAAA;AAAA,IAGA,cAAc,MAAM;AAClB,UAAI,KAAK,UAAU,KAAK,MAAM,OAAO,qBAAqB,KAAK,OAAO,IAAI,CAAC,EAAG,OAAM,IAAI,WAAW,kBAAkB;AACrH,UAAI,IAAI;AAER,eAAS,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG;AAEtD,YAAI,SAAS,EAAG,SAAQ;AACxB,iBAAS,OAAO,GAAG,OAAO,KAAK,MAAM,QAAQ;AAE3C,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAM,IAAI,QAAQ;AAClB,kBAAM,UAAU,QAAQ,IAAI,MAAM;AAClC,kBAAM,IAAI,SAAS,KAAK,OAAO,IAAI,OAAO;AAC1C,gBAAI,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,SAAS,GAAG;AACjD,mBAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,GAAG,KAAK,IAAI,EAAE;AACtD;AAAA,YACF;AAAA,UAGF;AAAA,QACF;AAAA,MACF;AACA,aAAO,KAAK,KAAK,SAAS,CAAC;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,UAAU,MAAM;AACd,UAAI,OAAO,KAAK,OAAO,EAAG,OAAM,IAAI,WAAW,yBAAyB;AACxE,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,cAAI;AACJ,kBAAQ,MAAM;AAAA,YACZ,KAAK;AACH,wBAAU,IAAI,KAAK,KAAK;AACxB;AAAA,YACF,KAAK;AACH,uBAAS,IAAI,KAAK;AAClB;AAAA,YACF,KAAK;AACH,uBAAS,IAAI,KAAK;AAClB;AAAA,YACF,KAAK;AACH,wBAAU,IAAI,KAAK,KAAK;AACxB;AAAA,YACF,KAAK;AACH,wBAAU,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK;AACxD;AAAA,YACF,KAAK;AACH,uBAAS,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAClC;AAAA,YACF,KAAK;AACH,wBAAU,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK;AACxC;AAAA,YACF,KAAK;AACH,yBAAW,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK;AAC1C;AAAA,YACF;AACE,oBAAM,IAAI,MAAM,aAAa;AAAA,UACjC;AACA,cAAI,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,KAAK,OAAQ,MAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,EAAE,CAAC;AAAA,QAC/E;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA,IAGA,kBAAkB;AAChB,UAAI,SAAS;AAEb,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,YAAI,WAAW;AACf,YAAI,OAAO;AACX,cAAM,aAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACvC,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,cAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,UAAU;AAClC;AACA,gBAAI,QAAQ,EAAG,WAAU,OAAO;AAAA,qBAAoB,OAAO,EAAG;AAAA,UAChE,OAAO;AACL,iBAAK,wBAAwB,MAAM,UAAU;AAC7C,gBAAI,CAAC,SAAU,WAAU,KAAK,2BAA2B,UAAU,IAAI,OAAO;AAC9E,uBAAW,KAAK,QAAQ,CAAC,EAAE,CAAC;AAC5B,mBAAO;AAAA,UACT;AAAA,QACF;AACA,kBAAU,KAAK,+BAA+B,UAAU,MAAM,UAAU,IAAI,OAAO;AAAA,MACrF;AAEA,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,YAAI,WAAW;AACf,YAAI,OAAO;AACX,cAAM,aAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACvC,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,cAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,UAAU;AAClC;AACA,gBAAI,QAAQ,EAAG,WAAU,OAAO;AAAA,qBAAoB,OAAO,EAAG;AAAA,UAChE,OAAO;AACL,iBAAK,wBAAwB,MAAM,UAAU;AAC7C,gBAAI,CAAC,SAAU,WAAU,KAAK,2BAA2B,UAAU,IAAI,OAAO;AAC9E,uBAAW,KAAK,QAAQ,CAAC,EAAE,CAAC;AAC5B,mBAAO;AAAA,UACT;AAAA,QACF;AACA,kBAAU,KAAK,+BAA+B,UAAU,MAAM,UAAU,IAAI,OAAO;AAAA,MACrF;AAEA,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,GAAG,KAAK;AACtC,iBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,GAAG,KAAK;AACtC,gBAAM,QAAQ,KAAK,QAAQ,CAAC,EAAE,CAAC;AAC/B,cAAI,SAAS,KAAK,QAAQ,CAAC,EAAE,IAAI,CAAC,KAAK,SAAS,KAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,KAAK,SAAS,KAAK,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC,EAAG,WAAU,OAAO;AAAA,QAClI;AAAA,MACF;AAEA,UAAI,OAAO;AACX,iBAAW,OAAO,KAAK,QAAS,QAAO,IAAI,OAAO,CAAC,KAAK,UAAU,OAAO,QAAQ,IAAI,IAAI,IAAI;AAC7F,YAAM,QAAQ,KAAK,OAAO,KAAK;AAE/B,YAAM,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,QAAQ,EAAE,IAAI,KAAK,IAAI;AAChE,aAAO,KAAK,KAAK,KAAK,CAAC;AACvB,gBAAU,IAAI,OAAO;AACrB,aAAO,UAAU,KAAK,UAAU,OAAO;AACvC,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,+BAA+B;AAC7B,UAAI,KAAK,WAAW,EAAG,QAAO,CAAC;AAAA,WAAO;AACpC,cAAM,WAAW,KAAK,MAAM,KAAK,UAAU,CAAC,IAAI;AAChD,cAAM,OAAO,KAAK,WAAW,KAAK,KAAK,KAAK,MAAM,KAAK,UAAU,IAAI,MAAM,WAAW,IAAI,EAAE,IAAI;AAChG,cAAM,SAAS,CAAC,CAAC;AACjB,iBAAS,MAAM,KAAK,OAAO,GAAG,OAAO,SAAS,UAAU,OAAO,KAAM,QAAO,OAAO,GAAG,GAAG,GAAG;AAC5F,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO,qBAAqB,KAAK;AAC/B,UAAI,MAAM,OAAO,eAAe,MAAM,OAAO,YAAa,OAAM,IAAI,WAAW,6BAA6B;AAC5G,UAAI,UAAU,KAAK,MAAM,OAAO,MAAM;AACtC,UAAI,OAAO,GAAG;AACZ,cAAM,WAAW,KAAK,MAAM,MAAM,CAAC,IAAI;AACvC,mBAAW,KAAK,WAAW,MAAM,WAAW;AAC5C,YAAI,OAAO,EAAG,WAAU;AAAA,MAC1B;AACA,aAAO,UAAU,OAAO,UAAU,KAAK;AACvC,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO,oBAAoB,KAAK,KAAK;AACnC,aAAO,KAAK,MAAM,OAAO,qBAAqB,GAAG,IAAI,CAAC,IAAI,OAAO,wBAAwB,IAAI,OAAO,EAAE,GAAG,IAAI,OAAO,4BAA4B,IAAI,OAAO,EAAE,GAAG;AAAA,IAClK;AAAA;AAAA;AAAA,IAGA,OAAO,0BAA0B,QAAQ;AACvC,UAAI,SAAS,KAAK,SAAS,IAAK,OAAM,IAAI,WAAW,qBAAqB;AAG1E,YAAM,SAAS,CAAC;AAChB,eAAS,IAAI,GAAG,IAAI,SAAS,GAAG,IAAK,QAAO,KAAK,CAAC;AAClD,aAAO,KAAK,CAAC;AAIb,UAAI,OAAO;AACX,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAE/B,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,iBAAO,CAAC,IAAI,OAAO,oBAAoB,OAAO,CAAC,GAAG,IAAI;AACtD,cAAI,IAAI,IAAI,OAAO,OAAQ,QAAO,CAAC,KAAK,OAAO,IAAI,CAAC;AAAA,QACtD;AACA,eAAO,OAAO,oBAAoB,MAAM,CAAI;AAAA,MAC9C;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAEA,OAAO,4BAA4B,MAAM,SAAS;AAChD,YAAM,SAAS,QAAQ,IAAI,OAAK,CAAC;AACjC,iBAAW,KAAK,MAAM;AAEpB,cAAM,SAAS,IAAI,OAAO,MAAM;AAChC,eAAO,KAAK,CAAC;AACb,gBAAQ,QAAQ,CAAC,MAAM,MAAM,OAAO,CAAC,KAAK,OAAO,oBAAoB,MAAM,MAAM,CAAC;AAAA,MACpF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA,IAGA,OAAO,oBAAoB,GAAG,GAAG;AAC/B,UAAI,MAAM,KAAK,KAAK,MAAM,KAAK,EAAG,OAAM,IAAI,WAAW,mBAAmB;AAE1E,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,YAAI,KAAK,KAAK,MAAM,KAAK;AACzB,cAAM,MAAM,IAAI,KAAK;AAAA,MACvB;AACA,aAAO,MAAM,KAAK,CAAC;AACnB,aAAO;AAAA,IACT;AAAA;AAAA;AAAA,IAGA,2BAA2B,YAAY;AACrC,YAAM,IAAI,WAAW,CAAC;AACtB,aAAO,KAAK,KAAK,OAAO,CAAC;AACzB,YAAM,OAAO,IAAI,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK;AAC7G,cAAQ,QAAQ,WAAW,CAAC,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,IAAI,IAAI,MAAM,QAAQ,WAAW,CAAC,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,IAAI,IAAI;AAAA,IACtI;AAAA;AAAA,IAEA,+BAA+B,iBAAiB,kBAAkB,YAAY;AAC5E,UAAI,iBAAiB;AAEnB,aAAK,wBAAwB,kBAAkB,UAAU;AACzD,2BAAmB;AAAA,MACrB;AACA,0BAAoB,KAAK;AACzB,WAAK,wBAAwB,kBAAkB,UAAU;AACzD,aAAO,KAAK,2BAA2B,UAAU;AAAA,IACnD;AAAA;AAAA,IAEA,wBAAwB,kBAAkB,YAAY;AACpD,UAAI,WAAW,CAAC,KAAK,EAAG,qBAAoB,KAAK;AACjD,iBAAW,IAAI;AACf,iBAAW,QAAQ,gBAAgB;AAAA,IACrC;AAAA;AAAA;AAAA,IAGA,OAAO,cAAc;AAAA;AAAA,IAErB,OAAO,cAAc;AAAA;AAAA,IAErB,OAAO,aAAa;AAAA,IACpB,OAAO,aAAa;AAAA,IACpB,OAAO,aAAa;AAAA,IACpB,OAAO,aAAa;AAAA,IACpB,OAAO,0BAA0B;AAAA;AAAA;AAAA,MAGjC,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAElK,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAEnK,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAEnK,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,IACnK;AAAA,IACA,OAAO,8BAA8B;AAAA;AAAA;AAAA,MAGrC,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAE5I,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAErJ,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAExJ,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,IACzJ;AAAA,EACF;AACA,EAAAD,WAAU,SAAS;AAGnB,WAAS,WAAW,KAAK,KAAK,IAAI;AAChC,QAAI,MAAM,KAAK,MAAM,MAAM,QAAQ,OAAO,EAAG,OAAM,IAAI,WAAW,oBAAoB;AACtF,aAAS,IAAI,MAAM,GAAG,KAAK,GAAG,IAC5B,IAAG,KAAK,QAAQ,IAAI,CAAC;AAAA,EACzB;AAEA,WAAS,OAAO,GAAG,GAAG;AACpB,YAAQ,MAAM,IAAI,MAAM;AAAA,EAC1B;AAEA,WAAS,OAAO,MAAM;AACpB,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,iBAAiB;AAAA,EAC9C;AAAA,EAaA,MAAM,UAAU;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO,UAAU,MAAM;AACrB,YAAM,KAAK,CAAC;AACZ,iBAAW,KAAK,KAAM,YAAW,GAAG,GAAG,EAAE;AACzC,aAAO,IAAI,UAAU,UAAU,KAAK,MAAM,KAAK,QAAQ,EAAE;AAAA,IAC3D;AAAA;AAAA,IAEA,OAAO,YAAY,QAAQ;AACzB,UAAI,CAAC,UAAU,UAAU,MAAM,EAAG,OAAM,IAAI,WAAW,wCAAwC;AAC/F,YAAM,KAAK,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,OAAO,UAAS;AAElC,cAAM,IAAI,KAAK,IAAI,OAAO,SAAS,GAAG,CAAC;AACvC,mBAAW,SAAS,OAAO,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,GAAG,EAAE;AAClE,aAAK;AAAA,MACP;AACA,aAAO,IAAI,UAAU,UAAU,KAAK,SAAS,OAAO,QAAQ,EAAE;AAAA,IAChE;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO,iBAAiB,MAAM;AAC5B,UAAI,CAAC,UAAU,eAAe,IAAI,EAAG,OAAM,IAAI,WAAW,6DAA6D;AACvH,YAAM,KAAK,CAAC;AACZ,UAAI;AACJ,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK,GAAG;AAExC,YAAI,OAAO,UAAU,qBAAqB,QAAQ,KAAK,OAAO,CAAC,CAAC,IAAI;AACpE,gBAAQ,UAAU,qBAAqB,QAAQ,KAAK,OAAO,IAAI,CAAC,CAAC;AACjE,mBAAW,MAAM,IAAI,EAAE;AAAA,MACzB;AACA,UAAI,IAAI,KAAK;AAEX,mBAAW,UAAU,qBAAqB,QAAQ,KAAK,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE;AAC1E,aAAO,IAAI,UAAU,UAAU,KAAK,cAAc,KAAK,QAAQ,EAAE;AAAA,IACnE;AAAA;AAAA;AAAA,IAGA,OAAO,aAAa,MAAM;AAExB,UAAI,QAAQ,GAAI,QAAO,CAAC;AAAA,eAAW,UAAU,UAAU,IAAI,EAAG,QAAO,CAAC,UAAU,YAAY,IAAI,CAAC;AAAA,eAAW,UAAU,eAAe,IAAI,EAAG,QAAO,CAAC,UAAU,iBAAiB,IAAI,CAAC;AAAA,UAAO,QAAO,CAAC,UAAU,UAAU,UAAU,gBAAgB,IAAI,CAAC,CAAC;AAAA,IACzP;AAAA;AAAA;AAAA,IAGA,OAAO,QAAQ,WAAW;AACxB,YAAM,KAAK,CAAC;AACZ,UAAI,YAAY,EAAG,OAAM,IAAI,WAAW,mCAAmC;AAAA,eAAW,YAAY,KAAK,EAAG,YAAW,WAAW,GAAG,EAAE;AAAA,eAAW,YAAY,KAAK,IAAI;AACnK,mBAAW,GAAM,GAAG,EAAE;AACtB,mBAAW,WAAW,IAAI,EAAE;AAAA,MAC9B,WAAW,YAAY,KAAS;AAC9B,mBAAW,GAAO,GAAG,EAAE;AACvB,mBAAW,WAAW,IAAI,EAAE;AAAA,MAC9B,MAAO,OAAM,IAAI,WAAW,mCAAmC;AAC/D,aAAO,IAAI,UAAU,UAAU,KAAK,KAAK,GAAG,EAAE;AAAA,IAChD;AAAA;AAAA;AAAA,IAGA,OAAO,UAAU,MAAM;AACrB,aAAO,UAAU,cAAc,KAAK,IAAI;AAAA,IAC1C;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO,eAAe,MAAM;AAC1B,aAAO,UAAU,mBAAmB,KAAK,IAAI;AAAA,IAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,YAEA,MAIA,UAEA,SAAS;AACP,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,UAAI,WAAW,EAAG,OAAM,IAAI,WAAW,kBAAkB;AACzD,WAAK,UAAU,QAAQ,MAAM;AAAA,IAC/B;AAAA;AAAA;AAAA,IAGA,UAAU;AACR,aAAO,KAAK,QAAQ,MAAM;AAAA,IAC5B;AAAA;AAAA;AAAA,IAGA,OAAO,aAAa,MAAM,SAAS;AACjC,UAAI,SAAS;AACb,iBAAW,OAAO,MAAM;AACtB,cAAM,SAAS,IAAI,KAAK,iBAAiB,OAAO;AAChD,YAAI,IAAI,YAAY,KAAK,OAAQ,QAAO;AACxC,kBAAU,IAAI,SAAS,IAAI,QAAQ;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAEA,OAAO,gBAAgB,KAAK;AAC1B,YAAM,UAAU,GAAG;AACnB,YAAM,SAAS,CAAC;AAChB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,IAAI,OAAO,CAAC,KAAK,IAAK,QAAO,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,aAAO;AAC5D,iBAAO,KAAK,SAAS,IAAI,UAAU,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACrD,eAAK;AAAA,QACP;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA,IAGA,OAAO,gBAAgB;AAAA;AAAA,IAEvB,OAAO,qBAAqB;AAAA;AAAA;AAAA,IAG5B,OAAO,uBAAuB;AAAA,EAChC;AACA,EAAAA,WAAU,YAAY;AACxB,GAAG,cAAc,YAAY,CAAC,EAAE;AAAA,CAG/B,SAAUA,YAAW;AACpB,MAAI;AACJ,GAAC,SAAUE,SAAQ;AAAA,IAIjB,MAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA;AAAA,MAEA,OAAO,MAAM,IAAI,IAAI,GAAG,CAAC;AAAA;AAAA,MACzB,OAAO,SAAS,IAAI,IAAI,GAAG,CAAC;AAAA;AAAA,MAC5B,OAAO,WAAW,IAAI,IAAI,GAAG,CAAC;AAAA;AAAA,MAC9B,OAAO,OAAO,IAAI,IAAI,GAAG,CAAC;AAAA;AAAA;AAAA,MAE1B,YAEA,SAEA,YAAY;AACV,aAAK,UAAU;AACf,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AACA,IAAAA,QAAO,MAAM;AAAA,EACf,GAAG,SAASF,WAAU,WAAWA,WAAU,SAAS,CAAC,EAAE;AACzD,GAAG,cAAc,YAAY,CAAC,EAAE;AAAA,CAG/B,SAAUA,YAAW;AACpB,MAAI;AACJ,GAAC,SAAUG,YAAW;AAAA,IAIpB,MAAM,KAAK;AAAA,MACT;AAAA,MACA;AAAA;AAAA,MAEA,OAAO,UAAU,IAAI,KAAK,GAAK,CAAC,IAAI,IAAI,EAAE,CAAC;AAAA,MAC3C,OAAO,eAAe,IAAI,KAAK,GAAK,CAAC,GAAG,IAAI,EAAE,CAAC;AAAA,MAC/C,OAAO,OAAO,IAAI,KAAK,GAAK,CAAC,GAAG,IAAI,EAAE,CAAC;AAAA,MACvC,OAAO,QAAQ,IAAI,KAAK,GAAK,CAAC,GAAG,IAAI,EAAE,CAAC;AAAA,MACxC,OAAO,MAAM,IAAI,KAAK,GAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA;AAAA,MAEpC,YAEA,UAEA,kBAAkB;AAChB,aAAK,WAAW;AAChB,aAAK,mBAAmB;AAAA,MAC1B;AAAA;AAAA;AAAA;AAAA,MAIA,iBAAiB,KAAK;AACpB,eAAO,KAAK,iBAAiB,KAAK,OAAO,MAAM,KAAK,EAAE,CAAC;AAAA,MACzD;AAAA,IACF;AACA,IAAAA,WAAU,OAAO;AAAA,EACnB,GAAG,YAAYH,WAAU,cAAcA,WAAU,YAAY,CAAC,EAAE;AAClE,GAAG,cAAc,YAAY,CAAC,EAAE;AAEhC,IAAI,cAAc;AAMlB,IAAM,kBAAkB;AAAA,EACtB,GAAG,YAAY,OAAO,IAAI;AAAA,EAC1B,GAAG,YAAY,OAAO,IAAI;AAAA,EAC1B,GAAG,YAAY,OAAO,IAAI;AAAA,EAC1B,GAAG,YAAY,OAAO,IAAI;AAC5B;AACA,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,gBAAgB;AACtB,IAAM,2BAA2B;AACjC,IAAM,mBAAmB;AACzB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB,CAAC,OAAO,QAAQ,kBAAkB;AACvD,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,YAAY,OAAO,WAAW,OAAO,gBAAgB,KAAK,CAAC;AACpE;AACA,SAAS,WAAW,QAAQ,OAAO,OAAO,cAAc,QAAQ,eAAe,UAAU,iBAAiB,QAAQ,eAAe,kBAAkB,0BAA0B,WAAW,kBAAkB,MAAM;AAC9M,QAAM,MAAM,OAAO,WAAW,IAAI;AAClC,QAAM,mBAAmB,cAAc,OAAO;AAC9C,SAAO,MAAM,QAAQ,GAAG,IAAI;AAC5B,SAAO,MAAM,SAAS,GAAG,IAAI;AAC7B,MAAI,CAAC,OAAO;AACV,QAAI,YAAY;AAChB,QAAI,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC9C;AAAA,EACF;AACA,SAAO,QAAQ,MAAM,OAAO,QAAQ,iBAAiB,CAAC,IAAI,iBAAiB,CAAC;AAC5E,SAAO,SAAS,MAAM,OAAO,QAAQ,iBAAiB,CAAC,IAAI,iBAAiB,CAAC;AAC7E,MAAI,CAAC,MAAM;AACT,yBAAqB,KAAK,OAAO,OAAO,OAAO,QAAQ,OAAO,eAAe;AAC7E,oBAAgB,KAAK,OAAO,OAAO,kBAAkB,iBAAiB,KAAK;AAAA,EAC7E,OAAO;AACL,UAAM,UAAU,IAAI,MAAM;AAC1B,YAAQ,MAAM;AACd,YAAQ,cAAc;AACtB,YAAQ,QAAQ,YAAY,OAAO,QAAQ;AAC3C,YAAQ,SAAS,YAAY,OAAO,QAAQ;AAC5C,UAAM,SAAS,MAAM;AACnB,cAAQ;AACR,2BAAqB,KAAK,OAAO,OAAO,OAAO,QAAQ,OAAO,eAAe;AAC7E,sBAAgB,KAAK,OAAO,OAAO,kBAAkB,iBAAiB,KAAK;AAC3E,YAAM,iBAAiB,OAAO,QAAQ,IAAI,YAAY,OAAO,QAAQ,QAAQ;AAC7E,UAAI,SAAS,gBAAgB,gBAAgB,YAAY,OAAO,QAAQ,OAAO,YAAY,OAAO,QAAQ,KAAK;AAC/G,UAAI,UAAU,SAAS,gBAAgB,gBAAgB,YAAY,OAAO,QAAQ,OAAO,YAAY,OAAO,QAAQ,KAAK;AAAA,IAC3H;AACA,UAAM,UAAU,MAAM;AACpB,cAAQ;AACR,2BAAqB,KAAK,OAAO,OAAO,OAAO,QAAQ,OAAO,eAAe;AAC7E,sBAAgB,KAAK,OAAO,OAAO,kBAAkB,iBAAiB,KAAK;AAAA,IAC7E;AACA,UAAM,UAAU,MAAM;AACpB,cAAQ,oBAAoB,QAAQ,MAAM;AAC1C,cAAQ,oBAAoB,SAAS,OAAO;AAAA,IAC9C;AACA,YAAQ,iBAAiB,QAAQ,MAAM;AACvC,YAAQ,iBAAiB,SAAS,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,gBAAgB,KAAK,OAAO,OAAO,SAAS,iBAAiB,OAAO;AAC3E,WAAS,IAAI,GAAG,IAAI,MAAM,MAAM,KAAK;AACnC,aAAS,IAAI,GAAG,IAAI,MAAM,MAAM,KAAK;AACnC,UAAI,YAAY,MAAM,UAAU,GAAG,CAAC,IAAI,QAAQ;AAChD,UAAI,SAAS,QAAQ,CAAC,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,IAAI,OAAO,OAAO,KAAK;AAAA,IAC3E;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,KAAK,OAAO,QAAQ,OAAO,iBAAiB;AACxE,MAAI,YAAY;AAChB,MAAI,SAAS,GAAG,GAAG,QAAQ,OAAO,SAAS,KAAK;AAClD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,MAAM,QAAQ,OAAO,GAAG;AAE1B,WAAO,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU,QAAQ,QAAQ,QAAQ,MAAM,CAAC;AAAA,EAC3E,OAAO;AACL,WAAO,CAAC,SAAS,SAAS,SAAS,OAAO;AAAA,EAC5C;AACF;AAMA,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,YAAY,IAAI,aAAa;AAAA,EAC7B;AAAA;AAAA;AAAA,EAGA,YAAY;AAAA,EACZ,WAAW,IAAI,QAAQ;AAAA,EACvB,aAAa,OAAO,WAAW;AAAA,EAC/B,YAAY,MAAM,IAAI,KAAK;AACzB,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,MAAM;AACX,SAAK,YAAY,kBAAkB,KAAK,UAAU;AAClD,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,GAAG,cAAc,MAAM,kBAAkB,KAAK;AACnD,SAAK,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpE,WAAK,SAAS,KAAK,KAAK,cAAc,QAAQ;AAC9C,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,WAAW,UAAU,WAAW,UAAU,cAAc,WAAW,aAAa,cAAc,KAAK,QAAQ;AAC9G,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,WAAW;AACb,WAAK,GAAG,cAAc,MAAM,kBAAkB,KAAK;AAAA,IACrD;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,eAAe;AACb,SAAK,iBAAiB;AACtB,SAAK,UAAU,KAAK,SAAS;AAAA,EAC/B;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,QAAQ;AACf,iBAAW,KAAK,OAAO,eAAe,eAAe,KAAK,SAAS,KAAK,OAAO,GAAG,KAAK,QAAQ,IAAI,KAAK,WAAW,KAAK,SAAS,KAAK,WAAW,KAAK,YAAY,KAAK,MAAM;AAAA,IAC/K;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAsB,kBAAqB,aAAa,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,EAC7K;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,YAAY;AAAA,IAC3B,UAAU;AAAA,IACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,qBAAqB,IAAI,UAAU;AAAA,MACpD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,SAAS;AAAA,MACT,WAAW;AAAA,MACX,QAAQ,CAAC,GAAG,UAAU,UAAU,eAAe;AAAA,MAC/C,QAAQ;AAAA,MACR,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,UAAU;AAAA,MACV,SAAS;AAAA,MACT,gBAAgB;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAC,UAAU;AAAA,IACrB,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,aAAa,IAAI,UAAU,QAAQ,GAAG,OAAO,GAAG,CAAC,UAAU,UAAU,WAAW,SAAS,CAAC;AAAA,IACtM,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,QAAQ;AAAA,MACrM;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,CAAC,CAAC,IAAI,iBAAiB,IAAI,IAAI,aAAa,WAAW,IAAI,EAAE;AAC9E,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,YAAY,IAAI,EAAE;AAAA,MACzC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,iBAAiB,gBAAmB,mBAAsB,4BAA6B,cAAiB,iBAAiB,+BAA+B;AAAA,IACxL,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiCV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,6BAA6B;AAAA,MAC/B;AAAA,MACA,SAAS,CAAC,cAAc,gBAAgB,cAAc,+BAA+B;AAAA,IACvF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB;AAAA,IAC3B,SAAS,CAAC,iBAAiB;AAAA,EAC7B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB;AAAA,EAC7B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB;AAAA,MAC3B,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["qrcodegen", "dataCapacityBits", "QrCode", "QrSegment"]}