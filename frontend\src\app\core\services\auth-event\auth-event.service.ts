import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { AuthEventType, EventConfirmType } from '@share-types/auth-event.types';

import { HttpClient } from '@angular/common/http';
import { ServerTimeService } from '../server-time.service';
import { Res } from '@share-types/center.types';

type ScheduleId = string;
type EventId = string;

export interface EventRes {
  event_id: string;
  type: AuthEventType;
  data: string;
}
const msgType = {
  waiting: 'info',
  confirmed: 'info',
  disagreed: 'error',
  agreed: 'success',
};

export class AuthEvent<T = any> {
  type: AuthEventType;
  event_id: string;
  schedule_id: string;
  confirm: EventConfirmType = 'waiting';
  detail: T;
  offline_data: string;
  created_at: number; // ms
  constructor(event_id: string, type: AuthEventType, schedule_id: string) {
    this.event_id = event_id;
    this.schedule_id = schedule_id;
    this.type = type;
  }
}

@Injectable({
  providedIn: 'root',
})
export class AuthEventService {
  private authEvents = new Map<ScheduleId, AuthEvent[]>();
  private _eventMsg$ = new Subject<AuthEvent>();

  constructor(private http: HttpClient, private serverTime: ServerTimeService) {
    // this.socket.clientIO.onMsg(M.MSG_AUTH_EVENT, (req) => {
    //   const { msg, params } = req;
    //   const { type, confirm, event_id, err } = params;
    //   const event = this.getAuthByEventId(event_id);
    //   event.confirm = confirm;
    //   if (confirm === "agreed") {
    //     if (event.entry_id) {
    //       this.refreshNotice$.next("entry");
    //     } else {
    //       this.refreshNotice$.next("session");
    //     }
    //   }
    //   if (err) {
    //     this._alertSvc.open(this.translate.instant(err) || err, this.getTranslate(type));
    //   }
    //   this._eventMsg$.next(event);
    //   this.messageService.createNotice(
    //     msgType[confirm],
    //     `${this.getTranslate(type)}`,
    //     `${this.translate.instant("authEvent.authType")}: ${this.translate.instant("authEvent." + confirm)}`
    //   );
    // });
  }

  get eventMsg$() {
    return this._eventMsg$.asObservable();
  }

  authCode(event_id: string, code: number) {
    return this.http.post<Res<boolean>>('/data/auth/code', { event_id, code });
  }

  getAuth(id: EventId | ScheduleId, type: AuthEventType) {
    if (id) {
      const auth_event = this.getAuthByEventId(id);
      if (auth_event) {
        return this.checkAuthTimeout(auth_event);
      }
      const events = this.authEvents.get(id) || [];
      for (const event of events) {
        if (event.type === type) {
          return this.checkAuthTimeout(event);
        }
      }
    }
    return null;
  }

  getAuthByType(type: AuthEventType) {
    const result: AuthEvent[] = [];
    for (const events of this.authEvents.values()) {
      const r = events.filter((i) => i.type === type);
      result.push(...r);
    }
    console.log(`get auth event #${type} # ${result.length}`);
    return result;
  }

  /** 考生事件是否待授权 */
  is_auth(id: EventId | ScheduleId, type: AuthEventType) {
    const event = this.getAuth(id, type);
    return event && event.confirm === 'waiting';
  }

  getAuthByEventId(event_id: string) {
    for (const [id, events] of this.authEvents) {
      for (const event of events) {
        if (event.event_id === event_id) {
          return this.checkAuthTimeout(event);
        }
      }
    }
    return null;
  }

  checkAuthTimeout(event: AuthEvent) {
    const is_timeout =
      event.created_at + 15 * 60 * 1000 < this.serverTime.getServerTimeValue(); //  与服务器超时时间保持一致
    if (is_timeout) {
      console.log('warning: auth event timeout', event.event_id);
      this.deleteEvent(event.event_id);
      return null;
    }
    return event;
  }

  addEvent(id: ScheduleId, event: AuthEvent) {
    if (this.authEvents.has(id)) {
      const schedule_events = this.authEvents.get(id);
      if (schedule_events?.length) {
        schedule_events.unshift(event);
      }
    } else {
      this.authEvents.set(id, [event]);
    }
  }

  deleteEvent(event_id: string) {
    const eventsArr = this.authEvents.values();
    for (const events of eventsArr) {
      const i = events.findIndex((e) => e.event_id === event_id);
      if (i !== -1) {
        events.splice(i, 1);
      }
    }
  }

  checkAuth(id: EventId | ScheduleId, type: AuthEventType) {
    const auth_event = this.getAuth(id, type);
    if (auth_event && auth_event.confirm === 'waiting') {
      if (auth_event.offline_data) {
        // this.openQrCodeDialog(this.getTranslate(type), {
        //   event_id: auth_event.event_id,
        //   data: auth_event.offline_data,
        // });
        return false;
      }
      // this._alertSvc.setValue({
      //   status: true,
      //   info: {
      //     bodyText: `${this.getTranslate(type)} ${this.translate.instant(
      //       'authEvent.pleaseWait'
      //     )}`,
      //   },
      // });
      return false;
    }
    return true;
  }

  authHandle(res: Res<EventRes>) {
    if (res.status === 'success') {
      // this.messageService.infoNotice(
      //   this.getTranslate(res.info.type),
      //   this.translate.instant('authEvent.requestSuccess')
      // );
    } else {
      // if (res.info.data) {
      // this.openQrCodeDialog(
      //   this.translate.instant('authEvent.' + res.info.type),
      //   res.info as any
      // );
      // }
    }
  }

  getTranslate(type: AuthEventType) {
    // return this.translate.instant('authEvent.' + type);
  }

  clearEvents(schedule_id: string) {
    this.authEvents.forEach((v, k, m) => {
      if (schedule_id === k || v.some((e) => e.schedule_id === schedule_id)) {
        m.delete(k);
      }
    });
  }
}
