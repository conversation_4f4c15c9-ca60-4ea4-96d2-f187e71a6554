<div class="container">
  <div class="title">考试详情</div>
  <div class="schedule-card">
    <div class="time-range">{{ examTimeRange }}</div>
    <div class="subjects" [title]="subjects">{{ subjects }}</div>
    <div class="project">项目：{{ project.name }}</div>
  </div>
  <div class="tabs-button">
    <nz-segmented
      [nzOptions]="options"
      (nzValueChange)="handleIndexChange($event)"
    ></nz-segmented>
  </div>
  <div class="content">
    @if (current_tab === ETab.Overview) {
    <div class="form-card">
      <div class="header">
        <div class="sub-title">
          <div class="mark"></div>
          试卷上传
        </div>
        <div class="action-buttons">
          @if (!schedule.form_published) {
          <button
            nz-button
            nzType="primary"
            (click)="publishForm()"
            *ngIf="!schedule.form_published"
          >
            试卷发布
          </button>
          } @else {
          <div class="published">
            <span nz-icon nzType="check" nzTheme="outline"></span>
            <span>试卷已发布</span>
          </div>
          } @if (!schedule.password_published) {
          <button
            nz-button
            nzType="primary"
            (click)="publishPassword()"
            *ngIf="!schedule.password_published"
          >
            密码发布
          </button>
          } @else {
          <div class="published">
            <span nz-icon nzType="check" nzTheme="outline"></span>
            <span>密码已发布</span>
          </div>
          }
          <!-- <button
            nz-button
            nzType="primary"
            (click)="uploadFormPackage()"
            *ngIf="is_form_expanded && form_data_set.length"
          >
            <span nz-icon nzType="upload"></span>
            上传试卷包/密码包
          </button> -->
          <app-upload-button
            *ngIf="is_form_expanded && form_data_set.length"
            [scheduleId]="schedule_id"
            (uploadSuccess)="reqFormList()"
          ></app-upload-button>
          <app-expand
            [expanded]="is_form_expanded"
            (expandedChange)="expandChanged('form', $event)"
            *ngIf="form_data_set.length"
          ></app-expand>
        </div>
      </div>
      <nz-divider></nz-divider>
      <div class="upload-button" *ngIf="!form_data_set.length">
        <app-upload-button
          [scheduleId]="schedule_id"
          (uploadSuccess)="reqFormList()"
        ></app-upload-button>
      </div>
      <div class="form-table" *ngIf="is_form_expanded && form_data_set.length">
        <nz-table
          #basicTable
          [nzData]="form_data_set"
          [nzFrontPagination]="false"
          nzSize="small"
        >
          <thead>
            <tr>
              <th>科目</th>
              <th>试卷名称</th>
              <th nzWidth="160px">试卷上传时间</th>
              <th nzWidth="160px">密码上传时间</th>
              <th nzWidth="160px">试卷发布时间</th>
              <th nzWidth="160px">密码发布时间</th>
              <th nzWidth="50px">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let form of basicTable.data">
              <td>{{ form.subject?.name || "无" }}</td>
              <td [title]="'试卷ID: '+form.form_id">{{ form.name }}</td>
              <td>{{ form.form_time | customDate : "date" }}</td>
              <td>{{ form.password_time | customDate : "date" }}</td>
              <td>{{ form.form_publish_time | customDate : "date" }}</td>
              <td>{{ form.password_publish_time | customDate : "date" }}</td>
              <td>
                <button
                  nz-button
                  nzType="text"
                  nzDanger
                  (click)="delForm(form.form_id)"
                >
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </div>
    <div class="exam-card">
      <div class="header">
        <div class="sub-title">
          <div class="mark"></div>
          考中监控
        </div>
        <div class="stat-info">
          <span
            ><img src="assets/images/user-total.svg" alt="" />考生总数：
            {{ entry_count.total }}</span
          >
          <span
            ><img src="assets/images/user-tested.svg" alt="" />参考：
            {{ entry_count.test }}</span
          >
          <span
            ><img src="assets/images/room.svg" alt="" />考场总数：
            {{ rooms.length }}</span
          >
          <span
            ><img src="assets/images/client.svg" alt="" />在线：
            {{ onlineRoomsCount }}</span
          >
          <app-expand [expanded]="is_stat_expanded" (expandedChange)="expandChanged('stat', $event)"></app-expand>
        </div>
      </div>
      <nz-divider></nz-divider>
      <div class="exam-content-wrap" *ngIf="is_stat_expanded">
        <div class="room-stat">
          <div class="stat-title">考场进度</div>
          <div class="content">
            <nz-table
              #basicTable
              [nzData]="room_stat"
              [nzFrontPagination]="false"
              nzSize="small"
            >
              <thead>
                <tr>
                  <th>开始进场</th>
                  <th>试卷同步</th>
                  <th>密码同步</th>
                  <th>开始考试</th>
                  <th>考试结束</th>
                  <th>上传结果</th>
                  <!-- <th>上传备份</th> -->
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let stat of basicTable.data">
                  <td>{{ stat.enter }}%</td>
                  <td>{{ stat.form }}%</td>
                  <td>{{ stat.password }}%</td>
                  <td>{{ stat.start }}%</td>
                  <td>{{ stat.end }}%</td>
                  <td>{{ stat.result }}%</td>
                  <!-- <td>{{ stat.backup }}%</td> -->
                </tr>
              </tbody>
            </nz-table>
          </div>
        </div>
        <div class="test-stat">
          <div class="stat-title">参考率</div>
          <div class="content">
            <app-chart-pie [data]="entry_count"></app-chart-pie>
          </div>
        </div>
        <div class="event-stat">
          <div class="stat-title">考生违纪及答题行为异常</div>
          @if (event_list.length) {
          <div class="content">
            <div class="event-list">
              @for (event of event_list; track $index) {
              <div class="row">
                <span class="tag" [class.warning]="event.type === 'anomaly'">{{
                  event.type === "anomaly" ? "异常" : "违纪"
                }}</span>
                <span class="room">[{{ event.room }}]</span>
                <span class="event-content" [title]="event.content">{{
                  event.content
                }}</span>
                <span class="time">{{
                  event.created_at * 1000 | customDate : "date"
                }}</span>
              </div>
              }
            </div>
          </div>
          } @else {
          <div class="empty">暂无考生违纪及答题行为异常</div>
          }
        </div>
        <div class="entry-stat">
          <div class="sub-title-wrap">
            <div class="stat-title">考生统计</div>
            <span class="note">注：场次结束后未进场的考生将变为缺考</span>
          </div>
          <div class="content">
            <app-chart-pie-entry [data]="entry_stat"></app-chart-pie-entry>
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="card status-upload">
                <div class="header">
                    <div class="sub-title"><div class="mark"></div>数据上报</div>
                </div>
                <nz-divider></nz-divider>
                <div class="content">
                    <div class="qrcode">
                        <span class="qrcode-title">扫描二维码进行数据上报</span>
                        <nz-qrcode nzValue="https://ng.ant.design/"></nz-qrcode> 
                    </div>
                    <div class="record">
                        <div class="record-title">上报记录</div>
                        <nz-steps [nzCurrent]="1" >
                            <nz-step nzTitle="开始进场"></nz-step>
                            <nz-step nzTitle="开始考试"></nz-step>
                            <nz-step nzTitle="考中上报"></nz-step>
                            <nz-step nzTitle="考试结束"></nz-step>
                        </nz-steps>
                    </div>
                </div>
            </div> -->
    <div class="card">
      <div class="header">
        <div class="sub-title">
          <div class="mark"></div>
          数据导出
        </div>
      </div>
      <nz-divider></nz-divider>

      <div class="data-buttons">
        <button nz-button nzType="primary" (click)="exportExamResult()">
          <span nz-icon nzType="upload"></span>导出考生答题数据
        </button>
        <!-- <button nz-button nzType="primary">
                        <span nz-icon nzType="upload"></span>导出考点单机包
                    </button> -->
      </div>
    </div>
    } @else {
    <app-room-monitor [schedule]="schedule"></app-room-monitor>
    }
  </div>
</div>
