import { CommonModule } from '@angular/common';
import {
  Component,
  type OnInit,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { ActivatedRoute, Router } from '@angular/router';
import { CloudHttpService } from '../core/http/cloud.http';
import { ModalService } from '../core/service/modal.service';

@Component({
    selector: 'app-login-card',
    imports: [
        CommonModule,
        FormsModule,
        NzButtonModule,
        NzInputModule,
        NzIconModule,
    ],
    template: ` <div class="container">
    <div class="user">
      <input
        #serverCodeInput
        class="server-code"
        type="text"
        nz-input
        nzSize="large"
        autofocus
        [(ngModel)]="server_code"
        placeholder="请输入系统注册码"
        (keyup.enter)="login()"
      />
    </div>

    <button
      nz-button
      nzType="primary"
      nzBlock
      [nzLoading]="button_loading"
      (click)="login()"
    >
      注册
    </button>
  </div>`,
    styles: `
  @use 'mixins' as *;
  :host {
    display: block;
    border-radius: 8px;
  }
  .container {
    padding: 20px;
  }
  .header {
    @include flex-box(row, center, center);
    margin-bottom: 30px;
    .logo {
      width: 120px;
    }
    .title {
      font-size: 32px;
      font-weight: 500;
      margin-left: 36px;
    }
  } 
  .user {
    margin-bottom: 26px;
    height: 40px;
  }
  button {
    height: 48px;
    border-radius: 6px;
  }
  `
})
export class LoginCardComponent implements OnInit {
  server_code = '';
  passwordVisible = false;
  button_loading = false;

  constructor(
    private cloudHttp: CloudHttpService,
    private router: Router,
    private route: ActivatedRoute,
    private modalService: ModalService,
  ) {}
  async ngOnInit(): Promise<void> {
    this.route.queryParams.subscribe(params => {
      const serverCode = params['serverCode'];
      if (serverCode) {
        this.server_code = serverCode;
      } else {
        this.loadServerCodeFromCache();
      }
    });

  }

  private async loadServerCodeFromCache(): Promise<void> {
    try {
      const cache = await caches.open('server-info');
      const matchedResponse = await cache.match(new Request('/server-code'));
      if (matchedResponse) {
        const data = await matchedResponse.json();
        if (data && data.serverCode) {
          this.server_code = data.serverCode;
          console.log('Server code loaded from cacheStorage:', this.server_code);
        }
      }
    } catch (cacheError) {
      console.warn('Error reading from cacheStorage or no server-info cache found:', cacheError);
    }
  }

  private async saveServerCodeToCache(serverCode: string): Promise<void> {
    try {
      const cache = await caches.open('server-info');
      const responseBody = { serverCode: serverCode, serverCodeTimestamp: Date.now() };
      const response = new Response(JSON.stringify(responseBody));
      await cache.put(new Request('/server-code'), response);
      console.log('Server code and timestamp saved to cacheStorage.');
    } catch (cacheError) {
      console.error('Error saving to cacheStorage:', cacheError);
      // Optionally, inform the user or handle the error in another way
    }
  }

  async login() {
    if (this.button_loading) {
      return;
    }
    this.button_loading = true;

    this.cloudHttp.getCenterInfo(this.server_code).subscribe({
      next: async (res) => {
        if (res.errcode) {
          console.error('Cloud: login error:', res.errcode, res.errmsg);
          this.modalService.error('错误', res.errmsg || '注册失败');
          this.button_loading = false;
          return;
        }
        await this.saveServerCodeToCache(this.server_code);
        this.router.navigate([this.server_code], {relativeTo: this.route});
      },
      error: (err) => {
        console.error('Cloud: login error:', err);
        this.button_loading = false;
        this.modalService.error('错误', '系统注册码错误');
      },
      complete: () => {
        this.button_loading = false;
      }
    });
  }
}
