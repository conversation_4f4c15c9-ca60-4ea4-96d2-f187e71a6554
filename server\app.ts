import * as http from 'node:http';
import * as https from 'node:https';
import * as fs from 'node:fs';
import { EventEmitter } from 'node:events';
import * as env from './config/env';
import express from 'express';
import cors from 'cors';
import { DBProvider } from '@data/db';
import { router as room_router } from './routes/room.route';
import { router as data_router } from './routes/data.route';
import { init } from './init';
import { ICenterServer } from '@share-types/joyshell.types';
import { getIPAddresses } from './utils/utils';

const app = express();

process.env.NODE_TLS_REJECT_UNAUTHORIZED = '1';
if (process.env.JOY_TLS_SECURE === '0') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

app.disable('x-powered-by');
app.use(cors());
app.use(express.json());

app.use('/room', room_router);
app.use('/data', data_router);

process
  .on('unhandledRejection', (reason, p) => {
    console.error('Unhandled Rejection at Promise', reason, p);
  })
  .on('uncaughtException', function (er) {
    console.error('Uncaught Exception', er.message);
    setTimeout(() => {
      process.exit(1);
    }, 2000);
  });

export class Server extends EventEmitter implements ICenterServer {
  private port: number;
  private secure_port: number;
  private httpServer: http.Server;
  private httpsServer: https.Server;
  constructor() {
    super();
    this.port = normalizePort(process.env.JOY_CENTER_PORT || env.port);
    this.secure_port = env.secure_port || this.port + 1;

    app.set('port', this.port);

    console.info('IP addr: ', getIPAddresses());
  }

  public async start() {
    try {
      await this.openDB();
      await this.initializeApp();
      this.startWebServer();
    } catch (err) {
      console.log('Error starting the server:', err);
      this.emit('error', err);
    }
  }

  private async openDB() {
    try {
      await DBProvider.getDb().open();
    } catch (err: any) {
      console.error('Failed to start server.', err);
      if (
        err.name === 'SequelizeDatabaseError' &&
        err.original?.code === 'SQLITE_NOTADB'
      ) {
        throw appError('dberr', '数据库可能损坏');
      }
      throw appError('unknown', '读取数据失败');
    }
  }

  private async initializeApp() {
    try {
      await init();
    } catch (error) {
      console.error('Error starting the server:', error);
      throw appError('initerr', 'Initialization failure');
    }
  }

  public async stop() {
    try {
      await this.stopWebServer();
      await DBProvider.getDb().close();
    } catch (error) {
      console.error('Error stopping the server:', error);
    }
  }

  private startWebServer() {
    this.httpServer = http.createServer(app);

    this.httpServer
      .on('error', (err) => this.onError(err, this.port))
      .on('listening', () => this.onListening(this.httpServer))
      .on('close', () => {
        console.log('HTTP Server closed');
        this.emit('shutdown');
      })
      .listen(this.port, '0.0.0.0', () => {
        console.log(`HTTP server is running on port ${this.port}`);
        this.emit('started', this.port);
      });

    const httpsOptions = {
      key: fs.readFileSync(`${__dirname}/config/cert/server.key`),
      cert: fs.readFileSync(`${__dirname}/config/cert/server_full.crt`),
    };
    this.httpsServer = https.createServer(httpsOptions, app);
    this.httpsServer
      .on('error', (err) => this.onError(err, this.port))
      .on('listening', () => this.onListening(this.httpsServer))
      .on('close', () => {
        console.log('HTTPS Server closed');
        this.emit('shutdown');
      })
      .listen(this.secure_port, '0.0.0.0', () => {
        console.log(`HTTPS server is running on port ${this.secure_port}`);
        this.emit('started', this.secure_port);
      });
  }

  private stopWebServer() {
    const stopServer = (
      server: http.Server | https.Server,
      serverType: string,
    ) =>
      new Promise<void>((resolve, reject) => {
        if (server !== undefined) {
          server.close((err) => {
            if (err) {
              console.error(`Error stopping ${serverType} server:`, err);
              reject(err);
            } else {
              resolve();
            }
          });
        } else {
          resolve();
        }
      });

    return Promise.all([
      stopServer(this.httpServer, 'HTTP'),
      stopServer(this.httpsServer, 'HTTPS'),
    ]);
  }

  private onError(error: any, port: number) {
    if (error.syscall !== 'listen') {
      this.emit('error', error);
      throw error;
    }

    const bind = 'Port ' + port;

    // handle specific listen errors with friendly messages
    switch (error.code) {
      case 'EACCES':
        console.error(bind + ' requires elevated privileges');
        this.emit('error', appError('porterr', '', error));
        process.exit(1);
        break;
      case 'EADDRINUSE':
        if (this.listenerCount('portInUse') > 0) {
          this.emit('portInUse', this.port);
          return;
        }
        this.emit('error', appError('porterr', 'port is in use', error));
        console.error(bind + ' is already in use');
        process.exit(1);
        break;
      default:
        this.emit('error', appError('server', error.message));
    }
  }

  private onListening(svr: http.Server | https.Server) {
    const addr = svr.address();
    const bind =
      typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr?.port;
    console.info('Listening on ' + bind);
  }
}

function normalizePort(val: number | string): number {
  return typeof val === 'string' ? parseInt(val, 10) : val;
}

function appError(code: string, message: string, inner?: Error): Error {
  const error = Error(message) as any;
  error.name = 'AppError';
  error.code = code;
  error.inner = inner;
  if (Error.captureStackTrace) {
    Error.captureStackTrace(error, appError);
  }
  return error as Error;
}
