{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_@angular+animations@19.2.9_@angular+common@19.2.9_@angular+core@19.2.9_r_i5xlsxr3thsqcwddfzjvckvf54/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-badge.mjs"], "sourcesContent": ["import { __esDecorate, __runInitializers } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { numberAttribute, Input, ChangeDetectionStrategy, ViewEncapsulation, Component, inject, booleanAttribute, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { zoomBadgeMotion } from 'ng-zorro-antd/core/animation';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport * as i3 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i2 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction NzBadgeSupComponent_Conditional_0_For_1_Conditional_1_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const p_r1 = ctx.$implicit;\n    const ɵ$index_2_r2 = i0.ɵɵnextContext(2).$index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current\", p_r1 === ctx_r2.countArray[ɵ$index_2_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", p_r1, \" \");\n  }\n}\nfunction NzBadgeSupComponent_Conditional_0_For_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NzBadgeSupComponent_Conditional_0_For_1_Conditional_1_For_1_Template, 2, 3, \"p\", 2, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵrepeater(ctx_r2.countSingleArray);\n  }\n}\nfunction NzBadgeSupComponent_Conditional_0_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵtemplate(1, NzBadgeSupComponent_Conditional_0_For_1_Conditional_1_Template, 2, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ɵ$index_2_r2 = ctx.$index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"transform\", \"translateY(\" + -ctx_r2.countArray[ɵ$index_2_r2] * 100 + \"%)\");\n    i0.ɵɵproperty(\"nzNoAnimation\", ctx_r2.noAnimation);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r2.nzDot && ctx_r2.countArray[ɵ$index_2_r2] !== undefined ? 1 : -1);\n  }\n}\nfunction NzBadgeSupComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NzBadgeSupComponent_Conditional_0_For_1_Template, 2, 4, \"span\", 0, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r2.maxNumberArray);\n  }\n}\nfunction NzBadgeSupComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.nzOverflowCount, \"+ \");\n  }\n}\nconst _c0 = [\"*\"];\nfunction NzBadgeComponent_Conditional_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzText);\n  }\n}\nfunction NzBadgeComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\");\n    i0.ɵɵelementStart(1, \"span\", 1);\n    i0.ɵɵtemplate(2, NzBadgeComponent_Conditional_0_ng_container_2_Template, 2, 1, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r0.nzStyle);\n    i0.ɵɵclassMapInterpolate1(\"ant-badge-status-dot ant-badge-status-\", ctx_r0.nzStatus || ctx_r0.presetColor, \"\");\n    i0.ɵɵstyleProp(\"background\", !ctx_r0.presetColor && ctx_r0.nzColor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzText);\n  }\n}\nfunction NzBadgeComponent_ng_container_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-badge-sup\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzOffset\", ctx_r0.nzOffset)(\"nzSize\", ctx_r0.nzSize)(\"nzTitle\", ctx_r0.nzTitle)(\"nzStyle\", ctx_r0.nzStyle)(\"nzDot\", ctx_r0.nzDot)(\"nzOverflowCount\", ctx_r0.nzOverflowCount)(\"disableAnimation\", !!(ctx_r0.nzStandalone || ctx_r0.nzStatus || ctx_r0.nzColor || (ctx_r0.noAnimation == null ? null : ctx_r0.noAnimation.nzNoAnimation)))(\"nzCount\", ctx_r0.nzCount)(\"noAnimation\", !!(ctx_r0.noAnimation == null ? null : ctx_r0.noAnimation.nzNoAnimation));\n  }\n}\nfunction NzBadgeComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzBadgeComponent_ng_container_2_Conditional_1_Template, 1, 9, \"nz-badge-sup\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.showSup ? 1 : -1);\n  }\n}\nfunction NzRibbonComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.nzText);\n  }\n}\nclass NzBadgeSupComponent {\n  nzOffset;\n  nzTitle;\n  nzStyle = null;\n  nzDot = false;\n  nzOverflowCount = 99;\n  disableAnimation = false;\n  nzCount;\n  noAnimation = false;\n  nzSize = 'default';\n  maxNumberArray = [];\n  countArray = [];\n  count = 0;\n  countSingleArray = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];\n  generateMaxNumberArray() {\n    this.maxNumberArray = this.nzOverflowCount.toString().split('').map((value, index) => `${value}-${index}`);\n  }\n  ngOnInit() {\n    this.generateMaxNumberArray();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzOverflowCount,\n      nzCount\n    } = changes;\n    if (nzCount && typeof nzCount.currentValue === 'number') {\n      this.count = Math.max(0, nzCount.currentValue);\n      this.countArray = this.count.toString().split('').map(item => +item);\n    }\n    if (nzOverflowCount) {\n      this.generateMaxNumberArray();\n    }\n  }\n  static ɵfac = function NzBadgeSupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzBadgeSupComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzBadgeSupComponent,\n    selectors: [[\"nz-badge-sup\"]],\n    hostAttrs: [1, \"ant-scroll-number\"],\n    hostVars: 17,\n    hostBindings: function NzBadgeSupComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵsyntheticHostProperty(\"@.disabled\", ctx.disableAnimation)(\"@zoomBadgeMotion\", undefined);\n        i0.ɵɵattribute(\"title\", ctx.nzTitle === null ? \"\" : ctx.nzTitle || ctx.nzCount);\n        i0.ɵɵstyleMap(ctx.nzStyle);\n        i0.ɵɵstyleProp(\"right\", ctx.nzOffset && ctx.nzOffset[0] ? -ctx.nzOffset[0] : null, \"px\")(\"margin-top\", ctx.nzOffset && ctx.nzOffset[1] ? ctx.nzOffset[1] : null, \"px\");\n        i0.ɵɵclassProp(\"ant-badge-count\", !ctx.nzDot)(\"ant-badge-count-sm\", ctx.nzSize === \"small\")(\"ant-badge-dot\", ctx.nzDot)(\"ant-badge-multiple-words\", ctx.countArray.length >= 2);\n      }\n    },\n    inputs: {\n      nzOffset: \"nzOffset\",\n      nzTitle: \"nzTitle\",\n      nzStyle: \"nzStyle\",\n      nzDot: \"nzDot\",\n      nzOverflowCount: [2, \"nzOverflowCount\", \"nzOverflowCount\", numberAttribute],\n      disableAnimation: \"disableAnimation\",\n      nzCount: \"nzCount\",\n      noAnimation: \"noAnimation\",\n      nzSize: \"nzSize\"\n    },\n    exportAs: [\"nzBadgeSup\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 2,\n    vars: 1,\n    consts: [[1, \"ant-scroll-number-only\", 3, \"nzNoAnimation\", \"transform\"], [1, \"ant-scroll-number-only\", 3, \"nzNoAnimation\"], [1, \"ant-scroll-number-only-unit\", 3, \"current\"], [1, \"ant-scroll-number-only-unit\"]],\n    template: function NzBadgeSupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzBadgeSupComponent_Conditional_0_Template, 2, 0)(1, NzBadgeSupComponent_Conditional_1_Template, 1, 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.count <= ctx.nzOverflowCount ? 0 : 1);\n      }\n    },\n    dependencies: [NzNoAnimationDirective],\n    encapsulation: 2,\n    data: {\n      animation: [zoomBadgeMotion]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBadgeSupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-badge-sup',\n      exportAs: 'nzBadgeSup',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [zoomBadgeMotion],\n      imports: [NzNoAnimationDirective],\n      template: `\n    @if (count <= nzOverflowCount) {\n      @for (n of maxNumberArray; track n; let i = $index) {\n        <span\n          [nzNoAnimation]=\"noAnimation\"\n          class=\"ant-scroll-number-only\"\n          [style.transform]=\"'translateY(' + -countArray[i] * 100 + '%)'\"\n        >\n          @if (!nzDot && countArray[i] !== undefined) {\n            @for (p of countSingleArray; track p) {\n              <p class=\"ant-scroll-number-only-unit\" [class.current]=\"p === countArray[i]\">\n                {{ p }}\n              </p>\n            }\n          }\n        </span>\n      }\n    } @else {\n      {{ nzOverflowCount }}+\n    }\n  `,\n      host: {\n        class: 'ant-scroll-number',\n        '[@.disabled]': `disableAnimation`,\n        '[@zoomBadgeMotion]': '',\n        '[attr.title]': `nzTitle === null ? '' : nzTitle || nzCount`,\n        '[style]': `nzStyle`,\n        '[style.right.px]': `nzOffset && nzOffset[0] ? -nzOffset[0] : null`,\n        '[style.margin-top.px]': `nzOffset && nzOffset[1] ? nzOffset[1] : null`,\n        '[class.ant-badge-count]': `!nzDot`,\n        '[class.ant-badge-count-sm]': `nzSize === 'small'`,\n        '[class.ant-badge-dot]': `nzDot`,\n        '[class.ant-badge-multiple-words]': `countArray.length >= 2`\n      }\n    }]\n  }], null, {\n    nzOffset: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzStyle: [{\n      type: Input\n    }],\n    nzDot: [{\n      type: Input\n    }],\n    nzOverflowCount: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    disableAnimation: [{\n      type: Input\n    }],\n    nzCount: [{\n      type: Input\n    }],\n    noAnimation: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst badgePresetColors = ['pink', 'red', 'yellow', 'orange', 'cyan', 'green', 'blue', 'purple', 'geekblue', 'magenta', 'volcano', 'gold', 'lime'];\nconst NZ_CONFIG_MODULE_NAME = 'badge';\nlet NzBadgeComponent = (() => {\n  let _nzOverflowCount_decorators;\n  let _nzOverflowCount_initializers = [];\n  let _nzOverflowCount_extraInitializers = [];\n  let _nzColor_decorators;\n  let _nzColor_initializers = [];\n  let _nzColor_extraInitializers = [];\n  return class NzBadgeComponent {\n    static {\n      const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(null) : void 0;\n      _nzOverflowCount_decorators = [WithConfig()];\n      _nzColor_decorators = [WithConfig()];\n      __esDecorate(null, null, _nzOverflowCount_decorators, {\n        kind: \"field\",\n        name: \"nzOverflowCount\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzOverflowCount\" in obj,\n          get: obj => obj.nzOverflowCount,\n          set: (obj, value) => {\n            obj.nzOverflowCount = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzOverflowCount_initializers, _nzOverflowCount_extraInitializers);\n      __esDecorate(null, null, _nzColor_decorators, {\n        kind: \"field\",\n        name: \"nzColor\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzColor\" in obj,\n          get: obj => obj.nzColor,\n          set: (obj, value) => {\n            obj.nzColor = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzColor_initializers, _nzColor_extraInitializers);\n      if (_metadata) Object.defineProperty(this, Symbol.metadata, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value: _metadata\n      });\n    }\n    nzConfigService;\n    renderer;\n    cdr;\n    elementRef;\n    directionality;\n    _nzModuleName = NZ_CONFIG_MODULE_NAME;\n    showSup = false;\n    presetColor = null;\n    dir = 'ltr';\n    destroy$ = new Subject();\n    nzShowZero = false;\n    nzShowDot = true;\n    nzStandalone = false;\n    nzDot = false;\n    nzOverflowCount = __runInitializers(this, _nzOverflowCount_initializers, 99);\n    nzColor = (__runInitializers(this, _nzOverflowCount_extraInitializers), __runInitializers(this, _nzColor_initializers, undefined));\n    nzStyle = (__runInitializers(this, _nzColor_extraInitializers), null);\n    nzText = null;\n    nzTitle;\n    nzStatus;\n    nzCount;\n    nzOffset;\n    nzSize = 'default';\n    noAnimation = inject(NzNoAnimationDirective, {\n      host: true,\n      optional: true\n    });\n    constructor(nzConfigService, renderer, cdr, elementRef, directionality) {\n      this.nzConfigService = nzConfigService;\n      this.renderer = renderer;\n      this.cdr = cdr;\n      this.elementRef = elementRef;\n      this.directionality = directionality;\n    }\n    ngOnInit() {\n      this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n        this.dir = direction;\n        this.prepareBadgeForRtl();\n        this.cdr.detectChanges();\n      });\n      this.dir = this.directionality.value;\n      this.prepareBadgeForRtl();\n    }\n    ngOnChanges(changes) {\n      const {\n        nzColor,\n        nzShowDot,\n        nzDot,\n        nzCount,\n        nzShowZero\n      } = changes;\n      if (nzColor) {\n        this.presetColor = this.nzColor && badgePresetColors.indexOf(this.nzColor) !== -1 ? this.nzColor : null;\n      }\n      if (nzShowDot || nzDot || nzCount || nzShowZero) {\n        this.showSup = this.nzShowDot && this.nzDot || typeof this.nzCount === 'number' && this.nzCount > 0 || typeof this.nzCount === 'number' && this.nzCount <= 0 && this.nzShowZero;\n      }\n    }\n    prepareBadgeForRtl() {\n      if (this.isRtlLayout) {\n        this.renderer.addClass(this.elementRef.nativeElement, 'ant-badge-rtl');\n      } else {\n        this.renderer.removeClass(this.elementRef.nativeElement, 'ant-badge-rtl');\n      }\n    }\n    get isRtlLayout() {\n      return this.dir === 'rtl';\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    static ɵfac = function NzBadgeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NzBadgeComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzBadgeComponent,\n      selectors: [[\"nz-badge\"]],\n      hostAttrs: [1, \"ant-badge\"],\n      hostVars: 4,\n      hostBindings: function NzBadgeComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-badge-status\", ctx.nzStatus)(\"ant-badge-not-a-wrapper\", !!(ctx.nzStandalone || ctx.nzStatus || ctx.nzColor));\n        }\n      },\n      inputs: {\n        nzShowZero: [2, \"nzShowZero\", \"nzShowZero\", booleanAttribute],\n        nzShowDot: [2, \"nzShowDot\", \"nzShowDot\", booleanAttribute],\n        nzStandalone: [2, \"nzStandalone\", \"nzStandalone\", booleanAttribute],\n        nzDot: [2, \"nzDot\", \"nzDot\", booleanAttribute],\n        nzOverflowCount: \"nzOverflowCount\",\n        nzColor: \"nzColor\",\n        nzStyle: \"nzStyle\",\n        nzText: \"nzText\",\n        nzTitle: \"nzTitle\",\n        nzStatus: \"nzStatus\",\n        nzCount: \"nzCount\",\n        nzOffset: \"nzOffset\",\n        nzSize: \"nzSize\"\n      },\n      exportAs: [\"nzBadge\"],\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 2,\n      consts: [[4, \"nzStringTemplateOutlet\"], [1, \"ant-badge-status-text\"], [3, \"nzOffset\", \"nzSize\", \"nzTitle\", \"nzStyle\", \"nzDot\", \"nzOverflowCount\", \"disableAnimation\", \"nzCount\", \"noAnimation\"]],\n      template: function NzBadgeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzBadgeComponent_Conditional_0_Template, 3, 8);\n          i0.ɵɵprojection(1);\n          i0.ɵɵtemplate(2, NzBadgeComponent_ng_container_2_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.nzStatus || ctx.nzColor ? 0 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzCount);\n        }\n      },\n      dependencies: [NzBadgeSupComponent, NzOutletModule, i3.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      data: {\n        animation: [zoomBadgeMotion]\n      },\n      changeDetection: 0\n    });\n  };\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBadgeComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-badge',\n      exportAs: 'nzBadge',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [zoomBadgeMotion],\n      imports: [NzBadgeSupComponent, NzOutletModule],\n      template: `\n    @if (nzStatus || nzColor) {\n      <span\n        class=\"ant-badge-status-dot ant-badge-status-{{ nzStatus || presetColor }}\"\n        [style.background]=\"!presetColor && nzColor\"\n        [style]=\"nzStyle\"\n      ></span>\n      <span class=\"ant-badge-status-text\">\n        <ng-container *nzStringTemplateOutlet=\"nzText\">{{ nzText }}</ng-container>\n      </span>\n    }\n    <ng-content />\n    <ng-container *nzStringTemplateOutlet=\"nzCount\">\n      @if (showSup) {\n        <nz-badge-sup\n          [nzOffset]=\"nzOffset\"\n          [nzSize]=\"nzSize\"\n          [nzTitle]=\"nzTitle\"\n          [nzStyle]=\"nzStyle\"\n          [nzDot]=\"nzDot\"\n          [nzOverflowCount]=\"nzOverflowCount\"\n          [disableAnimation]=\"!!(nzStandalone || nzStatus || nzColor || noAnimation?.nzNoAnimation)\"\n          [nzCount]=\"nzCount\"\n          [noAnimation]=\"!!noAnimation?.nzNoAnimation\"\n        />\n      }\n    </ng-container>\n  `,\n      host: {\n        class: 'ant-badge',\n        '[class.ant-badge-status]': 'nzStatus',\n        '[class.ant-badge-not-a-wrapper]': '!!(nzStandalone || nzStatus || nzColor)'\n      }\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.Directionality\n  }], {\n    nzShowZero: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzShowDot: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzStandalone: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzDot: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzOverflowCount: [{\n      type: Input\n    }],\n    nzColor: [{\n      type: Input\n    }],\n    nzStyle: [{\n      type: Input\n    }],\n    nzText: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzCount: [{\n      type: Input\n    }],\n    nzOffset: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRibbonComponent {\n  nzColor;\n  nzPlacement = 'end';\n  nzText = null;\n  presetColor = null;\n  ngOnChanges(changes) {\n    const {\n      nzColor\n    } = changes;\n    if (nzColor) {\n      this.presetColor = this.nzColor && badgePresetColors.indexOf(this.nzColor) !== -1 ? this.nzColor : null;\n    }\n  }\n  static ɵfac = function NzRibbonComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzRibbonComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzRibbonComponent,\n    selectors: [[\"nz-ribbon\"]],\n    hostAttrs: [1, \"ant-ribbon-wrapper\"],\n    inputs: {\n      nzColor: \"nzColor\",\n      nzPlacement: \"nzPlacement\",\n      nzText: \"nzText\"\n    },\n    exportAs: [\"nzRibbon\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 4,\n    vars: 11,\n    consts: [[1, \"ant-ribbon\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-ribbon-corner\"], [1, \"ant-ribbon-text\"]],\n    template: function NzRibbonComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n        i0.ɵɵelementStart(1, \"div\", 0);\n        i0.ɵɵtemplate(2, NzRibbonComponent_ng_container_2_Template, 3, 1, \"ng-container\", 1);\n        i0.ɵɵelement(3, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵclassMap(ctx.presetColor && \"ant-ribbon-color-\" + ctx.presetColor);\n        i0.ɵɵstyleProp(\"background-color\", !ctx.presetColor && ctx.nzColor);\n        i0.ɵɵclassProp(\"ant-ribbon-placement-end\", ctx.nzPlacement === \"end\")(\"ant-ribbon-placement-start\", ctx.nzPlacement === \"start\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzText);\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"color\", !ctx.presetColor && ctx.nzColor);\n      }\n    },\n    dependencies: [NzOutletModule, i3.NzStringTemplateOutletDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRibbonComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-ribbon',\n      exportAs: 'nzRibbon',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NzOutletModule],\n      template: `\n    <ng-content></ng-content>\n    <div\n      class=\"ant-ribbon\"\n      [class]=\"presetColor && 'ant-ribbon-color-' + presetColor\"\n      [class.ant-ribbon-placement-end]=\"nzPlacement === 'end'\"\n      [class.ant-ribbon-placement-start]=\"nzPlacement === 'start'\"\n      [style.background-color]=\"!presetColor && nzColor\"\n    >\n      <ng-container *nzStringTemplateOutlet=\"nzText\">\n        <span class=\"ant-ribbon-text\">{{ nzText }}</span>\n      </ng-container>\n      <div class=\"ant-ribbon-corner\" [style.color]=\"!presetColor && nzColor\"></div>\n    </div>\n  `,\n      host: {\n        class: 'ant-ribbon-wrapper'\n      }\n    }]\n  }], null, {\n    nzColor: [{\n      type: Input\n    }],\n    nzPlacement: [{\n      type: Input\n    }],\n    nzText: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBadgeModule {\n  static ɵfac = function NzBadgeModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzBadgeModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzBadgeModule,\n    imports: [NzBadgeComponent, NzRibbonComponent],\n    exports: [NzBadgeComponent, NzRibbonComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzBadgeComponent, NzRibbonComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBadgeModule, [{\n    type: NgModule,\n    args: [{\n      exports: [NzBadgeComponent, NzRibbonComponent],\n      imports: [NzBadgeComponent, NzRibbonComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzBadgeComponent, NzBadgeModule, NzRibbonComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,UAAM,eAAkB,cAAc,CAAC,EAAE;AACzC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,WAAW,SAAS,OAAO,WAAW,YAAY,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,MAAM,GAAG;AAAA,EACtC;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,sEAAsE,GAAG,GAAG,KAAK,GAAM,yBAAyB;AAAA,EACzI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,gBAAgB;AAAA,EACvC;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,gEAAgE,GAAG,CAAC;AACrF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,aAAa,gBAAgB,CAAC,OAAO,WAAW,YAAY,IAAI,MAAM,IAAI;AACzF,IAAG,WAAW,iBAAiB,OAAO,WAAW;AACjD,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,OAAO,SAAS,OAAO,WAAW,YAAY,MAAM,SAAY,IAAI,EAAE;AAAA,EAC1F;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,kDAAkD,GAAG,GAAG,QAAQ,GAAM,yBAAyB;AAAA,EACxH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,cAAc;AAAA,EACrC;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,mBAAmB,KAAK,OAAO,iBAAiB,IAAI;AAAA,EACzD;AACF;AACA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM;AACtB,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,gBAAgB,CAAC;AAChG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,OAAO;AAC5B,IAAG,uBAAuB,0CAA0C,OAAO,YAAY,OAAO,aAAa,EAAE;AAC7G,IAAG,YAAY,cAAc,CAAC,OAAO,eAAe,OAAO,OAAO;AAClE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,0BAA0B,OAAO,MAAM;AAAA,EACvD;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,gBAAgB,CAAC;AAAA,EACnC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,UAAU,OAAO,MAAM,EAAE,WAAW,OAAO,OAAO,EAAE,WAAW,OAAO,OAAO,EAAE,SAAS,OAAO,KAAK,EAAE,mBAAmB,OAAO,eAAe,EAAE,oBAAoB,CAAC,EAAE,OAAO,gBAAgB,OAAO,YAAY,OAAO,YAAY,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,eAAe,EAAE,WAAW,OAAO,OAAO,EAAE,eAAe,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc;AAAA,EAC5c;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,gBAAgB,CAAC;AAChG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,UAAU,IAAI,EAAE;AAAA,EAC1C;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,IAAM,uBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB;AAAA,EACA,cAAc;AAAA,EACd,SAAS;AAAA,EACT,iBAAiB,CAAC;AAAA,EAClB,aAAa,CAAC;AAAA,EACd,QAAQ;AAAA,EACR,mBAAmB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAChD,yBAAyB;AACvB,SAAK,iBAAiB,KAAK,gBAAgB,SAAS,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,OAAO,UAAU,GAAG,KAAK,IAAI,KAAK,EAAE;AAAA,EAC3G;AAAA,EACA,WAAW;AACT,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,OAAO,QAAQ,iBAAiB,UAAU;AACvD,WAAK,QAAQ,KAAK,IAAI,GAAG,QAAQ,YAAY;AAC7C,WAAK,aAAa,KAAK,MAAM,SAAS,EAAE,MAAM,EAAE,EAAE,IAAI,UAAQ,CAAC,IAAI;AAAA,IACrE;AACA,QAAI,iBAAiB;AACnB,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAiDF;AAhDE,cAjCI,sBAiCG,QAAO,SAAS,4BAA4B,mBAAmB;AACpE,SAAO,KAAK,qBAAqB,sBAAqB;AACxD;AACA,cApCI,sBAoCG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,EAC5B,WAAW,CAAC,GAAG,mBAAmB;AAAA,EAClC,UAAU;AAAA,EACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,QAAI,KAAK,GAAG;AACV,MAAG,wBAAwB,cAAc,IAAI,gBAAgB,EAAE,oBAAoB,MAAS;AAC5F,MAAG,YAAY,SAAS,IAAI,YAAY,OAAO,KAAK,IAAI,WAAW,IAAI,OAAO;AAC9E,MAAG,WAAW,IAAI,OAAO;AACzB,MAAG,YAAY,SAAS,IAAI,YAAY,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,MAAM,IAAI,EAAE,cAAc,IAAI,YAAY,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,MAAM,IAAI;AACrK,MAAG,YAAY,mBAAmB,CAAC,IAAI,KAAK,EAAE,sBAAsB,IAAI,WAAW,OAAO,EAAE,iBAAiB,IAAI,KAAK,EAAE,4BAA4B,IAAI,WAAW,UAAU,CAAC;AAAA,IAChL;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,OAAO;AAAA,IACP,iBAAiB,CAAC,GAAG,mBAAmB,mBAAmB,eAAe;AAAA,IAC1E,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA,UAAU,CAAC,YAAY;AAAA,EACvB,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,0BAA0B,GAAG,iBAAiB,WAAW,GAAG,CAAC,GAAG,0BAA0B,GAAG,eAAe,GAAG,CAAC,GAAG,+BAA+B,GAAG,SAAS,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,EAChN,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,4CAA4C,GAAG,CAAC,EAAE,GAAG,4CAA4C,GAAG,CAAC;AAAA,IACxH;AACA,QAAI,KAAK,GAAG;AACV,MAAG,cAAc,IAAI,SAAS,IAAI,kBAAkB,IAAI,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,cAAc,CAAC,sBAAsB;AAAA,EACrC,eAAe;AAAA,EACf,MAAM;AAAA,IACJ,WAAW,CAAC,eAAe;AAAA,EAC7B;AAAA,EACA,iBAAiB;AACnB,CAAC;AAhFH,IAAM,sBAAN;AAAA,CAkFC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY,CAAC,eAAe;AAAA,MAC5B,SAAS,CAAC,sBAAsB;AAAA,MAChC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,sBAAsB;AAAA,QACtB,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,2BAA2B;AAAA,QAC3B,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,oCAAoC;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,oBAAoB,CAAC,QAAQ,OAAO,UAAU,UAAU,QAAQ,SAAS,QAAQ,UAAU,YAAY,WAAW,WAAW,QAAQ,MAAM;AACjJ,IAAM,wBAAwB;AAC9B,IAAI,oBAAoB,MAAM;AAlT9B;AAmTE,MAAI;AACJ,MAAI,gCAAgC,CAAC;AACrC,MAAI,qCAAqC,CAAC;AAC1C,MAAI;AACJ,MAAI,wBAAwB,CAAC;AAC7B,MAAI,6BAA6B,CAAC;AAClC,SAAO,WAAuB;AAAA,IAwC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW,IAAI,QAAQ;AAAA,IACvB,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,kBAAkB,kBAAkB,MAAM,+BAA+B,EAAE;AAAA,IAC3E,WAAW,kBAAkB,MAAM,kCAAkC,GAAG,kBAAkB,MAAM,uBAAuB,MAAS;AAAA,IAChI,WAAW,kBAAkB,MAAM,0BAA0B,GAAG;AAAA,IAChE,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,cAAc,OAAO,wBAAwB;AAAA,MAC3C,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AAAA,IACD,YAAY,iBAAiB,UAAU,KAAK,YAAY,gBAAgB;AACtE,WAAK,kBAAkB;AACvB,WAAK,WAAW;AAChB,WAAK,MAAM;AACX,WAAK,aAAa;AAClB,WAAK,iBAAiB;AAAA,IACxB;AAAA,IACA,WAAW;AACT,WAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,aAAK,MAAM;AACX,aAAK,mBAAmB;AACxB,aAAK,IAAI,cAAc;AAAA,MACzB,CAAC;AACD,WAAK,MAAM,KAAK,eAAe;AAC/B,WAAK,mBAAmB;AAAA,IAC1B;AAAA,IACA,YAAY,SAAS;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,SAAS;AACX,aAAK,cAAc,KAAK,WAAW,kBAAkB,QAAQ,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU;AAAA,MACrG;AACA,UAAI,aAAa,SAAS,WAAW,YAAY;AAC/C,aAAK,UAAU,KAAK,aAAa,KAAK,SAAS,OAAO,KAAK,YAAY,YAAY,KAAK,UAAU,KAAK,OAAO,KAAK,YAAY,YAAY,KAAK,WAAW,KAAK,KAAK;AAAA,MACvK;AAAA,IACF;AAAA,IACA,qBAAqB;AACnB,UAAI,KAAK,aAAa;AACpB,aAAK,SAAS,SAAS,KAAK,WAAW,eAAe,eAAe;AAAA,MACvE,OAAO;AACL,aAAK,SAAS,YAAY,KAAK,WAAW,eAAe,eAAe;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,IAAI,cAAc;AAChB,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,IACA,cAAc;AACZ,WAAK,SAAS,KAAK;AACnB,WAAK,SAAS,SAAS;AAAA,IACzB;AAAA,EAuDF,IArKE,MAAO;AACL,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,uBAAO,OAAO,IAAI,IAAI;AAC1F,kCAA8B,CAAC,WAAW,CAAC;AAC3C,0BAAsB,CAAC,WAAW,CAAC;AACnC,iBAAa,MAAM,MAAM,6BAA6B;AAAA,MACpD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,KAAK,SAAO,qBAAqB;AAAA,QACjC,KAAK,SAAO,IAAI;AAAA,QAChB,KAAK,CAAC,KAAK,UAAU;AACnB,cAAI,kBAAkB;AAAA,QACxB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,IACZ,GAAG,+BAA+B,kCAAkC;AACpE,iBAAa,MAAM,MAAM,qBAAqB;AAAA,MAC5C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,KAAK,SAAO,aAAa;AAAA,QACzB,KAAK,SAAO,IAAI;AAAA,QAChB,KAAK,CAAC,KAAK,UAAU;AACnB,cAAI,UAAU;AAAA,QAChB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,IACZ,GAAG,uBAAuB,0BAA0B;AACpD,QAAI,UAAW,QAAO,eAAe,IAAM,OAAO,UAAU;AAAA,MAC1D,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT,CAAC;AAAA,EACH,MAyEA,cAhHK,IAgHE,QAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,IAAqB,kBAAqB,eAAe,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,cAAc,CAAC;AAAA,EAC3P,IACA,cAnHK,IAmHE,QAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,oBAAoB,IAAI,QAAQ,EAAE,2BAA2B,CAAC,EAAE,IAAI,gBAAgB,IAAI,YAAY,IAAI,QAAQ;AAAA,MACjI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,MAClE,OAAO,CAAC,GAAG,SAAS,SAAS,gBAAgB;AAAA,MAC7C,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,SAAS;AAAA,IACpB,UAAU,CAAI,oBAAoB;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,YAAY,UAAU,WAAW,WAAW,SAAS,mBAAmB,oBAAoB,WAAW,aAAa,CAAC;AAAA,IAC/L,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,yCAAyC,GAAG,CAAC;AAC9D,QAAG,aAAa,CAAC;AACjB,QAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,CAAC;AAAA,MACpF;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,YAAY,IAAI,UAAU,IAAI,EAAE;AACrD,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,0BAA0B,IAAI,OAAO;AAAA,MACrD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,qBAAqB,gBAAmB,+BAA+B;AAAA,IACtF,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,eAAe;AAAA,IAC7B;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC,IArKI;AAuKT,GAAG;AAAA,CACF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY,CAAC,eAAe;AAAA,MAC5B,SAAS,CAAC,qBAAqB,cAAc;AAAA,MAC7C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA4BV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,4BAA4B;AAAA,QAC5B,mCAAmC;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,qBAAN,MAAM,mBAAkB;AAAA,EACtB;AAAA,EACA,cAAc;AAAA,EACd,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,WAAK,cAAc,KAAK,WAAW,kBAAkB,QAAQ,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU;AAAA,IACrG;AAAA,EACF;AA2CF;AA1CE,cAbI,oBAaG,QAAO,SAAS,0BAA0B,mBAAmB;AAClE,SAAO,KAAK,qBAAqB,oBAAmB;AACtD;AACA,cAhBI,oBAgBG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,EACzB,WAAW,CAAC,GAAG,oBAAoB;AAAA,EACnC,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA,UAAU,CAAC,UAAU;AAAA,EACrB,UAAU,CAAI,oBAAoB;AAAA,EAClC,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,iBAAiB,CAAC;AAAA,EAC3G,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,aAAa,CAAC;AACjB,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,gBAAgB,CAAC;AACnF,MAAG,UAAU,GAAG,OAAO,CAAC;AACxB,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU;AACb,MAAG,WAAW,IAAI,eAAe,sBAAsB,IAAI,WAAW;AACtE,MAAG,YAAY,oBAAoB,CAAC,IAAI,eAAe,IAAI,OAAO;AAClE,MAAG,YAAY,4BAA4B,IAAI,gBAAgB,KAAK,EAAE,8BAA8B,IAAI,gBAAgB,OAAO;AAC/H,MAAG,UAAU;AACb,MAAG,WAAW,0BAA0B,IAAI,MAAM;AAClD,MAAG,UAAU;AACb,MAAG,YAAY,SAAS,CAAC,IAAI,eAAe,IAAI,OAAO;AAAA,IACzD;AAAA,EACF;AAAA,EACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,EACjE,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAtDH,IAAM,oBAAN;AAAA,CAwDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,cAAc;AAAA,MACxB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAeV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,iBAAN,MAAM,eAAc;AAYpB;AAXE,cADI,gBACG,QAAO,SAAS,sBAAsB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,gBAAe;AAClD;AACA,cAJI,gBAIG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AAAA,EACN,SAAS,CAAC,kBAAkB,iBAAiB;AAAA,EAC7C,SAAS,CAAC,kBAAkB,iBAAiB;AAC/C,CAAC;AACD,cATI,gBASG,QAAyB,iBAAiB;AAAA,EAC/C,SAAS,CAAC,kBAAkB,iBAAiB;AAC/C,CAAC;AAXH,IAAM,gBAAN;AAAA,CAaC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,kBAAkB,iBAAiB;AAAA,IAC/C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}