import * as path from 'path';
import { copyFile, readdir, mkdir, stat } from 'fs/promises';

export async function copyFiles(srcPath: string, destDir: string) {
  try {
    await mkdir(destDir, { recursive: true });
    const srcStat = await stat(srcPath);

    if (srcStat.isFile()) {
      await copyFile(srcPath, path.join(destDir, path.basename(srcPath)));
      return;
    }

    const items = await readdir(srcPath);
    const tasks = items.map(async (item) => {
      const srcFilePath = path.join(srcPath, item);
      const destFilePath = path.join(destDir, item);
      const itemStat = await stat(srcFilePath);

      if (itemStat.isFile()) {
        await copyFile(srcFilePath, destFilePath);
        console.debug(`Copied '${srcFilePath}' to '${destFilePath}'`);
      } else {
        await copyFiles(srcFilePath, destFilePath);
      }
    });

    await Promise.all(tasks);
  } catch (error) {
    console.error(`Error occurred while copying files:`, error);
    throw error;
  }
}
