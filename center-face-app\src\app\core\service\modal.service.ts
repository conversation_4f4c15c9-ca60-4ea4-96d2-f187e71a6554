import { Injectable, TemplateRef, ViewContainerRef } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';


@Injectable({
  providedIn: 'root',
})
export class ModalService {
  constructor(private nzModalService: NzModalService) {}

  confirm(content: string, onOk: any) {
    this.nzModalService.confirm({
      nzTitle: '提示',
      nzContent: content,
      nzOnOk: onOk,
    });
  }

  error(title: string, content: string) {
    return this.nzModalService.error({
      nzTitle: title,
      nzContent: content,
    });
  }
  create(
    type: 'success' | 'error' | 'warning',
    options: {
      title?: string;
      content?: string;
      onOk?: any;
    }
  ) {
    const opts = {
      nzTitle: options.title,
      nzContent: options.content,
      nzOnOk: options.onOk
    };
    if (type === 'success') {
      this.nzModalService.success(opts);
    } else if (type === 'error') {
      this.nzModalService.error(opts);
    } else if (type === 'warning') {
      this.nzModalService.warning(opts);
    }
  }

  createTemplate(tplTitle: TemplateRef<{}>, tplContent: TemplateRef<{}>, tplFooter: TemplateRef<{}>, opts: any = {}) {
    const modal = this.nzModalService.create({
      nzTitle: tplTitle,
      nzContent: tplContent,
      nzFooter: tplFooter,
      nzWidth: 800,
      nzMaskClosable: false,
      ...opts
    })
    return modal;
  }


}
