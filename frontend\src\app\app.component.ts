import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterOutlet } from '@angular/router';
import { ProjectDataService, RoomDataService } from './core/data';
import { forkJoin } from 'rxjs';
import { UpdateService } from './core/services/update.service';

@Component({
    selector: 'app-root',
    imports: [CommonModule, RouterOutlet],
    template: `<router-outlet></router-outlet> `,
    styles: ``
})
export class AppComponent implements OnInit {

  constructor(
    private router: Router,
    private projectData: ProjectDataService,
    private roomData: RoomDataService,
    private updateService: UpdateService
  ) {}
  ngOnInit() {
    this.updateService.checkAppVersion();
    forkJoin([this.projectData.reqCenterStatus(), this.roomData.reqRooms()]).subscribe(([statusRes, roomsRes]) => {
      if (statusRes.status === 'success') {
        const center = statusRes.data;
        if (!center) {
          this.router.navigate(['/login']);
        } else {
          if (roomsRes.status === 'success' && !roomsRes.data.length) {
            this.router.navigate(['/dashboard/room']);
            return;
          }
          this.router.navigate(['/dashboard/home']);
        }
    }});
    this.projectData.reqProjects().subscribe();
  }
  
}
