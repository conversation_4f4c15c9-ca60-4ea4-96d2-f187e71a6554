import { JoyShell } from '@share-types/joyshell.types';
import { context<PERSON><PERSON>, ipc<PERSON><PERSON><PERSON> } from 'electron';
import {listenKeyHook} from './keyhook'
import * as IPC from "@share-types/ipc.constants";
import { ICenterSetting, NetworkConfig } from '@share-types/settings.types';
import { getAddon } from './utils/addon';

declare global {
  interface Window {
    joyshell: JoyShell;
  }
}

let settings_: ICenterSetting;
let userSettings_: Record<string, any>;
let serverUrl_: string;
let appVersion_: string;

const joyshell = {} as JoyShell;
async function initialize () {
  const data = await ipcRenderer.invoke(IPC.IPC_GET_SETTINGS);
  appVersion_ = data.appVersion;
  settings_ = data.settings;
  userSettings_ = data.userSettings;

  // if (userSettings_.remote) {
  //   merge(settings_, userSettings_.remote);
  // }

  // mainArgv_ = data.mainArgv;
  serverUrl_ = data.serverUrl;
  if (process.contextIsolated) {
    exposeJoyShellInMainWorld();
  }
  listenKeyHook();
  return Promise.resolve();
};

const initPromise = initialize();

joyshell.WhenReady = function()  { return initPromise};
joyshell.GetSettings = function()  { return settings_} ;
joyshell.Send = function(channel: string, ...args: any[])  {
  if (!channel.startsWith("ipc-")) {
    console.error("invalid channel name");
    return;
  }
  ipcRenderer.send(channel, ...args);
};
joyshell.GetUserConfig = function <T>(name: string, defval?: T): T {
  if (name in userSettings_) {
    return userSettings_[name];
  }

  name = name.toUpperCase();
  if (name in settings_) {
    return settings_[name];
  }

  return defval as T;
};
joyshell.SetUserConfig = function (cfg: Record<string, any> | string, value?: any) {
  if (typeof cfg === "string") {
    userSettings_[cfg] = value;
  } else {
    Object.keys(cfg).forEach((k) => {
      userSettings_[k] = cfg[k];
    });
  }
  ipcRenderer.invoke(IPC.IPC_UPDATE_USER_SETTINGS, cfg, value);
};

joyshell.GetNetworkConfig = function (): NetworkConfig {
  return userSettings_.network;
};

joyshell.SetNetworkConfig = function (info: NetworkConfig) {
  ipcRenderer.invoke(IPC.IPC_UPDATE_USER_SETTINGS, "network", info);
  if (userSettings_.network) {
    Object.assign(userSettings_.network, info);
  } else {
    userSettings_.network = info;
  }
};

joyshell.GetAddon = function (name: string): any {
  console.log('get addon:', name)
  return getAddon(name as any);
};

joyshell.ShowOpenDialog = function (options: Electron.OpenDialogOptions): Promise<Electron.OpenDialogReturnValue> {
  return ipcRenderer.invoke("ipc-show-open-dialog", options);
};

joyshell.ShowSaveDialog = function (options: Electron.SaveDialogOptions): Promise<Electron.SaveDialogReturnValue> {
  return ipcRenderer.invoke("ipc-show-save-dialog", options);
};

joyshell.GetServerURL = function() {
  return serverUrl_;
};

joyshell.OpenUrl = function(url: string): void {
  ipcRenderer.invoke(IPC.IPC_OPEN_URL, url);
};

Object.defineProperty(joyshell, "AppVersion", {
  get() {
    return appVersion_;
  },
  enumerable: true,
});

Object.defineProperty(joyshell, "Settings", {
  get() {
    return this.GetSettings();
  },
  enumerable: true,
});

if (!process.contextIsolated) {
  window.joyshell = joyshell;
}

function exposeJoyShellInMainWorld() {
  contextBridge.exposeInMainWorld("joyshell", joyshell);
}
