import path from 'node:path';
import { DataAccess } from '@data/types';
import {
  DataPackage,
  IPackageStrategy,
  PackageError,
  PackageErrorCode,
  readZipFile,
} from './package.manager';
import * as IPackage from './package.type';
import { ZipReader } from '@share-types/addon.types';
import { fileMd5 } from '@server/utils/filemd5';
import fs from 'fs-extra';

// 外部试卷包(悦题)
export class ExternalFormPackageStrategy implements IPackageStrategy {
  dataAccess: DataAccess;
  formBaseDir: string;

  constructor(dataAccess: DataAccess, formBaseDir: string) {
    this.dataAccess = dataAccess;
    this.formBaseDir = formBaseDir;
  }
  async handlePackage(dataPackage: DataPackage) {
    const { zip, schedule_id } = dataPackage;
    const manifest = await readZipFile<IPackage.Manifest>(zip, 'manifest.json');

    const forms = manifest?.forms;
    if (!forms?.length) {
      throw new PackageError(
        'Package: no forms found',
        PackageErrorCode.DATA_ERROR,
      );
    }
    const schedule = await this.dataAccess.schedule.get(schedule_id);
    const is_form_published = !!schedule?.form_published;
    const is_password_published = !!schedule?.password_published;
    let form_count = 0;
    let password_count = 0;
    for (const form of forms) {
      let is_form = false;
      const subjects =
        await this.dataAccess.subject.getScheduleSubjects(schedule_id);
      const form_subject = subjects.find((s) => s.code === form.subject_code);
      if (!form_subject) {
        console.error('Package: subject not found:', form.subject_code);
        continue;
      }
      const form_path = await this.importForm(zip, form, schedule_id);
      if (form_path) {
        const isFileOk = (await fileMd5(form_path)) === form.md5.toLowerCase();
        if (!isFileOk) {
          console.error('Package: file md5 is wrong:', form.form_id + '.form');
          throw new PackageError(
            'Package: file md5 is wrong: ' + form.form_id + '.form',
            PackageErrorCode.DATA_ERROR,
          );
        }
        is_form = true;
        form_count++;
      } else {
        console.warn('Package: import form failed:', form.form_id + '.form');
      }
      const session = await this.dataAccess.session.get({
        schedule_id: schedule_id,
      });
      if (!session) {
        throw new PackageError(
          'Package: session not found: ' + schedule_id,
          PackageErrorCode.DATA_ERROR,
        );
      }
      if (form.password) {
        password_count++;
      }
      await this.dataAccess.form.addOrUpdateForm({
        form_id: form.form_id,
        schedule_id: schedule_id,
        subject_id: form_subject.subject_id,
        form_time: is_form ? new Date().toISOString() : undefined,
        password_time: form.password ? new Date().toISOString() : undefined,
        form_publish_time: is_form_published
          ? new Date().toISOString()
          : undefined,
        password_publish_time:
          is_password_published && form.password
            ? new Date().toISOString()
            : undefined,
        name: form.form_name,
        md5: form.md5,
        code: '',
        password: form.password,
        version: 0,
      });
    }
    return { form: form_count, password: password_count };
  }

  async importForm(
    zip: ZipReader,
    form: IPackage.ManifestForm,
    schduleId: string,
  ) {
    if (!zip.exists(form.file)) {
      console.warn(`Package: form not exists: ${form.file}`);
      return null;
    }
    const formPath = path.join(
      this.formBaseDir,
      schduleId,
      `${form.form_id}.form`,
    );
    fs.ensureDirSync(path.join(this.formBaseDir, schduleId));
    await zip.extract(form.file, formPath);
    return formPath;
  }
}
