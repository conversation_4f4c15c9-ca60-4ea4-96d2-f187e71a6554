import * as crypto from "crypto";

const kCIPHER_METHOD = "aes-256-cfb";

// 加密
//    text: 要加密的字符串；password：密码
export function encrypt(text: string, password: string): string {
  const iv = Buffer.from(crypto.randomBytes(16));
  const key = crypto.pbkdf2Sync(password, iv, 10000, 32, "sha256");
  const cipher = crypto.createCipheriv("AES-256-CBC", key, iv);
  let cipher_text = cipher.update(text, "utf8", "base64");
  cipher_text += cipher.final("base64");
  return cipher_text + "$" + iv.toString("base64");
}

// 解密
//    blob: encrypt函数返回值；
//    password: 密码
export function decrypt(blob: string, password: string): string {
  const [cipher_text, ivs] = blob.split("$");
  const iv = Buffer.from(ivs, "base64");
  const key = crypto.pbkdf2Sync(password, iv, 10000, 32, "sha256");
  const decipher = crypto.createDecipheriv("AES-256-CBC", key, iv);
  let plain = decipher.update(cipher_text, "base64", "utf8");
  plain += decipher.final("utf8");
  return plain;
}

export function encryptText(text: string, password: string) {
  const key = crypto.createHash("sha256").update(password).digest();
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(kCIPHER_METHOD, key, iv);
  const enc = [iv, cipher.update(text, "utf8")];
  enc.push(cipher.final());
  return Buffer.concat(enc).toString("base64");
}

export function decryptText(text: string, password: string) {
  const key = crypto.createHash("sha256").update(password).digest();
  const contents = Buffer.from(text, "base64");
  const iv = contents.slice(0, 16);
  const edata = contents.slice(16);
  if (iv.length < 16 || edata.length === 0) {
    return "";
  }
  const decipher = crypto.createDecipheriv(kCIPHER_METHOD, key, iv);
  let res = decipher.update(edata, undefined, "utf8");
  res += decipher.final("utf8");
  return res;
}

export function Hmac256(password: string, code: string): string {
  const hmac = crypto.createHmac("sha256", password);
  return hmac.update(code).digest("hex");
}
