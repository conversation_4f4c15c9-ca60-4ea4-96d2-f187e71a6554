export interface ZipEntry {
  name: string;
  is_encrypted: boolean;
  is_directory: boolean;
  is_symlink: boolean;
  comment: string;
  linkname?: string;
  uncompressed_size: number;
  modified_date: number;
}

export declare class ZipReader {
  count: number;
  item(index: number): ZipEntry;
  exists(name: string): boolean;
  extract(name: number | string, dest: string): Promise<number>;
  extract_all(dest: string, pattern?: string): Promise<number>;
  read(name: string): Promise<string>;
  readBuffer(name: string): Promise<Buffer>;
  close(): void;
}

export declare class ZipWriter {
  addFile(filepath: string, newName?: string): Promise<boolean>;
  addDir(dir: string, root?: string, recursive?: boolean): Promise<boolean>;
  addBuffer(name: string, buf: Buffer): Promise<boolean>;
  close(): void;
}

export declare class MZip {
  open(path: string, password: string): Promise<ZipReader>;
  create(zipfile: string, password?: string): Promise<ZipWriter>;
}

export interface LoggerConstruct {
  new (type: "rotating", name: string, filepath: string, maxFileSize: number, maxFiles: number): Logger;
  new (name: string): Logger;

  version: string;
  shutdown(): void;
  setDefaultLevel(level: number): void;
  setDefaultPattern(pattern: string): void;
  loadLevels(config: string): void;
}

export declare class Logger {
  debug(message: string): void;
  trace(message: string): void;
  info(message: string): void;
  error(message: string): void;
  critical(message: string): void;

  setPattern(pattern: string): void;
  getLevel(): number;
  setLevel(level: number): void;
  flush(): void;
  drop(): void;
}

interface InputSourceManagerConstruct {
  new (): InputSourceManager;
}

export declare class InputSourceManager {
  current(): string;
  select(name: string): boolean;
  list(): { name: string }[];
}

interface DisplayManagerConstruct {
  new (): DisplayManager;
}

export declare class DisplayManager {
  getDisplays(): { width: number; height: number }[];
  numOfDisplays(): number;
}

export declare class ProcessEntry {
  pid: number;
  name: string;
  full_path?: string;
  kill(): boolean;
}

export declare class WindowEntry{
  handle: number;
  title: string;
  class_name: string;
  pid: number;
  image_path: string;
  minimized: boolean;
  focus(): boolean;
  close(): boolean;
  kill(): boolean;
}

export declare class ConnEntry {
  pid: number;
  localAddr: string;
  localPort: number;
  remoteAddr: string;
  remotePort: number;
  process: string;
  state: "LISTEN" | "ESTAB";
}

declare class PartitionInfo {
  total: number; // MB
  free: number; // MB
}

export declare class OSExt {
  getTick(): number;
  getLocale(): string;
  getUILang(): string;
  isRemoteSession(): boolean;
  getMachineID(type?: number): string;
  getHddSn(): string;
  getPartitionSpace(name: string): Promise<PartitionInfo>;
  checkVM(): boolean;
  isWin64(): boolean;
  isWow64(): boolean;
  isArch64(): boolean;
  getPath(name: string): string;
  findApp(name: string): string;
  isLocked(): boolean;
  listProcesses(cb: (p: ProcessEntry) => void): boolean;
  listWindows(cb: (w: WindowEntry) => void): boolean;
  killConnections(cb: (proto: string, conn: ConnEntry) => boolean): boolean;
  startCalc(options: { preferSystem?: boolean; createNew?: boolean; calculator?: string }): Promise<boolean>;
  getFileVersion(p: string): any;
  setAppState(enable: boolean): void;
  fwAllowApp(
    name: string,
    path: string,
    options: { group: string; description: string; direction: string }
  ): Promise<void>;
  toggleNet(enable: boolean, allowApps: string[]): boolean;
  isElevated(): boolean;

  //optional
  getTime(): number;
  setTime(value: number): boolean;
  getPCType(): number;
  addToJob(pid: number): boolean;
  DisplayManager: DisplayManagerConstruct;
  Logger: LoggerConstruct;
  InputSourceManager: InputSourceManagerConstruct;
}

export declare class AudioAddonInterface {
  get(device: number): number;
  set(volume: number, device: number): void;
  mute(device: number, state: 0 | 1): void;
  isMuted(device: number): number;

  fixup_webm_async(src: string, dst: string): Promise<number>;
}
