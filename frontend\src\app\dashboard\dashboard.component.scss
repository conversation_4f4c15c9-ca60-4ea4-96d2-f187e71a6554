@use "variables" as *;

:host {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

nav {
  .nav-bar {
    padding: 12px 24px;
    height: 56px;
    background: $color-base;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .logo {
      width: 78px;
      height: 30px;
    }
    .time {
      color: #fff;
      font-size: 16px;
      margin-right: 16px;
    }
    .setting {
      display: inline-block;
      cursor: pointer;
      height: 20px;
      width: 20px;
      span {
        font-size: 20px;
        color: #fff;
        vertical-align: sub;
      }
    }
  }
  .tabs {
    background-color: #f6f7f9;
    position: relative;
    .back-btn {
      display: inline-block;
      position: absolute;
      z-index: 1000;
      top: 20px;
      left: 30px;
      min-width: 82px;
      cursor: pointer;
    }
  }
}
main {
  background-color: #f6f7f9;
  flex-grow: 1;
  position: relative;
  app-back-btn {
    position: absolute;
    top: 15px;
    left: 10px;
  }
  overflow: auto;
  height: 0;
  .detail {
    flex: 1;
  }
}

.version-info {
  position: fixed;
  top: 60px;
  left: 24px;
  color: #a9a9a9;
  font-size: 14px;
}
