import { Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { RegisterCardComponent } from './login/register-card.component';
import { LoginCardComponent } from './login/login-card.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { FaceSigninComponent } from './dashboard/face-signin/face-signin.component';
import { AuthGuard } from './core/guard/auth.guard';

export const routes: Routes = [
  { path: '', redirectTo: '/login', pathMatch: 'full' },
  {
    path: 'login',
    component: LoginComponent,
    children: [
      { path: '', component: LoginCardComponent },
      { path: ':r_server_code', component: RegisterCardComponent },
    ],
  },
  { path: 'dashboard/:server_code', component: DashboardComponent, canActivate: [AuthGuard],
    // children: [
    //   { path: 'signin/:schedule_id', component: FaceSigninComponent },
    // ],
  },
  { path: "dashboard/:server_code/signin/:r_schedule_id", component: FaceSigninComponent, canActivate: [AuthGuard] },
];
