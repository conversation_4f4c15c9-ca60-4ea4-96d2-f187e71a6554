import { Pipe, PipeTransform } from '@angular/core';
import { ElogType } from '@share-types/center.types';

@Pipe({ name: 'logFilter', standalone: true })
export class FilterLogPipe implements PipeTransform {
  transform(value: { content: string }[], query: string): any {
    const regex = new RegExp(query, 'i'); // 不区分大小写
    return value.filter(({ content }) => regex.test(content));
  }
}

@Pipe({ name: 'dateFilter', standalone: true })
export class DateFilterPipe implements PipeTransform {
  transform(value: { time: number }[], dateRange: [Date, Date] | null): any {
    if (!dateRange?.length) {
      return value;
    }
    return value.filter(({ time }) => {
      const [start, end] = dateRange;
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      return time >= start.getTime() && time <= end.getTime();
    });
  }
}

@Pipe({ name: 'typeFilter', standalone: true })
export class TypeFilterPipe implements PipeTransform {
  transform(value: { type: ElogType }[], type: number | null): any {
    return type === null ? value : value.filter(({ type: t }) => t === type);
  }
}
