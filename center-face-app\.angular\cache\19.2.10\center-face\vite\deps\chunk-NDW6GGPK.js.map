{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_@angular+animations@19.2.9_@angular+common@19.2.9_@angular+core@19.2.9_r_i5xlsxr3thsqcwddfzjvckvf54/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-menu.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/bidi';\nimport { Directionality } from '@angular/cdk/bidi';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, inject, booleanAttribute, ContentChildren, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, EventEmitter, Output, ElementRef, forwardRef, ViewChild, Directive, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, merge, combineLatest } from 'rxjs';\nimport { map, mergeMap, filter, auditTime, distinctUntilChanged, takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { RouterLink, Router, NavigationEnd } from '@angular/router';\nimport { numberAttributeWithZeroFallback } from 'ng-zorro-antd/core/util';\nimport * as i3$1 from '@angular/cdk/overlay';\nimport { OverlayModule, CdkOverlayOrigin } from '@angular/cdk/overlay';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { POSITION_MAP, getPlacementName } from 'ng-zorro-antd/core/overlay';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { collapseMotion, zoomBigMotion, slideMotion } from 'ng-zorro-antd/core/animation';\nimport * as i3 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2$1 from '@angular/cdk/platform';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"nz-menu-item\", \"\"];\nconst _c1 = [\"*\"];\nconst _c2 = [\"nz-submenu-inline-child\", \"\"];\nfunction NzSubmenuInlineChildComponent_ng_template_0_Template(rf, ctx) {}\nconst _c3 = [\"nz-submenu-none-inline-child\", \"\"];\nfunction NzSubmenuNoneInlineChildComponent_ng_template_1_Template(rf, ctx) {}\nconst _c4 = [\"nz-submenu-title\", \"\"];\nfunction NzSubMenuTitleComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", ctx_r0.nzIcon);\n  }\n}\nfunction NzSubMenuTitleComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 4);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.nzTitle);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_3_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 5);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_3_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 6);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵtemplate(1, NzSubMenuTitleComponent_Conditional_3_Case_1_Template, 1, 0, \"nz-icon\", 5)(2, NzSubMenuTitleComponent_Conditional_3_Case_2_Template, 1, 0, \"nz-icon\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional((tmp_1_0 = ctx_r0.dir) === \"rtl\" ? 1 : 2);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n}\nconst _c5 = [\"nz-submenu\", \"\"];\nconst _c6 = [[[\"\", \"title\", \"\"]], \"*\"];\nconst _c7 = [\"[title]\", \"*\"];\nfunction NzSubMenuComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzSubMenuComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const subMenuTemplate_r3 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"mode\", ctx_r1.mode)(\"nzOpen\", ctx_r1.nzOpen)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation)(\"menuClass\", ctx_r1.nzMenuClassName)(\"templateOutlet\", subMenuTemplate_r3);\n  }\n}\nfunction NzSubMenuComponent_Conditional_4_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"subMenuMouseState\", function NzSubMenuComponent_Conditional_4_ng_template_0_Template_div_subMenuMouseState_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setMouseEnterState($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const subMenuTemplate_r3 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"theme\", ctx_r1.theme)(\"mode\", ctx_r1.mode)(\"nzOpen\", ctx_r1.nzOpen)(\"position\", ctx_r1.position)(\"nzDisabled\", ctx_r1.nzDisabled)(\"isMenuInsideDropDown\", ctx_r1.isMenuInsideDropDown)(\"nzTriggerSubMenuAction\", ctx_r1.nzTriggerSubMenuAction)(\"templateOutlet\", subMenuTemplate_r3)(\"menuClass\", ctx_r1.nzMenuClassName)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation);\n  }\n}\nfunction NzSubMenuComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, NzSubMenuComponent_Conditional_4_ng_template_0_Template, 1, 11, \"ng-template\", 5);\n    i0.ɵɵlistener(\"positionChange\", function NzSubMenuComponent_Conditional_4_Template_ng_template_positionChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPositionChange($event));\n    })(\"overlayOutsideClick\", function NzSubMenuComponent_Conditional_4_Template_ng_template_overlayOutsideClick_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setMouseEnterState(false));\n    });\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const origin_r6 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"cdkConnectedOverlayPositions\", ctx_r1.overlayPositions)(\"cdkConnectedOverlayOrigin\", origin_r6)(\"cdkConnectedOverlayWidth\", ctx_r1.triggerWidth)(\"cdkConnectedOverlayOpen\", ctx_r1.nzOpen)(\"cdkConnectedOverlayTransformOriginOn\", \".ant-menu-submenu\");\n  }\n}\nfunction NzSubMenuComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nconst _c8 = [\"titleElement\"];\nconst _c9 = [\"nz-menu-group\", \"\"];\nconst _c10 = [\"*\", [[\"\", \"title\", \"\"]]];\nconst _c11 = [\"*\", \"[title]\"];\nfunction NzMenuGroupComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzTitle);\n  }\n}\nfunction NzMenuGroupComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nconst NzIsMenuInsideDropDownToken = new InjectionToken('NzIsInDropDownMenuToken');\nconst NzMenuServiceLocalToken = new InjectionToken('NzMenuServiceLocalToken');\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass MenuService {\n  /** all descendant menu click **/\n  descendantMenuItemClick$ = new Subject();\n  /** child menu item click **/\n  childMenuItemClick$ = new Subject();\n  theme$ = new BehaviorSubject('light');\n  mode$ = new BehaviorSubject('vertical');\n  inlineIndent$ = new BehaviorSubject(24);\n  isChildSubMenuOpen$ = new BehaviorSubject(false);\n  onDescendantMenuItemClick(menu) {\n    this.descendantMenuItemClick$.next(menu);\n  }\n  onChildMenuItemClick(menu) {\n    this.childMenuItemClick$.next(menu);\n  }\n  setMode(mode) {\n    this.mode$.next(mode);\n  }\n  setTheme(theme) {\n    this.theme$.next(theme);\n  }\n  setInlineIndent(indent) {\n    this.inlineIndent$.next(indent);\n  }\n  static ɵfac = function MenuService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MenuService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MenuService,\n    factory: MenuService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSubmenuService {\n  nzMenuService = inject(MenuService);\n  mode$ = this.nzMenuService.mode$.pipe(map(mode => {\n    if (mode === 'inline') {\n      return 'inline';\n      /** if inside another submenu, set the mode to vertical **/\n    } else if (mode === 'vertical' || this.nzHostSubmenuService) {\n      return 'vertical';\n    } else {\n      return 'horizontal';\n    }\n  }));\n  level = 1;\n  isMenuInsideDropDown = inject(NzIsMenuInsideDropDownToken);\n  isCurrentSubMenuOpen$ = new BehaviorSubject(false);\n  isChildSubMenuOpen$ = new BehaviorSubject(false);\n  /** submenu title & overlay mouse enter status **/\n  isMouseEnterTitleOrOverlay$ = new Subject();\n  childMenuItemClick$ = new Subject();\n  destroy$ = new Subject();\n  nzHostSubmenuService = inject(NzSubmenuService, {\n    optional: true,\n    skipSelf: true\n  });\n  /**\n   * menu item inside submenu clicked\n   *\n   * @param menu\n   */\n  onChildMenuItemClick(menu) {\n    this.childMenuItemClick$.next(menu);\n  }\n  setOpenStateWithoutDebounce(value) {\n    this.isCurrentSubMenuOpen$.next(value);\n  }\n  setMouseEnterTitleOrOverlayState(value) {\n    this.isMouseEnterTitleOrOverlay$.next(value);\n  }\n  constructor() {\n    if (this.nzHostSubmenuService) {\n      this.level = this.nzHostSubmenuService.level + 1;\n    }\n    /** close if menu item clicked **/\n    const isClosedByMenuItemClick = this.childMenuItemClick$.pipe(mergeMap(() => this.mode$), filter(mode => mode !== 'inline' || this.isMenuInsideDropDown), map(() => false));\n    const isCurrentSubmenuOpen$ = merge(this.isMouseEnterTitleOrOverlay$, isClosedByMenuItemClick);\n    /** combine the child submenu status with current submenu status to calculate host submenu open **/\n    const isSubMenuOpenWithDebounce$ = combineLatest([this.isChildSubMenuOpen$, isCurrentSubmenuOpen$]).pipe(map(([isChildSubMenuOpen, isCurrentSubmenuOpen]) => isChildSubMenuOpen || isCurrentSubmenuOpen), auditTime(150), distinctUntilChanged(), takeUntil(this.destroy$));\n    isSubMenuOpenWithDebounce$.pipe(distinctUntilChanged()).subscribe(data => {\n      this.setOpenStateWithoutDebounce(data);\n      if (this.nzHostSubmenuService) {\n        /** set parent submenu's child submenu open status **/\n        this.nzHostSubmenuService.isChildSubMenuOpen$.next(data);\n      } else {\n        this.nzMenuService.isChildSubMenuOpen$.next(data);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzSubmenuService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSubmenuService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzSubmenuService,\n    factory: NzSubmenuService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubmenuService, [{\n    type: Injectable\n  }], () => [], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMenuItemComponent {\n  nzMenuService;\n  cdr;\n  destroy$ = new Subject();\n  nzSubmenuService = inject(NzSubmenuService, {\n    optional: true\n  });\n  directionality = inject(Directionality);\n  routerLink = inject(RouterLink, {\n    optional: true\n  });\n  router = inject(Router, {\n    optional: true\n  });\n  isMenuInsideDropDown = inject(NzIsMenuInsideDropDownToken);\n  level = this.nzSubmenuService ? this.nzSubmenuService.level + 1 : 1;\n  selected$ = new Subject();\n  inlinePaddingLeft = null;\n  dir = 'ltr';\n  nzPaddingLeft;\n  nzDisabled = false;\n  nzSelected = false;\n  nzDanger = false;\n  nzMatchRouterExact = false;\n  nzMatchRouter = false;\n  listOfRouterLink;\n  /** clear all item selected status except this */\n  clickMenuItem(e) {\n    if (this.nzDisabled) {\n      e.preventDefault();\n      e.stopPropagation();\n    } else {\n      this.nzMenuService.onDescendantMenuItemClick(this);\n      if (this.nzSubmenuService) {\n        /** menu item inside the submenu **/\n        this.nzSubmenuService.onChildMenuItemClick(this);\n      } else {\n        /** menu item inside the root menu **/\n        this.nzMenuService.onChildMenuItemClick(this);\n      }\n    }\n  }\n  setSelectedState(value) {\n    this.nzSelected = value;\n    this.selected$.next(value);\n  }\n  updateRouterActive() {\n    if (!this.listOfRouterLink || !this.router || !this.router.navigated || !this.nzMatchRouter) {\n      return;\n    }\n    Promise.resolve().then(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      if (this.nzSelected !== hasActiveLinks) {\n        this.nzSelected = hasActiveLinks;\n        this.setSelectedState(this.nzSelected);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  hasActiveLinks() {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return this.routerLink && isActiveCheckFn(this.routerLink) || this.listOfRouterLink.some(isActiveCheckFn);\n  }\n  isLinkActive(router) {\n    return link => router.isActive(link.urlTree || '', {\n      paths: this.nzMatchRouterExact ? 'exact' : 'subset',\n      queryParams: this.nzMatchRouterExact ? 'exact' : 'subset',\n      fragment: 'ignored',\n      matrixParams: 'ignored'\n    });\n  }\n  constructor(nzMenuService, cdr) {\n    this.nzMenuService = nzMenuService;\n    this.cdr = cdr;\n    if (this.router) {\n      this.router.events.pipe(takeUntil(this.destroy$), filter(e => e instanceof NavigationEnd)).subscribe(() => {\n        this.updateRouterActive();\n      });\n    }\n  }\n  ngOnInit() {\n    /** store origin padding in padding */\n    combineLatest([this.nzMenuService.mode$, this.nzMenuService.inlineIndent$]).pipe(takeUntil(this.destroy$)).subscribe(([mode, inlineIndent]) => {\n      this.inlinePaddingLeft = mode === 'inline' ? this.level * inlineIndent : null;\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngAfterContentInit() {\n    this.listOfRouterLink.changes.pipe(takeUntil(this.destroy$)).subscribe(() => this.updateRouterActive());\n    this.updateRouterActive();\n  }\n  ngOnChanges(changes) {\n    if (changes.nzSelected) {\n      this.setSelectedState(this.nzSelected);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzMenuItemComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzMenuItemComponent)(i0.ɵɵdirectiveInject(MenuService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzMenuItemComponent,\n    selectors: [[\"\", \"nz-menu-item\", \"\"]],\n    contentQueries: function NzMenuItemComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, RouterLink, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);\n      }\n    },\n    hostVars: 20,\n    hostBindings: function NzMenuItemComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function NzMenuItemComponent_click_HostBindingHandler($event) {\n          return ctx.clickMenuItem($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"padding-left\", ctx.dir === \"rtl\" ? null : ctx.nzPaddingLeft || ctx.inlinePaddingLeft, \"px\")(\"padding-right\", ctx.dir === \"rtl\" ? ctx.nzPaddingLeft || ctx.inlinePaddingLeft : null, \"px\");\n        i0.ɵɵclassProp(\"ant-dropdown-menu-item\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-item-selected\", ctx.isMenuInsideDropDown && ctx.nzSelected)(\"ant-dropdown-menu-item-danger\", ctx.isMenuInsideDropDown && ctx.nzDanger)(\"ant-dropdown-menu-item-disabled\", ctx.isMenuInsideDropDown && ctx.nzDisabled)(\"ant-menu-item\", !ctx.isMenuInsideDropDown)(\"ant-menu-item-selected\", !ctx.isMenuInsideDropDown && ctx.nzSelected)(\"ant-menu-item-danger\", !ctx.isMenuInsideDropDown && ctx.nzDanger)(\"ant-menu-item-disabled\", !ctx.isMenuInsideDropDown && ctx.nzDisabled);\n      }\n    },\n    inputs: {\n      nzPaddingLeft: [2, \"nzPaddingLeft\", \"nzPaddingLeft\", numberAttributeWithZeroFallback],\n      nzDisabled: [2, \"nzDisabled\", \"nzDisabled\", booleanAttribute],\n      nzSelected: [2, \"nzSelected\", \"nzSelected\", booleanAttribute],\n      nzDanger: [2, \"nzDanger\", \"nzDanger\", booleanAttribute],\n      nzMatchRouterExact: [2, \"nzMatchRouterExact\", \"nzMatchRouterExact\", booleanAttribute],\n      nzMatchRouter: [2, \"nzMatchRouter\", \"nzMatchRouter\", booleanAttribute]\n    },\n    exportAs: [\"nzMenuItem\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    attrs: _c0,\n    ngContentSelectors: _c1,\n    decls: 2,\n    vars: 0,\n    consts: [[1, \"ant-menu-title-content\"]],\n    template: function NzMenuItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuItemComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-menu-item]',\n      exportAs: 'nzMenuItem',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      preserveWhitespaces: false,\n      template: `\n    <span class=\"ant-menu-title-content\">\n      <ng-content></ng-content>\n    </span>\n  `,\n      host: {\n        '[class.ant-dropdown-menu-item]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-item-selected]': `isMenuInsideDropDown && nzSelected`,\n        '[class.ant-dropdown-menu-item-danger]': `isMenuInsideDropDown && nzDanger`,\n        '[class.ant-dropdown-menu-item-disabled]': `isMenuInsideDropDown && nzDisabled`,\n        '[class.ant-menu-item]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-item-selected]': `!isMenuInsideDropDown && nzSelected`,\n        '[class.ant-menu-item-danger]': `!isMenuInsideDropDown && nzDanger`,\n        '[class.ant-menu-item-disabled]': `!isMenuInsideDropDown && nzDisabled`,\n        '[style.paddingLeft.px]': `dir === 'rtl' ? null : nzPaddingLeft || inlinePaddingLeft`,\n        '[style.paddingRight.px]': `dir === 'rtl' ? nzPaddingLeft || inlinePaddingLeft : null`,\n        '(click)': 'clickMenuItem($event)'\n      }\n    }]\n  }], () => [{\n    type: MenuService\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    nzPaddingLeft: [{\n      type: Input,\n      args: [{\n        transform: numberAttributeWithZeroFallback\n      }]\n    }],\n    nzDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzSelected: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzDanger: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzMatchRouterExact: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzMatchRouter: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    listOfRouterLink: [{\n      type: ContentChildren,\n      args: [RouterLink, {\n        descendants: true\n      }]\n    }]\n  });\n})();\nclass NzSubmenuInlineChildComponent {\n  elementRef;\n  renderer;\n  directionality;\n  templateOutlet = null;\n  menuClass = '';\n  mode = 'vertical';\n  nzOpen = false;\n  listOfCacheClassName = [];\n  expandState = 'collapsed';\n  dir = 'ltr';\n  destroy$ = new Subject();\n  constructor(elementRef, renderer, directionality) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.directionality = directionality;\n  }\n  calcMotionState() {\n    if (this.nzOpen) {\n      this.expandState = 'expanded';\n    } else {\n      this.expandState = 'collapsed';\n    }\n  }\n  ngOnInit() {\n    this.calcMotionState();\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      mode,\n      nzOpen,\n      menuClass\n    } = changes;\n    if (mode || nzOpen) {\n      this.calcMotionState();\n    }\n    if (menuClass) {\n      if (this.listOfCacheClassName.length) {\n        this.listOfCacheClassName.filter(item => !!item).forEach(className => {\n          this.renderer.removeClass(this.elementRef.nativeElement, className);\n        });\n      }\n      if (this.menuClass) {\n        this.listOfCacheClassName = this.menuClass.split(' ');\n        this.listOfCacheClassName.filter(item => !!item).forEach(className => {\n          this.renderer.addClass(this.elementRef.nativeElement, className);\n        });\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzSubmenuInlineChildComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSubmenuInlineChildComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzSubmenuInlineChildComponent,\n    selectors: [[\"\", \"nz-submenu-inline-child\", \"\"]],\n    hostAttrs: [1, \"ant-menu\", \"ant-menu-inline\", \"ant-menu-sub\"],\n    hostVars: 3,\n    hostBindings: function NzSubmenuInlineChildComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵsyntheticHostProperty(\"@collapseMotion\", ctx.expandState);\n        i0.ɵɵclassProp(\"ant-menu-rtl\", ctx.dir === \"rtl\");\n      }\n    },\n    inputs: {\n      templateOutlet: \"templateOutlet\",\n      menuClass: \"menuClass\",\n      mode: \"mode\",\n      nzOpen: \"nzOpen\"\n    },\n    exportAs: [\"nzSubmenuInlineChild\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    attrs: _c2,\n    decls: 1,\n    vars: 1,\n    consts: [[3, \"ngTemplateOutlet\"]],\n    template: function NzSubmenuInlineChildComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzSubmenuInlineChildComponent_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.templateOutlet);\n      }\n    },\n    dependencies: [NgTemplateOutlet],\n    encapsulation: 2,\n    data: {\n      animation: [collapseMotion]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubmenuInlineChildComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu-inline-child]',\n      animations: [collapseMotion],\n      exportAs: 'nzSubmenuInlineChild',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template> `,\n      host: {\n        class: 'ant-menu ant-menu-inline ant-menu-sub',\n        '[class.ant-menu-rtl]': `dir === 'rtl'`,\n        '[@collapseMotion]': 'expandState'\n      },\n      imports: [NgTemplateOutlet]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i1.Directionality\n  }], {\n    templateOutlet: [{\n      type: Input\n    }],\n    menuClass: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    nzOpen: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSubmenuNoneInlineChildComponent {\n  directionality;\n  menuClass = '';\n  theme = 'light';\n  templateOutlet = null;\n  isMenuInsideDropDown = false;\n  mode = 'vertical';\n  nzTriggerSubMenuAction = 'hover';\n  position = 'right';\n  nzDisabled = false;\n  nzOpen = false;\n  subMenuMouseState = new EventEmitter();\n  constructor(directionality) {\n    this.directionality = directionality;\n  }\n  setMouseState(state) {\n    if (!this.nzDisabled && this.nzTriggerSubMenuAction === 'hover') {\n      this.subMenuMouseState.next(state);\n    }\n  }\n  expandState = 'collapsed';\n  dir = 'ltr';\n  destroy$ = new Subject();\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  calcMotionState() {\n    if (this.nzOpen) {\n      if (this.mode === 'horizontal') {\n        this.expandState = 'bottom';\n      } else if (this.mode === 'vertical') {\n        this.expandState = 'active';\n      }\n    } else {\n      this.expandState = 'collapsed';\n    }\n  }\n  ngOnInit() {\n    this.calcMotionState();\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      mode,\n      nzOpen\n    } = changes;\n    if (mode || nzOpen) {\n      this.calcMotionState();\n    }\n  }\n  static ɵfac = function NzSubmenuNoneInlineChildComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSubmenuNoneInlineChildComponent)(i0.ɵɵdirectiveInject(i1.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzSubmenuNoneInlineChildComponent,\n    selectors: [[\"\", \"nz-submenu-none-inline-child\", \"\"]],\n    hostAttrs: [1, \"ant-menu-submenu\", \"ant-menu-submenu-popup\"],\n    hostVars: 14,\n    hostBindings: function NzSubmenuNoneInlineChildComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"mouseenter\", function NzSubmenuNoneInlineChildComponent_mouseenter_HostBindingHandler() {\n          return ctx.setMouseState(true);\n        })(\"mouseleave\", function NzSubmenuNoneInlineChildComponent_mouseleave_HostBindingHandler() {\n          return ctx.setMouseState(false);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵsyntheticHostProperty(\"@slideMotion\", ctx.expandState)(\"@zoomBigMotion\", ctx.expandState);\n        i0.ɵɵclassProp(\"ant-menu-light\", ctx.theme === \"light\")(\"ant-menu-dark\", ctx.theme === \"dark\")(\"ant-menu-submenu-placement-bottom\", ctx.mode === \"horizontal\")(\"ant-menu-submenu-placement-right\", ctx.mode === \"vertical\" && ctx.position === \"right\")(\"ant-menu-submenu-placement-left\", ctx.mode === \"vertical\" && ctx.position === \"left\")(\"ant-menu-submenu-rtl\", ctx.dir === \"rtl\");\n      }\n    },\n    inputs: {\n      menuClass: \"menuClass\",\n      theme: \"theme\",\n      templateOutlet: \"templateOutlet\",\n      isMenuInsideDropDown: \"isMenuInsideDropDown\",\n      mode: \"mode\",\n      nzTriggerSubMenuAction: \"nzTriggerSubMenuAction\",\n      position: \"position\",\n      nzDisabled: \"nzDisabled\",\n      nzOpen: \"nzOpen\"\n    },\n    outputs: {\n      subMenuMouseState: \"subMenuMouseState\"\n    },\n    exportAs: [\"nzSubmenuNoneInlineChild\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    attrs: _c3,\n    decls: 2,\n    vars: 17,\n    consts: [[3, \"ngTemplateOutlet\"]],\n    template: function NzSubmenuNoneInlineChildComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\");\n        i0.ɵɵtemplate(1, NzSubmenuNoneInlineChildComponent_ng_template_1_Template, 0, 0, \"ng-template\", 0);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.menuClass);\n        i0.ɵɵclassProp(\"ant-dropdown-menu\", ctx.isMenuInsideDropDown)(\"ant-menu\", !ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-vertical\", ctx.isMenuInsideDropDown)(\"ant-menu-vertical\", !ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-sub\", ctx.isMenuInsideDropDown)(\"ant-menu-sub\", !ctx.isMenuInsideDropDown)(\"ant-menu-rtl\", ctx.dir === \"rtl\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.templateOutlet);\n      }\n    },\n    dependencies: [NgTemplateOutlet],\n    encapsulation: 2,\n    data: {\n      animation: [zoomBigMotion, slideMotion]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubmenuNoneInlineChildComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu-none-inline-child]',\n      exportAs: 'nzSubmenuNoneInlineChild',\n      encapsulation: ViewEncapsulation.None,\n      animations: [zoomBigMotion, slideMotion],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div\n      [class.ant-dropdown-menu]=\"isMenuInsideDropDown\"\n      [class.ant-menu]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-vertical]=\"isMenuInsideDropDown\"\n      [class.ant-menu-vertical]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-sub]=\"isMenuInsideDropDown\"\n      [class.ant-menu-sub]=\"!isMenuInsideDropDown\"\n      [class.ant-menu-rtl]=\"dir === 'rtl'\"\n      [class]=\"menuClass\"\n    >\n      <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template>\n    </div>\n  `,\n      host: {\n        class: 'ant-menu-submenu ant-menu-submenu-popup',\n        '[class.ant-menu-light]': \"theme === 'light'\",\n        '[class.ant-menu-dark]': \"theme === 'dark'\",\n        '[class.ant-menu-submenu-placement-bottom]': \"mode === 'horizontal'\",\n        '[class.ant-menu-submenu-placement-right]': \"mode === 'vertical' && position === 'right'\",\n        '[class.ant-menu-submenu-placement-left]': \"mode === 'vertical' && position === 'left'\",\n        '[class.ant-menu-submenu-rtl]': 'dir ===\"rtl\"',\n        '[@slideMotion]': 'expandState',\n        '[@zoomBigMotion]': 'expandState',\n        '(mouseenter)': 'setMouseState(true)',\n        '(mouseleave)': 'setMouseState(false)'\n      },\n      imports: [NgTemplateOutlet]\n    }]\n  }], () => [{\n    type: i1.Directionality\n  }], {\n    menuClass: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    templateOutlet: [{\n      type: Input\n    }],\n    isMenuInsideDropDown: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    nzTriggerSubMenuAction: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzOpen: [{\n      type: Input\n    }],\n    subMenuMouseState: [{\n      type: Output\n    }]\n  });\n})();\nclass NzSubMenuTitleComponent {\n  cdr;\n  directionality;\n  nzIcon = null;\n  nzTitle = null;\n  isMenuInsideDropDown = false;\n  nzDisabled = false;\n  paddingLeft = null;\n  mode = 'vertical';\n  nzTriggerSubMenuAction = 'hover';\n  toggleSubMenu = new EventEmitter();\n  subMenuMouseState = new EventEmitter();\n  dir = 'ltr';\n  destroy$ = new Subject();\n  constructor(cdr, directionality) {\n    this.cdr = cdr;\n    this.directionality = directionality;\n  }\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setMouseState(state) {\n    if (!this.nzDisabled && this.nzTriggerSubMenuAction === 'hover') {\n      this.subMenuMouseState.next(state);\n    }\n  }\n  clickTitle() {\n    if ((this.mode === 'inline' || this.nzTriggerSubMenuAction === 'click') && !this.nzDisabled) {\n      this.subMenuMouseState.next(true);\n      this.toggleSubMenu.emit();\n    }\n  }\n  static ɵfac = function NzSubMenuTitleComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSubMenuTitleComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzSubMenuTitleComponent,\n    selectors: [[\"\", \"nz-submenu-title\", \"\"]],\n    hostVars: 8,\n    hostBindings: function NzSubMenuTitleComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function NzSubMenuTitleComponent_click_HostBindingHandler() {\n          return ctx.clickTitle();\n        })(\"mouseenter\", function NzSubMenuTitleComponent_mouseenter_HostBindingHandler() {\n          return ctx.setMouseState(true);\n        })(\"mouseleave\", function NzSubMenuTitleComponent_mouseleave_HostBindingHandler() {\n          return ctx.setMouseState(false);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"padding-left\", ctx.dir === \"rtl\" ? null : ctx.paddingLeft, \"px\")(\"padding-right\", ctx.dir === \"rtl\" ? ctx.paddingLeft : null, \"px\");\n        i0.ɵɵclassProp(\"ant-dropdown-menu-submenu-title\", ctx.isMenuInsideDropDown)(\"ant-menu-submenu-title\", !ctx.isMenuInsideDropDown);\n      }\n    },\n    inputs: {\n      nzIcon: \"nzIcon\",\n      nzTitle: \"nzTitle\",\n      isMenuInsideDropDown: \"isMenuInsideDropDown\",\n      nzDisabled: \"nzDisabled\",\n      paddingLeft: \"paddingLeft\",\n      mode: \"mode\",\n      nzTriggerSubMenuAction: \"nzTriggerSubMenuAction\"\n    },\n    outputs: {\n      toggleSubMenu: \"toggleSubMenu\",\n      subMenuMouseState: \"subMenuMouseState\"\n    },\n    exportAs: [\"nzSubmenuTitle\"],\n    attrs: _c4,\n    ngContentSelectors: _c1,\n    decls: 5,\n    vars: 3,\n    consts: [[3, \"nzType\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-dropdown-menu-submenu-expand-icon\"], [1, \"ant-menu-submenu-arrow\"], [1, \"ant-menu-title-content\"], [\"nzType\", \"left\", 1, \"ant-dropdown-menu-submenu-arrow-icon\"], [\"nzType\", \"right\", 1, \"ant-dropdown-menu-submenu-arrow-icon\"]],\n    template: function NzSubMenuTitleComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NzSubMenuTitleComponent_Conditional_0_Template, 1, 1, \"nz-icon\", 0)(1, NzSubMenuTitleComponent_ng_container_1_Template, 3, 1, \"ng-container\", 1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵtemplate(3, NzSubMenuTitleComponent_Conditional_3_Template, 3, 1, \"span\", 2)(4, NzSubMenuTitleComponent_Conditional_4_Template, 1, 0, \"span\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.nzIcon ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzTitle);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx.isMenuInsideDropDown ? 3 : 4);\n      }\n    },\n    dependencies: [NzIconModule, i2.NzIconDirective, NzOutletModule, i3.NzStringTemplateOutletDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubMenuTitleComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu-title]',\n      exportAs: 'nzSubmenuTitle',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (nzIcon) {\n      <nz-icon [nzType]=\"nzIcon\" />\n    }\n    <ng-container *nzStringTemplateOutlet=\"nzTitle\">\n      <span class=\"ant-menu-title-content\">{{ nzTitle }}</span>\n    </ng-container>\n    <ng-content />\n    @if (isMenuInsideDropDown) {\n      <span class=\"ant-dropdown-menu-submenu-expand-icon\">\n        @switch (dir) {\n          @case ('rtl') {\n            <nz-icon nzType=\"left\" class=\"ant-dropdown-menu-submenu-arrow-icon\" />\n          }\n          @default {\n            <nz-icon nzType=\"right\" class=\"ant-dropdown-menu-submenu-arrow-icon\" />\n          }\n        }\n      </span>\n    } @else {\n      <span class=\"ant-menu-submenu-arrow\"></span>\n    }\n  `,\n      host: {\n        '[class.ant-dropdown-menu-submenu-title]': 'isMenuInsideDropDown',\n        '[class.ant-menu-submenu-title]': '!isMenuInsideDropDown',\n        '[style.paddingLeft.px]': `dir === 'rtl' ? null : paddingLeft `,\n        '[style.paddingRight.px]': `dir === 'rtl' ? paddingLeft : null`,\n        '(click)': 'clickTitle()',\n        '(mouseenter)': 'setMouseState(true)',\n        '(mouseleave)': 'setMouseState(false)'\n      },\n      imports: [NzIconModule, NzOutletModule]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality\n  }], {\n    nzIcon: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    isMenuInsideDropDown: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    paddingLeft: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    nzTriggerSubMenuAction: [{\n      type: Input\n    }],\n    toggleSubMenu: [{\n      type: Output\n    }],\n    subMenuMouseState: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst listOfVerticalPositions = [POSITION_MAP.rightTop, POSITION_MAP.right, POSITION_MAP.rightBottom, POSITION_MAP.leftTop, POSITION_MAP.left, POSITION_MAP.leftBottom];\nconst listOfHorizontalPositions = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];\nclass NzSubMenuComponent {\n  nzMenuService;\n  cdr;\n  platform;\n  nzMenuClassName = '';\n  nzPaddingLeft = null;\n  nzTitle = null;\n  nzIcon = null;\n  nzTriggerSubMenuAction = 'hover';\n  nzOpen = false;\n  nzDisabled = false;\n  nzPlacement = 'bottomLeft';\n  nzOpenChange = new EventEmitter();\n  cdkOverlayOrigin = null;\n  // fix errors about circular dependency\n  // Can't construct a query for the property ... since the query selector wasn't defined\"\n  listOfNzSubMenuComponent = null;\n  listOfNzMenuItemDirective = null;\n  nzSubmenuService = inject(NzSubmenuService);\n  level = this.nzSubmenuService.level;\n  destroy$ = new Subject();\n  position = 'right';\n  triggerWidth = null;\n  theme = 'light';\n  mode = 'vertical';\n  inlinePaddingLeft = null;\n  overlayPositions = listOfVerticalPositions;\n  isSelected = false;\n  isActive = false;\n  dir = 'ltr';\n  isMenuInsideDropDown = inject(NzIsMenuInsideDropDownToken);\n  noAnimation = inject(NzNoAnimationDirective, {\n    optional: true,\n    host: true\n  });\n  directionality = inject(Directionality);\n  /** set the submenu host open status directly **/\n  setOpenStateWithoutDebounce(open) {\n    this.nzSubmenuService.setOpenStateWithoutDebounce(open);\n  }\n  toggleSubMenu() {\n    this.setOpenStateWithoutDebounce(!this.nzOpen);\n  }\n  setMouseEnterState(value) {\n    this.isActive = value;\n    if (this.mode !== 'inline') {\n      this.nzSubmenuService.setMouseEnterTitleOrOverlayState(value);\n    }\n  }\n  setTriggerWidth() {\n    if (this.mode === 'horizontal' && this.platform.isBrowser && this.cdkOverlayOrigin && this.nzPlacement === 'bottomLeft') {\n      /** TODO: fast dom **/\n      this.triggerWidth = this.cdkOverlayOrigin.nativeElement.getBoundingClientRect().width;\n    }\n  }\n  onPositionChange(position) {\n    const placement = getPlacementName(position);\n    if (placement === 'rightTop' || placement === 'rightBottom' || placement === 'right') {\n      this.position = 'right';\n    } else if (placement === 'leftTop' || placement === 'leftBottom' || placement === 'left') {\n      this.position = 'left';\n    }\n  }\n  constructor(nzMenuService, cdr, platform) {\n    this.nzMenuService = nzMenuService;\n    this.cdr = cdr;\n    this.platform = platform;\n  }\n  ngOnInit() {\n    /** submenu theme update **/\n    this.nzMenuService.theme$.pipe(takeUntil(this.destroy$)).subscribe(theme => {\n      this.theme = theme;\n      this.cdr.markForCheck();\n    });\n    /** submenu mode update **/\n    this.nzSubmenuService.mode$.pipe(takeUntil(this.destroy$)).subscribe(mode => {\n      this.mode = mode;\n      if (mode === 'horizontal') {\n        this.overlayPositions = [POSITION_MAP[this.nzPlacement], ...listOfHorizontalPositions];\n      } else if (mode === 'vertical') {\n        this.overlayPositions = listOfVerticalPositions;\n      }\n      this.cdr.markForCheck();\n    });\n    /** inlineIndent update **/\n    combineLatest([this.nzSubmenuService.mode$, this.nzMenuService.inlineIndent$]).pipe(takeUntil(this.destroy$)).subscribe(([mode, inlineIndent]) => {\n      this.inlinePaddingLeft = mode === 'inline' ? this.level * inlineIndent : null;\n      this.cdr.markForCheck();\n    });\n    /** current submenu open status **/\n    this.nzSubmenuService.isCurrentSubMenuOpen$.pipe(takeUntil(this.destroy$)).subscribe(open => {\n      this.isActive = open;\n      if (open !== this.nzOpen) {\n        this.setTriggerWidth();\n        this.nzOpen = open;\n        this.nzOpenChange.emit(this.nzOpen);\n        this.cdr.markForCheck();\n      }\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.markForCheck();\n    });\n  }\n  ngAfterContentInit() {\n    this.setTriggerWidth();\n    const listOfNzMenuItemDirective = this.listOfNzMenuItemDirective;\n    const changes = listOfNzMenuItemDirective.changes;\n    const mergedObservable = merge(...[changes, ...listOfNzMenuItemDirective.map(menu => menu.selected$)]);\n    changes.pipe(startWith(listOfNzMenuItemDirective), switchMap(() => mergedObservable), startWith(true), map(() => listOfNzMenuItemDirective.some(e => e.nzSelected)), takeUntil(this.destroy$)).subscribe(selected => {\n      this.isSelected = selected;\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzOpen\n    } = changes;\n    if (nzOpen) {\n      this.nzSubmenuService.setOpenStateWithoutDebounce(this.nzOpen);\n      this.setTriggerWidth();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzSubMenuComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzSubMenuComponent)(i0.ɵɵdirectiveInject(MenuService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2$1.Platform));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzSubMenuComponent,\n    selectors: [[\"\", \"nz-submenu\", \"\"]],\n    contentQueries: function NzSubMenuComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, NzSubMenuComponent, 5);\n        i0.ɵɵcontentQuery(dirIndex, NzMenuItemComponent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzSubMenuComponent = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzMenuItemDirective = _t);\n      }\n    },\n    viewQuery: function NzSubMenuComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);\n      }\n    },\n    hostVars: 34,\n    hostBindings: function NzSubMenuComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-dropdown-menu-submenu\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-submenu-disabled\", ctx.isMenuInsideDropDown && ctx.nzDisabled)(\"ant-dropdown-menu-submenu-open\", ctx.isMenuInsideDropDown && ctx.nzOpen)(\"ant-dropdown-menu-submenu-selected\", ctx.isMenuInsideDropDown && ctx.isSelected)(\"ant-dropdown-menu-submenu-vertical\", ctx.isMenuInsideDropDown && ctx.mode === \"vertical\")(\"ant-dropdown-menu-submenu-horizontal\", ctx.isMenuInsideDropDown && ctx.mode === \"horizontal\")(\"ant-dropdown-menu-submenu-inline\", ctx.isMenuInsideDropDown && ctx.mode === \"inline\")(\"ant-dropdown-menu-submenu-active\", ctx.isMenuInsideDropDown && ctx.isActive)(\"ant-menu-submenu\", !ctx.isMenuInsideDropDown)(\"ant-menu-submenu-disabled\", !ctx.isMenuInsideDropDown && ctx.nzDisabled)(\"ant-menu-submenu-open\", !ctx.isMenuInsideDropDown && ctx.nzOpen)(\"ant-menu-submenu-selected\", !ctx.isMenuInsideDropDown && ctx.isSelected)(\"ant-menu-submenu-vertical\", !ctx.isMenuInsideDropDown && ctx.mode === \"vertical\")(\"ant-menu-submenu-horizontal\", !ctx.isMenuInsideDropDown && ctx.mode === \"horizontal\")(\"ant-menu-submenu-inline\", !ctx.isMenuInsideDropDown && ctx.mode === \"inline\")(\"ant-menu-submenu-active\", !ctx.isMenuInsideDropDown && ctx.isActive)(\"ant-menu-submenu-rtl\", ctx.dir === \"rtl\");\n      }\n    },\n    inputs: {\n      nzMenuClassName: \"nzMenuClassName\",\n      nzPaddingLeft: \"nzPaddingLeft\",\n      nzTitle: \"nzTitle\",\n      nzIcon: \"nzIcon\",\n      nzTriggerSubMenuAction: \"nzTriggerSubMenuAction\",\n      nzOpen: [2, \"nzOpen\", \"nzOpen\", booleanAttribute],\n      nzDisabled: [2, \"nzDisabled\", \"nzDisabled\", booleanAttribute],\n      nzPlacement: \"nzPlacement\"\n    },\n    outputs: {\n      nzOpenChange: \"nzOpenChange\"\n    },\n    exportAs: [\"nzSubmenu\"],\n    features: [i0.ɵɵProvidersFeature([NzSubmenuService]), i0.ɵɵNgOnChangesFeature],\n    attrs: _c5,\n    ngContentSelectors: _c7,\n    decls: 7,\n    vars: 9,\n    consts: [[\"origin\", \"cdkOverlayOrigin\"], [\"subMenuTemplate\", \"\"], [\"nz-submenu-title\", \"\", \"cdkOverlayOrigin\", \"\", 3, \"subMenuMouseState\", \"toggleSubMenu\", \"nzIcon\", \"nzTitle\", \"mode\", \"nzDisabled\", \"isMenuInsideDropDown\", \"paddingLeft\", \"nzTriggerSubMenuAction\"], [\"nz-submenu-inline-child\", \"\", 3, \"mode\", \"nzOpen\", \"nzNoAnimation\", \"menuClass\", \"templateOutlet\"], [\"cdkConnectedOverlay\", \"\", 3, \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayTransformOriginOn\"], [\"cdkConnectedOverlay\", \"\", 3, \"positionChange\", \"overlayOutsideClick\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayTransformOriginOn\"], [\"nz-submenu-none-inline-child\", \"\", 3, \"subMenuMouseState\", \"theme\", \"mode\", \"nzOpen\", \"position\", \"nzDisabled\", \"isMenuInsideDropDown\", \"nzTriggerSubMenuAction\", \"templateOutlet\", \"menuClass\", \"nzNoAnimation\"]],\n    template: function NzSubMenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c6);\n        i0.ɵɵelementStart(0, \"div\", 2, 0);\n        i0.ɵɵlistener(\"subMenuMouseState\", function NzSubMenuComponent_Template_div_subMenuMouseState_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setMouseEnterState($event));\n        })(\"toggleSubMenu\", function NzSubMenuComponent_Template_div_toggleSubMenu_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggleSubMenu());\n        });\n        i0.ɵɵtemplate(2, NzSubMenuComponent_Conditional_2_Template, 1, 0);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, NzSubMenuComponent_Conditional_3_Template, 1, 6, \"div\", 3)(4, NzSubMenuComponent_Conditional_4_Template, 1, 5, null, 4)(5, NzSubMenuComponent_ng_template_5_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"nzIcon\", ctx.nzIcon)(\"nzTitle\", ctx.nzTitle)(\"mode\", ctx.mode)(\"nzDisabled\", ctx.nzDisabled)(\"isMenuInsideDropDown\", ctx.isMenuInsideDropDown)(\"paddingLeft\", ctx.nzPaddingLeft || ctx.inlinePaddingLeft)(\"nzTriggerSubMenuAction\", ctx.nzTriggerSubMenuAction);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(!ctx.nzTitle ? 2 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.mode === \"inline\" ? 3 : 4);\n      }\n    },\n    dependencies: [NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzNoAnimationDirective, NzSubmenuNoneInlineChildComponent, OverlayModule, i3$1.CdkConnectedOverlay, i3$1.CdkOverlayOrigin],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubMenuComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu]',\n      exportAs: 'nzSubmenu',\n      providers: [NzSubmenuService],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      template: `\n    <div\n      nz-submenu-title\n      cdkOverlayOrigin\n      #origin=\"cdkOverlayOrigin\"\n      [nzIcon]=\"nzIcon\"\n      [nzTitle]=\"nzTitle\"\n      [mode]=\"mode\"\n      [nzDisabled]=\"nzDisabled\"\n      [isMenuInsideDropDown]=\"isMenuInsideDropDown\"\n      [paddingLeft]=\"nzPaddingLeft || inlinePaddingLeft\"\n      [nzTriggerSubMenuAction]=\"nzTriggerSubMenuAction\"\n      (subMenuMouseState)=\"setMouseEnterState($event)\"\n      (toggleSubMenu)=\"toggleSubMenu()\"\n    >\n      @if (!nzTitle) {\n        <ng-content select=\"[title]\" />\n      }\n    </div>\n    @if (mode === 'inline') {\n      <div\n        nz-submenu-inline-child\n        [mode]=\"mode\"\n        [nzOpen]=\"nzOpen\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [menuClass]=\"nzMenuClassName\"\n        [templateOutlet]=\"subMenuTemplate\"\n      ></div>\n    } @else {\n      <ng-template\n        cdkConnectedOverlay\n        (positionChange)=\"onPositionChange($event)\"\n        [cdkConnectedOverlayPositions]=\"overlayPositions\"\n        [cdkConnectedOverlayOrigin]=\"origin\"\n        [cdkConnectedOverlayWidth]=\"triggerWidth!\"\n        [cdkConnectedOverlayOpen]=\"nzOpen\"\n        [cdkConnectedOverlayTransformOriginOn]=\"'.ant-menu-submenu'\"\n        (overlayOutsideClick)=\"setMouseEnterState(false)\"\n      >\n        <div\n          nz-submenu-none-inline-child\n          [theme]=\"theme\"\n          [mode]=\"mode\"\n          [nzOpen]=\"nzOpen\"\n          [position]=\"position\"\n          [nzDisabled]=\"nzDisabled\"\n          [isMenuInsideDropDown]=\"isMenuInsideDropDown\"\n          [nzTriggerSubMenuAction]=\"nzTriggerSubMenuAction\"\n          [templateOutlet]=\"subMenuTemplate\"\n          [menuClass]=\"nzMenuClassName\"\n          [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n          [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n          (subMenuMouseState)=\"setMouseEnterState($event)\"\n        ></div>\n      </ng-template>\n    }\n\n    <ng-template #subMenuTemplate>\n      <ng-content />\n    </ng-template>\n  `,\n      host: {\n        '[class.ant-dropdown-menu-submenu]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-submenu-disabled]': `isMenuInsideDropDown && nzDisabled`,\n        '[class.ant-dropdown-menu-submenu-open]': `isMenuInsideDropDown && nzOpen`,\n        '[class.ant-dropdown-menu-submenu-selected]': `isMenuInsideDropDown && isSelected`,\n        '[class.ant-dropdown-menu-submenu-vertical]': `isMenuInsideDropDown && mode === 'vertical'`,\n        '[class.ant-dropdown-menu-submenu-horizontal]': `isMenuInsideDropDown && mode === 'horizontal'`,\n        '[class.ant-dropdown-menu-submenu-inline]': `isMenuInsideDropDown && mode === 'inline'`,\n        '[class.ant-dropdown-menu-submenu-active]': `isMenuInsideDropDown && isActive`,\n        '[class.ant-menu-submenu]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-submenu-disabled]': `!isMenuInsideDropDown && nzDisabled`,\n        '[class.ant-menu-submenu-open]': `!isMenuInsideDropDown && nzOpen`,\n        '[class.ant-menu-submenu-selected]': `!isMenuInsideDropDown && isSelected`,\n        '[class.ant-menu-submenu-vertical]': `!isMenuInsideDropDown && mode === 'vertical'`,\n        '[class.ant-menu-submenu-horizontal]': `!isMenuInsideDropDown && mode === 'horizontal'`,\n        '[class.ant-menu-submenu-inline]': `!isMenuInsideDropDown && mode === 'inline'`,\n        '[class.ant-menu-submenu-active]': `!isMenuInsideDropDown && isActive`,\n        '[class.ant-menu-submenu-rtl]': `dir === 'rtl'`\n      },\n      imports: [NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzNoAnimationDirective, NzSubmenuNoneInlineChildComponent, OverlayModule]\n    }]\n  }], () => [{\n    type: MenuService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2$1.Platform\n  }], {\n    nzMenuClassName: [{\n      type: Input\n    }],\n    nzPaddingLeft: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzIcon: [{\n      type: Input\n    }],\n    nzTriggerSubMenuAction: [{\n      type: Input\n    }],\n    nzOpen: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzPlacement: [{\n      type: Input\n    }],\n    nzOpenChange: [{\n      type: Output\n    }],\n    cdkOverlayOrigin: [{\n      type: ViewChild,\n      args: [CdkOverlayOrigin, {\n        static: true,\n        read: ElementRef\n      }]\n    }],\n    listOfNzSubMenuComponent: [{\n      type: ContentChildren,\n      args: [forwardRef(() => NzSubMenuComponent), {\n        descendants: true\n      }]\n    }],\n    listOfNzMenuItemDirective: [{\n      type: ContentChildren,\n      args: [NzMenuItemComponent, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction MenuServiceFactory() {\n  const serviceInsideDropDown = inject(MenuService, {\n    skipSelf: true,\n    optional: true\n  });\n  const serviceOutsideDropDown = inject(NzMenuServiceLocalToken);\n  return serviceInsideDropDown ?? serviceOutsideDropDown;\n}\nfunction MenuDropDownTokenFactory() {\n  const isMenuInsideDropDownToken = inject(NzIsMenuInsideDropDownToken, {\n    skipSelf: true,\n    optional: true\n  });\n  return isMenuInsideDropDownToken ?? false;\n}\nclass NzMenuDirective {\n  nzMenuService;\n  cdr;\n  listOfNzMenuItemDirective;\n  isMenuInsideDropDown = inject(NzIsMenuInsideDropDownToken);\n  listOfNzSubMenuComponent;\n  nzInlineIndent = 24;\n  nzTheme = 'light';\n  nzMode = 'vertical';\n  nzInlineCollapsed = false;\n  nzSelectable = !this.isMenuInsideDropDown;\n  nzClick = new EventEmitter();\n  actualMode = 'vertical';\n  dir = 'ltr';\n  inlineCollapsed$ = new BehaviorSubject(this.nzInlineCollapsed);\n  mode$ = new BehaviorSubject(this.nzMode);\n  destroy$ = new Subject();\n  listOfOpenedNzSubMenuComponent = [];\n  directionality = inject(Directionality);\n  setInlineCollapsed(inlineCollapsed) {\n    this.nzInlineCollapsed = inlineCollapsed;\n    this.inlineCollapsed$.next(inlineCollapsed);\n  }\n  updateInlineCollapse() {\n    if (this.listOfNzMenuItemDirective) {\n      if (this.nzInlineCollapsed) {\n        this.listOfOpenedNzSubMenuComponent = this.listOfNzSubMenuComponent.filter(submenu => submenu.nzOpen);\n        this.listOfNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(false));\n      } else {\n        this.listOfOpenedNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(true));\n        this.listOfOpenedNzSubMenuComponent = [];\n      }\n    }\n  }\n  constructor(nzMenuService, cdr) {\n    this.nzMenuService = nzMenuService;\n    this.cdr = cdr;\n  }\n  ngOnInit() {\n    combineLatest([this.inlineCollapsed$, this.mode$]).pipe(takeUntil(this.destroy$)).subscribe(([inlineCollapsed, mode]) => {\n      this.actualMode = inlineCollapsed ? 'vertical' : mode;\n      this.nzMenuService.setMode(this.actualMode);\n      this.cdr.markForCheck();\n    });\n    this.nzMenuService.descendantMenuItemClick$.pipe(takeUntil(this.destroy$)).subscribe(menu => {\n      this.nzClick.emit(menu);\n      if (this.nzSelectable && !menu.nzMatchRouter) {\n        this.listOfNzMenuItemDirective.forEach(item => item.setSelectedState(item === menu));\n      }\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.nzMenuService.setMode(this.actualMode);\n      this.cdr.markForCheck();\n    });\n  }\n  ngAfterContentInit() {\n    this.inlineCollapsed$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateInlineCollapse();\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzInlineCollapsed,\n      nzInlineIndent,\n      nzTheme,\n      nzMode\n    } = changes;\n    if (nzInlineCollapsed) {\n      this.inlineCollapsed$.next(this.nzInlineCollapsed);\n    }\n    if (nzInlineIndent) {\n      this.nzMenuService.setInlineIndent(this.nzInlineIndent);\n    }\n    if (nzTheme) {\n      this.nzMenuService.setTheme(this.nzTheme);\n    }\n    if (nzMode) {\n      this.mode$.next(this.nzMode);\n      if (!changes.nzMode.isFirstChange() && this.listOfNzSubMenuComponent) {\n        this.listOfNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(false));\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzMenuDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzMenuDirective)(i0.ɵɵdirectiveInject(MenuService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzMenuDirective,\n    selectors: [[\"\", \"nz-menu\", \"\"]],\n    contentQueries: function NzMenuDirective_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, NzMenuItemComponent, 5);\n        i0.ɵɵcontentQuery(dirIndex, NzSubMenuComponent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzMenuItemDirective = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzSubMenuComponent = _t);\n      }\n    },\n    hostVars: 34,\n    hostBindings: function NzMenuDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-dropdown-menu\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-root\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-light\", ctx.isMenuInsideDropDown && ctx.nzTheme === \"light\")(\"ant-dropdown-menu-dark\", ctx.isMenuInsideDropDown && ctx.nzTheme === \"dark\")(\"ant-dropdown-menu-vertical\", ctx.isMenuInsideDropDown && ctx.actualMode === \"vertical\")(\"ant-dropdown-menu-horizontal\", ctx.isMenuInsideDropDown && ctx.actualMode === \"horizontal\")(\"ant-dropdown-menu-inline\", ctx.isMenuInsideDropDown && ctx.actualMode === \"inline\")(\"ant-dropdown-menu-inline-collapsed\", ctx.isMenuInsideDropDown && ctx.nzInlineCollapsed)(\"ant-menu\", !ctx.isMenuInsideDropDown)(\"ant-menu-root\", !ctx.isMenuInsideDropDown)(\"ant-menu-light\", !ctx.isMenuInsideDropDown && ctx.nzTheme === \"light\")(\"ant-menu-dark\", !ctx.isMenuInsideDropDown && ctx.nzTheme === \"dark\")(\"ant-menu-vertical\", !ctx.isMenuInsideDropDown && ctx.actualMode === \"vertical\")(\"ant-menu-horizontal\", !ctx.isMenuInsideDropDown && ctx.actualMode === \"horizontal\")(\"ant-menu-inline\", !ctx.isMenuInsideDropDown && ctx.actualMode === \"inline\")(\"ant-menu-inline-collapsed\", !ctx.isMenuInsideDropDown && ctx.nzInlineCollapsed)(\"ant-menu-rtl\", ctx.dir === \"rtl\");\n      }\n    },\n    inputs: {\n      nzInlineIndent: \"nzInlineIndent\",\n      nzTheme: \"nzTheme\",\n      nzMode: \"nzMode\",\n      nzInlineCollapsed: [2, \"nzInlineCollapsed\", \"nzInlineCollapsed\", booleanAttribute],\n      nzSelectable: [2, \"nzSelectable\", \"nzSelectable\", booleanAttribute]\n    },\n    outputs: {\n      nzClick: \"nzClick\"\n    },\n    exportAs: [\"nzMenu\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NzMenuServiceLocalToken,\n      useClass: MenuService\n    }, /** use the top level service **/\n    {\n      provide: MenuService,\n      useFactory: MenuServiceFactory\n    }, /** check if menu inside dropdown-menu component **/\n    {\n      provide: NzIsMenuInsideDropDownToken,\n      useFactory: MenuDropDownTokenFactory\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-menu]',\n      exportAs: 'nzMenu',\n      providers: [{\n        provide: NzMenuServiceLocalToken,\n        useClass: MenuService\n      }, /** use the top level service **/\n      {\n        provide: MenuService,\n        useFactory: MenuServiceFactory\n      }, /** check if menu inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useFactory: MenuDropDownTokenFactory\n      }],\n      host: {\n        '[class.ant-dropdown-menu]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-root]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-light]': `isMenuInsideDropDown && nzTheme === 'light'`,\n        '[class.ant-dropdown-menu-dark]': `isMenuInsideDropDown && nzTheme === 'dark'`,\n        '[class.ant-dropdown-menu-vertical]': `isMenuInsideDropDown && actualMode === 'vertical'`,\n        '[class.ant-dropdown-menu-horizontal]': `isMenuInsideDropDown && actualMode === 'horizontal'`,\n        '[class.ant-dropdown-menu-inline]': `isMenuInsideDropDown && actualMode === 'inline'`,\n        '[class.ant-dropdown-menu-inline-collapsed]': `isMenuInsideDropDown && nzInlineCollapsed`,\n        '[class.ant-menu]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-root]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-light]': `!isMenuInsideDropDown && nzTheme === 'light'`,\n        '[class.ant-menu-dark]': `!isMenuInsideDropDown && nzTheme === 'dark'`,\n        '[class.ant-menu-vertical]': `!isMenuInsideDropDown && actualMode === 'vertical'`,\n        '[class.ant-menu-horizontal]': `!isMenuInsideDropDown && actualMode === 'horizontal'`,\n        '[class.ant-menu-inline]': `!isMenuInsideDropDown && actualMode === 'inline'`,\n        '[class.ant-menu-inline-collapsed]': `!isMenuInsideDropDown && nzInlineCollapsed`,\n        '[class.ant-menu-rtl]': `dir === 'rtl'`\n      }\n    }]\n  }], () => [{\n    type: MenuService\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    listOfNzMenuItemDirective: [{\n      type: ContentChildren,\n      args: [NzMenuItemComponent, {\n        descendants: true\n      }]\n    }],\n    listOfNzSubMenuComponent: [{\n      type: ContentChildren,\n      args: [NzSubMenuComponent, {\n        descendants: true\n      }]\n    }],\n    nzInlineIndent: [{\n      type: Input\n    }],\n    nzTheme: [{\n      type: Input\n    }],\n    nzMode: [{\n      type: Input\n    }],\n    nzInlineCollapsed: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzSelectable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzClick: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction MenuGroupFactory() {\n  const isMenuInsideDropDownToken = inject(NzIsMenuInsideDropDownToken, {\n    optional: true,\n    skipSelf: true\n  });\n  return isMenuInsideDropDownToken ?? false;\n}\nclass NzMenuGroupComponent {\n  elementRef;\n  renderer;\n  nzTitle;\n  titleElement;\n  isMenuInsideDropDown = inject(NzIsMenuInsideDropDownToken);\n  constructor(elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    const className = this.isMenuInsideDropDown ? 'ant-dropdown-menu-item-group' : 'ant-menu-item-group';\n    this.renderer.addClass(elementRef.nativeElement, className);\n  }\n  ngAfterViewInit() {\n    const ulElement = this.titleElement.nativeElement.nextElementSibling;\n    if (ulElement) {\n      /** add classname to ul **/\n      const className = this.isMenuInsideDropDown ? 'ant-dropdown-menu-item-group-list' : 'ant-menu-item-group-list';\n      this.renderer.addClass(ulElement, className);\n    }\n  }\n  static ɵfac = function NzMenuGroupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzMenuGroupComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzMenuGroupComponent,\n    selectors: [[\"\", \"nz-menu-group\", \"\"]],\n    viewQuery: function NzMenuGroupComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c8, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.titleElement = _t.first);\n      }\n    },\n    inputs: {\n      nzTitle: \"nzTitle\"\n    },\n    exportAs: [\"nzMenuGroup\"],\n    features: [i0.ɵɵProvidersFeature([/** check if menu inside dropdown-menu component **/\n    {\n      provide: NzIsMenuInsideDropDownToken,\n      useFactory: MenuGroupFactory\n    }])],\n    attrs: _c9,\n    ngContentSelectors: _c11,\n    decls: 5,\n    vars: 6,\n    consts: [[\"titleElement\", \"\"], [4, \"nzStringTemplateOutlet\"]],\n    template: function NzMenuGroupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c10);\n        i0.ɵɵelementStart(0, \"div\", null, 0);\n        i0.ɵɵtemplate(2, NzMenuGroupComponent_ng_container_2_Template, 2, 1, \"ng-container\", 1)(3, NzMenuGroupComponent_Conditional_3_Template, 1, 0);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(4);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-menu-item-group-title\", !ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-item-group-title\", ctx.isMenuInsideDropDown);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzTitle);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(!ctx.nzTitle ? 3 : -1);\n      }\n    },\n    dependencies: [NzOutletModule, i3.NzStringTemplateOutletDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-menu-group]',\n      exportAs: 'nzMenuGroup',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [/** check if menu inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useFactory: MenuGroupFactory\n      }],\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div\n      [class.ant-menu-item-group-title]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-item-group-title]=\"isMenuInsideDropDown\"\n      #titleElement\n    >\n      <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n      @if (!nzTitle) {\n        <ng-content select=\"[title]\" />\n      }\n    </div>\n    <ng-content></ng-content>\n  `,\n      preserveWhitespaces: false,\n      imports: [NzOutletModule]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }], {\n    nzTitle: [{\n      type: Input\n    }],\n    titleElement: [{\n      type: ViewChild,\n      args: ['titleElement']\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMenuDividerDirective {\n  elementRef;\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n  }\n  static ɵfac = function NzMenuDividerDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzMenuDividerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzMenuDividerDirective,\n    selectors: [[\"\", \"nz-menu-divider\", \"\"]],\n    hostAttrs: [1, \"ant-dropdown-menu-item-divider\"],\n    exportAs: [\"nzMenuDivider\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuDividerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-menu-divider]',\n      exportAs: 'nzMenuDivider',\n      host: {\n        class: 'ant-dropdown-menu-item-divider'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMenuModule {\n  static ɵfac = function NzMenuModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzMenuModule,\n    imports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent, NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzSubmenuNoneInlineChildComponent],\n    exports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzSubMenuComponent, NzMenuGroupComponent, NzSubMenuTitleComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent, NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzSubmenuNoneInlineChildComponent],\n      exports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MenuDropDownTokenFactory, MenuGroupFactory, MenuService, MenuServiceFactory, NzIsMenuInsideDropDownToken, NzMenuDirective, NzMenuDividerDirective, NzMenuGroupComponent, NzMenuItemComponent, NzMenuModule, NzMenuServiceLocalToken, NzSubMenuComponent, NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzSubmenuNoneInlineChildComponent, NzSubmenuService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAM,MAAM,CAAC,gBAAgB,EAAE;AAC/B,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,2BAA2B,EAAE;AAC1C,SAAS,qDAAqD,IAAI,KAAK;AAAC;AACxE,IAAM,MAAM,CAAC,gCAAgC,EAAE;AAC/C,SAAS,yDAAyD,IAAI,KAAK;AAAC;AAC5E,IAAM,MAAM,CAAC,oBAAoB,EAAE;AACnC,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,MAAM;AAAA,EACvC;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,WAAW,CAAC,EAAE,GAAG,uDAAuD,GAAG,GAAG,WAAW,CAAC;AACxK,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,eAAe,UAAU,OAAO,SAAS,QAAQ,IAAI,CAAC;AAAA,EAC3D;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,IAAM,MAAM,CAAC,cAAc,EAAE;AAC7B,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC,GAAG,GAAG;AACrC,IAAM,MAAM,CAAC,WAAW,GAAG;AAC3B,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,QAAQ,OAAO,IAAI,EAAE,UAAU,OAAO,MAAM,EAAE,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa,EAAE,aAAa,OAAO,eAAe,EAAE,kBAAkB,kBAAkB;AAAA,EACtT;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,qBAAqB,SAAS,yFAAyF,QAAQ;AAC3I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,SAAS,OAAO,KAAK,EAAE,QAAQ,OAAO,IAAI,EAAE,UAAU,OAAO,MAAM,EAAE,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,wBAAwB,OAAO,oBAAoB,EAAE,0BAA0B,OAAO,sBAAsB,EAAE,kBAAkB,kBAAkB,EAAE,aAAa,OAAO,eAAe,EAAE,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa;AAAA,EACzf;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,WAAW,GAAG,yDAAyD,GAAG,IAAI,eAAe,CAAC;AACjG,IAAG,WAAW,kBAAkB,SAAS,gFAAgF,QAAQ;AAC/H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,uBAAuB,SAAS,uFAAuF;AACxH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,KAAK,CAAC;AAAA,IACxD,CAAC;AAAA,EACH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,YAAe,YAAY,CAAC;AAClC,IAAG,WAAW,gCAAgC,OAAO,gBAAgB,EAAE,6BAA6B,SAAS,EAAE,4BAA4B,OAAO,YAAY,EAAE,2BAA2B,OAAO,MAAM,EAAE,wCAAwC,mBAAmB;AAAA,EACvQ;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,iBAAiB,EAAE;AAChC,IAAM,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC,CAAC;AACtC,IAAM,OAAO,CAAC,KAAK,SAAS;AAC5B,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,IAAM,8BAA8B,IAAI,eAAe,yBAAyB;AAChF,IAAM,0BAA0B,IAAI,eAAe,yBAAyB;AAM5E,IAAM,eAAN,MAAM,aAAY;AAAA;AAAA,EAEhB,2BAA2B,IAAI,QAAQ;AAAA;AAAA,EAEvC,sBAAsB,IAAI,QAAQ;AAAA,EAClC,SAAS,IAAI,gBAAgB,OAAO;AAAA,EACpC,QAAQ,IAAI,gBAAgB,UAAU;AAAA,EACtC,gBAAgB,IAAI,gBAAgB,EAAE;AAAA,EACtC,sBAAsB,IAAI,gBAAgB,KAAK;AAAA,EAC/C,0BAA0B,MAAM;AAC9B,SAAK,yBAAyB,KAAK,IAAI;AAAA,EACzC;AAAA,EACA,qBAAqB,MAAM;AACzB,SAAK,oBAAoB,KAAK,IAAI;AAAA,EACpC;AAAA,EACA,QAAQ,MAAM;AACZ,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA,EACA,gBAAgB,QAAQ;AACtB,SAAK,cAAc,KAAK,MAAM;AAAA,EAChC;AAQF;AAPE,cAxBI,cAwBG,QAAO,SAAS,oBAAoB,mBAAmB;AAC5D,SAAO,KAAK,qBAAqB,cAAa;AAChD;AACA,cA3BI,cA2BG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,aAAY;AACvB,CAAC;AA9BH,IAAM,cAAN;AAAA,CAgCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,oBAAN,MAAM,kBAAiB;AAAA,EACrB,gBAAgB,OAAO,WAAW;AAAA,EAClC,QAAQ,KAAK,cAAc,MAAM,KAAK,IAAI,UAAQ;AAChD,QAAI,SAAS,UAAU;AACrB,aAAO;AAAA,IAET,WAAW,SAAS,cAAc,KAAK,sBAAsB;AAC3D,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,CAAC,CAAC;AAAA,EACF,QAAQ;AAAA,EACR,uBAAuB,OAAO,2BAA2B;AAAA,EACzD,wBAAwB,IAAI,gBAAgB,KAAK;AAAA,EACjD,sBAAsB,IAAI,gBAAgB,KAAK;AAAA;AAAA,EAE/C,8BAA8B,IAAI,QAAQ;AAAA,EAC1C,sBAAsB,IAAI,QAAQ;AAAA,EAClC,WAAW,IAAI,QAAQ;AAAA,EACvB,uBAAuB,OAAO,mBAAkB;AAAA,IAC9C,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,qBAAqB,MAAM;AACzB,SAAK,oBAAoB,KAAK,IAAI;AAAA,EACpC;AAAA,EACA,4BAA4B,OAAO;AACjC,SAAK,sBAAsB,KAAK,KAAK;AAAA,EACvC;AAAA,EACA,iCAAiC,OAAO;AACtC,SAAK,4BAA4B,KAAK,KAAK;AAAA,EAC7C;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,sBAAsB;AAC7B,WAAK,QAAQ,KAAK,qBAAqB,QAAQ;AAAA,IACjD;AAEA,UAAM,0BAA0B,KAAK,oBAAoB,KAAK,SAAS,MAAM,KAAK,KAAK,GAAG,OAAO,UAAQ,SAAS,YAAY,KAAK,oBAAoB,GAAG,IAAI,MAAM,KAAK,CAAC;AAC1K,UAAM,wBAAwB,MAAM,KAAK,6BAA6B,uBAAuB;AAE7F,UAAM,6BAA6B,cAAc,CAAC,KAAK,qBAAqB,qBAAqB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,oBAAoB,oBAAoB,MAAM,sBAAsB,oBAAoB,GAAG,UAAU,GAAG,GAAG,qBAAqB,GAAG,UAAU,KAAK,QAAQ,CAAC;AAC1Q,+BAA2B,KAAK,qBAAqB,CAAC,EAAE,UAAU,UAAQ;AACxE,WAAK,4BAA4B,IAAI;AACrC,UAAI,KAAK,sBAAsB;AAE7B,aAAK,qBAAqB,oBAAoB,KAAK,IAAI;AAAA,MACzD,OAAO;AACL,aAAK,cAAc,oBAAoB,KAAK,IAAI;AAAA,MAClD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAQF;AAPE,cA7DI,mBA6DG,QAAO,SAAS,yBAAyB,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,mBAAkB;AACrD;AACA,cAhEI,mBAgEG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,kBAAiB;AAC5B,CAAC;AAnEH,IAAM,mBAAN;AAAA,CAqEC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,uBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,WAAW,IAAI,QAAQ;AAAA,EACvB,mBAAmB,OAAO,kBAAkB;AAAA,IAC1C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,iBAAiB,OAAO,cAAc;AAAA,EACtC,aAAa,OAAO,YAAY;AAAA,IAC9B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS,OAAO,QAAQ;AAAA,IACtB,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,uBAAuB,OAAO,2BAA2B;AAAA,EACzD,QAAQ,KAAK,mBAAmB,KAAK,iBAAiB,QAAQ,IAAI;AAAA,EAClE,YAAY,IAAI,QAAQ;AAAA,EACxB,oBAAoB;AAAA,EACpB,MAAM;AAAA,EACN;AAAA,EACA,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB;AAAA;AAAA,EAEA,cAAc,GAAG;AACf,QAAI,KAAK,YAAY;AACnB,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAAA,IACpB,OAAO;AACL,WAAK,cAAc,0BAA0B,IAAI;AACjD,UAAI,KAAK,kBAAkB;AAEzB,aAAK,iBAAiB,qBAAqB,IAAI;AAAA,MACjD,OAAO;AAEL,aAAK,cAAc,qBAAqB,IAAI;AAAA,MAC9C;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa;AAClB,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,oBAAoB,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO,aAAa,CAAC,KAAK,eAAe;AAC3F;AAAA,IACF;AACA,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,YAAM,iBAAiB,KAAK,eAAe;AAC3C,UAAI,KAAK,eAAe,gBAAgB;AACtC,aAAK,aAAa;AAClB,aAAK,iBAAiB,KAAK,UAAU;AACrC,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,UAAM,kBAAkB,KAAK,aAAa,KAAK,MAAM;AACrD,WAAO,KAAK,cAAc,gBAAgB,KAAK,UAAU,KAAK,KAAK,iBAAiB,KAAK,eAAe;AAAA,EAC1G;AAAA,EACA,aAAa,QAAQ;AACnB,WAAO,UAAQ,OAAO,SAAS,KAAK,WAAW,IAAI;AAAA,MACjD,OAAO,KAAK,qBAAqB,UAAU;AAAA,MAC3C,aAAa,KAAK,qBAAqB,UAAU;AAAA,MACjD,UAAU;AAAA,MACV,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,eAAe,KAAK;AAC9B,SAAK,gBAAgB;AACrB,SAAK,MAAM;AACX,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,OAAO,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAK,aAAa,aAAa,CAAC,EAAE,UAAU,MAAM;AACzG,aAAK,mBAAmB;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW;AAET,kBAAc,CAAC,KAAK,cAAc,OAAO,KAAK,cAAc,aAAa,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,MAAM,YAAY,MAAM;AAC7I,WAAK,oBAAoB,SAAS,WAAW,KAAK,QAAQ,eAAe;AAAA,IAC3E,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,iBAAiB,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,CAAC;AACtG,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,YAAY;AACtB,WAAK,iBAAiB,KAAK,UAAU;AAAA,IACvC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAsDF;AArDE,cAvGI,sBAuGG,QAAO,SAAS,4BAA4B,mBAAmB;AACpE,SAAO,KAAK,qBAAqB,sBAAwB,kBAAkB,WAAW,GAAM,kBAAqB,iBAAiB,CAAC;AACrI;AACA,cA1GI,sBA0GG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,EACpC,gBAAgB,SAAS,mCAAmC,IAAI,KAAK,UAAU;AAC7E,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,YAAY,CAAC;AAAA,IAC3C;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB;AAAA,IACtE;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,6CAA6C,QAAQ;AACnF,eAAO,IAAI,cAAc,MAAM;AAAA,MACjC,CAAC;AAAA,IACH;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,gBAAgB,IAAI,QAAQ,QAAQ,OAAO,IAAI,iBAAiB,IAAI,mBAAmB,IAAI,EAAE,iBAAiB,IAAI,QAAQ,QAAQ,IAAI,iBAAiB,IAAI,oBAAoB,MAAM,IAAI;AACxM,MAAG,YAAY,0BAA0B,IAAI,oBAAoB,EAAE,mCAAmC,IAAI,wBAAwB,IAAI,UAAU,EAAE,iCAAiC,IAAI,wBAAwB,IAAI,QAAQ,EAAE,mCAAmC,IAAI,wBAAwB,IAAI,UAAU,EAAE,iBAAiB,CAAC,IAAI,oBAAoB,EAAE,0BAA0B,CAAC,IAAI,wBAAwB,IAAI,UAAU,EAAE,wBAAwB,CAAC,IAAI,wBAAwB,IAAI,QAAQ,EAAE,0BAA0B,CAAC,IAAI,wBAAwB,IAAI,UAAU;AAAA,IACziB;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,+BAA+B;AAAA,IACpF,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,IAC5D,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,IAC5D,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACtD,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,IACpF,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,EACvE;AAAA,EACA,UAAU,CAAC,YAAY;AAAA,EACvB,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,CAAC;AAAA,EACtC,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,MAAG,aAAa,CAAC;AACjB,MAAG,aAAa;AAAA,IAClB;AAAA,EACF;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AA3JH,IAAM,sBAAN;AAAA,CA6JC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,kCAAkC;AAAA,QAClC,2CAA2C;AAAA,QAC3C,yCAAyC;AAAA,QACzC,2CAA2C;AAAA,QAC3C,yBAAyB;AAAA,QACzB,kCAAkC;AAAA,QAClC,gCAAgC;AAAA,QAChC,kCAAkC;AAAA,QAClC,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iCAAN,MAAM,+BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AAAA,EACT,uBAAuB,CAAC;AAAA,EACxB,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,YAAY,UAAU,gBAAgB;AAChD,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,QAAQ;AACf,WAAK,cAAc;AAAA,IACrB,OAAO;AACL,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB;AACrB,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,QAAQ;AAClB,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,WAAW;AACb,UAAI,KAAK,qBAAqB,QAAQ;AACpC,aAAK,qBAAqB,OAAO,UAAQ,CAAC,CAAC,IAAI,EAAE,QAAQ,eAAa;AACpE,eAAK,SAAS,YAAY,KAAK,WAAW,eAAe,SAAS;AAAA,QACpE,CAAC;AAAA,MACH;AACA,UAAI,KAAK,WAAW;AAClB,aAAK,uBAAuB,KAAK,UAAU,MAAM,GAAG;AACpD,aAAK,qBAAqB,OAAO,UAAQ,CAAC,CAAC,IAAI,EAAE,QAAQ,eAAa;AACpE,eAAK,SAAS,SAAS,KAAK,WAAW,eAAe,SAAS;AAAA,QACjE,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AA0CF;AAzCE,cA1DI,gCA0DG,QAAO,SAAS,sCAAsC,mBAAmB;AAC9E,SAAO,KAAK,qBAAqB,gCAAkC,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,cAAc,CAAC;AAClL;AACA,cA7DI,gCA6DG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,EAC/C,WAAW,CAAC,GAAG,YAAY,mBAAmB,cAAc;AAAA,EAC5D,UAAU;AAAA,EACV,cAAc,SAAS,2CAA2C,IAAI,KAAK;AACzE,QAAI,KAAK,GAAG;AACV,MAAG,wBAAwB,mBAAmB,IAAI,WAAW;AAC7D,MAAG,YAAY,gBAAgB,IAAI,QAAQ,KAAK;AAAA,IAClD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,EACV;AAAA,EACA,UAAU,CAAC,sBAAsB;AAAA,EACjC,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,kBAAkB,CAAC;AAAA,EAChC,UAAU,SAAS,uCAAuC,IAAI,KAAK;AACjE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,eAAe,CAAC;AAAA,IAC/F;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,oBAAoB,IAAI,cAAc;AAAA,IACtD;AAAA,EACF;AAAA,EACA,cAAc,CAAC,gBAAgB;AAAA,EAC/B,eAAe;AAAA,EACf,MAAM;AAAA,IACJ,WAAW,CAAC,cAAc;AAAA,EAC5B;AAAA,EACA,iBAAiB;AACnB,CAAC;AAlGH,IAAM,gCAAN;AAAA,CAoGC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY,CAAC,cAAc;AAAA,MAC3B,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,MACvB;AAAA,MACA,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qCAAN,MAAM,mCAAkC;AAAA,EACtC;AAAA,EACA,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,OAAO;AAAA,EACP,yBAAyB;AAAA,EACzB,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AAAA,EACT,oBAAoB,IAAI,aAAa;AAAA,EACrC,YAAY,gBAAgB;AAC1B,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,cAAc,KAAK,2BAA2B,SAAS;AAC/D,WAAK,kBAAkB,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW,IAAI,QAAQ;AAAA,EACvB,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,QAAQ;AACf,UAAI,KAAK,SAAS,cAAc;AAC9B,aAAK,cAAc;AAAA,MACrB,WAAW,KAAK,SAAS,YAAY;AACnC,aAAK,cAAc;AAAA,MACrB;AAAA,IACF,OAAO;AACL,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB;AACrB,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,QAAQ;AAClB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AA8DF;AA7DE,cAtDI,oCAsDG,QAAO,SAAS,0CAA0C,mBAAmB;AAClF,SAAO,KAAK,qBAAqB,oCAAsC,kBAAqB,cAAc,CAAC;AAC7G;AACA,cAzDI,oCAyDG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,gCAAgC,EAAE,CAAC;AAAA,EACpD,WAAW,CAAC,GAAG,oBAAoB,wBAAwB;AAAA,EAC3D,UAAU;AAAA,EACV,cAAc,SAAS,+CAA+C,IAAI,KAAK;AAC7E,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,cAAc,SAAS,kEAAkE;AACrG,eAAO,IAAI,cAAc,IAAI;AAAA,MAC/B,CAAC,EAAE,cAAc,SAAS,kEAAkE;AAC1F,eAAO,IAAI,cAAc,KAAK;AAAA,MAChC,CAAC;AAAA,IACH;AACA,QAAI,KAAK,GAAG;AACV,MAAG,wBAAwB,gBAAgB,IAAI,WAAW,EAAE,kBAAkB,IAAI,WAAW;AAC7F,MAAG,YAAY,kBAAkB,IAAI,UAAU,OAAO,EAAE,iBAAiB,IAAI,UAAU,MAAM,EAAE,qCAAqC,IAAI,SAAS,YAAY,EAAE,oCAAoC,IAAI,SAAS,cAAc,IAAI,aAAa,OAAO,EAAE,mCAAmC,IAAI,SAAS,cAAc,IAAI,aAAa,MAAM,EAAE,wBAAwB,IAAI,QAAQ,KAAK;AAAA,IAC1X;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,gBAAgB;AAAA,IAChB,sBAAsB;AAAA,IACtB,MAAM;AAAA,IACN,wBAAwB;AAAA,IACxB,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,SAAS;AAAA,IACP,mBAAmB;AAAA,EACrB;AAAA,EACA,UAAU,CAAC,0BAA0B;AAAA,EACrC,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,kBAAkB,CAAC;AAAA,EAChC,UAAU,SAAS,2CAA2C,IAAI,KAAK;AACrE,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,KAAK;AAC1B,MAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,eAAe,CAAC;AACjG,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,IAAI,SAAS;AAC3B,MAAG,YAAY,qBAAqB,IAAI,oBAAoB,EAAE,YAAY,CAAC,IAAI,oBAAoB,EAAE,8BAA8B,IAAI,oBAAoB,EAAE,qBAAqB,CAAC,IAAI,oBAAoB,EAAE,yBAAyB,IAAI,oBAAoB,EAAE,gBAAgB,CAAC,IAAI,oBAAoB,EAAE,gBAAgB,IAAI,QAAQ,KAAK;AAC5U,MAAG,UAAU;AACb,MAAG,WAAW,oBAAoB,IAAI,cAAc;AAAA,IACtD;AAAA,EACF;AAAA,EACA,cAAc,CAAC,gBAAgB;AAAA,EAC/B,eAAe;AAAA,EACf,MAAM;AAAA,IACJ,WAAW,CAAC,eAAe,WAAW;AAAA,EACxC;AAAA,EACA,iBAAiB;AACnB,CAAC;AAlHH,IAAM,oCAAN;AAAA,CAoHC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mCAAmC,CAAC;AAAA,IAC1G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,YAAY,CAAC,eAAe,WAAW;AAAA,MACvC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,QACzB,6CAA6C;AAAA,QAC7C,4CAA4C;AAAA,QAC5C,2CAA2C;AAAA,QAC3C,gCAAgC;AAAA,QAChC,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,yBAAwB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,OAAO;AAAA,EACP,yBAAyB;AAAA,EACzB,gBAAgB,IAAI,aAAa;AAAA,EACjC,oBAAoB,IAAI,aAAa;AAAA,EACrC,MAAM;AAAA,EACN,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,KAAK,gBAAgB;AAC/B,SAAK,MAAM;AACX,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,cAAc,KAAK,2BAA2B,SAAS;AAC/D,WAAK,kBAAkB,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,KAAK,SAAS,YAAY,KAAK,2BAA2B,YAAY,CAAC,KAAK,YAAY;AAC3F,WAAK,kBAAkB,KAAK,IAAI;AAChC,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF;AA6DF;AA5DE,cAxCI,0BAwCG,QAAO,SAAS,gCAAgC,mBAAmB;AACxE,SAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,iBAAiB,GAAM,kBAAqB,cAAc,CAAC;AAC/I;AACA,cA3CI,0BA2CG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,EACxC,UAAU;AAAA,EACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,mDAAmD;AACjF,eAAO,IAAI,WAAW;AAAA,MACxB,CAAC,EAAE,cAAc,SAAS,wDAAwD;AAChF,eAAO,IAAI,cAAc,IAAI;AAAA,MAC/B,CAAC,EAAE,cAAc,SAAS,wDAAwD;AAChF,eAAO,IAAI,cAAc,KAAK;AAAA,MAChC,CAAC;AAAA,IACH;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,gBAAgB,IAAI,QAAQ,QAAQ,OAAO,IAAI,aAAa,IAAI,EAAE,iBAAiB,IAAI,QAAQ,QAAQ,IAAI,cAAc,MAAM,IAAI;AAClJ,MAAG,YAAY,mCAAmC,IAAI,oBAAoB,EAAE,0BAA0B,CAAC,IAAI,oBAAoB;AAAA,IACjI;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,sBAAsB;AAAA,IACtB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,MAAM;AAAA,IACN,wBAAwB;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB;AAAA,EACA,UAAU,CAAC,gBAAgB;AAAA,EAC3B,OAAO;AAAA,EACP,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,uCAAuC,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,UAAU,QAAQ,GAAG,sCAAsC,GAAG,CAAC,UAAU,SAAS,GAAG,sCAAsC,CAAC;AAAA,EAChS,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,WAAW,CAAC,EAAE,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC;AAChK,MAAG,aAAa,CAAC;AACjB,MAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,gDAAgD,GAAG,GAAG,QAAQ,CAAC;AAAA,IACtJ;AACA,QAAI,KAAK,GAAG;AACV,MAAG,cAAc,IAAI,SAAS,IAAI,EAAE;AACpC,MAAG,UAAU;AACb,MAAG,WAAW,0BAA0B,IAAI,OAAO;AACnD,MAAG,UAAU,CAAC;AACd,MAAG,cAAc,IAAI,uBAAuB,IAAI,CAAC;AAAA,IACnD;AAAA,EACF;AAAA,EACA,cAAc,CAAC,cAAiB,iBAAiB,gBAAmB,+BAA+B;AAAA,EACnG,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAnGH,IAAM,0BAAN;AAAA,CAqGC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBV,MAAM;AAAA,QACJ,2CAA2C;AAAA,QAC3C,kCAAkC;AAAA,QAClC,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS,CAAC,cAAc,cAAc;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,0BAA0B,CAAC,aAAa,UAAU,aAAa,OAAO,aAAa,aAAa,aAAa,SAAS,aAAa,MAAM,aAAa,UAAU;AACtK,IAAM,4BAA4B,CAAC,aAAa,YAAY,aAAa,aAAa,aAAa,UAAU,aAAa,OAAO;AACjI,IAAM,sBAAN,MAAM,oBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,yBAAyB;AAAA,EACzB,SAAS;AAAA,EACT,aAAa;AAAA,EACb,cAAc;AAAA,EACd,eAAe,IAAI,aAAa;AAAA,EAChC,mBAAmB;AAAA;AAAA;AAAA,EAGnB,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mBAAmB,OAAO,gBAAgB;AAAA,EAC1C,QAAQ,KAAK,iBAAiB;AAAA,EAC9B,WAAW,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,MAAM;AAAA,EACN,uBAAuB,OAAO,2BAA2B;AAAA,EACzD,cAAc,OAAO,wBAAwB;AAAA,IAC3C,UAAU;AAAA,IACV,MAAM;AAAA,EACR,CAAC;AAAA,EACD,iBAAiB,OAAO,cAAc;AAAA;AAAA,EAEtC,4BAA4B,MAAM;AAChC,SAAK,iBAAiB,4BAA4B,IAAI;AAAA,EACxD;AAAA,EACA,gBAAgB;AACd,SAAK,4BAA4B,CAAC,KAAK,MAAM;AAAA,EAC/C;AAAA,EACA,mBAAmB,OAAO;AACxB,SAAK,WAAW;AAChB,QAAI,KAAK,SAAS,UAAU;AAC1B,WAAK,iBAAiB,iCAAiC,KAAK;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,SAAS,gBAAgB,KAAK,SAAS,aAAa,KAAK,oBAAoB,KAAK,gBAAgB,cAAc;AAEvH,WAAK,eAAe,KAAK,iBAAiB,cAAc,sBAAsB,EAAE;AAAA,IAClF;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU;AACzB,UAAM,YAAY,iBAAiB,QAAQ;AAC3C,QAAI,cAAc,cAAc,cAAc,iBAAiB,cAAc,SAAS;AACpF,WAAK,WAAW;AAAA,IAClB,WAAW,cAAc,aAAa,cAAc,gBAAgB,cAAc,QAAQ;AACxF,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,YAAY,eAAe,KAAK,UAAU;AACxC,SAAK,gBAAgB;AACrB,SAAK,MAAM;AACX,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AAET,SAAK,cAAc,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC1E,WAAK,QAAQ;AACb,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAED,SAAK,iBAAiB,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC3E,WAAK,OAAO;AACZ,UAAI,SAAS,cAAc;AACzB,aAAK,mBAAmB,CAAC,aAAa,KAAK,WAAW,GAAG,GAAG,yBAAyB;AAAA,MACvF,WAAW,SAAS,YAAY;AAC9B,aAAK,mBAAmB;AAAA,MAC1B;AACA,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAED,kBAAc,CAAC,KAAK,iBAAiB,OAAO,KAAK,cAAc,aAAa,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,MAAM,YAAY,MAAM;AAChJ,WAAK,oBAAoB,SAAS,WAAW,KAAK,QAAQ,eAAe;AACzE,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAED,SAAK,iBAAiB,sBAAsB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC3F,WAAK,WAAW;AAChB,UAAI,SAAS,KAAK,QAAQ;AACxB,aAAK,gBAAgB;AACrB,aAAK,SAAS;AACd,aAAK,aAAa,KAAK,KAAK,MAAM;AAClC,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,gBAAgB;AACrB,UAAM,4BAA4B,KAAK;AACvC,UAAM,UAAU,0BAA0B;AAC1C,UAAM,mBAAmB,MAAM,GAAG,CAAC,SAAS,GAAG,0BAA0B,IAAI,UAAQ,KAAK,SAAS,CAAC,CAAC;AACrG,YAAQ,KAAK,UAAU,yBAAyB,GAAG,UAAU,MAAM,gBAAgB,GAAG,UAAU,IAAI,GAAG,IAAI,MAAM,0BAA0B,KAAK,OAAK,EAAE,UAAU,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AACnN,WAAK,aAAa;AAClB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ;AACV,WAAK,iBAAiB,4BAA4B,KAAK,MAAM;AAC7D,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAiFF;AAhFE,cAhII,qBAgIG,QAAO,SAAS,2BAA2B,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,qBAAuB,kBAAkB,WAAW,GAAM,kBAAqB,iBAAiB,GAAM,kBAAuB,QAAQ,CAAC;AACzK;AACA,cAnII,qBAmIG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,EAClC,gBAAgB,SAAS,kCAAkC,IAAI,KAAK,UAAU;AAC5E,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,qBAAoB,CAAC;AACjD,MAAG,eAAe,UAAU,qBAAqB,CAAC;AAAA,IACpD;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAC5E,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,4BAA4B;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,kBAAkB,GAAG,UAAU;AAAA,IAChD;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,IACzE;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,6BAA6B,IAAI,oBAAoB,EAAE,sCAAsC,IAAI,wBAAwB,IAAI,UAAU,EAAE,kCAAkC,IAAI,wBAAwB,IAAI,MAAM,EAAE,sCAAsC,IAAI,wBAAwB,IAAI,UAAU,EAAE,sCAAsC,IAAI,wBAAwB,IAAI,SAAS,UAAU,EAAE,wCAAwC,IAAI,wBAAwB,IAAI,SAAS,YAAY,EAAE,oCAAoC,IAAI,wBAAwB,IAAI,SAAS,QAAQ,EAAE,oCAAoC,IAAI,wBAAwB,IAAI,QAAQ,EAAE,oBAAoB,CAAC,IAAI,oBAAoB,EAAE,6BAA6B,CAAC,IAAI,wBAAwB,IAAI,UAAU,EAAE,yBAAyB,CAAC,IAAI,wBAAwB,IAAI,MAAM,EAAE,6BAA6B,CAAC,IAAI,wBAAwB,IAAI,UAAU,EAAE,6BAA6B,CAAC,IAAI,wBAAwB,IAAI,SAAS,UAAU,EAAE,+BAA+B,CAAC,IAAI,wBAAwB,IAAI,SAAS,YAAY,EAAE,2BAA2B,CAAC,IAAI,wBAAwB,IAAI,SAAS,QAAQ,EAAE,2BAA2B,CAAC,IAAI,wBAAwB,IAAI,QAAQ,EAAE,wBAAwB,IAAI,QAAQ,KAAK;AAAA,IAClwC;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,wBAAwB;AAAA,IACxB,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,IAChD,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,IAC5D,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,EAChB;AAAA,EACA,UAAU,CAAC,WAAW;AAAA,EACtB,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,oBAAoB;AAAA,EAC7E,OAAO;AAAA,EACP,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,UAAU,kBAAkB,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,oBAAoB,IAAI,oBAAoB,IAAI,GAAG,qBAAqB,iBAAiB,UAAU,WAAW,QAAQ,cAAc,wBAAwB,eAAe,wBAAwB,GAAG,CAAC,2BAA2B,IAAI,GAAG,QAAQ,UAAU,iBAAiB,aAAa,gBAAgB,GAAG,CAAC,uBAAuB,IAAI,GAAG,gCAAgC,6BAA6B,4BAA4B,2BAA2B,sCAAsC,GAAG,CAAC,uBAAuB,IAAI,GAAG,kBAAkB,uBAAuB,gCAAgC,6BAA6B,4BAA4B,2BAA2B,sCAAsC,GAAG,CAAC,gCAAgC,IAAI,GAAG,qBAAqB,SAAS,QAAQ,UAAU,YAAY,cAAc,wBAAwB,0BAA0B,kBAAkB,aAAa,eAAe,CAAC;AAAA,EACn+B,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,QAAI,KAAK,GAAG;AACV,YAAM,MAAS,iBAAiB;AAChC,MAAG,gBAAgB,GAAG;AACtB,MAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,MAAG,WAAW,qBAAqB,SAAS,6DAA6D,QAAQ;AAC/G,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,mBAAmB,MAAM,CAAC;AAAA,MACtD,CAAC,EAAE,iBAAiB,SAAS,2DAA2D;AACtF,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,cAAc,CAAC;AAAA,MAC3C,CAAC;AACD,MAAG,WAAW,GAAG,2CAA2C,GAAG,CAAC;AAChE,MAAG,aAAa;AAChB,MAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,IAChP;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,UAAU,IAAI,MAAM,EAAE,WAAW,IAAI,OAAO,EAAE,QAAQ,IAAI,IAAI,EAAE,cAAc,IAAI,UAAU,EAAE,wBAAwB,IAAI,oBAAoB,EAAE,eAAe,IAAI,iBAAiB,IAAI,iBAAiB,EAAE,0BAA0B,IAAI,sBAAsB;AAC7Q,MAAG,UAAU,CAAC;AACd,MAAG,cAAc,CAAC,IAAI,UAAU,IAAI,EAAE;AACtC,MAAG,UAAU;AACb,MAAG,cAAc,IAAI,SAAS,WAAW,IAAI,CAAC;AAAA,IAChD;AAAA,EACF;AAAA,EACA,cAAc,CAAC,yBAAyB,+BAA+B,wBAAwB,mCAAmC,eAAoB,qBAA0B,gBAAgB;AAAA,EAChM,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AA/MH,IAAM,qBAAN;AAAA,CAiNC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,gBAAgB;AAAA,MAC5B,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8DV,MAAM;AAAA,QACJ,qCAAqC;AAAA,QACrC,8CAA8C;AAAA,QAC9C,0CAA0C;AAAA,QAC1C,8CAA8C;AAAA,QAC9C,8CAA8C;AAAA,QAC9C,gDAAgD;AAAA,QAChD,4CAA4C;AAAA,QAC5C,4CAA4C;AAAA,QAC5C,4BAA4B;AAAA,QAC5B,qCAAqC;AAAA,QACrC,iCAAiC;AAAA,QACjC,qCAAqC;AAAA,QACrC,qCAAqC;AAAA,QACrC,uCAAuC;AAAA,QACvC,mCAAmC;AAAA,QACnC,mCAAmC;AAAA,QACnC,gCAAgC;AAAA,MAClC;AAAA,MACA,SAAS,CAAC,yBAAyB,+BAA+B,wBAAwB,mCAAmC,aAAa;AAAA,IAC5I,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,kBAAkB,GAAG;AAAA,QAC3C,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,SAAS,qBAAqB;AAC5B,QAAM,wBAAwB,OAAO,aAAa;AAAA,IAChD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,yBAAyB,OAAO,uBAAuB;AAC7D,SAAO,yBAAyB;AAClC;AACA,SAAS,2BAA2B;AAClC,QAAM,4BAA4B,OAAO,6BAA6B;AAAA,IACpE,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,6BAA6B;AACtC;AACA,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB,OAAO,2BAA2B;AAAA,EACzD;AAAA,EACA,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,oBAAoB;AAAA,EACpB,eAAe,CAAC,KAAK;AAAA,EACrB,UAAU,IAAI,aAAa;AAAA,EAC3B,aAAa;AAAA,EACb,MAAM;AAAA,EACN,mBAAmB,IAAI,gBAAgB,KAAK,iBAAiB;AAAA,EAC7D,QAAQ,IAAI,gBAAgB,KAAK,MAAM;AAAA,EACvC,WAAW,IAAI,QAAQ;AAAA,EACvB,iCAAiC,CAAC;AAAA,EAClC,iBAAiB,OAAO,cAAc;AAAA,EACtC,mBAAmB,iBAAiB;AAClC,SAAK,oBAAoB;AACzB,SAAK,iBAAiB,KAAK,eAAe;AAAA,EAC5C;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,2BAA2B;AAClC,UAAI,KAAK,mBAAmB;AAC1B,aAAK,iCAAiC,KAAK,yBAAyB,OAAO,aAAW,QAAQ,MAAM;AACpG,aAAK,yBAAyB,QAAQ,aAAW,QAAQ,4BAA4B,KAAK,CAAC;AAAA,MAC7F,OAAO;AACL,aAAK,+BAA+B,QAAQ,aAAW,QAAQ,4BAA4B,IAAI,CAAC;AAChG,aAAK,iCAAiC,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,eAAe,KAAK;AAC9B,SAAK,gBAAgB;AACrB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,WAAW;AACT,kBAAc,CAAC,KAAK,kBAAkB,KAAK,KAAK,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,iBAAiB,IAAI,MAAM;AACvH,WAAK,aAAa,kBAAkB,aAAa;AACjD,WAAK,cAAc,QAAQ,KAAK,UAAU;AAC1C,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,SAAK,cAAc,yBAAyB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC3F,WAAK,QAAQ,KAAK,IAAI;AACtB,UAAI,KAAK,gBAAgB,CAAC,KAAK,eAAe;AAC5C,aAAK,0BAA0B,QAAQ,UAAQ,KAAK,iBAAiB,SAAS,IAAI,CAAC;AAAA,MACrF;AAAA,IACF,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,cAAc,QAAQ,KAAK,UAAU;AAC1C,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,iBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACnE,WAAK,qBAAqB;AAC1B,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,mBAAmB;AACrB,WAAK,iBAAiB,KAAK,KAAK,iBAAiB;AAAA,IACnD;AACA,QAAI,gBAAgB;AAClB,WAAK,cAAc,gBAAgB,KAAK,cAAc;AAAA,IACxD;AACA,QAAI,SAAS;AACX,WAAK,cAAc,SAAS,KAAK,OAAO;AAAA,IAC1C;AACA,QAAI,QAAQ;AACV,WAAK,MAAM,KAAK,KAAK,MAAM;AAC3B,UAAI,CAAC,QAAQ,OAAO,cAAc,KAAK,KAAK,0BAA0B;AACpE,aAAK,yBAAyB,QAAQ,aAAW,QAAQ,4BAA4B,KAAK,CAAC;AAAA,MAC7F;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAgDF;AA/CE,cA1FI,kBA0FG,QAAO,SAAS,wBAAwB,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,kBAAoB,kBAAkB,WAAW,GAAM,kBAAqB,iBAAiB,CAAC;AACjI;AACA,cA7FI,kBA6FG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,EAC/B,gBAAgB,SAAS,+BAA+B,IAAI,KAAK,UAAU;AACzE,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,qBAAqB,CAAC;AAClD,MAAG,eAAe,UAAU,oBAAoB,CAAC;AAAA,IACnD;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,4BAA4B;AAC7E,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,qBAAqB,IAAI,oBAAoB,EAAE,0BAA0B,IAAI,oBAAoB,EAAE,2BAA2B,IAAI,wBAAwB,IAAI,YAAY,OAAO,EAAE,0BAA0B,IAAI,wBAAwB,IAAI,YAAY,MAAM,EAAE,8BAA8B,IAAI,wBAAwB,IAAI,eAAe,UAAU,EAAE,gCAAgC,IAAI,wBAAwB,IAAI,eAAe,YAAY,EAAE,4BAA4B,IAAI,wBAAwB,IAAI,eAAe,QAAQ,EAAE,sCAAsC,IAAI,wBAAwB,IAAI,iBAAiB,EAAE,YAAY,CAAC,IAAI,oBAAoB,EAAE,iBAAiB,CAAC,IAAI,oBAAoB,EAAE,kBAAkB,CAAC,IAAI,wBAAwB,IAAI,YAAY,OAAO,EAAE,iBAAiB,CAAC,IAAI,wBAAwB,IAAI,YAAY,MAAM,EAAE,qBAAqB,CAAC,IAAI,wBAAwB,IAAI,eAAe,UAAU,EAAE,uBAAuB,CAAC,IAAI,wBAAwB,IAAI,eAAe,YAAY,EAAE,mBAAmB,CAAC,IAAI,wBAAwB,IAAI,eAAe,QAAQ,EAAE,6BAA6B,CAAC,IAAI,wBAAwB,IAAI,iBAAiB,EAAE,gBAAgB,IAAI,QAAQ,KAAK;AAAA,IAC5rC;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,IACjF,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,EACpE;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,UAAU,CAAC,QAAQ;AAAA,EACnB,UAAU,CAAI,mBAAmB;AAAA,IAAC;AAAA,MAChC,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,EAAC,CAAC,GAAM,oBAAoB;AAC9B,CAAC;AAxIH,IAAM,kBAAN;AAAA,CA0IC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,QAAC;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,MAAC;AAAA,MACD,MAAM;AAAA,QACJ,6BAA6B;AAAA,QAC7B,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,QACnC,kCAAkC;AAAA,QAClC,sCAAsC;AAAA,QACtC,wCAAwC;AAAA,QACxC,oCAAoC;AAAA,QACpC,8CAA8C;AAAA,QAC9C,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,QACzB,6BAA6B;AAAA,QAC7B,+BAA+B;AAAA,QAC/B,2BAA2B;AAAA,QAC3B,qCAAqC;AAAA,QACrC,wBAAwB;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,SAAS,mBAAmB;AAC1B,QAAM,4BAA4B,OAAO,6BAA6B;AAAA,IACpE,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,6BAA6B;AACtC;AACA,IAAM,wBAAN,MAAM,sBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB,OAAO,2BAA2B;AAAA,EACzD,YAAY,YAAY,UAAU;AAChC,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,UAAM,YAAY,KAAK,uBAAuB,iCAAiC;AAC/E,SAAK,SAAS,SAAS,WAAW,eAAe,SAAS;AAAA,EAC5D;AAAA,EACA,kBAAkB;AAChB,UAAM,YAAY,KAAK,aAAa,cAAc;AAClD,QAAI,WAAW;AAEb,YAAM,YAAY,KAAK,uBAAuB,sCAAsC;AACpF,WAAK,SAAS,SAAS,WAAW,SAAS;AAAA,IAC7C;AAAA,EACF;AAkDF;AAjDE,cApBI,uBAoBG,QAAO,SAAS,6BAA6B,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAChI;AACA,cAvBI,uBAuBG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,EACrC,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,KAAK,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,IACrE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,UAAU,CAAC,aAAa;AAAA,EACxB,UAAU,CAAI,mBAAmB;AAAA;AAAA,IACjC;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,EAAC,CAAC,CAAC;AAAA,EACH,OAAO;AAAA,EACP,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,EAC5D,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB,IAAI;AACvB,MAAG,eAAe,GAAG,OAAO,MAAM,CAAC;AACnC,MAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,6CAA6C,GAAG,CAAC;AAC5I,MAAG,aAAa;AAChB,MAAG,aAAa,CAAC;AAAA,IACnB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,6BAA6B,CAAC,IAAI,oBAAoB,EAAE,sCAAsC,IAAI,oBAAoB;AACrI,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,0BAA0B,IAAI,OAAO;AACnD,MAAG,UAAU;AACb,MAAG,cAAc,CAAC,IAAI,UAAU,IAAI,EAAE;AAAA,IACxC;AAAA,EACF;AAAA,EACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,EACjE,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AApEH,IAAM,uBAAN;AAAA,CAsEC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,WAAW;AAAA;AAAA,QACX;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,MAAC;AAAA,MACD,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,qBAAqB;AAAA,MACrB,SAAS,CAAC,cAAc;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,0BAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA,YAAY,YAAY;AACtB,SAAK,aAAa;AAAA,EACpB;AAUF;AATE,cALI,yBAKG,QAAO,SAAS,+BAA+B,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,yBAA2B,kBAAqB,UAAU,CAAC;AAC9F;AACA,cARI,yBAQG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACvC,WAAW,CAAC,GAAG,gCAAgC;AAAA,EAC/C,UAAU,CAAC,eAAe;AAC5B,CAAC;AAbH,IAAM,yBAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,gBAAN,MAAM,cAAa;AAYnB;AAXE,cADI,eACG,QAAO,SAAS,qBAAqB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,eAAc;AACjD;AACA,cAJI,eAIG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AAAA,EACN,SAAS,CAAC,iBAAiB,qBAAqB,oBAAoB,wBAAwB,sBAAsB,yBAAyB,+BAA+B,iCAAiC;AAAA,EAC3M,SAAS,CAAC,iBAAiB,qBAAqB,oBAAoB,wBAAwB,oBAAoB;AAClH,CAAC;AACD,cATI,eASG,QAAyB,iBAAiB;AAAA,EAC/C,SAAS,CAAC,oBAAoB,sBAAsB,uBAAuB;AAC7E,CAAC;AAXH,IAAM,eAAN;AAAA,CAaC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,qBAAqB,oBAAoB,wBAAwB,sBAAsB,yBAAyB,+BAA+B,iCAAiC;AAAA,MAC3M,SAAS,CAAC,iBAAiB,qBAAqB,oBAAoB,wBAAwB,oBAAoB;AAAA,IAClH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}