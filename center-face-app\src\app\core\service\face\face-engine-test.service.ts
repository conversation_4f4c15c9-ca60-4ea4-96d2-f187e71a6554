import { Injectable } from '@angular/core';
import { FaceDetectFactoryService } from './face-detect-factory.service';
import { HumanModelManagerService } from './human-model-manager.service';
import { NetworkStatusService } from '../network-status.service';

export interface EngineTestResult {
  engine: 'mediapipe' | 'human';
  available: boolean;
  mode: string;
  error?: string;
  performance?: {
    initTime: number;
    ready: boolean;
  };
}

@Injectable({
  providedIn: 'root'
})
export class FaceEngineTestService {

  constructor(
    private faceDetectFactory: FaceDetectFactoryService,
    private modelManager: HumanModelManagerService,
    private networkStatus: NetworkStatusService
  ) {}

  /**
   * 测试所有可用的人脸识别引擎
   */
  async testAllEngines(): Promise<EngineTestResult[]> {
    const results: EngineTestResult[] = [];
    
    // 测试 MediaPipe 引擎
    results.push(await this.testMediaPipeEngine());
    
    // 测试 Human.js 引擎
    results.push(await this.testHumanEngine());
    
    return results;
  }

  /**
   * 测试 MediaPipe 引擎
   */
  private async testMediaPipeEngine(): Promise<EngineTestResult> {
    const startTime = performance.now();
    
    try {
      const service = await this.faceDetectFactory.switchEngine('mediapipe');
      const initTime = performance.now() - startTime;
      
      return {
        engine: 'mediapipe',
        available: true,
        mode: 'local',
        performance: {
          initTime,
          ready: service.isReady()
        }
      };
    } catch (error) {
      return {
        engine: 'mediapipe',
        available: false,
        mode: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 测试 Human.js 引擎
   */
  private async testHumanEngine(): Promise<EngineTestResult> {
    const startTime = performance.now();
    
    try {
      // 检查本地模型可用性
      const localCheck = await this.modelManager.checkLocalModelsAvailability();
      
      const service = await this.faceDetectFactory.switchEngine('human');
      const initTime = performance.now() - startTime;
      
      // 获取实际使用的引擎（可能因降级而不同）
      const actualEngine = this.faceDetectFactory.getCurrentEngine();
      
      if (actualEngine !== 'human') {
        // 降级到了其他引擎
        return {
          engine: 'human',
          available: false,
          mode: 'degraded',
          error: 'Degraded to MediaPipe engine'
        };
      }
      
      const mode = localCheck.available ? 'offline' : 
                   this.networkStatus.isOnline ? 'online' : 'failed';
      
      return {
        engine: 'human',
        available: true,
        mode,
        performance: {
          initTime,
          ready: service.isReady()
        }
      };
    } catch (error) {
      return {
        engine: 'human',
        available: false,
        mode: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 生成测试报告
   */
  async generateTestReport(): Promise<string> {
    const results = await this.testAllEngines();
    const networkStatus = this.networkStatus.isOnline;
    const localModels = await this.modelManager.checkLocalModelsAvailability();
    
    let report = '# 人脸识别引擎测试报告\n\n';
    report += `**测试时间**: ${new Date().toLocaleString()}\n`;
    report += `**网络状态**: ${networkStatus ? '在线' : '离线'}\n`;
    report += `**本地模型**: ${localModels.available ? '可用' : '不可用'}\n\n`;
    
    if (!localModels.available && localModels.missingModels.length > 0) {
      report += `**缺失模型**: ${localModels.missingModels.join(', ')}\n\n`;
    }
    
    report += '## 引擎测试结果\n\n';
    
    for (const result of results) {
      report += `### ${result.engine === 'mediapipe' ? 'MediaPipe' : 'Human.js'} 引擎\n`;
      report += `- **状态**: ${result.available ? '✅ 可用' : '❌ 不可用'}\n`;
      report += `- **模式**: ${result.mode}\n`;
      
      if (result.error) {
        report += `- **错误**: ${result.error}\n`;
      }
      
      if (result.performance) {
        report += `- **初始化时间**: ${result.performance.initTime.toFixed(2)}ms\n`;
        report += `- **就绪状态**: ${result.performance.ready ? '是' : '否'}\n`;
      }
      
      report += '\n';
    }
    
    // 添加建议
    report += '## 建议\n\n';
    
    if (!localModels.available) {
      report += '- 运行 `npm run download-models` 下载 Human.js 模型以支持离线模式\n';
    }
    
    if (!networkStatus && !localModels.available) {
      report += '- 当前处于离线状态且无本地模型，建议使用 MediaPipe 引擎\n';
    }
    
    const humanResult = results.find(r => r.engine === 'human');
    if (humanResult?.available && humanResult.mode === 'offline') {
      report += '- Human.js 离线模式已就绪，可获得更好的检测精度\n';
    }
    
    return report;
  }

  /**
   * 快速健康检查
   */
  async quickHealthCheck(): Promise<{
    overall: 'healthy' | 'warning' | 'error';
    message: string;
    details: any;
  }> {
    try {
      const results = await this.testAllEngines();
      const availableEngines = results.filter(r => r.available);
      
      if (availableEngines.length === 0) {
        return {
          overall: 'error',
          message: '没有可用的人脸识别引擎',
          details: results
        };
      }
      
      if (availableEngines.length === 1) {
        return {
          overall: 'warning',
          message: `只有 ${availableEngines[0].engine} 引擎可用`,
          details: results
        };
      }
      
      return {
        overall: 'healthy',
        message: '所有引擎都可用',
        details: results
      };
    } catch (error) {
      return {
        overall: 'error',
        message: '健康检查失败',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }
}
