<div class="container">
    <div class="title">{{project?.name}}</div>
    <div class="date">
        考试日期：{{project?.start | customDate:"yyyyMMdd"}} - {{project?.end | customDate: "yyyyMMdd"}}
    </div>

    <div class="room-card">
        @if (rooms.length) {
            <div class="header">
                <div class="info">
                    当前考点服务器IP: {{server_ip}} 已连接管理机 {{rooms.length}}，当前在线 {{online_num}}
                </div>
                <div class="tip">
                    考点服务器连接方式： 管理机-设置-考点服务器设置-填写考点服务器的IP地址，并在管理机考前准备模块点击考点服务器检查，完成考点服务器连接
                </div>
            </div>
            <div class="room-list">
                <nz-table #basicTable [nzData]="rooms" [nzFrontPagination]="false" [nzSize]="'small'">
                    <thead>
                      <tr>
                        <th
                        [nzChecked]="allChecked"
                        [nzIndeterminate]="indeterminate"
                        nzLabel="Select all"
                        (nzCheckedChange)="onAllChecked($event)"
                      ></th>
    
                        <th>考场SN</th>
                        <th>考场IP</th>
                        <th>考场地址</th>
                        <th>版本号</th>
                        <th>状态</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                        @for (item of basicTable.data; track $index) {
                            <tr>
                                <td
                                [nzChecked]="item.checked"
                                [nzLabel]="item.sn_code"
                                (nzCheckedChange)="onItemChecked($event, item)"
                              ></td>
                                <td>{{item.sn_code}}</td>
                                <td>{{item.host}}</td>
                                <td>{{item.address}}</td>
                                <td>{{item.app_version}}</td>
                                <td><span [ngClass]="{'color-green':item.is_online, 'color-red': !item.is_online}">
                                    <span class="dot" [ngClass]="{'bg-green': item.is_online, 'bg-red': !item.is_online}"></span>
                                    {{item.is_online ? "在线": "离线"}}</span></td>
                                <td>
                                    <button nz-button nzType="text"  nzDanger (click)="remove(item.sn_code)">移除</button>
                                </td>
                              </tr>
                        }
                    </tbody>
                  </nz-table>
            </div>
            <div class="footer">
                <div class="left">
                    已选中 {{checked_num}}台管理机
                </div>
                <div>
                    <button nz-button nzType="default" [disabled]="!checked_num"  nzDanger (click)="remove()">移除</button>
                </div>
            </div>
        } @else {
            <div class="empty">
                <div class="header">
                    <div class="info">
                        当前考点服务器IP: {{server_ip}}
                    </div>
                </div>
                <div class="body">
                    <div class="empty-wrap">
                        <img src="assets/images/room_empty.png" alt="">
                        <p>暂无已连接的管理机，请在管理机-设置-考点服务器设置-填写考点服务器的IP地址，</p>
                        <p>并在管理机考前准备模块点击考点服务器检查，完成考点服务器连接</p>
                    </div>
                </div>
            </div>
        }
    </div>
</div>