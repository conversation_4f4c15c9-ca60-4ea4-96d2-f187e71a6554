import * as path from "path";
import { app, BrowserWindow } from "electron";
import * as config from "../config";
import { log } from "src/logger";

// import { startServer } from "./init";
// import { JoyShell } from "@share-types/joyshell.types";

export let win: Electron.BrowserWindow;

// declare global {
//   let joyshell: JoyShell;
// }
export function init() {
  if (win) {
    win.show();
    return;
  }

  win = new BrowserWindow({
    width: 600,
    height: 480,
    frame: false,
    resizable: false,
    backgroundColor: "#3c89f3",
    title: '考点服务器',
    closable: false,
    show: false,
    webPreferences: {
      webSecurity: false,
      nodeIntegration: true,
      contextIsolation: false,
      backgroundThrottling: false,
      additionalArguments: ["--server-mode"],
      preload: path.join(__dirname, "preload.js"),
    },
  });

  win.loadFile(`${__dirname}/server/server.html`);

  if (config.DEBUG_LEVEL > 1) {
    win.webContents.openDevTools();
  }

  win.once("ready-to-show", () => {
    win.removeMenu();
    win.show();
  });

  win.on("close", function (e) {
    e.preventDefault();
    win.hide();
  });

  win.webContents.setBackgroundThrottling(false);
  win.webContents.on("render-process-gone", (_event, details) => {
    console.error("server process crashed: ", details.reason);
    if (config.DEBUG_LEVEL > 0) {
      return;
    }
    process.exit(2);
  });

  win.webContents.on("console-message", (_event, level, message, line, sourceId) => {
    switch (level) {
      case 0:
        console.debug(message, " (" + path.basename(sourceId) + ":" + line + ")");
        break;
      case 1:
        log.info(message);
        break;
      case 2: // warning
        if (message.startsWith("This site does not have a valid") || message.startsWith("The vm module")) {
          // no logging this message
          return;
        }
        log.warn(message);
        break;
      case 3:
        log.error(message, " (" + path.basename(sourceId) + ":" + line + ")");
        break;
      default:
        log.info(message);
    }
  });
}
