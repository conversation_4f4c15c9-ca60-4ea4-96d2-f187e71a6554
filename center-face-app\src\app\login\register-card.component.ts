import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  type OnInit,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { CloudHttpService, Group } from '../core/http/cloud.http';
import { AuthService } from '../core/service/auth.service';
import { ModalService } from '../core/service/modal.service';
import { CenterService } from '@app/core/service/center.service';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
    selector: 'app-register-card',
    imports: [CommonModule, FormsModule, NzButtonModule, NzInputModule, NzSelectModule],
    template: `
    <div class="container">
      <div class="tip">
      请确认以下考点及项目信息，并输入动态验证码完成悦考智签系统注册
      </div>
      <div class="name row">
        <span class="row">考点名称：</span><span>{{ name }}</span>
      </div>
      <div class="address row">
        <span class="row">考点地址：</span><span>{{ address }}</span>
      </div>
      <div class="project_name row">
        <span class="row">项目名称：</span><span>{{ project_name }}</span>
      </div>

      <div class="project_name row">
        <span class="row">签到区域：</span>
        <nz-select *ngIf="groups && groups.length > 0" [(ngModel)]="selectedGroupNum" nzPlaceHolder="请选择" style="width: 200px;" (ngModelChange)="onSigninAreaChange($event)">
          <nz-option *ngFor="let group of groups" [nzValue]="group.group_num" [nzLabel]="group.group_name"></nz-option>
        </nz-select>
        <span *ngIf="groups && groups.length === 0">{{ project_name }}</span>
        <span *ngIf="!groups">{{ project_name }}</span>
      </div>

      <div class="button-wrap">
        <button
          nz-button
          nzType="primary"
          [nzLoading]="button_loading"
          (click)="register()"
        >
          确认
        </button>
      </div>
    </div>
  `,
    styles: `
  :host {
  display: block;
  // background-color: #fff;
  border-radius: 8px;
}

.container {
  padding: 20px;
  width: 100%;
  max-width: 400px;
  > div {
    margin-bottom: 16px;
  }
}
.title {
  font-size: 18px;
  font-weight: 600;
  width: 126px;

  color: #419cf8;
}
.row {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-right: 10px;
}
.code > input {
  width: 300px;
}
.button-wrap {
  margin-top: 24px;
  button {
    width: 100%;
    height: 48px;
    border-radius: 6px;
  }
}
`
})
export class RegisterCardComponent implements OnInit {
  button_loading = false;
  name = '';
  address = '';
  project_name = '';
  server_code = '';
  groups: Group[] = [];
  selectedGroupNum: number | null = null;
  selectedSigninArea: Group | null = null;

  @Input() set r_server_code(id: string) {
    console.log(id);
    this.server_code = id;
    this.cloudHttp.getCenterInfo(id).subscribe((res) => {
      this.name = res.data.venue_name;
      this.address = res.data.venue_address;
      this.project_name = res.data.project_name;
      this.groups = res.data.groups;
      this.selectedGroupNum = null;
      this.selectedSigninArea = null;
    });
  };

  onSigninAreaChange(selectedGroupNum: number | null): void {
    if (selectedGroupNum === null) {
      this.selectedSigninArea = null;
    } else {
      this.selectedSigninArea = this.groups.find(group => group.group_num === selectedGroupNum) || null;
    }
  }

  constructor(
    private router: Router,
    private cloudHttp: CloudHttpService,
    private authService: AuthService,
    private modal: ModalService,
    private centerService: CenterService
  ) {}

  ngOnInit(): void {}

  register() {
    if (this.groups && this.groups.length > 0 && this.selectedGroupNum === null) {
      this.modal.error("提示", "请选择签到区域");
      this.button_loading = false;
      return;
    }
    this.button_loading = true;
    const hardware = this.authService.hardwareId || this.authService.createHardwareId();
    this.cloudHttp.register(this.server_code, hardware).subscribe({
      next: (res) => {
        if (res.errcode) {
          this.modal.error("错误", res.errmsg || '注册失败');
          this.button_loading = false;
          return;
        }
        const formal = res.data?.formal || {};
        const center = {
          name: this.name,
          address: this.address,
          code: this.server_code,
          project_name: this.project_name,
          selectedGroup: this.selectedSigninArea || undefined,
          formal: {
            name: formal.name,
            project_id: formal.project_id,
            start: formal.start,
            end: formal.end,
            // late_limit: formal.late_limit,
          },
        };
        this.centerService.register(center);

        this.authService.setHardwareId(hardware);
        this.authService.setAuthToken(res.data.token);
        this.authService.setServerCode(this.server_code);
        this.router.navigate(['dashboard', this.server_code]);
      },
      error: (res) => {
        this.button_loading = false;
        if (res instanceof HttpErrorResponse) {
          const statusCode = res.status;
          console.log('register error:', statusCode);
          if (statusCode === 404) {
            this.modal.error("错误", '数据尚未开放');
          } else {
            this.modal.error("错误", '请求失败');
          }
        }
      },
      complete: () => {
        this.button_loading = false;
      }
    });
  }
  back() {
    this.router.navigate(['/login']);
  }
}
