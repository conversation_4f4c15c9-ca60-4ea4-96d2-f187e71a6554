export interface RoomSessionData {
    session_id: string;
    name: string;
    project_id: string;
    schedule_id: string;
    start: string;
    end: string;
    test: 0 | 1;
    status: number;
    room_name: string;
    form_status: boolean;
    password_status: boolean;
    version: number;
    entry_stat: IEntryStat;
    subjects: RoomSubjectData[];
}
export interface IEntryStat {
    not_enter: number;
    register: number;
    login: number;
    testing: number;
    finished: number;
    absent: number;
  }
  
interface RoomSubjectData {
    subject_id: string;
    name: string;
    code: string;
}

export interface RoomEventData {
    id: string;
    session_id: string;
    type: string;
    content: string;
    created_at: number;
    is_deleted: 0 | 1;
    data_type: 'log' | 'event';
    version: number;
}

function randomString(length = 10) {
    return Math.random().toString(36).substring(2, length + 2);
}

function randomNumber(max = 1000) {
    return Math.floor(Math.random() * max);
}

export function generateSessionData(sessionId: string, scheduleId: string, projectId: string): RoomSessionData {
    return {
        session_id: sessionId,
        name: `Test Session ${sessionId}`,
        project_id: projectId,
        schedule_id: scheduleId,
        start: new Date().toISOString(),
        end: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
        test: 1,
        status: 1,
        room_name: `Test Room ${sessionId}`,
        form_status: true,
        password_status: true,
        version: Date.now(),
        entry_stat: { not_enter: 10, register: 10, login: 10, testing: 10, finished: 10, absent: 0 },
        subjects: [
            {
                subject_id: 'subj-1',
                name: 'Math',
                code: 'M101',
            }
        ]
    };
}

export function generateEventData(sessionId: string, count: number): RoomEventData[] {
    const events: RoomEventData[] = [];
    for (let i = 0; i < count; i++) {
        events.push({
            id: randomString(20),
            session_id: sessionId,
            type: '1',
            content: `Event content ${i}`,
            created_at: Math.floor(Date.now() / 1000),
            is_deleted: 0,
            data_type: 'event',
            version: Date.now() + i,
        });
    }
    return events;
} 