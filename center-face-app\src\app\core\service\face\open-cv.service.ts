import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { OpenCVLoadResult } from './open-cv.models';

declare const cv: any;

const DEFAULT_OPTIONS = {
  scriptUrl: 'assets/opencv/opencv4.9.js',
  //wasmBinaryFile: "assets/opencv/opencv_js.wasm",
  preRun: [
    function () {
      // 提前加载文件
      cv.FS_createPreloadedFile(
        '/',
        'haarcascade_frontalface_default.xml',
        'assets/opencv/haarcascade_frontalface_default.xml',
        true,
        false
      );
    },
  ],
  onRuntimeInitialized: () => {
    console.log('opencv initialized');
  },
};

@Injectable({
  providedIn: 'root',
})
export class OpenCvService {
  private _isReady = new BehaviorSubject<OpenCVLoadResult>({ ready: false, error: false, loading: true });
  isReady: OpenCVLoadResult = { ready: false, error: false, loading: true };
  isReady$ = this._isReady.asObservable();

  loadOpenCV(delayms = 1000) {
    if (this.isReady.ready) {
      return;
    }
    const options = DEFAULT_OPTIONS;
    this.emitStatus({
      ready: false,
      error: false,
      loading: true,
    });
    // @ts-ignore
    window['Module'] = { ...options };
    const script = document.createElement('script');
    script.setAttribute('id', 'opencvjs');
    script.setAttribute('async', '');
    script.setAttribute('type', 'text/javascript');
    script.addEventListener('load', () => {
      const onRuntimeInitializedCallback = () => {
        if (options.onRuntimeInitialized) {
          options.onRuntimeInitialized();
        }
        this.emitStatus({
          ready: true,
          error: false,
          loading: false,
        });
      };
      cv.onRuntimeInitialized = onRuntimeInitializedCallback;
    });
    script.addEventListener('error', (e) => {
      console.error('Failed to load ' + options.scriptUrl);
      this.emitStatus({
        ready: false,
        error: true,
        loading: false,
      });
    });
    script.src = options.scriptUrl;
    if (delayms) {
      setTimeout(() => {
        // 推迟加载opencv: 避免启动时长时间白屏
        const node = document.getElementsByTagName('script')[0];
        if (node) {
          node.parentNode!.insertBefore(script, node);
        } else {
          document.head.appendChild(script);
        }
      }, 1000);
    } else {
      document.head.appendChild(script);
    }
  }

  emitStatus(loadInfo: OpenCVLoadResult) {
    this.isReady = loadInfo;
    this._isReady.next(loadInfo);
  }
}
