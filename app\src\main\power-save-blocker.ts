// Block the system from entering low-power (sleep) mode.
import { powerSaveBlocker } from "electron";
import { getAddon } from "../utils/addon";

let id = -1;

/**
 * Block the system from entering low-power (sleep) mode or turning off the
 * display.
 */
export async function enable() {
  if (process.platform === "win32") {
    const osext = await getAddon("osext");
    console.log("osext", osext);
    osext.setAppState(true);
  }
  id = powerSaveBlocker.start("prevent-display-sleep");
  console.info(`powerSaveBlocker enabled`);
}

/**
 * Stop blocking the system from entering low-power mode.
 */
export async function disable() {
  if (process.platform === "win32") {
    const osext = await getAddon("osext");
    osext.setAppState(false);
  }
  if (id >= 0) {
    id = -1;
  }

  console.info(`powerSaveBlocker disabled`);
}
