import {
  Sequelize,
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';

export default function (sequelize: Sequelize) {
  const Form = sequelize.define<FormModelFields>(
    'form',
    {
      form_id: {
        type: DataTypes.UUID,
        primaryKey: true,
      },
      schedule_id: {
        type: DataTypes.UUID,
        primaryKey: true,
      },
      subject_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      code: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      name: {
        type: DataTypes.STRING,
      },
      md5: {
        type: DataTypes.STRING,
      },
      password: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      form_time: {
        type: DataTypes.STRING,
      },
      password_time: {
        type: DataTypes.STRING,
      },
      form_publish_time: {
        type: DataTypes.STRING,
      },
      password_publish_time: {
        type: DataTypes.STRING,
      },
      version: {
        type: DataTypes.INTEGER,
      },
      status: {
        type: DataTypes.INTEGER,
      },
      created_at: {
        type: DataTypes.INTEGER,
      },
    },
    {
      timestamps: false,
      underscored: true,
      freezeTableName: true,
      hooks: {
        beforeUpdate: (form: FormModelFields, opt) => {},
        beforeCreate: (form: FormModelFields, opt) => {},
      },
    },
  );
  return Form;
}

export interface FormModelFields
  extends Model<
    InferAttributes<FormModelFields>,
    InferCreationAttributes<FormModelFields>
  > {
  form_id: string;
  schedule_id: string;
  subject_id: string;
  code: string;
  name: string;
  md5: string;
  password: string;
  form_time: CreationOptional<string>;
  password_time: CreationOptional<string>;
  form_publish_time: CreationOptional<string>;
  password_publish_time: CreationOptional<string>;
  status: CreationOptional<number>;
  version: CreationOptional<number>;
  created_at: CreationOptional<string>;
}
