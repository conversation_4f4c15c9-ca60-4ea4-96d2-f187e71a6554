import serverTime from '@services/serverTime';
import { IRouter, Router } from 'express';
import * as jwt from 'jsonwebtoken';
import * as path from 'path';
import multer from 'multer';
import fse from 'fs-extra';

import { dataAccess } from '../data/data-access';
import { asyncWrap } from '../utils/express-async-wrap';
import { expressjwt, Request as JWTRequest } from 'express-jwt';
import {
  centerService,
  formService,
  scheduleService,
  roomService,
  logService,
} from '@services/index';
import { HMAC256 } from '@server/utils/utils';
import { CENTER_REGISTER_TOKEN } from '@share-types/constants';
import { RoomEventData, handleRoomEvent } from '@services/room/event.handle';
import { cloudService } from '@services/cloud/cloud.service';
import {
  RoomSessionData,
  handleRoomSession,
} from '@services/room/session.handle';
import {
  EXAM_BASE_PATH,
  examFileManager,
  FORM_BASE_PATH,
} from '@services/resource/file';
import { ESessionStatus } from '@data/session.data';
import { ElogType } from '@share-types/center.types';
export const router: IRouter = Router();

const JWT_SECRET = 'Ha3np4GCzBYaZsIGku7FdY5oapXCM5hE';

enum StatusCode {
  InvalidToken = 401, // 认证失败
  NotFound = 404, // 未找到
  Wait = 408, // 资源暂时不可用 （稍后请求）
  ServerError = 500, // 服务器错误
}

//logger
// router.use(
//   asyncWrap(async (req, res, next) => {
//     console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
//     next();
//   }),
// );

router.use((req, res, next) => {
  const center = centerService.getCenter();
  if (!center?.is_active) {
    return res.status(StatusCode.Wait).send('center not ready');
  }
  next();
});

router.get(
  '/server-time',
  asyncWrap(async (req, res) => {
    res.send({
      server_time: serverTime.now(),
      is_time_synced: cloudService.is_time_synced,
    });
  }),
);

router.get(
  '/version',
  asyncWrap(async (req, res) => {
    res.send({
      version: joyshell?.AppVersion || '2.0',
    });
  }),
);

router.get(
  '/project',
  asyncWrap(async (req, res) => {
    const projects = dataAccess.project.getProjects();
    if (!projects?.length) {
      res.status(StatusCode.NotFound).send();
      return;
    }
    res.send(
      projects.map((project) => ({
        project_id: project.id,
        name: project.name,
      })),
    );
  }),
);

interface IRegister {
  room: {
    name: string;
    room_sn: string;
    address: string;
    host: string;
    app_version: string;
  };
  auth: {
    time: string;
    sign: string;
  };
}
router.post(
  '/register',
  (req, res, next) => {
    const { room, auth } = req.body as IRegister;
    if (!auth) {
      return res.status(StatusCode.InvalidToken).send();
    }
    const hash = HMAC256(CENTER_REGISTER_TOKEN, room.room_sn + auth.time);
    if (hash !== auth.sign) {
      return res.status(StatusCode.InvalidToken).send();
    }
    next();
  },
  asyncWrap(async (req, res) => {
    const { room: register_room } = req.body as IRegister;
    const { name, room_sn, address, host, app_version } = register_room;
    const room = await roomService.register({
      name,
      room_sn,
      host: host || req.ip || '',
      address,
      app_version,
    });
    const token = jwt.sign({ room_sn, token: room.token }, JWT_SECRET);
    res.send({ token });
    await logService.createLog(ElogType.RoomRegister, room_sn);
  }),
);

// eslint-disable-next-line @typescript-eslint/require-await
const checkRevoked = (req: JWTRequest, { payload }: any) => {
  const { room_sn, token } = payload;
  const room = roomService.getRoom(room_sn);
  if (!room || room.token !== token) {
    return true;
  }
  return false;
};
type JWTReq = JWTRequest<{ room_sn: string; token: string }>;

router.use(
  // eslint-disable-next-line @typescript-eslint/no-misused-promises
  expressjwt({
    secret: JWT_SECRET,
    algorithms: ['HS256'],
    isRevoked: checkRevoked,
  }),
  (req: JWTReq, res, next) => {
    if (!req.auth) {
      res.status(401).send();
      return;
    }
    next();
  },
);

interface IFormData {
  id: string; // 试卷id
  subject: string; // 科目id
  version: number;
  code: string;
  password: string;
  md5: string;
  key_version: number;
  series_num: number;
  key?: any;
}
router.post(
  '/forms/info',
  asyncWrap(async (req, res) => {
    const { session_id, schedule_id } = req.body as {
      session_id: string;
      schedule_id: string;
    };
    const forms = await formService.getSessionForms(session_id);
    const schedule = await scheduleService.getSchedule(schedule_id);
    const form_data = forms.map((form) => ({
      id: form.form_id,
      subject: form.subject?.id,
      md5: form.md5,
      code: form.code,
      password: schedule?.password_published ? form.password : null,
      version: form.version,
      key_version: 0,
    }));
    res.send(form_data);
  }),
);

router.get(
  '/forms/:form_id.form',
  asyncWrap(async (req, res) => {
    const form_id = req.params.form_id;
    const session_id = req.query.session_id as string;
    const schedule_id = (await dataAccess.session.get({ id: session_id }))
      ?.schedule_id;

    if (!form_id || !session_id || !schedule_id) {
      res.status(StatusCode.NotFound).send();
      return;
    }
    const schedule = await scheduleService.getSchedule(schedule_id);
    if (!schedule?.form_published) {
      res.status(StatusCode.NotFound).send();
      return;
    }
    res.sendFile(
      `${form_id}.form`,
      { root: path.join(FORM_BASE_PATH, schedule_id) },
      (err) => {
        if (err) {
          res.status(StatusCode.ServerError).send(err.message);
        }
      },
    );
  }),
);

router.get(
  '/sessions',
  asyncWrap(async (req: JWTReq, res) => {
    if (!req.auth) {
      res.status(401).send();
      return;
    }
    const { room_sn } = req.auth;
    const sessions = await dataAccess.session.getSessions({ room_sn });
    const event_versions = await Promise.all(
      sessions.map((session) => dataAccess.event.getMaxVersion(session.id)),
    );
    res.send(
      sessions.map((session, index) => ({
        id: session.id,
        status: session.status,
        event_version: event_versions[index],
      })),
    );
  }),
);

router.post(
  '/sessions',
  asyncWrap(async (req: JWTReq, res) => {
    if (!req.auth) {
      res.status(401).send();
      return;
    }
    const { room_sn } = req.auth;
    const session_data = req.body as RoomSessionData[];
    for (const session of session_data) {
      await handleRoomSession(room_sn, session);
    }
    res.send({});
  }),
);

router.post(
  '/sessions/:session_id/events',
  asyncWrap(async (req: JWTReq, res) => {
    if (!req.auth) {
      res.status(401).send();
      return;
    }
    const { room_sn } = req.auth;
    const session_id = req.params.session_id;
    const event_data = req.body as RoomEventData[];
    await handleRoomEvent(room_sn, session_id, event_data);
    res.send({});
  }),
);

router.post('/files/status', (req, res) => {
  const { schedule_id, session_id, file_name, file_type } = req.body;
  const file_path = path.join(EXAM_BASE_PATH, schedule_id, file_name);
  if (fse.existsSync(file_path)) {
    res.send({ file_name, exist: true });
  } else {
    res.send({ file_name, exist: false });
  }
});

const fileNameFn = function (req, file, cb) {
  const fileName = Buffer.from(file.originalname, 'latin1').toString('utf8');
  cb(null, fileName);
};
const examFile = multer({
  storage: multer.diskStorage({
    destination: function (req, file, cb) {
      const { schedule_id } = req.body;
      if (!schedule_id) {
        cb(new Error('schedule_id is required'), EXAM_BASE_PATH);
        return;
      }
      const dir = path.join(EXAM_BASE_PATH, schedule_id, 'results');
      if (!fse.existsSync(dir)) {
        fse.ensureDirSync(dir);
      }
      cb(null, dir);
    },
    filename: fileNameFn,
  }),
});
router.post(
  '/files/exam-file',
  examFile.any(),
  asyncWrap(async (req, res) => {
    const { schedule_id, session_id, file_name, md5 } = req.body;
    const room_sn = (req as any).auth.room_sn;
    examFileManager.create(file_name, {
      schedule_id,
      session_id,
      room_sn,
      md5,
      type: 'exam-result',
    });
    console.log('upload file:', schedule_id, session_id, room_sn);
    res.send();
  }),
  (err, req, res, next) => {
    if (err) {
      res.status(409).send(err.message);
    }
  },
);

const RoomPackage = multer({
  storage: multer.diskStorage({
    destination: function (req, file, cb) {
      const { schedule_id } = req.body;
      if (!schedule_id) {
        cb(new Error('schedule_id is required'), EXAM_BASE_PATH);
        return;
      }
      const dir = path.join(EXAM_BASE_PATH, schedule_id);
      if (!fse.existsSync(dir)) {
        fse.ensureDirSync(dir);
      }
      cb(null, dir);
    },
    filename: fileNameFn,
  }),
});
router.post(
  '/files/room-package',
  RoomPackage.any(),
  asyncWrap(async (req, res) => {
    const { schedule_id, session_id, file_name } = req.body;
    const room_sn = (req as any).auth.room_sn;
    console.log('upload file:', schedule_id, session_id, room_sn);
    examFileManager.create(file_name, {
      schedule_id,
      session_id,
      room_sn,
      type: 'room-package',
    });
    res.send({});
  }),
  (err, req, res, next) => {
    if (err) {
      res.status(409).send(err.message);
    }
  },
);

router.post(
  '/sync/end',
  asyncWrap(async (req: JWTReq, res) => {
    const room_sn = req.auth?.room_sn;
    if (!room_sn) {
      res.sendStatus(StatusCode.NotFound);
      return;
    }
    const room = roomService.getRoom(room_sn);
    if (!room) {
      res.sendStatus(StatusCode.NotFound);
      return;
    }
    const { sync_type, session_id } = req.body;
    if (sync_type === 'end') {
      await dataAccess.session.update(
        { room_sn, id: session_id },
        { status: ESessionStatus.centerUploaded },
      );
      console.log('sync end:', room_sn, session_id);
      await logService.createLog(
        ElogType.ResultUpload,
        `${room_sn}-${session_id}`,
      );
    }
    room.last_sync_time = serverTime.now();
    res.send();
  }),
);
