{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-checkbox.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/a11y';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport * as i2 from '@angular/cdk/bidi';\nimport { Directionality } from '@angular/cdk/bidi';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Output, ViewEncapsulation, ChangeDetectionStrategy, Component, InjectionToken, inject, effect, forwardRef, booleanAttribute, Input, ViewChild, input, signal, linkedSignal, computed, ElementRef, DestroyRef, afterNextRender, untracked, NgModule } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as i3 from '@angular/forms';\nimport { FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * @deprecated Deprecated in v19.0.0. It is recommended to use `<nz-checkbox-group>`.\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"inputElement\"];\nconst _c2 = [\"nz-checkbox\", \"\"];\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction NzCheckboxGroupComponent_ProjectionFallback_0_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzValue\", option_r1.value)(\"nzName\", ctx_r1.nzName())(\"nzDisabled\", option_r1.disabled || ctx_r1.finalDisabled());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r1.label, \" \");\n  }\n}\nfunction NzCheckboxGroupComponent_ProjectionFallback_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NzCheckboxGroupComponent_ProjectionFallback_0_For_1_Template, 2, 4, \"label\", 0, _forTrack0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r1.normalizedOptions());\n  }\n}\nclass NzCheckboxWrapperComponent {\n  nzOnChange = new EventEmitter();\n  checkboxList = [];\n  addCheckbox(value) {\n    this.checkboxList.push(value);\n  }\n  removeCheckbox(value) {\n    this.checkboxList.splice(this.checkboxList.indexOf(value), 1);\n  }\n  onChange() {\n    const listOfCheckedValue = this.checkboxList.filter(item => item.nzChecked).map(item => item.nzValue);\n    this.nzOnChange.emit(listOfCheckedValue);\n  }\n  static ɵfac = function NzCheckboxWrapperComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzCheckboxWrapperComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzCheckboxWrapperComponent,\n    selectors: [[\"nz-checkbox-wrapper\"]],\n    hostAttrs: [1, \"ant-checkbox-group\"],\n    outputs: {\n      nzOnChange: \"nzOnChange\"\n    },\n    exportAs: [\"nzCheckboxWrapper\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function NzCheckboxWrapperComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxWrapperComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-checkbox-wrapper',\n      exportAs: 'nzCheckboxWrapper',\n      template: `<ng-content></ng-content>`,\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'ant-checkbox-group'\n      }\n    }]\n  }], null, {\n    nzOnChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NZ_CHECKBOX_GROUP = new InjectionToken('NZ_CHECKBOX_GROUP');\nclass NzCheckboxComponent {\n  ngZone;\n  elementRef;\n  cdr;\n  focusMonitor;\n  directionality;\n  dir = 'ltr';\n  destroy$ = new Subject();\n  isNzDisableFirstChange = true;\n  onChange = () => {};\n  onTouched = () => {};\n  inputElement;\n  nzCheckedChange = new EventEmitter();\n  nzValue = null;\n  nzAutoFocus = false;\n  nzDisabled = false;\n  nzIndeterminate = false;\n  nzChecked = false;\n  nzId = null;\n  nzName = null;\n  innerCheckedChange(checked) {\n    if (!this.nzDisabled && !this.checkboxGroupComponent?.finalDisabled()) {\n      this.setValue(checked);\n      this.nzCheckboxWrapperComponent?.onChange();\n      this.checkboxGroupComponent?.onCheckedChange(this.nzValue, checked);\n    }\n  }\n  writeValue(value) {\n    this.nzChecked = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  focus() {\n    this.focusMonitor.focusVia(this.inputElement, 'keyboard');\n  }\n  blur() {\n    this.inputElement.nativeElement.blur();\n  }\n  /** @deprecated */\n  nzCheckboxWrapperComponent = inject(NzCheckboxWrapperComponent, {\n    optional: true\n  });\n  checkboxGroupComponent = inject(NZ_CHECKBOX_GROUP, {\n    optional: true\n  });\n  nzFormStatusService = inject(NzFormStatusService, {\n    optional: true\n  });\n  constructor(ngZone, elementRef, cdr, focusMonitor, directionality) {\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    this.cdr = cdr;\n    this.focusMonitor = focusMonitor;\n    this.directionality = directionality;\n    if (this.checkboxGroupComponent) {\n      effect(() => {\n        const values = this.checkboxGroupComponent.value() || [];\n        this.setValue(values.includes(this.nzValue));\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  ngOnInit() {\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        Promise.resolve().then(() => this.onTouched());\n      }\n    });\n    this.nzCheckboxWrapperComponent?.addCheckbox(this);\n    this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    fromEventOutsideAngular(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n      event.preventDefault();\n      this.focus();\n      if (this.nzDisabled) {\n        return;\n      }\n      this.ngZone.run(() => {\n        this.innerCheckedChange(!this.nzChecked);\n        this.cdr.markForCheck();\n      });\n    });\n    fromEventOutsideAngular(this.inputElement.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => event.stopPropagation());\n  }\n  ngAfterViewInit() {\n    if (this.nzAutoFocus) {\n      this.focus();\n    }\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.elementRef);\n    this.nzCheckboxWrapperComponent?.removeCheckbox(this);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setValue(value) {\n    this.nzChecked = value;\n    this.onChange(value);\n    this.nzCheckedChange.emit(value);\n  }\n  static ɵfac = function NzCheckboxComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzCheckboxComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i2.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzCheckboxComponent,\n    selectors: [[\"\", \"nz-checkbox\", \"\"]],\n    viewQuery: function NzCheckboxComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputElement = _t.first);\n      }\n    },\n    hostAttrs: [1, \"ant-checkbox-wrapper\"],\n    hostVars: 10,\n    hostBindings: function NzCheckboxComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-checkbox-group-item\", !!ctx.checkboxGroupComponent)(\"ant-checkbox-wrapper-in-form-item\", !!ctx.nzFormStatusService)(\"ant-checkbox-wrapper-checked\", ctx.nzChecked)(\"ant-checkbox-wrapper-disabled\", ctx.nzDisabled || (ctx.checkboxGroupComponent == null ? null : ctx.checkboxGroupComponent.finalDisabled()))(\"ant-checkbox-rtl\", ctx.dir === \"rtl\");\n      }\n    },\n    inputs: {\n      nzValue: \"nzValue\",\n      nzAutoFocus: [2, \"nzAutoFocus\", \"nzAutoFocus\", booleanAttribute],\n      nzDisabled: [2, \"nzDisabled\", \"nzDisabled\", booleanAttribute],\n      nzIndeterminate: [2, \"nzIndeterminate\", \"nzIndeterminate\", booleanAttribute],\n      nzChecked: [2, \"nzChecked\", \"nzChecked\", booleanAttribute],\n      nzId: \"nzId\",\n      nzName: \"nzName\"\n    },\n    outputs: {\n      nzCheckedChange: \"nzCheckedChange\"\n    },\n    exportAs: [\"nzCheckbox\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzCheckboxComponent),\n      multi: true\n    }])],\n    attrs: _c2,\n    ngContentSelectors: _c0,\n    decls: 6,\n    vars: 12,\n    consts: [[\"inputElement\", \"\"], [1, \"ant-checkbox\"], [\"type\", \"checkbox\", 1, \"ant-checkbox-input\", 3, \"ngModelChange\", \"checked\", \"ngModel\", \"disabled\"], [1, \"ant-checkbox-inner\"]],\n    template: function NzCheckboxComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"span\", 1)(1, \"input\", 2, 0);\n        i0.ɵɵlistener(\"ngModelChange\", function NzCheckboxComponent_Template_input_ngModelChange_1_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.innerCheckedChange($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(3, \"span\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"span\");\n        i0.ɵɵprojection(5);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        let tmp_6_0;\n        i0.ɵɵclassProp(\"ant-checkbox-checked\", ctx.nzChecked && !ctx.nzIndeterminate)(\"ant-checkbox-disabled\", ctx.nzDisabled || (ctx.checkboxGroupComponent == null ? null : ctx.checkboxGroupComponent.finalDisabled()))(\"ant-checkbox-indeterminate\", ctx.nzIndeterminate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"checked\", ctx.nzChecked)(\"ngModel\", ctx.nzChecked)(\"disabled\", ctx.nzDisabled || ((tmp_6_0 = ctx.checkboxGroupComponent == null ? null : ctx.checkboxGroupComponent.finalDisabled()) !== null && tmp_6_0 !== undefined ? tmp_6_0 : false));\n        i0.ɵɵattribute(\"autofocus\", ctx.nzAutoFocus ? \"autofocus\" : null)(\"id\", ctx.nzId)(\"name\", ctx.nzName || (ctx.checkboxGroupComponent == null ? null : ctx.checkboxGroupComponent.nzName()));\n      }\n    },\n    dependencies: [FormsModule, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-checkbox]',\n      exportAs: 'nzCheckbox',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span\n      class=\"ant-checkbox\"\n      [class.ant-checkbox-checked]=\"nzChecked && !nzIndeterminate\"\n      [class.ant-checkbox-disabled]=\"nzDisabled || checkboxGroupComponent?.finalDisabled()\"\n      [class.ant-checkbox-indeterminate]=\"nzIndeterminate\"\n    >\n      <input\n        #inputElement\n        type=\"checkbox\"\n        class=\"ant-checkbox-input\"\n        [attr.autofocus]=\"nzAutoFocus ? 'autofocus' : null\"\n        [attr.id]=\"nzId\"\n        [attr.name]=\"nzName || checkboxGroupComponent?.nzName()\"\n        [checked]=\"nzChecked\"\n        [ngModel]=\"nzChecked\"\n        [disabled]=\"nzDisabled || (checkboxGroupComponent?.finalDisabled() ?? false)\"\n        (ngModelChange)=\"innerCheckedChange($event)\"\n      />\n      <span class=\"ant-checkbox-inner\"></span>\n    </span>\n    <span><ng-content></ng-content></span>\n  `,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxComponent),\n        multi: true\n      }],\n      host: {\n        class: 'ant-checkbox-wrapper',\n        '[class.ant-checkbox-group-item]': '!!checkboxGroupComponent',\n        '[class.ant-checkbox-wrapper-in-form-item]': '!!nzFormStatusService',\n        '[class.ant-checkbox-wrapper-checked]': 'nzChecked',\n        '[class.ant-checkbox-wrapper-disabled]': 'nzDisabled || checkboxGroupComponent?.finalDisabled()',\n        '[class.ant-checkbox-rtl]': `dir === 'rtl'`\n      },\n      imports: [FormsModule]\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: i2.Directionality\n  }], {\n    inputElement: [{\n      type: ViewChild,\n      args: ['inputElement', {\n        static: true\n      }]\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }],\n    nzValue: [{\n      type: Input\n    }],\n    nzAutoFocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzIndeterminate: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzChecked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzId: [{\n      type: Input\n    }],\n    nzName: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCheckboxGroupComponent {\n  onChange = () => {};\n  onTouched = () => {};\n  isDisabledFirstChange = true;\n  directionality = inject(Directionality);\n  nzName = input(null);\n  nzDisabled = input(false, {\n    transform: booleanAttribute\n  });\n  nzOptions = input([]);\n  value = signal(null);\n  finalDisabled = linkedSignal(() => this.nzDisabled());\n  dir = toSignal(this.directionality.change, {\n    initialValue: this.directionality.value\n  });\n  normalizedOptions = computed(() => normalizeOptions(this.nzOptions()));\n  constructor() {\n    const elementRef = inject(ElementRef);\n    const focusMonitor = inject(FocusMonitor);\n    const destroyRef = inject(DestroyRef);\n    afterNextRender(() => {\n      focusMonitor.monitor(elementRef, true).pipe(takeUntilDestroyed(destroyRef)).subscribe(focusOrigin => {\n        if (!focusOrigin) {\n          this.onTouched();\n        }\n      });\n      destroyRef.onDestroy(() => {\n        focusMonitor.stopMonitoring(elementRef);\n      });\n    });\n  }\n  writeValue(value) {\n    untracked(() => {\n      this.value.set(value);\n    });\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    untracked(() => {\n      this.finalDisabled.set(this.isDisabledFirstChange && this.nzDisabled() || disabled);\n    });\n    this.isDisabledFirstChange = false;\n  }\n  onCheckedChange(optionValue, checked) {\n    if (this.finalDisabled()) return;\n    this.value.update(value => {\n      if (checked) {\n        return value?.concat(optionValue) || [optionValue];\n      } else {\n        return value?.filter(val => val !== optionValue) || [];\n      }\n    });\n    this.onChange(this.value());\n  }\n  static ɵfac = function NzCheckboxGroupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzCheckboxGroupComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzCheckboxGroupComponent,\n    selectors: [[\"nz-checkbox-group\"]],\n    hostAttrs: [1, \"ant-checkbox-group\"],\n    hostVars: 2,\n    hostBindings: function NzCheckboxGroupComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-checkbox-group-rtl\", ctx.dir() === \"rtl\");\n      }\n    },\n    inputs: {\n      nzName: [1, \"nzName\"],\n      nzDisabled: [1, \"nzDisabled\"],\n      nzOptions: [1, \"nzOptions\"]\n    },\n    exportAs: [\"nzCheckboxGroup\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzCheckboxGroupComponent),\n      multi: true\n    }, {\n      provide: NZ_CHECKBOX_GROUP,\n      useExisting: forwardRef(() => NzCheckboxGroupComponent)\n    }])],\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 0,\n    consts: [[\"nz-checkbox\", \"\", 3, \"nzValue\", \"nzName\", \"nzDisabled\"]],\n    template: function NzCheckboxGroupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0, 0, null, NzCheckboxGroupComponent_ProjectionFallback_0_Template, 2, 0);\n      }\n    },\n    dependencies: [NzCheckboxComponent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-checkbox-group',\n      exportAs: 'nzCheckboxGroup',\n      imports: [NzCheckboxComponent],\n      template: `\n    <ng-content>\n      @for (option of normalizedOptions(); track option.value) {\n        <label\n          nz-checkbox\n          [nzValue]=\"option.value\"\n          [nzName]=\"nzName()\"\n          [nzDisabled]=\"option.disabled || finalDisabled()\"\n        >\n          {{ option.label }}\n        </label>\n      }\n    </ng-content>\n  `,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxGroupComponent),\n        multi: true\n      }, {\n        provide: NZ_CHECKBOX_GROUP,\n        useExisting: forwardRef(() => NzCheckboxGroupComponent)\n      }],\n      host: {\n        class: 'ant-checkbox-group',\n        '[class.ant-checkbox-group-rtl]': `dir() === 'rtl'`\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [], null);\n})();\nfunction normalizeOptions(value) {\n  return value.map(item => {\n    if (typeof item === 'string' || typeof item === 'number') {\n      return {\n        label: `${item}`,\n        value: item\n      };\n    }\n    return item;\n  });\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCheckboxModule {\n  static ɵfac = function NzCheckboxModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzCheckboxModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzCheckboxModule,\n    imports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent],\n    exports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzCheckboxComponent, NzCheckboxGroupComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent],\n      exports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_CHECKBOX_GROUP, NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxModule, NzCheckboxWrapperComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,eAAe,EAAE;AAC9B,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,UAAU,KAAK,EAAE,UAAU,OAAO,OAAO,CAAC,EAAE,cAAc,UAAU,YAAY,OAAO,cAAc,CAAC;AAC/H,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,UAAU,OAAO,GAAG;AAAA,EACjD;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,8DAA8D,GAAG,GAAG,SAAS,GAAG,UAAU;AAAA,EACnH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,kBAAkB,CAAC;AAAA,EAC1C;AACF;AACA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,aAAa,IAAI,aAAa;AAAA,EAC9B,eAAe,CAAC;AAAA,EAChB,YAAY,OAAO;AACjB,SAAK,aAAa,KAAK,KAAK;AAAA,EAC9B;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,aAAa,OAAO,KAAK,aAAa,QAAQ,KAAK,GAAG,CAAC;AAAA,EAC9D;AAAA,EACA,WAAW;AACT,UAAM,qBAAqB,KAAK,aAAa,OAAO,UAAQ,KAAK,SAAS,EAAE,IAAI,UAAQ,KAAK,OAAO;AACpG,SAAK,WAAW,KAAK,kBAAkB;AAAA,EACzC;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,IACnC,WAAW,CAAC,GAAG,oBAAoB;AAAA,IACnC,SAAS;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA,UAAU,CAAC,mBAAmB;AAAA,IAC9B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAChE,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,WAAW,IAAI,QAAQ;AAAA,EACvB,yBAAyB;AAAA,EACzB,WAAW,MAAM;AAAA,EAAC;AAAA,EAClB,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB;AAAA,EACA,kBAAkB,IAAI,aAAa;AAAA,EACnC,UAAU;AAAA,EACV,cAAc;AAAA,EACd,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AAAA,EACT,mBAAmB,SAAS;AAC1B,QAAI,CAAC,KAAK,cAAc,CAAC,KAAK,wBAAwB,cAAc,GAAG;AACrE,WAAK,SAAS,OAAO;AACrB,WAAK,4BAA4B,SAAS;AAC1C,WAAK,wBAAwB,gBAAgB,KAAK,SAAS,OAAO;AAAA,IACpE;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,YAAY;AACjB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,QAAQ;AACN,SAAK,aAAa,SAAS,KAAK,cAAc,UAAU;AAAA,EAC1D;AAAA,EACA,OAAO;AACL,SAAK,aAAa,cAAc,KAAK;AAAA,EACvC;AAAA;AAAA,EAEA,6BAA6B,OAAO,4BAA4B;AAAA,IAC9D,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,yBAAyB,OAAO,mBAAmB;AAAA,IACjD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,sBAAsB,OAAO,qBAAqB;AAAA,IAChD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,QAAQ,YAAY,KAAK,cAAc,gBAAgB;AACjE,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,QAAI,KAAK,wBAAwB;AAC/B,aAAO,MAAM;AACX,cAAM,SAAS,KAAK,uBAAuB,MAAM,KAAK,CAAC;AACvD,aAAK,SAAS,OAAO,SAAS,KAAK,OAAO,CAAC;AAC3C,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACvG,UAAI,CAAC,aAAa;AAChB,gBAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,UAAU,CAAC;AAAA,MAC/C;AAAA,IACF,CAAC;AACD,SAAK,4BAA4B,YAAY,IAAI;AACjD,SAAK,eAAe,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAC/E,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,4BAAwB,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAChH,YAAM,eAAe;AACrB,WAAK,MAAM;AACX,UAAI,KAAK,YAAY;AACnB;AAAA,MACF;AACA,WAAK,OAAO,IAAI,MAAM;AACpB,aAAK,mBAAmB,CAAC,KAAK,SAAS;AACvC,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AACD,4BAAwB,KAAK,aAAa,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS,MAAM,gBAAgB,CAAC;AAAA,EAC7I;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,aAAa;AACpB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,eAAe,KAAK,UAAU;AAChD,SAAK,4BAA4B,eAAe,IAAI;AACpD,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,YAAY;AACjB,SAAK,SAAS,KAAK;AACnB,SAAK,gBAAgB,KAAK,KAAK;AAAA,EACjC;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAwB,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,cAAc,CAAC;AAAA,EACxP;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IACnC,WAAW,SAAS,0BAA0B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,MACrE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,sBAAsB;AAAA,IACrC,UAAU;AAAA,IACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,2BAA2B,CAAC,CAAC,IAAI,sBAAsB,EAAE,qCAAqC,CAAC,CAAC,IAAI,mBAAmB,EAAE,gCAAgC,IAAI,SAAS,EAAE,iCAAiC,IAAI,eAAe,IAAI,0BAA0B,OAAO,OAAO,IAAI,uBAAuB,cAAc,EAAE,EAAE,oBAAoB,IAAI,QAAQ,KAAK;AAAA,MAC3W;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,MAC/D,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,iBAAiB,CAAC,GAAG,mBAAmB,mBAAmB,gBAAgB;AAAA,MAC3E,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,IACvB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa,WAAW,MAAM,oBAAmB;AAAA,MACjD,OAAO;AAAA,IACT,CAAC,CAAC,CAAC;AAAA,IACH,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,QAAQ,YAAY,GAAG,sBAAsB,GAAG,iBAAiB,WAAW,WAAW,UAAU,GAAG,CAAC,GAAG,oBAAoB,CAAC;AAAA,IAClL,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,SAAS,GAAG,CAAC;AAChD,QAAG,WAAW,iBAAiB,SAAS,4DAA4D,QAAQ;AAC1G,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,mBAAmB,MAAM,CAAC;AAAA,QACtD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,MAAM;AAC3B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,YAAY,wBAAwB,IAAI,aAAa,CAAC,IAAI,eAAe,EAAE,yBAAyB,IAAI,eAAe,IAAI,0BAA0B,OAAO,OAAO,IAAI,uBAAuB,cAAc,EAAE,EAAE,8BAA8B,IAAI,eAAe;AACpQ,QAAG,UAAU;AACb,QAAG,WAAW,WAAW,IAAI,SAAS,EAAE,WAAW,IAAI,SAAS,EAAE,YAAY,IAAI,gBAAgB,UAAU,IAAI,0BAA0B,OAAO,OAAO,IAAI,uBAAuB,cAAc,OAAO,QAAQ,YAAY,SAAY,UAAU,MAAM;AACxP,QAAG,YAAY,aAAa,IAAI,cAAc,cAAc,IAAI,EAAE,MAAM,IAAI,IAAI,EAAE,QAAQ,IAAI,WAAW,IAAI,0BAA0B,OAAO,OAAO,IAAI,uBAAuB,OAAO,EAAE;AAAA,MAC3L;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAgB,8BAAiC,iBAAoB,OAAO;AAAA,IAC3F,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,mBAAmB;AAAA,QACjD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,mCAAmC;AAAA,QACnC,6CAA6C;AAAA,QAC7C,wCAAwC;AAAA,QACxC,yCAAyC;AAAA,QACzC,4BAA4B;AAAA,MAC9B;AAAA,MACA,SAAS,CAAC,WAAW;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,WAAW,MAAM;AAAA,EAAC;AAAA,EAClB,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB,wBAAwB;AAAA,EACxB,iBAAiB,OAAO,cAAc;AAAA,EACtC,SAAS,MAAM,IAAI;AAAA,EACnB,aAAa,MAAM,OAAO;AAAA,IACxB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,YAAY,MAAM,CAAC,CAAC;AAAA,EACpB,QAAQ,OAAO,IAAI;AAAA,EACnB,gBAAgB,aAAa,MAAM,KAAK,WAAW,CAAC;AAAA,EACpD,MAAM,SAAS,KAAK,eAAe,QAAQ;AAAA,IACzC,cAAc,KAAK,eAAe;AAAA,EACpC,CAAC;AAAA,EACD,oBAAoB,SAAS,MAAM,iBAAiB,KAAK,UAAU,CAAC,CAAC;AAAA,EACrE,cAAc;AACZ,UAAM,aAAa,OAAO,UAAU;AACpC,UAAM,eAAe,OAAO,YAAY;AACxC,UAAM,aAAa,OAAO,UAAU;AACpC,oBAAgB,MAAM;AACpB,mBAAa,QAAQ,YAAY,IAAI,EAAE,KAAK,mBAAmB,UAAU,CAAC,EAAE,UAAU,iBAAe;AACnG,YAAI,CAAC,aAAa;AAChB,eAAK,UAAU;AAAA,QACjB;AAAA,MACF,CAAC;AACD,iBAAW,UAAU,MAAM;AACzB,qBAAa,eAAe,UAAU;AAAA,MACxC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,cAAU,MAAM;AACd,WAAK,MAAM,IAAI,KAAK;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,UAAU;AACzB,cAAU,MAAM;AACd,WAAK,cAAc,IAAI,KAAK,yBAAyB,KAAK,WAAW,KAAK,QAAQ;AAAA,IACpF,CAAC;AACD,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,gBAAgB,aAAa,SAAS;AACpC,QAAI,KAAK,cAAc,EAAG;AAC1B,SAAK,MAAM,OAAO,WAAS;AACzB,UAAI,SAAS;AACX,eAAO,OAAO,OAAO,WAAW,KAAK,CAAC,WAAW;AAAA,MACnD,OAAO;AACL,eAAO,OAAO,OAAO,SAAO,QAAQ,WAAW,KAAK,CAAC;AAAA,MACvD;AAAA,IACF,CAAC;AACD,SAAK,SAAS,KAAK,MAAM,CAAC;AAAA,EAC5B;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,WAAW,CAAC,GAAG,oBAAoB;AAAA,IACnC,UAAU;AAAA,IACV,cAAc,SAAS,sCAAsC,IAAI,KAAK;AACpE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0BAA0B,IAAI,IAAI,MAAM,KAAK;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,WAAW,CAAC,GAAG,WAAW;AAAA,IAC5B;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,IAC5B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa,WAAW,MAAM,yBAAwB;AAAA,MACtD,OAAO;AAAA,IACT,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa,WAAW,MAAM,yBAAwB;AAAA,IACxD,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,eAAe,IAAI,GAAG,WAAW,UAAU,YAAY,CAAC;AAAA,IAClE,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,GAAG,GAAG,MAAM,wDAAwD,GAAG,CAAC;AAAA,MAC1F;AAAA,IACF;AAAA,IACA,cAAc,CAAC,mBAAmB;AAAA,IAClC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS,CAAC,mBAAmB;AAAA,MAC7B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,wBAAwB;AAAA,QACtD,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,wBAAwB;AAAA,MACxD,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,kCAAkC;AAAA,MACpC;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,SAAS,iBAAiB,OAAO;AAC/B,SAAO,MAAM,IAAI,UAAQ;AACvB,QAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU;AACxD,aAAO;AAAA,QACL,OAAO,GAAG,IAAI;AAAA,QACd,OAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAMA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,IACnF,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,EACrF,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,qBAAqB,wBAAwB;AAAA,EACzD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,MACnF,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,IACrF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}