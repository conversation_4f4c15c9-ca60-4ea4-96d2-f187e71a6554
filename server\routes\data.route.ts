import * as path from 'path';

import { I<PERSON><PERSON><PERSON>, Router } from 'express';
import multer from 'multer';

import { asyncWrap } from '../utils/express-async-wrap';
import { packageManager } from '../services/resource/package';
import { dataAccess } from '@data/data-access';
import {
  centerService,
  scheduleService,
  systemSettingService,
} from '@services/index';
import * as cloud_api from '@services/cloud/cloud.api';
import { copyFiles } from '@server/utils/copyFiles';
import { SuccessRes, ErrorRes, ElogType } from '@share-types/center.types';
import serverTime from '@services/serverTime';
import { cloudService } from '@services/cloud/cloud.service';
import { auth_event_handler } from '@services/event/handlers';
import { authEventService, roomService, formService } from '@services/index';
import { AuthEventRes } from '@share-types/auth-event.types';
import { JTA_PASSWORD, UPDATE_URL_STAGE1 } from '@share-types/constants';
import {
  EXAM_BASE_PATH,
  examFileManager,
  TEMP_BASE_PATH,
} from '@services/resource/file';
import { logService } from '@services/index';
import { SystemSetting } from '@services/systemSetting.service';

export const router: IRouter = Router();

const ResSuccess = <T>(data?: T): SuccessRes<T> => ({
  status: 'success',
  data: data as any,
});
const ResError = <T>(code: number, msg: string, data: T): ErrorRes<T> => ({
  status: 'error',
  error: { code, msg, data },
});

router.get(
  '/server-time',
  asyncWrap(async (req, res) => {
    if (!cloudService.is_time_synced) {
      await cloudService.getCloudTime();
    }
    res.send(
      ResSuccess({
        server_time: serverTime.now(),
        is_time_synced: cloudService.is_time_synced,
      }),
    );
  }),
);

// 获取最新版本号
router.get(
  '/latest-version',
  asyncWrap(async (req, res) => {
    const version = joyshell?.AppVersion || '1.0';
    const url = joyshell?.Settings.UPDATE_URL || UPDATE_URL_STAGE1;
    try {
      const info = await cloud_api.getLatestVersion(url, version, 'release');
      res.send(ResSuccess(info));
    } catch (error) {
      console.error('Data: get lastest version error: ', error);
      res.send(ResError(500, '获取版本号失败！', error));
    }
  }),
);

router.get(
  '/center',
  asyncWrap(async (req, res) => {
    const center = centerService.getCenter();
    const data = center?.is_active
      ? {
          center_id: center.center_name,
          center_name: center.center_name,
          center_address: center.center_address,
          project_id: center.project_id,
          project_name: center.project_name,
        }
      : null;
    res.send(ResSuccess(data));
  }),
);

// 登录考点
router.post(
  '/login',
  asyncWrap(async (req, res) => {
    const { code } = req.body as { code: string };
    try {
      const login_data = await cloud_api.cloudLogin(code);
      res.send(ResSuccess(login_data));
    } catch (error) {
      // res.status(500).send(error);
      if (error instanceof cloud_api.CloudError) {
        res.send(ResError(error.code, error.message, error));
        return;
      }
      res.send(ResError(500, '登录失败', null));
    }
  }),
);

// 注册考点
router.post(
  '/register',
  asyncWrap(async (req, res) => {
    const code = req.body.code as string;
    try {
      const {
        projects,
        center_name,
        center_address,
        venue_id: center_id,
      } = await cloud_api.cloudRegister(code);
      await centerService.login({
        center_id,
        center_name,
        center_address,
        projects,
      });
      await logService.createLog(ElogType.CenterRegister, center_name);
      res.send(
        ResSuccess({
          center_name,
          center_address,
          projects,
        }),
      );
    } catch (error: any) {
      if (error instanceof cloud_api.CloudError) {
        res.send(ResError(error.code, error.message, error));
        return;
      }
      res.send(ResError(500, error.message, null));
    }
  }),
);

// 获取系统设置
router.get(
  '/settings',
  asyncWrap(async (req, res) => {
    const setting = systemSettingService.getSetting();
    res.send(ResSuccess(setting));
  }),
);

// 保存系统设置
router.post(
  '/settings',
  asyncWrap(async (req, res) => {
    const setting = req.body as SystemSetting;
    systemSettingService.setSetting(setting);
    res.send(ResSuccess());
  }),
);

// 退出登录
router.get(
  '/logout',
  asyncWrap(async (req, res) => {
    centerService.logout();
    res.send(ResSuccess(null));
  }),
);

// 获取考场
router.get(
  '/rooms',
  asyncWrap(async (req, res) => {
    const rooms = roomService.rooms;
    const data = rooms.map((r) => ({
      sn_code: r.sn_code,
      host: r.host,
      address: r.address,
      app_version: r.app_version,
      is_online: r.isOnline(),
    }));
    res.send(ResSuccess(data));
  }),
);

// 删除考场
router.post(
  '/rooms/delete',
  asyncWrap(async (req, res) => {
    const sn_codes = req.body.sn_codes as string[];
    console.log('Data: delete room', sn_codes);
    sn_codes.forEach((sn_code) => {
      const room = roomService.getRoom(sn_code);
      if (room) {
        logService
          .createLog(ElogType.RoomDelete, room.name)
          .catch((e) => console.error(e));
      }
    });
    try {
      await roomService.remove(sn_codes);
      res.send(ResSuccess(sn_codes));
    } catch (error) {
      res.send(ResError(500, '删除考场失败', error));
    }
  }),
);

// 获取所有项目
router.get(
  '/projects',
  asyncWrap(async (req, res) => {
    const projects = dataAccess.project.getProjects();
    res.send(ResSuccess(projects));
  }),
);

// 获取所有日程
router.get(
  '/schedules',
  asyncWrap(async (req, res) => {
    const schedules = await scheduleService.getAllSchedules();
    const data = schedules.map((schedule) => ({
      schedule_id: schedule.id,
      start: schedule.start,
      end: schedule.end,
      test: schedule.test,
      form_published: schedule.form_published,
      password_published: schedule.password_published,
      sessions: schedule.sessions,
      subjects: schedule.subjects,
    }));
    res.send(ResSuccess(data));
  }),
);

// 获取日程
router.get(
  '/schedules/:schedule_id',
  asyncWrap(async (req, res) => {
    const { schedule_id } = req.params;
    const schedule = await scheduleService.getSchedule(schedule_id);

    const data = schedule
      ? {
          schedule_id: schedule.id,
          start: schedule.start,
          end: schedule.end,
          form_published: schedule.form_published,
          password_published: schedule.password_published,
          sessions: schedule.sessions,
          subjects: schedule.subjects,
          events: schedule.events,
        }
      : null;
    res.send(ResSuccess(data));
  }),
);

const tempPackageStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, TEMP_BASE_PATH);
  },
  filename: function (req, file, cb) {
    cb(null, Buffer.from(file.originalname, 'latin1').toString('utf8'));
  },
});

// 导入数据包（试卷包/密码包/考点数据）
router.post(
  '/schedules/:schedule_id/package/import',
  multer({ storage: tempPackageStorage }).single('file'),
  asyncWrap(async (req, res) => {
    const { schedule_id } = req.params;
    const { file_path, password } = req.body as {
      file_path: string;
      password: string;
    };
    const filePath = file_path || req.file?.path;
    if (!filePath) {
      res.status(500).send();
      return;
    }
    try {
      await packageManager.importPackage(
        schedule_id,
        filePath,
        password || JTA_PASSWORD,
      );
      res.send(ResSuccess());
      const fileName = path.basename(filePath);
      await logService.createLog(ElogType.PackageImport, fileName);
    } catch (err: any) {
      res.send(ResError(err.code, err.msg, err));
    }
  }),
);

// 获取试卷
router.get(
  '/schedules/:schedule_id/forms',
  asyncWrap(async (req, res) => {
    const { schedule_id } = req.params;
    const forms = await formService.getForms(schedule_id);
    const data = forms.map((form) => ({
      form_id: form.form_id,
      schedule_id: form.schedule_id,
      name: form.name,
      form_time: form.form_time,
      password_time: form.password_time,
      form_publish_time: form.form_publish_time,
      password_publish_time: form.password_publish_time,
      status: form.status,
      subject: form.subject,
    }));
    res.send(ResSuccess(data));
  }),
);

// 发布试卷
router.post(
  '/schedules/:schedule_id/forms/publish-form',
  asyncWrap(async (req, res) => {
    const { schedule_id } = req.params;
    await formService.publishForm(schedule_id);
    await scheduleService.publishForm(schedule_id);
    res.send(ResSuccess());
  }),
);

// 发布密码
router.post(
  '/schedules/:schedule_id/forms/publish-password',
  asyncWrap(async (req, res) => {
    const { schedule_id } = req.params;
    await formService.publishPassword(schedule_id);
    await scheduleService.publishPassword(schedule_id);
    res.send(ResSuccess());
  }),
);

// 删除试卷
router.delete(
  '/schedules/:schedule_id/forms/:form_id',
  asyncWrap(async (req, res) => {
    const { schedule_id, form_id } = req.params;
    await formService.deleteForm(schedule_id, form_id);
    res.send(ResSuccess());
  }),
);

// 检查日程结果文件完整性
router.get(
  '/schedules/:schedule_id/exam-result/check',
  asyncWrap(async (req, res) => {
    const { schedule_id } = req.params;
    const sessions = await dataAccess.session.getSessions({
      schedule_id,
    });
    const result: { name: string; file: any }[] = [];
    for (const session of sessions) {
      const file = examFileManager.queryFile({
        schedule_id,
        session_id: session.id,
        type: 'exam-result',
      });
      result.push({
        name: session.room_name,
        file: {
          exist: file ? file.isExist() : false,
          integrity: file ? await file.checkIntegrity() : false,
          name: file?.name,
        },
      });
    }
    res.send(ResSuccess(result));
  }),
);

router.post(
  '/schedules/:schedule_id/exam-result/export',
  asyncWrap(async (req, res) => {
    const { dir_path, session_id } = req.body as {
      dir_path: string;
      session_id: string;
    };
    const schedule_id = req.params.schedule_id;
    let file_path: string = '';
    if (session_id) {
      const file = examFileManager.queryFile({
        session_id,
        schedule_id,
        type: 'exam-result',
      });
      if (!file) {
        // TODO: 下载
        res.send(ResError(404, '文件不存在', null));
        return;
      } else {
        file_path = path.join(
          EXAM_BASE_PATH,
          schedule_id,
          'results',
          file.name,
        );
      }
    } else {
      file_path = path.join(EXAM_BASE_PATH, schedule_id, 'results');
    }
    if (!file_path) {
      res.send(ResError(500, '下载失败', null));
      return;
    }
    try {
      await copyFiles(file_path, dir_path);
    } catch (error) {
      res.send(ResError(500, '导出失败', null));
    }
    await logService.createLog(ElogType.ResultExport, schedule_id);
    res.send(ResSuccess());
  }),
);

router.get(
  '/schedules/:schedule_id/room-package/list',
  asyncWrap(async (req, res) => {
    const { schedule_id } = req.params;
    const session_id = req.query.session_id as string;
    const list = examFileManager
      .getFileList({
        schedule_id,
        session_id,
        type: 'room-package',
      })
      .filter((f) => f.isExist());
    res.send(
      ResSuccess(
        list.map((f) => ({
          id: f.id,
          name: f.name,
          created_at: f.created_at * 1000,
          size: f.size,
        })),
      ),
    );
  }),
);

router.post(
  '/schedules/:schedule_id/room-package/export',
  asyncWrap(async (req, res) => {
    const { dir_path, session_id, file_id } = req.body as {
      dir_path: string;
      session_id: string;
      file_id: string;
    };
    const schedule_id = req.params.schedule_id;
    const fileList = examFileManager.getFileList({
      session_id,
      type: 'room-package',
    });
    const file = fileList.find((f) => f.id === file_id);
    if (!file) {
      res.send(ResError(404, '文件不存在', null));
      return;
    }
    const file_path = path.join(EXAM_BASE_PATH, schedule_id, file.name);
    try {
      await copyFiles(file_path, dir_path);
    } catch (error) {
      res.send(ResError(500, '导出失败', null));
    }
    res.send(ResSuccess());
  }),
);

router.post(
  '/auth-event/create',
  asyncWrap(async (req, res) => {
    const { type, detail } = req.body;
    const handler = auth_event_handler[type];
    if (!handler) {
      res.send(404);
    }
    const result: AuthEventRes = await handler(detail);
    res.send(result);
  }),
);

router.post(
  '/auth/code',
  asyncWrap(async (req, res) => {
    const { event_id, code } = req.body;
    const result = authEventService.authCode(event_id, +code);
    if (result) {
      const auth_event = authEventService.getAuthEventById(event_id);
      if (auth_event) {
        auth_event.confirmed = 'agreed';
        await auth_event.excute();
      }
    }
    res.send(ResSuccess(result));
  }),
);

router.post(
  '/logs/create',
  asyncWrap(async (req, res) => {
    const { type, content } = req.body;
    await logService.createLog(type, content);
    res.send(ResSuccess());
  }),
);

router.get(
  '/logs',
  asyncWrap(async (req, res) => {
    const logs = await dataAccess.log.getLogs();
    res.send(ResSuccess(logs));
  }),
);

router.use((err, req, res, next) => {
  if (err.name === 'PackageError') {
    res.send(ResError(err.code, err.msg, err));
  } else {
    console.error('ServerError:', req.originalUrl, err.message, err.stack);
    res.send(ResError(500, err.message, err));
  }
});
