@use "variables" as *;
@use "mixins" as *;

:host {
  display: block;
}
.container {
  padding: 0 24px 24px 24px;
}
.card {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
}

.test-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  .test-block {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #419cf8;
    color: #fff;
    height: 120px;
    width: 120px;
    border-radius: 8px 0 0 8px;
    font-size: 18px;
  }
  .content {
    flex-grow: 1;
    display: flex;
    justify-content: space-between;
    padding: 24px;
    .right {
      display: flex;
      align-items: center;

      .published {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 400;
        color: #30a46c;
        span {
          margin: 0 2px;
        }
        margin-right: 5px;
      }

      button {
        margin-left: 8px;
      }
    }
  }
}
.title {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 20px;
}
.sub-title {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.45);
}
.form-card {
  margin-left: 124px;
  app-upload-button {
    margin-bottom: 10px;
    float: right;
  }
  button {
    float: right;
    margin-bottom: 20px;
  }
  .upload-button {
    display: flex;
    justify-content: center;
  }
}
.formal-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 0;
  .formal-block {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #1633ed;
    color: #fff;
    height: 120px;
    width: 120px;
    border-radius: 8px 0 0 8px;
    font-size: 18px;
  }
  .content {
    flex-grow: 1;
    padding: 24px;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
.schedule-list {
  margin-top: 10px;
  margin-left: 124px;
  .schedule-card {
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: 12px 24px;
    border: 1px solid #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    background-color: #fff;
    cursor: pointer;
    &.completed {
      border-color: #30a46c;
      .status {
        color: #30a46c;
      }
    }
    &.not-start {
      color: rgba(0, 0, 0, 0.45);
    }
    &.active {
      background-color: rgba(22, 51, 237, 0.1);
      border: 1px solid rgba(22, 51, 237, 0.3);
      &::before {
        background-color: #fff;
        border: 5px solid $color-base;
        box-shadow: 0 0 16px rgba(7, 115, 226, 0.8);
      }
    }
    &:first-child {
      &::after {
        content: none;
      }
    }
    &::before {
      content: "";
      position: absolute;
      z-index: 1;
      left: -76px;
      top: 12px;
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #c4caef;
    }
    &::after {
      content: "";
      position: absolute;
      left: -65px;
      top: -30px;
      display: block;
      width: 2px;
      height: 60px;
      background: #c4caef;
    }
    .schedule-info {
      display: flex;
      flex: 1;
      span {
        margin-right: 10px;
      }
      .info {
        min-width: fit-content;
      }
      .status {
        display: inline-block;
        min-width: 85px;
      }
      .subject {
        flex: 1;
        @include text-overflow-ellipsis();
      }
    }
    .schedule-stat {
      min-width: fit-content;
      span {
        margin-left: 10px;
      }
    }
  }
}
