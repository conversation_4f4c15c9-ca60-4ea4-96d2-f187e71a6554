import { Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpContextToken, HttpParams } from '@angular/common/http';
import { Observable, tap, timeout } from 'rxjs';
import { CenterService } from '../service/center.service';


export const AUTH_DISABLE = new HttpContextToken<boolean>(() => false);
@Injectable({
  providedIn: 'root',
})
export class CloudHttpService {
  center!: {name: string, address: string, code: string, project_name: string};
  constructor(private http: HttpClient, private centerService: CenterService) {}

  public getCloudTime(): Observable<any> {
    return this.http.post<{ timestamp: number }>('/ts-api/v1/time', {
      method: 'time', type: 'time' 
    }, {context: new HttpContext().set(AUTH_DISABLE, true)});
  }

  public getCenterInfo(server_id: string){
    return this.http.post<CloudRes<CenterInfo>>(`/ts-api/v1/venue/info/`, {
      server_id,device_type: "venue_signin"
    }, {context: new HttpContext().set(AUTH_DISABLE, true)}).pipe(
      tap((res: CloudRes<CenterInfo>) => {
        if (res.data) {
          this.center = {
            name: res.data.venue_name,
            address: res.data.venue_address,
            project_name: res.data.project_name,
            code: server_id,
          };
        }
      })
    );
  }

  public register(server_id: string, hardware: string) {
    return this.http.post<CloudRes<RegisterData>>(`/ts-api/v1/venue/register/`, {
      device_type: 'venue_signin',
      server_id,
      hardware,
    }, {context: new HttpContext().set(AUTH_DISABLE, true)});
  }

  public getScheduleList() {
    let params = new HttpParams();
    const group_num = this.centerService.center().selectedGroup?.group_num;
    if (group_num) {
      params = params.set('group', group_num);
    }
    return this.http.get<CloudRes<CloudSchedule[]>>(`/ts-api/v1/venue/schedules/`, {params});
  }

  public searchFace(schedule_id: string, photo: string, mode?: "secondly") {
    if (mode) {
      console.log('search face mode:', mode);
    }
    const group_num = this.centerService.center().selectedGroup?.group_num;
    const body: any = {
      schedule_id,
      photo,
      mode: mode || undefined
    };
    if (group_num) {
      body.group = group_num;
    }
    return this.http.post<CloudRes<SearchFaceRes>>(`/ts-api/v1/venue/search-face/`, body).pipe(timeout(5000));
  }
}


interface CloudRes<T = any> {
  code: number;
  data: T;
  errcode?: number;
  errmsg?: string;
}

interface CenterInfo {
  venue_name: string;
  venue_address: string;
  project_name: string;
  same_hardware: boolean;
  groups: Group[];
}

export interface Group {
  group_num: number;
  group_name: string;
}

interface RegisterData {
  token: string;
  venue_id: string;
  formal: {
    project_id: string;
    name: string;
    start: string;
    end: string;
    late_limit: number;
  };
  groups: {  // 字段预留 用于后续同一个考点登录之后选择不同楼栋
    id: string;
    name: string;
  }[];
}

export interface CloudSchedule {
  id: string;
  start: string;
  end: string;
  subject: string[];
  signin_count: number;
  total_count: number;
  late_limit: number;
  subjects: {
    id: string;
    name: string;
  }[];
}

// error_code 110002 222207 表示不在该考点
export interface SearchFaceRes {
  has_match: boolean;
  entry_info: {
    id: string;
    name: string;
    permit: string;
  } | null;
  error_code: string;
  error_msg: string;
}
