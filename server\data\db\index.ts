/* eslint-disable @typescript-eslint/no-var-requires */
import { DbConnection } from '../types';
interface DBProviderOptions {
  use: 'sqlite' | 'mysql';
}

class DataBaseProvider {
  private db: DbConnection;
  constructor(options: DBProviderOptions) {
    if (options.use === 'mysql') {
      this.db = require('./mysql.db').default;
    } else {
      this.db = require('./sqlite.db').default;
    }
  }
  getDb() {
    return this.db;
  }
}

export const DBProvider = new DataBaseProvider({
  use: 'sqlite',
});
