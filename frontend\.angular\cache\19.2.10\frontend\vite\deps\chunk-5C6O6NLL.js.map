{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-no-animation.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, booleanAttribute, Input, Directive, NgModule } from '@angular/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzNoAnimationDirective {\n  animationType = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  nzNoAnimation = false;\n  static ɵfac = function NzNoAnimationDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzNoAnimationDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzNoAnimationDirective,\n    selectors: [[\"\", \"nzNoAnimation\", \"\"]],\n    hostVars: 2,\n    hostBindings: function NzNoAnimationDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"nz-animate-disabled\", ctx.nzNoAnimation || ctx.animationType === \"NoopAnimations\");\n      }\n    },\n    inputs: {\n      nzNoAnimation: [2, \"nzNoAnimation\", \"nzNoAnimation\", booleanAttribute]\n    },\n    exportAs: [\"nzNoAnimation\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzNoAnimationDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzNoAnimation]',\n      exportAs: 'nzNoAnimation',\n      host: {\n        '[class.nz-animate-disabled]': `nzNoAnimation || animationType === 'NoopAnimations'`\n      }\n    }]\n  }], null, {\n    nzNoAnimation: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzNoAnimationModule {\n  static ɵfac = function NzNoAnimationModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzNoAnimationModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzNoAnimationModule,\n    imports: [NzNoAnimationDirective],\n    exports: [NzNoAnimationDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzNoAnimationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzNoAnimationDirective],\n      exports: [NzNoAnimationDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzNoAnimationDirective, NzNoAnimationModule };\n"], "mappings": ";;;;;;;;;;;;;;;AAQA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,gBAAgB,OAAO,uBAAuB;AAAA,IAC5C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,gBAAgB;AAAA,EAChB,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACrC,UAAU;AAAA,IACV,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,uBAAuB,IAAI,iBAAiB,IAAI,kBAAkB,gBAAgB;AAAA,MACnG;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,IACvE;AAAA,IACA,UAAU,CAAC,eAAe;AAAA,EAC5B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,+BAA+B;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,sBAAsB;AAAA,IAChC,SAAS,CAAC,sBAAsB;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,sBAAsB;AAAA,MAChC,SAAS,CAAC,sBAAsB;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}