import { CommonModule } from '@angular/common';
import { Component, inject, type OnInit } from '@angular/core';
import { ExamHttpService } from '@app/core/http/exam.service';
import { MessageService } from '@app/core/services/message.service';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzTableModule } from 'ng-zorro-antd/table';

export interface IExamResultModalData {
  schedule_id: string;
}
interface IExamResult {
  name: string;
  file: {
    exist: boolean;
    integrity: boolean
    name: string;
  }
}
@Component({
    selector: 'app-exam-result-modal',
    imports: [
        CommonModule,
        NzTableModule,
        NzButtonModule,
        NzIconModule,
        NzPopoverModule
    ],
    templateUrl: './exam-result-modal.component.html',
    styleUrls: ['./exam-result-modal.component.scss']
})
export class ExamResultModalComponent implements OnInit {
  schedule_id = ''
  resultList: IExamResult[] = []

  readonly #modal = inject(NzModalRef);
  readonly nzModalData: IExamResultModalData = inject(NZ_MODAL_DATA);

  constructor(
    private examHttp: ExamHttpService,
    private messageService: MessageService,
  ) {}
  
  ngOnInit(): void { 
    this.schedule_id = this.nzModalData.schedule_id;
    this.examHttp.checkExamResult(this.schedule_id).subscribe(res => {
      if (res.status === 'success') {
        this.resultList = (res.data as IExamResult[]).sort((a, b) => {
          const aStatus = (a.file.exist ? 1 : 0) + (a.file.integrity ? 1 : 0);
          const bStatus = (b.file.exist ? 1 : 0) + (b.file.integrity ? 1 : 0);
          return aStatus - bStatus;
      });
      }
    })
  }

  close() {
    this.#modal.destroy();
  }

  exportResult() {
    if (!window.joyshell) {
      return;
    }
    joyshell
      .ShowOpenDialog({
        title: '请选择导出文件夹',
        properties: ['openDirectory', 'createDirectory'],
      })
      .then(({ filePaths, canceled }) => {
        if (canceled) {
          return;
        }
        const msgRef = this.messageService.loading('正在导出....');
        this.examHttp
          .exportExamResult(this.schedule_id, filePaths[0])
          .subscribe(() => {
            msgRef.close();
            this.messageService.create('success', '导出成功！');
            this.close();
          });
      });
  }
}
