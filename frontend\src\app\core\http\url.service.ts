import { Injectable } from '@angular/core';
import { ICenterSetting } from '@share-types/settings.types';

@Injectable({
  providedIn: 'root',
})
export class URLService {
  readonly serverUrl: string;
  config: ICenterSetting;

  constructor() {
    // If running on joyshell
    const joyshell = window.joyshell;
    if (joyshell) {
      this.config = joyshell.Settings;
      // this.serverUrl = 'https://127.0.0.1:' + this.config.SERVER_SSL_PORT;
      this.serverUrl = `http://127.0.0.1:${this.config.SERVER_PORT}`;
    } else {
      this.serverUrl = `${window.location.protocol}//${window.location.host}`;
    }
  }

  // 内部访问地址
  getServerUrl(): string {
    return this.serverUrl;
  }

  // 外部访问地址
  getExternalServerUrl(): string {
    if (this.config) {
      return joyshell.GetServerURL();
    } else {
      return this.serverUrl;
    }
  }

  // 返回webrtc websocket server
  // getSignalUrl(): string {
  //   if (this.config && this.config.SIGNAL_URL) {
  //     return this.config.SIGNAL_URL;
  //   } else {
  //     return WS_URL_PRODUCTION;
  //   }
  // }
}
