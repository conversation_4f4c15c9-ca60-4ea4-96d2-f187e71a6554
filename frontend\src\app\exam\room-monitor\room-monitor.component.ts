import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Component, Input, type OnInit } from '@angular/core';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { RoomCardComponent } from './room-card/room-card.component';
import { IRoomData, RoomDataService } from '../../core/data';
import { ListStatusPipe } from '@app/shared/pipes/list-status.pipe';
import {
  IHttpSchedule,
  IHttpSession,
  SessionStatus,
} from '../../core/http/exam.service';

enum RoomStatus {
  NotRegister,
  NotEnter,
  NotFormSync,
  NotPasswordSync,
  NotStart,
  NotEnd,
  NotUploadResult,
  NotUploadBackup,
}
export interface RoomData extends IHttpSession {
  is_online: boolean;
  subjects: string[];
  event_list: any[];
}
@Component({
    selector: 'app-room-monitor',
    imports: [
        CommonModule,
        NzCheckboxModule,
        NzGridModule,
        NzDividerModule,
        FormsModule,
        RoomCardComponent,
        ListStatusPipe,
    ],
    templateUrl: './room-monitor.component.html',
    styleUrls: ['./room-monitor.component.scss']
})
export class RoomMonitorComponent implements OnInit {
  scheduleData: IHttpSchedule;
  room_list: IRoomData[] = [];
  rooms: RoomData[] = [];
  statusFilter = new Map<SessionStatus, boolean>([
    [SessionStatus.notEnter, true],
    [SessionStatus.enter, true],
    [SessionStatus.test, true],
    [SessionStatus.pause, true],
    [SessionStatus.end, true],
    [SessionStatus.centerUploaded, true],
  ]);
  RoomStatus = RoomStatus;
  SessionStatus = SessionStatus;
  @Input() set schedule(data: IHttpSchedule) {
    this.scheduleData = data;
    this.updateData();
  }

  constructor(private roomDataService: RoomDataService) {}
  ngOnInit(): void {
    this.roomDataService.rooms$.subscribe((rooms) => {
      this.room_list = rooms;
      this.updateData();
    });
  }
  updateStatusFilter(arg: SessionStatus[]) {
    const arr = arg.map((number) => [number, true]);
    this.statusFilter = new Map(arr as any);
  }

  updateData() {
    const { sessions, subjects, events } = this.scheduleData;
    if (!sessions || !subjects) {
      return;
    }
    this.rooms = sessions.reduce((acc, cur) => {
      const room = this.room_list.find((r) => r.sn_code === cur.room_sn);
      if (room) {
        acc.push({
          ...cur,
          is_online: room.is_online,
          subjects: subjects
            .filter((subject) => cur.id === subject.session_id)
            .map((subject) => subject.name),
          event_list: events?.filter((e) => e.session_id === cur.id) || [],
        });
      }
      return acc;
    }, [] as RoomData[]);
  }
}
