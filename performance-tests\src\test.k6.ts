import { check, sleep, group } from 'k6';
import { Trend } from 'k6/metrics';
import http from 'k6/http';
import crypto from "k6/crypto";
import { generateSessionData, generateEventData } from './data-generator.ts';

// --- Configuration ---
const BASE_URL = 'https://127.0.0.1:3004/room'; // Adjust to your server's address
// const BASE_URL = 'https://************:19101/room';
const SCHEDULE_ID = '63bfb84c55ee1d3fb169e15b';
// const SCHEDULE_ID = "6842436daedc72c54238fe84" //'6842436daedc72c54238fe84';
const CENTER_REGISTER_TOKEN = 'Mw8T3UWbAJ4Wzbn892xY7wOb8Siqy1zO';

// --- Custom Metrics ---
const sessionsPostDuration = new Trend('sessions_post_duration', true);
const eventsPostDuration = new Trend('events_post_duration', true);
const sessionsGetDuration = new Trend('sessions_get_duration', true);
const syncEndPostDuration = new Trend('sync_end_post_duration', true);
const formsInfoPostDuration = new Trend('forms_info_post_duration', true);
const formsDownloadGetDuration = new Trend('forms_download_get_duration', true);
const examFileUploadPostDuration = new Trend('exam_file_upload_post_duration', true);
const roomPackageUploadPostDuration = new Trend('room_package_upload_post_duration', true);

const MAX_VUS = 50;
const formDownloaded = new Array(MAX_VUS).fill(false);
const fileUploaded = new Array(MAX_VUS).fill(false);
// --- k6 Options ---
export const options = {
  insecureSkipTlsVerify: true,
  stages: [
    { duration: '1m', target: MAX_VUS }, 
    { duration: '5m', target: MAX_VUS },
    { duration: '1m', target: 0 },
  ],
  thresholds: {
    'http_req_failed': ['rate<0.01'], // http errors should be less than 1%
    'http_req_duration': ['p(95)<3000'], // 95% of requests should be below 3s
    'sessions_get_duration': ['p(95)<1000'],
    'sessions_post_duration': ['p(95)<1500'], // 95% of POST /sessions requests should be below 1.5s
    'events_post_duration': ['p(95)<3000'], // 95% of POST /events requests should be below 3s to accommodate batching
    'sync_end_post_duration': ['p(95)<1000'],
    'forms_info_post_duration': ['p(95)<1500'],
    'forms_download_get_duration': ['p(95)<3000'],
    'exam_file_upload_post_duration': ['p(95)<3000'],
    'room_package_upload_post_duration': ['p(95)<3000'],
  },
};

// --- Setup Function (runs once per test) ---
export function setup() {

  console.log(`Setting up and registering ${MAX_VUS} rooms...`);

  const tokens = [];
  for (let vu = 1; vu <= MAX_VUS; vu++) {
    const room_sn = `PERF_TEST_ROOM_${vu}`;
    const registerTime = new Date().toISOString();
    const sign = crypto.hmac(
      'sha256',
      CENTER_REGISTER_TOKEN,
      room_sn + registerTime,
      'hex',
    );

    const registerPayload = {
      room: {
        name: `Perf Test Room ${vu}`,
        room_sn: room_sn,
        address: 'Test Address',
        host: 'localhost',
        app_version: '1.0.0',
      },
      auth: {
        time: registerTime,
        sign: sign,
      },
    };

    const res = http.post(
      `${BASE_URL}/register`,
      JSON.stringify(registerPayload),
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );

    if (res.status === 200 && res.json('token')) {
      const { token } = res.json() as { token: string };
      tokens.push(`Bearer ${token}`);
    } else {
      console.error(`Registration failed for VU ${vu}. It will be skipped.`);
      console.error(`  Response status: ${res.status}`);
      console.error(`  Response body: ${res.body}`);
      tokens.push(null);
    }
  }
  console.log('Setup complete. All rooms registered.');
  return tokens;
}

// --- Main VU Function (runs in a loop for each VU) ---
export default function (data: string[]) {
  const token = data[__VU - 1]; // Get the token for the current VU
  if (!token) {
    return; // Stop this VU if registration failed
  }

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': token,
  };

  const sessionId = `session-${__VU}`;
  const scheduleId = SCHEDULE_ID;

  group('Session and Event Posting', () => {
    let projectId: string | null = null;
    // 0. Get Project
    group('GET /project', () => {
      const res = http.get(`${BASE_URL}/project`);
      check(res, { 'get project ok': (r) => r.status === 200 });
      const projects = res.json();
      if (Array.isArray(projects) && projects.length > 0) {
        projectId = (projects[0] as any).project_id;
      }
    });

    // 1. Get Sessions
    group('GET /sessions', () => {
      const res = http.get(`${BASE_URL}/sessions`, { headers });
      check(res, { 'get sessions ok': (r) => r.status === 200 });
      sessionsGetDuration.add(res.timings.duration);
    });

    sleep(1);

    // 2. Post Session Data
    group('POST /sessions', () => {
      if (!projectId) {
        console.warn(`[VU=${__VU}] Skipping POST /sessions because projectId is not available.`);
        return;
      }
      const sessionPayload = [generateSessionData(sessionId, scheduleId, projectId)];
      const res = http.post(`${BASE_URL}/sessions`, JSON.stringify(sessionPayload), { headers });
      check(res, { 'post session ok': (r) => r.status === 200 });
      sessionsPostDuration.add(res.timings.duration);
    });

    sleep(1);

    // 3. Post Event Data
    group('POST /sessions/:session_id/events', () => {
      const eventPayload = generateEventData(sessionId, 5); // 5 events per batch
      const res = http.post(`${BASE_URL}/sessions/${sessionId}/events`, JSON.stringify(eventPayload), { headers });
      check(res, { 'post events ok': (r) => r.status === 200 });
      eventsPostDuration.add(res.timings.duration);
    });

    sleep(1);

    // 4. Post Sync End
    group('POST /sync/end', () => {
      const payload = {
        sync_type: 'sync',
        session_id: sessionId,
      };
      const res = http.post(`${BASE_URL}/sync/end`, JSON.stringify(payload), { headers });
      check(res, { 'post sync end ok': (r) => r.status === 200 });
      syncEndPostDuration.add(res.timings.duration);
    });
  });

  if (!formDownloaded[__VU - 1]) {
    sleep(1);

    group('Form Operations', () => {
      let forms: any[] = [];
      // 1. Get Forms Info
      group('POST /forms/info', () => {
        const payload = {
          session_id: sessionId,
          schedule_id: scheduleId,
        };
        const res = http.post(`${BASE_URL}/forms/info`, JSON.stringify(payload), { headers });
        check(res, {
          'get forms info ok': (r) => r.status === 200,
          'forms info is an array': (r) => {
            try {
              const body = r.json();
              return Array.isArray(body);
            } catch (e) {
              return false;
            }
          }
        });
        if (res.status === 200) {
          forms = res.json() as any[];
        }
        formsInfoPostDuration.add(res.timings.duration);
      });

      if (forms.length > 0) {
        sleep(1);

        // 2. Download a Form
        group('GET /forms/:form_id.form', () => {
          const formToDownload = forms[0];
          const res = http.get(`${BASE_URL}/forms/${formToDownload.id}.form?session_id=${sessionId}`, { headers });
          check(res, { 'download form ok': (r) => r.status === 200 });
          if (res.status === 200) {
            formDownloaded[__VU - 1] = true;
          }
          formsDownloadGetDuration.add(res.timings.duration);
        });
      }
    });
  }

  if (!fileUploaded[__VU - 1]) {
    sleep(1);

    group('File Operations', () => {
      // 1. Upload Exam File
      group('POST /files/exam-file', () => {
        const examFileName = `result-${__VU}.zip`;
        const examFileData = 'this is a dummy exam result file';
        const examFileMD5 = crypto.md5(examFileData, 'hex');

        const formData = {
          schedule_id: scheduleId,
          session_id: sessionId,
          file_name: examFileName,
          md5: examFileMD5,
          file: http.file(examFileData, examFileName),
        };

        const res = http.post(`${BASE_URL}/files/exam-file`, formData, { headers: { 'Authorization': headers.Authorization } });
        check(res, { 'upload exam file ok': (r) => r.status === 200 });
        examFileUploadPostDuration.add(res.timings.duration);
      });

      sleep(1);

      // 2. Upload Room Package
      group('POST /files/room-package', () => {
        const pkgFileName = `package-${__VU}.zip`;
        const pkgFileData = 'this is a dummy room package file';

        const formData = {
          schedule_id: scheduleId,
          session_id: sessionId,
          file_name: pkgFileName,
          file: http.file(pkgFileData, pkgFileName),
        };

        const res = http.post(`${BASE_URL}/files/room-package`, formData, { headers: { 'Authorization': headers.Authorization } });
        check(res, { 'upload room package ok': (r) => r.status === 200 });
        roomPackageUploadPostDuration.add(res.timings.duration);
      });
    });

    fileUploaded[__VU - 1] = true;
  }

  sleep(1);
}

export function teardown(data: string[]) {
  console.log(`\n--- Tearing down and sending final sync for ${data.filter(t => t).length} rooms... ---\n`);

  const tokens = data;
  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i];
    if (!token) {
      continue; // Skip if registration failed
    }

    const vu = i + 1;
    const sessionId = `session-${vu}`;
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': token,
    };

    const payload = {
      sync_type: 'end',
      session_id: sessionId,
    };

    const res = http.post(`${BASE_URL}/sync/end`, JSON.stringify(payload), { headers });

    if (res.status === 200) {
      console.log(`[VU=${vu}] Final sync 'end' sent successfully for session ${sessionId}.`);
    } else {
      console.error(`[VU=${vu}] Failed to send final sync 'end' for session ${sessionId}. Status: ${res.status}, Body: ${res.body}`);
    }
  }
  console.log('\n--- Teardown complete. ---\n');
} 