import { Observable } from 'rxjs';

export interface VideoDetectControl {
  start(): void;
  stop(): void;
  faceDetect$: Observable<FaceDetectResult | false>;
}

export interface VideoProcessingOptions {
  video: HTMLVideoElement;
  videoWidth: number;
  videoHeight: number;
  detectFrame: { x: number, y: number, width: number, height: number };
  faceOptions?: FaceDetectOptions;
}

export enum FaceDetectResultCode {
  SUCCESS = 0,
  NO_FACE = 201,
  MULTI_FACE = 202,
  FACE_NOT_IN_CENTER = 203,
  FACE_TOO_SMALL = 204,
}

export interface FaceDetectResult {
  code: FaceDetectResultCode;
  image: string | null;
  faceImage: string | null;
}

export interface FaceDetectOptions {
  faceInCenter?: boolean;
  faceSize?: boolean;
  minFaceNum?: number;
}

export type FaceDetectEngine = 'mediapipe' | 'human';

/**
 * 人脸检测服务基类
 * 定义了所有人脸检测引擎必须实现的接口
 */
export abstract class FaceDetectBaseService {
  /**
   * 设置视频检测配置
   * @param options 视频处理选项
   * @returns 视频检测控制器
   */
  abstract setVideoDetectConfig(options: VideoProcessingOptions): VideoDetectControl;

  /**
   * 设置人脸检测选项
   * @param options 人脸检测选项
   */
  abstract setFaceOptions(options: FaceDetectOptions): void;

  /**
   * 获取引擎名称
   */
  abstract getEngineName(): FaceDetectEngine;

  /**
   * 检查引擎是否已准备就绪
   */
  abstract isReady(): boolean;

  /**
   * 初始化引擎
   */
  abstract initialize(): Promise<any>;

  /**
   * 销毁引擎资源
   */
  abstract destroy(): void;
}
