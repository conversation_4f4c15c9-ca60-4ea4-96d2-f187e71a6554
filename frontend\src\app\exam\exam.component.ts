import { CommonModule } from '@angular/common';
import {
  Component,
  <PERSON><PERSON><PERSON>roy,
  ViewContainerRef,
  type OnInit,
} from '@angular/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSegmentedModule } from 'ng-zorro-antd/segmented';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { ExpandComponent } from '../shared/components/expand.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzStepsModule } from 'ng-zorro-antd/steps';
import { ActivatedRoute } from '@angular/router';
import { RoomMonitorComponent } from './room-monitor/room-monitor.component';
import { ChartPieComponent } from '../shared/components/chart-pie.component';
import { ChartPieEntryComponent } from '../shared/components/chart-pie-entry.component';
import { NzQRCodeModule } from 'ng-zorro-antd/qr-code';
import { ModalService } from '../core/services/modal.service';
import {
  ExamHttpService,
  IHttpEntryStat,
  IHttpProject,
  IHttpSchedule,
  IHttpSession,
  IHttpSubject,
  SessionStatus,
} from '../core/http/exam.service';
import { IHttpForm } from '../core/http/form.service';
import { CustomDatePipe } from '../shared/pipes/custom-date.pipe';
import { UploadButtonComponent } from '../shared/components/upload-button.component';
import { MessageService } from '../core/services/message.service';
import {
  FormDataService,
  IRoomData,
  ProjectDataService,
  RoomDataService,
  ScheduleDataService,
} from '../core/data';
import { Subject, switchMap, takeUntil, tap, timer } from 'rxjs';
import { ServerTimeService } from '../core/services/server-time.service';

interface ISessionStat {
  total: number;
  enter: number;
  form: number;
  password: number;
  start: number;
  end: number;
  result: number;
}
enum ETab {
  Overview = "考点概览",
  Monitor = "考场监控",
}

@Component({
    selector: 'app-exam',
    imports: [
        CommonModule,
        NzTableModule,
        NzButtonModule,
        NzIconModule,
        NzSegmentedModule,
        NzDividerModule,
        NzStepsModule,
        NzQRCodeModule,
        ExpandComponent,
        RoomMonitorComponent,
        ChartPieComponent,
        ChartPieEntryComponent,
        CustomDatePipe,
        UploadButtonComponent,
    ],
    templateUrl: './exam.component.html',
    styleUrls: ['./exam.component.scss']
})
export class ExamComponent implements OnInit, OnDestroy {
  schedule_id = '';
  schedule: IHttpSchedule = { sessions: [] as IHttpSession[] } as IHttpSchedule;
  subjects: string = '';
  is_form_expanded = true;
  is_stat_expanded = true;
  form_data_set: IHttpForm[] = [];
  room_stat: ISessionStat[] = [];
  options = [ETab.Overview, ETab.Monitor];
  current_tab = ETab.Overview;
  ETab = ETab;

  event_list: {
    type: string;
    room: string;
    content: string;
    created_at: number;
  }[] = [];

  examTimeRange = '';
  project: IHttpProject = {} as IHttpProject;
  entry_stat: IHttpEntryStat = {
    total: 0,
    not_enter: 0,
    register: 0,
    login: 0,
    testing: 0,
    finished: 0,
    absent: 0,
  };

  entry_count = { total: 0, test: 0, absent: 0 };

  unsubscribe$ = new Subject();

  rooms: IRoomData[] = [];
  onlineRoomsCount = 0;

  constructor(
    private modalService: ModalService,
    private messageService: MessageService,
    private route: ActivatedRoute,
    private examHttpService: ExamHttpService,
    private projectData: ProjectDataService,
    private scheduleData: ScheduleDataService,
    private formData: FormDataService,
    private timeService: ServerTimeService,
    private viewContainerRef: ViewContainerRef,
    private roomDataService: RoomDataService
  ) {}
  ngOnInit(): void {
    this.route.params
      .pipe(
        tap((params) => {
          this.schedule_id = params['id'];
          this.loadData();
        }),
        switchMap(() => this.timeService.is_time_synced$)
      )
      .subscribe((is_synced) => {
        if (!is_synced) {
          this.modalService.syncTimeModal(this.viewContainerRef, {
            schedule_id: this.schedule_id,
          });
        }
      });
    this.roomDataService.rooms$.subscribe((rooms) => {
      this.rooms = rooms;
      this.onlineRoomsCount = rooms.filter((r) => r.is_online).length;
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(1);
    this.unsubscribe$.complete();
  }

  loadData() {
    this.scheduleData
      .selectSchedule(this.schedule_id)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((schedule) => {
        if (!schedule) {
          return;
        }
        this.schedule = schedule;
        this.examTimeRange = formatDateRange(
          new Date(schedule.start),
          new Date(schedule.end)
        );
        this.subjects = Array.from(new Set(schedule.subjects.map(s => s.name))).join(',');
        this.handleStat(schedule.sessions);
        const room = schedule.sessions.map((s) => ({
          room_name: s.room_name,
          session_id: s.id,
        }));

        if (schedule.events?.length) {
          this.event_list = schedule.events
            .map((e) => ({
              type: +e.type < 500 ? 'breakRule' : 'anomaly',
              room: room.find((r) => r.session_id === e.session_id)
                ?.room_name as string,
              content: e.content,
              created_at: e.created_at,
            }))
            .sort((a, b) => b.created_at - a.created_at);
        }
      });
    this.formData
      .selectScheduleForms(this.schedule_id)
      .pipe(
        tap((forms) => (this.form_data_set = forms)),
        takeUntil(this.unsubscribe$)
      )
      .subscribe();

    this.projectData.formalProject$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((p) => (this.project = p));

    this.scheduleData.scheduleUi$(this.schedule_id)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(ui => {
        this.is_form_expanded = ui.form_expanded;
        this.is_stat_expanded = ui.stat_expanded;
      })
    this.reqFormList();

    timer(0, 10 * 1000)
      .pipe(
        switchMap(() => this.scheduleData.reqSchedule(this.schedule_id)),
        takeUntil(this.unsubscribe$)
      )
      .subscribe();
  }

  handleStat(sessions: IHttpSession[]) {
    const entry_stat = sessions
      .filter((s) => s.entry_status)
      .map((s) => s.entry_status);

    this.entry_stat = entry_stat.reduce(
      (stat, entry) => {
        stat.not_enter += entry.not_enter;
        stat.register += entry.register;
        stat.login += entry.login;
        stat.testing += entry.testing;
        stat.finished += entry.finished;
        stat.absent += entry.absent;
        return stat;
      },
      {
        not_enter: 0,
        register: 0,
        login: 0,
        testing: 0,
        finished: 0,
        absent: 0,
      } as IHttpEntryStat
    );
    const total = Object.values(this.entry_stat).reduce((a, b) => a + b, 0);
    const absent = this.entry_stat.register + this.entry_stat.absent + this.entry_stat.not_enter;
    const test = this.entry_stat.login + this.entry_stat.testing + this.entry_stat.finished;
    this.entry_count = { total, test, absent };
    const room_stat = sessions.reduce(
      (stat, session) => {
        const statusCounts = {
          [SessionStatus.enter]: () => { stat.enter += 1 },
          [SessionStatus.test]: () => { stat.start += 1; stat.enter += 1; },
          [SessionStatus.pause]: () => { stat.start += 1; stat.enter += 1; },
          [SessionStatus.end]: () => { stat.end += 1; stat.start += 1; stat.enter += 1; },
          [SessionStatus.centerUploaded]: () => { stat.result += 1; stat.end += 1; stat.start += 1; stat.enter += 1; },
        };
        (statusCounts as any)[session.status]?.();
        if (session.form_status) { stat.form += 1 }
        if (session.password_status) { stat.password += 1 }
        return stat;
      },
      { total: 0, enter: 0, form: 0, password: 0, start: 0, end: 0, result: 0, backup: 0 } as ISessionStat
    );
    room_stat.total = sessions.length;
    const toPercent = (c:number, t:number) => {
      const p = c / t * 100;
      return p % 1 === 0 ? p : +p.toFixed(2);
    };
    this.room_stat = [{
      total: room_stat.total,
      enter: toPercent(room_stat.enter, room_stat.total),
      form: toPercent(room_stat.form, room_stat.total),
      password: toPercent(room_stat.password, room_stat.total),
      start: toPercent(room_stat.start, room_stat.total),
      end: toPercent(room_stat.end, room_stat.total),
      result: toPercent(room_stat.result, room_stat.total),
    }];
  }

  handleIndexChange(index: any) {
    this.current_tab = index;
  }

  reqFormList() {
    this.formData.reqForms(this.schedule_id).subscribe();
  }

  delForm(id: string) {
    this.modalService.confirm('确认删除该试卷吗？', () => {
      this.formData.reqDelForm(this.schedule_id, id).subscribe();
    });
  }

  publishForm() {
    this.modalService.confirm('确认发布试卷吗？', () => {
      this.formData.reqPublishForm(this.schedule_id).subscribe(() => {
        this.reqFormList();
      });
    });
  }

  publishPassword() {
    this.modalService.confirm('确认发布密码吗？', () => {
      this.formData.reqPublishPassword(this.schedule_id).subscribe(() => {
        this.reqFormList();
      });
    });
  }

  exportExamResult() {
    this.modalService.examResultModal({schedule_id: this.schedule_id}).afterClose.subscribe()
  }

  expandChanged(name: 'form' | 'stat', isExpanded: boolean) {
    const expandUi: {form_expanded?: boolean, stat_expanded?: boolean} = {}
    if (name === 'form') {
      expandUi.form_expanded = isExpanded
    }
    if(name === 'stat') {
      expandUi.stat_expanded = isExpanded
    }
    this.scheduleData.updateScheduleUi(this.schedule_id, expandUi);
  }
}

function formatDateRange(start: Date, end: Date): string {
  const isSameDay =
    start.toISOString().slice(0, 10) === end.toISOString().slice(0, 10);
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  };

  if (isSameDay) {
    const date = start.toLocaleString('zh-CN', options as any);
    const endTime = end.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
    return `${date} - ${endTime}`;
  } else {
    const startDate = start.toLocaleString('zh-CN', options as any);
    const endDate = end.toLocaleString('zh-CN', options as any);
    return `${startDate} - ${endDate}`;
  }
}
