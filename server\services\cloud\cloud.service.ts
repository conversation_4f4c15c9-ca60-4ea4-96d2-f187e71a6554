import serverTime from '@services/serverTime';
import * as cloudApi from './cloud.api';
import { logService } from '..';
import { ElogType } from '@share-types/center.types';

class CloudService {
  is_time_synced = false;

  async getCloudTime() {
    try {
      const time = await cloudApi.cloudTime();
      if (!this.is_time_synced || Math.abs(time - serverTime.now()) >= 2) {
        // 首次同步 或 误差超过2s时才同步
        if (this.is_time_synced) {
          serverTime.sync(time); // 同步时间, 保证jstimestamp的返回时间不回退
        } else {
          serverTime.reset(time); // 以服务器时间为准
        }
        const now = serverTime.date().toLocaleString();
        console.log(`Cloud: cloud time: ${now}`);
        this.is_time_synced = true;
        await logService.createLog(ElogType.TimeSynced, '时间: ' + now);
      }
    } catch (error) {
      console.error('Cloud: sync time error:', error);
    }
  }
}

export const cloudService = new CloudService();
