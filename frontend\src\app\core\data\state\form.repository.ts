import { Injectable } from '@angular/core';
import { createStore } from '@ngneat/elf';
import {
  deleteEntities,
  selectManyByPredicate,
  updateEntities,
  upsertEntities,
  withEntities,
} from '@ngneat/elf-entities';
import { tap } from 'rxjs';
import { EFormStatus, FormHttpService } from '../../http/form.service';
import { scheduleStore } from './schedule.repository';
import { LogHttpService } from '../../http/log.service';
import { ElogType } from '@share-types/center.types';

export interface IFormData {
  form_id: string;
  schedule_id: string;
  name: string;
  form_time: string;
  password_time: string;
  form_publish_time: string;
  password_publish_time: string;
  subject: {
    subject_id: string;
    name: string;
    code: string;
  };
  status: EFormStatus;
}

const formStore = createStore(
  { name: 'forms' },
  withEntities<IFormData, 'form_id'>({ idKey: 'form_id' })
);
@Injectable({ providedIn: 'root' })
export class FormDataService {
  constructor(
    private formHttp: FormHttpService,
    private logHttp: LogHttpService
  ) {}

  reqForms(schedule_id: string) {
    return this.formHttp.getFormList(schedule_id).pipe(
      tap((res) => {
        if (res.status === 'success') {
          formStore.update(upsertEntities(res.data));
        }
      })
    );
  }

  reqDelForm(schedule_id: string, form_id: string) {
    return this.formHttp.deleteForm(schedule_id, form_id).pipe(
      tap((res) => {
        if (res.status === 'success') {
          this.logHttp
            .createLog(ElogType.FormDelete, form_id)
            .subscribe();
          formStore.update(deleteEntities(form_id));
        }
      })
    );
  }

  reqPublishForm(schedule_id: string) {
    return this.formHttp.publishForm(schedule_id).pipe(
      tap((res) => {
        if (res.status === 'success') {
          scheduleStore.update(
            updateEntities(schedule_id, {
              form_published: EFormStatus.PUBLISHED,
            })
          );
          this.logHttp
            .createLog(ElogType.FormPublish, schedule_id)
            .subscribe();
        }
      })
    );
  }

  reqPublishPassword(schedule_id: string) {
    return this.formHttp.publishPassword(schedule_id).pipe(
      tap((res) => {
        if (res.status === 'success') {
          scheduleStore.update(
            updateEntities(schedule_id, {
              password_published: EFormStatus.PUBLISHED,
            })
          );
          this.logHttp
            .createLog(ElogType.PasswordPublish, schedule_id)
            .subscribe();
        }
      })
    );
  }

  selectScheduleForms(schedule_id: string) {
    return formStore.pipe(
      selectManyByPredicate((f) => f.schedule_id === schedule_id)
    );
  }
}
