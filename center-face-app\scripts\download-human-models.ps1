# Human.js Models Download Script (PowerShell)
# 下载 Human.js 人脸检测模型到本地 assets 目录以支持离线使用

param(
    [switch]$Force = $false  # 强制重新下载已存在的文件
)

# 模型文件配置
$models = @(
    @{
        Name = "BlazeFace Model"
        Url = "https://cdn.jsdelivr.net/npm/@vladmandic/human/models/blazeface.json"
        Filename = "blazeface.json"
    },
    @{
        Name = "BlazeFace Weights"
        Url = "https://cdn.jsdelivr.net/npm/@vladmandic/human/models/blazeface.bin"
        Filename = "blazeface.bin"
    }
)

# 目标目录
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$targetDir = Join-Path $scriptDir "..\src\assets\human-models"
$targetDir = Resolve-Path $targetDir -ErrorAction SilentlyContinue

if (-not $targetDir) {
    $targetDir = Join-Path $scriptDir "..\src\assets\human-models"
}

function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

function Ensure-DirectoryExists {
    param([string]$Path)
    
    if (-not (Test-Path $Path)) {
        New-Item -ItemType Directory -Path $Path -Force | Out-Null
        Write-ColorText "Created directory: $Path" "Green"
    }
}

function Download-File {
    param(
        [string]$Url,
        [string]$FilePath,
        [string]$DisplayName
    )
    
    Write-ColorText "Downloading: $DisplayName..." "Yellow"
    
    try {
        $webClient = New-Object System.Net.WebClient
        
        # 添加进度事件处理
        $webClient.add_DownloadProgressChanged({
            param($sender, $e)
            $percent = $e.ProgressPercentage
            Write-Progress -Activity "Downloading $DisplayName" -Status "$percent% Complete" -PercentComplete $percent
        })
        
        $webClient.DownloadFile($Url, $FilePath)
        $webClient.Dispose()
        
        Write-Progress -Activity "Downloading $DisplayName" -Completed
        
        $fileSize = (Get-Item $FilePath).Length / 1MB
        Write-ColorText "  ✓ Downloaded: $DisplayName ($($fileSize.ToString('F2')) MB)" "Green"
        return $true
    }
    catch {
        Write-ColorText "  ❌ Failed to download $DisplayName`: $($_.Exception.Message)" "Red"
        if (Test-Path $FilePath) {
            Remove-Item $FilePath -Force
        }
        return $false
    }
}

function Get-FileSizeMB {
    param([string]$FilePath)
    
    if (Test-Path $FilePath) {
        $size = (Get-Item $FilePath).Length / 1MB
        return $size.ToString('F2')
    }
    return "N/A"
}

# 主函数
function Main {
    Write-ColorText "🤖 Human.js Models Download Script" "Cyan"
    Write-ColorText "=====================================" "Cyan"
    Write-Host ""
    
    # 确保目标目录存在
    Ensure-DirectoryExists $targetDir
    
    $totalDownloaded = 0
    $totalSkipped = 0
    $totalFailed = 0
    
    foreach ($model in $models) {
        $filePath = Join-Path $targetDir $model.Filename
        
        if ((Test-Path $filePath) -and -not $Force) {
            $size = Get-FileSizeMB $filePath
            Write-ColorText "⏭️  Skipped: $($model.Filename) (already exists, $size MB)" "Gray"
            $totalSkipped++
            continue
        }
        
        $success = Download-File -Url $model.Url -FilePath $filePath -DisplayName $model.Filename
        
        if ($success) {
            $totalDownloaded++
        } else {
            $totalFailed++
        }
    }
    
    Write-Host ""
    Write-ColorText "=====================================" "Cyan"
    Write-ColorText "📊 Download Summary:" "Cyan"
    Write-ColorText "   Downloaded: $totalDownloaded files" "White"
    Write-ColorText "   Skipped: $totalSkipped files" "White"
    if ($totalFailed -gt 0) {
        Write-ColorText "   Failed: $totalFailed files" "Red"
    }
    Write-ColorText "   Target directory: $targetDir" "White"
    
    if ($totalFailed -eq 0) {
        if ($totalDownloaded -gt 0) {
            Write-ColorText "`n✅ Models downloaded successfully!" "Green"
            Write-ColorText "   Human.js engine now supports offline mode." "Green"
        } else {
            Write-ColorText "`n✅ All models are already available." "Green"
        }
    } else {
        Write-ColorText "`n❌ Some downloads failed. Please check your internet connection and try again." "Red"
        exit 1
    }
    
    # 生成模型清单文件
    $manifest = @{
        version = "1.0.0"
        generatedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        models = @()
    }
    
    foreach ($model in $models) {
        $filePath = Join-Path $targetDir $model.Filename
        $manifest.models += @{
            name = $model.Name
            filename = $model.Filename
            path = "/assets/human-models/$($model.Filename)"
            exists = Test-Path $filePath
            size = if (Test-Path $filePath) { "$(Get-FileSizeMB $filePath) MB" } else { "N/A" }
        }
    }
    
    $manifestPath = Join-Path $targetDir "manifest.json"
    $manifest | ConvertTo-Json -Depth 3 | Set-Content $manifestPath -Encoding UTF8
    Write-ColorText "`n📋 Generated manifest: $manifestPath" "Cyan"
}

# 运行脚本
try {
    Main
}
catch {
    Write-ColorText "`n❌ Script failed: $($_.Exception.Message)" "Red"
    exit 1
}
