import { dataAccess } from '@data/data-access';

export interface RoomEventData {
  id: string;
  session_id: string;
  type: string;
  content: string;
  created_at: number;
  is_deleted: 0 | 1;
  data_type: 'log' | 'event';
  version: number;
}
export async function handleRoomEvent(
  room_sn: string,
  session_id: string,
  event_data: RoomEventData[],
) {
  const events = event_data.map((e) => ({
    id: e.id,
    session_id: e.session_id,
    content: e.content,
    created_at: e.created_at,
    is_deleted: (e.is_deleted ? 1 : 0) as 0 | 1,
    type: e.data_type === 'log' ? 1000 + e.type : e.type,
    version: e.version,
  }));

  await dataAccess.event.addOrUpdateEvents(events);
}
