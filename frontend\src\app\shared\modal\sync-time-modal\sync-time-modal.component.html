<div>
  <div class="header">校时申请</div>

  <div class="body">
    @if(!isQR) {
      <div class="tip">1、请确认考点服务器识别到的本机时间为当前北京时间</div>
      <div class="tip">2、若确认过程中对本机时间进行了调整，考点服务器将重新识别本机时间</div>
      <div class="time-box">
        <span>本机时间：</span
        ><span class="time">{{ time | customDate: "date" }}</span>
      </div>
    } @else {
      <div class="tip">请用手机扫描以下二维码发送校时申请，并获取授权码</div>
      <div class="qr-box">
        <!-- <nz-qrcode [nzSize]="200" [nzValue]="qr_code" [nzStatus]="qr_status"></nz-qrcode> -->
        <img [src]="qrCode" alt="">
      </div>
      <div class="auth-code">
        <span class="row">请输入授权码</span>
        <input
          nz-input
          placeholder="授权码"
          [(ngModel)]="authCode"
          type="number"
        />
      </div>
    }
  </div>

  <div class="footer">
    <div class="buttons">
      <button nz-button nzType="default" (click)="closeModal()">取消</button>
      <button nz-button nzType="primary" [disabled]="isQR && !authCode" (click)="confirm()">确认</button>
    </div>
  </div>
</div>
