import { Injectable } from "@angular/core";
import { LocalStorageService } from "./local-storage.service";
import { ModalService } from "./modal.service";


const mediaError = {
  // firefox error
  'AbortError': '中止错误',
  'NotAllowedError': '拒绝错误',
  'NotFoundError': '无法找到媒体设备',
  'NotReadableError': '媒体设备被占用',
  'SecurityError': '安全错误',
  'OverConstrainedError': '无法满足要求错误',
  // chrome error
  'TypeError': '类型错误',
  'ConstraintNotSatisfiedError': '无法满足要求错误',
  'DevicesNotFoundError': '无法找到媒体设备',
  'TrackStartError': '媒体设备被占用'
}

@Injectable({
  providedIn: "root",
})
export class MediaService {
  private isCameraOn = false;
  private mediaStream!: MediaStream | null;
  private count = 0;
  private config: { width: number; height: number } = { width: 640, height: 480 };
  private timer: any;

  constructor(
    private localStorage: LocalStorageService,
  ) {}

  set mediaConfig(config: any) {
    this.localStorage.set("media-config", config);
  }

  get mediaConfig() {
    return this.mediaStream ? this.mediaStream.getVideoTracks()[0].getSettings() : null;
  }

  public async getMediaStream(width?: number, height?: number): Promise<MediaStream | null> {
    if (!this.mediaStream || !this.isVideoMediaVaild()) {
      await this.openCamera(width, height);
    } else if (this.config.width !== width || this.config.height !== height) {
      await this.closeCamera();
      await this.openCamera(width, height);
    }
    if (!this.mediaStream) {
      // 如果没有媒体流重新再试一次默认640*480
      console.warn("warn: retry get media stream 640*480");
      await this.openCamera();
    }
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.count++;
    return this.mediaStream;
  }

  public closeMediaStream(): void {
    if (this.count !== 0) {
      this.count--;
    }
    if (this.count === 0) {
      this.timer = setTimeout(() => this.closeCamera(), 1000 * 60 * 5);
    }
  }

  public async refreshCamera(width?: number, height?: number) {
    this.closeCamera();
    await this.openCamera(width, height);
    return this.mediaStream;
  }

  private async openCamera(width?: number, height?: number): Promise<MediaStream | void> {
    const deviceId = await this.checkDeviceId();
    console.log('deviceId:', deviceId);
    const options = {
      video: {
        deviceId: deviceId ? { exact: deviceId } : undefined,
        width: { ideal: width || 640, max: width || 640 },
        height: { ideal: height || 480, max: height || 480 },
        facingMode: 'user',
      },
    };
    console.log("open webcam:", JSON.stringify(options, null, 2));
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      return navigator.mediaDevices
        .getUserMedia({ ...options })
        .then((mediaStream) => {
          this.isCameraOn = true;
          this.config = { width: width || 640, height: height || 480 };
          this.mediaStream = mediaStream;
          return mediaStream;
        })
        .catch((error: DOMException) => {
          console.error("getUserMedia error:", error.name, error.message);
          const errMsg = mediaError[error.name as keyof typeof mediaError] || error.message;
          throw new Error(errMsg);
        });
    } else {
      throw new Error('getUserMedia not supported');
    }
  }

  closeCamera(): void {
    if (this.isCameraOn) {
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach((stream) => stream.stop());
        this.mediaStream = null;
      }
      this.isCameraOn = false;
    }
  }

  // 获取摄像头信息列表
  async getVideoDevicesInfo() {
    if (navigator.mediaDevices) {
      const mediaDevicesInfo = await navigator.mediaDevices.enumerateDevices();
      return mediaDevicesInfo.filter((d) => d.kind === "videoinput");
    } else {
      return null;
    }
  }

  async checkDeviceId() {
    const videoDevice = await this.getVideoDevicesInfo();
    const media_config = this.localStorage.get("media-config");
    let deviceId: string | null = null;
    if (videoDevice && videoDevice.length > 1 && media_config) {
      const valid_device = videoDevice.find((d) => d.deviceId === media_config.deviceId);
      if (valid_device) {
        deviceId = valid_device.deviceId;
      } else {
        this.localStorage.set("media-config", null);
      }
    }
    return deviceId;
  }

  setDeviceConfig(device: MediaDeviceInfo) {
    this.localStorage.set("media-config", device);
  }

  // 检查视频流是否可用
  private isVideoMediaVaild(): boolean {
    return !!this.mediaStream && this.mediaStream.getVideoTracks().some((video) => video.readyState === "live");
  }
}
