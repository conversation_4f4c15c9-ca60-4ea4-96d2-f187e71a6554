import {
  Sequelize,
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';

export default function (sequelize: Sequelize) {
  const Session = sequelize.define<SessionModelFields>(
    'session',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      schedule_id: {
        type: DataTypes.UUID,
      },
      start: {
        type: DataTypes.STRING,
      },
      end: {
        type: DataTypes.STRING,
      },
      name: {
        type: DataTypes.STRING,
      },
      room_sn: {
        type: DataTypes.STRING,
      },
      room_name: {
        type: DataTypes.STRING,
      },
      status: {
        type: DataTypes.INTEGER,
      },
      form_status: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      password_status: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      test: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      version: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      entry_status: {
        type: DataTypes.STRING,
        set(val) {
          const s = typeof val === 'string' ? val : JSON.stringify(val);
          this.setDataValue('entry_status', s);
        },
        get() {
          const s = this.getDataValue('entry_status');
          return typeof s === 'string' ? (JSON.parse(s) as unknown) : {};
        },
      },
      created_at: {
        type: DataTypes.INTEGER,
      },
    },
    {
      timestamps: false,
      underscored: true,
      freezeTableName: true,
      hooks: {
        beforeUpdate: (session: SessionModelFields, opt) => {},
        beforeCreate: (session: SessionModelFields, opt) => {},
      },
    },
  );
  return Session;
}

export interface SessionModelFields
  extends Model<
    InferAttributes<SessionModelFields>,
    InferCreationAttributes<SessionModelFields>
  > {
  id: string;
  name: string;
  schedule_id: string;
  start: string;
  end: string;
  room_sn: string;
  room_name: string;
  test: 0 | 1;
  status: number;
  form_status: 0 | 1;
  password_status: 0 | 1;
  entry_status: CreationOptional<string>;
  created_at: CreationOptional<number>;
  version: number;
}
