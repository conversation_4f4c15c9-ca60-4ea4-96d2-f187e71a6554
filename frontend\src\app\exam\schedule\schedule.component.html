<div class="container">
    <div class="test-card card" *ngIf="test_project">
        <div class="test-block" >
            <!-- <span nz-icon nzType="solution" nzTheme="outline"></span> -->
            <img src="assets/images/test-project.svg" alt="">
            <span>试考</span>
        </div>
        <div class="content">
            <div class="left">
                <div class="title">{{test_project.name }}</div>
                <div class="sub-title">考试日期：{{test_project.start | customDate: 'yyyyMMdd'}} - {{test_project.end | customDate: 'yyyyMMdd'}}</div>
            </div>
            <div class="right" *ngIf="schedule_test.schedule_id">
                @if (schedule_test.form_published) {
                  <div class="published">
                    <span nz-icon nzType="check" nzTheme="outline"></span>
                    <span>试卷已发布</span>
                  </div>
                }
                @if (schedule_test.password_published) {
                  <div class="published">
                    <span nz-icon nzType="check" nzTheme="outline"></span>
                    <span>密码已发布</span>
                  </div>
                }
                <button nz-button nzType="primary" (click)="goScheduleDetail(schedule_test.schedule_id)">试考</button>
            </div>
        </div>
    </div>
    <div class="form-card card" *ngIf="is_form_expanded">
        <app-upload-button [scheduleId]="schedule_test.schedule_id" (uploadSuccess)="getScheduleFormsList()"></app-upload-button>
        <nz-table #basicTable [nzData]="form_data_set" [nzFrontPagination]="false"  nzSize='small'>
            <thead>
                <tr>
                    <th >科目</th>
                    <th >试卷名称</th>
                    <th >试卷ID</th>
                    <th nzWidth="160px">试卷上传时间</th>
                    <th nzWidth="160px">密码上传时间</th>
                    <th nzWidth="160px">试卷发布时间</th>
                    <th nzWidth="160px">密码发布时间</th>
                    <th nzWidth="50px" >操作</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let form of basicTable.data">
                    <td>{{form.subject?.name || "无"}}</td>
                    <td>{{form.name}}</td>
                    <td>{{form.form_id}}</td>
                    <td>{{form.form_time | customDate: 'date'}}</td>
                    <td>{{form.password_time | customDate: 'date'}}</td>
                    <td>{{form.form_publish_time | customDate: 'date'}}</td>
                    <td>{{form.password_publish_time | customDate: 'date'}}</td>
                    <td>
                        <button nz-button nzType="text" nzDanger (click)="delForm(form.form_id)">删除</button>
                    </td>
                </tr>
            </tbody>
        </nz-table>
    </div>
    <div class="formal-card card" *ngIf="formal_project">
        <div class="formal-block">
            <!-- <span nz-icon nzType="solution" nzTheme="outline"></span> -->
            <img src="assets/images/normal-project.svg" alt="">
            <span>正式考试</span>
        </div>
        <div class="content">
            <div class="title">{{formal_project.name}}</div>
            <div class="sub-title">考试日期：{{formal_project.start | customDate: 'yyyyMMdd'}} - {{formal_project.end | customDate: 'yyyyMMdd'}}</div>
        </div>
       
    </div>
    <div class="schedule-list">
        @for (schedule of schedule_data_set; track $index) {
            <div class="schedule-card" [ngClass]="{
                'not-start': schedule.schedule_status === 'not-start',
                'completed': schedule.schedule_status === 'completed',
                'active': schedule.schedule_status === 'active',
            }" (click)="goScheduleDetail(schedule.id)" >
                <div class="schedule-info" >
                    <span class="status">{{schedule.schedule_status | statusTrans: 'scheduleStatus'}}</span>
                    <span class="info">{{schedule.date}}</span>
                    <span class="subject" [title]="schedule.subject_name">考试科目：{{schedule.subject_name}}</span>
                </div>
                <div class="schedule-stat">
                    <span class="room">考场总数：{{schedule.room_total}}</span>
                    <span class="entry">考生总数：{{schedule.entry_total}}</span>
                </div>
            </div>
        }
    </div>
</div>