import { Injectable, signal, computed } from '@angular/core';
import { CloudHttpService } from '../http/cloud.http';
import { ServerTimeService } from './server-time.service';
import { Observable, map } from 'rxjs';

export interface Schedule {
  id: string;
  start: string;
  end: string;
  subjects: string[];
  attendanceRate: string;
  attended: number;
  total: number;
  status: ScheduleStatus;
}

export enum ScheduleStatus {
  NotStarted = 'not-start',
  Active = 'active',
  Completed = 'completed',
}

@Injectable({
  providedIn: 'root'
})
export class ScheduleStateService {
  
  // 全局schedules状态
  private _schedules = signal<Schedule[]>([]);
  
  // 当前选中的schedule ID
  private _currentScheduleId = signal<string>('');
  
  // 只读的schedules访问器
  readonly schedules = this._schedules.asReadonly();
  
  // 只读的currentScheduleId访问器
  readonly currentScheduleId = this._currentScheduleId.asReadonly();
  
  // 计算当前schedule
  readonly currentSchedule = computed(() => {
    const scheduleId = this._currentScheduleId();
    const schedules = this._schedules();
    return schedules.find(schedule => schedule.id === scheduleId) || null;
  });
  
  constructor(
    private cloudHttp: CloudHttpService,
    private serverTime: ServerTimeService
  ) {}
  
  /**
   * 设置schedules数据
   */
  setSchedules(schedules: Schedule[]): void {
    this._schedules.set(schedules);
  }
  
  /**
   * 设置当前schedule ID
   */
  setCurrentScheduleId(scheduleId: string): void {
    this._currentScheduleId.set(scheduleId);
  }
  
  /**
   * 根据ID获取schedule
   */
  getScheduleById(scheduleId: string): Schedule | undefined {
    return this._schedules().find(schedule => schedule.id === scheduleId);
  }
  
  /**
   * 从Cloud获取schedule数据
   */
  getScheduleFromCloud(scheduleId: string): Observable<Schedule | null> {
    return this.cloudHttp.getScheduleList().pipe(
      map(res => {
        const schedules = res.data.map((s) => {
          const attendanceRate = s.total_count > 0 ? (s.signin_count / s.total_count) * 100 : 0;
          return {
            id: s.id,
            start: s.start,
            end: s.end,
            attendanceRate: Number.isInteger(attendanceRate) 
            ? `${attendanceRate}` 
            : `${attendanceRate.toFixed(2)}`,
            attended: s.signin_count,
            total: s.total_count,
            late_limit: s.late_limit,
            subjects: s.subjects.map(s => s.name),
            status: this.getStatus(s),
          }
        });
        
        // 更新全局状态
        this.setSchedules(schedules);
        
        return schedules.find(schedule => schedule.id === scheduleId) || null;
      })
    );
  }
  
  /**
   * 获取schedule状态
   */
  private getStatus(schedule: any): ScheduleStatus {
    const now = this.serverTime.getServerTimeValue();
    const isDateExpired = new Date(now) > new Date(schedule.end);
    const isDateStarted = new Date(now) > new Date(schedule.start);
    if (isDateStarted && !isDateExpired) {
      return ScheduleStatus.Active;
    }
    if (isDateExpired) {
      return ScheduleStatus.Completed;
    }
    return ScheduleStatus.NotStarted;
  }
  
  /**
   * 清空状态
   */
  clear(): void {
    this._schedules.set([]);
    this._currentScheduleId.set('');
  }
} 