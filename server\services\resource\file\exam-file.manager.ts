import { guid } from '@server/utils/utils';
import { FileManager, IFile, IManagerFile, ITime } from './file.manager';
import path from 'node:path';
import fs from 'node:fs';
import { fileMd5 } from '@server/utils/filemd5';
interface ExamFileData {
  schedule_id: string;
  room_sn: string;
  session_id: string;
  md5?: string;
  type: ExamFileType;
}

type ExamFileType = 'exam-result' | 'room-package';

class ExamFile implements IFile {
  id = guid();

  _path: string;
  deleted: boolean;
  created_at: number;

  name: string;
  data: ExamFileData;

  fileManager: IManagerFile;
  constructor(name: string, data: ExamFileData, fileManager: IManagerFile) {
    this.name = name;
    this.data = data;

    this.fileManager = fileManager;
    this.created_at = this.fileManager.serverTime.now();

    return new Proxy(this, {
      set(target, p, value) {
        target[p] = value;
        fileManager.markWrite();
        return true;
      },
    });
  }

  get path() {
    const p =
      this.data.type === 'exam-result'
        ? path.join('results', this.name)
        : this.name;
    return path.join(this.fileManager.file_base_dir, this.data.schedule_id, p);
  }

  get size() {
    try {
      return fs.statSync(this.path).size;
    } catch (error) {
      console.error('Error while getting file size:', this.name, error);
      return -1;
    }
  }

  isExist() {
    const is_exist = fs.existsSync(this.path);
    return is_exist;
  }

  async checkIntegrity() {
    if (!this.data?.md5 || !this.isExist()) {
      console.log('File is corrupted', this.name, this.data.md5);
      return false;
    }
    try {
      const fileMD5 = await fileMd5(this.path);
      if (fileMD5 === this.data.md5) {
        return true;
      } else {
        console.log('File is corrupted: ', this.name, this.data.md5, fileMD5);
        return false;
      }
    } catch (error) {
      console.error('Error while checking file integrity:', this.name, error);
      return false;
    }
  }

  toJSON() {
    return {
      id: this.id,
      _path: this._path,
      deleted: this.deleted,
      created_at: this.created_at,
      name: this.name,
      data: this.data,
    };
  }

  static load(fileData: ExamFile, fileManager: IManagerFile) {
    const file = new ExamFile(fileData.name, fileData.data, fileManager);
    Object.assign(file, fileData);
    return file;
  }
}

export class ExamFileManager extends FileManager<ExamFile> {
  serverTime: ITime;
  constructor(basePath: string, serverTime: ITime) {
    super(basePath);
    this.serverTime = serverTime;
    this.init().catch((err) => console.error('ExamFile: init error:\n' + err));
  }

  async init() {
    const config = await this.readConfig();
    if (config) {
      console.log('ExamFile: read package file config');
      this.config = config;
    } else {
      console.log('ExamFile: no package file config');
      this.config = { update_at: 0, file_list: [] };
    }
    console.log('ExamFile: load package file #' + this.config.file_list.length);
    for (const fileData of this.config.file_list) {
      this.files.push(ExamFile.load(fileData, this));
    }
  }

  create(file_name: string, data: ExamFileData) {
    const file = this.getFile(file_name, data.schedule_id);
    if (file) {
      console.warn('ExamFile: file already exist', file_name, file.created_at);
      file.created_at = this.serverTime.now();
      file.data = data;
      return file;
    }
    const examFile = new ExamFile(file_name, data, this);
    this.pushFile(examFile);
    return examFile;
  }

  getFile(name: string, schedule_id: string) {
    return this.files.find(
      (file) => file.name === name && file.data.schedule_id === schedule_id,
    );
  }

  getFileList(query: Partial<ExamFileData>) {
    return this.files
      .filter((file) => {
        return Object.entries(query).every(
          ([key, value]) =>
            !value || file.data[key as keyof ExamFileData] === value,
        );
      })
      .sort((a, b) => b.created_at - a.created_at);
  }

  queryFile(query: Partial<ExamFileData>) {
    return this.getFileList(query)[0];
  }
}
