import { LOG_PATH } from "./config";

import { log, initLog } from "./utils/logger";

initLog("center", LOG_PATH, 8 * 1024 * 1024, 2);

const DEBUG_LEVEL: number = global.parseInt(process.env.JOY_DEBUG || '') || 0;

if (DEBUG_LEVEL) {
  if (!console.debug) {
    console.debug = function (...args: any[]) {
      log.log(...args);
    };
  }
} else {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  console.debug = function () {};
}

export { log };
