/* eslint-disable @typescript-eslint/no-unsafe-return */
import * as path from 'path';
import * as fs from 'fs';
import nativeRequire from './native-require';
import { MZip, OSExt } from '@share-types/addon.types';

const isElectron = process.versions['electron'];
global['JOYTEST_ON'] = 1;

function getAddonBasePath(): string {
  if (process.platform === 'win32') {
    return 'addon/' + (process.arch === 'ia32' ? 'win32' : 'win64');
  }
  return 'addon/' + process.platform;
}

export function getAddon(name: 'mzip'): MZip;
export function getAddon(name: 'osext'): OSExt;
export function getAddon(name: string) {
  try {
    if (!name.endsWith('.node')) {
      name += '.node';
    }

    let rp = '';
    if (isElectron) {
      if (joyshell) {
        return joyshell.GetAddon(name);
      }
      rp = process['resourcesPath'];
      if (fs.existsSync(path.join(rp, name))) {
        return nativeRequire(path.join(rp, name));
      }
      rp = path.dirname(process.execPath);
    } else {
      rp = path.resolve(__dirname, '../../app/' + getAddonBasePath());
    }

    return nativeRequire(path.join(rp, name));
  } catch (error: any) {
    if (error.code === 'MODULE_NOT_FOUND') {
      throw new Error(`Module ${name} not found`);
    }
    throw error;
  }
}
