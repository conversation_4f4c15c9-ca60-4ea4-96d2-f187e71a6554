import { Injectable } from '@angular/core';
import { BehaviorSubject, interval, Observable, of } from 'rxjs';
import { exhaustMap, filter, switchMap, takeUntil, distinctUntilChanged } from 'rxjs/operators';
import { 
  FaceDetectBaseService, 
  VideoDetectControl, 
  VideoProcessingOptions, 
  FaceDetectResult, 
  FaceDetectResultCode, 
  FaceDetectOptions,
  FaceDetectEngine
} from './face-detect-base.service';
import { NetworkStatusService } from '../network-status.service';
import { HumanModelManagerService } from './human-model-manager.service';

// Human 库类型声明
declare const Human: any;

export interface HumanInitializationResult {
  success: boolean;
  mode: 'offline' | 'online' | 'failed';
  error?: string;
}

@Injectable({
  providedIn: 'root',
})
export class HumanFaceDetectService extends FaceDetectBaseService {
  private detectControl$ = new BehaviorSubject<'stop' | 'start'>('stop');
  private stop$ = this.detectControl$.pipe(filter((control) => control === 'stop'));
  private videoProcess$ = this.detectControl$.pipe(
    distinctUntilChanged(),
    switchMap((control) =>
      control === 'start'
        ? interval(300).pipe(
            exhaustMap(() => of(this.detectFace())),
            takeUntil(this.stop$)
          )
        : of(false)
    ),
  ) as Observable<FaceDetectResult | false>;

  private faceOptions?: FaceDetectOptions;
  private video!: HTMLVideoElement;
  private videoHeight!: number;
  private videoWidth!: number;
  private canvasCtx!: CanvasRenderingContext2D;
  private detectFrame: { x: number, y: number, width: number, height: number } = { x: 0, y: 0, width: 0, height: 0 };
  
  private human: any;
  private isInitialized = false;
  private initializationMode: 'offline' | 'online' | 'failed' = 'failed';

  constructor(
    private networkStatus: NetworkStatusService,
    private modelManager: HumanModelManagerService
  ) {
    super();
    this.initializeHuman();
  }

  private async initializeHuman(): Promise<HumanInitializationResult> {
    try {
      // 动态导入 Human 库
      const { Human } = await import('@vladmandic/human');

      // 尝试获取最佳的模型路径（本地优先）
      let modelBasePath: string;
      let mode: 'offline' | 'online';

      try {
        modelBasePath = await this.modelManager.getModelBasePath();
        mode = modelBasePath.startsWith('/assets') ? 'offline' : 'online';
      } catch (error) {
        console.warn('No model source available, trying fallback');
        // 降级到 MediaPipe 引擎的逻辑将在工厂服务中处理
        throw new Error('No model source available for Human.js');
      }

      // 配置 Human
      const config = {
        modelBasePath,
        face: {
          enabled: true,
          detector: { enabled: true, rotation: false },
          mesh: { enabled: false },
          iris: { enabled: false },
          description: { enabled: false },
          emotion: { enabled: false },
          antispoof: { enabled: false },
          liveness: { enabled: false }
        },
        body: { enabled: false },
        hand: { enabled: false },
        object: { enabled: false },
        segmentation: { enabled: false },
        filter: { enabled: false },
        debug: false,
        async: true,
        warmup: 'face' as any
      };

      console.log(`Initializing Human.js in ${mode} mode with models from: ${modelBasePath}`);

      this.human = new Human(config);
      await this.human.load();
      await this.human.warmup();

      this.isInitialized = true;
      this.initializationMode = mode;

      console.log(`Human face detection engine initialized successfully in ${mode} mode`);
      return { success: true, mode };

    } catch (error) {
      console.error('Failed to initialize Human face detection:', error);
      this.isInitialized = false;
      this.initializationMode = 'failed';
      return {
        success: false,
        mode: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  getEngineName(): FaceDetectEngine {
    return 'human';
  }

  isReady(): boolean {
    return this.isInitialized && this.human;
  }

  async initialize(): Promise<HumanInitializationResult> {
    if (!this.isInitialized) {
      return await this.initializeHuman();
    }
    return {
      success: true,
      mode: this.initializationMode === 'failed' ? 'online' : this.initializationMode
    };
  }

  /**
   * 获取当前初始化模式
   */
  getInitializationMode(): 'offline' | 'online' | 'failed' {
    return this.initializationMode;
  }

  /**
   * 检查是否支持离线模式
   */
  async supportsOfflineMode(): Promise<boolean> {
    const localCheck = await this.modelManager.checkLocalModelsAvailability();
    return localCheck.available;
  }

  /**
   * 获取引擎状态信息
   */
  getEngineStatus(): {
    ready: boolean;
    mode: 'offline' | 'online' | 'failed';
    supportsOffline: boolean;
  } {
    return {
      ready: this.isReady(),
      mode: this.initializationMode,
      supportsOffline: false // 将在异步检查后更新
    };
  }

  destroy(): void {
    if (this.human) {
      // Human 库的清理
      this.human = null;
    }
    this.isInitialized = false;
  }

  setVideoDetectConfig(options: VideoProcessingOptions): VideoDetectControl {
    const { video, detectFrame, videoWidth, videoHeight, faceOptions } = options;
    this.faceOptions = faceOptions;
    this.video = video;
    
    const canvas = document.createElement('canvas');
    this.videoWidth = videoWidth;
    this.videoHeight = videoHeight;
    canvas.width = this.videoWidth;
    canvas.height = this.videoHeight;
    this.canvasCtx = canvas.getContext('2d', { willReadFrequently: true })!;

    this.detectFrame = detectFrame;

    return {
      stop: () => {
        this.detectControl$.next('stop');
      },
      start: () => {
        this.detectControl$.next('start');
      },
      faceDetect$: this.videoProcess$
    };
  }

  setFaceOptions(options: FaceDetectOptions): void {
    this.faceOptions = options;
  }

  private async detectFace(): Promise<FaceDetectResult> {
    const { faceInCenter, faceSize, minFaceNum } = this.faceOptions || {};
    const result: FaceDetectResult = {
      code: FaceDetectResultCode.SUCCESS,
      image: null,
      faceImage: null,
    };

    if (!this.canvasCtx || !this.isReady()) {
      result.code = FaceDetectResultCode.NO_FACE;
      return result;
    }

    try {
      // 绘制视频帧到画布
      this.canvasCtx.drawImage(this.video, 0, 0, this.videoWidth, this.videoHeight);
      
      // 获取检测区域的图像数据
      const { x, y, width, height } = this.detectFrame;
      const imageData = this.canvasCtx.getImageData(x, y, width, height);
      
      // 创建临时画布用于 Human 检测
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = width;
      tempCanvas.height = height;
      const tempCtx = tempCanvas.getContext('2d')!;
      tempCtx.putImageData(imageData, 0, 0);

      // 使用 Human 进行人脸检测
      const predictions = await this.human.detect(tempCanvas);
      const faces = predictions.face || [];

      // 检查人脸数量
      if (faces.length === 0) {
        result.code = FaceDetectResultCode.NO_FACE;
        return result;
      }

      if (minFaceNum && faces.length > minFaceNum) {
        result.code = FaceDetectResultCode.MULTI_FACE;
        return result;
      }

      // 获取第一个检测到的人脸
      const face = faces[0];
      const faceBox = face.box || face.boxRaw;
      
      if (!faceBox) {
        result.code = FaceDetectResultCode.NO_FACE;
        return result;
      }

      // 转换坐标系（Human 使用相对坐标）
      const faceRect = {
        x: Math.round(faceBox[0] * width),
        y: Math.round(faceBox[1] * height),
        width: Math.round(faceBox[2] * width),
        height: Math.round(faceBox[3] * height)
      };

      // 检查人脸位置是否居中
      if (faceInCenter && !this.isFaceCentered(faceRect, width, height)) {
        result.code = FaceDetectResultCode.FACE_NOT_IN_CENTER;
        return result;
      }

      // 检查人脸大小是否合适
      if (faceSize && !this.isFaceSizeAppropriate(faceRect, width, height)) {
        result.code = FaceDetectResultCode.FACE_TOO_SMALL;
        return result;
      }

      // 生成图像数据
      result.image = this.imageDataToBase64(imageData);
      
      // 截取人脸图像
      const faceImageData = this.canvasCtx.getImageData(
        faceRect.x + x, 
        faceRect.y + y, 
        faceRect.width, 
        faceRect.height
      );
      result.faceImage = this.imageDataToBase64(faceImageData);

      return result;
    } catch (error) {
      console.error('Human face detection error:', error);
      result.code = FaceDetectResultCode.NO_FACE;
      return result;
    }
  }

  private imageDataToBase64(imageData: ImageData): string {
    const canvas = document.createElement('canvas');
    canvas.width = imageData.width;
    canvas.height = imageData.height;
    const ctx = canvas.getContext('2d')!;
    ctx.putImageData(imageData, 0, 0);
    return canvas.toDataURL();
  }

  private isFaceCentered(faceRect: any, imageWidth: number, imageHeight: number): boolean {
    const left = faceRect.x;
    const right = imageWidth - (left + faceRect.width);
    const leftRate = left / imageWidth;
    const rightRate = right / imageWidth;
    const leftRightRate = Math.abs(leftRate - rightRate);
    return leftRightRate <= 0.2;
  }

  private isFaceSizeAppropriate(faceRect: any, frameWidth: number, frameHeight: number): boolean {
    const faceArea = faceRect.width * faceRect.height;
    const frameArea = frameWidth * frameHeight;
    const faceRatio = faceArea / frameArea;
    return faceRatio >= 0.15;
  }
}
