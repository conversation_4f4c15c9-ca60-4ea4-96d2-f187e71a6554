<div class="container">
  <nav [class.mobile]="isMobile()">
    <div class="back" (click)="goBack()">
      <span nz-icon nzType="left" nzTheme="outline"></span>
    </div>
    <!-- <div class="title">
      <span>{{ project().name }}</span>
    </div> -->
    <div class="time">
      <span>{{ serverTime() | customDate: 'date' }}</span>
    </div>
  </nav>
  <main #main> 
    <div class="vedio-card">
      <div class="hint">
        <span>{{ tip() }}</span>
        <div class="settings">
          <span nz-icon nzType="setting" nzTheme="outline" nz-dropdown [nzDropdownMenu]="menu" nzTrigger="click" nzPlacement="bottomLeft"></span>
          <nz-dropdown-menu #menu="nzDropdownMenu">
            <ul nz-menu>
              <li nz-submenu nzTitle="输入源" (nzOpenChange)="getVideoDeviceInfo()">
                <ul>
                  <li nz-menu-item *ngFor="let device of videoDevicesInfo" (click)="chooseDevice(device)">
                    {{ device.label }} <nz-badge nzStatus="success" *ngIf="device.deviceId === currentDeviceId()"></nz-badge>
                  </li>
                </ul>
              </li>
              <li nz-submenu nzTitle="分辨率">
                <ul>
                  <li nz-menu-item *ngFor="let device of videoResolutions" (click)="chooseResolution(device.value)">
                    {{ device.label }} <nz-badge nzStatus="success" *ngIf="device.value === currentResolution()"></nz-badge>
                  </li>
                </ul>
              </li>
              <li nz-submenu nzTitle="检测框">
                <ul>
                  <li nz-menu-item *ngFor="let frame of frameOptions" (click)="chooseDetectFrame(frame.value)">
                    {{ frame.label }} <nz-badge nzStatus="success" *ngIf="frame.value === detectFrameSize()"></nz-badge>
                  </li>
                </ul>
              </li>
              <li nz-menu-item (click)="togglePoseDetection()">姿态检测 <nz-badge nzStatus="success" *ngIf="isPoseDetection()"></nz-badge></li>
              <li nz-menu-item (click)="toggleMirror()">镜像 <nz-badge nzStatus="success" *ngIf="isVideoMirror()"></nz-badge></li>
              <li nz-menu-item (click)="toggleFullscreen()">全屏 <nz-badge nzStatus="success" *ngIf="isFullscreen()"></nz-badge></li>
              <!-- <li nz-menu-item (click)="showLogs.set(!showLogs())">调试 <nz-badge nzStatus="success" *ngIf="showLogs()"></nz-badge></li> -->
            </ul>
          </nz-dropdown-menu>
        </div>
      </div>
      <div class="video-container" [style.width]="videoWidth + 'px'" [style.height]="videoHeight + 'px' "> 
        <canvas #face [style.zIndex]="100" style="position: absolute"></canvas>
        <img #detectFrameImg src="/assets/images/signin_frame.png" alt="signin-mask" class="signin-mask" [ngClass]="{'mask-sm': detectFrameSize() === 'small', 'mask-md': detectFrameSize() === 'middle', 'mask-lg': detectFrameSize() === 'large'}">
        <video id="videoTrack" [class.mirror]="isVideoMirror()" #videoPlayer autoplay playsinline muted></video>
      </div>
    </div>
    
    <!-- 超时遮罩层 -->
    <div class="timeout-overlay" *ngIf="isLate()">
      <div class="timeout-message">
        <p><span>签到结</span><span class="no-spacing">束</span></p>
        <p><span>禁止入</span><span class="no-spacing">场</span></p>
      </div>
    </div>
  </main>
</div>

<div class="log-container" *ngIf="showLogs()">
  <div class="log-header">
    <span>调试日志</span>
    <span class="expand-btn" (click)="isDebugExpanded.set(!isDebugExpanded())">{{ isDebugExpanded() ? '收起' : '展开' }}</span>
  </div>
  <div class="log-content" #logContent [ngClass]="{'shrink': !isDebugExpanded()}">
    <div *ngFor="let log of logs" [class]="'log-item ' + log.type">
      {{ log.timestamp | date:'HH:mm:ss' }} - {{ log.message }}
    </div>
  </div>
</div>

@if(signinStatus().show) {
<div class="signin-status" [class.error]="signinStatus().status === 'failed'">
  <div class="status-content">
    @if(signinStatus().info) {
      <span class="info">{{ signinStatus().info?.name }}</span>
    }
    <span>{{ signinStatus().message }}</span>
  </div>
  <div class="status-icon">
    <div class="icon-wrap">
      @if(signinStatus().status === 'success') {
        <span nz-icon nzType="check" nzTheme="outline"></span>
      } @else {
        <span nz-icon nzType="exclamation" nzTheme="outline"></span>
      }
    </div>
  </div>
</div>
}
