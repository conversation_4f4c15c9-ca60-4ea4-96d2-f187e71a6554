import path from 'path';
import { Configuration, IgnorePlugin } from 'webpack';
import {merge} from "webpack-merge"
import { base_config } from './webpack.base';
import copy from 'copy-webpack-plugin';

const buildTargetDir = path.resolve(__dirname, "..", 'build', "app");


export const config: Configuration = merge(base_config,{
  target:"electron-main",
  entry: {
    main: path.resolve(__dirname, "src/main.ts") ,
    "server/init": path.resolve(__dirname, "src/server/init.ts"),
  },
  externals: [
    // Function
    function ({ context, request }, callback) {
      if (/package.json$/.test(request) && !context.includes("node_modules")) {
        return callback(null, "commonjs2 " + request);
      }
      if (/server.js$/.test(request) && !context.includes("node_modules")) {
        return callback(null, "commonjs2 " + request);
      }
      callback();
    },
  ],
  plugins: [
    new IgnorePlugin({
      resourceRegExp: /^\.\/server.html$/,
    }),
    new copy({
      patterns: [
        {
          from: path.resolve(__dirname, "src/server/server.html"),
          to: path.join(buildTargetDir, "server"),
        },
        {
          from: path.resolve(__dirname, "src/assets/"),
          to: path.join(buildTargetDir, "assets"),
        },
      ],
    }),
  ]
});

