import {
  Sequelize,
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';

export default function (sequelize: Sequelize) {
  const Room = sequelize.define<RoomModelFields>(
    'room',
    {
      id: {
        type: DataTypes.STRING,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      sn_code: {
        type: DataTypes.STRING,
        unique: true,
      },
      name: {
        type: DataTypes.STRING,
      },
      token: {
        type: DataTypes.STRING,
      },
      host: {
        type: DataTypes.STRING,
      },
      address: {
        type: DataTypes.STRING,
      },
      app_version: {
        type: DataTypes.STRING,
      },
      created_at: {
        type: DataTypes.INTEGER,
      },
    },
    {
      timestamps: false,
      underscored: true,
      freezeTableName: true,
      hooks: {
        beforeUpdate: (room: RoomModelFields, opt) => {},
        beforeCreate: (room: RoomModelFields, opt) => {},
      },
    },
  );
  return Room;
}

export interface RoomModelFields
  extends Model<
    InferAttributes<RoomModelFields>,
    InferCreationAttributes<RoomModelFields>
  > {
  id: CreationOptional<string>;
  name: string;
  sn_code: string;
  token: CreationOptional<string>;
  host: string;
  address: string;
  app_version: string;
  created_at: CreationOptional<string>;
}
