import serverTime from '@services/serverTime';
import {
  Sequelize,
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
} from 'sequelize';

export default function (sequelize: Sequelize) {
  const Log = sequelize.define<LogModelFields>(
    'log',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      type: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      content: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      created_at: {
        type: DataTypes.INTEGER,
      },
    },
    {
      timestamps: false,
      underscored: true,
      freezeTableName: true,
      hooks: {
        beforeUpdate: (log: LogModelFields, opt) => {},
        beforeCreate: (log: LogModelFields, opt) => {
          if (!log.created_at) {
            log.created_at = serverTime.timestamp();
          }
        },
      },
    },
  );
  return Log;
}

export interface LogModelFields
  extends Model<
    InferAttributes<LogModelFields>,
    InferCreationAttributes<LogModelFields>
  > {
  id: string;
  type: number;
  content: string;
  created_at: number;
}
