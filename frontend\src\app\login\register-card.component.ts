import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  OnDestroy,
  type OnInit,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { CenterHttpService } from '../core/http/center.service';
import { ProjectDataService } from '../core/data';
import { ModalService } from '../core/services/modal.service';
import { Subscription, filter, fromEvent, throttleTime } from 'rxjs';
import { CloudErrorCode } from '@share-types/center.types';

@Component({
    selector: 'app-register-card',
    imports: [CommonModule, FormsModule, NzButtonModule, NzInputModule],
    template: `
    <div class="container">
      <div class="title">悦考考点服务器</div>
      <div class="tip">
        请确认以下考点信息并输入考试代码，完成该考试的考点服务器注册
      </div>
      <div class="name">
        <span class="row">考点名称</span> <span>{{ name }}</span>
      </div>
      <div class="address">
        <span class="row">考点地址</span> <span>{{ address }}</span>
      </div>
      <div class="code">
        <span class="row">验证码</span>
        <input
          class="code-input"
          nz-input
          autofocus
          placeholder="请输入二次验证码"
          [(ngModel)]="code"
          type="number"
        />
      </div>
      <div class="button-wrap">
        <button nz-button nzType="text" (click)="back()">取消</button>
        <button
          nz-button
          nzType="primary"
          [nzLoading]="button_loading"
          (click)="register()"
        >
          注册
        </button>
      </div>
    </div>
  `,
    styles: `
  :host {
  display: block;
  background-color: #fff;
  border-radius: 8px;
}

.container {
  padding: 24px;
  > div {
    margin-bottom: 16px;
  }
}
.title {
  font-size: 18px;
  font-weight: 600;
  width: 126px;

  color: #419cf8;
}
.row {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-right: 16px;
}
.code > input {
  width: 300px;
}
.button-wrap {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  button {
    margin-left: 16px;
  }
}
`
})
export class RegisterCardComponent implements OnInit, AfterViewInit, OnDestroy {
  button_loading = false;
  name = '';
  address = '';
  code = '';

  keyEnter$$: Subscription;
  center$$: Subscription;
  constructor(
    private router: Router,
    private centerHttp: CenterHttpService,
    private projectData: ProjectDataService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.center$$ = this.projectData.center$.subscribe((center) => {
      if (center) {
        this.name = center.center_name;
        this.address = center.center_address;
      }
    });
  }

  ngAfterViewInit(): void {
    const input = <HTMLInputElement>document.querySelector('input.code-input');
    if (input) {
      input.focus();
    }
    this.keyEnter$$ = fromEvent<KeyboardEvent>(input, 'keyup')
      .pipe(
        filter((event) => event.key === 'Enter' && !!this.code),
        throttleTime(1000)
      )
      .subscribe(() => this.register());
  }

  ngOnDestroy(): void {
    this.center$$.unsubscribe();
    this.keyEnter$$.unsubscribe();
  }

  register() {
    this.button_loading = true;
    this.centerHttp
      .register({ code: this.code.toString() })
      .subscribe((res) => {
        if (res.status === 'success') {
          this.projectData.reqCenterStatus().subscribe();
          this.projectData.reqProjects().subscribe();
          this.router.navigate(['/dashboard']);
        } else {
          if (res.error?.data) {
            const { message, code} = res.error.data as any;
            console.error('Login error', code, message);
            const msg = code === CloudErrorCode.InvalidCode ? '验证码错误!' : message;
            this.modalService.error('注册失败', msg);
          } else {
            this.modalService.error('注册失败', res.error.msg);
          }
        }
        this.button_loading = false;
      });
  }
  back() {
    this.router.navigate(['/login']);
  }
}
