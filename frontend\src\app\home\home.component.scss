@use "mixins" as *;
@use "variables" as *;

:host {
  display: block;
  height: 100%;
}

.container {
  padding: 0 24px 24px 24px;
  height: 100%;
  background: #f6f7f9;
}
.sub-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  img {
    vertical-align: sub;
  }
}
.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 24px;
  }
}
.date {
  font-size: 14px;
  color: #999;
  margin-bottom: 24px;
}

.stat-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  .stat {
    width: 49%;
    min-height: 200px;
    background-color: #fff;
    border-radius: 8px;
    padding: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    cursor: pointer;
  }
  .stat-box {
    display: flex;
    justify-content: space-between;
    .stat-item {
      display: flex;
      flex-direction: column;
      min-width: 100px;
      .stat-data {
        font-size: 36px;
        font-weight: 600;
        color: #333;
      }
      .stat-name {
        font-size: 14px;
        color: #999;
      }
    }
  }
}
.exam-list {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  .exam-nav {
    @include flex-box(row, space-between);
    a {
      font-size: $font-size-body;
    }
  }
  .tag {
    border-radius: 8px;
    color: #fff;
    padding: 4px 8px;
    font-size: 14px;
    margin: 0 4px;
  }
  .color-grey {
    background: #7997a8;
  }
  .color-yellow {
    background: #fb9a0e;
  }
  .color-blue {
    background: #419cf8;
  }
  .list-item {
    @include flex-box(row, space-between);
    width: 100%;
    .content {
      flex: 1;
      @include flex-box(row, flex-start);
      span {
        margin-left: 5px;
      }
      .subject {
        flex: 1;
        @include text-overflow-ellipsis();
      }
    }
    .room-status {
      min-width: fit-content;
      .total-room {
        display: inline-block;
        margin-right: 5px;
        min-width: 100px;
      }
      .online-room {
        display: inline-block;
        min-width: 70px;
      }
    }
  }
}
