import gulp from 'gulp';
import webpack from 'webpack';
import path from 'node:path';
import fs from 'node:fs';
import { spawn } from 'node:child_process';

const isWin32 = process.platform === "win32";
const isProd = process.env.NODE_ENV === "production";

async function webpackAsync(options) {
  return new Promise(function (resolve, reject) {
    webpack(options, (err, stats) => {
      if (err || stats.hasErrors()) {
        console.error("[webpack]", err);
        console.log("[webpack]", stats.toString({ colors: process.stdout.isTTY, preset: "minimal" }));
        reject(err);
        return;
      }

      if (stats.hasWarnings()) {
        const info = stats.toString({ colors: process.stdout.isTTY, preset: "minimal" });
        console.warn("[webpack]", info);
      }

      resolve(stats);
    });
  });
}


async function buildServer() {
  const {config} = await import('./server/webpack.config.ts') as {config: webpack.Configuration};
  await webpackAsync(config);

  return new Promise(function (resolve, reject) {
    gulp
      .src(["build/server/server.js*"], { base: "build/server" })
      .pipe(gulp.dest("build/app/server"))
      .on("error", reject)
      .on("finish", resolve);
  });
}

function copy_server_assets() {
  return gulp
    .src(["server/tools/**", "server/config/cert/**"], { base: "server" })
    .pipe(gulp.dest("build/app/server"))
}


function genVersion() {
  const version = "1.0.0";
  const regex = /(\d+\.\d+\.).*/;
  const date = new Date();
  const d = date.getDate();
  const m = date.getMonth() + 1; // Month from 0 to 11
  const y = date.getFullYear() - 2018;
  const build_num = (y * 12 + m) * 100 + d;
  const realVersion = version.match(regex)[1] + build_num;
  fs.writeFileSync("VERSION", realVersion, "utf8");
  return realVersion;
}
async function buildApp() {
  const configs = ['./app/webpack.main.ts','./app/webpack.preload.ts' ]
  for (const path of configs) {
    const {config} = await import(path) as {config: webpack.Configuration};
    await webpackAsync(config);
  }

  const file = require('./app/package.json'); // eslint-disable-line
  file.version = genVersion();
  delete file.dependencies;
  delete file.devDependencies;
  delete file.scripts;
  await fs.promises.writeFile('./build/app/package.json', JSON.stringify(file, null, 2), "utf8");
}

async function buildFrontend() {
  const ng_exec = isWin32
    ? path.join(__dirname, "frontend/node_modules/.bin/ng.cmd")
    : path.join(__dirname, "frontend/node_modules/.bin/ng");

  let args = ["build"];
  if (isProd) {
    args = args.concat(["--configuration=production", "--aot=true"]);
  } else {
    args = args.concat(["--source-map=true"]);
  }
  args = args.concat(["--output-path=../build/app/frontend/", "--base-href=./"]);
  return spawn(ng_exec, args, {
    cwd: "./frontend",
    stdio: "inherit",
  });
}

function build() {
  return gulp.parallel('build:frontend', 'build:app', 'build:server')
}

gulp.task('build:frontend', buildFrontend);
gulp.task('build:app', buildApp);
gulp.task('build:server', gulp.series(buildServer, copy_server_assets));
gulp.task('build', build());

export default build();
