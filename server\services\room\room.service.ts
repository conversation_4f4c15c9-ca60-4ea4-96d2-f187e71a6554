import { DataAccess } from '@data/types';
import { Room } from './room.model';

export class RoomService {
  public rooms: Room[] = [];
  dataAccess: DataAccess;

  constructor(dataAccess: DataAccess) {
    this.dataAccess = dataAccess;
  }

  async loadAllRooms() {
    const rooms = await this.dataAccess.room.getAllRooms();
    this.rooms = rooms.map((room) => new Room(room));
    return this.rooms;
  }

  getRoom(sn_code: string) {
    return this.rooms.find((room) => room.sn_code === sn_code);
  }

  async register(opts: {
    name: string;
    room_sn: string;
    host: string;
    address: string;
    app_version: string;
  }) {
    const { name, room_sn, host, address, app_version } = opts;
    const room = this.rooms.find((room) => room.sn_code === room_sn);
    if (room) {
      if (room.host === host) {
        console.warn(`Room ${room_sn} already registered`);
      } else {
        console.warn(`Room ${room_sn} register with different host`);
      }
      room.name = name;
      room.sn_code = room_sn;
      room.host = host;
      room.address = address;
      room.app_version = app_version;
      await this.dataAccess.room.update({
        id: room.id,
        name,
        sn_code: room_sn,
        host,
        address,
        token: room.token,
        app_version,
      });
      return room;
    } else {
      const room = await this.dataAccess.room.create({
        name,
        sn_code: room_sn,
        host,
        address,
        token: generateToken(),
        app_version,
      });
      const new_room = new Room(room);
      this.rooms.push(new_room);
      return new_room;
    }
  }

  async remove(sn_codes: string[]) {
    this.rooms = this.rooms.filter((room) => !sn_codes.includes(room.sn_code));
    await this.dataAccess.room.delete(sn_codes);
  }
}

function generateToken() {
  const chars =
    '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const length = 8;
  let token = '';
  for (let i = 0; i < length; i++) {
    const index = Math.floor(Math.random() * chars.length);
    token += chars[index];
  }
  return token;
}
