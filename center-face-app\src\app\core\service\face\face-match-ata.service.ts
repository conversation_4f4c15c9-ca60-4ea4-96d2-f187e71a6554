import { Injectable } from '@angular/core';
import { FaceDetection, Results } from '@mediapipe/face_detection';
import { InferenceSession, Tensor, env } from 'onnxruntime-web';

declare let cv: any;

interface Box {
  _x: number;
  _y: number;
  _width: number;
  _height: number;
  width: number;
  height: number;
}

interface FaceDetectOptions {
  faceInCenter: boolean;
  faceSize: number;
  minFaceNum: number;
}

export enum FaceDetectResultCode {
  SUCCESS = 0,
  NO_FACE = 201,
  MULTI_FACE = 202,
  FACE_NOT_IN_CENTER = 203,
  FACE_TOO_SMALL = 204,
}

interface FaceDetectResult {
  code: FaceDetectResultCode;
  image?: HTMLImageElement;
}


@Injectable({
  providedIn: 'root',
})
export class FaceMatchByATAService {
  private facenet!: InferenceSession;
  private inputShape = [1, 3, 160, 160];
  private faceDetection!: FaceDetection;

  constructor() {
    this.init();
  }

  async init() {
    return this.initFaceDetection();
  }

  private async initFaceDetection() {
    env.wasm.wasmPaths = 'assets/face-match/';

    this.faceDetection = new FaceDetection({
      locateFile: (file) => {
        return `assets/face-match/${file}`; // load target files
      },
    });
    this.faceDetection.setOptions({
      model: 'short', // short range detection
      minDetectionConfidence: 0.5,
    });

    this.facenet = await InferenceSession.create('assets/face-match/facenet.onnx');
  }

  // 检测图片中的人脸
  async detectFace(image: HTMLImageElement, options?: FaceDetectOptions) {
    const { faceInCenter, faceSize, minFaceNum } = options || {};
    const detectionResults = await new Promise<Results>((resolve, reject) => {
      this.faceDetection.onResults((results) => {
        resolve(results);
      });
      this.faceDetection.send({ image: image });
    });

    const faceDetectResult: FaceDetectResult = {
      code: FaceDetectResultCode.SUCCESS,
    };
    const detections = detectionResults.detections;
    const faceData: { box: Box; leftEye: any[]; rightEye: any[] }[] = [];
    if (minFaceNum && detections.length > minFaceNum) {
      faceDetectResult.code = FaceDetectResultCode.MULTI_FACE;
      return faceDetectResult;
    }
    detections.forEach((detection) => {
      const box = this.getBoundingBox(detection.boundingBox, image.naturalWidth, image.naturalHeight);
      const landmarks = this.getLandmarks(detection.landmarks, image.naturalWidth, image.naturalHeight);
      const leftEye = [landmarks[0]];
      const rightEye = [landmarks[1]];
      faceData.push({ box: box, leftEye: leftEye, rightEye: rightEye });
    });
    const targetFace = faceData[0];
    if(!targetFace){
      console.warn('face detect warning: no face', targetFace);
      faceDetectResult.code = FaceDetectResultCode.NO_FACE;
      return faceDetectResult;
    }
    if (faceInCenter && !this.isFaceInCenter(targetFace, image.naturalWidth, image.naturalHeight)) {
      faceDetectResult.code = FaceDetectResultCode.FACE_NOT_IN_CENTER;
      return faceDetectResult;
    }
    if (faceSize) {
      const faceAreaRate = this.getFaceAreaRate(targetFace, image.naturalWidth, image.naturalHeight);
      console.log('faceAreaRate:', faceAreaRate);
      if (faceAreaRate < faceSize) {
        faceDetectResult.code = FaceDetectResultCode.FACE_TOO_SMALL;
        return faceDetectResult;
      }
    }
    faceDetectResult.image = await this.cropImage(image, targetFace);
    return faceDetectResult;
  }

  private isFaceInCenter(face: { box: Box; leftEye: any[]; rightEye: any[] }, imageNaturalWidth: number, imageNaturalHeight: number) {
    if(!face){
      console.warn('face detect warning: ', face);
      return true;
    }
    const left = face.box._x;
    const right = imageNaturalWidth - (face.box._x + face.box.width);
    const leftRate = left / imageNaturalWidth;
    const rightRate = right / imageNaturalWidth;
    const leftRightRate = Math.abs(leftRate - rightRate);
    return leftRightRate <= 0.20;
  }

  private getFaceAreaRate(face: { box: Box; leftEye: any[]; rightEye: any[] }, imageNaturalWidth: number, imageNaturalHeight: number) {
    const faceArea = face.box.width * face.box.height;
    const imageArea = imageNaturalWidth * imageNaturalHeight;
    const faceAreaRate = faceArea / imageArea;
    return faceAreaRate;
  }

  private getBoundingBox(mediapipeBoundingBox: any, imageNaturalWidth: number, imageNaturalHeight: number) {
    const boundingBox: Box = { _x: 0, _y: 0, _width: 0, _height: 0, width: 0, height: 0 };
    // top left
    const width = mediapipeBoundingBox.width * imageNaturalWidth;
    const height = mediapipeBoundingBox.height * imageNaturalHeight;
    boundingBox._x = mediapipeBoundingBox.xCenter * imageNaturalWidth - width / 2;
    boundingBox._y = mediapipeBoundingBox.yCenter * imageNaturalHeight - height / 2;

    boundingBox._width = width;
    boundingBox._height = height;
    boundingBox.width = width;
    boundingBox.height = height;
    return boundingBox;
  }

  private getLandmarks(mediapipeLandmarks: any, imageNaturalWidth: number, imageNaturalHeight: number) {
    const landmarks: { _x: number; _y: number }[] = [];

    mediapipeLandmarks.forEach((mediapipeLandmark: any) => {
      landmarks.push({ _x: mediapipeLandmark.x * imageNaturalWidth, _y: mediapipeLandmark.y * imageNaturalHeight });
    });
    return landmarks;
  }

  private async cropImage(image: HTMLImageElement, targetFace: { box: Box; leftEye: any[]; rightEye: any[] }) {
    const box = targetFace.box;
    const leftEye = targetFace.leftEye;
    const rightEye = targetFace.rightEye;

    let leftEyeCenterX = 0;
    let leftEyeCenterY = 0;
    let rightEyeCenterX = 0;
    let rightEyeCenterY = 0;

    for (let i = 0; i < leftEye.length; i++) {
      leftEyeCenterX += leftEye[i]._x;
      leftEyeCenterY += leftEye[i]._y;
      rightEyeCenterX += rightEye[i]._x;
      rightEyeCenterY += rightEye[i]._y;
    }

    leftEyeCenterX /= leftEye.length;
    leftEyeCenterY /= leftEye.length;
    rightEyeCenterX /= rightEye.length;
    rightEyeCenterY /= rightEye.length;

    const angle = Math.atan2(leftEyeCenterY - rightEyeCenterY, rightEyeCenterX - leftEyeCenterX);
    const faceCenterX = targetFace.box._x + targetFace.box.width / 2;
    const faceCenterY = targetFace.box._y + targetFace.box.height / 2;

    const canvas = document.createElement('canvas');
    canvas.width = box.width;
    canvas.height = box.height;

    const ctx = canvas.getContext('2d');

    ctx!.translate(canvas.width / 2, canvas.height / 2);
    ctx!.rotate(angle);
    ctx!.drawImage(image, -faceCenterX, -faceCenterY);

    return await this.getHTMLImageElement(canvas.toDataURL('image/png'));
  }

  getHTMLImageElement(imageData: string) {
    return new Promise<HTMLImageElement>((resolve, reject) => {
      const imageEle = new Image();
      imageEle.onload = () => resolve(imageEle);
      imageEle.onerror = (error) => {
        console.error('create HTMLImageElement error: ', error);
        resolve(null as any); // hack: 手机容易出错，所以这里返回null
      }
      imageEle.src = imageData;
    });
  }

  private async embed(image: HTMLImageElement) {
    const [modelWidth, modelHeight] = this.inputShape.slice(2);

    // Data preprocessing
    const mat = cv.imread(image); // RGBA
    const matC3 = new cv.Mat(mat.rows, mat.cols, cv.CV_8UC3); // BGR
    cv.cvtColor(mat, matC3, cv.COLOR_RGBA2BGR);

    // 先resize
    const scale = Math.min(modelWidth / matC3.cols, modelHeight / matC3.rows);
    const nw = parseInt((matC3.cols * scale).toString());
    const nh = parseInt((matC3.rows * scale).toString());

    let dst = new cv.Mat(nh, nw, cv.CV_8UC3);
    let dsize = new cv.Size(nw, nh);
    cv.resize(matC3, dst, dsize, 0, 0, cv.INTER_CUBIC); // BGR

    const yPad = modelHeight - nh;
    const xPad = modelWidth - nw;

    // 再padding，注意是[128, 128, 128, 255]，和训练时保持一致
    let matPad = new cv.Mat();
    cv.copyMakeBorder(
      dst,
      matPad,
      Math.floor(yPad / 2),
      yPad - Math.floor(yPad / 2),
      Math.floor(xPad / 2),
      xPad - Math.floor(xPad / 2),
      cv.BORDER_CONSTANT,
      [128, 128, 128, 255]
    ); // padding black

    // 这边实际只需要除以255
    const input = cv.blobFromImage(
      matPad,
      1 / 255.0,
      new cv.Size(modelWidth, modelHeight),
      new cv.Scalar(0, 0, 0), // 这边是(0, 0, 0)
      true, // 交换红蓝通道，RGB
      false
    );

    const tensor = new Tensor('float32', input.data32F, this.inputShape); // to ort.Tensor
    const { output } = await this.facenet.run({ images: tensor }); // run session and get output layer

    // Memory release
    mat.delete();
    matC3.delete();
    matPad.delete();
    dst.delete();
    input.delete();

    return output;
  }

  // 照片比对
  async compare(image1: HTMLImageElement, image2: HTMLImageElement) {
    const faceMatchResult = {
      code: 0,
      confidence: -1,
      thresholds: [1.44, 1.18, 0.9],
      compareResult: false,
      error_description: '',
    };
    // const signFace = (await this.detectFace(image1)).image;
    // if (!signFace) {
    //   faceMatchResult.code = 201;
    //   console.log('face match by ata: ', JSON.stringify(faceMatchResult));
    //   return faceMatchResult;
    // }

    // const takeFace = (await this.detectFace(image2)).image;
    // if (!takeFace) {
    //   faceMatchResult.code = 202;
    //   console.log('face match by ata: ', JSON.stringify(faceMatchResult));
    //   return faceMatchResult;
    // }

    const signEmbed = await this.embed(image1);
    const takeEmbed = await this.embed(image2);

    faceMatchResult.confidence = this.comparePhoto(signEmbed, takeEmbed);

    signEmbed.dispose();
    takeEmbed.dispose();

    return faceMatchResult;
  }

  private comparePhoto(embedding1: any, embedding2: any) {
    let distance = 0;
    for (let i = 0; i < embedding1.size; i++) {
      distance += Math.pow(embedding1.data[i] - embedding2.data[i], 2);
    }
    return distance;
  }

  // 检查图片清晰度
  async detect_sharp(image: HTMLImageElement, passLine: number = 50) {

    const cvImage = cv.imread(image); // RGBA

    const grayImage = new cv.Mat();
    cv.cvtColor(cvImage, grayImage, cv.COLOR_RGBA2GRAY, 0);

    const laplacianMat = new cv.Mat();

    cv.Laplacian(grayImage, laplacianMat, cv.CV_64F);

    const mean = new cv.Mat(laplacianMat.rows, laplacianMat.cols, cv.CV_64F);
    const standardDeviationMat = new cv.Mat(laplacianMat.rows, laplacianMat.cols, cv.CV_64F);

    cv.meanStdDev(laplacianMat, mean, standardDeviationMat);

    const standardDeviation = standardDeviationMat.doubleAt(0, 0);
    const laplacianVar = standardDeviation * standardDeviation;
    console.log(`detect sharp: ${laplacianVar}`);

    grayImage.delete();
    laplacianMat.delete();
    mean.delete();
    standardDeviationMat.delete();

    return { result: laplacianVar >= passLine, sharpNum: laplacianVar }
  }
}
