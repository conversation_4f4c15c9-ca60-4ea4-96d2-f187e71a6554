import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PwaInstallService {
  public deferredPrompt: any;
  private installPromptSubject = new BehaviorSubject<boolean>(false);
  private manualInstallRequiredSubject = new BehaviorSubject<boolean>(false);
  private eventFired = false;
  private timeoutId: any;

  constructor() {
    this.initInstallPrompt();
  }

  public get isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent) || /Macintosh/i.test(navigator.userAgent) && !(window as any).MSStream;
  }

  public get manualInstallRequired(): Observable<boolean> {
    return this.manualInstallRequiredSubject.asObservable();
  }

  private initInstallPrompt(): void {
    const beforeInstallPromptListener = (e: any) => {
      this.eventFired = true;
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
      }
      e.preventDefault();
      this.deferredPrompt = e;
      this.installPromptSubject.next(true);
      this.manualInstallRequiredSubject.next(false);
    };

    window.addEventListener('beforeinstallprompt', beforeInstallPromptListener);

    if (!this.isInStandaloneMode && !this.deferredPrompt && !this.isIOS) {
      this.timeoutId = setTimeout(() => {
        if (!this.eventFired && !this.isInStandaloneMode) {
          this.installPromptSubject.next(false);
          this.manualInstallRequiredSubject.next(true);
        }
      }, 5000);
    }

    window.addEventListener('appinstalled', () => {
      console.log('appinstalled');
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
      }
      this.deferredPrompt = null;
      this.installPromptSubject.next(false);
      this.manualInstallRequiredSubject.next(false);
      this.eventFired = true;
    });
  }

  public get canInstall(): Observable<boolean> {
    return this.installPromptSubject.asObservable();
  }

  public promptInstall(): Promise<boolean> {
    if (!this.deferredPrompt) {
      return Promise.resolve(false);
    }

    try {
      this.deferredPrompt.prompt();

      return this.deferredPrompt.userChoice.then((choiceResult: any) => {
        if (choiceResult.outcome === 'accepted') {
          console.log('用户已接受安装应用');
          this.installPromptSubject.next(false);
          this.manualInstallRequiredSubject.next(false);
          this.deferredPrompt = null;
          return true;
        } else {
          console.log('用户已拒绝安装应用');
          return false;
        }
      });
    } catch (error) {
      console.error('调用安装提示时出错:', error);
      return Promise.resolve(false);
    }
  }

  public get isMobile(): boolean {
    return /Android|iPhone|iPad/i.test(navigator.userAgent);
  }

  public get isInStandaloneMode(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches || 
           (window.navigator as any).standalone || 
           document.referrer.includes('android-app://');
  }
} 