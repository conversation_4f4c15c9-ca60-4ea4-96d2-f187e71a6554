import { CommonModule } from '@angular/common';
import { Component, inject, ViewChild, type OnInit, ElementRef } from '@angular/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ExamHttpService } from '../../core/http/exam.service';
import { NzInputModule } from 'ng-zorro-antd/input';
import { ModalService } from '../../core/services/modal.service';
import { FormsModule } from '@angular/forms';
import { of, switchMap } from 'rxjs';

export interface IChooseFileModalData {
  schedule_id: string;
}
@Component({
    selector: 'app-choose-file-modal',
    imports: [CommonModule, NzInputModule, FormsModule, NzButtonModule],
    template: `<div>
    <div class="body">
      <div class="file">
        <div class="label">数据包</div>
        <div class="input">
          <input
            type="file"
            class="file-input"
            (change)="handleFileInput($event)"
            #fileInput
          />
          <button nz-button nzType="link" (click)="chooseFile()">
            选择文件
          </button>
        </div>
      </div>
      <div class="file-name" *ngIf="file_name">{{ file_name }}</div>
      <div class="password">
        <div class="label">数据包密码</div>
        <input nz-input placeholder="请输入密码" [(ngModel)]="password" />
      </div>
    </div>
    <div class="footer">
      <button nz-button nzType="default" (click)="close()">取消</button>
      <button
        nz-button
        nzType="primary"
        (click)="uploadFile()"
        [nzLoading]="loading"
      >
        确定
      </button>
    </div>
  </div>`,
    styles: [
        `
      :host {
        display: block;
      }
      .body {
        .file {
          display: flex;
          align-items: center;
          .label {
            width: 100px;
          }
          .input {
            flex: 1;
            display: flex;
            align-items: center;
            input {
              flex: 1;
            }
          }
        }
        .file-name {
          min-height: 30px;
          background-color: #f5f5f5;
          margin: 10px 0;
          padding: 5px 10px;
        }
        .password {
          display: flex;
          align-items: center;
          .label {
            width: 100px;
          }
          input {
            flex: 1;
          }
        }
      }
      .footer {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        button {
          margin-left: 10px;
        }
      }
      input.file-input {
        position: absolute;
        z-index: -1;
        opacity: 0;
        filter: alpha(opacity=0);
        display: block;
        height: 0;
        width: 0;
      }
    `,
    ]
})
export class ChooseFileModalComponent implements OnInit {
  file: File | null = null;
  file_name: string;
  file_path: string;
  loading = false;
  password: string = '';
  readonly #modal = inject(NzModalRef);
  readonly nzModalData: IChooseFileModalData = inject(NZ_MODAL_DATA);

  @ViewChild("fileInput") fileInput: ElementRef<HTMLInputElement>;
  constructor(
    private examHttp: ExamHttpService,
    private modalService: ModalService
  ) {}
  ngOnInit(): void {}

  chooseFile() {
    if (!window.joyshell) {
      this.fileInput.nativeElement.click();
    } else {
      window.joyshell
        .ShowOpenDialog({
          title: '请选择导入的数据包',
          properties: ["openFile"],
          filters: [
            { name: "考试数据包", extensions: ["zip", "jfp"] },
            { name: '所有文件', extensions: ["*"] },
          ],
        })
        .then((r) => {
          if (r.filePaths && r.filePaths.length !== 0) {
            this.file_path = r.filePaths[0];
            this.file_name = this.file_path.split('\\').pop() as string;
          }
        });
    }
  }

  handleFileInput($event: any) {
    const files: FileList = $event.target.files;
    this.file = files.item(0);
    this.file_name = this.file?.name || '';
  }

  uploadFile() {
    if (!this.file && !this.file_name) {
      return;
    }
    this.loading = true;
    of(this.file_path).pipe(
      switchMap((filePath) => {
        if (filePath) {
          console.log("Import file:", filePath);
          return this.examHttp.importFile(this.nzModalData.schedule_id, filePath, this.password);
        } else {
          console.log("Post file:", this.file?.name);
          return this.examHttp.postFile(this.nzModalData.schedule_id, this.file as File, this.password);
        }
      })
    ).subscribe((res) => {
      if (res.status === 'error') {
        const errorDetailMap = {
          1001: { title: '无效的数据包!', message: '请检查文件' },
          1002: { title: '解压错误!', message: '请检查文件或密码' },
          1003: { title: '数据错误!', message: '请检查文件' },
          1004: { title: '数据不匹配!', message: '请确保文件属于该日程' },
          1005: { title: '保存文件失败!', message: '请检查文件' },
          1006: { title: '读取文件错误!', message: '请检查文件' },
          default: { title: '导入失败!', message: '请检查文件' },
        }
        const errorCode = res.error?.code as keyof typeof errorDetailMap;
        const { title, message } = 
        errorDetailMap[errorCode || "default"] as {
          title: string;
          message: string;
        } || { title: '导入失败!', message: '请检查文件' };
        this.modalService.error(title, message);
        console.warn(title, message, this.file_name);
      } else {
        this.close({ success: true });
      }
      this.loading = false;
      // this.uploadSuccess.emit(res);
    })
  }

  close(result?: any): void {
    this.#modal.destroy(result);
  }
}
