{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-upload.mjs"], "sourcesContent": ["import { ENTER } from '@angular/cdk/keycodes';\nimport { HttpRequest, HttpHeaders, HttpEventType, HttpResponse, HttpClient } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { inject, Input, ViewChild, ViewEncapsulation, Component, ChangeDetectionStrategy, EventEmitter, booleanAttribute, numberAttribute, Output, NgModule } from '@angular/core';\nimport { Subject, of, Observable, Subscription, fromEvent } from 'rxjs';\nimport { switchMap, map, tap, takeUntil, filter } from 'rxjs/operators';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { fromEventOutsideAngular, toBoolean } from 'ng-zorro-antd/core/util';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { DOCUMENT, NgTemplateOutlet } from '@angular/common';\nimport * as i4 from 'ng-zorro-antd/button';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i6 from 'ng-zorro-antd/progress';\nimport { NzProgressModule } from 'ng-zorro-antd/progress';\nimport * as i2 from 'ng-zorro-antd/tooltip';\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\nimport * as i1 from '@angular/cdk/platform';\nimport { Platform } from '@angular/cdk/platform';\nimport * as i5 from 'ng-zorro-antd/core/transition-patch';\nimport * as i1$1 from 'ng-zorro-antd/i18n';\nimport * as i2$1 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"file\"];\nconst _c1 = [\"nz-upload-btn\", \"\"];\nconst _c2 = [\"*\"];\nconst _c3 = a0 => ({\n  $implicit: a0\n});\nconst _c4 = () => ({\n  opacity: 0.5,\n  \"pointer-events\": \"none\"\n});\nfunction NzUploadListComponent_For_1_ng_template_2_Case_0_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_ng_template_2_Case_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, NzUploadListComponent_For_1_ng_template_2_Case_0_ng_template_1_Template, 0, 0, \"ng-template\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const iconNode_r2 = i0.ɵɵreference(5);\n    i0.ɵɵclassProp(\"ant-upload-list-item-file\", !file_r1.isUploading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconNode_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c3, file_r1));\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_2_Case_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 19);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"src\", file_r1.thumbUrl || file_r1.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"alt\", file_r1.name);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_2_Case_1_Conditional_2_ng_template_0_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_ng_template_2_Case_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_2_Case_1_Conditional_2_ng_template_0_Template, 0, 0, \"ng-template\", 17);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(3).$implicit;\n    const iconNode_r2 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconNode_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, file_r1));\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_2_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 18);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_For_1_ng_template_2_Case_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵtemplate(1, NzUploadListComponent_For_1_ng_template_2_Case_1_Conditional_1_Template, 1, 2, \"img\", 19)(2, NzUploadListComponent_For_1_ng_template_2_Case_1_Conditional_2_Template, 1, 4, null, 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"ant-upload-list-item-file\", !file_r1.isImageUrl);\n    i0.ɵɵproperty(\"href\", file_r1.url || file_r1.thumbUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(file_r1.isImageUrl ? 1 : 2);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_2_Case_2_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_ng_template_2_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, NzUploadListComponent_For_1_ng_template_2_Case_2_ng_template_1_Template, 0, 0, \"ng-template\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const iconNode_r2 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconNode_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, file_r1));\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_2_Case_0_Template, 2, 6, \"div\", 13)(1, NzUploadListComponent_For_1_ng_template_2_Case_1_Template, 3, 4, \"a\", 14)(2, NzUploadListComponent_For_1_ng_template_2_Case_2_Template, 2, 4, \"div\", 15);\n  }\n  if (rf & 2) {\n    let tmp_17_0;\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵconditional((tmp_17_0 = file_r1.iconType) === \"uploading\" ? 0 : tmp_17_0 === \"thumbnail\" ? 1 : 2);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 21);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 22);\n  }\n  if (rf & 2) {\n    const file_r5 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"nzType\", file_r5.isImageUrl ? \"picture\" : \"file\");\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_0_Conditional_0_Template, 1, 0, \"nz-icon\", 21)(1, NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_0_Conditional_1_Template, 1, 1, \"nz-icon\", 22);\n  }\n  if (rf & 2) {\n    const file_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵconditional(file_r5.isUploading ? 0 : 1);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.locale.uploading, \" \");\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 22);\n  }\n  if (rf & 2) {\n    const file_r5 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"nzType\", file_r5.isImageUrl ? \"picture\" : \"file\");\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_1_Conditional_0_Template, 1, 1)(1, NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_1_Conditional_1_Template, 1, 1, \"nz-icon\", 22);\n  }\n  if (rf & 2) {\n    const file_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵconditional(file_r5.isUploading ? 0 : 1);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 20);\n  }\n  if (rf & 2) {\n    const file_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"nzType\", file_r5.isUploading ? \"loading\" : \"paper-clip\");\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_4_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_0_Template, 2, 1)(1, NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_1_Template, 2, 1)(2, NzUploadListComponent_For_1_ng_template_4_Conditional_0_Case_2_Template, 1, 1, \"nz-icon\", 20);\n  }\n  if (rf & 2) {\n    let tmp_19_0;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵconditional((tmp_19_0 = ctx_r3.listType) === \"picture\" ? 0 : tmp_19_0 === \"picture-card\" ? 1 : 2);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_4_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_ng_template_4_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_4_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 17);\n  }\n  if (rf & 2) {\n    const file_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.iconRender)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, file_r5));\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_4_Conditional_0_Template, 3, 1)(1, NzUploadListComponent_For_1_ng_template_4_Conditional_1_Template, 1, 4, null, 17);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(!ctx_r3.iconRender ? 0 : 1);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_6_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_For_1_ng_template_6_Conditional_0_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleRemove(file_r1, $event));\n    });\n    i0.ɵɵelement(1, \"nz-icon\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"title\", ctx_r3.locale.removeFile);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_6_Conditional_0_Template, 2, 1, \"button\", 23);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(ctx_r3.icons.showRemoveIcon ? 0 : -1);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_8_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_For_1_ng_template_8_Conditional_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleDownload(file_r1));\n    });\n    i0.ɵɵelement(1, \"nz-icon\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"title\", ctx_r3.locale.downloadFile);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_8_Conditional_0_Template, 2, 1, \"button\", 23);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵconditional(file_r1.showDownload ? 0 : -1);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_10_Conditional_0_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_ng_template_10_Conditional_0_ng_template_2_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_ng_template_10_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, NzUploadListComponent_For_1_ng_template_10_Conditional_0_ng_template_1_Template, 0, 0, \"ng-template\", 10)(2, NzUploadListComponent_For_1_ng_template_10_Conditional_0_ng_template_2_Template, 0, 0, \"ng-template\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const removeIcon_r8 = i0.ɵɵreference(7);\n    const downloadIcon_r9 = i0.ɵɵreference(9);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ant-upload-list-item-card-actions \", ctx_r3.listType === \"picture\" ? \"picture\" : \"\", \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", downloadIcon_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", removeIcon_r8);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_10_Conditional_0_Template, 3, 5, \"span\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(ctx_r3.listType !== \"picture-card\" ? 0 : -1);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_12_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 29);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_For_1_ng_template_12_Conditional_0_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"href\", file_r1.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"title\", file_r1.name)(\"download\", file_r1.linkProps && file_r1.linkProps.download);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", file_r1.name, \" \");\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_12_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_For_1_ng_template_12_Conditional_1_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵattribute(\"title\", file_r1.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", file_r1.name, \" \");\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_12_ng_template_2_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_ng_template_12_Conditional_0_Template, 2, 4, \"a\", 27)(1, NzUploadListComponent_For_1_ng_template_12_Conditional_1_Template, 2, 2, \"span\", 28)(2, NzUploadListComponent_For_1_ng_template_12_ng_template_2_Template, 0, 0, \"ng-template\", 10);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    const downloadOrDelete_r12 = i0.ɵɵreference(11);\n    i0.ɵɵconditional(file_r1.url ? 0 : 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", downloadOrDelete_r12);\n  }\n}\nfunction NzUploadListComponent_For_1_ng_template_16_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_ng_template_17_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_Conditional_18_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 32);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_For_1_Conditional_18_Conditional_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵelement(1, \"nz-icon\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(!(file_r1.url || file_r1.thumbUrl) ? i0.ɵɵpureFunction0(4, _c4) : null);\n    i0.ɵɵproperty(\"href\", file_r1.url || file_r1.thumbUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"title\", ctx_r3.locale.previewFile);\n  }\n}\nfunction NzUploadListComponent_For_1_Conditional_18_Conditional_2_ng_template_0_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_Conditional_18_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_For_1_Conditional_18_Conditional_2_ng_template_0_Template, 0, 0, \"ng-template\", 10);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const downloadIcon_r9 = i0.ɵɵreference(9);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", downloadIcon_r9);\n  }\n}\nfunction NzUploadListComponent_For_1_Conditional_18_ng_template_3_Template(rf, ctx) {}\nfunction NzUploadListComponent_For_1_Conditional_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtemplate(1, NzUploadListComponent_For_1_Conditional_18_Conditional_1_Template, 2, 5, \"a\", 31)(2, NzUploadListComponent_For_1_Conditional_18_Conditional_2_Template, 1, 1, null, 10)(3, NzUploadListComponent_For_1_Conditional_18_ng_template_3_Template, 0, 0, \"ng-template\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    const removeIcon_r8 = i0.ɵɵreference(7);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r3.icons.showPreviewIcon ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(file_r1.status === \"done\" ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", removeIcon_r8);\n  }\n}\nfunction NzUploadListComponent_For_1_Conditional_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"nz-progress\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzPercent\", file_r1.percent)(\"nzShowInfo\", false)(\"nzStrokeWidth\", 2);\n  }\n}\nfunction NzUploadListComponent_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 7);\n    i0.ɵɵtemplate(2, NzUploadListComponent_For_1_ng_template_2_Template, 3, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, NzUploadListComponent_For_1_ng_template_4_Template, 2, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(6, NzUploadListComponent_For_1_ng_template_6_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(8, NzUploadListComponent_For_1_ng_template_8_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(10, NzUploadListComponent_For_1_ng_template_10_Template, 1, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor)(12, NzUploadListComponent_For_1_ng_template_12_Template, 3, 2, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(14, \"div\", 8)(15, \"span\", 9);\n    i0.ɵɵtemplate(16, NzUploadListComponent_For_1_ng_template_16_Template, 0, 0, \"ng-template\", 10)(17, NzUploadListComponent_For_1_ng_template_17_Template, 0, 0, \"ng-template\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, NzUploadListComponent_For_1_Conditional_18_Template, 4, 3, \"span\", 11)(19, NzUploadListComponent_For_1_Conditional_19_Template, 2, 3, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r1 = ctx.$implicit;\n    const icon_r14 = i0.ɵɵreference(3);\n    const preview_r15 = i0.ɵɵreference(13);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ant-upload-list-\", ctx_r3.listType, \"-container\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate2(\"ant-upload-list-item ant-upload-list-item-\", file_r1.status, \" ant-upload-list-item-list-type-\", ctx_r3.listType, \"\");\n    i0.ɵɵproperty(\"@itemState\", undefined)(\"nzTooltipTitle\", file_r1.status === \"error\" ? file_r1.message : null);\n    i0.ɵɵattribute(\"data-key\", file_r1.key);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", icon_r14);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", preview_r15);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r3.listType === \"picture-card\" && !file_r1.isUploading ? 18 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(file_r1.isUploading ? 19 : -1);\n  }\n}\nconst _c5 = [\"uploadComp\"];\nconst _c6 = [\"listComp\"];\nconst _c7 = () => [];\nfunction NzUploadComponent_ng_template_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-upload-list\", 6, 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"display\", ctx_r0.nzShowUploadList ? \"\" : \"none\");\n    i0.ɵɵproperty(\"locale\", ctx_r0.locale)(\"listType\", ctx_r0.nzListType)(\"items\", ctx_r0.nzFileList || i0.ɵɵpureFunction0(13, _c7))(\"icons\", ctx_r0.nzShowUploadList)(\"iconRender\", ctx_r0.nzIconRender)(\"previewFile\", ctx_r0.nzPreviewFile)(\"previewIsImage\", ctx_r0.nzPreviewIsImage)(\"onPreview\", ctx_r0.nzPreview)(\"onRemove\", ctx_r0.onRemove)(\"onDownload\", ctx_r0.nzDownload)(\"dir\", ctx_r0.dir);\n  }\n}\nfunction NzUploadComponent_ng_template_0_Conditional_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NzUploadComponent_ng_template_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_ng_template_0_Conditional_1_ng_container_0_Template, 1, 0, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzFileListRender)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, ctx_r0.nzFileList));\n  }\n}\nfunction NzUploadComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_ng_template_0_Conditional_0_Template, 2, 14, \"nz-upload-list\", 5)(1, NzUploadComponent_ng_template_0_Conditional_1_Template, 1, 4, \"ng-container\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r0.locale && !ctx_r0.nzFileListRender ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.nzFileListRender ? 1 : -1);\n  }\n}\nfunction NzUploadComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzUploadComponent_ng_template_4_ng_template_3_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 8, 4);\n    i0.ɵɵtemplate(3, NzUploadComponent_ng_template_4_ng_template_3_Template, 0, 0, \"ng-template\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const con_r2 = i0.ɵɵreference(3);\n    i0.ɵɵclassMap(ctx_r0.classList);\n    i0.ɵɵstyleProp(\"display\", ctx_r0.nzShowButton ? \"\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0._btnOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", con_r2);\n  }\n}\nfunction NzUploadComponent_Conditional_6_ng_template_4_Template(rf, ctx) {}\nfunction NzUploadComponent_Conditional_6_ng_template_5_Template(rf, ctx) {}\nfunction NzUploadComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"drop\", function NzUploadComponent_Conditional_6_Template_div_drop_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.fileDrop($event));\n    })(\"dragover\", function NzUploadComponent_Conditional_6_Template_div_dragover_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.fileDrop($event));\n    })(\"dragleave\", function NzUploadComponent_Conditional_6_Template_div_dragleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.fileDrop($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 11, 4)(3, \"div\", 12);\n    i0.ɵɵtemplate(4, NzUploadComponent_Conditional_6_ng_template_4_Template, 0, 0, \"ng-template\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, NzUploadComponent_Conditional_6_ng_template_5_Template, 0, 0, \"ng-template\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const list_r4 = i0.ɵɵreference(1);\n    const con_r2 = i0.ɵɵreference(3);\n    i0.ɵɵclassMap(ctx_r0.classList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0._btnOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", con_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", list_r4);\n  }\n}\nfunction NzUploadComponent_Conditional_7_Conditional_0_ng_template_0_Template(rf, ctx) {}\nfunction NzUploadComponent_Conditional_7_Conditional_0_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadComponent_Conditional_7_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_Conditional_7_Conditional_0_ng_template_0_Template, 0, 0, \"ng-template\", 9)(1, NzUploadComponent_Conditional_7_Conditional_0_ng_template_1_Template, 0, 0, \"ng-template\", 9);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const list_r4 = i0.ɵɵreference(1);\n    const btn_r5 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", list_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", btn_r5);\n  }\n}\nfunction NzUploadComponent_Conditional_7_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzUploadComponent_Conditional_7_Conditional_1_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadComponent_Conditional_7_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_Conditional_7_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 9)(1, NzUploadComponent_Conditional_7_Conditional_1_ng_template_1_Template, 0, 0, \"ng-template\", 9);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const list_r4 = i0.ɵɵreference(1);\n    const btn_r5 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", btn_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", list_r4);\n  }\n}\nfunction NzUploadComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_Conditional_7_Conditional_0_Template, 2, 2)(1, NzUploadComponent_Conditional_7_Conditional_1_Template, 2, 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r0.nzListType === \"picture-card\" ? 0 : 1);\n  }\n}\nclass NzUploadBtnComponent {\n  elementRef;\n  reqs = {};\n  destroy = false;\n  destroy$ = new Subject();\n  file;\n  options;\n  onClick() {\n    if (this.options.disabled || !this.options.openFileDialogOnClick) {\n      return;\n    }\n    this.file.nativeElement.click();\n  }\n  // skip safari bug\n  onFileDrop(e) {\n    if (this.options.disabled || e.type === 'dragover') {\n      e.preventDefault();\n      return;\n    }\n    if (this.options.directory) {\n      this.traverseFileTree(e.dataTransfer.items);\n    } else {\n      const files = Array.prototype.slice.call(e.dataTransfer.files).filter(file => this.attrAccept(file, this.options.accept));\n      if (files.length) {\n        this.uploadFiles(files);\n      }\n    }\n    e.preventDefault();\n  }\n  onChange(e) {\n    if (this.options.disabled) {\n      return;\n    }\n    const hie = e.target;\n    this.uploadFiles(hie.files);\n    hie.value = '';\n  }\n  traverseFileTree(files) {\n    const _traverseFileTree = (item, path) => {\n      if (item.isFile) {\n        item.file(file => {\n          if (this.attrAccept(file, this.options.accept)) {\n            this.uploadFiles([file]);\n          }\n        });\n      } else if (item.isDirectory) {\n        const dirReader = item.createReader();\n        dirReader.readEntries(entries => {\n          for (const entrieItem of entries) {\n            _traverseFileTree(entrieItem, `${path}${item.name}/`);\n          }\n        });\n      }\n    };\n    for (const file of files) {\n      _traverseFileTree(file.webkitGetAsEntry(), '');\n    }\n  }\n  attrAccept(file, acceptedFiles) {\n    if (file && acceptedFiles) {\n      const acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n      const fileName = `${file.name}`;\n      const mimeType = `${file.type}`;\n      const baseMimeType = mimeType.replace(/\\/.*$/, '');\n      return acceptedFilesArray.some(type => {\n        const validType = type.trim();\n        if (validType.charAt(0) === '.') {\n          return fileName.toLowerCase().indexOf(validType.toLowerCase(), fileName.toLowerCase().length - validType.toLowerCase().length) !== -1;\n        } else if (/\\/\\*$/.test(validType)) {\n          // This is something like an image/* mime type\n          return baseMimeType === validType.replace(/\\/.*$/, '');\n        }\n        return mimeType === validType;\n      });\n    }\n    return true;\n  }\n  attachUid(file) {\n    if (!file.uid) {\n      file.uid = Math.random().toString(36).substring(2);\n    }\n    return file;\n  }\n  uploadFiles(fileList) {\n    let filters$ = of(Array.prototype.slice.call(fileList));\n    if (this.options.filters) {\n      this.options.filters.forEach(f => {\n        filters$ = filters$.pipe(switchMap(list => {\n          const fnRes = f.fn(list);\n          return fnRes instanceof Observable ? fnRes : of(fnRes);\n        }));\n      });\n    }\n    filters$.subscribe({\n      next: list => {\n        list.forEach(file => {\n          this.attachUid(file);\n          this.upload(file, list);\n        });\n      },\n      error: e => {\n        warn(`Unhandled upload filter error`, e);\n      }\n    });\n  }\n  upload(file, fileList) {\n    if (!this.options.beforeUpload) {\n      return this.post(file);\n    }\n    const before = this.options.beforeUpload(file, fileList);\n    if (before instanceof Observable) {\n      before.subscribe({\n        next: processedFile => {\n          const processedFileType = Object.prototype.toString.call(processedFile);\n          if (processedFileType === '[object File]' || processedFileType === '[object Blob]') {\n            this.attachUid(processedFile);\n            this.post(processedFile);\n          } else if (processedFile) {\n            this.post(file);\n          }\n        },\n        error: e => {\n          warn(`Unhandled upload beforeUpload error`, e);\n        }\n      });\n    } else if (before) {\n      return this.post(file);\n    }\n  }\n  post(file) {\n    if (this.destroy) {\n      return;\n    }\n    let process$ = of(file);\n    let transformedFile;\n    const opt = this.options;\n    const {\n      uid\n    } = file;\n    const {\n      action,\n      data,\n      headers,\n      transformFile\n    } = opt;\n    const args = {\n      action: typeof action === 'string' ? action : '',\n      name: opt.name,\n      headers,\n      file,\n      postFile: file,\n      data,\n      withCredentials: opt.withCredentials,\n      onProgress: opt.onProgress ? e => {\n        opt.onProgress(e, file);\n      } : undefined,\n      onSuccess: (ret, xhr) => {\n        this.clean(uid);\n        opt.onSuccess(ret, file, xhr);\n      },\n      onError: xhr => {\n        this.clean(uid);\n        opt.onError(xhr, file);\n      }\n    };\n    if (typeof action === 'function') {\n      const actionResult = action(file);\n      if (actionResult instanceof Observable) {\n        process$ = process$.pipe(switchMap(() => actionResult), map(res => {\n          args.action = res;\n          return file;\n        }));\n      } else {\n        args.action = actionResult;\n      }\n    }\n    if (typeof transformFile === 'function') {\n      const transformResult = transformFile(file);\n      process$ = process$.pipe(switchMap(() => transformResult instanceof Observable ? transformResult : of(transformResult)), tap(newFile => transformedFile = newFile));\n    }\n    if (typeof data === 'function') {\n      const dataResult = data(file);\n      if (dataResult instanceof Observable) {\n        process$ = process$.pipe(switchMap(() => dataResult), map(res => {\n          args.data = res;\n          return transformedFile ?? file;\n        }));\n      } else {\n        args.data = dataResult;\n      }\n    }\n    if (typeof headers === 'function') {\n      const headersResult = headers(file);\n      if (headersResult instanceof Observable) {\n        process$ = process$.pipe(switchMap(() => headersResult), map(res => {\n          args.headers = res;\n          return transformedFile ?? file;\n        }));\n      } else {\n        args.headers = headersResult;\n      }\n    }\n    process$.subscribe(newFile => {\n      args.postFile = newFile;\n      const req$ = (opt.customRequest || this.xhr).call(this, args);\n      if (!(req$ instanceof Subscription)) {\n        warn(`Must return Subscription type in '[nzCustomRequest]' property`);\n      }\n      this.reqs[uid] = req$;\n      opt.onStart(file);\n    });\n  }\n  xhr(args) {\n    const formData = new FormData();\n    if (args.data) {\n      Object.keys(args.data).map(key => {\n        formData.append(key, args.data[key]);\n      });\n    }\n    formData.append(args.name, args.postFile);\n    if (!args.headers) {\n      args.headers = {};\n    }\n    if (args.headers['X-Requested-With'] !== null) {\n      args.headers['X-Requested-With'] = `XMLHttpRequest`;\n    } else {\n      delete args.headers['X-Requested-With'];\n    }\n    const req = new HttpRequest('POST', args.action, formData, {\n      reportProgress: true,\n      withCredentials: args.withCredentials,\n      headers: new HttpHeaders(args.headers)\n    });\n    return this.http.request(req).subscribe({\n      next: event => {\n        if (event.type === HttpEventType.UploadProgress) {\n          if (event.total > 0) {\n            event.percent = event.loaded / event.total * 100;\n          }\n          args.onProgress(event, args.file);\n        } else if (event instanceof HttpResponse) {\n          args.onSuccess(event.body, args.file, event);\n        }\n      },\n      error: err => {\n        this.abort(args.file);\n        args.onError(err, args.file);\n      }\n    });\n  }\n  clean(uid) {\n    const req$ = this.reqs[uid];\n    if (req$ instanceof Subscription) {\n      req$.unsubscribe();\n    }\n    delete this.reqs[uid];\n  }\n  abort(file) {\n    if (file) {\n      this.clean(file && file.uid);\n    } else {\n      Object.keys(this.reqs).forEach(uid => this.clean(uid));\n    }\n  }\n  http = inject(HttpClient, {\n    optional: true\n  });\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    if (!this.http) {\n      throw new Error(`Not found 'HttpClient', You can configure 'HttpClient' with 'provideHttpClient()' in your root module.`);\n    }\n  }\n  ngOnInit() {\n    // Caretaker note: `input[type=file].click()` will open a native OS file picker,\n    // it doesn't require Angular to run `ApplicationRef.tick()`.\n    fromEventOutsideAngular(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(() => this.onClick());\n    fromEventOutsideAngular(this.elementRef.nativeElement, 'keydown').pipe(takeUntil(this.destroy$)).subscribe(event => {\n      if (this.options.disabled) {\n        return;\n      }\n      if (event.key === 'Enter' || event.keyCode === ENTER) {\n        this.onClick();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy = true;\n    this.destroy$.next();\n    this.abort();\n  }\n  static ɵfac = function NzUploadBtnComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzUploadBtnComponent)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzUploadBtnComponent,\n    selectors: [[\"\", \"nz-upload-btn\", \"\"]],\n    viewQuery: function NzUploadBtnComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.file = _t.first);\n      }\n    },\n    hostAttrs: [1, \"ant-upload\"],\n    hostVars: 4,\n    hostBindings: function NzUploadBtnComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"drop\", function NzUploadBtnComponent_drop_HostBindingHandler($event) {\n          return ctx.onFileDrop($event);\n        })(\"dragover\", function NzUploadBtnComponent_dragover_HostBindingHandler($event) {\n          return ctx.onFileDrop($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"tabindex\", \"0\")(\"role\", \"button\");\n        i0.ɵɵclassProp(\"ant-upload-disabled\", ctx.options.disabled);\n      }\n    },\n    inputs: {\n      options: \"options\"\n    },\n    exportAs: [\"nzUploadBtn\"],\n    attrs: _c1,\n    ngContentSelectors: _c2,\n    decls: 3,\n    vars: 6,\n    consts: [[\"file\", \"\"], [\"type\", \"file\", 3, \"change\", \"multiple\"]],\n    template: function NzUploadBtnComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"input\", 1, 0);\n        i0.ɵɵlistener(\"change\", function NzUploadBtnComponent_Template_input_change_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onChange($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(2);\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"display\", \"none\");\n        i0.ɵɵproperty(\"multiple\", ctx.options.multiple);\n        i0.ɵɵattribute(\"accept\", ctx.options.accept)(\"directory\", ctx.options.directory ? \"directory\" : null)(\"webkitdirectory\", ctx.options.directory ? \"webkitdirectory\" : null);\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadBtnComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-upload-btn]',\n      exportAs: 'nzUploadBtn',\n      host: {\n        class: 'ant-upload',\n        '[attr.tabindex]': '\"0\"',\n        '[attr.role]': '\"button\"',\n        '[class.ant-upload-disabled]': 'options.disabled',\n        '(drop)': 'onFileDrop($event)',\n        '(dragover)': 'onFileDrop($event)'\n      },\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<!--\\n  We explicitly bind `style.display` to avoid using an inline style\\n  attribute property (which is not allowed when CSP `unsafe-inline`\\n  is not specified).\\n-->\\n<input\\n  type=\\\"file\\\"\\n  #file\\n  (change)=\\\"onChange($event)\\\"\\n  [attr.accept]=\\\"options.accept\\\"\\n  [attr.directory]=\\\"options.directory ? 'directory' : null\\\"\\n  [attr.webkitdirectory]=\\\"options.directory ? 'webkitdirectory' : null\\\"\\n  [multiple]=\\\"options.multiple\\\"\\n  [style.display]=\\\"'none'\\\"\\n/>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    file: [{\n      type: ViewChild,\n      args: ['file', {\n        static: true\n      }]\n    }],\n    options: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst isImageFileType = type => !!type && type.indexOf('image/') === 0;\nconst MEASURE_SIZE = 200;\nclass NzUploadListComponent {\n  cdr;\n  ngZone;\n  platform;\n  list = [];\n  get showPic() {\n    return this.listType === 'picture' || this.listType === 'picture-card';\n  }\n  locale = {};\n  listType;\n  set items(list) {\n    this.list = list;\n  }\n  icons;\n  onPreview;\n  onRemove;\n  onDownload;\n  previewFile;\n  previewIsImage;\n  iconRender = null;\n  dir = 'ltr';\n  document = inject(DOCUMENT);\n  destroy$ = new Subject();\n  genErr(file) {\n    if (file.response && typeof file.response === 'string') {\n      return file.response;\n    }\n    return file.error && file.error.statusText || this.locale.uploadError;\n  }\n  extname(url) {\n    const temp = url.split('/');\n    const filename = temp[temp.length - 1];\n    const filenameWithoutSuffix = filename.split(/#|\\?/)[0];\n    return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];\n  }\n  isImageUrl(file) {\n    if (isImageFileType(file.type)) {\n      return true;\n    }\n    const url = file.thumbUrl || file.url || '';\n    if (!url) {\n      return false;\n    }\n    const extension = this.extname(url);\n    if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg)$/i.test(extension)) {\n      return true;\n    } else if (/^data:/.test(url)) {\n      // other file types of base64\n      return false;\n    } else if (extension) {\n      // other file types which have extension\n      return false;\n    }\n    return true;\n  }\n  getIconType(file) {\n    if (!this.showPic) {\n      return '';\n    }\n    if (file.isUploading || !file.thumbUrl && !file.url) {\n      return 'uploading';\n    } else {\n      return 'thumbnail';\n    }\n  }\n  previewImage(file) {\n    if (!isImageFileType(file.type) || !this.platform.isBrowser) {\n      return of('');\n    }\n    const canvas = this.document.createElement('canvas');\n    canvas.width = MEASURE_SIZE;\n    canvas.height = MEASURE_SIZE;\n    canvas.style.cssText = `position: fixed; left: 0; top: 0; width: ${MEASURE_SIZE}px; height: ${MEASURE_SIZE}px; z-index: 9999; display: none;`;\n    this.document.body.appendChild(canvas);\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    const objectUrl = URL.createObjectURL(file);\n    img.src = objectUrl;\n    return fromEvent(img, 'load').pipe(map(() => {\n      const {\n        width,\n        height\n      } = img;\n      let drawWidth = MEASURE_SIZE;\n      let drawHeight = MEASURE_SIZE;\n      let offsetX = 0;\n      let offsetY = 0;\n      if (width < height) {\n        drawHeight = height * (MEASURE_SIZE / width);\n        offsetY = -(drawHeight - drawWidth) / 2;\n      } else {\n        drawWidth = width * (MEASURE_SIZE / height);\n        offsetX = -(drawWidth - drawHeight) / 2;\n      }\n      try {\n        ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);\n      } catch {\n        // noop\n      }\n      const dataURL = canvas.toDataURL();\n      this.document.body.removeChild(canvas);\n      URL.revokeObjectURL(objectUrl);\n      return dataURL;\n    }));\n  }\n  genThumb() {\n    if (!this.platform.isBrowser) {\n      return;\n    }\n    const win = window;\n    if (!this.showPic || typeof document === 'undefined' || typeof win === 'undefined' || !win.FileReader || !win.File) {\n      return;\n    }\n    this.list.filter(file => file.originFileObj instanceof File && file.thumbUrl === undefined).forEach(file => {\n      file.thumbUrl = '';\n      // Caretaker note: we shouldn't use promises here since they're not cancellable.\n      // A promise microtask can be resolved after the view is destroyed. Thus running `detectChanges()`\n      // will cause a runtime exception (`detectChanges()` cannot be run on destroyed views).\n      const dataUrl$ = (this.previewFile ? this.previewFile(file) : this.previewImage(file.originFileObj)).pipe(takeUntil(this.destroy$));\n      this.ngZone.runOutsideAngular(() => {\n        dataUrl$.subscribe(dataUrl => {\n          this.ngZone.run(() => {\n            file.thumbUrl = dataUrl;\n            this.detectChanges();\n          });\n        });\n      });\n    });\n  }\n  showDownload(file) {\n    return !!(this.icons.showDownloadIcon && file.status === 'done');\n  }\n  fixData() {\n    this.list.forEach(file => {\n      file.isUploading = file.status === 'uploading';\n      file.message = this.genErr(file);\n      file.linkProps = typeof file.linkProps === 'string' ? JSON.parse(file.linkProps) : file.linkProps;\n      file.isImageUrl = this.previewIsImage ? this.previewIsImage(file) : this.isImageUrl(file);\n      file.iconType = this.getIconType(file);\n      file.showDownload = this.showDownload(file);\n    });\n  }\n  handlePreview(file, e) {\n    if (!this.onPreview) {\n      return;\n    }\n    e.preventDefault();\n    return this.onPreview(file);\n  }\n  handleRemove(file, e) {\n    e.preventDefault();\n    if (this.onRemove) {\n      this.onRemove(file);\n    }\n    return;\n  }\n  handleDownload(file) {\n    if (typeof this.onDownload === 'function') {\n      this.onDownload(file);\n    } else if (file.url) {\n      window.open(file.url);\n    }\n  }\n  // #endregion\n  constructor(cdr, ngZone, platform) {\n    this.cdr = cdr;\n    this.ngZone = ngZone;\n    this.platform = platform;\n  }\n  detectChanges() {\n    this.fixData();\n    this.cdr.detectChanges();\n  }\n  ngOnChanges() {\n    this.fixData();\n    this.genThumb();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n  }\n  static ɵfac = function NzUploadListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzUploadListComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.Platform));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzUploadListComponent,\n    selectors: [[\"nz-upload-list\"]],\n    hostAttrs: [1, \"ant-upload-list\"],\n    hostVars: 8,\n    hostBindings: function NzUploadListComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-upload-list-rtl\", ctx.dir === \"rtl\")(\"ant-upload-list-text\", ctx.listType === \"text\")(\"ant-upload-list-picture\", ctx.listType === \"picture\")(\"ant-upload-list-picture-card\", ctx.listType === \"picture-card\");\n      }\n    },\n    inputs: {\n      locale: \"locale\",\n      listType: \"listType\",\n      items: \"items\",\n      icons: \"icons\",\n      onPreview: \"onPreview\",\n      onRemove: \"onRemove\",\n      onDownload: \"onDownload\",\n      previewFile: \"previewFile\",\n      previewIsImage: \"previewIsImage\",\n      iconRender: \"iconRender\",\n      dir: \"dir\"\n    },\n    exportAs: [\"nzUploadList\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 2,\n    vars: 0,\n    consts: [[\"icon\", \"\"], [\"iconNode\", \"\"], [\"removeIcon\", \"\"], [\"downloadIcon\", \"\"], [\"downloadOrDelete\", \"\"], [\"preview\", \"\"], [3, \"class\"], [\"nz-tooltip\", \"\", 3, \"nzTooltipTitle\"], [1, \"ant-upload-list-item-info\"], [1, \"ant-upload-span\"], [3, \"ngTemplateOutlet\"], [1, \"ant-upload-list-item-actions\"], [1, \"ant-upload-list-item-progress\"], [1, \"ant-upload-list-item-thumbnail\", 3, \"ant-upload-list-item-file\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"ant-upload-list-item-thumbnail\", 3, \"ant-upload-list-item-file\", \"href\"], [1, \"ant-upload-text-icon\"], [1, \"ant-upload-list-item-thumbnail\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"ant-upload-list-item-thumbnail\", 3, \"click\", \"href\"], [1, \"ant-upload-list-item-image\", 3, \"src\"], [3, \"nzType\"], [\"nzType\", \"loading\"], [\"nzTheme\", \"twotone\", 3, \"nzType\"], [\"type\", \"button\", \"nz-button\", \"\", \"nzType\", \"text\", \"nzSize\", \"small\", 1, \"ant-upload-list-item-card-actions-btn\"], [\"type\", \"button\", \"nz-button\", \"\", \"nzType\", \"text\", \"nzSize\", \"small\", 1, \"ant-upload-list-item-card-actions-btn\", 3, \"click\"], [\"nzType\", \"delete\"], [\"nzType\", \"download\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"ant-upload-list-item-name\", 3, \"href\"], [1, \"ant-upload-list-item-name\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"ant-upload-list-item-name\", 3, \"click\", \"href\"], [1, \"ant-upload-list-item-name\", 3, \"click\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\", \"style\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"click\", \"href\"], [\"nzType\", \"eye\"], [\"nzType\", \"line\", 3, \"nzPercent\", \"nzShowInfo\", \"nzStrokeWidth\"]],\n    template: function NzUploadListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵrepeaterCreate(0, NzUploadListComponent_For_1_Template, 20, 14, \"div\", 6, i0.ɵɵrepeaterTrackByIdentity);\n      }\n      if (rf & 2) {\n        i0.ɵɵrepeater(ctx.list);\n      }\n    },\n    dependencies: [NzToolTipModule, i2.NzTooltipDirective, NgTemplateOutlet, NzIconModule, i3.NzIconDirective, NzButtonModule, i4.NzButtonComponent, i5.ɵNzTransitionPatchDirective, NzProgressModule, i6.NzProgressComponent],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('itemState', [transition(':enter', [style({\n        height: '0',\n        width: '0',\n        opacity: 0\n      }), animate(150, style({\n        height: '*',\n        width: '*',\n        opacity: 1\n      }))]), transition(':leave', [animate(150, style({\n        height: '0',\n        width: '0',\n        opacity: 0\n      }))])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadListComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-upload-list',\n      exportAs: 'nzUploadList',\n      animations: [trigger('itemState', [transition(':enter', [style({\n        height: '0',\n        width: '0',\n        opacity: 0\n      }), animate(150, style({\n        height: '*',\n        width: '*',\n        opacity: 1\n      }))]), transition(':leave', [animate(150, style({\n        height: '0',\n        width: '0',\n        opacity: 0\n      }))])])],\n      host: {\n        class: 'ant-upload-list',\n        '[class.ant-upload-list-rtl]': `dir === 'rtl'`,\n        '[class.ant-upload-list-text]': `listType === 'text'`,\n        '[class.ant-upload-list-picture]': `listType === 'picture'`,\n        '[class.ant-upload-list-picture-card]': `listType === 'picture-card'`\n      },\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NzToolTipModule, NgTemplateOutlet, NzIconModule, NzButtonModule, NzProgressModule],\n      template: \"@for (file of list; track file) {\\n  <div class=\\\"ant-upload-list-{{ listType }}-container\\\">\\n    <div\\n      class=\\\"ant-upload-list-item ant-upload-list-item-{{ file.status }} ant-upload-list-item-list-type-{{ listType }}\\\"\\n      [attr.data-key]=\\\"file.key\\\"\\n      @itemState\\n      nz-tooltip\\n      [nzTooltipTitle]=\\\"file.status === 'error' ? file.message : null\\\"\\n    >\\n      <ng-template #icon>\\n        @switch (file.iconType) {\\n          @case ('uploading') {\\n            <div class=\\\"ant-upload-list-item-thumbnail\\\" [class.ant-upload-list-item-file]=\\\"!file.isUploading\\\">\\n              <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n            </div>\\n          }\\n          @case ('thumbnail') {\\n            <a\\n              class=\\\"ant-upload-list-item-thumbnail\\\"\\n              [class.ant-upload-list-item-file]=\\\"!file.isImageUrl\\\"\\n              target=\\\"_blank\\\"\\n              rel=\\\"noopener noreferrer\\\"\\n              [href]=\\\"file.url || file.thumbUrl\\\"\\n              (click)=\\\"handlePreview(file, $event)\\\"\\n            >\\n              @if (file.isImageUrl) {\\n                <img class=\\\"ant-upload-list-item-image\\\" [src]=\\\"file.thumbUrl || file.url\\\" [attr.alt]=\\\"file.name\\\" />\\n              } @else {\\n                <ng-template\\n                  [ngTemplateOutlet]=\\\"iconNode\\\"\\n                  [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"\\n                ></ng-template>\\n              }\\n            </a>\\n          }\\n          @default {\\n            <div class=\\\"ant-upload-text-icon\\\">\\n              <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n            </div>\\n          }\\n        }\\n      </ng-template>\\n\\n      <ng-template #iconNode let-file>\\n        @if (!iconRender) {\\n          @switch (listType) {\\n            @case ('picture') {\\n              @if (file.isUploading) {\\n                <nz-icon nzType=\\\"loading\\\" />\\n              } @else {\\n                <nz-icon [nzType]=\\\"file.isImageUrl ? 'picture' : 'file'\\\" nzTheme=\\\"twotone\\\" />\\n              }\\n            }\\n            @case ('picture-card') {\\n              @if (file.isUploading) {\\n                {{ locale.uploading }}\\n              } @else {\\n                <nz-icon [nzType]=\\\"file.isImageUrl ? 'picture' : 'file'\\\" nzTheme=\\\"twotone\\\" />\\n              }\\n            }\\n            @default {\\n              <nz-icon [nzType]=\\\"file.isUploading ? 'loading' : 'paper-clip'\\\" />\\n            }\\n          }\\n        } @else {\\n          <ng-template [ngTemplateOutlet]=\\\"iconRender\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n        }\\n      </ng-template>\\n\\n      <ng-template #removeIcon>\\n        @if (icons.showRemoveIcon) {\\n          <button\\n            type=\\\"button\\\"\\n            nz-button\\n            nzType=\\\"text\\\"\\n            nzSize=\\\"small\\\"\\n            (click)=\\\"handleRemove(file, $event)\\\"\\n            [attr.title]=\\\"locale.removeFile\\\"\\n            class=\\\"ant-upload-list-item-card-actions-btn\\\"\\n          >\\n            <nz-icon nzType=\\\"delete\\\" />\\n          </button>\\n        }\\n      </ng-template>\\n\\n      <ng-template #downloadIcon>\\n        @if (file.showDownload) {\\n          <button\\n            type=\\\"button\\\"\\n            nz-button\\n            nzType=\\\"text\\\"\\n            nzSize=\\\"small\\\"\\n            (click)=\\\"handleDownload(file)\\\"\\n            [attr.title]=\\\"locale.downloadFile\\\"\\n            class=\\\"ant-upload-list-item-card-actions-btn\\\"\\n          >\\n            <nz-icon nzType=\\\"download\\\" />\\n          </button>\\n        }\\n      </ng-template>\\n\\n      <ng-template #downloadOrDelete>\\n        @if (listType !== 'picture-card') {\\n          <span class=\\\"ant-upload-list-item-card-actions {{ listType === 'picture' ? 'picture' : '' }}\\\">\\n            <ng-template [ngTemplateOutlet]=\\\"downloadIcon\\\"></ng-template>\\n            <ng-template [ngTemplateOutlet]=\\\"removeIcon\\\"></ng-template>\\n          </span>\\n        }\\n      </ng-template>\\n\\n      <ng-template #preview>\\n        @if (file.url) {\\n          <a\\n            target=\\\"_blank\\\"\\n            rel=\\\"noopener noreferrer\\\"\\n            class=\\\"ant-upload-list-item-name\\\"\\n            [attr.title]=\\\"file.name\\\"\\n            [href]=\\\"file.url\\\"\\n            [attr.download]=\\\"file.linkProps && file.linkProps.download\\\"\\n            (click)=\\\"handlePreview(file, $event)\\\"\\n          >\\n            {{ file.name }}\\n          </a>\\n        } @else {\\n          <span class=\\\"ant-upload-list-item-name\\\" [attr.title]=\\\"file.name\\\" (click)=\\\"handlePreview(file, $event)\\\">\\n            {{ file.name }}\\n          </span>\\n        }\\n        <ng-template [ngTemplateOutlet]=\\\"downloadOrDelete\\\"></ng-template>\\n      </ng-template>\\n\\n      <div class=\\\"ant-upload-list-item-info\\\">\\n        <span class=\\\"ant-upload-span\\\">\\n          <ng-template [ngTemplateOutlet]=\\\"icon\\\"></ng-template>\\n          <ng-template [ngTemplateOutlet]=\\\"preview\\\"></ng-template>\\n        </span>\\n      </div>\\n      @if (listType === 'picture-card' && !file.isUploading) {\\n        <span class=\\\"ant-upload-list-item-actions\\\">\\n          @if (icons.showPreviewIcon) {\\n            <a\\n              [href]=\\\"file.url || file.thumbUrl\\\"\\n              target=\\\"_blank\\\"\\n              rel=\\\"noopener noreferrer\\\"\\n              [attr.title]=\\\"locale.previewFile\\\"\\n              [style]=\\\"!(file.url || file.thumbUrl) ? { opacity: 0.5, 'pointer-events': 'none' } : null\\\"\\n              (click)=\\\"handlePreview(file, $event)\\\"\\n            >\\n              <nz-icon nzType=\\\"eye\\\" />\\n            </a>\\n          }\\n          @if (file.status === 'done') {\\n            <ng-template [ngTemplateOutlet]=\\\"downloadIcon\\\"></ng-template>\\n          }\\n          <ng-template [ngTemplateOutlet]=\\\"removeIcon\\\"></ng-template>\\n        </span>\\n      }\\n      @if (file.isUploading) {\\n        <div class=\\\"ant-upload-list-item-progress\\\">\\n          <nz-progress [nzPercent]=\\\"file.percent!\\\" nzType=\\\"line\\\" [nzShowInfo]=\\\"false\\\" [nzStrokeWidth]=\\\"2\\\"></nz-progress>\\n        </div>\\n      }\\n    </div>\\n  </div>\\n}\\n\"\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1.Platform\n  }], {\n    locale: [{\n      type: Input\n    }],\n    listType: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    icons: [{\n      type: Input\n    }],\n    onPreview: [{\n      type: Input\n    }],\n    onRemove: [{\n      type: Input\n    }],\n    onDownload: [{\n      type: Input\n    }],\n    previewFile: [{\n      type: Input\n    }],\n    previewIsImage: [{\n      type: Input\n    }],\n    iconRender: [{\n      type: Input\n    }],\n    dir: [{\n      type: Input\n    }]\n  });\n})();\nclass NzUploadComponent {\n  cdr;\n  i18n;\n  directionality;\n  static ngAcceptInputType_nzShowUploadList;\n  destroy$ = new Subject();\n  uploadComp;\n  listComp;\n  locale;\n  dir = 'ltr';\n  // #region fields\n  nzType = 'select';\n  nzLimit = 0;\n  nzSize = 0;\n  nzFileType;\n  nzAccept;\n  nzAction;\n  nzDirectory = false;\n  nzOpenFileDialogOnClick = true;\n  nzBeforeUpload;\n  nzCustomRequest;\n  nzData;\n  nzFilter = [];\n  nzFileList = [];\n  nzDisabled = false;\n  nzHeaders;\n  nzListType = 'text';\n  nzMultiple = false;\n  nzName = 'file';\n  _showUploadList = true;\n  document = inject(DOCUMENT);\n  set nzShowUploadList(value) {\n    this._showUploadList = typeof value === 'boolean' ? toBoolean(value) : value;\n  }\n  get nzShowUploadList() {\n    return this._showUploadList;\n  }\n  nzShowButton = true;\n  nzWithCredentials = false;\n  nzRemove;\n  nzPreview;\n  nzPreviewFile;\n  nzPreviewIsImage;\n  nzTransformFile;\n  nzDownload;\n  nzIconRender = null;\n  nzFileListRender = null;\n  nzChange = new EventEmitter();\n  nzFileListChange = new EventEmitter();\n  _btnOptions;\n  zipOptions() {\n    if (typeof this.nzShowUploadList === 'boolean' && this.nzShowUploadList) {\n      this.nzShowUploadList = {\n        showPreviewIcon: true,\n        showRemoveIcon: true,\n        showDownloadIcon: true\n      };\n    }\n    // filters\n    const filters = this.nzFilter.slice();\n    if (this.nzMultiple && this.nzLimit > 0 && filters.findIndex(w => w.name === 'limit') === -1) {\n      filters.push({\n        name: 'limit',\n        fn: fileList => fileList.slice(-this.nzLimit)\n      });\n    }\n    if (this.nzSize > 0 && filters.findIndex(w => w.name === 'size') === -1) {\n      filters.push({\n        name: 'size',\n        fn: fileList => fileList.filter(w => w.size / 1024 <= this.nzSize)\n      });\n    }\n    if (this.nzFileType && this.nzFileType.length > 0 && filters.findIndex(w => w.name === 'type') === -1) {\n      const types = this.nzFileType.split(',');\n      filters.push({\n        name: 'type',\n        fn: fileList => fileList.filter(w => ~types.indexOf(w.type))\n      });\n    }\n    this._btnOptions = {\n      disabled: this.nzDisabled,\n      accept: this.nzAccept,\n      action: this.nzAction,\n      directory: this.nzDirectory,\n      openFileDialogOnClick: this.nzOpenFileDialogOnClick,\n      beforeUpload: this.nzBeforeUpload,\n      customRequest: this.nzCustomRequest,\n      data: this.nzData,\n      headers: this.nzHeaders,\n      name: this.nzName,\n      multiple: this.nzMultiple,\n      withCredentials: this.nzWithCredentials,\n      filters,\n      transformFile: this.nzTransformFile,\n      onStart: this.onStart,\n      onProgress: this.onProgress,\n      onSuccess: this.onSuccess,\n      onError: this.onError\n    };\n    return this;\n  }\n  platform = inject(Platform);\n  // #endregion\n  constructor(cdr, i18n, directionality) {\n    this.cdr = cdr;\n    this.i18n = i18n;\n    this.directionality = directionality;\n  }\n  // #region upload\n  fileToObject(file) {\n    return {\n      lastModified: file.lastModified,\n      lastModifiedDate: file.lastModifiedDate,\n      name: file.filename || file.name,\n      size: file.size,\n      type: file.type,\n      uid: file.uid,\n      response: file.response,\n      error: file.error,\n      percent: 0,\n      originFileObj: file\n    };\n  }\n  getFileItem(file, fileList) {\n    return fileList.filter(item => item.uid === file.uid)[0];\n  }\n  removeFileItem(file, fileList) {\n    return fileList.filter(item => item.uid !== file.uid);\n  }\n  onStart = file => {\n    if (!this.nzFileList) {\n      this.nzFileList = [];\n    }\n    const targetItem = this.fileToObject(file);\n    targetItem.status = 'uploading';\n    this.nzFileList = this.nzFileList.concat(targetItem);\n    this.nzFileListChange.emit(this.nzFileList);\n    this.nzChange.emit({\n      file: targetItem,\n      fileList: this.nzFileList,\n      type: 'start'\n    });\n    this.detectChangesList();\n  };\n  onProgress = (e, file) => {\n    const fileList = this.nzFileList;\n    const targetItem = this.getFileItem(file, fileList);\n    targetItem.percent = e.percent;\n    this.nzChange.emit({\n      event: e,\n      file: {\n        ...targetItem\n      },\n      fileList: this.nzFileList,\n      type: 'progress'\n    });\n    this.detectChangesList();\n  };\n  onSuccess = (res, file) => {\n    const fileList = this.nzFileList;\n    const targetItem = this.getFileItem(file, fileList);\n    targetItem.status = 'done';\n    targetItem.response = res;\n    this.nzChange.emit({\n      file: {\n        ...targetItem\n      },\n      fileList,\n      type: 'success'\n    });\n    this.detectChangesList();\n  };\n  onError = (err, file) => {\n    const fileList = this.nzFileList;\n    const targetItem = this.getFileItem(file, fileList);\n    targetItem.error = err;\n    targetItem.status = 'error';\n    this.nzChange.emit({\n      file: {\n        ...targetItem\n      },\n      fileList,\n      type: 'error'\n    });\n    this.detectChangesList();\n  };\n  // #endregion\n  // #region drag\n  dragState;\n  // skip safari bug\n  fileDrop(e) {\n    if (e.type === this.dragState) {\n      return;\n    }\n    this.dragState = e.type;\n    this.setClassMap();\n  }\n  // #endregion\n  // #region list\n  detectChangesList() {\n    this.cdr.detectChanges();\n    this.listComp?.detectChanges();\n  }\n  onRemove = file => {\n    this.uploadComp.abort(file);\n    file.status = 'removed';\n    const fnRes = typeof this.nzRemove === 'function' ? this.nzRemove(file) : this.nzRemove == null ? true : this.nzRemove;\n    (fnRes instanceof Observable ? fnRes : of(fnRes)).pipe(filter(res => res)).subscribe(() => {\n      this.nzFileList = this.removeFileItem(file, this.nzFileList);\n      this.nzChange.emit({\n        file,\n        fileList: this.nzFileList,\n        type: 'removed'\n      });\n      this.nzFileListChange.emit(this.nzFileList);\n      this.cdr.detectChanges();\n    });\n  };\n  // #endregion\n  // #region styles\n  prefixCls = 'ant-upload';\n  classList = [];\n  setClassMap() {\n    let subCls = [];\n    if (this.nzType === 'drag') {\n      if (this.nzFileList.some(file => file.status === 'uploading')) {\n        subCls.push(`${this.prefixCls}-drag-uploading`);\n      }\n      if (this.dragState === 'dragover') {\n        subCls.push(`${this.prefixCls}-drag-hover`);\n      }\n    } else {\n      subCls = [`${this.prefixCls}-select-${this.nzListType}`];\n    }\n    this.classList = [this.prefixCls, `${this.prefixCls}-${this.nzType}`, ...subCls, this.nzDisabled && `${this.prefixCls}-disabled` || '', this.dir === 'rtl' && `${this.prefixCls}-rtl` || ''].filter(item => !!item);\n    this.cdr.detectChanges();\n  }\n  // #endregion\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.setClassMap();\n      this.cdr.detectChanges();\n    });\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Upload');\n      this.detectChangesList();\n    });\n  }\n  ngAfterViewInit() {\n    if (this.platform.FIREFOX) {\n      // fix firefox drop open new tab\n      fromEventOutsideAngular(this.document.body, 'drop').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        event.preventDefault();\n        event.stopPropagation();\n      });\n    }\n  }\n  ngOnChanges() {\n    this.zipOptions().setClassMap();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzUploadComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzUploadComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.NzI18nService), i0.ɵɵdirectiveInject(i2$1.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzUploadComponent,\n    selectors: [[\"nz-upload\"]],\n    viewQuery: function NzUploadComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.uploadComp = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listComp = _t.first);\n      }\n    },\n    hostVars: 2,\n    hostBindings: function NzUploadComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-upload-picture-card-wrapper\", ctx.nzListType === \"picture-card\");\n      }\n    },\n    inputs: {\n      nzType: \"nzType\",\n      nzLimit: [2, \"nzLimit\", \"nzLimit\", numberAttribute],\n      nzSize: [2, \"nzSize\", \"nzSize\", numberAttribute],\n      nzFileType: \"nzFileType\",\n      nzAccept: \"nzAccept\",\n      nzAction: \"nzAction\",\n      nzDirectory: [2, \"nzDirectory\", \"nzDirectory\", booleanAttribute],\n      nzOpenFileDialogOnClick: [2, \"nzOpenFileDialogOnClick\", \"nzOpenFileDialogOnClick\", booleanAttribute],\n      nzBeforeUpload: \"nzBeforeUpload\",\n      nzCustomRequest: \"nzCustomRequest\",\n      nzData: \"nzData\",\n      nzFilter: \"nzFilter\",\n      nzFileList: \"nzFileList\",\n      nzDisabled: [2, \"nzDisabled\", \"nzDisabled\", booleanAttribute],\n      nzHeaders: \"nzHeaders\",\n      nzListType: \"nzListType\",\n      nzMultiple: [2, \"nzMultiple\", \"nzMultiple\", booleanAttribute],\n      nzName: \"nzName\",\n      nzShowUploadList: \"nzShowUploadList\",\n      nzShowButton: [2, \"nzShowButton\", \"nzShowButton\", booleanAttribute],\n      nzWithCredentials: [2, \"nzWithCredentials\", \"nzWithCredentials\", booleanAttribute],\n      nzRemove: \"nzRemove\",\n      nzPreview: \"nzPreview\",\n      nzPreviewFile: \"nzPreviewFile\",\n      nzPreviewIsImage: \"nzPreviewIsImage\",\n      nzTransformFile: \"nzTransformFile\",\n      nzDownload: \"nzDownload\",\n      nzIconRender: \"nzIconRender\",\n      nzFileListRender: \"nzFileListRender\"\n    },\n    outputs: {\n      nzChange: \"nzChange\",\n      nzFileListChange: \"nzFileListChange\"\n    },\n    exportAs: [\"nzUpload\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c2,\n    decls: 8,\n    vars: 1,\n    consts: [[\"list\", \"\"], [\"con\", \"\"], [\"btn\", \"\"], [\"listComp\", \"\"], [\"uploadComp\", \"\"], [3, \"display\", \"locale\", \"listType\", \"items\", \"icons\", \"iconRender\", \"previewFile\", \"previewIsImage\", \"onPreview\", \"onRemove\", \"onDownload\", \"dir\"], [3, \"locale\", \"listType\", \"items\", \"icons\", \"iconRender\", \"previewFile\", \"previewIsImage\", \"onPreview\", \"onRemove\", \"onDownload\", \"dir\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"nz-upload-btn\", \"\", 3, \"options\"], [3, \"ngTemplateOutlet\"], [3, \"drop\", \"dragover\", \"dragleave\"], [\"nz-upload-btn\", \"\", 1, \"ant-upload-btn\", 3, \"options\"], [1, \"ant-upload-drag-container\"]],\n    template: function NzUploadComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NzUploadComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzUploadComponent_ng_template_2_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(4, NzUploadComponent_ng_template_4_Template, 4, 6, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, NzUploadComponent_Conditional_6_Template, 6, 5)(7, NzUploadComponent_Conditional_7_Template, 2, 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵconditional(ctx.nzType === \"drag\" ? 6 : 7);\n      }\n    },\n    dependencies: [NzUploadListComponent, NgTemplateOutlet, NzUploadBtnComponent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-upload',\n      exportAs: 'nzUpload',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class.ant-upload-picture-card-wrapper]': 'nzListType === \"picture-card\"'\n      },\n      imports: [NzUploadListComponent, NgTemplateOutlet, NzUploadBtnComponent],\n      template: \"<ng-template #list>\\n  @if (locale && !nzFileListRender) {\\n    <nz-upload-list\\n      #listComp\\n      [style.display]=\\\"nzShowUploadList ? '' : 'none'\\\"\\n      [locale]=\\\"locale\\\"\\n      [listType]=\\\"nzListType\\\"\\n      [items]=\\\"nzFileList || []\\\"\\n      [icons]=\\\"$any(nzShowUploadList)\\\"\\n      [iconRender]=\\\"nzIconRender\\\"\\n      [previewFile]=\\\"nzPreviewFile\\\"\\n      [previewIsImage]=\\\"nzPreviewIsImage\\\"\\n      [onPreview]=\\\"nzPreview\\\"\\n      [onRemove]=\\\"onRemove\\\"\\n      [onDownload]=\\\"nzDownload\\\"\\n      [dir]=\\\"dir\\\"\\n    ></nz-upload-list>\\n  }\\n  @if (nzFileListRender) {\\n    <ng-container *ngTemplateOutlet=\\\"nzFileListRender; context: { $implicit: nzFileList }\\\"></ng-container>\\n  }\\n</ng-template>\\n<ng-template #con><ng-content></ng-content></ng-template>\\n<ng-template #btn>\\n  <div [class]=\\\"classList\\\" [style.display]=\\\"nzShowButton ? '' : 'none'\\\">\\n    <div nz-upload-btn #uploadComp [options]=\\\"_btnOptions!\\\">\\n      <ng-template [ngTemplateOutlet]=\\\"con\\\"></ng-template>\\n    </div>\\n  </div>\\n</ng-template>\\n@if (nzType === 'drag') {\\n  <div [class]=\\\"classList\\\" (drop)=\\\"fileDrop($event)\\\" (dragover)=\\\"fileDrop($event)\\\" (dragleave)=\\\"fileDrop($event)\\\">\\n    <div nz-upload-btn #uploadComp [options]=\\\"_btnOptions!\\\" class=\\\"ant-upload-btn\\\">\\n      <div class=\\\"ant-upload-drag-container\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"con\\\"></ng-template>\\n      </div>\\n    </div>\\n  </div>\\n  <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n} @else {\\n  @if (nzListType === 'picture-card') {\\n    <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n    <ng-template [ngTemplateOutlet]=\\\"btn\\\"></ng-template>\\n  } @else {\\n    <ng-template [ngTemplateOutlet]=\\\"btn\\\"></ng-template>\\n    <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n  }\\n}\\n\"\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$1.NzI18nService\n  }, {\n    type: i2$1.Directionality\n  }], {\n    uploadComp: [{\n      type: ViewChild,\n      args: ['uploadComp', {\n        static: false\n      }]\n    }],\n    listComp: [{\n      type: ViewChild,\n      args: ['listComp', {\n        static: false\n      }]\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzLimit: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    nzSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    nzFileType: [{\n      type: Input\n    }],\n    nzAccept: [{\n      type: Input\n    }],\n    nzAction: [{\n      type: Input\n    }],\n    nzDirectory: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzOpenFileDialogOnClick: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzBeforeUpload: [{\n      type: Input\n    }],\n    nzCustomRequest: [{\n      type: Input\n    }],\n    nzData: [{\n      type: Input\n    }],\n    nzFilter: [{\n      type: Input\n    }],\n    nzFileList: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzHeaders: [{\n      type: Input\n    }],\n    nzListType: [{\n      type: Input\n    }],\n    nzMultiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzName: [{\n      type: Input\n    }],\n    nzShowUploadList: [{\n      type: Input\n    }],\n    nzShowButton: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzWithCredentials: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzRemove: [{\n      type: Input\n    }],\n    nzPreview: [{\n      type: Input\n    }],\n    nzPreviewFile: [{\n      type: Input\n    }],\n    nzPreviewIsImage: [{\n      type: Input\n    }],\n    nzTransformFile: [{\n      type: Input\n    }],\n    nzDownload: [{\n      type: Input\n    }],\n    nzIconRender: [{\n      type: Input\n    }],\n    nzFileListRender: [{\n      type: Input\n    }],\n    nzChange: [{\n      type: Output\n    }],\n    nzFileListChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzUploadModule {\n  static ɵfac = function NzUploadModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzUploadModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzUploadModule,\n    imports: [NzUploadComponent, NzUploadBtnComponent, NzUploadListComponent],\n    exports: [NzUploadComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzUploadComponent, NzUploadListComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzUploadComponent, NzUploadBtnComponent, NzUploadListComponent],\n      exports: [NzUploadComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzUploadBtnComponent, NzUploadComponent, NzUploadListComponent, NzUploadModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,iBAAiB,EAAE;AAChC,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,SAAS;AAAA,EACT,kBAAkB;AACpB;AACA,SAAS,wEAAwE,IAAI,KAAK;AAAC;AAC3F,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,eAAe,EAAE;AACjH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,cAAiB,YAAY,CAAC;AACpC,IAAG,YAAY,6BAA6B,CAAC,QAAQ,WAAW;AAChE,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,WAAW,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,CAAC;AAAA,EAC/G;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAQ,aAAa;AACtE,IAAG,YAAY,OAAO,QAAQ,IAAI;AAAA,EACpC;AACF;AACA,SAAS,sFAAsF,IAAI,KAAK;AAAC;AACzG,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uFAAuF,GAAG,GAAG,eAAe,EAAE;AAAA,EACjI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,cAAiB,YAAY,CAAC;AACpC,IAAG,WAAW,oBAAoB,WAAW,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,CAAC;AAAA,EAC/G;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,6EAA6E,QAAQ;AACnH,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,SAAS,MAAM,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,yEAAyE,GAAG,GAAG,MAAM,EAAE;AACrM,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,YAAY,6BAA6B,CAAC,QAAQ,UAAU;AAC/D,IAAG,WAAW,QAAQ,QAAQ,OAAO,QAAQ,UAAa,aAAa;AACvE,IAAG,UAAU;AACb,IAAG,cAAc,QAAQ,aAAa,IAAI,CAAC;AAAA,EAC7C;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AAAC;AAC3F,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,eAAe,EAAE;AACjH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,cAAiB,YAAY,CAAC;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,WAAW,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,CAAC;AAAA,EAC/G;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,2DAA2D,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,2DAA2D,GAAG,GAAG,OAAO,EAAE;AAAA,EACzP;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,eAAe,WAAW,QAAQ,cAAc,cAAc,IAAI,aAAa,cAAc,IAAI,CAAC;AAAA,EACvG;AACF;AACA,SAAS,sFAAsF,IAAI,KAAK;AACtG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACF;AACA,SAAS,sFAAsF,IAAI,KAAK;AACtG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,UAAU,QAAQ,aAAa,YAAY,MAAM;AAAA,EACjE;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uFAAuF,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,uFAAuF,GAAG,GAAG,WAAW,EAAE;AAAA,EAC5O;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,cAAc,QAAQ,cAAc,IAAI,CAAC;AAAA,EAC9C;AACF;AACA,SAAS,sFAAsF,IAAI,KAAK;AACtG,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,mBAAmB,KAAK,OAAO,OAAO,WAAW,GAAG;AAAA,EACzD;AACF;AACA,SAAS,sFAAsF,IAAI,KAAK;AACtG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,UAAU,QAAQ,aAAa,YAAY,MAAM;AAAA,EACjE;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uFAAuF,GAAG,CAAC,EAAE,GAAG,uFAAuF,GAAG,GAAG,WAAW,EAAE;AAAA,EAC7N;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,cAAc,QAAQ,cAAc,IAAI,CAAC;AAAA,EAC9C;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,UAAU,QAAQ,cAAc,YAAY,YAAY;AAAA,EACxE;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yEAAyE,GAAG,CAAC,EAAE,GAAG,yEAAyE,GAAG,CAAC,EAAE,GAAG,yEAAyE,GAAG,GAAG,WAAW,EAAE;AAAA,EACnR;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,eAAe,WAAW,OAAO,cAAc,YAAY,IAAI,aAAa,iBAAiB,IAAI,CAAC;AAAA,EACvG;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAAC;AAClG,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gFAAgF,GAAG,GAAG,eAAe,EAAE;AAAA,EAC1H;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,UAAU,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,CAAC;AAAA,EACrH;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,kEAAkE,GAAG,CAAC,EAAE,GAAG,kEAAkE,GAAG,GAAG,MAAM,EAAE;AAAA,EAC9K;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,CAAC,OAAO,aAAa,IAAI,CAAC;AAAA,EAC7C;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,yFAAyF,QAAQ;AAC/H,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,SAAS,MAAM,CAAC;AAAA,IAC5D,CAAC;AACD,IAAG,UAAU,GAAG,WAAW,EAAE;AAC7B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,OAAO,OAAO,UAAU;AAAA,EAClD;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,UAAU,EAAE;AAAA,EACvG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,OAAO,MAAM,iBAAiB,IAAI,EAAE;AAAA,EACvD;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,2FAA2F;AACzH,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,OAAO,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,UAAU,GAAG,WAAW,EAAE;AAC7B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,OAAO,OAAO,YAAY;AAAA,EACpD;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,UAAU,EAAE;AAAA,EACvG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,cAAc,QAAQ,eAAe,IAAI,EAAE;AAAA,EAChD;AACF;AACA,SAAS,gFAAgF,IAAI,KAAK;AAAC;AACnG,SAAS,gFAAgF,IAAI,KAAK;AAAC;AACnG,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,WAAW,GAAG,iFAAiF,GAAG,GAAG,eAAe,EAAE,EAAE,GAAG,iFAAiF,GAAG,GAAG,eAAe,EAAE;AACtO,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,gBAAmB,YAAY,CAAC;AACtC,UAAM,kBAAqB,YAAY,CAAC;AACxC,UAAM,SAAY,cAAc;AAChC,IAAG,uBAAuB,sCAAsC,OAAO,aAAa,YAAY,YAAY,IAAI,EAAE;AAClH,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,eAAe;AACjD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,QAAQ,CAAC;AAAA,EACrG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,OAAO,aAAa,iBAAiB,IAAI,EAAE;AAAA,EAC9D;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,qFAAqF,QAAQ;AAC3H,MAAG,cAAc,IAAI;AACrB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,SAAS,MAAM,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,QAAQ,QAAQ,KAAQ,aAAa;AACnD,IAAG,YAAY,SAAS,QAAQ,IAAI,EAAE,YAAY,QAAQ,aAAa,QAAQ,UAAU,QAAQ;AACjG,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,QAAQ,MAAM,GAAG;AAAA,EAC9C;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,SAAS,SAAS,wFAAwF,QAAQ;AAC9H,MAAG,cAAc,IAAI;AACrB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,SAAS,MAAM,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,YAAY,SAAS,QAAQ,IAAI;AACpC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,QAAQ,MAAM,GAAG;AAAA,EAC9C;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAAC;AACrF,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,mEAAmE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,mEAAmE,GAAG,GAAG,eAAe,EAAE;AAAA,EAC1R;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,uBAA0B,YAAY,EAAE;AAC9C,IAAG,cAAc,QAAQ,MAAM,IAAI,CAAC;AACpC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,oBAAoB;AAAA,EACxD;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AAAC;AACvE,SAAS,oDAAoD,IAAI,KAAK;AAAC;AACvE,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,qFAAqF,QAAQ;AAC3H,MAAG,cAAc,IAAI;AACrB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,SAAS,MAAM,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,UAAU,GAAG,WAAW,EAAE;AAC7B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,EAAE,QAAQ,OAAO,QAAQ,YAAe,gBAAgB,GAAG,GAAG,IAAI,IAAI;AACpF,IAAG,WAAW,QAAQ,QAAQ,OAAO,QAAQ,UAAa,aAAa;AACvE,IAAG,YAAY,SAAS,OAAO,OAAO,WAAW;AAAA,EACnD;AACF;AACA,SAAS,gFAAgF,IAAI,KAAK;AAAC;AACnG,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,iFAAiF,GAAG,GAAG,eAAe,EAAE;AAAA,EAC3H;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,kBAAqB,YAAY,CAAC;AACxC,IAAG,WAAW,oBAAoB,eAAe;AAAA,EACnD;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAAC;AACrF,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,mEAAmE,GAAG,GAAG,MAAM,EAAE,EAAE,GAAG,mEAAmE,GAAG,GAAG,eAAe,EAAE;AACtR,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,gBAAmB,YAAY,CAAC;AACtC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,MAAM,kBAAkB,IAAI,EAAE;AACtD,IAAG,UAAU;AACb,IAAG,cAAc,QAAQ,WAAW,SAAS,IAAI,EAAE;AACnD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,eAAe,EAAE;AACjC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,QAAQ,OAAO,EAAE,cAAc,KAAK,EAAE,iBAAiB,CAAC;AAAA,EACrF;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,qDAAqD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,qDAAqD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAChrB,IAAG,eAAe,IAAI,OAAO,CAAC,EAAE,IAAI,QAAQ,CAAC;AAC7C,IAAG,WAAW,IAAI,qDAAqD,GAAG,GAAG,eAAe,EAAE,EAAE,IAAI,qDAAqD,GAAG,GAAG,eAAe,EAAE;AAChL,IAAG,aAAa,EAAE;AAClB,IAAG,WAAW,IAAI,qDAAqD,GAAG,GAAG,QAAQ,EAAE,EAAE,IAAI,qDAAqD,GAAG,GAAG,OAAO,EAAE;AACjK,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,WAAc,YAAY,CAAC;AACjC,UAAM,cAAiB,YAAY,EAAE;AACrC,UAAM,SAAY,cAAc;AAChC,IAAG,uBAAuB,oBAAoB,OAAO,UAAU,YAAY;AAC3E,IAAG,UAAU;AACb,IAAG,uBAAuB,8CAA8C,QAAQ,QAAQ,oCAAoC,OAAO,UAAU,EAAE;AAC/I,IAAG,WAAW,cAAc,MAAS,EAAE,kBAAkB,QAAQ,WAAW,UAAU,QAAQ,UAAU,IAAI;AAC5G,IAAG,YAAY,YAAY,QAAQ,GAAG;AACtC,IAAG,UAAU,EAAE;AACf,IAAG,WAAW,oBAAoB,QAAQ;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,WAAW;AAC7C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,aAAa,kBAAkB,CAAC,QAAQ,cAAc,KAAK,EAAE;AACrF,IAAG,UAAU;AACb,IAAG,cAAc,QAAQ,cAAc,KAAK,EAAE;AAAA,EAChD;AACF;AACA,IAAM,MAAM,CAAC,YAAY;AACzB,IAAM,MAAM,CAAC,UAAU;AACvB,IAAM,MAAM,MAAM,CAAC;AACnB,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,GAAG,CAAC;AAAA,EACxC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,WAAW,OAAO,mBAAmB,KAAK,MAAM;AAC/D,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,YAAY,OAAO,UAAU,EAAE,SAAS,OAAO,cAAiB,gBAAgB,IAAI,GAAG,CAAC,EAAE,SAAS,OAAO,gBAAgB,EAAE,cAAc,OAAO,YAAY,EAAE,eAAe,OAAO,aAAa,EAAE,kBAAkB,OAAO,gBAAgB,EAAE,aAAa,OAAO,SAAS,EAAE,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,OAAO,OAAO,GAAG;AAAA,EACtY;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,gBAAgB,CAAC;AAAA,EACjH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,gBAAgB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,UAAU,CAAC;AAAA,EACrI;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,IAAI,kBAAkB,CAAC,EAAE,GAAG,wDAAwD,GAAG,GAAG,cAAc;AAAA,EACtL;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,UAAU,CAAC,OAAO,mBAAmB,IAAI,EAAE;AACnE,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,mBAAmB,IAAI,EAAE;AAAA,EACnD;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,GAAG,CAAC;AAC1C,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,CAAC;AAC/F,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,SAAY,YAAY,CAAC;AAC/B,IAAG,WAAW,OAAO,SAAS;AAC9B,IAAG,YAAY,WAAW,OAAO,eAAe,KAAK,MAAM;AAC3D,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,WAAW;AAC3C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,MAAM;AAAA,EAC1C;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,QAAQ,SAAS,6DAA6D,QAAQ;AAClG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC,EAAE,YAAY,SAAS,iEAAiE,QAAQ;AAC/F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC,EAAE,aAAa,SAAS,kEAAkE,QAAQ;AACjG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC,EAAE,GAAG,OAAO,EAAE;AAC/C,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,CAAC;AAC/F,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,CAAC;AAAA,EACjG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,UAAa,YAAY,CAAC;AAChC,UAAM,SAAY,YAAY,CAAC;AAC/B,IAAG,WAAW,OAAO,SAAS;AAC9B,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,WAAW;AAC3C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,MAAM;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AAAC;AACxF,SAAS,qEAAqE,IAAI,KAAK;AAAC;AACxF,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,sEAAsE,GAAG,GAAG,eAAe,CAAC;AAAA,EAChN;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,UAAa,YAAY,CAAC;AAChC,UAAM,SAAY,YAAY,CAAC;AAC/B,IAAG,WAAW,oBAAoB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,MAAM;AAAA,EAC1C;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AAAC;AACxF,SAAS,qEAAqE,IAAI,KAAK;AAAC;AACxF,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,sEAAsE,GAAG,GAAG,eAAe,CAAC;AAAA,EAChN;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,UAAa,YAAY,CAAC;AAChC,UAAM,SAAY,YAAY,CAAC;AAC/B,IAAG,WAAW,oBAAoB,MAAM;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,CAAC,EAAE,GAAG,wDAAwD,GAAG,CAAC;AAAA,EAChJ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,eAAe,iBAAiB,IAAI,CAAC;AAAA,EAC/D;AACF;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB;AAAA,EACA,OAAO,CAAC;AAAA,EACR,UAAU;AAAA,EACV,WAAW,IAAI,QAAQ;AAAA,EACvB;AAAA,EACA;AAAA,EACA,UAAU;AACR,QAAI,KAAK,QAAQ,YAAY,CAAC,KAAK,QAAQ,uBAAuB;AAChE;AAAA,IACF;AACA,SAAK,KAAK,cAAc,MAAM;AAAA,EAChC;AAAA;AAAA,EAEA,WAAW,GAAG;AACZ,QAAI,KAAK,QAAQ,YAAY,EAAE,SAAS,YAAY;AAClD,QAAE,eAAe;AACjB;AAAA,IACF;AACA,QAAI,KAAK,QAAQ,WAAW;AAC1B,WAAK,iBAAiB,EAAE,aAAa,KAAK;AAAA,IAC5C,OAAO;AACL,YAAM,QAAQ,MAAM,UAAU,MAAM,KAAK,EAAE,aAAa,KAAK,EAAE,OAAO,UAAQ,KAAK,WAAW,MAAM,KAAK,QAAQ,MAAM,CAAC;AACxH,UAAI,MAAM,QAAQ;AAChB,aAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IACF;AACA,MAAE,eAAe;AAAA,EACnB;AAAA,EACA,SAAS,GAAG;AACV,QAAI,KAAK,QAAQ,UAAU;AACzB;AAAA,IACF;AACA,UAAM,MAAM,EAAE;AACd,SAAK,YAAY,IAAI,KAAK;AAC1B,QAAI,QAAQ;AAAA,EACd;AAAA,EACA,iBAAiB,OAAO;AACtB,UAAM,oBAAoB,CAAC,MAAM,SAAS;AACxC,UAAI,KAAK,QAAQ;AACf,aAAK,KAAK,UAAQ;AAChB,cAAI,KAAK,WAAW,MAAM,KAAK,QAAQ,MAAM,GAAG;AAC9C,iBAAK,YAAY,CAAC,IAAI,CAAC;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,MACH,WAAW,KAAK,aAAa;AAC3B,cAAM,YAAY,KAAK,aAAa;AACpC,kBAAU,YAAY,aAAW;AAC/B,qBAAW,cAAc,SAAS;AAChC,8BAAkB,YAAY,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG;AAAA,UACtD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,eAAW,QAAQ,OAAO;AACxB,wBAAkB,KAAK,iBAAiB,GAAG,EAAE;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,WAAW,MAAM,eAAe;AAC9B,QAAI,QAAQ,eAAe;AACzB,YAAM,qBAAqB,MAAM,QAAQ,aAAa,IAAI,gBAAgB,cAAc,MAAM,GAAG;AACjG,YAAM,WAAW,GAAG,KAAK,IAAI;AAC7B,YAAM,WAAW,GAAG,KAAK,IAAI;AAC7B,YAAM,eAAe,SAAS,QAAQ,SAAS,EAAE;AACjD,aAAO,mBAAmB,KAAK,UAAQ;AACrC,cAAM,YAAY,KAAK,KAAK;AAC5B,YAAI,UAAU,OAAO,CAAC,MAAM,KAAK;AAC/B,iBAAO,SAAS,YAAY,EAAE,QAAQ,UAAU,YAAY,GAAG,SAAS,YAAY,EAAE,SAAS,UAAU,YAAY,EAAE,MAAM,MAAM;AAAA,QACrI,WAAW,QAAQ,KAAK,SAAS,GAAG;AAElC,iBAAO,iBAAiB,UAAU,QAAQ,SAAS,EAAE;AAAA,QACvD;AACA,eAAO,aAAa;AAAA,MACtB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,MAAM;AACd,QAAI,CAAC,KAAK,KAAK;AACb,WAAK,MAAM,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC;AAAA,IACnD;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,UAAU;AACpB,QAAI,WAAW,GAAG,MAAM,UAAU,MAAM,KAAK,QAAQ,CAAC;AACtD,QAAI,KAAK,QAAQ,SAAS;AACxB,WAAK,QAAQ,QAAQ,QAAQ,OAAK;AAChC,mBAAW,SAAS,KAAK,UAAU,UAAQ;AACzC,gBAAM,QAAQ,EAAE,GAAG,IAAI;AACvB,iBAAO,iBAAiB,aAAa,QAAQ,GAAG,KAAK;AAAA,QACvD,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AACA,aAAS,UAAU;AAAA,MACjB,MAAM,UAAQ;AACZ,aAAK,QAAQ,UAAQ;AACnB,eAAK,UAAU,IAAI;AACnB,eAAK,OAAO,MAAM,IAAI;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,MACA,OAAO,OAAK;AACV,aAAK,iCAAiC,CAAC;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,MAAM,UAAU;AACrB,QAAI,CAAC,KAAK,QAAQ,cAAc;AAC9B,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB;AACA,UAAM,SAAS,KAAK,QAAQ,aAAa,MAAM,QAAQ;AACvD,QAAI,kBAAkB,YAAY;AAChC,aAAO,UAAU;AAAA,QACf,MAAM,mBAAiB;AACrB,gBAAM,oBAAoB,OAAO,UAAU,SAAS,KAAK,aAAa;AACtE,cAAI,sBAAsB,mBAAmB,sBAAsB,iBAAiB;AAClF,iBAAK,UAAU,aAAa;AAC5B,iBAAK,KAAK,aAAa;AAAA,UACzB,WAAW,eAAe;AACxB,iBAAK,KAAK,IAAI;AAAA,UAChB;AAAA,QACF;AAAA,QACA,OAAO,OAAK;AACV,eAAK,uCAAuC,CAAC;AAAA,QAC/C;AAAA,MACF,CAAC;AAAA,IACH,WAAW,QAAQ;AACjB,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB;AAAA,EACF;AAAA,EACA,KAAK,MAAM;AACT,QAAI,KAAK,SAAS;AAChB;AAAA,IACF;AACA,QAAI,WAAW,GAAG,IAAI;AACtB,QAAI;AACJ,UAAM,MAAM,KAAK;AACjB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO;AAAA,MACX,QAAQ,OAAO,WAAW,WAAW,SAAS;AAAA,MAC9C,MAAM,IAAI;AAAA,MACV;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA,iBAAiB,IAAI;AAAA,MACrB,YAAY,IAAI,aAAa,OAAK;AAChC,YAAI,WAAW,GAAG,IAAI;AAAA,MACxB,IAAI;AAAA,MACJ,WAAW,CAAC,KAAK,QAAQ;AACvB,aAAK,MAAM,GAAG;AACd,YAAI,UAAU,KAAK,MAAM,GAAG;AAAA,MAC9B;AAAA,MACA,SAAS,SAAO;AACd,aAAK,MAAM,GAAG;AACd,YAAI,QAAQ,KAAK,IAAI;AAAA,MACvB;AAAA,IACF;AACA,QAAI,OAAO,WAAW,YAAY;AAChC,YAAM,eAAe,OAAO,IAAI;AAChC,UAAI,wBAAwB,YAAY;AACtC,mBAAW,SAAS,KAAK,UAAU,MAAM,YAAY,GAAG,IAAI,SAAO;AACjE,eAAK,SAAS;AACd,iBAAO;AAAA,QACT,CAAC,CAAC;AAAA,MACJ,OAAO;AACL,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AACA,QAAI,OAAO,kBAAkB,YAAY;AACvC,YAAM,kBAAkB,cAAc,IAAI;AAC1C,iBAAW,SAAS,KAAK,UAAU,MAAM,2BAA2B,aAAa,kBAAkB,GAAG,eAAe,CAAC,GAAG,IAAI,aAAW,kBAAkB,OAAO,CAAC;AAAA,IACpK;AACA,QAAI,OAAO,SAAS,YAAY;AAC9B,YAAM,aAAa,KAAK,IAAI;AAC5B,UAAI,sBAAsB,YAAY;AACpC,mBAAW,SAAS,KAAK,UAAU,MAAM,UAAU,GAAG,IAAI,SAAO;AAC/D,eAAK,OAAO;AACZ,iBAAO,mBAAmB;AAAA,QAC5B,CAAC,CAAC;AAAA,MACJ,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AACA,QAAI,OAAO,YAAY,YAAY;AACjC,YAAM,gBAAgB,QAAQ,IAAI;AAClC,UAAI,yBAAyB,YAAY;AACvC,mBAAW,SAAS,KAAK,UAAU,MAAM,aAAa,GAAG,IAAI,SAAO;AAClE,eAAK,UAAU;AACf,iBAAO,mBAAmB;AAAA,QAC5B,CAAC,CAAC;AAAA,MACJ,OAAO;AACL,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AACA,aAAS,UAAU,aAAW;AAC5B,WAAK,WAAW;AAChB,YAAM,QAAQ,IAAI,iBAAiB,KAAK,KAAK,KAAK,MAAM,IAAI;AAC5D,UAAI,EAAE,gBAAgB,eAAe;AACnC,aAAK,+DAA+D;AAAA,MACtE;AACA,WAAK,KAAK,GAAG,IAAI;AACjB,UAAI,QAAQ,IAAI;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,IAAI,MAAM;AACR,UAAM,WAAW,IAAI,SAAS;AAC9B,QAAI,KAAK,MAAM;AACb,aAAO,KAAK,KAAK,IAAI,EAAE,IAAI,SAAO;AAChC,iBAAS,OAAO,KAAK,KAAK,KAAK,GAAG,CAAC;AAAA,MACrC,CAAC;AAAA,IACH;AACA,aAAS,OAAO,KAAK,MAAM,KAAK,QAAQ;AACxC,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU,CAAC;AAAA,IAClB;AACA,QAAI,KAAK,QAAQ,kBAAkB,MAAM,MAAM;AAC7C,WAAK,QAAQ,kBAAkB,IAAI;AAAA,IACrC,OAAO;AACL,aAAO,KAAK,QAAQ,kBAAkB;AAAA,IACxC;AACA,UAAM,MAAM,IAAI,YAAY,QAAQ,KAAK,QAAQ,UAAU;AAAA,MACzD,gBAAgB;AAAA,MAChB,iBAAiB,KAAK;AAAA,MACtB,SAAS,IAAI,YAAY,KAAK,OAAO;AAAA,IACvC,CAAC;AACD,WAAO,KAAK,KAAK,QAAQ,GAAG,EAAE,UAAU;AAAA,MACtC,MAAM,WAAS;AACb,YAAI,MAAM,SAAS,cAAc,gBAAgB;AAC/C,cAAI,MAAM,QAAQ,GAAG;AACnB,kBAAM,UAAU,MAAM,SAAS,MAAM,QAAQ;AAAA,UAC/C;AACA,eAAK,WAAW,OAAO,KAAK,IAAI;AAAA,QAClC,WAAW,iBAAiB,cAAc;AACxC,eAAK,UAAU,MAAM,MAAM,KAAK,MAAM,KAAK;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,OAAO,SAAO;AACZ,aAAK,MAAM,KAAK,IAAI;AACpB,aAAK,QAAQ,KAAK,KAAK,IAAI;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,MAAM,KAAK;AACT,UAAM,OAAO,KAAK,KAAK,GAAG;AAC1B,QAAI,gBAAgB,cAAc;AAChC,WAAK,YAAY;AAAA,IACnB;AACA,WAAO,KAAK,KAAK,GAAG;AAAA,EACtB;AAAA,EACA,MAAM,MAAM;AACV,QAAI,MAAM;AACR,WAAK,MAAM,QAAQ,KAAK,GAAG;AAAA,IAC7B,OAAO;AACL,aAAO,KAAK,KAAK,IAAI,EAAE,QAAQ,SAAO,KAAK,MAAM,GAAG,CAAC;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO,OAAO,YAAY;AAAA,IACxB,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,YAAY;AACtB,SAAK,aAAa;AAClB,QAAI,CAAC,KAAK,MAAM;AACd,YAAM,IAAI,MAAM,wGAAwG;AAAA,IAC1H;AAAA,EACF;AAAA,EACA,WAAW;AAGT,4BAAwB,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,QAAQ,CAAC;AAC7H,4BAAwB,KAAK,WAAW,eAAe,SAAS,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAClH,UAAI,KAAK,QAAQ,UAAU;AACzB;AAAA,MACF;AACA,UAAI,MAAM,QAAQ,WAAW,MAAM,YAAY,OAAO;AACpD,aAAK,QAAQ;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,SAAS,KAAK;AACnB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,UAAU,CAAC;AAAA,EAC5F;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACrC,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,OAAO,GAAG;AAAA,MAC7D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,YAAY;AAAA,IAC3B,UAAU;AAAA,IACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,SAAS,6CAA6C,QAAQ;AAClF,iBAAO,IAAI,WAAW,MAAM;AAAA,QAC9B,CAAC,EAAE,YAAY,SAAS,iDAAiD,QAAQ;AAC/E,iBAAO,IAAI,WAAW,MAAM;AAAA,QAC9B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,YAAY,GAAG,EAAE,QAAQ,QAAQ;AAChD,QAAG,YAAY,uBAAuB,IAAI,QAAQ,QAAQ;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,QAAQ,GAAG,UAAU,UAAU,CAAC;AAAA,IAChE,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,QAAG,WAAW,UAAU,SAAS,sDAAsD,QAAQ;AAC7F,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,SAAS,MAAM,CAAC;AAAA,QAC5C,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,aAAa,CAAC;AAAA,MACnB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,WAAW,MAAM;AAChC,QAAG,WAAW,YAAY,IAAI,QAAQ,QAAQ;AAC9C,QAAG,YAAY,UAAU,IAAI,QAAQ,MAAM,EAAE,aAAa,IAAI,QAAQ,YAAY,cAAc,IAAI,EAAE,mBAAmB,IAAI,QAAQ,YAAY,oBAAoB,IAAI;AAAA,MAC3K;AAAA,IACF;AAAA,IACA,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,+BAA+B;AAAA,QAC/B,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,MACA,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,kBAAkB,UAAQ,CAAC,CAAC,QAAQ,KAAK,QAAQ,QAAQ,MAAM;AACrE,IAAM,eAAe;AACrB,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,CAAC;AAAA,EACR,IAAI,UAAU;AACZ,WAAO,KAAK,aAAa,aAAa,KAAK,aAAa;AAAA,EAC1D;AAAA,EACA,SAAS,CAAC;AAAA,EACV;AAAA,EACA,IAAI,MAAM,MAAM;AACd,SAAK,OAAO;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW,OAAO,QAAQ;AAAA,EAC1B,WAAW,IAAI,QAAQ;AAAA,EACvB,OAAO,MAAM;AACX,QAAI,KAAK,YAAY,OAAO,KAAK,aAAa,UAAU;AACtD,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,SAAS,KAAK,MAAM,cAAc,KAAK,OAAO;AAAA,EAC5D;AAAA,EACA,QAAQ,KAAK;AACX,UAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,UAAM,WAAW,KAAK,KAAK,SAAS,CAAC;AACrC,UAAM,wBAAwB,SAAS,MAAM,MAAM,EAAE,CAAC;AACtD,YAAQ,cAAc,KAAK,qBAAqB,KAAK,CAAC,EAAE,GAAG,CAAC;AAAA,EAC9D;AAAA,EACA,WAAW,MAAM;AACf,QAAI,gBAAgB,KAAK,IAAI,GAAG;AAC9B,aAAO;AAAA,IACT;AACA,UAAM,MAAM,KAAK,YAAY,KAAK,OAAO;AACzC,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,UAAM,YAAY,KAAK,QAAQ,GAAG;AAClC,QAAI,gBAAgB,KAAK,GAAG,KAAK,6CAA6C,KAAK,SAAS,GAAG;AAC7F,aAAO;AAAA,IACT,WAAW,SAAS,KAAK,GAAG,GAAG;AAE7B,aAAO;AAAA,IACT,WAAW,WAAW;AAEpB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,MAAM;AAChB,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,eAAe,CAAC,KAAK,YAAY,CAAC,KAAK,KAAK;AACnD,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa,MAAM;AACjB,QAAI,CAAC,gBAAgB,KAAK,IAAI,KAAK,CAAC,KAAK,SAAS,WAAW;AAC3D,aAAO,GAAG,EAAE;AAAA,IACd;AACA,UAAM,SAAS,KAAK,SAAS,cAAc,QAAQ;AACnD,WAAO,QAAQ;AACf,WAAO,SAAS;AAChB,WAAO,MAAM,UAAU,4CAA4C,YAAY,eAAe,YAAY;AAC1G,SAAK,SAAS,KAAK,YAAY,MAAM;AACrC,UAAM,MAAM,OAAO,WAAW,IAAI;AAClC,UAAM,MAAM,IAAI,MAAM;AACtB,UAAM,YAAY,IAAI,gBAAgB,IAAI;AAC1C,QAAI,MAAM;AACV,WAAO,UAAU,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM;AAC3C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,YAAY;AAChB,UAAI,aAAa;AACjB,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,QAAQ,QAAQ;AAClB,qBAAa,UAAU,eAAe;AACtC,kBAAU,EAAE,aAAa,aAAa;AAAA,MACxC,OAAO;AACL,oBAAY,SAAS,eAAe;AACpC,kBAAU,EAAE,YAAY,cAAc;AAAA,MACxC;AACA,UAAI;AACF,YAAI,UAAU,KAAK,SAAS,SAAS,WAAW,UAAU;AAAA,MAC5D,QAAQ;AAAA,MAER;AACA,YAAM,UAAU,OAAO,UAAU;AACjC,WAAK,SAAS,KAAK,YAAY,MAAM;AACrC,UAAI,gBAAgB,SAAS;AAC7B,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,SAAS,WAAW;AAC5B;AAAA,IACF;AACA,UAAM,MAAM;AACZ,QAAI,CAAC,KAAK,WAAW,OAAO,aAAa,eAAe,OAAO,QAAQ,eAAe,CAAC,IAAI,cAAc,CAAC,IAAI,MAAM;AAClH;AAAA,IACF;AACA,SAAK,KAAK,OAAO,UAAQ,KAAK,yBAAyB,QAAQ,KAAK,aAAa,MAAS,EAAE,QAAQ,UAAQ;AAC1G,WAAK,WAAW;AAIhB,YAAM,YAAY,KAAK,cAAc,KAAK,YAAY,IAAI,IAAI,KAAK,aAAa,KAAK,aAAa,GAAG,KAAK,UAAU,KAAK,QAAQ,CAAC;AAClI,WAAK,OAAO,kBAAkB,MAAM;AAClC,iBAAS,UAAU,aAAW;AAC5B,eAAK,OAAO,IAAI,MAAM;AACpB,iBAAK,WAAW;AAChB,iBAAK,cAAc;AAAA,UACrB,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,CAAC,EAAE,KAAK,MAAM,oBAAoB,KAAK,WAAW;AAAA,EAC3D;AAAA,EACA,UAAU;AACR,SAAK,KAAK,QAAQ,UAAQ;AACxB,WAAK,cAAc,KAAK,WAAW;AACnC,WAAK,UAAU,KAAK,OAAO,IAAI;AAC/B,WAAK,YAAY,OAAO,KAAK,cAAc,WAAW,KAAK,MAAM,KAAK,SAAS,IAAI,KAAK;AACxF,WAAK,aAAa,KAAK,iBAAiB,KAAK,eAAe,IAAI,IAAI,KAAK,WAAW,IAAI;AACxF,WAAK,WAAW,KAAK,YAAY,IAAI;AACrC,WAAK,eAAe,KAAK,aAAa,IAAI;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EACA,cAAc,MAAM,GAAG;AACrB,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,MAAE,eAAe;AACjB,WAAO,KAAK,UAAU,IAAI;AAAA,EAC5B;AAAA,EACA,aAAa,MAAM,GAAG;AACpB,MAAE,eAAe;AACjB,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,IAAI;AAAA,IACpB;AACA;AAAA,EACF;AAAA,EACA,eAAe,MAAM;AACnB,QAAI,OAAO,KAAK,eAAe,YAAY;AACzC,WAAK,WAAW,IAAI;AAAA,IACtB,WAAW,KAAK,KAAK;AACnB,aAAO,KAAK,KAAK,GAAG;AAAA,IACtB;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,KAAK,QAAQ,UAAU;AACjC,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,gBAAgB;AACd,SAAK,QAAQ;AACb,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAA0B,kBAAqB,iBAAiB,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,QAAQ,CAAC;AAAA,EACxK;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,WAAW,CAAC,GAAG,iBAAiB;AAAA,IAChC,UAAU;AAAA,IACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,uBAAuB,IAAI,QAAQ,KAAK,EAAE,wBAAwB,IAAI,aAAa,MAAM,EAAE,2BAA2B,IAAI,aAAa,SAAS,EAAE,gCAAgC,IAAI,aAAa,cAAc;AAAA,MAClO;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,KAAK;AAAA,IACP;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,cAAc,IAAI,GAAG,gBAAgB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,GAAG,kCAAkC,GAAG,2BAA2B,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,GAAG,kCAAkC,GAAG,6BAA6B,MAAM,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,GAAG,kCAAkC,GAAG,SAAS,MAAM,GAAG,CAAC,GAAG,8BAA8B,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,UAAU,SAAS,GAAG,CAAC,WAAW,WAAW,GAAG,QAAQ,GAAG,CAAC,QAAQ,UAAU,aAAa,IAAI,UAAU,QAAQ,UAAU,SAAS,GAAG,uCAAuC,GAAG,CAAC,QAAQ,UAAU,aAAa,IAAI,UAAU,QAAQ,UAAU,SAAS,GAAG,yCAAyC,GAAG,OAAO,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,UAAU,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,GAAG,6BAA6B,GAAG,MAAM,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,GAAG,6BAA6B,GAAG,SAAS,MAAM,GAAG,CAAC,GAAG,6BAA6B,GAAG,OAAO,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,GAAG,QAAQ,OAAO,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,GAAG,SAAS,MAAM,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,QAAQ,GAAG,aAAa,cAAc,eAAe,CAAC;AAAA,IACzpD,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,iBAAiB,GAAG,sCAAsC,IAAI,IAAI,OAAO,GAAM,yBAAyB;AAAA,MAC7G;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iBAAoB,oBAAoB,kBAAkB,cAAiB,iBAAiB,gBAAmB,mBAAsB,4BAA6B,kBAAqB,mBAAmB;AAAA,IACzN,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,aAAa,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QAC5D,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC,GAAG,QAAQ,KAAK,MAAM;AAAA,QACrB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,KAAK,MAAM;AAAA,QAC9C,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY,CAAC,QAAQ,aAAa,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QAC7D,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC,GAAG,QAAQ,KAAK,MAAM;AAAA,QACrB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,KAAK,MAAM;AAAA,QAC9C,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MACP,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,+BAA+B;AAAA,QAC/B,gCAAgC;AAAA,QAChC,mCAAmC;AAAA,QACnC,wCAAwC;AAAA,MAC1C;AAAA,MACA,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,iBAAiB,kBAAkB,cAAc,gBAAgB,gBAAgB;AAAA,MAC3F,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,WAAW,IAAI,QAAQ;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA;AAAA,EAEN,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd,0BAA0B;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW,CAAC;AAAA,EACZ,aAAa,CAAC;AAAA,EACd,aAAa;AAAA,EACb;AAAA,EACA,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,WAAW,OAAO,QAAQ;AAAA,EAC1B,IAAI,iBAAiB,OAAO;AAC1B,SAAK,kBAAkB,OAAO,UAAU,YAAY,UAAU,KAAK,IAAI;AAAA,EACzE;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,WAAW,IAAI,aAAa;AAAA,EAC5B,mBAAmB,IAAI,aAAa;AAAA,EACpC;AAAA,EACA,aAAa;AACX,QAAI,OAAO,KAAK,qBAAqB,aAAa,KAAK,kBAAkB;AACvE,WAAK,mBAAmB;AAAA,QACtB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,MACpB;AAAA,IACF;AAEA,UAAM,UAAU,KAAK,SAAS,MAAM;AACpC,QAAI,KAAK,cAAc,KAAK,UAAU,KAAK,QAAQ,UAAU,OAAK,EAAE,SAAS,OAAO,MAAM,IAAI;AAC5F,cAAQ,KAAK;AAAA,QACX,MAAM;AAAA,QACN,IAAI,cAAY,SAAS,MAAM,CAAC,KAAK,OAAO;AAAA,MAC9C,CAAC;AAAA,IACH;AACA,QAAI,KAAK,SAAS,KAAK,QAAQ,UAAU,OAAK,EAAE,SAAS,MAAM,MAAM,IAAI;AACvE,cAAQ,KAAK;AAAA,QACX,MAAM;AAAA,QACN,IAAI,cAAY,SAAS,OAAO,OAAK,EAAE,OAAO,QAAQ,KAAK,MAAM;AAAA,MACnE,CAAC;AAAA,IACH;AACA,QAAI,KAAK,cAAc,KAAK,WAAW,SAAS,KAAK,QAAQ,UAAU,OAAK,EAAE,SAAS,MAAM,MAAM,IAAI;AACrG,YAAM,QAAQ,KAAK,WAAW,MAAM,GAAG;AACvC,cAAQ,KAAK;AAAA,QACX,MAAM;AAAA,QACN,IAAI,cAAY,SAAS,OAAO,OAAK,CAAC,MAAM,QAAQ,EAAE,IAAI,CAAC;AAAA,MAC7D,CAAC;AAAA,IACH;AACA,SAAK,cAAc;AAAA,MACjB,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,uBAAuB,KAAK;AAAA,MAC5B,cAAc,KAAK;AAAA,MACnB,eAAe,KAAK;AAAA,MACpB,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,MAAM,KAAK;AAAA,MACX,UAAU,KAAK;AAAA,MACf,iBAAiB,KAAK;AAAA,MACtB;AAAA,MACA,eAAe,KAAK;AAAA,MACpB,SAAS,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,MAChB,SAAS,KAAK;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,OAAO,QAAQ;AAAA;AAAA,EAE1B,YAAY,KAAK,MAAM,gBAAgB;AACrC,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,aAAa,MAAM;AACjB,WAAO;AAAA,MACL,cAAc,KAAK;AAAA,MACnB,kBAAkB,KAAK;AAAA,MACvB,MAAM,KAAK,YAAY,KAAK;AAAA,MAC5B,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,KAAK,KAAK;AAAA,MACV,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,YAAY,MAAM,UAAU;AAC1B,WAAO,SAAS,OAAO,UAAQ,KAAK,QAAQ,KAAK,GAAG,EAAE,CAAC;AAAA,EACzD;AAAA,EACA,eAAe,MAAM,UAAU;AAC7B,WAAO,SAAS,OAAO,UAAQ,KAAK,QAAQ,KAAK,GAAG;AAAA,EACtD;AAAA,EACA,UAAU,UAAQ;AAChB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,CAAC;AAAA,IACrB;AACA,UAAM,aAAa,KAAK,aAAa,IAAI;AACzC,eAAW,SAAS;AACpB,SAAK,aAAa,KAAK,WAAW,OAAO,UAAU;AACnD,SAAK,iBAAiB,KAAK,KAAK,UAAU;AAC1C,SAAK,SAAS,KAAK;AAAA,MACjB,MAAM;AAAA,MACN,UAAU,KAAK;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AACD,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,aAAa,CAAC,GAAG,SAAS;AACxB,UAAM,WAAW,KAAK;AACtB,UAAM,aAAa,KAAK,YAAY,MAAM,QAAQ;AAClD,eAAW,UAAU,EAAE;AACvB,SAAK,SAAS,KAAK;AAAA,MACjB,OAAO;AAAA,MACP,MAAM,mBACD;AAAA,MAEL,UAAU,KAAK;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AACD,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,YAAY,CAAC,KAAK,SAAS;AACzB,UAAM,WAAW,KAAK;AACtB,UAAM,aAAa,KAAK,YAAY,MAAM,QAAQ;AAClD,eAAW,SAAS;AACpB,eAAW,WAAW;AACtB,SAAK,SAAS,KAAK;AAAA,MACjB,MAAM,mBACD;AAAA,MAEL;AAAA,MACA,MAAM;AAAA,IACR,CAAC;AACD,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,UAAU,CAAC,KAAK,SAAS;AACvB,UAAM,WAAW,KAAK;AACtB,UAAM,aAAa,KAAK,YAAY,MAAM,QAAQ;AAClD,eAAW,QAAQ;AACnB,eAAW,SAAS;AACpB,SAAK,SAAS,KAAK;AAAA,MACjB,MAAM,mBACD;AAAA,MAEL;AAAA,MACA,MAAM;AAAA,IACR,CAAC;AACD,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA,EAGA;AAAA;AAAA,EAEA,SAAS,GAAG;AACV,QAAI,EAAE,SAAS,KAAK,WAAW;AAC7B;AAAA,IACF;AACA,SAAK,YAAY,EAAE;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA,EAGA,oBAAoB;AAClB,SAAK,IAAI,cAAc;AACvB,SAAK,UAAU,cAAc;AAAA,EAC/B;AAAA,EACA,WAAW,UAAQ;AACjB,SAAK,WAAW,MAAM,IAAI;AAC1B,SAAK,SAAS;AACd,UAAM,QAAQ,OAAO,KAAK,aAAa,aAAa,KAAK,SAAS,IAAI,IAAI,KAAK,YAAY,OAAO,OAAO,KAAK;AAC9G,KAAC,iBAAiB,aAAa,QAAQ,GAAG,KAAK,GAAG,KAAK,OAAO,SAAO,GAAG,CAAC,EAAE,UAAU,MAAM;AACzF,WAAK,aAAa,KAAK,eAAe,MAAM,KAAK,UAAU;AAC3D,WAAK,SAAS,KAAK;AAAA,QACjB;AAAA,QACA,UAAU,KAAK;AAAA,QACf,MAAM;AAAA,MACR,CAAC;AACD,WAAK,iBAAiB,KAAK,KAAK,UAAU;AAC1C,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA,EAGA,YAAY;AAAA,EACZ,YAAY,CAAC;AAAA,EACb,cAAc;AACZ,QAAI,SAAS,CAAC;AACd,QAAI,KAAK,WAAW,QAAQ;AAC1B,UAAI,KAAK,WAAW,KAAK,UAAQ,KAAK,WAAW,WAAW,GAAG;AAC7D,eAAO,KAAK,GAAG,KAAK,SAAS,iBAAiB;AAAA,MAChD;AACA,UAAI,KAAK,cAAc,YAAY;AACjC,eAAO,KAAK,GAAG,KAAK,SAAS,aAAa;AAAA,MAC5C;AAAA,IACF,OAAO;AACL,eAAS,CAAC,GAAG,KAAK,SAAS,WAAW,KAAK,UAAU,EAAE;AAAA,IACzD;AACA,SAAK,YAAY,CAAC,KAAK,WAAW,GAAG,KAAK,SAAS,IAAI,KAAK,MAAM,IAAI,GAAG,QAAQ,KAAK,cAAc,GAAG,KAAK,SAAS,eAAe,IAAI,KAAK,QAAQ,SAAS,GAAG,KAAK,SAAS,UAAU,EAAE,EAAE,OAAO,UAAQ,CAAC,CAAC,IAAI;AAClN,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,YAAY;AACjB,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpE,WAAK,SAAS,KAAK,KAAK,cAAc,QAAQ;AAC9C,WAAK,kBAAkB;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,SAAS,SAAS;AAEzB,8BAAwB,KAAK,SAAS,MAAM,MAAM,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AACpG,cAAM,eAAe;AACrB,cAAM,gBAAgB;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,EAAE,YAAY;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAsB,kBAAqB,iBAAiB,GAAM,kBAAuB,aAAa,GAAM,kBAAuB,cAAc,CAAC;AAAA,EACrL;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,MACjE;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,mCAAmC,IAAI,eAAe,cAAc;AAAA,MACrF;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,CAAC,GAAG,WAAW,WAAW,eAAe;AAAA,MAClD,QAAQ,CAAC,GAAG,UAAU,UAAU,eAAe;AAAA,MAC/C,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,MAC/D,yBAAyB,CAAC,GAAG,2BAA2B,2BAA2B,gBAAgB;AAAA,MACnG,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,MAClE,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,MACjF,UAAU;AAAA,MACV,WAAW;AAAA,MACX,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,kBAAkB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU,CAAC,UAAU;AAAA,IACrB,UAAU,CAAI,oBAAoB;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,GAAG,WAAW,UAAU,YAAY,SAAS,SAAS,cAAc,eAAe,kBAAkB,aAAa,YAAY,cAAc,KAAK,GAAG,CAAC,GAAG,UAAU,YAAY,SAAS,SAAS,cAAc,eAAe,kBAAkB,aAAa,YAAY,cAAc,KAAK,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,iBAAiB,IAAI,GAAG,SAAS,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,QAAQ,YAAY,WAAW,GAAG,CAAC,iBAAiB,IAAI,GAAG,kBAAkB,GAAG,SAAS,GAAG,CAAC,GAAG,2BAA2B,CAAC;AAAA,IACxmB,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,0CAA0C,GAAG,CAAC,EAAE,GAAG,0CAA0C,GAAG,CAAC;AAAA,MACta;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,WAAW,SAAS,IAAI,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,uBAAuB,kBAAkB,oBAAoB;AAAA,IAC5E,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,2CAA2C;AAAA,MAC7C;AAAA,MACA,SAAS,CAAC,uBAAuB,kBAAkB,oBAAoB;AAAA,MACvE,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,mBAAmB,sBAAsB,qBAAqB;AAAA,IACxE,SAAS,CAAC,iBAAiB;AAAA,EAC7B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,mBAAmB,qBAAqB;AAAA,EACpD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,mBAAmB,sBAAsB,qBAAqB;AAAA,MACxE,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}