import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-loading',
    imports: [CommonModule],
    template: `
    <div class="loading-wrapper">
      <div class="loading-content">
        <div class="loading">
          <div></div>
          <div></div>
          <div></div>
        </div>
        <div class="loading-text">{{ text }}</div>
      </div>
    </div>
  `,
    styles: [`
    .loading-wrapper {
      position: absolute;
      top: 50%; 
      left: 50%;
      transform: translate(-50%, -50%);
      width: auto;
      height: auto;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    
    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      padding: 24px;
      border-radius: 8px;
      background: #fff;
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    }

    .loading {
      display: flex;
      gap: 8px;
    }
    
    .loading div {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #1890ff;
      animation: bounce 0.5s ease-in-out infinite;
    }
    
    .loading div:nth-child(2) {
      animation-delay: 0.1s;
    }
    
    .loading div:nth-child(3) {
      animation-delay: 0.2s;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
    }
    
    @keyframes bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-10px); }
    }
  `]
})
export class LoadingComponent {
  @Input() text = '加载中...';
}