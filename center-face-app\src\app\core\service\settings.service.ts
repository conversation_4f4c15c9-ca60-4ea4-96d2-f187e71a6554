import { Injectable } from "@angular/core";
import { LocalStorageService } from "./local-storage.service";

export interface CameraSetting {
  resolution: string;
  mirror: boolean;
  frame: DetectFrameOption;
}

export type DetectFrameOption = 'small' | 'middle' | 'large';

const DEFAULT_SETTINGS: Partial<CameraSetting> = {
  // deviceId: 'default',
  // resolution: 'Responsive',
  // mirror: true,
  // frame: 'small'
};

@Injectable({
  providedIn: "root",
})
export class SettingsService {
  private cameraSettings: Partial<CameraSetting> | null = null;

  constructor(private localStorageService: LocalStorageService) {}

  getCameraSetting(): Partial<CameraSetting> {
    if (this.cameraSettings === null) {
      const settings = this.localStorageService.get("camera-settings");
      this.cameraSettings = settings || DEFAULT_SETTINGS;
    }
    return this.cameraSettings;
  }

  saveCameraSetting(settings: Partial<CameraSetting>): void {
    const currentSettings = this.getCameraSetting();
    this.cameraSettings = { ...currentSettings, ...settings };
    this.localStorageService.set("camera-settings", this.cameraSettings);
  }

  resetCameraSetting(): void {
    this.cameraSettings = DEFAULT_SETTINGS;
    this.localStorageService.remove("camera-settings");
  }
} 