{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@angular+service-worker@19.2.9_@angular+core@19.2.9_rxjs@7.8.1_zone.js@0.15.0__rxjs@7.8.1/node_modules/@angular/service-worker/fesm2022/service-worker.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ApplicationRef, Injectable, makeEnvironmentProviders, InjectionToken, Injector, provideAppInitializer, inject, NgZone, NgModule } from '@angular/core';\nimport { Observable, Subject, NEVER } from 'rxjs';\nimport { switchMap, take, filter, map } from 'rxjs/operators';\nconst ERR_SW_NOT_SUPPORTED = 'Service workers are disabled or not supported by this browser';\n/**\n * @publicApi\n */\nclass NgswCommChannel {\n  serviceWorker;\n  worker;\n  registration;\n  events;\n  constructor(serviceWorker, injector) {\n    this.serviceWorker = serviceWorker;\n    if (!serviceWorker) {\n      this.worker = this.events = this.registration = new Observable(subscriber => subscriber.error(new Error(ERR_SW_NOT_SUPPORTED)));\n    } else {\n      let currentWorker = null;\n      const workerSubject = new Subject();\n      this.worker = new Observable(subscriber => {\n        if (currentWorker !== null) {\n          subscriber.next(currentWorker);\n        }\n        return workerSubject.subscribe(v => subscriber.next(v));\n      });\n      const updateController = () => {\n        const {\n          controller\n        } = serviceWorker;\n        if (controller === null) {\n          return;\n        }\n        currentWorker = controller;\n        workerSubject.next(currentWorker);\n      };\n      serviceWorker.addEventListener('controllerchange', updateController);\n      updateController();\n      this.registration = this.worker.pipe(switchMap(() => serviceWorker.getRegistration()));\n      const _events = new Subject();\n      this.events = _events.asObservable();\n      const messageListener = event => {\n        const {\n          data\n        } = event;\n        if (data?.type) {\n          _events.next(data);\n        }\n      };\n      serviceWorker.addEventListener('message', messageListener);\n      // The injector is optional to avoid breaking changes.\n      const appRef = injector?.get(ApplicationRef, null, {\n        optional: true\n      });\n      appRef?.onDestroy(() => {\n        serviceWorker.removeEventListener('controllerchange', updateController);\n        serviceWorker.removeEventListener('message', messageListener);\n      });\n    }\n  }\n  postMessage(action, payload) {\n    return new Promise(resolve => {\n      this.worker.pipe(take(1)).subscribe(sw => {\n        sw.postMessage({\n          action,\n          ...payload\n        });\n        resolve();\n      });\n    });\n  }\n  postMessageWithOperation(type, payload, operationNonce) {\n    const waitForOperationCompleted = this.waitForOperationCompleted(operationNonce);\n    const postMessage = this.postMessage(type, payload);\n    return Promise.all([postMessage, waitForOperationCompleted]).then(([, result]) => result);\n  }\n  generateNonce() {\n    return Math.round(Math.random() * 10000000);\n  }\n  eventsOfType(type) {\n    let filterFn;\n    if (typeof type === 'string') {\n      filterFn = event => event.type === type;\n    } else {\n      filterFn = event => type.includes(event.type);\n    }\n    return this.events.pipe(filter(filterFn));\n  }\n  nextEventOfType(type) {\n    return this.eventsOfType(type).pipe(take(1));\n  }\n  waitForOperationCompleted(nonce) {\n    return new Promise((resolve, reject) => {\n      this.eventsOfType('OPERATION_COMPLETED').pipe(filter(event => event.nonce === nonce), take(1), map(event => {\n        if (event.result !== undefined) {\n          return event.result;\n        }\n        throw new Error(event.error);\n      })).subscribe({\n        next: resolve,\n        error: reject\n      });\n    });\n  }\n  get isEnabled() {\n    return !!this.serviceWorker;\n  }\n}\n\n/**\n * Subscribe and listen to\n * [Web Push\n * Notifications](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices) through\n * Angular Service Worker.\n *\n * @usageNotes\n *\n * You can inject a `SwPush` instance into any component or service\n * as a dependency.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"inject-sw-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * To subscribe, call `SwPush.requestSubscription()`, which asks the user for permission.\n * The call returns a `Promise` with a new\n * [`PushSubscription`](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n * instance.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * A request is rejected if the user denies permission, or if the browser\n * blocks or does not support the Push API or ServiceWorkers.\n * Check `SwPush.isEnabled` to confirm status.\n *\n * Invoke Push Notifications by pushing a message with the following payload.\n *\n * ```ts\n * {\n *   \"notification\": {\n *     \"actions\": NotificationAction[],\n *     \"badge\": USVString,\n *     \"body\": DOMString,\n *     \"data\": any,\n *     \"dir\": \"auto\"|\"ltr\"|\"rtl\",\n *     \"icon\": USVString,\n *     \"image\": USVString,\n *     \"lang\": DOMString,\n *     \"renotify\": boolean,\n *     \"requireInteraction\": boolean,\n *     \"silent\": boolean,\n *     \"tag\": DOMString,\n *     \"timestamp\": DOMTimeStamp,\n *     \"title\": DOMString,\n *     \"vibrate\": number[]\n *   }\n * }\n * ```\n *\n * Only `title` is required. See `Notification`\n * [instance\n * properties](https://developer.mozilla.org/en-US/docs/Web/API/Notification#Instance_properties).\n *\n * While the subscription is active, Service Worker listens for\n * [PushEvent](https://developer.mozilla.org/en-US/docs/Web/API/PushEvent)\n * occurrences and creates\n * [Notification](https://developer.mozilla.org/en-US/docs/Web/API/Notification)\n * instances in response.\n *\n * Unsubscribe using `SwPush.unsubscribe()`.\n *\n * An application can subscribe to `SwPush.notificationClicks` observable to be notified when a user\n * clicks on a notification. For example:\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-notification-clicks\"\n * header=\"app.component.ts\"></code-example>\n *\n * You can read more on handling notification clicks in the [Service worker notifications\n * guide](ecosystem/service-workers/push-notifications).\n *\n * @see [Push Notifications](https://developers.google.com/web/fundamentals/codelabs/push-notifications/)\n * @see [Angular Push Notifications](https://blog.angular-university.io/angular-push-notifications/)\n * @see [MDN: Push API](https://developer.mozilla.org/en-US/docs/Web/API/Push_API)\n * @see [MDN: Notifications API](https://developer.mozilla.org/en-US/docs/Web/API/Notifications_API)\n * @see [MDN: Web Push API Notifications best practices](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices)\n *\n * @publicApi\n */\nclass SwPush {\n  sw;\n  /**\n   * Emits the payloads of the received push notification messages.\n   */\n  messages;\n  /**\n   * Emits the payloads of the received push notification messages as well as the action the user\n   * interacted with. If no action was used the `action` property contains an empty string `''`.\n   *\n   * Note that the `notification` property does **not** contain a\n   * [Notification][Mozilla Notification] object but rather a\n   * [NotificationOptions](https://notifications.spec.whatwg.org/#dictdef-notificationoptions)\n   * object that also includes the `title` of the [Notification][Mozilla Notification] object.\n   *\n   * [Mozilla Notification]: https://developer.mozilla.org/en-US/docs/Web/API/Notification\n   */\n  notificationClicks;\n  /**\n   * Emits the currently active\n   * [PushSubscription](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n   * associated to the Service Worker registration or `null` if there is no subscription.\n   */\n  subscription;\n  /**\n   * True if the Service Worker is enabled (supported by the browser and enabled via\n   * `ServiceWorkerModule`).\n   */\n  get isEnabled() {\n    return this.sw.isEnabled;\n  }\n  pushManager = null;\n  subscriptionChanges = new Subject();\n  constructor(sw) {\n    this.sw = sw;\n    if (!sw.isEnabled) {\n      this.messages = NEVER;\n      this.notificationClicks = NEVER;\n      this.subscription = NEVER;\n      return;\n    }\n    this.messages = this.sw.eventsOfType('PUSH').pipe(map(message => message.data));\n    this.notificationClicks = this.sw.eventsOfType('NOTIFICATION_CLICK').pipe(map(message => message.data));\n    this.pushManager = this.sw.registration.pipe(map(registration => registration.pushManager));\n    const workerDrivenSubscriptions = this.pushManager.pipe(switchMap(pm => pm.getSubscription()));\n    this.subscription = new Observable(subscriber => {\n      const workerDrivenSubscription = workerDrivenSubscriptions.subscribe(subscriber);\n      const subscriptionChanges = this.subscriptionChanges.subscribe(subscriber);\n      return () => {\n        workerDrivenSubscription.unsubscribe();\n        subscriptionChanges.unsubscribe();\n      };\n    });\n  }\n  /**\n   * Subscribes to Web Push Notifications,\n   * after requesting and receiving user permission.\n   *\n   * @param options An object containing the `serverPublicKey` string.\n   * @returns A Promise that resolves to the new subscription object.\n   */\n  requestSubscription(options) {\n    if (!this.sw.isEnabled || this.pushManager === null) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const pushOptions = {\n      userVisibleOnly: true\n    };\n    let key = this.decodeBase64(options.serverPublicKey.replace(/_/g, '/').replace(/-/g, '+'));\n    let applicationServerKey = new Uint8Array(new ArrayBuffer(key.length));\n    for (let i = 0; i < key.length; i++) {\n      applicationServerKey[i] = key.charCodeAt(i);\n    }\n    pushOptions.applicationServerKey = applicationServerKey;\n    return new Promise((resolve, reject) => {\n      this.pushManager.pipe(switchMap(pm => pm.subscribe(pushOptions)), take(1)).subscribe({\n        next: sub => {\n          this.subscriptionChanges.next(sub);\n          resolve(sub);\n        },\n        error: reject\n      });\n    });\n  }\n  /**\n   * Unsubscribes from Service Worker push notifications.\n   *\n   * @returns A Promise that is resolved when the operation succeeds, or is rejected if there is no\n   *          active subscription or the unsubscribe operation fails.\n   */\n  unsubscribe() {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const doUnsubscribe = sub => {\n      if (sub === null) {\n        throw new Error('Not subscribed to push notifications.');\n      }\n      return sub.unsubscribe().then(success => {\n        if (!success) {\n          throw new Error('Unsubscribe failed!');\n        }\n        this.subscriptionChanges.next(null);\n      });\n    };\n    return new Promise((resolve, reject) => {\n      this.subscription.pipe(take(1), switchMap(doUnsubscribe)).subscribe({\n        next: resolve,\n        error: reject\n      });\n    });\n  }\n  decodeBase64(input) {\n    return atob(input);\n  }\n  static ɵfac = function SwPush_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SwPush)(i0.ɵɵinject(NgswCommChannel));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SwPush,\n    factory: SwPush.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SwPush, [{\n    type: Injectable\n  }], () => [{\n    type: NgswCommChannel\n  }], null);\n})();\n\n/**\n * Subscribe to update notifications from the Service Worker, trigger update\n * checks, and forcibly activate updates.\n *\n * @see {@link /ecosystem/service-workers/communications Service Worker Communication Guide}\n *\n * @publicApi\n */\nclass SwUpdate {\n  sw;\n  /**\n   * Emits a `VersionDetectedEvent` event whenever a new version is detected on the server.\n   *\n   * Emits a `VersionInstallationFailedEvent` event whenever checking for or downloading a new\n   * version fails.\n   *\n   * Emits a `VersionReadyEvent` event whenever a new version has been downloaded and is ready for\n   * activation.\n   */\n  versionUpdates;\n  /**\n   * Emits an `UnrecoverableStateEvent` event whenever the version of the app used by the service\n   * worker to serve this client is in a broken state that cannot be recovered from without a full\n   * page reload.\n   */\n  unrecoverable;\n  /**\n   * True if the Service Worker is enabled (supported by the browser and enabled via\n   * `ServiceWorkerModule`).\n   */\n  get isEnabled() {\n    return this.sw.isEnabled;\n  }\n  constructor(sw) {\n    this.sw = sw;\n    if (!sw.isEnabled) {\n      this.versionUpdates = NEVER;\n      this.unrecoverable = NEVER;\n      return;\n    }\n    this.versionUpdates = this.sw.eventsOfType(['VERSION_DETECTED', 'VERSION_INSTALLATION_FAILED', 'VERSION_READY', 'NO_NEW_VERSION_DETECTED']);\n    this.unrecoverable = this.sw.eventsOfType('UNRECOVERABLE_STATE');\n  }\n  /**\n   * Checks for an update and waits until the new version is downloaded from the server and ready\n   * for activation.\n   *\n   * @returns a promise that\n   * - resolves to `true` if a new version was found and is ready to be activated.\n   * - resolves to `false` if no new version was found\n   * - rejects if any error occurs\n   */\n  checkForUpdate() {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const nonce = this.sw.generateNonce();\n    return this.sw.postMessageWithOperation('CHECK_FOR_UPDATES', {\n      nonce\n    }, nonce);\n  }\n  /**\n   * Updates the current client (i.e. browser tab) to the latest version that is ready for\n   * activation.\n   *\n   * In most cases, you should not use this method and instead should update a client by reloading\n   * the page.\n   *\n   * <div class=\"docs-alert docs-alert-important\">\n   *\n   * Updating a client without reloading can easily result in a broken application due to a version\n   * mismatch between the application shell and other page resources,\n   * such as lazy-loaded chunks, whose filenames may change between\n   * versions.\n   *\n   * Only use this method, if you are certain it is safe for your specific use case.\n   *\n   * </div>\n   *\n   * @returns a promise that\n   *  - resolves to `true` if an update was activated successfully\n   *  - resolves to `false` if no update was available (for example, the client was already on the\n   *    latest version).\n   *  - rejects if any error occurs\n   */\n  activateUpdate() {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const nonce = this.sw.generateNonce();\n    return this.sw.postMessageWithOperation('ACTIVATE_UPDATE', {\n      nonce\n    }, nonce);\n  }\n  static ɵfac = function SwUpdate_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SwUpdate)(i0.ɵɵinject(NgswCommChannel));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SwUpdate,\n    factory: SwUpdate.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SwUpdate, [{\n    type: Injectable\n  }], () => [{\n    type: NgswCommChannel\n  }], null);\n})();\n\n/*!\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nconst SCRIPT = new InjectionToken(ngDevMode ? 'NGSW_REGISTER_SCRIPT' : '');\nfunction ngswAppInitializer() {\n  if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n    return;\n  }\n  const options = inject(SwRegistrationOptions);\n  if (!('serviceWorker' in navigator && options.enabled !== false)) {\n    return;\n  }\n  const script = inject(SCRIPT);\n  const ngZone = inject(NgZone);\n  const appRef = inject(ApplicationRef);\n  // Set up the `controllerchange` event listener outside of\n  // the Angular zone to avoid unnecessary change detections,\n  // as this event has no impact on view updates.\n  ngZone.runOutsideAngular(() => {\n    // Wait for service worker controller changes, and fire an INITIALIZE action when a new SW\n    // becomes active. This allows the SW to initialize itself even if there is no application\n    // traffic.\n    const sw = navigator.serviceWorker;\n    const onControllerChange = () => sw.controller?.postMessage({\n      action: 'INITIALIZE'\n    });\n    sw.addEventListener('controllerchange', onControllerChange);\n    appRef.onDestroy(() => {\n      sw.removeEventListener('controllerchange', onControllerChange);\n    });\n  });\n  // Run outside the Angular zone to avoid preventing the app from stabilizing (especially\n  // given that some registration strategies wait for the app to stabilize).\n  ngZone.runOutsideAngular(() => {\n    let readyToRegister;\n    const {\n      registrationStrategy\n    } = options;\n    if (typeof registrationStrategy === 'function') {\n      readyToRegister = new Promise(resolve => registrationStrategy().subscribe(() => resolve()));\n    } else {\n      const [strategy, ...args] = (registrationStrategy || 'registerWhenStable:30000').split(':');\n      switch (strategy) {\n        case 'registerImmediately':\n          readyToRegister = Promise.resolve();\n          break;\n        case 'registerWithDelay':\n          readyToRegister = delayWithTimeout(+args[0] || 0);\n          break;\n        case 'registerWhenStable':\n          readyToRegister = Promise.race([appRef.whenStable(), delayWithTimeout(+args[0])]);\n          break;\n        default:\n          // Unknown strategy.\n          throw new Error(`Unknown ServiceWorker registration strategy: ${options.registrationStrategy}`);\n      }\n    }\n    // Don't return anything to avoid blocking the application until the SW is registered.\n    // Catch and log the error if SW registration fails to avoid uncaught rejection warning.\n    readyToRegister.then(() => navigator.serviceWorker.register(script, {\n      scope: options.scope\n    }).catch(err => console.error('Service worker registration failed with:', err)));\n  });\n}\nfunction delayWithTimeout(timeout) {\n  return new Promise(resolve => setTimeout(resolve, timeout));\n}\nfunction ngswCommChannelFactory(opts, injector) {\n  const isBrowser = !(typeof ngServerMode !== 'undefined' && ngServerMode);\n  return new NgswCommChannel(isBrowser && opts.enabled !== false ? navigator.serviceWorker : undefined, injector);\n}\n/**\n * Token that can be used to provide options for `ServiceWorkerModule` outside of\n * `ServiceWorkerModule.register()`.\n *\n * You can use this token to define a provider that generates the registration options at runtime,\n * for example via a function call:\n *\n * {@example service-worker/registration-options/module.ts region=\"registration-options\"\n *     header=\"app.module.ts\"}\n *\n * @publicApi\n */\nclass SwRegistrationOptions {\n  /**\n   * Whether the ServiceWorker will be registered and the related services (such as `SwPush` and\n   * `SwUpdate`) will attempt to communicate and interact with it.\n   *\n   * Default: true\n   */\n  enabled;\n  /**\n   * A URL that defines the ServiceWorker's registration scope; that is, what range of URLs it can\n   * control. It will be used when calling\n   * [ServiceWorkerContainer#register()](https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register).\n   */\n  scope;\n  /**\n   * Defines the ServiceWorker registration strategy, which determines when it will be registered\n   * with the browser.\n   *\n   * The default behavior of registering once the application stabilizes (i.e. as soon as there are\n   * no pending micro- and macro-tasks) is designed to register the ServiceWorker as soon as\n   * possible but without affecting the application's first time load.\n   *\n   * Still, there might be cases where you want more control over when the ServiceWorker is\n   * registered (for example, there might be a long-running timeout or polling interval, preventing\n   * the app from stabilizing). The available option are:\n   *\n   * - `registerWhenStable:<timeout>`: Register as soon as the application stabilizes (no pending\n   *     micro-/macro-tasks) but no later than `<timeout>` milliseconds. If the app hasn't\n   *     stabilized after `<timeout>` milliseconds (for example, due to a recurrent asynchronous\n   *     task), the ServiceWorker will be registered anyway.\n   *     If `<timeout>` is omitted, the ServiceWorker will only be registered once the app\n   *     stabilizes.\n   * - `registerImmediately`: Register immediately.\n   * - `registerWithDelay:<timeout>`: Register with a delay of `<timeout>` milliseconds. For\n   *     example, use `registerWithDelay:5000` to register the ServiceWorker after 5 seconds. If\n   *     `<timeout>` is omitted, is defaults to `0`, which will register the ServiceWorker as soon\n   *     as possible but still asynchronously, once all pending micro-tasks are completed.\n   * - An Observable factory function: A function that returns an `Observable`.\n   *     The function will be used at runtime to obtain and subscribe to the `Observable` and the\n   *     ServiceWorker will be registered as soon as the first value is emitted.\n   *\n   * Default: 'registerWhenStable:30000'\n   */\n  registrationStrategy;\n}\n/**\n * @publicApi\n *\n * Sets up providers to register the given Angular Service Worker script.\n *\n * If `enabled` is set to `false` in the given options, the module will behave as if service\n * workers are not supported by the browser, and the service worker will not be registered.\n *\n * Example usage:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideServiceWorker('ngsw-worker.js')\n *   ],\n * });\n * ```\n */\nfunction provideServiceWorker(script, options = {}) {\n  return makeEnvironmentProviders([SwPush, SwUpdate, {\n    provide: SCRIPT,\n    useValue: script\n  }, {\n    provide: SwRegistrationOptions,\n    useValue: options\n  }, {\n    provide: NgswCommChannel,\n    useFactory: ngswCommChannelFactory,\n    deps: [SwRegistrationOptions, Injector]\n  }, provideAppInitializer(ngswAppInitializer)]);\n}\n\n/**\n * @publicApi\n */\nclass ServiceWorkerModule {\n  /**\n   * Register the given Angular Service Worker script.\n   *\n   * If `enabled` is set to `false` in the given options, the module will behave as if service\n   * workers are not supported by the browser, and the service worker will not be registered.\n   */\n  static register(script, options = {}) {\n    return {\n      ngModule: ServiceWorkerModule,\n      providers: [provideServiceWorker(script, options)]\n    };\n  }\n  static ɵfac = function ServiceWorkerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ServiceWorkerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ServiceWorkerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [SwPush, SwUpdate]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ServiceWorkerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [SwPush, SwUpdate]\n    }]\n  }], null, null);\n})();\nexport { ServiceWorkerModule, SwPush, SwRegistrationOptions, SwUpdate, provideServiceWorker };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,uBAAuB;AAI7B,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,eAAe,UAAU;AACnC,SAAK,gBAAgB;AACrB,QAAI,CAAC,eAAe;AAClB,WAAK,SAAS,KAAK,SAAS,KAAK,eAAe,IAAI,WAAW,gBAAc,WAAW,MAAM,IAAI,MAAM,oBAAoB,CAAC,CAAC;AAAA,IAChI,OAAO;AACL,UAAI,gBAAgB;AACpB,YAAM,gBAAgB,IAAI,QAAQ;AAClC,WAAK,SAAS,IAAI,WAAW,gBAAc;AACzC,YAAI,kBAAkB,MAAM;AAC1B,qBAAW,KAAK,aAAa;AAAA,QAC/B;AACA,eAAO,cAAc,UAAU,OAAK,WAAW,KAAK,CAAC,CAAC;AAAA,MACxD,CAAC;AACD,YAAM,mBAAmB,MAAM;AAC7B,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,eAAe,MAAM;AACvB;AAAA,QACF;AACA,wBAAgB;AAChB,sBAAc,KAAK,aAAa;AAAA,MAClC;AACA,oBAAc,iBAAiB,oBAAoB,gBAAgB;AACnE,uBAAiB;AACjB,WAAK,eAAe,KAAK,OAAO,KAAK,UAAU,MAAM,cAAc,gBAAgB,CAAC,CAAC;AACrF,YAAM,UAAU,IAAI,QAAQ;AAC5B,WAAK,SAAS,QAAQ,aAAa;AACnC,YAAM,kBAAkB,WAAS;AAC/B,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,MAAM,MAAM;AACd,kBAAQ,KAAK,IAAI;AAAA,QACnB;AAAA,MACF;AACA,oBAAc,iBAAiB,WAAW,eAAe;AAEzD,YAAM,SAAS,UAAU,IAAI,gBAAgB,MAAM;AAAA,QACjD,UAAU;AAAA,MACZ,CAAC;AACD,cAAQ,UAAU,MAAM;AACtB,sBAAc,oBAAoB,oBAAoB,gBAAgB;AACtE,sBAAc,oBAAoB,WAAW,eAAe;AAAA,MAC9D,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,QAAQ,SAAS;AAC3B,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,OAAO,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,QAAM;AACxC,WAAG,YAAY;AAAA,UACb;AAAA,WACG,QACJ;AACD,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,yBAAyB,MAAM,SAAS,gBAAgB;AACtD,UAAM,4BAA4B,KAAK,0BAA0B,cAAc;AAC/E,UAAM,cAAc,KAAK,YAAY,MAAM,OAAO;AAClD,WAAO,QAAQ,IAAI,CAAC,aAAa,yBAAyB,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,MAAM,MAAM;AAAA,EAC1F;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,MAAM,KAAK,OAAO,IAAI,GAAQ;AAAA,EAC5C;AAAA,EACA,aAAa,MAAM;AACjB,QAAI;AACJ,QAAI,OAAO,SAAS,UAAU;AAC5B,iBAAW,WAAS,MAAM,SAAS;AAAA,IACrC,OAAO;AACL,iBAAW,WAAS,KAAK,SAAS,MAAM,IAAI;AAAA,IAC9C;AACA,WAAO,KAAK,OAAO,KAAK,OAAO,QAAQ,CAAC;AAAA,EAC1C;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,KAAK,aAAa,IAAI,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,EAC7C;AAAA,EACA,0BAA0B,OAAO;AAC/B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,WAAK,aAAa,qBAAqB,EAAE,KAAK,OAAO,WAAS,MAAM,UAAU,KAAK,GAAG,KAAK,CAAC,GAAG,IAAI,WAAS;AAC1G,YAAI,MAAM,WAAW,QAAW;AAC9B,iBAAO,MAAM;AAAA,QACf;AACA,cAAM,IAAI,MAAM,MAAM,KAAK;AAAA,MAC7B,CAAC,CAAC,EAAE,UAAU;AAAA,QACZ,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,IAAI,YAAY;AACd,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AACF;AAiFA,IAAM,UAAN,MAAM,QAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,KAAK,GAAG;AAAA,EACjB;AAAA,EACA,cAAc;AAAA,EACd,sBAAsB,IAAI,QAAQ;AAAA,EAClC,YAAY,IAAI;AACd,SAAK,KAAK;AACV,QAAI,CAAC,GAAG,WAAW;AACjB,WAAK,WAAW;AAChB,WAAK,qBAAqB;AAC1B,WAAK,eAAe;AACpB;AAAA,IACF;AACA,SAAK,WAAW,KAAK,GAAG,aAAa,MAAM,EAAE,KAAK,IAAI,aAAW,QAAQ,IAAI,CAAC;AAC9E,SAAK,qBAAqB,KAAK,GAAG,aAAa,oBAAoB,EAAE,KAAK,IAAI,aAAW,QAAQ,IAAI,CAAC;AACtG,SAAK,cAAc,KAAK,GAAG,aAAa,KAAK,IAAI,kBAAgB,aAAa,WAAW,CAAC;AAC1F,UAAM,4BAA4B,KAAK,YAAY,KAAK,UAAU,QAAM,GAAG,gBAAgB,CAAC,CAAC;AAC7F,SAAK,eAAe,IAAI,WAAW,gBAAc;AAC/C,YAAM,2BAA2B,0BAA0B,UAAU,UAAU;AAC/E,YAAM,sBAAsB,KAAK,oBAAoB,UAAU,UAAU;AACzE,aAAO,MAAM;AACX,iCAAyB,YAAY;AACrC,4BAAoB,YAAY;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,SAAS;AAC3B,QAAI,CAAC,KAAK,GAAG,aAAa,KAAK,gBAAgB,MAAM;AACnD,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;AAAA,IACvD;AACA,UAAM,cAAc;AAAA,MAClB,iBAAiB;AAAA,IACnB;AACA,QAAI,MAAM,KAAK,aAAa,QAAQ,gBAAgB,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG,CAAC;AACzF,QAAI,uBAAuB,IAAI,WAAW,IAAI,YAAY,IAAI,MAAM,CAAC;AACrE,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,2BAAqB,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,IAC5C;AACA,gBAAY,uBAAuB;AACnC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,WAAK,YAAY,KAAK,UAAU,QAAM,GAAG,UAAU,WAAW,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU;AAAA,QACnF,MAAM,SAAO;AACX,eAAK,oBAAoB,KAAK,GAAG;AACjC,kBAAQ,GAAG;AAAA,QACb;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,QAAI,CAAC,KAAK,GAAG,WAAW;AACtB,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;AAAA,IACvD;AACA,UAAM,gBAAgB,SAAO;AAC3B,UAAI,QAAQ,MAAM;AAChB,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AACA,aAAO,IAAI,YAAY,EAAE,KAAK,aAAW;AACvC,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,qBAAqB;AAAA,QACvC;AACA,aAAK,oBAAoB,KAAK,IAAI;AAAA,MACpC,CAAC;AAAA,IACH;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,WAAK,aAAa,KAAK,KAAK,CAAC,GAAG,UAAU,aAAa,CAAC,EAAE,UAAU;AAAA,QAClE,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,aAAa,OAAO;AAClB,WAAO,KAAK,KAAK;AAAA,EACnB;AAQF;AAPE,cAnHI,SAmHG,QAAO,SAAS,eAAe,mBAAmB;AACvD,SAAO,KAAK,qBAAqB,SAAW,SAAS,eAAe,CAAC;AACvE;AACA,cAtHI,SAsHG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,QAAO;AAClB,CAAC;AAzHH,IAAM,SAAN;AAAA,CA2HC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAUH,IAAM,YAAN,MAAM,UAAS;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,KAAK,GAAG;AAAA,EACjB;AAAA,EACA,YAAY,IAAI;AACd,SAAK,KAAK;AACV,QAAI,CAAC,GAAG,WAAW;AACjB,WAAK,iBAAiB;AACtB,WAAK,gBAAgB;AACrB;AAAA,IACF;AACA,SAAK,iBAAiB,KAAK,GAAG,aAAa,CAAC,oBAAoB,+BAA+B,iBAAiB,yBAAyB,CAAC;AAC1I,SAAK,gBAAgB,KAAK,GAAG,aAAa,qBAAqB;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB;AACf,QAAI,CAAC,KAAK,GAAG,WAAW;AACtB,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;AAAA,IACvD;AACA,UAAM,QAAQ,KAAK,GAAG,cAAc;AACpC,WAAO,KAAK,GAAG,yBAAyB,qBAAqB;AAAA,MAC3D;AAAA,IACF,GAAG,KAAK;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA,iBAAiB;AACf,QAAI,CAAC,KAAK,GAAG,WAAW;AACtB,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;AAAA,IACvD;AACA,UAAM,QAAQ,KAAK,GAAG,cAAc;AACpC,WAAO,KAAK,GAAG,yBAAyB,mBAAmB;AAAA,MACzD;AAAA,IACF,GAAG,KAAK;AAAA,EACV;AAQF;AAPE,cAtFI,WAsFG,QAAO,SAAS,iBAAiB,mBAAmB;AACzD,SAAO,KAAK,qBAAqB,WAAa,SAAS,eAAe,CAAC;AACzE;AACA,cAzFI,WAyFG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,UAAS;AACpB,CAAC;AA5FH,IAAM,WAAN;AAAA,CA8FC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AASH,IAAM,SAAS,IAAI,eAAe,YAAY,yBAAyB,EAAE;AACzE,SAAS,qBAAqB;AAC5B,MAA2C,OAAc;AACvD;AAAA,EACF;AACA,QAAM,UAAU,OAAO,qBAAqB;AAC5C,MAAI,EAAE,mBAAmB,aAAa,QAAQ,YAAY,QAAQ;AAChE;AAAA,EACF;AACA,QAAM,SAAS,OAAO,MAAM;AAC5B,QAAM,SAAS,OAAO,MAAM;AAC5B,QAAM,SAAS,OAAO,cAAc;AAIpC,SAAO,kBAAkB,MAAM;AAI7B,UAAM,KAAK,UAAU;AACrB,UAAM,qBAAqB,MAAM,GAAG,YAAY,YAAY;AAAA,MAC1D,QAAQ;AAAA,IACV,CAAC;AACD,OAAG,iBAAiB,oBAAoB,kBAAkB;AAC1D,WAAO,UAAU,MAAM;AACrB,SAAG,oBAAoB,oBAAoB,kBAAkB;AAAA,IAC/D,CAAC;AAAA,EACH,CAAC;AAGD,SAAO,kBAAkB,MAAM;AAC7B,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,yBAAyB,YAAY;AAC9C,wBAAkB,IAAI,QAAQ,aAAW,qBAAqB,EAAE,UAAU,MAAM,QAAQ,CAAC,CAAC;AAAA,IAC5F,OAAO;AACL,YAAM,CAAC,UAAU,GAAG,IAAI,KAAK,wBAAwB,4BAA4B,MAAM,GAAG;AAC1F,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,4BAAkB,QAAQ,QAAQ;AAClC;AAAA,QACF,KAAK;AACH,4BAAkB,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC;AAChD;AAAA,QACF,KAAK;AACH,4BAAkB,QAAQ,KAAK,CAAC,OAAO,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChF;AAAA,QACF;AAEE,gBAAM,IAAI,MAAM,gDAAgD,QAAQ,oBAAoB,EAAE;AAAA,MAClG;AAAA,IACF;AAGA,oBAAgB,KAAK,MAAM,UAAU,cAAc,SAAS,QAAQ;AAAA,MAClE,OAAO,QAAQ;AAAA,IACjB,CAAC,EAAE,MAAM,SAAO,QAAQ,MAAM,4CAA4C,GAAG,CAAC,CAAC;AAAA,EACjF,CAAC;AACH;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,IAAI,QAAQ,aAAW,WAAW,SAAS,OAAO,CAAC;AAC5D;AACA,SAAS,uBAAuB,MAAM,UAAU;AAC9C,QAAM,YAAY;AAClB,SAAO,IAAI,gBAAgB,aAAa,KAAK,YAAY,QAAQ,UAAU,gBAAgB,QAAW,QAAQ;AAChH;AAaA,IAAM,wBAAN,MAA4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BA;AACF;AAkBA,SAAS,qBAAqB,QAAQ,UAAU,CAAC,GAAG;AAClD,SAAO,yBAAyB,CAAC,QAAQ,UAAU;AAAA,IACjD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM,CAAC,uBAAuB,QAAQ;AAAA,EACxC,GAAG,sBAAsB,kBAAkB,CAAC,CAAC;AAC/C;AAKA,IAAM,uBAAN,MAAM,qBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,OAAO,SAAS,QAAQ,UAAU,CAAC,GAAG;AACpC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,qBAAqB,QAAQ,OAAO,CAAC;AAAA,IACnD;AAAA,EACF;AAUF;AATE,cAbI,sBAaG,QAAO,SAAS,4BAA4B,mBAAmB;AACpE,SAAO,KAAK,qBAAqB,sBAAqB;AACxD;AACA,cAhBI,sBAgBG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AACR,CAAC;AACD,cAnBI,sBAmBG,QAAyB,iBAAiB;AAAA,EAC/C,WAAW,CAAC,QAAQ,QAAQ;AAC9B,CAAC;AArBH,IAAM,sBAAN;AAAA,CAuBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,QAAQ,QAAQ;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}