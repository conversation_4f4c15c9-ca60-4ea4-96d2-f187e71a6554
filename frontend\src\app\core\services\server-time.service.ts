import {
  Observable,
  BehaviorSubject,
  timer,
} from 'rxjs';
import {
  share,
  map,
  switchMap,
  tap,
  filter,
  take,
  distinctUntilChanged,
} from 'rxjs/operators';
import { Injectable, NgZone } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Res } from '@share-types/center.types';

interface ServerTime {
  server_time: number;
  is_time_synced: boolean;
}
@Injectable({
  providedIn: 'root',
})
export class ServerTimeService {
  private _is_time_synced$ = new BehaviorSubject(false); // 云端时间是否已同步
  private serverTime$: Observable<number>;
  private serverTime: number; // 毫秒
  private deltaTime = 0;

  constructor(private http: HttpClient, private ngZone: NgZone) {}

  get is_time_synced$() {
    return this._is_time_synced$.pipe(distinctUntilChanged());
  }

  public getServerTime() {
    return this.http.get<Res<ServerTime>>(`/data/server-time`).pipe(
      tap((res) => {
        if (res.status === 'success') {
          this.refreshTime(res.data.server_time);
        }
        if (res.status === 'success' && res.data.is_time_synced) {
          this._is_time_synced$.next(true);
        }
      })
    );
  }

  private create(): Observable<number> {
     // 每隔100ms计算一次时间
     const timeTick$ = this.getServerTime().pipe(
      switchMap(() => this.observableTimer(100)),
      map(() => {
        this.serverTime = this.absoluteTime() + this.deltaTime;
        return Math.trunc(this.serverTime / 1000);
      }),
      distinctUntilChanged(),
      map((time) => time * 1000)
    );

    // 60秒钟更新一次服务器时间
    timer(0, 60000)
      .pipe(switchMap(() => this.getServerTime()))
      .subscribe();

    return timeTick$.pipe(share());
  }


  observableTimer(ms: number, callback?: (diff: number) => void) {
    return new Observable<number>((subscriber) => {
      let counter = 0;
      let start = performance.now();
      const timeoutInstance: () => any = () => {
        const real = counter * ms;
        const ideal = performance.now() - start;
        counter++;
        const diff = ideal - real;
        subscriber.next(diff);
        if (callback) {
          callback(diff);
        }
        return setTimeout(timeoutInstance, Math.max(0, ms - diff));
      };
      let timerId: any;
      this.ngZone.runOutsideAngular(() => {
        timerId = timeoutInstance();
      });
      return function unsubscribe() {
        clearTimeout(timerId);
      };
    });
  }

  // 获得单个服务器时间值，以毫秒为单位
  public getServerTimeValue(): number {
    if (!this.serverTime$) {
      this.serverTime$ = this.create();
      console.log('create server time stream');
    }
    return this.serverTime;
  }

  // 获得服务器时间流
  public getServerTime$(): Observable<number> {
    if (!this.serverTime$) {
      this.serverTime$ = this.create();
      console.log('create server time stream');
    }
    return this.serverTime$;
  }

  /*
   * 输入输出都以毫秒为单位
   * 获得时间差=预期开始时间-目前时间
   * 结果为正，则为倒计时，为负
   * */
  public getDeltaTime$(startTime: number): Observable<number> {
    return this.getServerTime$().pipe(map((nowTime) => startTime - nowTime));
  }

  // 获取考试进程中所使用的时间
  public getUsedTime$(
    usedTime: number,
    lastStartTime: number
  ): Observable<number> {
    return this.getServerTime$().pipe(
      map((nowTime) => usedTime * 1000 + (nowTime - lastStartTime * 1000))
    );
  }

  // ⏰ time(ms)
  public alarmClock(time: number) {
    return this.getDeltaTime$(time).pipe(
      map((num) => Math.ceil(num / 1000)),
      filter((num) => num <= 0),
      take(1)
    );
  }

  private refreshTime(serverTime: number) {
    this.serverTime = serverTime * 1000;
    // 通过记录服务器时间与本地时间的差值来计算时间
    this.deltaTime = this.serverTime - this.absoluteTime();
  }

  private absoluteTime() {
    return Math.trunc(window.performance.timeOrigin + window.performance.now());
  }
}
