import Got from 'got';
import { cloud_url } from '@server/config/env';
import { createQRcode } from '@server/utils/QR-code';
import { EventConfirmType } from '@share-types/auth-event.types';
import { CloudErrorCode } from '@share-types/center.types';

const got = Got.extend({
  prefixUrl: cloud_url,
  retry: { limit: 0 },
  timeout: {
    connect: 5000, // 建立socket连接 超时
    secureConnect: 5000,
    response: 30 * 1000, // 30s
    socket: 30 * 1000, // 30s
    request: 60 * 1000, // 60s
  },
  responseType: 'json',
});

export interface CloudRes<T> {
  errcode: number;
  data: T;
  next?: string;
  errmsg?: string;
}

export interface IProjectData {
  project_id: string;
  name: string;
  start: string;
  end: string;
}

let server_code: string;
let login_data: ILoginData;

interface ILoginData {
  center_name: string;
  center_address: string;
}
export async function cloudLogin(code: string, hardware?: string) {
  const { body } = await got.post<
    CloudRes<{
      venue_name: string;
      venue_address: string;
      project_name: string;
      same_hardware: boolean;
    }>
  >(`ts-api/v1/venue/info/`, {
    json: { server_id: code, hardware },
  });
  if (body?.errcode) {
    console.error('CloudLogin Error: ', body.errcode, body.errmsg);
    throw new CloudError(body.errmsg || '', body.errcode);
  }
  if (!body?.data) {
    throw new Error('server error');
  }
  const data = body.data;
  server_code = code;
  login_data = {
    center_name: data.venue_name,
    center_address: data.venue_address,
  };
  return login_data;
}

export async function cloudRegister(password: string, hardware?: string) {
  const { body } = await got.post<
    CloudRes<{
      token: string;
      venue_id: string;
      try_venue_id: string;
      formal: IProjectData;
      test: IProjectData;
    }>
  >(`ts-api/v1/venue/register/`, {
    json: { server_id: server_code, hardware: 'test', password },
  });
  if (body?.errcode) {
    throw new CloudError(body.errmsg || '', body.errcode);
  }
  if (!body?.data) {
    throw new Error('server error');
  }
  return {
    ...login_data,
    venue_id: body.data.venue_id,
    try_venue_id: body.data.try_venue_id,
    projects: {
      formal: body.data.formal,
      test: body.data.test,
    },
  };
}

export async function getLatestVersion(
  preUrl: string,
  version: string,
  stage: string,
) {
  const url = `${preUrl}?app=center&v=${version}&os=win32&stage=${stage}&arch=amd64`;
  try {
    const res = await Got.get<{ version: string; pub_date: string }>(url, {
      timeout: 15000,
      responseType: 'json',
    });
    if (res.statusCode === 200) {
      return res.body;
    } else if (res.statusCode === 204) {
      return { version };
    } else {
      console.error('get version statusCode error: ', res.statusCode);
      return null;
    }
  } catch (error) {
    console.error('Cloud: get latest version error:', error);
    throw error;
  }
}

export async function cloudTime() {
  const res = await got.post<{ timestamp: number }>(`ts-api/v1/time`, {
    json: { method: 'time', type: 'time' },
  });
  return res.body.timestamp;
}

// 生成离线授权事件二维码
export async function offlineEventQrcode(query_string: string) {
  const url = `${cloud_url}/ts-api/v1/auth/create?${query_string}`;
  const data_url = await createQRcode(url);
  return data_url;
}

interface AuthEventStatus {
  event_id: string;
  // status: string;
  status: EventConfirmType;
}
export async function authEventStatus(center_id: string) {
  try {
    const res = await got.get<CloudRes<AuthEventStatus[]>>(
      `ts-api/v1/auth/status?center_id=${center_id}`,
    );
    if (res.body.errcode) {
      console.error('AuthEvent: get status error', center_id, res);
      return null;
    }
    return res.body.data;
  } catch (error) {
    console.error('AuthEvent: get status error', error);
  }
}

export class CloudError extends Error {
  name: 'CloudError';
  code: CloudErrorCode;
  constructor(message: string, code: CloudErrorCode) {
    super(message);
    this.code = code;
  }

  toJSON() {
    return {
      name: this.name,
      msg: this.message,
      code: this.code,
    };
  }
}
