import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Res } from '@share-types/center.types';

@Injectable({
  providedIn: 'root',
})
export class FormHttpService {
  constructor(private http: HttpClient) {}

  getFormList(schedule_id: string) {
    return this.http.get<Res<IHttpForm[]>>(
      `/data/schedules/${schedule_id}/forms`
    );
  }

  uploadPackage(data: any) {
    return this.http.post('/data/schedules/package/import', data);
  }

  deleteForm(schedule_id: string, form_id: string) {
    return this.http.delete<Res<void>>(
      `/data/schedules/${schedule_id}/forms/${form_id}`
    );
  }

  publishForm(schedule_id: string) {
    return this.http.post<Res<void>>(
      `/data/schedules/${schedule_id}/forms/publish-form`,
      {}
    );
  }

  publishPassword(schedule_id: string) {
    return this.http.post<Res<void>>(
      `/data/schedules/${schedule_id}/forms/publish-password`,
      {}
    );
  }
}

export interface IHttpForm {
  form_id: string;
  schedule_id: string;
  name: string;
  form_time: string;
  password_time: string;
  form_publish_time: string;
  password_publish_time: string;
  subject?: {
    subject_id: string;
    name: string;
    code: string;
  };
  status: EFormStatus;
}

export enum EFormStatus {
  CREATED = 0,
  PUBLISHED = 1,
  PASSWORD_PUBLISHED = 2,
}
