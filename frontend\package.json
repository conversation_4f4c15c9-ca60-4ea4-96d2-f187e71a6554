{"name": "frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 4203", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.9", "@angular/common": "^19.2.9", "@angular/compiler": "^19.2.9", "@angular/core": "^19.2.9", "@angular/forms": "^19.2.9", "@angular/platform-browser": "^19.2.9", "@angular/platform-browser-dynamic": "^19.2.9", "@angular/router": "^19.2.9", "@ant-design/icons-angular": "^19.0.0", "@ngneat/elf": "^2.5.1", "@ngneat/elf-entities": "^5.0.1", "echarts": "^5.4.3", "ng-zorro-antd": "19.2.2", "ngx-echarts": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.10", "@angular/cli": "^19.2.10", "@angular/compiler-cli": "^19.2.9", "@ngneat/elf-devtools": "^1.3.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.4"}}