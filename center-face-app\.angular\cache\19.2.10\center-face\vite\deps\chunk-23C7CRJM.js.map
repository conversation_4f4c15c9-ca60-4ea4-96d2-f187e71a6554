{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-wave.mjs"], "sourcesContent": ["import { Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, makeEnvironmentProviders, inject, CSP_NONCE, Input, Directive, NgModule } from '@angular/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzWaveRenderer {\n  triggerElement;\n  ngZone;\n  insertExtraNode;\n  platform;\n  cspNonce;\n  waveTransitionDuration = 400;\n  styleForPseudo = null;\n  extraNode = null;\n  lastTime = 0;\n  clickHandler;\n  get waveAttributeName() {\n    return this.insertExtraNode ? 'ant-click-animating' : 'ant-click-animating-without-extra-node';\n  }\n  constructor(triggerElement, ngZone, insertExtraNode, platform, cspNonce) {\n    this.triggerElement = triggerElement;\n    this.ngZone = ngZone;\n    this.insertExtraNode = insertExtraNode;\n    this.platform = platform;\n    this.cspNonce = cspNonce;\n    this.clickHandler = this.onClick.bind(this);\n    this.bindTriggerEvent();\n  }\n  onClick = event => {\n    if (!this.triggerElement || !this.triggerElement.getAttribute || this.triggerElement.getAttribute('disabled') || event.target.tagName === 'INPUT' || this.triggerElement.className.indexOf('disabled') >= 0) {\n      return;\n    }\n    this.fadeOutWave();\n  };\n  bindTriggerEvent() {\n    if (this.platform.isBrowser) {\n      this.ngZone.runOutsideAngular(() => {\n        this.removeTriggerEvent();\n        if (this.triggerElement) {\n          this.triggerElement.addEventListener('click', this.clickHandler, true);\n        }\n      });\n    }\n  }\n  removeTriggerEvent() {\n    if (this.triggerElement) {\n      this.triggerElement.removeEventListener('click', this.clickHandler, true);\n    }\n  }\n  removeStyleAndExtraNode() {\n    if (this.styleForPseudo && document.body.contains(this.styleForPseudo)) {\n      document.body.removeChild(this.styleForPseudo);\n      this.styleForPseudo = null;\n    }\n    if (this.insertExtraNode && this.triggerElement.contains(this.extraNode)) {\n      this.triggerElement.removeChild(this.extraNode);\n    }\n  }\n  destroy() {\n    this.removeTriggerEvent();\n    this.removeStyleAndExtraNode();\n  }\n  fadeOutWave() {\n    const node = this.triggerElement;\n    const waveColor = this.getWaveColor(node);\n    node.setAttribute(this.waveAttributeName, 'true');\n    if (Date.now() < this.lastTime + this.waveTransitionDuration) {\n      return;\n    }\n    if (this.isValidColor(waveColor)) {\n      if (!this.styleForPseudo) {\n        this.styleForPseudo = document.createElement('style');\n        if (this.cspNonce) {\n          this.styleForPseudo.nonce = this.cspNonce;\n        }\n      }\n      this.styleForPseudo.innerHTML = `\n      [ant-click-animating-without-extra-node='true']::after, .ant-click-animating-node {\n        --antd-wave-shadow-color: ${waveColor};\n      }`;\n      document.body.appendChild(this.styleForPseudo);\n    }\n    if (this.insertExtraNode) {\n      if (!this.extraNode) {\n        this.extraNode = document.createElement('div');\n      }\n      this.extraNode.className = 'ant-click-animating-node';\n      node.appendChild(this.extraNode);\n    }\n    this.lastTime = Date.now();\n    this.runTimeoutOutsideZone(() => {\n      node.removeAttribute(this.waveAttributeName);\n      this.removeStyleAndExtraNode();\n    }, this.waveTransitionDuration);\n  }\n  isValidColor(color) {\n    return !!color && color !== '#ffffff' && color !== 'rgb(255, 255, 255)' && this.isNotGrey(color) && !/rgba\\(\\d*, \\d*, \\d*, 0\\)/.test(color) && color !== 'transparent';\n  }\n  isNotGrey(color) {\n    const match = color.match(/rgba?\\((\\d*), (\\d*), (\\d*)(, [.\\d]*)?\\)/);\n    if (match && match[1] && match[2] && match[3]) {\n      return !(match[1] === match[2] && match[2] === match[3]);\n    }\n    return true;\n  }\n  getWaveColor(node) {\n    const nodeStyle = getComputedStyle(node);\n    return nodeStyle.getPropertyValue('border-top-color') ||\n    // Firefox Compatible\n    nodeStyle.getPropertyValue('border-color') || nodeStyle.getPropertyValue('background-color');\n  }\n  runTimeoutOutsideZone(fn, delay) {\n    this.ngZone.runOutsideAngular(() => setTimeout(fn, delay));\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NZ_WAVE_GLOBAL_DEFAULT_CONFIG = {\n  disabled: false\n};\nconst NZ_WAVE_GLOBAL_CONFIG = new InjectionToken('nz-wave-global-options');\nfunction provideNzWave(config) {\n  return makeEnvironmentProviders([{\n    provide: NZ_WAVE_GLOBAL_CONFIG,\n    useValue: config\n  }]);\n}\nclass NzWaveDirective {\n  ngZone;\n  elementRef;\n  nzWaveExtraNode = false;\n  waveRenderer;\n  waveDisabled = false;\n  get disabled() {\n    return this.waveDisabled;\n  }\n  get rendererRef() {\n    return this.waveRenderer;\n  }\n  cspNonce = inject(CSP_NONCE, {\n    optional: true\n  });\n  platform = inject(Platform);\n  config = inject(NZ_WAVE_GLOBAL_CONFIG, {\n    optional: true\n  });\n  animationType = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  constructor(ngZone, elementRef) {\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    this.waveDisabled = this.isConfigDisabled();\n  }\n  isConfigDisabled() {\n    let disabled = false;\n    if (this.config && typeof this.config.disabled === 'boolean') {\n      disabled = this.config.disabled;\n    }\n    if (this.animationType === 'NoopAnimations') {\n      disabled = true;\n    }\n    return disabled;\n  }\n  ngOnDestroy() {\n    if (this.waveRenderer) {\n      this.waveRenderer.destroy();\n    }\n  }\n  ngOnInit() {\n    this.renderWaveIfEnabled();\n  }\n  renderWaveIfEnabled() {\n    if (!this.waveDisabled && this.elementRef.nativeElement) {\n      this.waveRenderer = new NzWaveRenderer(this.elementRef.nativeElement, this.ngZone, this.nzWaveExtraNode, this.platform, this.cspNonce);\n    }\n  }\n  disable() {\n    this.waveDisabled = true;\n    if (this.waveRenderer) {\n      this.waveRenderer.removeTriggerEvent();\n      this.waveRenderer.removeStyleAndExtraNode();\n    }\n  }\n  enable() {\n    // config priority\n    this.waveDisabled = this.isConfigDisabled() || false;\n    if (this.waveRenderer) {\n      this.waveRenderer.bindTriggerEvent();\n    }\n  }\n  static ɵfac = function NzWaveDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzWaveDirective)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzWaveDirective,\n    selectors: [[\"\", \"nz-wave\", \"\"], [\"button\", \"nz-button\", \"\", 3, \"nzType\", \"link\", 3, \"nzType\", \"text\"]],\n    inputs: {\n      nzWaveExtraNode: \"nzWaveExtraNode\"\n    },\n    exportAs: [\"nzWave\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzWaveDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-wave],button[nz-button]:not([nzType=\"link\"]):not([nzType=\"text\"])',\n      exportAs: 'nzWave'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzWaveExtraNode: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzWaveModule {\n  static ɵfac = function NzWaveModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzWaveModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzWaveModule,\n    imports: [NzWaveDirective],\n    exports: [NzWaveDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [provideNzWave(NZ_WAVE_GLOBAL_DEFAULT_CONFIG)]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzWaveModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzWaveDirective],\n      exports: [NzWaveDirective],\n      providers: [provideNzWave(NZ_WAVE_GLOBAL_DEFAULT_CONFIG)]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_WAVE_GLOBAL_CONFIG, NZ_WAVE_GLOBAL_DEFAULT_CONFIG, NzWaveDirective, NzWaveModule, NzWaveRenderer, provideNzWave };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,yBAAyB;AAAA,EACzB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,kBAAkB,wBAAwB;AAAA,EACxD;AAAA,EACA,YAAY,gBAAgB,QAAQ,iBAAiB,UAAU,UAAU;AACvE,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,eAAe,KAAK,QAAQ,KAAK,IAAI;AAC1C,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,UAAU,WAAS;AACjB,QAAI,CAAC,KAAK,kBAAkB,CAAC,KAAK,eAAe,gBAAgB,KAAK,eAAe,aAAa,UAAU,KAAK,MAAM,OAAO,YAAY,WAAW,KAAK,eAAe,UAAU,QAAQ,UAAU,KAAK,GAAG;AAC3M;AAAA,IACF;AACA,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,SAAS,WAAW;AAC3B,WAAK,OAAO,kBAAkB,MAAM;AAClC,aAAK,mBAAmB;AACxB,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe,iBAAiB,SAAS,KAAK,cAAc,IAAI;AAAA,QACvE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,oBAAoB,SAAS,KAAK,cAAc,IAAI;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,QAAI,KAAK,kBAAkB,SAAS,KAAK,SAAS,KAAK,cAAc,GAAG;AACtE,eAAS,KAAK,YAAY,KAAK,cAAc;AAC7C,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,KAAK,mBAAmB,KAAK,eAAe,SAAS,KAAK,SAAS,GAAG;AACxE,WAAK,eAAe,YAAY,KAAK,SAAS;AAAA,IAChD;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,mBAAmB;AACxB,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,UAAM,OAAO,KAAK;AAClB,UAAM,YAAY,KAAK,aAAa,IAAI;AACxC,SAAK,aAAa,KAAK,mBAAmB,MAAM;AAChD,QAAI,KAAK,IAAI,IAAI,KAAK,WAAW,KAAK,wBAAwB;AAC5D;AAAA,IACF;AACA,QAAI,KAAK,aAAa,SAAS,GAAG;AAChC,UAAI,CAAC,KAAK,gBAAgB;AACxB,aAAK,iBAAiB,SAAS,cAAc,OAAO;AACpD,YAAI,KAAK,UAAU;AACjB,eAAK,eAAe,QAAQ,KAAK;AAAA,QACnC;AAAA,MACF;AACA,WAAK,eAAe,YAAY;AAAA;AAAA,oCAEF,SAAS;AAAA;AAEvC,eAAS,KAAK,YAAY,KAAK,cAAc;AAAA,IAC/C;AACA,QAAI,KAAK,iBAAiB;AACxB,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,YAAY,SAAS,cAAc,KAAK;AAAA,MAC/C;AACA,WAAK,UAAU,YAAY;AAC3B,WAAK,YAAY,KAAK,SAAS;AAAA,IACjC;AACA,SAAK,WAAW,KAAK,IAAI;AACzB,SAAK,sBAAsB,MAAM;AAC/B,WAAK,gBAAgB,KAAK,iBAAiB;AAC3C,WAAK,wBAAwB;AAAA,IAC/B,GAAG,KAAK,sBAAsB;AAAA,EAChC;AAAA,EACA,aAAa,OAAO;AAClB,WAAO,CAAC,CAAC,SAAS,UAAU,aAAa,UAAU,wBAAwB,KAAK,UAAU,KAAK,KAAK,CAAC,2BAA2B,KAAK,KAAK,KAAK,UAAU;AAAA,EAC3J;AAAA,EACA,UAAU,OAAO;AACf,UAAM,QAAQ,MAAM,MAAM,yCAAyC;AACnE,QAAI,SAAS,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AAC7C,aAAO,EAAE,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,MAAM,CAAC;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa,MAAM;AACjB,UAAM,YAAY,iBAAiB,IAAI;AACvC,WAAO,UAAU,iBAAiB,kBAAkB;AAAA,IAEpD,UAAU,iBAAiB,cAAc,KAAK,UAAU,iBAAiB,kBAAkB;AAAA,EAC7F;AAAA,EACA,sBAAsB,IAAI,OAAO;AAC/B,SAAK,OAAO,kBAAkB,MAAM,WAAW,IAAI,KAAK,CAAC;AAAA,EAC3D;AACF;AAMA,IAAM,gCAAgC;AAAA,EACpC,UAAU;AACZ;AACA,IAAM,wBAAwB,IAAI,eAAe,wBAAwB;AACzE,SAAS,cAAc,QAAQ;AAC7B,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AACA,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB;AAAA,EACA,eAAe;AAAA,EACf,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW,OAAO,WAAW;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,OAAO,QAAQ;AAAA,EAC1B,SAAS,OAAO,uBAAuB;AAAA,IACrC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,gBAAgB,OAAO,uBAAuB;AAAA,IAC5C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,QAAQ,YAAY;AAC9B,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,eAAe,KAAK,iBAAiB;AAAA,EAC5C;AAAA,EACA,mBAAmB;AACjB,QAAI,WAAW;AACf,QAAI,KAAK,UAAU,OAAO,KAAK,OAAO,aAAa,WAAW;AAC5D,iBAAW,KAAK,OAAO;AAAA,IACzB;AACA,QAAI,KAAK,kBAAkB,kBAAkB;AAC3C,iBAAW;AAAA,IACb;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,QAAQ;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,sBAAsB;AACpB,QAAI,CAAC,KAAK,gBAAgB,KAAK,WAAW,eAAe;AACvD,WAAK,eAAe,IAAI,eAAe,KAAK,WAAW,eAAe,KAAK,QAAQ,KAAK,iBAAiB,KAAK,UAAU,KAAK,QAAQ;AAAA,IACvI;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,eAAe;AACpB,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,mBAAmB;AACrC,WAAK,aAAa,wBAAwB;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,SAAS;AAEP,SAAK,eAAe,KAAK,iBAAiB,KAAK;AAC/C,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,iBAAiB;AAAA,IACrC;AAAA,EACF;AAYF;AAXE,cAhEI,kBAgEG,QAAO,SAAS,wBAAwB,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,kBAAoB,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,CAAC;AACxH;AACA,cAnEI,kBAmEG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,GAAG,CAAC,UAAU,aAAa,IAAI,GAAG,UAAU,QAAQ,GAAG,UAAU,MAAM,CAAC;AAAA,EACtG,QAAQ;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,UAAU,CAAC,QAAQ;AACrB,CAAC;AA1EH,IAAM,kBAAN;AAAA,CA4EC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,cAAa;AAYnB;AAXE,cADI,eACG,QAAO,SAAS,qBAAqB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,eAAc;AACjD;AACA,cAJI,eAIG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AAAA,EACN,SAAS,CAAC,eAAe;AAAA,EACzB,SAAS,CAAC,eAAe;AAC3B,CAAC;AACD,cATI,eASG,QAAyB,iBAAiB;AAAA,EAC/C,WAAW,CAAC,cAAc,6BAA6B,CAAC;AAC1D,CAAC;AAXH,IAAM,eAAN;AAAA,CAaC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe;AAAA,MACzB,SAAS,CAAC,eAAe;AAAA,MACzB,WAAW,CAAC,cAAc,6BAA6B,CAAC;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}