{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_@angular+animations@19.2.9_@angular+common@19.2.9_@angular+core@19.2.9_r_i5xlsxr3thsqcwddfzjvckvf54/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-environments.mjs", "../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_@angular+animations@19.2.9_@angular+common@19.2.9_@angular+core@19.2.9_r_i5xlsxr3thsqcwddfzjvckvf54/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-logger.mjs", "../../../../../../node_modules/.pnpm/@angular+cdk@19.2.14_@angular+common@19.2.9_@angular+core@19.2.9_rxjs@7.8.1_zone.js@0.15.0__r_wvf46cviac2ur7dbe2lzujwkzu/node_modules/@angular/cdk/fesm2022/element-x4z00URv.mjs", "../../../../../../node_modules/.pnpm/@angular+cdk@19.2.14_@angular+common@19.2.9_@angular+core@19.2.9_rxjs@7.8.1_zone.js@0.15.0__r_wvf46cviac2ur7dbe2lzujwkzu/node_modules/@angular/cdk/fesm2022/array-I1yfCXUO.mjs", "../../../../../../node_modules/.pnpm/@angular+cdk@19.2.14_@angular+common@19.2.9_@angular+core@19.2.9_rxjs@7.8.1_zone.js@0.15.0__r_wvf46cviac2ur7dbe2lzujwkzu/node_modules/@angular/cdk/fesm2022/css-pixel-value-C_HEqLhI.mjs", "../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_@angular+animations@19.2.9_@angular+common@19.2.9_@angular+core@19.2.9_r_i5xlsxr3thsqcwddfzjvckvf54/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-util.mjs"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst environment = {\n  isTestMode: false\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { environment };\n", "import { isDevMode } from '@angular/core';\nimport { environment } from 'ng-zorro-antd/core/environments';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst record = {};\nconst PREFIX = '[NG-ZORRO]:';\nfunction notRecorded(...args) {\n  const asRecord = args.reduce((acc, c) => acc + c.toString(), '');\n  if (record[asRecord]) {\n    return false;\n  } else {\n    record[asRecord] = true;\n    return true;\n  }\n}\nfunction consoleCommonBehavior(consoleFunc, ...args) {\n  if (environment.isTestMode || isDevMode() && notRecorded(...args)) {\n    consoleFunc(...args);\n  }\n}\n// Warning should only be printed in dev mode and only once.\nconst warn = (...args) => consoleCommonBehavior((...arg) => console.warn(PREFIX, ...arg), ...args);\n// eslint-disable-next-line @typescript-eslint/explicit-function-return-type\nconst warnDeprecation = (...args) => {\n  if (!environment.isTestMode) {\n    const stack = new Error().stack;\n    return consoleCommonBehavior((...arg) => console.warn(PREFIX, 'deprecated:', ...arg, stack), ...args);\n  } else {\n    return () => {};\n  }\n};\n// Log should only be printed in dev mode.\nconst log = (...args) => {\n  if (isDevMode()) {\n    console.log(PREFIX, ...args);\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PREFIX, log, warn, warnDeprecation };\n", "import { ElementRef } from '@angular/core';\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n  if (_isNumberValue(value)) {\n    return Number(value);\n  }\n  return arguments.length === 2 ? fallbackValue : 0;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n  return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\nexport { _isNumberValue as _, coerceElement as a, coerceNumberProperty as c };\n", "function coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nexport { coerceArray as c };\n", "/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n  if (value == null) {\n    return '';\n  }\n  return typeof value === 'string' ? value : `${value}px`;\n}\nexport { coerceCssPixelValue as c };\n", "import { TemplateRef, numberAttribute } from '@angular/core';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceCssPixelValue } from '@angular/cdk/coercion';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { Subject, isObservable, from, of, EMPTY, Observable, fromEvent } from 'rxjs';\nimport { take } from 'rxjs/operators';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction toArray(value) {\n  let ret;\n  if (value == null) {\n    ret = [];\n  } else if (!Array.isArray(value)) {\n    ret = [value];\n  } else {\n    ret = value;\n  }\n  return ret;\n}\nfunction arraysEqual(array1, array2) {\n  if (!array1 || !array2 || array1.length !== array2.length) {\n    return false;\n  }\n  const len = array1.length;\n  for (let i = 0; i < len; i++) {\n    if (array1[i] !== array2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction shallowCopyArray(source) {\n  return source.slice();\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isNotNil(value) {\n  return typeof value !== 'undefined' && value !== null;\n}\nfunction isNil(value) {\n  return typeof value === 'undefined' || value === null;\n}\n/**\n * Examine if two objects are shallowly equaled.\n */\nfunction shallowEqual(objA, objB) {\n  if (objA === objB) {\n    return true;\n  }\n  if (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n    return false;\n  }\n  const keysA = Object.keys(objA);\n  const keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n  const bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let idx = 0; idx < keysA.length; idx++) {\n    const key = keysA[idx];\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n    if (objA[key] !== objB[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isNonEmptyString(value) {\n  return typeof value === 'string' && value !== '';\n}\nfunction isTemplateRef(value) {\n  return value instanceof TemplateRef;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction toBoolean(value) {\n  return coerceBooleanProperty(value);\n}\nfunction numberAttributeWithZeroFallback(value) {\n  return numberAttribute(value, 0);\n}\nfunction numberAttributeWithOneFallback(value) {\n  return numberAttribute(value, 1);\n}\nfunction numberAttributeWithInfinityFallback(value) {\n  return numberAttribute(value, Infinity);\n}\nfunction toNumber(value, fallbackValue = 0) {\n  return coerceNumberProperty(value, fallbackValue);\n}\nfunction toCssPixel(value) {\n  return coerceCssPixelValue(value);\n}\n// eslint-disable  no-invalid-this\n/**\n * Get the function-property type's value\n */\nfunction valueFunctionProp(prop, ...args) {\n  return typeof prop === 'function' ? prop(...args) : prop;\n}\nfunction propDecoratorFactory(name, fallback) {\n  function propDecorator(target, propName, originalDescriptor) {\n    const privatePropName = `$$__zorroPropDecorator__${propName}`;\n    if (Object.prototype.hasOwnProperty.call(target, privatePropName)) {\n      warn(`The prop \"${privatePropName}\" is already exist, it will be overrided by ${name} decorator.`);\n    }\n    Object.defineProperty(target, privatePropName, {\n      configurable: true,\n      writable: true\n    });\n    return {\n      get() {\n        return originalDescriptor && originalDescriptor.get ? originalDescriptor.get.bind(this)() : this[privatePropName];\n      },\n      set(value) {\n        if (originalDescriptor && originalDescriptor.set) {\n          originalDescriptor.set.bind(this)(fallback(value));\n        }\n        this[privatePropName] = fallback(value);\n      }\n    };\n  }\n  return propDecorator;\n}\n/**\n * @deprecated Use input transform instead: `@Input({ transform })`\n *\n * Input decorator that handle a prop to do get/set automatically with toBoolean\n *\n * Why not using @InputBoolean alone without @Input? AOT needs @Input to be visible\n *\n * @howToUse\n * ```\n * @Input() @InputBoolean() visible: boolean = false;\n *\n * // Act as below:\n * // @Input()\n * // get visible() { return this.__visible; }\n * // set visible(value) { this.__visible = value; }\n * // __visible = false;\n * ```\n */\nfunction InputBoolean() {\n  return propDecoratorFactory('InputBoolean', toBoolean);\n}\n/**\n * @deprecated Use input transform instead: `@Input({ transform })`\n */\nfunction InputCssPixel() {\n  return propDecoratorFactory('InputCssPixel', toCssPixel);\n}\n/**\n * @deprecated Use input transform instead: `@Input({ transform })`\n */\nfunction InputNumber(fallbackValue) {\n  return propDecoratorFactory('InputNumber', value => toNumber(value, fallbackValue));\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Silent an event by stopping and preventing it.\n */\nfunction silentEvent(e) {\n  e.stopPropagation();\n  e.preventDefault();\n}\nfunction getElementOffset(elem) {\n  if (!elem.getClientRects().length) {\n    return {\n      top: 0,\n      left: 0\n    };\n  }\n  const rect = elem.getBoundingClientRect();\n  const win = elem.ownerDocument.defaultView;\n  return {\n    top: rect.top + win.pageYOffset,\n    left: rect.left + win.pageXOffset\n  };\n}\n/**\n * Investigate if an event is a `TouchEvent`.\n */\nfunction isTouchEvent(event) {\n  return event.type.startsWith('touch');\n}\nfunction getEventPosition(event) {\n  return isTouchEvent(event) ? event.touches[0] || event.changedTouches[0] : event;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getRegExp(prefix) {\n  const prefixArray = Array.isArray(prefix) ? prefix : [prefix];\n  let prefixToken = prefixArray.join('').replace(/(\\$|\\^)/g, '\\\\$1');\n  if (prefixArray.length > 1) {\n    prefixToken = `[${prefixToken}]`;\n  }\n  return new RegExp(`(\\\\s|^)(${prefixToken})[^\\\\s]*`, 'g');\n}\nfunction getMentions(value, prefix = '@') {\n  if (typeof value !== 'string') {\n    return [];\n  }\n  const regex = getRegExp(prefix);\n  const mentions = value.match(regex);\n  return mentions !== null ? mentions.map(e => e.trim()) : [];\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Much like lodash.\n */\nfunction padStart(toPad, length, element) {\n  if (toPad.length > length) {\n    return toPad;\n  }\n  const joined = `${getRepeatedElement(length, element)}${toPad}`;\n  return joined.slice(joined.length - length, joined.length);\n}\nfunction padEnd(toPad, length, element) {\n  const joined = `${toPad}${getRepeatedElement(length, element)}`;\n  return joined.slice(0, length);\n}\nfunction getRepeatedElement(length, element) {\n  return Array(length).fill(element).join('');\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isPromise(obj) {\n  return !!obj && typeof obj.then === 'function' && typeof obj.catch === 'function';\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getPercent(min, max, value) {\n  return (value - min) / (max - min) * 100;\n}\nfunction getPrecision(num) {\n  const numStr = num.toString();\n  const dotIndex = numStr.indexOf('.');\n  return dotIndex >= 0 ? numStr.length - dotIndex - 1 : 0;\n}\nfunction ensureNumberInRange(num, min, max) {\n  if (isNaN(num) || num < min) {\n    return min;\n  } else if (num > max) {\n    return max;\n  } else {\n    return num;\n  }\n}\nfunction isNumberFinite(value) {\n  return typeof value === 'number' && isFinite(value);\n}\nfunction toDecimal(value, decimal) {\n  return Math.round(value * Math.pow(10, decimal)) / Math.pow(10, decimal);\n}\nfunction sum(input, initial = 0) {\n  return input.reduce((previous, current) => previous + current, initial);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction scrollIntoView(node) {\n  const nodeAsAny = node;\n  if (nodeAsAny.scrollIntoViewIfNeeded) {\n    nodeAsAny.scrollIntoViewIfNeeded(false);\n    return;\n  }\n  if (node.scrollIntoView) {\n    node.scrollIntoView(false);\n    return;\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// from https://github.com/component/textarea-caret-position\n// We'll copy the properties below into the mirror div.\n// Note that some browsers, such as Firefox, do not concatenate properties\n// into their shorthand (e.g. padding-top, padding-bottom etc. -> padding),\n// so we have to list every single property explicitly.\nconst properties = ['direction',\n// RTL support\n'boxSizing', 'width',\n// on Chrome and IE, exclude the scrollbar, so the mirror div wraps exactly as the textarea does\n'height', 'overflowX', 'overflowY',\n// copy the scrollbar for IE\n'borderTopWidth', 'borderRightWidth', 'borderBottomWidth', 'borderLeftWidth', 'borderStyle', 'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft',\n// https://developer.mozilla.org/en-US/docs/Web/CSS/font\n'fontStyle', 'fontVariant', 'fontWeight', 'fontStretch', 'fontSize', 'fontSizeAdjust', 'lineHeight', 'fontFamily', 'textAlign', 'textTransform', 'textIndent', 'textDecoration',\n// might not make a difference, but better be safe\n'letterSpacing', 'wordSpacing', 'tabSize', 'MozTabSize'];\nconst isBrowser = typeof window !== 'undefined';\nconst isFirefox = isBrowser && window.mozInnerScreenX != null;\nconst _parseInt = str => parseInt(str, 10);\nfunction getCaretCoordinates(element, position, options) {\n  if (!isBrowser) {\n    throw new Error('textarea-caret-position#getCaretCoordinates should only be called in a browser');\n  }\n  const debug = options && options.debug || false;\n  if (debug) {\n    const el = document.querySelector('#input-textarea-caret-position-mirror-div');\n    if (el) {\n      el.parentNode.removeChild(el);\n    }\n  }\n  // The mirror div will replicate the textarea's style\n  const div = document.createElement('div');\n  div.id = 'input-textarea-caret-position-mirror-div';\n  document.body.appendChild(div);\n  const style = div.style;\n  const computed = window.getComputedStyle ? window.getComputedStyle(element) : element.currentStyle; // currentStyle for IE < 9\n  const isInput = element.nodeName === 'INPUT';\n  // Default textarea styles\n  style.whiteSpace = 'pre-wrap';\n  if (!isInput) {\n    style.wordWrap = 'break-word'; // only for textarea-s\n  }\n  // Position off-screen\n  style.position = 'absolute'; // required to return coordinates properly\n  if (!debug) {\n    style.visibility = 'hidden';\n  } // not 'display: none' because we want rendering\n  // Transfer the element's properties to the div\n  properties.forEach(prop => {\n    if (isInput && prop === 'lineHeight') {\n      // Special case for <input>s because text is rendered centered and line height may be != height\n      style.lineHeight = computed.height;\n    } else {\n      // @ts-ignore\n      style[prop] = computed[prop];\n    }\n  });\n  if (isFirefox) {\n    // Firefox lies about the overflow property for textareas: https://bugzilla.mozilla.org/show_bug.cgi?id=984275\n    if (element.scrollHeight > _parseInt(computed.height)) {\n      style.overflowY = 'scroll';\n    }\n  } else {\n    style.overflow = 'hidden'; // for Chrome to not render a scrollbar; IE keeps overflowY = 'scroll'\n  }\n  div.textContent = element.value.substring(0, position);\n  // The second special handling for input type=\"text\" vs textarea:\n  // spaces need to be replaced with non-breaking spaces - http://stackoverflow.com/a/13402035/1269037\n  if (isInput) {\n    div.textContent = div.textContent.replace(/\\s/g, '\\u00a0');\n  }\n  const span = document.createElement('span');\n  // Wrapping must be replicated *exactly*, including when a long word gets\n  // onto the next line, with whitespace at the end of the line before (#7).\n  // The  *only* reliable way to do that is to copy the *entire* rest of the\n  // textarea's content into the <span> created at the caret position.\n  // For inputs, just '.' would be enough, but no need to bother.\n  span.textContent = element.value.substring(position) || '.'; // || because a completely empty faux span doesn't render at all\n  div.appendChild(span);\n  const coordinates = {\n    top: span.offsetTop + _parseInt(computed.borderTopWidth),\n    left: span.offsetLeft + _parseInt(computed.borderLeftWidth),\n    height: _parseInt(computed.lineHeight)\n  };\n  if (debug) {\n    span.style.backgroundColor = '#eee';\n    createDebugEle(element, coordinates);\n  } else {\n    document.body.removeChild(div);\n  }\n  return coordinates;\n}\nfunction createDebugEle(element, coordinates) {\n  const fontSize = getComputedStyle(element).getPropertyValue('font-size');\n  const rect = document.querySelector('#DEBUG') || document.createElement('div');\n  document.body.appendChild(rect);\n  rect.id = 'DEBUG';\n  rect.style.position = 'absolute';\n  rect.style.backgroundColor = 'red';\n  rect.style.height = fontSize;\n  rect.style.width = '1px';\n  rect.style.top = `${element.getBoundingClientRect().top - element.scrollTop + window.pageYOffset + coordinates.top}px`;\n  rect.style.left = `${element.getBoundingClientRect().left - element.scrollLeft + window.pageXOffset + coordinates.left}px`;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isStyleSupport(styleName) {\n  if (typeof window !== 'undefined' && window.document && window.document.documentElement) {\n    const styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n    const {\n      documentElement\n    } = window.document;\n    return styleNameList.some(name => name in documentElement.style);\n  }\n  return false;\n}\nfunction getStyleAsText(styles) {\n  if (!styles) {\n    return '';\n  }\n  return Object.keys(styles).map(key => {\n    const val = styles[key];\n    return `${key}:${typeof val === 'string' ? val : `${val}px`}`;\n  }).join(';');\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// We only handle element & text node.\nconst ELEMENT_NODE = 1;\nconst TEXT_NODE = 3;\nconst COMMENT_NODE = 8;\nlet ellipsisContainer;\nconst wrapperStyle = {\n  padding: '0',\n  margin: '0',\n  display: 'inline',\n  lineHeight: 'inherit'\n};\nfunction pxToNumber(value) {\n  if (!value) {\n    return 0;\n  }\n  const match = value.match(/^\\d*(\\.\\d*)?/);\n  return match ? Number(match[0]) : 0;\n}\nfunction styleToString(style) {\n  // There are some different behavior between Firefox & Chrome.\n  // We have to handle this ourself.\n  const styleNames = Array.prototype.slice.apply(style);\n  return styleNames.map(name => `${name}: ${style.getPropertyValue(name)};`).join('');\n}\nfunction mergeChildren(children) {\n  const childList = [];\n  children.forEach(child => {\n    const prevChild = childList[childList.length - 1];\n    if (prevChild && child.nodeType === TEXT_NODE && prevChild.nodeType === TEXT_NODE) {\n      prevChild.data += child.data;\n    } else {\n      childList.push(child);\n    }\n  });\n  return childList;\n}\nfunction measure(originEle, rows, contentNodes, fixedContent, ellipsisStr, suffixStr = '') {\n  if (!ellipsisContainer) {\n    ellipsisContainer = document.createElement('div');\n    ellipsisContainer.setAttribute('aria-hidden', 'true');\n    document.body.appendChild(ellipsisContainer);\n  }\n  // Get origin style\n  const originStyle = window.getComputedStyle(originEle);\n  const originCSS = styleToString(originStyle);\n  const lineHeight = pxToNumber(originStyle.lineHeight);\n  const maxHeight = Math.round(lineHeight * (rows + 1) + pxToNumber(originStyle.paddingTop) + pxToNumber(originStyle.paddingBottom));\n  // Set shadow\n  ellipsisContainer.setAttribute('style', originCSS);\n  ellipsisContainer.style.position = 'fixed';\n  ellipsisContainer.style.left = '0';\n  ellipsisContainer.style.height = 'auto';\n  ellipsisContainer.style.minHeight = 'auto';\n  ellipsisContainer.style.maxHeight = 'auto';\n  ellipsisContainer.style.top = '-999999px';\n  ellipsisContainer.style.zIndex = '-1000';\n  // clean up css overflow\n  ellipsisContainer.style.textOverflow = 'clip';\n  ellipsisContainer.style.whiteSpace = 'normal';\n  ellipsisContainer.style.webkitLineClamp = 'none';\n  const contentList = mergeChildren(contentNodes);\n  const container = document.createElement('div');\n  const contentContainer = document.createElement('span');\n  const suffixContainer = document.createTextNode(suffixStr);\n  const fixedContainer = document.createElement('span');\n  // Add styles in container\n  Object.assign(container.style, wrapperStyle);\n  Object.assign(contentContainer.style, wrapperStyle);\n  Object.assign(fixedContainer.style, wrapperStyle);\n  contentList.forEach(n => {\n    contentContainer.appendChild(n);\n  });\n  contentContainer.appendChild(suffixContainer);\n  fixedContent.forEach(node => {\n    fixedContainer.appendChild(node.cloneNode(true));\n  });\n  container.appendChild(contentContainer);\n  container.appendChild(fixedContainer);\n  // Render in the fake container\n  ellipsisContainer.appendChild(container);\n  // Check if ellipsis in measure div is height enough for content\n  function inRange() {\n    return ellipsisContainer.offsetHeight < maxHeight;\n  }\n  if (inRange()) {\n    const text = ellipsisContainer.innerHTML;\n    ellipsisContainer.removeChild(container);\n    return {\n      contentNodes,\n      text,\n      ellipsis: false\n    };\n  }\n  // We should clone the childNode since they're controlled by React and we can't reuse it without warning\n  const childNodes = Array.prototype.slice.apply(ellipsisContainer.childNodes[0].childNodes[0].cloneNode(true).childNodes).filter(({\n    nodeType\n  }) => nodeType !== COMMENT_NODE);\n  const fixedNodes = Array.prototype.slice.apply(ellipsisContainer.childNodes[0].childNodes[1].cloneNode(true).childNodes);\n  ellipsisContainer.removeChild(container);\n  // ========================= Find match ellipsis content =========================\n  ellipsisContainer.innerHTML = '';\n  // Create origin content holder\n  const ellipsisContentHolder = document.createElement('span');\n  ellipsisContainer.appendChild(ellipsisContentHolder);\n  const ellipsisTextNode = document.createTextNode(ellipsisStr + suffixStr);\n  ellipsisContentHolder.appendChild(ellipsisTextNode);\n  fixedNodes.forEach(childNode => {\n    ellipsisContainer.appendChild(childNode);\n  });\n  // Append before fixed nodes\n  function appendChildNode(node) {\n    ellipsisContentHolder.insertBefore(node, ellipsisTextNode);\n  }\n  // Get maximum text\n  function measureText(textNode, fullText, startLoc = 0, endLoc = fullText.length, lastSuccessLoc = 0) {\n    const midLoc = Math.floor((startLoc + endLoc) / 2);\n    textNode.textContent = fullText.slice(0, midLoc);\n    if (startLoc >= endLoc - 1) {\n      // Loop when step is small\n      for (let step = endLoc; step >= startLoc; step -= 1) {\n        const currentStepText = fullText.slice(0, step);\n        textNode.textContent = currentStepText;\n        if (inRange() || !currentStepText) {\n          return step === fullText.length ? {\n            finished: false,\n            node: document.createTextNode(fullText)\n          } : {\n            finished: true,\n            node: document.createTextNode(currentStepText)\n          };\n        }\n      }\n    }\n    if (inRange()) {\n      return measureText(textNode, fullText, midLoc, endLoc, midLoc);\n    } else {\n      return measureText(textNode, fullText, startLoc, midLoc, lastSuccessLoc);\n    }\n  }\n  function measureNode(childNode, index) {\n    const type = childNode.nodeType;\n    if (type === ELEMENT_NODE) {\n      // We don't split element, it will keep if whole element can be displayed.\n      // appendChildNode(childNode);\n      if (inRange()) {\n        return {\n          finished: false,\n          node: contentList[index]\n        };\n      }\n      // Clean up if can not pull in\n      ellipsisContentHolder.removeChild(childNode);\n      return {\n        finished: true,\n        node: null\n      };\n    } else if (type === TEXT_NODE) {\n      const fullText = childNode.textContent || '';\n      const textNode = document.createTextNode(fullText);\n      appendChildNode(textNode);\n      return measureText(textNode, fullText);\n    }\n    // Not handle other type of content\n    // PS: This code should not be attached after react 16\n    return {\n      finished: false,\n      node: null\n    };\n  }\n  const ellipsisNodes = [];\n  childNodes.some((childNode, index) => {\n    const {\n      finished,\n      node\n    } = measureNode(childNode, index);\n    if (node) {\n      ellipsisNodes.push(node);\n    }\n    return finished;\n  });\n  const result = {\n    contentNodes: ellipsisNodes,\n    text: ellipsisContainer.innerHTML,\n    ellipsis: true\n  };\n  while (ellipsisContainer.firstChild) {\n    ellipsisContainer.removeChild(ellipsisContainer.firstChild);\n  }\n  return result;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nlet scrollbarVerticalSize;\nlet scrollbarHorizontalSize;\n// Measure scrollbar width for padding body during modal show/hide\nconst scrollbarMeasure = {\n  position: 'absolute',\n  top: '-9999px',\n  width: '50px',\n  height: '50px'\n};\nfunction measureScrollbar(direction = 'vertical', prefix = 'ant') {\n  if (typeof document === 'undefined' || typeof window === 'undefined') {\n    return 0;\n  }\n  const isVertical = direction === 'vertical';\n  if (isVertical && scrollbarVerticalSize) {\n    return scrollbarVerticalSize;\n  } else if (!isVertical && scrollbarHorizontalSize) {\n    return scrollbarHorizontalSize;\n  }\n  const scrollDiv = document.createElement('div');\n  Object.keys(scrollbarMeasure).forEach(scrollProp => {\n    // @ts-ignore\n    scrollDiv.style[scrollProp] = scrollbarMeasure[scrollProp];\n  });\n  // apply hide scrollbar className ahead\n  scrollDiv.className = `${prefix}-hide-scrollbar scroll-div-append-to-body`;\n  // Append related overflow style\n  if (isVertical) {\n    scrollDiv.style.overflowY = 'scroll';\n  } else {\n    scrollDiv.style.overflowX = 'scroll';\n  }\n  document.body.appendChild(scrollDiv);\n  let size = 0;\n  if (isVertical) {\n    size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n    scrollbarVerticalSize = size;\n  } else {\n    size = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n    scrollbarHorizontalSize = size;\n  }\n  document.body.removeChild(scrollDiv);\n  return size;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction ensureInBounds(value, boundValue) {\n  return value ? value < boundValue ? value : boundValue : boundValue;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction inNextTick() {\n  const timer = new Subject();\n  Promise.resolve().then(() => timer.next());\n  return timer.pipe(take(1));\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction wrapIntoObservable(value) {\n  if (isObservable(value)) {\n    return value;\n  }\n  if (isPromise(value)) {\n    // Use `Promise.resolve()` to wrap promise-like instances.\n    return from(Promise.resolve(value));\n  }\n  return of(value);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from rc-util [https://github.com/react-component/util]\n */\nfunction canUseDom() {\n  return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from rc-util [https://github.com/react-component/util]\n */\nconst MARK_KEY = `rc-util-key`;\nfunction getMark({\n  mark\n} = {}) {\n  if (mark) {\n    return mark.startsWith('data-') ? mark : `data-${mark}`;\n  }\n  return MARK_KEY;\n}\nfunction getContainer(option) {\n  if (option.attachTo) {\n    return option.attachTo;\n  }\n  const head = document.querySelector('head');\n  return head || document.body;\n}\nfunction injectCSS(css, options = {}) {\n  if (!canUseDom()) {\n    return null;\n  }\n  const styleNode = document.createElement('style');\n  if (options.cspNonce) {\n    styleNode.nonce = options.cspNonce;\n  }\n  styleNode.innerHTML = css;\n  const container = getContainer(options);\n  const {\n    firstChild\n  } = container;\n  if (options.prepend && container.prepend) {\n    // Use `prepend` first\n    container.prepend(styleNode);\n  } else if (options.prepend && firstChild) {\n    // Fallback to `insertBefore` like IE not support `prepend`\n    container.insertBefore(styleNode, firstChild);\n  } else {\n    container.appendChild(styleNode);\n  }\n  return styleNode;\n}\nconst containerCache = new Map();\nfunction findExistNode(key, option = {}) {\n  const container = getContainer(option);\n  return Array.from(containerCache.get(container)?.children || []).find(node => node.tagName === 'STYLE' && node.getAttribute(getMark(option)) === key);\n}\nfunction removeCSS(key, option = {}) {\n  const existNode = findExistNode(key, option);\n  existNode?.parentNode?.removeChild(existNode);\n}\nfunction updateCSS(css, key, options = {}) {\n  const container = getContainer(options);\n  // Get real parent\n  if (!containerCache.has(container)) {\n    const placeholderStyle = injectCSS('', options);\n    // @ts-ignore\n    const {\n      parentNode\n    } = placeholderStyle;\n    containerCache.set(container, parentNode);\n    parentNode.removeChild(placeholderStyle);\n  }\n  const existNode = findExistNode(key, options);\n  if (existNode) {\n    if (options.cspNonce && existNode.nonce !== options.cspNonce) {\n      existNode.nonce = options.cspNonce;\n    }\n    if (existNode.innerHTML !== css) {\n      existNode.innerHTML = css;\n    }\n    return existNode;\n  }\n  const newNode = injectCSS(css, options);\n  newNode?.setAttribute(getMark(options), key);\n  return newNode;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getStatusClassNames(prefixCls, status, hasFeedback) {\n  return {\n    [`${prefixCls}-status-success`]: status === 'success',\n    [`${prefixCls}-status-warning`]: status === 'warning',\n    [`${prefixCls}-status-error`]: status === 'error',\n    [`${prefixCls}-status-validating`]: status === 'validating',\n    [`${prefixCls}-has-feedback`]: hasFeedback\n  };\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction runOutsideAngular(fn) {\n  // The function that does the same job as `NgZone.runOutsideAngular`.\n  // The difference is that we don't need to rely on the `NgZone` service,\n  // allowing `fromEventOutsideAngular` to function without requiring an explicit\n  // injection context (where we might otherwise call `inject(NgZone)`).\n  return typeof Zone !== 'undefined' ? Zone.root.run(fn) : fn();\n}\n/**\n * This function replaces `runOutsideAngular` with `fromEvent`, introducing a\n * lot of boilerplate where we need to inject the `NgZone` service and then subscribe\n * to `fromEvent` within the `runOutsideAngular` callback.\n */\nfunction fromEventOutsideAngular(target, name, options) {\n  // Allow the event target to be nullable to avoid requiring callers to check\n  // if the target exists. We simply complete the observable immediately,\n  // as this might potentially be used within a `switchMap`.\n  if (!target) {\n    return EMPTY;\n  }\n  return new Observable(subscriber => {\n    // Note that we're wrapping fromEvent with an observable because `fromEvent`\n    // is eager and only calls `addEventListener` when a new subscriber comes in.\n    // Therefore, we're wrapping the subscription with `runOutsideAngular` to ensure\n    // that `addEventListener` is also called outside of Angular when there's a subscriber.\n    return runOutsideAngular(() =>\n    // Casting because the inferred overload is incorrect :(\n    fromEvent(target, name, options).subscribe(subscriber));\n  });\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputBoolean, InputCssPixel, InputNumber, arraysEqual, canUseDom, createDebugEle, ensureInBounds, ensureNumberInRange, fromEventOutsideAngular, getCaretCoordinates, getElementOffset, getEventPosition, getMentions, getPercent, getPrecision, getRegExp, getRepeatedElement, getStatusClassNames, getStyleAsText, inNextTick, injectCSS, isNil, isNonEmptyString, isNotNil, isNumberFinite, isPromise, isStyleSupport, isTemplateRef, isTouchEvent, measure, measureScrollbar, numberAttributeWithInfinityFallback, numberAttributeWithOneFallback, numberAttributeWithZeroFallback, padEnd, padStart, properties, pxToNumber, removeCSS, scrollIntoView, shallowCopyArray, shallowEqual, silentEvent, sum, toArray, toBoolean, toCssPixel, toDecimal, toNumber, updateCSS, valueFunctionProp, wrapIntoObservable };\n"], "mappings": ";;;;;;;;;;;;;;;AAIA,IAAM,cAAc;AAAA,EAClB,YAAY;AACd;;;ACCA,IAAM,SAAS,CAAC;AAChB,IAAM,SAAS;AACf,SAAS,eAAe,MAAM;AAC5B,QAAM,WAAW,KAAK,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,SAAS,GAAG,EAAE;AAC/D,MAAI,OAAO,QAAQ,GAAG;AACpB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,QAAQ,IAAI;AACnB,WAAO;AAAA,EACT;AACF;AACA,SAAS,sBAAsB,gBAAgB,MAAM;AACnD,MAAI,YAAY,cAAc,UAAU,KAAK,YAAY,GAAG,IAAI,GAAG;AACjE,gBAAY,GAAG,IAAI;AAAA,EACrB;AACF;AAEA,IAAM,OAAO,IAAI,SAAS,sBAAsB,IAAI,QAAQ,QAAQ,KAAK,QAAQ,GAAG,GAAG,GAAG,GAAG,IAAI;;;ACvBjG,SAAS,qBAAqB,OAAO,gBAAgB,GAAG;AACtD,MAAI,eAAe,KAAK,GAAG;AACzB,WAAO,OAAO,KAAK;AAAA,EACrB;AACA,SAAO,UAAU,WAAW,IAAI,gBAAgB;AAClD;AAKA,SAAS,eAAe,OAAO;AAI7B,SAAO,CAAC,MAAM,WAAW,KAAK,CAAC,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;AAC1D;AAMA,SAAS,cAAc,cAAc;AACnC,SAAO,wBAAwB,aAAa,aAAa,gBAAgB;AAC3E;;;ACxBA,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;;;ACDA,SAAS,oBAAoB,OAAO;AAClC,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,SAAO,OAAO,UAAU,WAAW,QAAQ,GAAG,KAAK;AACrD;;;ACmCA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU,eAAe,UAAU;AACnD;AAmCA,SAAS,cAAc,OAAO;AAC5B,SAAO,iBAAiB;AAC1B;AASA,SAAS,gCAAgC,OAAO;AAC9C,SAAO,gBAAgB,OAAO,CAAC;AACjC;AAIA,SAAS,oCAAoC,OAAO;AAClD,SAAO,gBAAgB,OAAO,QAAQ;AACxC;AAmFA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,CAAC,KAAK,eAAe,EAAE,QAAQ;AACjC,WAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AACA,QAAM,OAAO,KAAK,sBAAsB;AACxC,QAAM,MAAM,KAAK,cAAc;AAC/B,SAAO;AAAA,IACL,KAAK,KAAK,MAAM,IAAI;AAAA,IACpB,MAAM,KAAK,OAAO,IAAI;AAAA,EACxB;AACF;AAIA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,KAAK,WAAW,OAAO;AACtC;AACA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,aAAa,KAAK,IAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,eAAe,CAAC,IAAI;AAC7E;AAiDA,SAAS,UAAU,KAAK;AACtB,SAAO,CAAC,CAAC,OAAO,OAAO,IAAI,SAAS,cAAc,OAAO,IAAI,UAAU;AACzE;AAuBA,SAAS,eAAe,OAAO;AAC7B,SAAO,OAAO,UAAU,YAAY,SAAS,KAAK;AACpD;AACA,SAAS,UAAU,OAAO,SAAS;AACjC,SAAO,KAAK,MAAM,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,IAAI,OAAO;AACzE;AACA,SAAS,IAAI,OAAO,UAAU,GAAG;AAC/B,SAAO,MAAM,OAAO,CAAC,UAAU,YAAY,WAAW,SAAS,OAAO;AACxE;AAsCA,IAAM,YAAY,OAAO,WAAW;AACpC,IAAM,YAAY,aAAa,OAAO,mBAAmB;AA4YzD,SAAS,YAAY;AACnB,SAAO,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS;AAChF;AASA,IAAM,WAAW;AACjB,SAAS,QAAQ;AAAA,EACf;AACF,IAAI,CAAC,GAAG;AACN,MAAI,MAAM;AACR,WAAO,KAAK,WAAW,OAAO,IAAI,OAAO,QAAQ,IAAI;AAAA,EACvD;AACA,SAAO;AACT;AACA,SAAS,aAAa,QAAQ;AAC5B,MAAI,OAAO,UAAU;AACnB,WAAO,OAAO;AAAA,EAChB;AACA,QAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,SAAO,QAAQ,SAAS;AAC1B;AACA,SAAS,UAAU,KAAK,UAAU,CAAC,GAAG;AACpC,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,QAAM,YAAY,SAAS,cAAc,OAAO;AAChD,MAAI,QAAQ,UAAU;AACpB,cAAU,QAAQ,QAAQ;AAAA,EAC5B;AACA,YAAU,YAAY;AACtB,QAAM,YAAY,aAAa,OAAO;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,QAAQ,WAAW,UAAU,SAAS;AAExC,cAAU,QAAQ,SAAS;AAAA,EAC7B,WAAW,QAAQ,WAAW,YAAY;AAExC,cAAU,aAAa,WAAW,UAAU;AAAA,EAC9C,OAAO;AACL,cAAU,YAAY,SAAS;AAAA,EACjC;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,oBAAI,IAAI;AAC/B,SAAS,cAAc,KAAK,SAAS,CAAC,GAAG;AACvC,QAAM,YAAY,aAAa,MAAM;AACrC,SAAO,MAAM,KAAK,eAAe,IAAI,SAAS,GAAG,YAAY,CAAC,CAAC,EAAE,KAAK,UAAQ,KAAK,YAAY,WAAW,KAAK,aAAa,QAAQ,MAAM,CAAC,MAAM,GAAG;AACtJ;AAKA,SAAS,UAAU,KAAK,KAAK,UAAU,CAAC,GAAG;AACzC,QAAM,YAAY,aAAa,OAAO;AAEtC,MAAI,CAAC,eAAe,IAAI,SAAS,GAAG;AAClC,UAAM,mBAAmB,UAAU,IAAI,OAAO;AAE9C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,mBAAe,IAAI,WAAW,UAAU;AACxC,eAAW,YAAY,gBAAgB;AAAA,EACzC;AACA,QAAM,YAAY,cAAc,KAAK,OAAO;AAC5C,MAAI,WAAW;AACb,QAAI,QAAQ,YAAY,UAAU,UAAU,QAAQ,UAAU;AAC5D,gBAAU,QAAQ,QAAQ;AAAA,IAC5B;AACA,QAAI,UAAU,cAAc,KAAK;AAC/B,gBAAU,YAAY;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,QAAM,UAAU,UAAU,KAAK,OAAO;AACtC,WAAS,aAAa,QAAQ,OAAO,GAAG,GAAG;AAC3C,SAAO;AACT;AAMA,SAAS,oBAAoB,WAAW,QAAQ,aAAa;AAC3D,SAAO;AAAA,IACL,CAAC,GAAG,SAAS,iBAAiB,GAAG,WAAW;AAAA,IAC5C,CAAC,GAAG,SAAS,iBAAiB,GAAG,WAAW;AAAA,IAC5C,CAAC,GAAG,SAAS,eAAe,GAAG,WAAW;AAAA,IAC1C,CAAC,GAAG,SAAS,oBAAoB,GAAG,WAAW;AAAA,IAC/C,CAAC,GAAG,SAAS,eAAe,GAAG;AAAA,EACjC;AACF;AAMA,SAAS,kBAAkB,IAAI;AAK7B,SAAO,OAAO,SAAS,cAAc,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG;AAC9D;AAMA,SAAS,wBAAwB,QAAQ,MAAM,SAAS;AAItD,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,SAAO,IAAI,WAAW,gBAAc;AAKlC,WAAO,kBAAkB;AAAA;AAAA,MAEzB,UAAU,QAAQ,MAAM,OAAO,EAAE,UAAU,UAAU;AAAA,KAAC;AAAA,EACxD,CAAC;AACH;", "names": []}