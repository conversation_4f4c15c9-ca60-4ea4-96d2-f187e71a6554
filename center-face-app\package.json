{"name": "center-face", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build:stage1": "ng build --configuration stage1", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "dev_server": "ng serve --host 0.0.0.0 --port 4202 --ssl true --ssl-cert ./cert.pem --ssl-key ./key.pem --live-reload false"}, "packageManager": "pnpm@9.0.1", "private": true, "dependencies": {"@angular/animations": "^19.2.9", "@angular/common": "^19.2.9", "@angular/compiler": "^19.2.9", "@angular/core": "^19.2.9", "@angular/forms": "^19.2.9", "@angular/platform-browser": "^19.2.9", "@angular/platform-browser-dynamic": "^19.2.9", "@angular/router": "^19.2.9", "@angular/service-worker": "^19.2.9", "@ant-design/icons-angular": "^19.0.0", "@mediapipe/face_detection": "^0.4.1646425229", "@vladmandic/human": "^3.3.5", "ng-zorro-antd": "^19.2.2", "onnxruntime-web": "1.18.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.10", "@angular/cli": "^19.2.10", "@angular/compiler-cli": "^19.2.9", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}