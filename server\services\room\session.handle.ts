import { dataAccess } from '@data/data-access';
import { ESessionStatus, IEntryStat, ISessionData } from '@data/session.data';

export interface RoomSessionData {
  session_id: string;
  name: string;
  project_id: string;
  schedule_id: string;
  start: string;
  end: string;
  test: 0 | 1;
  status: number;
  room_name: string;
  form_status: boolean;
  password_status: boolean;
  version: number;
  entry_stat: IEntryStat;
  subjects: RoomSubjectData[];
}
interface RoomSubjectData {
  subject_id: string;
  name: string;
  code: string;
}

export async function handleRoomSession(
  room_sn: string,
  session_data: RoomSessionData,
) {
  const session = await dataAccess.session.get({
    id: session_data.session_id,
  });

  const schedule = await dataAccess.schedule.get(session_data.schedule_id);
  const subjects = session_data.subjects;

  if (!schedule) {
    await dataAccess.schedule.create({
      id: session_data.schedule_id,
      start: session_data.start,
      end: session_data.end,
      test: session_data.test ? 1 : 0,
      project_id: session_data.project_id,
    });
  }
  const session_row: ISessionData = {
    id: session_data.session_id,
    name: session_data.name,
    schedule_id: session_data.schedule_id,
    start: session_data.start,
    end: session_data.end,
    test: session_data.test,
    room_sn,
    room_name: session_data.room_name,
    form_status: session_data.form_status ? 1 : 0,
    password_status: session_data.password_status ? 1 : 0,
    status: Math.min(session_data.status, ESessionStatus.end),
    entry_status: session_data.entry_stat,
    version: session_data.version,
  };
  if (!session) {
    await dataAccess.session.create(session_row);

    const subjectsToCreate = subjects.map((subject) => ({
      ...subject,
      session_id: session_data.session_id,
    }));
    await dataAccess.subject.bulkCreate(subjectsToCreate);
  } else {
    if (session.status === ESessionStatus.centerUploaded) {
      session_row.status = ESessionStatus.centerUploaded;
    }
    await dataAccess.session.update(
      { id: session_data.session_id },
      session_row,
    );

    const subjectsToUpdate = subjects.map((subject) => ({
      ...subject,
      session_id: session_data.session_id,
    }));
    await dataAccess.subject.bulkCreateOrUpdate(subjectsToUpdate);
  }
}
