{"compilerOptions": {"target": "es2020", "resolveJsonModule": true, "module": "commonjs", "allowJs": true, "checkJs": false, "outDir": ".dist", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": false, "skipLibCheck": true, "strictPropertyInitialization": false, "baseUrl": "./", "paths": {"@data/*": ["./data/*"], "@services/*": ["./services/*"], "@server/*": ["./*"], "@share-types/*": ["../share/types/*"]}}, "types": ["node", "electron"], "exclude": ["node_modules"], "ts-node": {"esm": true, "transpileOnly": true, "require": ["tsconfig-paths/register"]}}