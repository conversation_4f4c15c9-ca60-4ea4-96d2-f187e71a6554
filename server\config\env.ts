import * as path from 'path';
import * as fs from 'fs';
import { ICenterSetting } from '@share-types/settings.types';

if (!global.joyshell) {
  global.joyshell = null as any;
}

let devOpts: ICenterSetting = {} as ICenterSetting;
export const settings = load_config();

// export const IS_RELEASE = __dirname.indexOf('.atar') > 0;
export const cloud_url = settings.CLOUD_URL;
export const port = settings.SERVER_PORT || 19100;
export const secure_port = settings.SERVER_SSL_PORT || 19101;

interface ISettings {
  [key: string]: any;
}

export const data_path = getDataPath();

export function getDataPath(): string {
  const data_path = joyshell
    ? joyshell.Settings.DATA_PATH
    : process.env.JOY_DATAPATH ||
      path.resolve(path.join(__dirname, '../../data'));
  if (!fs.existsSync(data_path)) {
    fs.mkdirSync(data_path);
  }
  return data_path;
}

function load_config() {
  if (joyshell) {
    return joyshell.Settings;
  }
  devOpts = {} as ICenterSetting;
  const conf = `${getDataPath()}/config.json`;
  if (fs.existsSync(conf)) {
    const text = fs.readFileSync(conf, 'utf8');
    devOpts = JSON.parse(text);
  }

  if (!devOpts.SERVER_PORT) {
    devOpts.SERVER_PORT = 3003;
    devOpts.SERVER_SSL_PORT = 3004;
  }

  if (!devOpts.CLOUD_URL) {
    devOpts.CLOUD_URL = 'http://172.16.18.210';
  }

  return devOpts;
}

type BaseNameType = 'forms' | 'packages' | 'temp' | 'exams';
export function getBasePath(name: BaseNameType) {
  const p = path.join(getDataPath(), name);
  if (!fs.existsSync(p)) {
    fs.mkdirSync(p);
  }
  return p;
}

export function getUserConfig<T>(name: string, defval?: T): T {
  if (joyshell) {
    return joyshell.GetUserConfig(name, defval);
  }
  return name in devOpts ? devOpts[name] : defval;
}

interface GenericConfig {
  [key: string]: any;
}
export function setUserConfig(cfgOrKey: GenericConfig | string, value?: any) {
  if (joyshell) {
    joyshell.SetUserConfig(cfgOrKey, value);
    return;
  }
  if (typeof cfgOrKey === 'string') {
    if (typeof value === 'undefined') {
      delete devOpts[cfgOrKey];
    } else {
      devOpts[cfgOrKey] = value;
    }
  } else {
    Object.keys(cfgOrKey).forEach((key) => {
      if (typeof cfgOrKey[key] === 'undefined') {
        delete devOpts[key];
      } else {
        devOpts[key] = cfgOrKey[key];
      }
    });
  }
  const configPath = path.join(getDataPath(), 'config.json');
  fs.writeFileSync(configPath, JSON.stringify(devOpts, null, 2));
}
