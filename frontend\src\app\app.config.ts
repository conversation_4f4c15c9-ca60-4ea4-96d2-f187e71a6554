import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { RouteReuseStrategy, provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { zh_CN, provideNzI18n } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import zh from '@angular/common/locales/zh';
import { FormsModule } from '@angular/forms';
import { provideAnimations } from '@angular/platform-browser/animations';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { RouteStrategyService } from './core/services/routeStrategy.service';
import { apiPrefixInterceptorProvider } from './core/http/api-prefix.interceptor';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

registerLocaleData(zh);

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideNzI18n(zh_CN),
    importProvidersFrom(FormsModule, NzModalModule),
    provideAnimations(),
    provideHttpClient(withInterceptorsFromDi()),
    // {
    //   provide: RouteReuseStrategy,
    //   useClass: RouteStrategyService,
    // },
    apiPrefixInterceptorProvider,
  ],
};
