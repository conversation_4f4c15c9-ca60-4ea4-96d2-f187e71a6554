import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { tap } from 'rxjs/operators';
import { AuthEventService, AuthEvent, EventRes } from './auth-event.service';
import { AuthEventReq, AuthEventType } from '@share-types/auth-event.types';
import { ServerTimeService } from '../server-time.service';
// import { Res } from '@share-types/center.types';

export interface Res<T> {
  status: 'success' | 'failed';
  err?: string;
  info: T;
}
interface AuthEventOpt {
  authHandle: boolean;
  offline: boolean;
}
@Injectable({
  providedIn: 'root',
})
export class AuthEventHttpService {
  constructor(
    private http: HttpClient,
    private authEventService: AuthEventService,
    private serverTime: ServerTimeService
  ) {}

  getAuthEvent(session_id: string, entry_id: string) {
    return this.http.post('/data/auth/event', { session_id, entry_id });
  }

  createAuthEvent<T extends keyof AuthEventReq>(
    type: T,
    detail: AuthEventReq[T],
    opt: Partial<AuthEventOpt> = {}
  ) {
    opt = { authHandle: true, ...opt };
    return this.http
      .post<Res<EventRes>>('/data/auth-event/create', { type, detail })
      .pipe(
        tap((res) => {
          this.handleRes(type, res, detail);
          if (opt.authHandle) {
            // this.authEventService.authHandle(res);
          }
        })
      );
  }

  handleRes<T extends keyof AuthEventReq>(
    type: AuthEventType,
    res: Res<EventRes>,
    detail: AuthEventReq[T]
  ) {
    const { schedule_id, entry_id } = detail as any;
    if (res.info && res.info.event_id) {
      // if (Array.isArray(entry_id)) {
      //   entry_id.forEach((id) => {
      //     const event = new AuthEvent(res.info.event_id, type, session_id, id);
      //     event.created_at = this.serverTime.getServerTime();
      //     event.detail = detail;
      //     if (res.info.data) {
      //       event.offline_data = res.info.data;
      //     }
      //     this.authEventService.addEvent(id, event);
      //   });
      // } else {
      const event = new AuthEvent(res.info.event_id, type, schedule_id);
      event.created_at = this.serverTime.getServerTimeValue();
      event.detail = detail;
      if (res.info.data) {
        event.offline_data = res.info.data;
      }
      this.authEventService.addEvent(schedule_id, event);
    }
    // } else {
    //   console.error(res);
    // }
  }
}
