import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, Signal } from '@angular/core';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { Router } from '@angular/router';
import { ScheduleListComponent } from './schedule-list/schedule-list.component';
import { ServerTimeService } from '../core/service/server-time.service';
import { toSignal } from '@angular/core/rxjs-interop';
import { CustomDatePipe } from '../shared/pipes/custom-date.pipe';
import { Center, CenterService } from '@app/core/service/center.service';
import { AuthService } from '@app/core/service/auth.service';
@Component({
    selector: 'app-center',
    imports: [
        CommonModule,
        NzIconModule,
        NzDropDownModule,
        ScheduleListComponent,
        CustomDatePipe
    ],
    template: `
    <nav>
        <div class="nav">
          <div class="nav-info">
            <span class="center">{{ center().name }}<ng-container *ngIf="center().selectedGroup?.group_name"> - {{ center().selectedGroup!.group_name }}</ng-container></span>
            <span class="server-time">{{ serverTime() | customDate:'date' }}</span>
          </div>
          <span class="settings" nz-icon nzType="setting" nzTheme="outline" nz-dropdown [nzDropdownMenu]="menu" nzTrigger="click"></span>
          <nz-dropdown-menu #menu="nzDropdownMenu">
            <ul nz-menu>
              <li nz-menu-item nzDanger (click)="logout()">注销</li>
            </ul>
          </nz-dropdown-menu>
        </div>
        <div class="project">
          <span>{{ center().project_name }}</span>
        </div>
    </nav>
    <main>
      <app-schedule-list></app-schedule-list>
    </main>
  `,
    styles: `
   @use 'variables' as *;
   @use 'mixins' as *;
    :host {
      display: block;
      height: 100%;
      display: flex;
      flex-direction: column; 
    }
    nav {
      min-height: 60px;
      background: url("/assets/images/nav_bg_top.png") #fff no-repeat center;
      background-size: cover;
      padding: 10px;
      .nav {
        @include flex-box(row, space-between);
        .nav-info {
          @include flex-box(row, space-between, center, wrap);
          flex: 1;
          .server-time {
            min-width: fit-content;
          }
          .center {
            min-width: fit-content;
          }
        }
        .settings {
          cursor: pointer;
          vertical-align: sub;
          min-width: fit-content;
          margin-left: 10px;
          font-size: 20px;
        }
      }
      .project {
        font-size: 18px;
        font-weight: 600;
        text-align: center;
      }
    }
    main {
      flex: 1;
      .project {
        background-color: #fff;
        text-align: center;
        font-size: 18px;
      }
      display: flex;
      flex-direction: column;
      app-schedule-list {
        flex: 1;
      }
    }
    @media screen and (max-width: 500px) {
      nav {
        .server-time {
          display: none;
        }
      }
    }
  `,
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardComponent implements OnInit {
  serverTime!: Signal<number | undefined>;

  center!: Signal<Center>;

  constructor(
    private router: Router,
    private serverTimeService: ServerTimeService,
    private centerService: CenterService,
    private authService: AuthService
  ) {
    this.serverTime = toSignal(this.serverTimeService.getServerTimeStream(), { initialValue: 0 });
    this.center = this.centerService.center;
  }

  ngOnInit(): void {

  }

  logout() {
    const serverCode = this.authService.serverCode;
    const serverCodeTimestamp = this.authService.serverCodeTimestamp;
    let navigationExtras = {};

    if (serverCode && serverCodeTimestamp) {
      const currentTime = Date.now();
      const oneWeekInMilliseconds = 7 * 24 * 60 * 60 * 1000;
      if ((currentTime - serverCodeTimestamp) < oneWeekInMilliseconds) {
        navigationExtras = { queryParams: { serverCode: serverCode } };
      }
    }

    this.authService.logout();
    this.centerService.logout();
    this.router.navigate(['/login'], navigationExtras);
  }
}
