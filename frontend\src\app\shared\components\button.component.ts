import { CommonModule } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
  selector: 'app-button',
  standalone: true,
  imports: [CommonModule, NzButtonModule, NzIconModule],
  template: ` <button nz-button nzType="primary"></button> `,
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
})
export class ButtonComponent implements OnInit {
  ngOnInit(): void {}
}
