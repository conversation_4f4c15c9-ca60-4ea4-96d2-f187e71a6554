/* You can add global styles to this file, and also import other style files */

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  // 不能选中文字
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}


.can-select {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}


/* 定义滚动条的宽度和轨道颜色 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #f5f5f5;
}

/* 定义滚动条的样式 */
::-webkit-scrollbar-thumb {
  background-color: #c6e1fd;
  border-radius: 10px;
}

/* 定义滚动条在悬停状态下的样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: #7db6f4;
}
