{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-radio.mjs", "../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-pagination.mjs", "../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-table.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, forwardRef, booleanAttribute, Input, ChangeDetectionStrategy, ViewEncapsulation, Component, inject, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i2 from '@angular/cdk/bidi';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\nimport * as i1 from '@angular/cdk/a11y';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"inputElement\"];\nconst _c2 = [\"nz-radio\", \"\"];\nclass NzRadioService {\n  selected$ = new ReplaySubject(1);\n  touched$ = new Subject();\n  disabled$ = new ReplaySubject(1);\n  name$ = new ReplaySubject(1);\n  touch() {\n    this.touched$.next();\n  }\n  select(value) {\n    this.selected$.next(value);\n  }\n  setDisabled(value) {\n    this.disabled$.next(value);\n  }\n  setName(value) {\n    this.name$.next(value);\n  }\n  static ɵfac = function NzRadioService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzRadioService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzRadioService,\n    factory: NzRadioService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRadioService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass NzRadioGroupComponent {\n  cdr;\n  nzRadioService;\n  directionality;\n  value = null;\n  destroy$ = new Subject();\n  isNzDisableFirstChange = true;\n  onChange = () => {};\n  onTouched = () => {};\n  nzDisabled = false;\n  nzButtonStyle = 'outline';\n  nzSize = 'default';\n  nzName = null;\n  dir = 'ltr';\n  constructor(cdr, nzRadioService, directionality) {\n    this.cdr = cdr;\n    this.nzRadioService = nzRadioService;\n    this.directionality = directionality;\n  }\n  ngOnInit() {\n    this.nzRadioService.selected$.pipe(takeUntil(this.destroy$)).subscribe(value => {\n      if (this.value !== value) {\n        this.value = value;\n        this.onChange(this.value);\n      }\n    });\n    this.nzRadioService.touched$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      Promise.resolve().then(() => this.onTouched());\n    });\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzDisabled,\n      nzName\n    } = changes;\n    if (nzDisabled) {\n      this.nzRadioService.setDisabled(this.nzDisabled);\n    }\n    if (nzName) {\n      this.nzRadioService.setName(this.nzName);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  writeValue(value) {\n    this.value = value;\n    this.nzRadioService.select(value);\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || isDisabled;\n    this.isNzDisableFirstChange = false;\n    this.nzRadioService.setDisabled(this.nzDisabled);\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function NzRadioGroupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzRadioGroupComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzRadioService), i0.ɵɵdirectiveInject(i2.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzRadioGroupComponent,\n    selectors: [[\"nz-radio-group\"]],\n    hostAttrs: [1, \"ant-radio-group\"],\n    hostVars: 8,\n    hostBindings: function NzRadioGroupComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-radio-group-large\", ctx.nzSize === \"large\")(\"ant-radio-group-small\", ctx.nzSize === \"small\")(\"ant-radio-group-solid\", ctx.nzButtonStyle === \"solid\")(\"ant-radio-group-rtl\", ctx.dir === \"rtl\");\n      }\n    },\n    inputs: {\n      nzDisabled: [2, \"nzDisabled\", \"nzDisabled\", booleanAttribute],\n      nzButtonStyle: \"nzButtonStyle\",\n      nzSize: \"nzSize\",\n      nzName: \"nzName\"\n    },\n    exportAs: [\"nzRadioGroup\"],\n    features: [i0.ɵɵProvidersFeature([NzRadioService, {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzRadioGroupComponent),\n      multi: true\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function NzRadioGroupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRadioGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-radio-group',\n      exportAs: 'nzRadioGroup',\n      preserveWhitespaces: false,\n      template: ` <ng-content></ng-content> `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [NzRadioService, {\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzRadioGroupComponent),\n        multi: true\n      }],\n      host: {\n        class: 'ant-radio-group',\n        '[class.ant-radio-group-large]': `nzSize === 'large'`,\n        '[class.ant-radio-group-small]': `nzSize === 'small'`,\n        '[class.ant-radio-group-solid]': `nzButtonStyle === 'solid'`,\n        '[class.ant-radio-group-rtl]': `dir === 'rtl'`\n      }\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzRadioService\n  }, {\n    type: i2.Directionality\n  }], {\n    nzDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzButtonStyle: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzName: [{\n      type: Input\n    }]\n  });\n})();\nclass NzRadioComponent {\n  ngZone;\n  elementRef;\n  cdr;\n  focusMonitor;\n  isNgModel = false;\n  destroy$ = new Subject();\n  isNzDisableFirstChange = true;\n  directionality = inject(Directionality);\n  nzRadioService = inject(NzRadioService, {\n    optional: true\n  });\n  nzFormStatusService = inject(NzFormStatusService, {\n    optional: true\n  });\n  isChecked = false;\n  name = null;\n  onChange = () => {};\n  onTouched = () => {};\n  inputElement;\n  nzValue = null;\n  nzDisabled = false;\n  nzAutoFocus = false;\n  isRadioButton = false;\n  dir = 'ltr';\n  focus() {\n    this.focusMonitor.focusVia(this.inputElement, 'keyboard');\n  }\n  blur() {\n    this.inputElement.nativeElement.blur();\n  }\n  constructor(ngZone, elementRef, cdr, focusMonitor) {\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    this.cdr = cdr;\n    this.focusMonitor = focusMonitor;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  writeValue(value) {\n    this.isChecked = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.isNgModel = true;\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  ngOnInit() {\n    if (this.nzRadioService) {\n      this.nzRadioService.name$.pipe(takeUntil(this.destroy$)).subscribe(name => {\n        this.name = name;\n        this.cdr.markForCheck();\n      });\n      this.nzRadioService.disabled$.pipe(takeUntil(this.destroy$)).subscribe(disabled => {\n        this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n        this.isNzDisableFirstChange = false;\n        this.cdr.markForCheck();\n      });\n      this.nzRadioService.selected$.pipe(takeUntil(this.destroy$)).subscribe(value => {\n        const isChecked = this.isChecked;\n        this.isChecked = this.nzValue === value;\n        // We don't have to run `onChange()` on each `nz-radio` button whenever the `selected$` emits.\n        // If we have 8 `nz-radio` buttons within the `nz-radio-group` and they're all connected with\n        // `ngModel` or `formControl` then `onChange()` will be called 8 times for each `nz-radio` button.\n        // We prevent this by checking if `isChecked` has been changed or not.\n        if (this.isNgModel && isChecked !== this.isChecked &&\n        // We're only intereted if `isChecked` has been changed to `false` value to emit `false` to the ascendant form,\n        // since we already emit `true` within the `setupClickListener`.\n        this.isChecked === false) {\n          this.onChange(false);\n        }\n        this.cdr.markForCheck();\n      });\n    }\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        Promise.resolve().then(() => this.onTouched());\n        if (this.nzRadioService) {\n          this.nzRadioService.touch();\n        }\n      }\n    });\n    this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.setupClickListener();\n  }\n  ngAfterViewInit() {\n    if (this.nzAutoFocus) {\n      this.focus();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.focusMonitor.stopMonitoring(this.elementRef);\n  }\n  setupClickListener() {\n    fromEventOutsideAngular(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n      /** prevent label click triggered twice. **/\n      event.stopPropagation();\n      event.preventDefault();\n      if (this.nzDisabled || this.isChecked) {\n        return;\n      }\n      this.ngZone.run(() => {\n        this.focus();\n        this.nzRadioService?.select(this.nzValue);\n        if (this.isNgModel) {\n          this.isChecked = true;\n          this.onChange(true);\n        }\n        this.cdr.markForCheck();\n      });\n    });\n  }\n  static ɵfac = function NzRadioComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzRadioComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzRadioComponent,\n    selectors: [[\"\", \"nz-radio\", \"\"], [\"\", \"nz-radio-button\", \"\"]],\n    viewQuery: function NzRadioComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputElement = _t.first);\n      }\n    },\n    hostVars: 18,\n    hostBindings: function NzRadioComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-radio-wrapper-in-form-item\", !!ctx.nzFormStatusService)(\"ant-radio-wrapper\", !ctx.isRadioButton)(\"ant-radio-button-wrapper\", ctx.isRadioButton)(\"ant-radio-wrapper-checked\", ctx.isChecked && !ctx.isRadioButton)(\"ant-radio-button-wrapper-checked\", ctx.isChecked && ctx.isRadioButton)(\"ant-radio-wrapper-disabled\", ctx.nzDisabled && !ctx.isRadioButton)(\"ant-radio-button-wrapper-disabled\", ctx.nzDisabled && ctx.isRadioButton)(\"ant-radio-wrapper-rtl\", !ctx.isRadioButton && ctx.dir === \"rtl\")(\"ant-radio-button-wrapper-rtl\", ctx.isRadioButton && ctx.dir === \"rtl\");\n      }\n    },\n    inputs: {\n      nzValue: \"nzValue\",\n      nzDisabled: [2, \"nzDisabled\", \"nzDisabled\", booleanAttribute],\n      nzAutoFocus: [2, \"nzAutoFocus\", \"nzAutoFocus\", booleanAttribute],\n      isRadioButton: [2, \"nz-radio-button\", \"isRadioButton\", booleanAttribute]\n    },\n    exportAs: [\"nzRadio\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzRadioComponent),\n      multi: true\n    }])],\n    attrs: _c2,\n    ngContentSelectors: _c0,\n    decls: 6,\n    vars: 24,\n    consts: [[\"inputElement\", \"\"], [\"type\", \"radio\", 3, \"disabled\", \"checked\"]],\n    template: function NzRadioComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"span\");\n        i0.ɵɵelement(1, \"input\", 1, 0)(3, \"span\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"span\");\n        i0.ɵɵprojection(5);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-radio\", !ctx.isRadioButton)(\"ant-radio-checked\", ctx.isChecked && !ctx.isRadioButton)(\"ant-radio-disabled\", ctx.nzDisabled && !ctx.isRadioButton)(\"ant-radio-button\", ctx.isRadioButton)(\"ant-radio-button-checked\", ctx.isChecked && ctx.isRadioButton)(\"ant-radio-button-disabled\", ctx.nzDisabled && ctx.isRadioButton);\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"ant-radio-input\", !ctx.isRadioButton)(\"ant-radio-button-input\", ctx.isRadioButton);\n        i0.ɵɵproperty(\"disabled\", ctx.nzDisabled)(\"checked\", ctx.isChecked);\n        i0.ɵɵattribute(\"autofocus\", ctx.nzAutoFocus ? \"autofocus\" : null)(\"name\", ctx.name);\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"ant-radio-inner\", !ctx.isRadioButton)(\"ant-radio-button-inner\", ctx.isRadioButton);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRadioComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-radio],[nz-radio-button]',\n      exportAs: 'nzRadio',\n      preserveWhitespaces: false,\n      template: `\n    <span\n      [class.ant-radio]=\"!isRadioButton\"\n      [class.ant-radio-checked]=\"isChecked && !isRadioButton\"\n      [class.ant-radio-disabled]=\"nzDisabled && !isRadioButton\"\n      [class.ant-radio-button]=\"isRadioButton\"\n      [class.ant-radio-button-checked]=\"isChecked && isRadioButton\"\n      [class.ant-radio-button-disabled]=\"nzDisabled && isRadioButton\"\n    >\n      <input\n        #inputElement\n        type=\"radio\"\n        [attr.autofocus]=\"nzAutoFocus ? 'autofocus' : null\"\n        [class.ant-radio-input]=\"!isRadioButton\"\n        [class.ant-radio-button-input]=\"isRadioButton\"\n        [disabled]=\"nzDisabled\"\n        [checked]=\"isChecked\"\n        [attr.name]=\"name\"\n      />\n      <span [class.ant-radio-inner]=\"!isRadioButton\" [class.ant-radio-button-inner]=\"isRadioButton\"></span>\n    </span>\n    <span><ng-content></ng-content></span>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzRadioComponent),\n        multi: true\n      }],\n      host: {\n        '[class.ant-radio-wrapper-in-form-item]': '!!nzFormStatusService',\n        '[class.ant-radio-wrapper]': '!isRadioButton',\n        '[class.ant-radio-button-wrapper]': 'isRadioButton',\n        '[class.ant-radio-wrapper-checked]': 'isChecked && !isRadioButton',\n        '[class.ant-radio-button-wrapper-checked]': 'isChecked && isRadioButton',\n        '[class.ant-radio-wrapper-disabled]': 'nzDisabled && !isRadioButton',\n        '[class.ant-radio-button-wrapper-disabled]': 'nzDisabled && isRadioButton',\n        '[class.ant-radio-wrapper-rtl]': `!isRadioButton && dir === 'rtl'`,\n        '[class.ant-radio-button-wrapper-rtl]': `isRadioButton && dir === 'rtl'`\n      }\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.FocusMonitor\n  }], {\n    inputElement: [{\n      type: ViewChild,\n      args: ['inputElement', {\n        static: true\n      }]\n    }],\n    nzValue: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzAutoFocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    isRadioButton: [{\n      type: Input,\n      args: [{\n        alias: 'nz-radio-button',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRadioModule {\n  static ɵfac = function NzRadioModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzRadioModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzRadioModule,\n    imports: [NzRadioComponent, NzRadioGroupComponent],\n    exports: [NzRadioComponent, NzRadioGroupComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRadioModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzRadioComponent, NzRadioGroupComponent],\n      exports: [NzRadioComponent, NzRadioGroupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzRadioComponent, NzRadioGroupComponent, NzRadioModule, NzRadioService };\n", "import { __esDecorate, __runInitializers } from 'tslib';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Output, Input, ChangeDetectionStrategy, ViewEncapsulation, Component, ViewChild, numberAttribute, booleanAttribute, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i3 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2$1 from 'ng-zorro-antd/core/services';\nimport { gridResponsiveMap, NzBreakpointEnum } from 'ng-zorro-antd/core/services';\nimport * as i1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport { toNumber } from 'ng-zorro-antd/core/util';\nimport * as i1$1 from 'ng-zorro-antd/select';\nimport { NzSelectModule } from 'ng-zorro-antd/select';\nimport * as i1$2 from '@angular/cdk/bidi';\nimport * as i1$3 from 'ng-zorro-antd/i18n';\n\n/* eslint-disable */\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"nz-pagination-item\", \"\"];\nconst _c1 = (a0, a1) => ({\n  $implicit: a0,\n  page: a1\n});\nfunction NzPaginationItemComponent_ng_template_0_Case_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r1 = i0.ɵɵnextContext().page;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(page_r1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 4);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 5);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 2);\n    i0.ɵɵtemplate(1, NzPaginationItemComponent_ng_template_0_Case_1_Conditional_1_Template, 1, 0, \"nz-icon\", 4)(2, NzPaginationItemComponent_ng_template_0_Case_1_Conditional_2_Template, 1, 0, \"nz-icon\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale.prev_page);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.direction === \"rtl\" ? 1 : 2);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 5);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_2_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 4);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 2);\n    i0.ɵɵtemplate(1, NzPaginationItemComponent_ng_template_0_Case_2_Conditional_1_Template, 1, 0, \"nz-icon\", 5)(2, NzPaginationItemComponent_ng_template_0_Case_2_Conditional_2_Template, 1, 0, \"nz-icon\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale.next_page);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.direction === \"rtl\" ? 1 : 2);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 8);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 9);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_0_Template, 1, 0, \"nz-icon\", 8)(1, NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_1_Template, 1, 0, \"nz-icon\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵconditional(ctx_r1.direction === \"rtl\" ? 0 : 1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 9);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 8);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_0_Template, 1, 0, \"nz-icon\", 9)(1, NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_1_Template, 1, 0, \"nz-icon\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵconditional(ctx_r1.direction === \"rtl\" ? 0 : 1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 3)(1, \"div\", 6);\n    i0.ɵɵtemplate(2, NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Template, 2, 1)(3, NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Template, 2, 1);\n    i0.ɵɵelementStart(4, \"span\", 7);\n    i0.ɵɵtext(5, \"\\u2022\\u2022\\u2022\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const type_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional((tmp_5_0 = type_r3) === \"prev_5\" ? 2 : tmp_5_0 === \"next_5\" ? 3 : -1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Case_0_Template, 2, 1, \"a\")(1, NzPaginationItemComponent_ng_template_0_Case_1_Template, 3, 3, \"button\", 2)(2, NzPaginationItemComponent_ng_template_0_Case_2_Template, 3, 3, \"button\", 2)(3, NzPaginationItemComponent_ng_template_0_Case_3_Template, 6, 1, \"a\", 3);\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵconditional((tmp_4_0 = type_r3) === \"page\" ? 0 : tmp_4_0 === \"prev\" ? 1 : tmp_4_0 === \"next\" ? 2 : 3);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_2_Template(rf, ctx) {}\nconst _c2 = [\"nz-pagination-options\", \"\"];\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction NzPaginationOptionsComponent_Conditional_0_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 3);\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r3.label)(\"nzValue\", option_r3.value);\n  }\n}\nfunction NzPaginationOptionsComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-select\", 2);\n    i0.ɵɵlistener(\"ngModelChange\", function NzPaginationOptionsComponent_Conditional_0_Template_nz_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageSizeChange($event));\n    });\n    i0.ɵɵrepeaterCreate(1, NzPaginationOptionsComponent_Conditional_0_For_2_Template, 1, 2, \"nz-option\", 3, _forTrack0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzDisabled\", ctx_r1.disabled)(\"nzSize\", ctx_r1.nzSize)(\"ngModel\", ctx_r1.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r1.listOfPageSizeOption);\n  }\n}\nfunction NzPaginationOptionsComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"input\", 4);\n    i0.ɵɵlistener(\"keydown.enter\", function NzPaginationOptionsComponent_Conditional_1_Template_input_keydown_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.jumpToPageViaInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.locale.jump_to, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.locale.page, \" \");\n  }\n}\nconst _c3 = [\"containerTemplate\"];\nconst _c4 = (a0, a1) => ({\n  $implicit: a0,\n  range: a1\n});\nfunction NzPaginationDefaultComponent_ng_template_0_Conditional_1_ng_template_1_Template(rf, ctx) {}\nfunction NzPaginationDefaultComponent_ng_template_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 1);\n    i0.ɵɵtemplate(1, NzPaginationDefaultComponent_ng_template_0_Conditional_1_ng_template_1_Template, 0, 0, \"ng-template\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.showTotal)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, ctx_r0.total, ctx_r0.ranges));\n  }\n}\nfunction NzPaginationDefaultComponent_ng_template_0_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 5);\n    i0.ɵɵlistener(\"gotoIndex\", function NzPaginationDefaultComponent_ng_template_0_For_3_Template_li_gotoIndex_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.jumpPage($event));\n    })(\"diffIndex\", function NzPaginationDefaultComponent_ng_template_0_For_3_Template_li_diffIndex_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.jumpDiff($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"locale\", ctx_r0.locale)(\"type\", page_r3.type)(\"index\", page_r3.index)(\"disabled\", !!page_r3.disabled)(\"itemRender\", ctx_r0.itemRender)(\"active\", ctx_r0.pageIndex === page_r3.index)(\"direction\", ctx_r0.dir);\n  }\n}\nfunction NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 6);\n    i0.ɵɵlistener(\"pageIndexChange\", function NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template_li_pageIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onPageIndexChange($event));\n    })(\"pageSizeChange\", function NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template_li_pageSizeChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onPageSizeChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"total\", ctx_r0.total)(\"locale\", ctx_r0.locale)(\"disabled\", ctx_r0.disabled)(\"nzSize\", ctx_r0.nzSize)(\"showSizeChanger\", ctx_r0.showSizeChanger)(\"showQuickJumper\", ctx_r0.showQuickJumper)(\"pageIndex\", ctx_r0.pageIndex)(\"pageSize\", ctx_r0.pageSize)(\"pageSizeOptions\", ctx_r0.pageSizeOptions);\n  }\n}\nfunction NzPaginationDefaultComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\");\n    i0.ɵɵtemplate(1, NzPaginationDefaultComponent_ng_template_0_Conditional_1_Template, 2, 5, \"li\", 1);\n    i0.ɵɵrepeaterCreate(2, NzPaginationDefaultComponent_ng_template_0_For_3_Template, 1, 7, \"li\", 2, i0.ɵɵcomponentInstance().trackByPageItem, true);\n    i0.ɵɵtemplate(4, NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template, 1, 9, \"li\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.showTotal ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.listOfPageItem);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.showQuickJumper || ctx_r0.showSizeChanger ? 4 : -1);\n  }\n}\nfunction NzPaginationSimpleComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\")(1, \"li\", 1);\n    i0.ɵɵlistener(\"click\", function NzPaginationSimpleComponent_ng_template_0_Template_li_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prePage());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"li\", 2)(3, \"input\", 3);\n    i0.ɵɵlistener(\"keydown.enter\", function NzPaginationSimpleComponent_ng_template_0_Template_input_keydown_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.jumpToPageViaInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 4);\n    i0.ɵɵtext(5, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"li\", 5);\n    i0.ɵɵlistener(\"click\", function NzPaginationSimpleComponent_ng_template_0_Template_li_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextPage());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"locale\", ctx_r1.locale)(\"disabled\", ctx_r1.isFirstIndex)(\"direction\", ctx_r1.dir)(\"itemRender\", ctx_r1.itemRender);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale.prev_page);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"title\", ctx_r1.pageIndex + \"/\" + ctx_r1.lastIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled)(\"value\", ctx_r1.pageIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.lastIndex, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"locale\", ctx_r1.locale)(\"disabled\", ctx_r1.isLastIndex)(\"direction\", ctx_r1.dir)(\"itemRender\", ctx_r1.itemRender);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale == null ? null : ctx_r1.locale.next_page);\n  }\n}\nfunction NzPaginationComponent_Conditional_0_Conditional_0_ng_template_0_Template(rf, ctx) {}\nfunction NzPaginationComponent_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Conditional_0_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const simplePagination_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", simplePagination_r2.template);\n  }\n}\nfunction NzPaginationComponent_Conditional_0_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzPaginationComponent_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const defaultPagination_r3 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", defaultPagination_r3.template);\n  }\n}\nfunction NzPaginationComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Conditional_0_Template, 1, 1, null, 4)(1, NzPaginationComponent_Conditional_0_Conditional_1_Template, 1, 1, null, 4);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r3.nzSimple ? 0 : 1);\n  }\n}\nclass NzPaginationItemComponent {\n  active = false;\n  locale;\n  index = null;\n  disabled = false;\n  direction = 'ltr';\n  type = null;\n  itemRender = null;\n  diffIndex = new EventEmitter();\n  gotoIndex = new EventEmitter();\n  title = null;\n  clickItem() {\n    if (!this.disabled) {\n      if (this.type === 'page') {\n        this.gotoIndex.emit(this.index);\n      } else {\n        this.diffIndex.emit({\n          next: 1,\n          prev: -1,\n          prev_5: -5,\n          next_5: 5\n        }[this.type]);\n      }\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      locale,\n      index,\n      type\n    } = changes;\n    if (locale || index || type) {\n      this.title = {\n        page: `${this.index}`,\n        next: this.locale?.next_page,\n        prev: this.locale?.prev_page,\n        prev_5: this.locale?.prev_5,\n        next_5: this.locale?.next_5\n      }[this.type];\n    }\n  }\n  static ɵfac = function NzPaginationItemComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzPaginationItemComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzPaginationItemComponent,\n    selectors: [[\"li\", \"nz-pagination-item\", \"\"]],\n    hostVars: 19,\n    hostBindings: function NzPaginationItemComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function NzPaginationItemComponent_click_HostBindingHandler() {\n          return ctx.clickItem();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"title\", ctx.title);\n        i0.ɵɵclassProp(\"ant-pagination-prev\", ctx.type === \"prev\")(\"ant-pagination-next\", ctx.type === \"next\")(\"ant-pagination-item\", ctx.type === \"page\")(\"ant-pagination-jump-prev\", ctx.type === \"prev_5\")(\"ant-pagination-jump-prev-custom-icon\", ctx.type === \"prev_5\")(\"ant-pagination-jump-next\", ctx.type === \"next_5\")(\"ant-pagination-jump-next-custom-icon\", ctx.type === \"next_5\")(\"ant-pagination-disabled\", ctx.disabled)(\"ant-pagination-item-active\", ctx.active);\n      }\n    },\n    inputs: {\n      active: \"active\",\n      locale: \"locale\",\n      index: \"index\",\n      disabled: \"disabled\",\n      direction: \"direction\",\n      type: \"type\",\n      itemRender: \"itemRender\"\n    },\n    outputs: {\n      diffIndex: \"diffIndex\",\n      gotoIndex: \"gotoIndex\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    attrs: _c0,\n    decls: 3,\n    vars: 5,\n    consts: [[\"renderItemTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"type\", \"button\", 1, \"ant-pagination-item-link\", 3, \"disabled\"], [1, \"ant-pagination-item-link\"], [\"nzType\", \"right\"], [\"nzType\", \"left\"], [1, \"ant-pagination-item-container\"], [1, \"ant-pagination-item-ellipsis\"], [\"nzType\", \"double-right\", 1, \"ant-pagination-item-link-icon\"], [\"nzType\", \"double-left\", 1, \"ant-pagination-item-link-icon\"]],\n    template: function NzPaginationItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Template, 4, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzPaginationItemComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        const renderItemTemplate_r4 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.itemRender || renderItemTemplate_r4)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c1, ctx.type, ctx.index));\n      }\n    },\n    dependencies: [NzIconModule, i1.NzIconDirective, NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationItemComponent, [{\n    type: Component,\n    args: [{\n      selector: 'li[nz-pagination-item]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template #renderItemTemplate let-type let-page=\"page\">\n      @switch (type) {\n        @case ('page') {\n          <a>{{ page }}</a>\n        }\n        @case ('prev') {\n          <button type=\"button\" [disabled]=\"disabled\" [attr.title]=\"locale.prev_page\" class=\"ant-pagination-item-link\">\n            @if (direction === 'rtl') {\n              <nz-icon nzType=\"right\" />\n            } @else {\n              <nz-icon nzType=\"left\" />\n            }\n          </button>\n        }\n        @case ('next') {\n          <button type=\"button\" [disabled]=\"disabled\" [attr.title]=\"locale.next_page\" class=\"ant-pagination-item-link\">\n            @if (direction === 'rtl') {\n              <nz-icon nzType=\"left\" />\n            } @else {\n              <nz-icon nzType=\"right\" />\n            }\n          </button>\n        }\n        @default {\n          <a class=\"ant-pagination-item-link\">\n            <div class=\"ant-pagination-item-container\">\n              @switch (type) {\n                @case ('prev_5') {\n                  @if (direction === 'rtl') {\n                    <nz-icon nzType=\"double-right\" class=\"ant-pagination-item-link-icon\" />\n                  } @else {\n                    <nz-icon nzType=\"double-left\" class=\"ant-pagination-item-link-icon\" />\n                  }\n                }\n                @case ('next_5') {\n                  @if (direction === 'rtl') {\n                    <nz-icon nzType=\"double-left\" class=\"ant-pagination-item-link-icon\" />\n                  } @else {\n                    <nz-icon nzType=\"double-right\" class=\"ant-pagination-item-link-icon\" />\n                  }\n                }\n              }\n              <span class=\"ant-pagination-item-ellipsis\">•••</span>\n            </div>\n          </a>\n        }\n      }\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"itemRender || renderItemTemplate\"\n      [ngTemplateOutletContext]=\"{ $implicit: type, page: index }\"\n    />\n  `,\n      host: {\n        '[class.ant-pagination-prev]': `type === 'prev'`,\n        '[class.ant-pagination-next]': `type === 'next'`,\n        '[class.ant-pagination-item]': `type === 'page'`,\n        '[class.ant-pagination-jump-prev]': `type === 'prev_5'`,\n        '[class.ant-pagination-jump-prev-custom-icon]': `type === 'prev_5'`,\n        '[class.ant-pagination-jump-next]': `type === 'next_5'`,\n        '[class.ant-pagination-jump-next-custom-icon]': `type === 'next_5'`,\n        '[class.ant-pagination-disabled]': 'disabled',\n        '[class.ant-pagination-item-active]': 'active',\n        '[attr.title]': 'title',\n        '(click)': 'clickItem()'\n      },\n      imports: [NzIconModule, NgTemplateOutlet]\n    }]\n  }], null, {\n    active: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    direction: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    itemRender: [{\n      type: Input\n    }],\n    diffIndex: [{\n      type: Output\n    }],\n    gotoIndex: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzPaginationOptionsComponent {\n  nzSize = 'default';\n  disabled = false;\n  showSizeChanger = false;\n  showQuickJumper = false;\n  locale;\n  total = 0;\n  pageIndex = 1;\n  pageSize = 10;\n  pageSizeOptions = [];\n  pageIndexChange = new EventEmitter();\n  pageSizeChange = new EventEmitter();\n  listOfPageSizeOption = [];\n  onPageSizeChange(size) {\n    if (this.pageSize !== size) {\n      this.pageSizeChange.next(size);\n    }\n  }\n  jumpToPageViaInput($event) {\n    const target = $event.target;\n    const index = Math.floor(toNumber(target.value, this.pageIndex));\n    this.pageIndexChange.next(index);\n    target.value = '';\n  }\n  ngOnChanges(changes) {\n    const {\n      pageSize,\n      pageSizeOptions,\n      locale\n    } = changes;\n    if (pageSize || pageSizeOptions || locale) {\n      this.listOfPageSizeOption = [...new Set([...this.pageSizeOptions, this.pageSize])].map(item => ({\n        value: item,\n        label: `${item} ${this.locale.items_per_page}`\n      }));\n    }\n  }\n  static ɵfac = function NzPaginationOptionsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzPaginationOptionsComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzPaginationOptionsComponent,\n    selectors: [[\"li\", \"nz-pagination-options\", \"\"]],\n    hostAttrs: [1, \"ant-pagination-options\"],\n    inputs: {\n      nzSize: \"nzSize\",\n      disabled: \"disabled\",\n      showSizeChanger: \"showSizeChanger\",\n      showQuickJumper: \"showQuickJumper\",\n      locale: \"locale\",\n      total: \"total\",\n      pageIndex: \"pageIndex\",\n      pageSize: \"pageSize\",\n      pageSizeOptions: \"pageSizeOptions\"\n    },\n    outputs: {\n      pageIndexChange: \"pageIndexChange\",\n      pageSizeChange: \"pageSizeChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    attrs: _c2,\n    decls: 2,\n    vars: 2,\n    consts: [[1, \"ant-pagination-options-size-changer\", 3, \"nzDisabled\", \"nzSize\", \"ngModel\"], [1, \"ant-pagination-options-quick-jumper\"], [1, \"ant-pagination-options-size-changer\", 3, \"ngModelChange\", \"nzDisabled\", \"nzSize\", \"ngModel\"], [3, \"nzLabel\", \"nzValue\"], [3, \"keydown.enter\", \"disabled\"]],\n    template: function NzPaginationOptionsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzPaginationOptionsComponent_Conditional_0_Template, 3, 3, \"nz-select\", 0)(1, NzPaginationOptionsComponent_Conditional_1_Template, 4, 3, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.showSizeChanger ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.showQuickJumper ? 1 : -1);\n      }\n    },\n    dependencies: [NzSelectModule, i1$1.NzOptionComponent, i1$1.NzSelectComponent, FormsModule, i2.NgControlStatus, i2.NgModel],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationOptionsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'li[nz-pagination-options]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (showSizeChanger) {\n      <nz-select\n        class=\"ant-pagination-options-size-changer\"\n        [nzDisabled]=\"disabled\"\n        [nzSize]=\"nzSize\"\n        [ngModel]=\"pageSize\"\n        (ngModelChange)=\"onPageSizeChange($event)\"\n      >\n        @for (option of listOfPageSizeOption; track option.value) {\n          <nz-option [nzLabel]=\"option.label\" [nzValue]=\"option.value\" />\n        }\n      </nz-select>\n    }\n\n    @if (showQuickJumper) {\n      <div class=\"ant-pagination-options-quick-jumper\">\n        {{ locale.jump_to }}\n        <input [disabled]=\"disabled\" (keydown.enter)=\"jumpToPageViaInput($event)\" />\n        {{ locale.page }}\n      </div>\n    }\n  `,\n      host: {\n        class: 'ant-pagination-options'\n      },\n      imports: [NzSelectModule, FormsModule]\n    }]\n  }], null, {\n    nzSize: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    showSizeChanger: [{\n      type: Input\n    }],\n    showQuickJumper: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    total: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    pageIndexChange: [{\n      type: Output\n    }],\n    pageSizeChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzPaginationDefaultComponent {\n  cdr;\n  renderer;\n  elementRef;\n  directionality;\n  template;\n  nzSize = 'default';\n  itemRender = null;\n  showTotal = null;\n  disabled = false;\n  locale;\n  showSizeChanger = false;\n  showQuickJumper = false;\n  total = 0;\n  pageIndex = 1;\n  pageSize = 10;\n  pageSizeOptions = [10, 20, 30, 40];\n  pageIndexChange = new EventEmitter();\n  pageSizeChange = new EventEmitter();\n  ranges = [0, 0];\n  listOfPageItem = [];\n  dir = 'ltr';\n  destroy$ = new Subject();\n  constructor(cdr, renderer, elementRef, directionality) {\n    this.cdr = cdr;\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.directionality = directionality;\n    renderer.removeChild(renderer.parentNode(elementRef.nativeElement), elementRef.nativeElement);\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.updateRtlStyle();\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.updateRtlStyle();\n  }\n  updateRtlStyle() {\n    if (this.dir === 'rtl') {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  jumpPage(index) {\n    this.onPageIndexChange(index);\n  }\n  jumpDiff(diff) {\n    this.jumpPage(this.pageIndex + diff);\n  }\n  trackByPageItem(_, value) {\n    return `${value.type}-${value.index}`;\n  }\n  onPageIndexChange(index) {\n    this.pageIndexChange.next(index);\n  }\n  onPageSizeChange(size) {\n    this.pageSizeChange.next(size);\n  }\n  getLastIndex(total, pageSize) {\n    return Math.ceil(total / pageSize);\n  }\n  buildIndexes() {\n    const lastIndex = this.getLastIndex(this.total, this.pageSize);\n    this.listOfPageItem = this.getListOfPageItem(this.pageIndex, lastIndex);\n  }\n  getListOfPageItem(pageIndex, lastIndex) {\n    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n    const concatWithPrevNext = listOfPage => {\n      const prevItem = {\n        type: 'prev',\n        disabled: pageIndex === 1\n      };\n      const nextItem = {\n        type: 'next',\n        disabled: pageIndex === lastIndex\n      };\n      return [prevItem, ...listOfPage, nextItem];\n    };\n    const generatePage = (start, end) => {\n      const list = [];\n      for (let i = start; i <= end; i++) {\n        list.push({\n          index: i,\n          type: 'page'\n        });\n      }\n      return list;\n    };\n    if (lastIndex <= 9) {\n      return concatWithPrevNext(generatePage(1, lastIndex));\n    } else {\n      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n      const generateRangeItem = (selected, last) => {\n        let listOfRange = [];\n        const prevFiveItem = {\n          type: 'prev_5'\n        };\n        const nextFiveItem = {\n          type: 'next_5'\n        };\n        const firstPageItem = generatePage(1, 1);\n        const lastPageItem = generatePage(lastIndex, lastIndex);\n        if (selected < 5) {\n          // If the 4th is selected, one more page will be displayed.\n          const maxLeft = selected === 4 ? 6 : 5;\n          listOfRange = [...generatePage(2, maxLeft), nextFiveItem];\n        } else if (selected < last - 3) {\n          listOfRange = [prevFiveItem, ...generatePage(selected - 2, selected + 2), nextFiveItem];\n        } else {\n          // If the 4th from last is selected, one more page will be displayed.\n          const minRight = selected === last - 3 ? last - 5 : last - 4;\n          listOfRange = [prevFiveItem, ...generatePage(minRight, last - 1)];\n        }\n        return [...firstPageItem, ...listOfRange, ...lastPageItem];\n      };\n      return concatWithPrevNext(generateRangeItem(pageIndex, lastIndex));\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      pageIndex,\n      pageSize,\n      total\n    } = changes;\n    if (pageIndex || pageSize || total) {\n      this.ranges = [(this.pageIndex - 1) * this.pageSize + 1, Math.min(this.pageIndex * this.pageSize, this.total)];\n      this.buildIndexes();\n    }\n  }\n  static ɵfac = function NzPaginationDefaultComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzPaginationDefaultComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$2.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzPaginationDefaultComponent,\n    selectors: [[\"nz-pagination-default\"]],\n    viewQuery: function NzPaginationDefaultComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c3, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      }\n    },\n    inputs: {\n      nzSize: \"nzSize\",\n      itemRender: \"itemRender\",\n      showTotal: \"showTotal\",\n      disabled: \"disabled\",\n      locale: \"locale\",\n      showSizeChanger: \"showSizeChanger\",\n      showQuickJumper: \"showQuickJumper\",\n      total: \"total\",\n      pageIndex: \"pageIndex\",\n      pageSize: \"pageSize\",\n      pageSizeOptions: \"pageSizeOptions\"\n    },\n    outputs: {\n      pageIndexChange: \"pageIndexChange\",\n      pageSizeChange: \"pageSizeChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 2,\n    vars: 0,\n    consts: [[\"containerTemplate\", \"\"], [1, \"ant-pagination-total-text\"], [\"nz-pagination-item\", \"\", 3, \"locale\", \"type\", \"index\", \"disabled\", \"itemRender\", \"active\", \"direction\"], [\"nz-pagination-options\", \"\", 3, \"total\", \"locale\", \"disabled\", \"nzSize\", \"showSizeChanger\", \"showQuickJumper\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"nz-pagination-item\", \"\", 3, \"gotoIndex\", \"diffIndex\", \"locale\", \"type\", \"index\", \"disabled\", \"itemRender\", \"active\", \"direction\"], [\"nz-pagination-options\", \"\", 3, \"pageIndexChange\", \"pageSizeChange\", \"total\", \"locale\", \"disabled\", \"nzSize\", \"showSizeChanger\", \"showQuickJumper\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"]],\n    template: function NzPaginationDefaultComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzPaginationDefaultComponent_ng_template_0_Template, 5, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n    },\n    dependencies: [NgTemplateOutlet, NzPaginationItemComponent, NzPaginationOptionsComponent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationDefaultComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-pagination-default',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template #containerTemplate>\n      <ul>\n        @if (showTotal) {\n          <li class=\"ant-pagination-total-text\">\n            <ng-template\n              [ngTemplateOutlet]=\"showTotal\"\n              [ngTemplateOutletContext]=\"{ $implicit: total, range: ranges }\"\n            />\n          </li>\n        }\n\n        @for (page of listOfPageItem; track trackByPageItem($index, page)) {\n          <li\n            nz-pagination-item\n            [locale]=\"locale\"\n            [type]=\"page.type\"\n            [index]=\"page.index\"\n            [disabled]=\"!!page.disabled\"\n            [itemRender]=\"itemRender\"\n            [active]=\"pageIndex === page.index\"\n            (gotoIndex)=\"jumpPage($event)\"\n            (diffIndex)=\"jumpDiff($event)\"\n            [direction]=\"dir\"\n          ></li>\n        }\n\n        @if (showQuickJumper || showSizeChanger) {\n          <li\n            nz-pagination-options\n            [total]=\"total\"\n            [locale]=\"locale\"\n            [disabled]=\"disabled\"\n            [nzSize]=\"nzSize\"\n            [showSizeChanger]=\"showSizeChanger\"\n            [showQuickJumper]=\"showQuickJumper\"\n            [pageIndex]=\"pageIndex\"\n            [pageSize]=\"pageSize\"\n            [pageSizeOptions]=\"pageSizeOptions\"\n            (pageIndexChange)=\"onPageIndexChange($event)\"\n            (pageSizeChange)=\"onPageSizeChange($event)\"\n          ></li>\n        }\n      </ul>\n    </ng-template>\n  `,\n      imports: [NgTemplateOutlet, NzPaginationItemComponent, NzPaginationOptionsComponent]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1$2.Directionality\n  }], {\n    template: [{\n      type: ViewChild,\n      args: ['containerTemplate', {\n        static: true\n      }]\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    itemRender: [{\n      type: Input\n    }],\n    showTotal: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    showSizeChanger: [{\n      type: Input\n    }],\n    showQuickJumper: [{\n      type: Input\n    }],\n    total: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    pageIndexChange: [{\n      type: Output\n    }],\n    pageSizeChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzPaginationSimpleComponent {\n  cdr;\n  renderer;\n  elementRef;\n  directionality;\n  template;\n  itemRender = null;\n  disabled = false;\n  locale;\n  total = 0;\n  pageIndex = 1;\n  pageSize = 10;\n  pageIndexChange = new EventEmitter();\n  lastIndex = 0;\n  isFirstIndex = false;\n  isLastIndex = false;\n  dir = 'ltr';\n  destroy$ = new Subject();\n  constructor(cdr, renderer, elementRef, directionality) {\n    this.cdr = cdr;\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.directionality = directionality;\n    renderer.removeChild(renderer.parentNode(elementRef.nativeElement), elementRef.nativeElement);\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.updateRtlStyle();\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.updateRtlStyle();\n  }\n  updateRtlStyle() {\n    if (this.dir === 'rtl') {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  jumpToPageViaInput($event) {\n    const target = $event.target;\n    const index = toNumber(target.value, this.pageIndex);\n    this.onPageIndexChange(index);\n    target.value = `${this.pageIndex}`;\n  }\n  prePage() {\n    this.onPageIndexChange(this.pageIndex - 1);\n  }\n  nextPage() {\n    this.onPageIndexChange(this.pageIndex + 1);\n  }\n  onPageIndexChange(index) {\n    this.pageIndexChange.next(index);\n  }\n  updateBindingValue() {\n    this.lastIndex = Math.ceil(this.total / this.pageSize);\n    this.isFirstIndex = this.pageIndex === 1;\n    this.isLastIndex = this.pageIndex === this.lastIndex;\n  }\n  ngOnChanges(changes) {\n    const {\n      pageIndex,\n      total,\n      pageSize\n    } = changes;\n    if (pageIndex || total || pageSize) {\n      this.updateBindingValue();\n    }\n  }\n  static ɵfac = function NzPaginationSimpleComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzPaginationSimpleComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$2.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzPaginationSimpleComponent,\n    selectors: [[\"nz-pagination-simple\"]],\n    viewQuery: function NzPaginationSimpleComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c3, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      }\n    },\n    inputs: {\n      itemRender: \"itemRender\",\n      disabled: \"disabled\",\n      locale: \"locale\",\n      total: \"total\",\n      pageIndex: \"pageIndex\",\n      pageSize: \"pageSize\"\n    },\n    outputs: {\n      pageIndexChange: \"pageIndexChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 2,\n    vars: 0,\n    consts: [[\"containerTemplate\", \"\"], [\"nz-pagination-item\", \"\", \"type\", \"prev\", 3, \"click\", \"locale\", \"disabled\", \"direction\", \"itemRender\"], [1, \"ant-pagination-simple-pager\"], [\"size\", \"3\", 3, \"keydown.enter\", \"disabled\", \"value\"], [1, \"ant-pagination-slash\"], [\"nz-pagination-item\", \"\", \"type\", \"next\", 3, \"click\", \"locale\", \"disabled\", \"direction\", \"itemRender\"]],\n    template: function NzPaginationSimpleComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzPaginationSimpleComponent_ng_template_0_Template, 8, 14, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n    },\n    dependencies: [NzPaginationItemComponent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationSimpleComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-pagination-simple',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template #containerTemplate>\n      <ul>\n        <li\n          nz-pagination-item\n          [locale]=\"locale\"\n          [attr.title]=\"locale.prev_page\"\n          [disabled]=\"isFirstIndex\"\n          [direction]=\"dir\"\n          (click)=\"prePage()\"\n          type=\"prev\"\n          [itemRender]=\"itemRender\"\n        ></li>\n        <li [attr.title]=\"pageIndex + '/' + lastIndex\" class=\"ant-pagination-simple-pager\">\n          <input [disabled]=\"disabled\" [value]=\"pageIndex\" (keydown.enter)=\"jumpToPageViaInput($event)\" size=\"3\" />\n          <span class=\"ant-pagination-slash\">/</span>\n          {{ lastIndex }}\n        </li>\n        <li\n          nz-pagination-item\n          [locale]=\"locale\"\n          [attr.title]=\"locale?.next_page\"\n          [disabled]=\"isLastIndex\"\n          [direction]=\"dir\"\n          (click)=\"nextPage()\"\n          type=\"next\"\n          [itemRender]=\"itemRender\"\n        ></li>\n      </ul>\n    </ng-template>\n  `,\n      imports: [NzPaginationItemComponent]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1$2.Directionality\n  }], {\n    template: [{\n      type: ViewChild,\n      args: ['containerTemplate', {\n        static: true\n      }]\n    }],\n    itemRender: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    total: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageIndexChange: [{\n      type: Output\n    }]\n  });\n})();\nconst NZ_CONFIG_MODULE_NAME = 'pagination';\nlet NzPaginationComponent = (() => {\n  let _nzSize_decorators;\n  let _nzSize_initializers = [];\n  let _nzSize_extraInitializers = [];\n  let _nzPageSizeOptions_decorators;\n  let _nzPageSizeOptions_initializers = [];\n  let _nzPageSizeOptions_extraInitializers = [];\n  let _nzShowSizeChanger_decorators;\n  let _nzShowSizeChanger_initializers = [];\n  let _nzShowSizeChanger_extraInitializers = [];\n  let _nzShowQuickJumper_decorators;\n  let _nzShowQuickJumper_initializers = [];\n  let _nzShowQuickJumper_extraInitializers = [];\n  let _nzSimple_decorators;\n  let _nzSimple_initializers = [];\n  let _nzSimple_extraInitializers = [];\n  return class NzPaginationComponent {\n    static {\n      const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(null) : void 0;\n      _nzSize_decorators = [WithConfig()];\n      _nzPageSizeOptions_decorators = [WithConfig()];\n      _nzShowSizeChanger_decorators = [WithConfig()];\n      _nzShowQuickJumper_decorators = [WithConfig()];\n      _nzSimple_decorators = [WithConfig()];\n      __esDecorate(null, null, _nzSize_decorators, {\n        kind: \"field\",\n        name: \"nzSize\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzSize\" in obj,\n          get: obj => obj.nzSize,\n          set: (obj, value) => {\n            obj.nzSize = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzSize_initializers, _nzSize_extraInitializers);\n      __esDecorate(null, null, _nzPageSizeOptions_decorators, {\n        kind: \"field\",\n        name: \"nzPageSizeOptions\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzPageSizeOptions\" in obj,\n          get: obj => obj.nzPageSizeOptions,\n          set: (obj, value) => {\n            obj.nzPageSizeOptions = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzPageSizeOptions_initializers, _nzPageSizeOptions_extraInitializers);\n      __esDecorate(null, null, _nzShowSizeChanger_decorators, {\n        kind: \"field\",\n        name: \"nzShowSizeChanger\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzShowSizeChanger\" in obj,\n          get: obj => obj.nzShowSizeChanger,\n          set: (obj, value) => {\n            obj.nzShowSizeChanger = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzShowSizeChanger_initializers, _nzShowSizeChanger_extraInitializers);\n      __esDecorate(null, null, _nzShowQuickJumper_decorators, {\n        kind: \"field\",\n        name: \"nzShowQuickJumper\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzShowQuickJumper\" in obj,\n          get: obj => obj.nzShowQuickJumper,\n          set: (obj, value) => {\n            obj.nzShowQuickJumper = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzShowQuickJumper_initializers, _nzShowQuickJumper_extraInitializers);\n      __esDecorate(null, null, _nzSimple_decorators, {\n        kind: \"field\",\n        name: \"nzSimple\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzSimple\" in obj,\n          get: obj => obj.nzSimple,\n          set: (obj, value) => {\n            obj.nzSimple = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzSimple_initializers, _nzSimple_extraInitializers);\n      if (_metadata) Object.defineProperty(this, Symbol.metadata, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value: _metadata\n      });\n    }\n    i18n;\n    cdr;\n    breakpointService;\n    nzConfigService;\n    directionality;\n    _nzModuleName = NZ_CONFIG_MODULE_NAME;\n    nzPageSizeChange = new EventEmitter();\n    nzPageIndexChange = new EventEmitter();\n    nzShowTotal = null;\n    nzItemRender = null;\n    nzSize = __runInitializers(this, _nzSize_initializers, 'default');\n    nzPageSizeOptions = (__runInitializers(this, _nzSize_extraInitializers), __runInitializers(this, _nzPageSizeOptions_initializers, [10, 20, 30, 40]));\n    nzShowSizeChanger = (__runInitializers(this, _nzPageSizeOptions_extraInitializers), __runInitializers(this, _nzShowSizeChanger_initializers, false));\n    nzShowQuickJumper = (__runInitializers(this, _nzShowSizeChanger_extraInitializers), __runInitializers(this, _nzShowQuickJumper_initializers, false));\n    nzSimple = (__runInitializers(this, _nzShowQuickJumper_extraInitializers), __runInitializers(this, _nzSimple_initializers, false));\n    nzDisabled = (__runInitializers(this, _nzSimple_extraInitializers), false);\n    nzResponsive = false;\n    nzHideOnSinglePage = false;\n    nzTotal = 0;\n    nzPageIndex = 1;\n    nzPageSize = 10;\n    showPagination = true;\n    locale;\n    size = 'default';\n    dir = 'ltr';\n    destroy$ = new Subject();\n    total$ = new ReplaySubject(1);\n    validatePageIndex(value, lastIndex) {\n      if (value > lastIndex) {\n        return lastIndex;\n      } else if (value < 1) {\n        return 1;\n      } else {\n        return value;\n      }\n    }\n    onPageIndexChange(index) {\n      const lastIndex = this.getLastIndex(this.nzTotal, this.nzPageSize);\n      const validIndex = this.validatePageIndex(index, lastIndex);\n      if (validIndex !== this.nzPageIndex && !this.nzDisabled) {\n        this.nzPageIndex = validIndex;\n        this.nzPageIndexChange.emit(this.nzPageIndex);\n      }\n    }\n    onPageSizeChange(size) {\n      this.nzPageSize = size;\n      this.nzPageSizeChange.emit(size);\n      const lastIndex = this.getLastIndex(this.nzTotal, this.nzPageSize);\n      if (this.nzPageIndex > lastIndex) {\n        this.onPageIndexChange(lastIndex);\n      }\n    }\n    onTotalChange(total) {\n      const lastIndex = this.getLastIndex(total, this.nzPageSize);\n      if (this.nzPageIndex > lastIndex) {\n        Promise.resolve().then(() => {\n          this.onPageIndexChange(lastIndex);\n          this.cdr.markForCheck();\n        });\n      }\n    }\n    getLastIndex(total, pageSize) {\n      return Math.ceil(total / pageSize);\n    }\n    constructor(i18n, cdr, breakpointService, nzConfigService, directionality) {\n      this.i18n = i18n;\n      this.cdr = cdr;\n      this.breakpointService = breakpointService;\n      this.nzConfigService = nzConfigService;\n      this.directionality = directionality;\n    }\n    ngOnInit() {\n      this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.locale = this.i18n.getLocaleData('Pagination');\n        this.cdr.markForCheck();\n      });\n      this.total$.pipe(takeUntil(this.destroy$)).subscribe(total => {\n        this.onTotalChange(total);\n      });\n      this.breakpointService.subscribe(gridResponsiveMap).pipe(takeUntil(this.destroy$)).subscribe(bp => {\n        if (this.nzResponsive) {\n          this.size = bp === NzBreakpointEnum.xs ? 'small' : 'default';\n          this.cdr.markForCheck();\n        }\n      });\n      this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n        this.dir = direction;\n        this.cdr.detectChanges();\n      });\n      this.dir = this.directionality.value;\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    ngOnChanges(changes) {\n      const {\n        nzHideOnSinglePage,\n        nzTotal,\n        nzPageSize,\n        nzSize\n      } = changes;\n      if (nzTotal) {\n        this.total$.next(this.nzTotal);\n      }\n      if (nzHideOnSinglePage || nzTotal || nzPageSize) {\n        this.showPagination = this.nzHideOnSinglePage && this.nzTotal > this.nzPageSize || this.nzTotal > 0 && !this.nzHideOnSinglePage;\n      }\n      if (nzSize) {\n        this.size = nzSize.currentValue;\n      }\n    }\n    static ɵfac = function NzPaginationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NzPaginationComponent)(i0.ɵɵdirectiveInject(i1$3.NzI18nService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2$1.NzBreakpointService), i0.ɵɵdirectiveInject(i3.NzConfigService), i0.ɵɵdirectiveInject(i1$2.Directionality));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationComponent,\n      selectors: [[\"nz-pagination\"]],\n      hostAttrs: [1, \"ant-pagination\"],\n      hostVars: 8,\n      hostBindings: function NzPaginationComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-pagination-simple\", ctx.nzSimple)(\"ant-pagination-disabled\", ctx.nzDisabled)(\"ant-pagination-mini\", !ctx.nzSimple && ctx.size === \"small\")(\"ant-pagination-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzShowTotal: \"nzShowTotal\",\n        nzItemRender: \"nzItemRender\",\n        nzSize: \"nzSize\",\n        nzPageSizeOptions: \"nzPageSizeOptions\",\n        nzShowSizeChanger: [2, \"nzShowSizeChanger\", \"nzShowSizeChanger\", booleanAttribute],\n        nzShowQuickJumper: [2, \"nzShowQuickJumper\", \"nzShowQuickJumper\", booleanAttribute],\n        nzSimple: [2, \"nzSimple\", \"nzSimple\", booleanAttribute],\n        nzDisabled: [2, \"nzDisabled\", \"nzDisabled\", booleanAttribute],\n        nzResponsive: [2, \"nzResponsive\", \"nzResponsive\", booleanAttribute],\n        nzHideOnSinglePage: [2, \"nzHideOnSinglePage\", \"nzHideOnSinglePage\", booleanAttribute],\n        nzTotal: [2, \"nzTotal\", \"nzTotal\", numberAttribute],\n        nzPageIndex: [2, \"nzPageIndex\", \"nzPageIndex\", numberAttribute],\n        nzPageSize: [2, \"nzPageSize\", \"nzPageSize\", numberAttribute]\n      },\n      outputs: {\n        nzPageSizeChange: \"nzPageSizeChange\",\n        nzPageIndexChange: \"nzPageIndexChange\"\n      },\n      exportAs: [\"nzPagination\"],\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 5,\n      vars: 18,\n      consts: [[\"simplePagination\", \"\"], [\"defaultPagination\", \"\"], [3, \"pageIndexChange\", \"disabled\", \"itemRender\", \"locale\", \"pageSize\", \"total\", \"pageIndex\"], [3, \"pageIndexChange\", \"pageSizeChange\", \"nzSize\", \"itemRender\", \"showTotal\", \"disabled\", \"locale\", \"showSizeChanger\", \"showQuickJumper\", \"total\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzPaginationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Template, 2, 1);\n          i0.ɵɵelementStart(1, \"nz-pagination-simple\", 2, 0);\n          i0.ɵɵlistener(\"pageIndexChange\", function NzPaginationComponent_Template_nz_pagination_simple_pageIndexChange_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPageIndexChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nz-pagination-default\", 3, 1);\n          i0.ɵɵlistener(\"pageIndexChange\", function NzPaginationComponent_Template_nz_pagination_default_pageIndexChange_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPageIndexChange($event));\n          })(\"pageSizeChange\", function NzPaginationComponent_Template_nz_pagination_default_pageSizeChange_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPageSizeChange($event));\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.showPagination ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.nzDisabled)(\"itemRender\", ctx.nzItemRender)(\"locale\", ctx.locale)(\"pageSize\", ctx.nzPageSize)(\"total\", ctx.nzTotal)(\"pageIndex\", ctx.nzPageIndex);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzSize\", ctx.size)(\"itemRender\", ctx.nzItemRender)(\"showTotal\", ctx.nzShowTotal)(\"disabled\", ctx.nzDisabled)(\"locale\", ctx.locale)(\"showSizeChanger\", ctx.nzShowSizeChanger)(\"showQuickJumper\", ctx.nzShowQuickJumper)(\"total\", ctx.nzTotal)(\"pageIndex\", ctx.nzPageIndex)(\"pageSize\", ctx.nzPageSize)(\"pageSizeOptions\", ctx.nzPageSizeOptions);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NzPaginationSimpleComponent, NzPaginationDefaultComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  };\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-pagination',\n      exportAs: 'nzPagination',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (showPagination) {\n      @if (nzSimple) {\n        <ng-template [ngTemplateOutlet]=\"simplePagination.template\" />\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"defaultPagination.template\" />\n      }\n    }\n\n    <nz-pagination-simple\n      #simplePagination\n      [disabled]=\"nzDisabled\"\n      [itemRender]=\"nzItemRender\"\n      [locale]=\"locale\"\n      [pageSize]=\"nzPageSize\"\n      [total]=\"nzTotal\"\n      [pageIndex]=\"nzPageIndex\"\n      (pageIndexChange)=\"onPageIndexChange($event)\"\n    ></nz-pagination-simple>\n    <nz-pagination-default\n      #defaultPagination\n      [nzSize]=\"size\"\n      [itemRender]=\"nzItemRender\"\n      [showTotal]=\"nzShowTotal\"\n      [disabled]=\"nzDisabled\"\n      [locale]=\"locale\"\n      [showSizeChanger]=\"nzShowSizeChanger\"\n      [showQuickJumper]=\"nzShowQuickJumper\"\n      [total]=\"nzTotal\"\n      [pageIndex]=\"nzPageIndex\"\n      [pageSize]=\"nzPageSize\"\n      [pageSizeOptions]=\"nzPageSizeOptions\"\n      (pageIndexChange)=\"onPageIndexChange($event)\"\n      (pageSizeChange)=\"onPageSizeChange($event)\"\n    ></nz-pagination-default>\n  `,\n      host: {\n        class: 'ant-pagination',\n        '[class.ant-pagination-simple]': 'nzSimple',\n        '[class.ant-pagination-disabled]': 'nzDisabled',\n        '[class.ant-pagination-mini]': `!nzSimple && size === 'small'`,\n        '[class.ant-pagination-rtl]': `dir === 'rtl'`\n      },\n      imports: [NgTemplateOutlet, NzPaginationSimpleComponent, NzPaginationDefaultComponent]\n    }]\n  }], () => [{\n    type: i1$3.NzI18nService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2$1.NzBreakpointService\n  }, {\n    type: i3.NzConfigService\n  }, {\n    type: i1$2.Directionality\n  }], {\n    nzPageSizeChange: [{\n      type: Output\n    }],\n    nzPageIndexChange: [{\n      type: Output\n    }],\n    nzShowTotal: [{\n      type: Input\n    }],\n    nzItemRender: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzPageSizeOptions: [{\n      type: Input\n    }],\n    nzShowSizeChanger: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzShowQuickJumper: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzSimple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzResponsive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzHideOnSinglePage: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzTotal: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    nzPageIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    nzPageSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzPaginationModule {\n  static ɵfac = function NzPaginationModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzPaginationModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzPaginationModule,\n    imports: [NzPaginationComponent, NzPaginationSimpleComponent, NzPaginationOptionsComponent, NzPaginationItemComponent, NzPaginationDefaultComponent],\n    exports: [NzPaginationComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzPaginationComponent, NzPaginationSimpleComponent, NzPaginationOptionsComponent, NzPaginationItemComponent, NzPaginationDefaultComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzPaginationComponent, NzPaginationSimpleComponent, NzPaginationOptionsComponent, NzPaginationItemComponent, NzPaginationDefaultComponent],\n      exports: [NzPaginationComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzPaginationComponent, NzPaginationDefaultComponent, NzPaginationItemComponent, NzPaginationModule, NzPaginationOptionsComponent, NzPaginationSimpleComponent };\n", "import * as i0 from '@angular/core';\nimport { EventEmitter, ElementRef, booleanAttribute, ViewChild, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, Directive, Injectable, inject, NgZone, ViewChildren, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { __esDecorate, __runInitializers } from 'tslib';\nimport { takeUntil, map, distinctUntilChanged, debounceTime, skip, filter, switchMap, startWith, delay, mergeMap } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2 from 'ng-zorro-antd/core/services';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { fromEventOutsideAngular, arraysEqual, isNil, measureScrollbar } from 'ng-zorro-antd/core/util';\nimport * as i4 from 'ng-zorro-antd/dropdown';\nimport { NzDropDownModule, NzDropDownDirective } from 'ng-zorro-antd/dropdown';\nimport { NgTemplateOutlet, AsyncPipe } from '@angular/common';\nimport * as i6 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, ReplaySubject, BehaviorSubject, combineLatest, merge, EMPTY, of } from 'rxjs';\nimport * as i7 from 'ng-zorro-antd/button';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport * as i5 from 'ng-zorro-antd/checkbox';\nimport { NzCheckboxModule } from 'ng-zorro-antd/checkbox';\nimport * as i2$1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzRadioComponent } from 'ng-zorro-antd/radio';\nimport * as i1$1 from 'ng-zorro-antd/i18n';\nimport * as i3 from 'ng-zorro-antd/menu';\nimport * as i8 from 'ng-zorro-antd/core/transition-patch';\nimport * as i9 from 'ng-zorro-antd/core/wave';\nimport * as i3$1 from '@angular/cdk/scrolling';\nimport { ScrollingModule, CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport * as i1$3 from 'ng-zorro-antd/empty';\nimport { NzEmptyModule } from 'ng-zorro-antd/empty';\nimport * as i1$2 from 'ng-zorro-antd/cdk/resize-observer';\nimport * as i1$4 from '@angular/cdk/platform';\nimport * as i6$1 from 'ng-zorro-antd/pagination';\nimport { NzPaginationModule } from 'ng-zorro-antd/pagination';\nimport { NzSpinComponent } from 'ng-zorro-antd/spin';\nimport * as i1$5 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i5$1 from '@angular/cdk/bidi';\nconst _c0 = [\"*\"];\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction NzTableFilterComponent_ng_template_1_Template(rf, ctx) {}\nfunction NzTableFilterComponent_Conditional_2_For_7_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 14);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTableFilterComponent_Conditional_2_For_7_Conditional_1_Template_label_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const f_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.check(f_r4));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const f_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", f_r4.checked);\n  }\n}\nfunction NzTableFilterComponent_Conditional_2_For_7_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 15);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTableFilterComponent_Conditional_2_For_7_Conditional_2_Template_label_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const f_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.check(f_r4));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const f_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", f_r4.checked);\n  }\n}\nfunction NzTableFilterComponent_Conditional_2_For_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 11);\n    i0.ɵɵlistener(\"click\", function NzTableFilterComponent_Conditional_2_For_7_Template_li_click_0_listener() {\n      const f_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.check(f_r4));\n    });\n    i0.ɵɵtemplate(1, NzTableFilterComponent_Conditional_2_For_7_Conditional_1_Template, 1, 1, \"label\", 12)(2, NzTableFilterComponent_Conditional_2_For_7_Conditional_2_Template, 1, 1, \"label\", 13);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const f_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzSelected\", f_r4.checked);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r1.filterMultiple ? 1 : 2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(f_r4.text);\n  }\n}\nfunction NzTableFilterComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-filter-trigger\", 3);\n    i0.ɵɵlistener(\"nzVisibleChange\", function NzTableFilterComponent_Conditional_2_Template_nz_filter_trigger_nzVisibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onVisibleChange($event));\n    });\n    i0.ɵɵelement(1, \"nz-icon\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"nz-dropdown-menu\", null, 0)(4, \"div\", 5)(5, \"ul\", 6);\n    i0.ɵɵrepeaterCreate(6, NzTableFilterComponent_Conditional_2_For_7_Template, 5, 3, \"li\", 7, _forTrack0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 8)(9, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function NzTableFilterComponent_Conditional_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.reset());\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function NzTableFilterComponent_Conditional_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.confirm());\n    });\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const filterMenu_r7 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzVisible\", ctx_r1.isVisible)(\"nzActive\", ctx_r1.isChecked)(\"nzDropdownMenu\", filterMenu_r7);\n    i0.ɵɵadvance(6);\n    i0.ɵɵrepeater(ctx_r1.listOfParsedFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isChecked);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.locale.filterReset, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.locale.filterConfirm);\n  }\n}\nfunction NzTableFilterComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 2);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.extraTemplate);\n  }\n}\nfunction NzTableSelectionComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 3);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTableSelectionComponent_Conditional_0_Template_label_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckedChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ant-table-selection-select-all-custom\", ctx_r1.showRowSelection);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.checked)(\"nzDisabled\", ctx_r1.disabled)(\"nzIndeterminate\", ctx_r1.indeterminate);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.label);\n  }\n}\nfunction NzTableSelectionComponent_Conditional_1_For_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 8);\n    i0.ɵɵlistener(\"click\", function NzTableSelectionComponent_Conditional_1_For_7_Template_li_click_0_listener() {\n      const selection_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      return i0.ɵɵresetView(selection_r4.onSelect());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const selection_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", selection_r4.text, \" \");\n  }\n}\nfunction NzTableSelectionComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"span\", 4);\n    i0.ɵɵelement(2, \"nz-icon\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nz-dropdown-menu\", null, 0)(5, \"ul\", 6);\n    i0.ɵɵrepeaterCreate(6, NzTableSelectionComponent_Conditional_1_For_7_Template, 2, 1, \"li\", 7, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const selectionMenu_r5 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzDropdownMenu\", selectionMenu_r5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵrepeater(ctx_r1.listOfSelections);\n  }\n}\nfunction NzTableSortersComponent_ng_template_1_Template(rf, ctx) {}\nfunction NzTableSortersComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r0.sortOrder === \"ascend\");\n  }\n}\nfunction NzTableSortersComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r0.sortOrder === \"descend\");\n  }\n}\nconst _c1 = [\"nzChecked\", \"\"];\nfunction NzTdAddOnComponent_Conditional_0_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzTdAddOnComponent_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzTdAddOnComponent_Conditional_0_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzExpandIcon);\n  }\n}\nfunction NzTdAddOnComponent_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 4);\n    i0.ɵɵlistener(\"expandChange\", function NzTdAddOnComponent_Conditional_0_Conditional_2_Template_button_expandChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onExpandChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"expand\", ctx_r0.nzExpand)(\"spaceMode\", !ctx_r0.nzShowExpand);\n  }\n}\nfunction NzTdAddOnComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-row-indent\", 1);\n    i0.ɵɵtemplate(1, NzTdAddOnComponent_Conditional_0_Conditional_1_Template, 1, 1, null, 2)(2, NzTdAddOnComponent_Conditional_0_Conditional_2_Template, 1, 2, \"button\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"indentSize\", ctx_r0.nzIndentSize);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.nzExpandIcon ? 1 : 2);\n  }\n}\nfunction NzTdAddOnComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 5);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTdAddOnComponent_Conditional_1_Template_label_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCheckedChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzDisabled\", ctx_r0.nzDisabled)(\"ngModel\", ctx_r0.nzChecked)(\"nzIndeterminate\", ctx_r0.nzIndeterminate);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.nzLabel);\n  }\n}\nconst _c2 = [\"nzColumnKey\", \"\"];\nconst _c3 = [[[\"\", \"nz-th-extra\", \"\"]], [[\"nz-filter-trigger\"]], \"*\"];\nconst _c4 = [\"[nz-th-extra]\", \"nz-filter-trigger\", \"*\"];\nfunction NzThAddOnComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-table-filter\", 6);\n    i0.ɵɵlistener(\"filterChange\", function NzThAddOnComponent_Conditional_0_Template_nz_table_filter_filterChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilterValueChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const notFilterTemplate_r3 = i0.ɵɵreference(3);\n    const extraTemplate_r4 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"contentTemplate\", notFilterTemplate_r3)(\"extraTemplate\", extraTemplate_r4)(\"customFilter\", ctx_r1.nzCustomFilter)(\"filterMultiple\", ctx_r1.nzFilterMultiple)(\"listOfFilter\", ctx_r1.nzFilters);\n  }\n}\nfunction NzThAddOnComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 5);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const notFilterTemplate_r3 = i0.ɵɵreference(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", notFilterTemplate_r3);\n  }\n}\nfunction NzThAddOnComponent_ng_template_2_ng_template_0_Template(rf, ctx) {}\nfunction NzThAddOnComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzThAddOnComponent_ng_template_2_ng_template_0_Template, 0, 0, \"ng-template\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const sortTemplate_r5 = i0.ɵɵreference(7);\n    const contentTemplate_r6 = i0.ɵɵreference(9);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nzShowSort ? sortTemplate_r5 : contentTemplate_r6);\n  }\n}\nfunction NzThAddOnComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵprojection(1, 1);\n  }\n}\nfunction NzThAddOnComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-sorters\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const contentTemplate_r6 = i0.ɵɵreference(9);\n    i0.ɵɵproperty(\"sortOrder\", ctx_r1.sortOrder)(\"sortDirections\", ctx_r1.sortDirections)(\"contentTemplate\", contentTemplate_r6);\n  }\n}\nfunction NzThAddOnComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nconst _c5 = [\"nzSelections\", \"\"];\nconst _c6 = [\"nz-table-content\", \"\"];\nfunction NzTableContentComponent_Conditional_0_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"col\");\n  }\n  if (rf & 2) {\n    const width_r1 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"width\", width_r1)(\"min-width\", width_r1);\n  }\n}\nfunction NzTableContentComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"colgroup\");\n    i0.ɵɵrepeaterCreate(1, NzTableContentComponent_Conditional_0_For_2_Template, 1, 4, \"col\", 3, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r1.listOfColWidth);\n  }\n}\nfunction NzTableContentComponent_Conditional_1_ng_template_1_Template(rf, ctx) {}\nfunction NzTableContentComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 0);\n    i0.ɵɵtemplate(1, NzTableContentComponent_Conditional_1_ng_template_1_Template, 0, 0, \"ng-template\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.theadTemplate);\n  }\n}\nfunction NzTableContentComponent_ng_template_2_Template(rf, ctx) {}\nfunction NzTableContentComponent_Conditional_4_ng_template_1_Template(rf, ctx) {}\nfunction NzTableContentComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tfoot\", 2);\n    i0.ɵɵtemplate(1, NzTableContentComponent_Conditional_4_ng_template_1_Template, 0, 0, \"ng-template\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.tfootTemplate);\n  }\n}\nconst _c7 = [\"tdElement\"];\nconst _c8 = [\"nz-table-fixed-row\", \"\"];\nfunction NzTableFixedRowComponent_Conditional_2_ng_template_2_Template(rf, ctx) {}\nfunction NzTableFixedRowComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵtemplate(2, NzTableFixedRowComponent_Conditional_2_ng_template_2_Template, 0, 0, \"ng-template\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const contentTemplate_r2 = i0.ɵɵreference(6);\n    i0.ɵɵstyleProp(\"width\", i0.ɵɵpipeBind1(1, 3, ctx_r0.hostWidth$), \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r2);\n  }\n}\nfunction NzTableFixedRowComponent_Conditional_4_ng_template_0_Template(rf, ctx) {}\nfunction NzTableFixedRowComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzTableFixedRowComponent_Conditional_4_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const contentTemplate_r2 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r2);\n  }\n}\nfunction NzTableFixedRowComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c9 = [\"nz-table-measure-row\", \"\"];\nfunction NzTrMeasureComponent_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 1, 0);\n  }\n}\nfunction NzTbodyComponent_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 2);\n    i0.ɵɵlistener(\"listOfAutoWidth\", function NzTbodyComponent_Conditional_0_Conditional_0_Template_tr_listOfAutoWidth_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onListOfAutoWidthChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const listOfMeasureColumn_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"listOfMeasureColumn\", listOfMeasureColumn_r3);\n  }\n}\nfunction NzTbodyComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzTbodyComponent_Conditional_0_Conditional_0_Template, 1, 1, \"tr\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1.isInsideTable && ctx.length ? 0 : -1);\n  }\n}\nfunction NzTbodyComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 0);\n    i0.ɵɵelement(1, \"nz-embed-empty\", 3);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"specificContent\", i0.ɵɵpipeBind1(2, 1, ctx_r1.noResult$));\n  }\n}\nconst _c10 = [\"tableHeaderElement\"];\nconst _c11 = [\"tableBodyElement\"];\nconst _c12 = [\"tableFootElement\"];\nconst _c13 = (a0, a1) => ({\n  $implicit: a0,\n  index: a1\n});\nfunction NzTableInnerScrollComponent_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9, 1);\n    i0.ɵɵelement(2, \"table\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(ctx_r0.bodyStyleMap);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth)(\"contentTemplate\", ctx_r0.contentTemplate);\n  }\n}\nfunction NzTableInnerScrollComponent_Conditional_0_Conditional_4_ng_container_4_ng_template_1_Template(rf, ctx) {}\nfunction NzTableInnerScrollComponent_Conditional_0_Conditional_4_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTableInnerScrollComponent_Conditional_0_Conditional_4_ng_container_4_ng_template_1_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.virtualTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c13, item_r2, i_r3));\n  }\n}\nfunction NzTableInnerScrollComponent_Conditional_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"cdk-virtual-scroll-viewport\", 11, 1)(2, \"table\", 12)(3, \"tbody\");\n    i0.ɵɵtemplate(4, NzTableInnerScrollComponent_Conditional_0_Conditional_4_ng_container_4_Template, 2, 5, \"ng-container\", 13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r0.data.length ? ctx_r0.scrollY : ctx_r0.noDataVirtualHeight);\n    i0.ɵɵproperty(\"itemSize\", ctx_r0.virtualItemSize)(\"maxBufferPx\", ctx_r0.virtualMaxBufferPx)(\"minBufferPx\", ctx_r0.virtualMinBufferPx);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cdkVirtualForOf\", ctx_r0.data)(\"cdkVirtualForTrackBy\", ctx_r0.virtualForTrackBy);\n  }\n}\nfunction NzTableInnerScrollComponent_Conditional_0_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15, 2);\n    i0.ɵɵelement(2, \"table\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(ctx_r0.headerStyleMap);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth)(\"tfootTemplate\", ctx_r0.tfootTemplate);\n  }\n}\nfunction NzTableInnerScrollComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4, 0);\n    i0.ɵɵelement(2, \"table\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NzTableInnerScrollComponent_Conditional_0_Conditional_3_Template, 3, 5, \"div\", 6)(4, NzTableInnerScrollComponent_Conditional_0_Conditional_4_Template, 5, 9, \"cdk-virtual-scroll-viewport\", 7)(5, NzTableInnerScrollComponent_Conditional_0_Conditional_5_Template, 3, 5, \"div\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r0.headerStyleMap);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth)(\"theadTemplate\", ctx_r0.theadTemplate)(\"tfootTemplate\", ctx_r0.tfootFixed === \"top\" ? ctx_r0.tfootTemplate : null);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r0.virtualTemplate ? 3 : 4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.tfootFixed === \"bottom\" ? 5 : -1);\n  }\n}\nfunction NzTableInnerScrollComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17, 1);\n    i0.ɵɵelement(2, \"table\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r0.bodyStyleMap);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth)(\"theadTemplate\", ctx_r0.theadTemplate)(\"contentTemplate\", ctx_r0.contentTemplate)(\"tfootTemplate\", ctx_r0.tfootTemplate);\n  }\n}\nfunction NzTableTitleFooterComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.title);\n  }\n}\nfunction NzTableTitleFooterComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.footer);\n  }\n}\nfunction NzTableComponent_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzTableComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzTableComponent_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const paginationTemplate_r1 = i0.ɵɵreference(10);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", paginationTemplate_r1);\n  }\n}\nfunction NzTableComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-title-footer\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"title\", ctx_r1.nzTitle);\n  }\n}\nfunction NzTableComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-inner-scroll\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const tableMainElement_r3 = i0.ɵɵreference(3);\n    const contentTemplate_r4 = i0.ɵɵreference(12);\n    i0.ɵɵproperty(\"data\", ctx_r1.data)(\"scrollX\", ctx_r1.scrollX)(\"scrollY\", ctx_r1.scrollY)(\"contentTemplate\", contentTemplate_r4)(\"listOfColWidth\", ctx_r1.listOfAutoColWidth)(\"theadTemplate\", ctx_r1.theadTemplate)(\"tfootTemplate\", ctx_r1.tfootTemplate)(\"tfootFixed\", ctx_r1.tfootFixed)(\"verticalScrollBarWidth\", ctx_r1.verticalScrollBarWidth)(\"virtualTemplate\", ctx_r1.nzVirtualScrollDirective ? ctx_r1.nzVirtualScrollDirective.templateRef : null)(\"virtualItemSize\", ctx_r1.nzVirtualItemSize)(\"virtualMaxBufferPx\", ctx_r1.nzVirtualMaxBufferPx)(\"virtualMinBufferPx\", ctx_r1.nzVirtualMinBufferPx)(\"tableMainElement\", tableMainElement_r3)(\"virtualForTrackBy\", ctx_r1.nzVirtualForTrackBy)(\"noDataVirtualHeight\", ctx_r1.noDataVirtualHeight);\n  }\n}\nfunction NzTableComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-inner-default\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const contentTemplate_r4 = i0.ɵɵreference(12);\n    i0.ɵɵproperty(\"tableLayout\", ctx_r1.nzTableLayout)(\"listOfColWidth\", ctx_r1.listOfManualColWidth)(\"theadTemplate\", ctx_r1.theadTemplate)(\"contentTemplate\", contentTemplate_r4)(\"tfootTemplate\", ctx_r1.tfootTemplate);\n  }\n}\nfunction NzTableComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-title-footer\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"footer\", ctx_r1.nzFooter);\n  }\n}\nfunction NzTableComponent_Conditional_8_ng_template_0_Template(rf, ctx) {}\nfunction NzTableComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzTableComponent_Conditional_8_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const paginationTemplate_r1 = i0.ɵɵreference(10);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", paginationTemplate_r1);\n  }\n}\nfunction NzTableComponent_ng_template_9_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-pagination\", 11);\n    i0.ɵɵlistener(\"nzPageSizeChange\", function NzTableComponent_ng_template_9_Conditional_0_Template_nz_pagination_nzPageSizeChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPageSizeChange($event));\n    })(\"nzPageIndexChange\", function NzTableComponent_ng_template_9_Conditional_0_Template_nz_pagination_nzPageIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPageIndexChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"hidden\", !ctx_r1.showPagination)(\"nzShowSizeChanger\", ctx_r1.nzShowSizeChanger)(\"nzPageSizeOptions\", ctx_r1.nzPageSizeOptions)(\"nzItemRender\", ctx_r1.nzItemRender)(\"nzShowQuickJumper\", ctx_r1.nzShowQuickJumper)(\"nzHideOnSinglePage\", ctx_r1.nzHideOnSinglePage)(\"nzShowTotal\", ctx_r1.nzShowTotal)(\"nzSize\", ctx_r1.nzPaginationType === \"small\" ? \"small\" : ctx_r1.nzSize === \"default\" ? \"default\" : \"small\")(\"nzPageSize\", ctx_r1.nzPageSize)(\"nzTotal\", ctx_r1.nzTotal)(\"nzSimple\", ctx_r1.nzSimple)(\"nzPageIndex\", ctx_r1.nzPageIndex);\n  }\n}\nfunction NzTableComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzTableComponent_ng_template_9_Conditional_0_Template, 1, 12, \"nz-pagination\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1.nzShowPagination && ctx_r1.data.length ? 0 : -1);\n  }\n}\nfunction NzTableComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c14 = [\"contentTemplate\"];\nconst _c15 = [\"nzSummary\", \"\"];\nfunction NzTfootSummaryComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzTfootSummaryComponent_Conditional_2_ng_template_0_Template(rf, ctx) {}\nfunction NzTfootSummaryComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzTfootSummaryComponent_Conditional_2_ng_template_0_Template, 0, 0, \"ng-template\", 1);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const contentTemplate_r1 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r1);\n  }\n}\nfunction NzTheadComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzTheadComponent_Conditional_2_ng_template_0_Template(rf, ctx) {}\nfunction NzTheadComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzTheadComponent_Conditional_2_ng_template_0_Template, 0, 0, \"ng-template\", 1);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const contentTemplate_r1 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r1);\n  }\n}\nconst NZ_CONFIG_MODULE_NAME$2 = 'filterTrigger';\nlet NzFilterTriggerComponent = (() => {\n  let _nzBackdrop_decorators;\n  let _nzBackdrop_initializers = [];\n  let _nzBackdrop_extraInitializers = [];\n  return class NzFilterTriggerComponent {\n    static {\n      const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(null) : void 0;\n      _nzBackdrop_decorators = [WithConfig()];\n      __esDecorate(null, null, _nzBackdrop_decorators, {\n        kind: \"field\",\n        name: \"nzBackdrop\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzBackdrop\" in obj,\n          get: obj => obj.nzBackdrop,\n          set: (obj, value) => {\n            obj.nzBackdrop = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzBackdrop_initializers, _nzBackdrop_extraInitializers);\n      if (_metadata) Object.defineProperty(this, Symbol.metadata, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value: _metadata\n      });\n    }\n    nzConfigService;\n    cdr;\n    destroy$;\n    _nzModuleName = NZ_CONFIG_MODULE_NAME$2;\n    nzActive = false;\n    nzDropdownMenu;\n    nzVisible = false;\n    nzBackdrop = __runInitializers(this, _nzBackdrop_initializers, false);\n    nzVisibleChange = (__runInitializers(this, _nzBackdrop_extraInitializers), new EventEmitter());\n    nzDropdown;\n    onVisibleChange(visible) {\n      this.nzVisible = visible;\n      this.nzVisibleChange.next(visible);\n    }\n    hide() {\n      this.nzVisible = false;\n      this.cdr.markForCheck();\n    }\n    show() {\n      this.nzVisible = true;\n      this.cdr.markForCheck();\n    }\n    constructor(nzConfigService, cdr, destroy$) {\n      this.nzConfigService = nzConfigService;\n      this.cdr = cdr;\n      this.destroy$ = destroy$;\n    }\n    ngOnInit() {\n      fromEventOutsideAngular(this.nzDropdown.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        event.stopPropagation();\n      });\n    }\n    static ɵfac = function NzFilterTriggerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NzFilterTriggerComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.NzDestroyService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFilterTriggerComponent,\n      selectors: [[\"nz-filter-trigger\"]],\n      viewQuery: function NzFilterTriggerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NzDropDownDirective, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzDropdown = _t.first);\n        }\n      },\n      inputs: {\n        nzActive: \"nzActive\",\n        nzDropdownMenu: \"nzDropdownMenu\",\n        nzVisible: \"nzVisible\",\n        nzBackdrop: [2, \"nzBackdrop\", \"nzBackdrop\", booleanAttribute]\n      },\n      outputs: {\n        nzVisibleChange: \"nzVisibleChange\"\n      },\n      exportAs: [\"nzFilterTrigger\"],\n      features: [i0.ɵɵProvidersFeature([NzDestroyService])],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 8,\n      consts: [[\"nz-dropdown\", \"\", \"nzTrigger\", \"click\", \"nzPlacement\", \"bottomRight\", 1, \"ant-table-filter-trigger\", 3, \"nzVisibleChange\", \"nzBackdrop\", \"nzClickHide\", \"nzDropdownMenu\", \"nzVisible\"]],\n      template: function NzFilterTriggerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵlistener(\"nzVisibleChange\", function NzFilterTriggerComponent_Template_span_nzVisibleChange_0_listener($event) {\n            return ctx.onVisibleChange($event);\n          });\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"active\", ctx.nzActive)(\"ant-table-filter-open\", ctx.nzVisible);\n          i0.ɵɵproperty(\"nzBackdrop\", ctx.nzBackdrop)(\"nzClickHide\", false)(\"nzDropdownMenu\", ctx.nzDropdownMenu)(\"nzVisible\", ctx.nzVisible);\n        }\n      },\n      dependencies: [NzDropDownModule, i4.NzDropDownDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  };\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFilterTriggerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-filter-trigger',\n      exportAs: `nzFilterTrigger`,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span\n      nz-dropdown\n      class=\"ant-table-filter-trigger\"\n      nzTrigger=\"click\"\n      nzPlacement=\"bottomRight\"\n      [nzBackdrop]=\"nzBackdrop\"\n      [nzClickHide]=\"false\"\n      [nzDropdownMenu]=\"nzDropdownMenu\"\n      [class.active]=\"nzActive\"\n      [class.ant-table-filter-open]=\"nzVisible\"\n      [nzVisible]=\"nzVisible\"\n      (nzVisibleChange)=\"onVisibleChange($event)\"\n    >\n      <ng-content></ng-content>\n    </span>\n  `,\n      providers: [NzDestroyService],\n      imports: [NzDropDownModule]\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.NzDestroyService\n  }], {\n    nzActive: [{\n      type: Input\n    }],\n    nzDropdownMenu: [{\n      type: Input\n    }],\n    nzVisible: [{\n      type: Input\n    }],\n    nzBackdrop: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzVisibleChange: [{\n      type: Output\n    }],\n    nzDropdown: [{\n      type: ViewChild,\n      args: [NzDropDownDirective, {\n        static: true,\n        read: ElementRef\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableFilterComponent {\n  cdr;\n  i18n;\n  contentTemplate = null;\n  customFilter = false;\n  extraTemplate = null;\n  filterMultiple = true;\n  listOfFilter = [];\n  filterChange = new EventEmitter();\n  destroy$ = new Subject();\n  locale;\n  isChecked = false;\n  isVisible = false;\n  listOfParsedFilter = [];\n  listOfChecked = [];\n  check(filter) {\n    if (this.filterMultiple) {\n      this.listOfParsedFilter = this.listOfParsedFilter.map(item => {\n        if (item === filter) {\n          return {\n            ...item,\n            checked: !filter.checked\n          };\n        } else {\n          return item;\n        }\n      });\n      filter.checked = !filter.checked;\n    } else {\n      this.listOfParsedFilter = this.listOfParsedFilter.map(item => ({\n        ...item,\n        checked: item === filter\n      }));\n    }\n    this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n  }\n  confirm() {\n    this.isVisible = false;\n    this.emitFilterData();\n  }\n  reset() {\n    this.isVisible = false;\n    this.listOfParsedFilter = this.parseListOfFilter(this.listOfFilter, true);\n    this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n    this.emitFilterData();\n  }\n  onVisibleChange(value) {\n    this.isVisible = value;\n    if (!value) {\n      this.emitFilterData();\n    } else {\n      this.listOfChecked = this.listOfParsedFilter.filter(item => item.checked).map(item => item.value);\n    }\n  }\n  emitFilterData() {\n    const listOfChecked = this.listOfParsedFilter.filter(item => item.checked).map(item => item.value);\n    if (!arraysEqual(this.listOfChecked, listOfChecked)) {\n      if (this.filterMultiple) {\n        this.filterChange.emit(listOfChecked);\n      } else {\n        this.filterChange.emit(listOfChecked.length > 0 ? listOfChecked[0] : null);\n      }\n    }\n  }\n  parseListOfFilter(listOfFilter, reset) {\n    return listOfFilter.map(item => {\n      const checked = reset ? false : !!item.byDefault;\n      return {\n        text: item.text,\n        value: item.value,\n        checked\n      };\n    });\n  }\n  getCheckedStatus(listOfParsedFilter) {\n    return listOfParsedFilter.some(item => item.checked);\n  }\n  constructor(cdr, i18n) {\n    this.cdr = cdr;\n    this.i18n = i18n;\n  }\n  ngOnInit() {\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Table');\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      listOfFilter\n    } = changes;\n    if (listOfFilter && this.listOfFilter && this.listOfFilter.length) {\n      this.listOfParsedFilter = this.parseListOfFilter(this.listOfFilter);\n      this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzTableFilterComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableFilterComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.NzI18nService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTableFilterComponent,\n    selectors: [[\"nz-table-filter\"]],\n    hostAttrs: [1, \"ant-table-filter-column\"],\n    inputs: {\n      contentTemplate: \"contentTemplate\",\n      customFilter: \"customFilter\",\n      extraTemplate: \"extraTemplate\",\n      filterMultiple: \"filterMultiple\",\n      listOfFilter: \"listOfFilter\"\n    },\n    outputs: {\n      filterChange: \"filterChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 4,\n    vars: 2,\n    consts: [[\"filterMenu\", \"nzDropdownMenu\"], [1, \"ant-table-column-title\"], [3, \"ngTemplateOutlet\"], [3, \"nzVisibleChange\", \"nzVisible\", \"nzActive\", \"nzDropdownMenu\"], [\"nzType\", \"filter\", \"nzTheme\", \"fill\"], [1, \"ant-table-filter-dropdown\"], [\"nz-menu\", \"\"], [\"nz-menu-item\", \"\", 3, \"nzSelected\"], [1, \"ant-table-filter-dropdown-btns\"], [\"nz-button\", \"\", \"nzType\", \"link\", \"nzSize\", \"small\", 3, \"click\", \"disabled\"], [\"nz-button\", \"\", \"nzType\", \"primary\", \"nzSize\", \"small\", 3, \"click\"], [\"nz-menu-item\", \"\", 3, \"click\", \"nzSelected\"], [\"nz-radio\", \"\", 3, \"ngModel\"], [\"nz-checkbox\", \"\", 3, \"ngModel\"], [\"nz-radio\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"nz-checkbox\", \"\", 3, \"ngModelChange\", \"ngModel\"]],\n    template: function NzTableFilterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"span\", 1);\n        i0.ɵɵtemplate(1, NzTableFilterComponent_ng_template_1_Template, 0, 0, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(2, NzTableFilterComponent_Conditional_2_Template, 13, 6)(3, NzTableFilterComponent_Conditional_3_Template, 1, 1, \"ng-container\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(!ctx.customFilter ? 2 : 3);\n      }\n    },\n    dependencies: [NgTemplateOutlet, NzFilterTriggerComponent, NzIconModule, i2$1.NzIconDirective, NzDropDownModule, i3.NzMenuDirective, i3.NzMenuItemComponent, i4.NzDropdownMenuComponent, NzRadioComponent, NzCheckboxModule, i5.NzCheckboxComponent, FormsModule, i6.NgControlStatus, i6.NgModel, NzButtonModule, i7.NzButtonComponent, i8.ɵNzTransitionPatchDirective, i9.NzWaveDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableFilterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-filter',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span class=\"ant-table-column-title\">\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    </span>\n    @if (!customFilter) {\n      <nz-filter-trigger\n        [nzVisible]=\"isVisible\"\n        [nzActive]=\"isChecked\"\n        [nzDropdownMenu]=\"filterMenu\"\n        (nzVisibleChange)=\"onVisibleChange($event)\"\n      >\n        <nz-icon nzType=\"filter\" nzTheme=\"fill\" />\n      </nz-filter-trigger>\n      <nz-dropdown-menu #filterMenu=\"nzDropdownMenu\">\n        <div class=\"ant-table-filter-dropdown\">\n          <ul nz-menu>\n            @for (f of listOfParsedFilter; track f.value) {\n              <li nz-menu-item [nzSelected]=\"f.checked\" (click)=\"check(f)\">\n                @if (!filterMultiple) {\n                  <label nz-radio [ngModel]=\"f.checked\" (ngModelChange)=\"check(f)\"></label>\n                } @else {\n                  <label nz-checkbox [ngModel]=\"f.checked\" (ngModelChange)=\"check(f)\"></label>\n                }\n                <span>{{ f.text }}</span>\n              </li>\n            }\n          </ul>\n          <div class=\"ant-table-filter-dropdown-btns\">\n            <button nz-button nzType=\"link\" nzSize=\"small\" (click)=\"reset()\" [disabled]=\"!isChecked\">\n              {{ locale.filterReset }}\n            </button>\n            <button nz-button nzType=\"primary\" nzSize=\"small\" (click)=\"confirm()\">{{ locale.filterConfirm }}</button>\n          </div>\n        </div>\n      </nz-dropdown-menu>\n    } @else {\n      <ng-container [ngTemplateOutlet]=\"extraTemplate\"></ng-container>\n    }\n  `,\n      host: {\n        class: 'ant-table-filter-column'\n      },\n      imports: [NgTemplateOutlet, NzFilterTriggerComponent, NzIconModule, NzDropDownModule, NzRadioComponent, NzCheckboxModule, FormsModule, NzButtonModule]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$1.NzI18nService\n  }], {\n    contentTemplate: [{\n      type: Input\n    }],\n    customFilter: [{\n      type: Input\n    }],\n    extraTemplate: [{\n      type: Input\n    }],\n    filterMultiple: [{\n      type: Input\n    }],\n    listOfFilter: [{\n      type: Input\n    }],\n    filterChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRowExpandButtonDirective {\n  expand = false;\n  spaceMode = false;\n  expandChange = new EventEmitter();\n  onHostClick() {\n    if (!this.spaceMode) {\n      this.expand = !this.expand;\n      this.expandChange.next(this.expand);\n    }\n  }\n  static ɵfac = function NzRowExpandButtonDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzRowExpandButtonDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzRowExpandButtonDirective,\n    selectors: [[\"button\", \"nz-row-expand-button\", \"\"]],\n    hostAttrs: [1, \"ant-table-row-expand-icon\"],\n    hostVars: 7,\n    hostBindings: function NzRowExpandButtonDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function NzRowExpandButtonDirective_click_HostBindingHandler() {\n          return ctx.onHostClick();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"type\", \"button\");\n        i0.ɵɵclassProp(\"ant-table-row-expand-icon-expanded\", !ctx.spaceMode && ctx.expand === true)(\"ant-table-row-expand-icon-collapsed\", !ctx.spaceMode && ctx.expand === false)(\"ant-table-row-expand-icon-spaced\", ctx.spaceMode);\n      }\n    },\n    inputs: {\n      expand: \"expand\",\n      spaceMode: \"spaceMode\"\n    },\n    outputs: {\n      expandChange: \"expandChange\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRowExpandButtonDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'button[nz-row-expand-button]',\n      host: {\n        class: 'ant-table-row-expand-icon',\n        '[type]': `'button'`,\n        '[class.ant-table-row-expand-icon-expanded]': `!spaceMode && expand === true`,\n        '[class.ant-table-row-expand-icon-collapsed]': `!spaceMode && expand === false`,\n        '[class.ant-table-row-expand-icon-spaced]': 'spaceMode',\n        '(click)': 'onHostClick()'\n      }\n    }]\n  }], null, {\n    expand: [{\n      type: Input\n    }],\n    spaceMode: [{\n      type: Input\n    }],\n    expandChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRowIndentDirective {\n  indentSize = 0;\n  static ɵfac = function NzRowIndentDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzRowIndentDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzRowIndentDirective,\n    selectors: [[\"nz-row-indent\"]],\n    hostAttrs: [1, \"ant-table-row-indent\"],\n    hostVars: 2,\n    hostBindings: function NzRowIndentDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"padding-left\", ctx.indentSize, \"px\");\n      }\n    },\n    inputs: {\n      indentSize: \"indentSize\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRowIndentDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'nz-row-indent',\n      host: {\n        class: 'ant-table-row-indent',\n        '[style.padding-left.px]': 'indentSize'\n      }\n    }]\n  }], null, {\n    indentSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableSelectionComponent {\n  listOfSelections = [];\n  checked = false;\n  disabled = false;\n  indeterminate = false;\n  label = null;\n  showCheckbox = false;\n  showRowSelection = false;\n  checkedChange = new EventEmitter();\n  onCheckedChange(checked) {\n    this.checked = checked;\n    this.checkedChange.emit(checked);\n  }\n  static ɵfac = function NzTableSelectionComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableSelectionComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTableSelectionComponent,\n    selectors: [[\"nz-table-selection\"]],\n    hostAttrs: [1, \"ant-table-selection\"],\n    inputs: {\n      listOfSelections: \"listOfSelections\",\n      checked: \"checked\",\n      disabled: \"disabled\",\n      indeterminate: \"indeterminate\",\n      label: \"label\",\n      showCheckbox: \"showCheckbox\",\n      showRowSelection: \"showRowSelection\"\n    },\n    outputs: {\n      checkedChange: \"checkedChange\"\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[\"selectionMenu\", \"nzDropdownMenu\"], [\"nz-checkbox\", \"\", 3, \"ant-table-selection-select-all-custom\", \"ngModel\", \"nzDisabled\", \"nzIndeterminate\"], [1, \"ant-table-selection-extra\"], [\"nz-checkbox\", \"\", 3, \"ngModelChange\", \"ngModel\", \"nzDisabled\", \"nzIndeterminate\"], [\"nz-dropdown\", \"\", \"nzPlacement\", \"bottomLeft\", 1, \"ant-table-selection-down\", 3, \"nzDropdownMenu\"], [\"nzType\", \"down\"], [\"nz-menu\", \"\", 1, \"ant-table-selection-menu\"], [\"nz-menu-item\", \"\"], [\"nz-menu-item\", \"\", 3, \"click\"]],\n    template: function NzTableSelectionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzTableSelectionComponent_Conditional_0_Template, 1, 6, \"label\", 1)(1, NzTableSelectionComponent_Conditional_1_Template, 8, 1, \"div\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.showCheckbox ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.showRowSelection ? 1 : -1);\n      }\n    },\n    dependencies: [FormsModule, i6.NgControlStatus, i6.NgModel, NzCheckboxModule, i5.NzCheckboxComponent, NzDropDownModule, i3.NzMenuDirective, i3.NzMenuItemComponent, i4.NzDropDownDirective, i4.NzDropdownMenuComponent, NzIconModule, i2$1.NzIconDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableSelectionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-selection',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @if (showCheckbox) {\n      <label\n        nz-checkbox\n        [class.ant-table-selection-select-all-custom]=\"showRowSelection\"\n        [ngModel]=\"checked\"\n        [nzDisabled]=\"disabled\"\n        [nzIndeterminate]=\"indeterminate\"\n        [attr.aria-label]=\"label\"\n        (ngModelChange)=\"onCheckedChange($event)\"\n      ></label>\n    }\n    @if (showRowSelection) {\n      <div class=\"ant-table-selection-extra\">\n        <span nz-dropdown class=\"ant-table-selection-down\" nzPlacement=\"bottomLeft\" [nzDropdownMenu]=\"selectionMenu\">\n          <nz-icon nzType=\"down\" />\n        </span>\n        <nz-dropdown-menu #selectionMenu=\"nzDropdownMenu\">\n          <ul nz-menu class=\"ant-table-selection-menu\">\n            @for (selection of listOfSelections; track selection) {\n              <li nz-menu-item (click)=\"selection.onSelect()\">\n                {{ selection.text }}\n              </li>\n            }\n          </ul>\n        </nz-dropdown-menu>\n      </div>\n    }\n  `,\n      host: {\n        class: 'ant-table-selection'\n      },\n      imports: [FormsModule, NzCheckboxModule, NzDropDownModule, NzIconModule]\n    }]\n  }], null, {\n    listOfSelections: [{\n      type: Input\n    }],\n    checked: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    indeterminate: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    showCheckbox: [{\n      type: Input\n    }],\n    showRowSelection: [{\n      type: Input\n    }],\n    checkedChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableSortersComponent {\n  sortDirections = ['ascend', 'descend', null];\n  sortOrder = null;\n  contentTemplate = null;\n  isUp = false;\n  isDown = false;\n  ngOnChanges(changes) {\n    const {\n      sortDirections\n    } = changes;\n    if (sortDirections) {\n      this.isUp = this.sortDirections.indexOf('ascend') !== -1;\n      this.isDown = this.sortDirections.indexOf('descend') !== -1;\n    }\n  }\n  static ɵfac = function NzTableSortersComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableSortersComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTableSortersComponent,\n    selectors: [[\"nz-table-sorters\"]],\n    hostAttrs: [1, \"ant-table-column-sorters\"],\n    inputs: {\n      sortDirections: \"sortDirections\",\n      sortOrder: \"sortOrder\",\n      contentTemplate: \"contentTemplate\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 6,\n    vars: 5,\n    consts: [[1, \"ant-table-column-title\"], [3, \"ngTemplateOutlet\"], [1, \"ant-table-column-sorter\"], [1, \"ant-table-column-sorter-inner\"], [\"nzType\", \"caret-up\", 1, \"ant-table-column-sorter-up\", 3, \"active\"], [\"nzType\", \"caret-down\", 1, \"ant-table-column-sorter-down\", 3, \"active\"], [\"nzType\", \"caret-up\", 1, \"ant-table-column-sorter-up\"], [\"nzType\", \"caret-down\", 1, \"ant-table-column-sorter-down\"]],\n    template: function NzTableSortersComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵtemplate(1, NzTableSortersComponent_ng_template_1_Template, 0, 0, \"ng-template\", 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"span\", 2)(3, \"span\", 3);\n        i0.ɵɵtemplate(4, NzTableSortersComponent_Conditional_4_Template, 1, 2, \"nz-icon\", 4)(5, NzTableSortersComponent_Conditional_5_Template, 1, 2, \"nz-icon\", 5);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"ant-table-column-sorter-full\", ctx.isDown && ctx.isUp);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx.isUp ? 4 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.isDown ? 5 : -1);\n      }\n    },\n    dependencies: [NzIconModule, i2$1.NzIconDirective, NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableSortersComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-sorters',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span class=\"ant-table-column-title\"><ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template></span>\n    <span class=\"ant-table-column-sorter\" [class.ant-table-column-sorter-full]=\"isDown && isUp\">\n      <span class=\"ant-table-column-sorter-inner\">\n        @if (isUp) {\n          <nz-icon nzType=\"caret-up\" class=\"ant-table-column-sorter-up\" [class.active]=\"sortOrder === 'ascend'\" />\n        }\n        @if (isDown) {\n          <nz-icon nzType=\"caret-down\" class=\"ant-table-column-sorter-down\" [class.active]=\"sortOrder === 'descend'\" />\n        }\n      </span>\n    </span>\n  `,\n      host: {\n        class: 'ant-table-column-sorters'\n      },\n      imports: [NzIconModule, NgTemplateOutlet]\n    }]\n  }], null, {\n    sortDirections: [{\n      type: Input\n    }],\n    sortOrder: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCellFixedDirective {\n  renderer;\n  elementRef;\n  nzRight = false;\n  nzLeft = false;\n  colspan = null;\n  colSpan = null;\n  changes$ = new Subject();\n  isAutoLeft = false;\n  isAutoRight = false;\n  isFixedLeft = false;\n  isFixedRight = false;\n  isFixed = false;\n  setAutoLeftWidth(autoLeft) {\n    this.renderer.setStyle(this.elementRef.nativeElement, 'left', autoLeft);\n  }\n  setAutoRightWidth(autoRight) {\n    this.renderer.setStyle(this.elementRef.nativeElement, 'right', autoRight);\n  }\n  setIsFirstRight(isFirstRight) {\n    this.setFixClass(isFirstRight, 'ant-table-cell-fix-right-first');\n  }\n  setIsLastLeft(isLastLeft) {\n    this.setFixClass(isLastLeft, 'ant-table-cell-fix-left-last');\n  }\n  setFixClass(flag, className) {\n    // the setFixClass function may call many times, so remove it first.\n    this.renderer.removeClass(this.elementRef.nativeElement, className);\n    if (flag) {\n      this.renderer.addClass(this.elementRef.nativeElement, className);\n    }\n  }\n  constructor(renderer, elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n  }\n  ngOnChanges() {\n    this.setIsFirstRight(false);\n    this.setIsLastLeft(false);\n    this.isAutoLeft = this.nzLeft === '' || this.nzLeft === true;\n    this.isAutoRight = this.nzRight === '' || this.nzRight === true;\n    this.isFixedLeft = this.nzLeft !== false;\n    this.isFixedRight = this.nzRight !== false;\n    this.isFixed = this.isFixedLeft || this.isFixedRight;\n    const validatePx = value => {\n      if (typeof value === 'string' && value !== '') {\n        return value;\n      } else {\n        return null;\n      }\n    };\n    this.setAutoLeftWidth(validatePx(this.nzLeft));\n    this.setAutoRightWidth(validatePx(this.nzRight));\n    this.changes$.next();\n  }\n  static ɵfac = function NzCellFixedDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzCellFixedDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzCellFixedDirective,\n    selectors: [[\"td\", \"nzRight\", \"\"], [\"th\", \"nzRight\", \"\"], [\"td\", \"nzLeft\", \"\"], [\"th\", \"nzLeft\", \"\"]],\n    hostVars: 6,\n    hostBindings: function NzCellFixedDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"position\", ctx.isFixed ? \"sticky\" : null);\n        i0.ɵɵclassProp(\"ant-table-cell-fix-right\", ctx.isFixedRight)(\"ant-table-cell-fix-left\", ctx.isFixedLeft);\n      }\n    },\n    inputs: {\n      nzRight: \"nzRight\",\n      nzLeft: \"nzLeft\",\n      colspan: \"colspan\",\n      colSpan: \"colSpan\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellFixedDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'td[nzRight],th[nzRight],td[nzLeft],th[nzLeft]',\n      host: {\n        '[class.ant-table-cell-fix-right]': `isFixedRight`,\n        '[class.ant-table-cell-fix-left]': `isFixedLeft`,\n        '[style.position]': `isFixed? 'sticky' : null`\n      }\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzRight: [{\n      type: Input\n    }],\n    nzLeft: [{\n      type: Input\n    }],\n    colspan: [{\n      type: Input\n    }],\n    colSpan: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableStyleService {\n  theadTemplate$ = new ReplaySubject(1);\n  tfootTemplate$ = new ReplaySubject(1);\n  tfootFixed$ = new ReplaySubject(1);\n  hasFixLeft$ = new ReplaySubject(1);\n  hasFixRight$ = new ReplaySubject(1);\n  hostWidth$ = new ReplaySubject(1);\n  columnCount$ = new ReplaySubject(1);\n  showEmpty$ = new ReplaySubject(1);\n  noResult$ = new ReplaySubject(1);\n  listOfThWidthConfigPx$ = new BehaviorSubject([]);\n  tableWidthConfigPx$ = new BehaviorSubject([]);\n  manualWidthConfigPx$ = combineLatest([this.tableWidthConfigPx$, this.listOfThWidthConfigPx$]).pipe(map(([widthConfig, listOfWidth]) => widthConfig.length ? widthConfig : listOfWidth));\n  listOfAutoWidthPx$ = new ReplaySubject(1);\n  listOfListOfThWidthPx$ = merge(/** init with manual width **/\n  this.manualWidthConfigPx$, combineLatest([this.listOfAutoWidthPx$, this.manualWidthConfigPx$]).pipe(map(([autoWidth, manualWidth]) => {\n    /** use autoWidth until column length match **/\n    if (autoWidth.length === manualWidth.length) {\n      return autoWidth.map((width, index) => {\n        if (width === '0px') {\n          return manualWidth[index] || null;\n        } else {\n          return manualWidth[index] || width;\n        }\n      });\n    } else {\n      return manualWidth;\n    }\n  })));\n  listOfMeasureColumn$ = new ReplaySubject(1);\n  listOfListOfThWidth$ = this.listOfAutoWidthPx$.pipe(map(list => list.map(width => parseInt(width, 10))));\n  enableAutoMeasure$ = new ReplaySubject(1);\n  setTheadTemplate(template) {\n    this.theadTemplate$.next(template);\n  }\n  setTfootTemplate(template) {\n    this.tfootTemplate$.next(template);\n  }\n  setTfootFixed(fixed) {\n    this.tfootFixed$.next(fixed);\n  }\n  setHasFixLeft(hasFixLeft) {\n    this.hasFixLeft$.next(hasFixLeft);\n  }\n  setHasFixRight(hasFixRight) {\n    this.hasFixRight$.next(hasFixRight);\n  }\n  setTableWidthConfig(widthConfig) {\n    this.tableWidthConfigPx$.next(widthConfig);\n  }\n  setListOfTh(listOfTh) {\n    let columnCount = 0;\n    listOfTh.forEach(th => {\n      columnCount += th.colspan && +th.colspan || th.colSpan && +th.colSpan || 1;\n    });\n    const listOfThPx = listOfTh.map(item => item.nzWidth);\n    this.columnCount$.next(columnCount);\n    this.listOfThWidthConfigPx$.next(listOfThPx);\n  }\n  setListOfMeasureColumn(listOfTh) {\n    const listOfKeys = [];\n    listOfTh.forEach(th => {\n      const length = th.colspan && +th.colspan || th.colSpan && +th.colSpan || 1;\n      for (let i = 0; i < length; i++) {\n        listOfKeys.push(`measure_key_${i}`);\n      }\n    });\n    this.listOfMeasureColumn$.next(listOfKeys);\n  }\n  setListOfAutoWidth(listOfAutoWidth) {\n    this.listOfAutoWidthPx$.next(listOfAutoWidth.map(width => `${width}px`));\n  }\n  setShowEmpty(showEmpty) {\n    this.showEmpty$.next(showEmpty);\n  }\n  setNoResult(noResult) {\n    this.noResult$.next(noResult);\n  }\n  setScroll(scrollX, scrollY) {\n    const enableAutoMeasure = !!(scrollX || scrollY);\n    if (!enableAutoMeasure) {\n      this.setListOfAutoWidth([]);\n    }\n    this.enableAutoMeasure$.next(enableAutoMeasure);\n  }\n  static ɵfac = function NzTableStyleService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableStyleService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzTableStyleService,\n    factory: NzTableStyleService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableStyleService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableCellDirective {\n  isInsideTable = !!inject(NzTableStyleService, {\n    optional: true\n  });\n  static ɵfac = function NzTableCellDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableCellDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTableCellDirective,\n    selectors: [[\"th\", 9, \"nz-disable-th\", 3, \"mat-cell\", \"\"], [\"td\", 9, \"nz-disable-td\", 3, \"mat-cell\", \"\"]],\n    hostVars: 2,\n    hostBindings: function NzTableCellDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-table-cell\", ctx.isInsideTable);\n      }\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableCellDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th:not(.nz-disable-th):not([mat-cell]), td:not(.nz-disable-td):not([mat-cell])',\n      host: {\n        '[class.ant-table-cell]': 'isInsideTable'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableDataService {\n  destroy$ = new Subject();\n  pageIndex$ = new BehaviorSubject(1);\n  frontPagination$ = new BehaviorSubject(true);\n  pageSize$ = new BehaviorSubject(10);\n  listOfData$ = new BehaviorSubject([]);\n  listOfCustomColumn$ = new BehaviorSubject([]);\n  pageIndexDistinct$ = this.pageIndex$.pipe(distinctUntilChanged());\n  pageSizeDistinct$ = this.pageSize$.pipe(distinctUntilChanged());\n  listOfCalcOperator$ = new BehaviorSubject([]);\n  queryParams$ = combineLatest([this.pageIndexDistinct$, this.pageSizeDistinct$, this.listOfCalcOperator$]).pipe(debounceTime(0), skip(1), map(([pageIndex, pageSize, listOfCalc]) => ({\n    pageIndex,\n    pageSize,\n    sort: listOfCalc.filter(item => item.sortFn).map(item => ({\n      key: item.key,\n      value: item.sortOrder\n    })),\n    filter: listOfCalc.filter(item => item.filterFn).map(item => ({\n      key: item.key,\n      value: item.filterValue\n    }))\n  })));\n  listOfDataAfterCalc$ = combineLatest([this.listOfData$, this.listOfCalcOperator$]).pipe(map(([listOfData, listOfCalcOperator]) => {\n    let listOfDataAfterCalc = [...listOfData];\n    const listOfFilterOperator = listOfCalcOperator.filter(item => {\n      const {\n        filterValue,\n        filterFn\n      } = item;\n      const isReset = filterValue === null || filterValue === undefined || Array.isArray(filterValue) && filterValue.length === 0;\n      return !isReset && typeof filterFn === 'function';\n    });\n    for (const item of listOfFilterOperator) {\n      const {\n        filterFn,\n        filterValue\n      } = item;\n      listOfDataAfterCalc = listOfDataAfterCalc.filter(data => filterFn(filterValue, data));\n    }\n    const listOfSortOperator = listOfCalcOperator.filter(item => item.sortOrder !== null && typeof item.sortFn === 'function').sort((a, b) => +b.sortPriority - +a.sortPriority);\n    if (listOfCalcOperator.length) {\n      listOfDataAfterCalc.sort((record1, record2) => {\n        for (const item of listOfSortOperator) {\n          const {\n            sortFn,\n            sortOrder\n          } = item;\n          if (sortFn && sortOrder) {\n            const compareResult = sortFn(record1, record2, sortOrder);\n            if (compareResult !== 0) {\n              return sortOrder === 'ascend' ? compareResult : -compareResult;\n            }\n          }\n        }\n        return 0;\n      });\n    }\n    return listOfDataAfterCalc;\n  }));\n  listOfFrontEndCurrentPageData$ = combineLatest([this.pageIndexDistinct$, this.pageSizeDistinct$, this.listOfDataAfterCalc$]).pipe(takeUntil(this.destroy$), filter(value => {\n    const [pageIndex, pageSize, listOfData] = value;\n    const maxPageIndex = Math.ceil(listOfData.length / pageSize) || 1;\n    return pageIndex <= maxPageIndex;\n  }), map(([pageIndex, pageSize, listOfData]) => listOfData.slice((pageIndex - 1) * pageSize, pageIndex * pageSize)));\n  listOfCurrentPageData$ = this.frontPagination$.pipe(switchMap(pagination => pagination ? this.listOfFrontEndCurrentPageData$ : this.listOfDataAfterCalc$));\n  total$ = this.frontPagination$.pipe(switchMap(pagination => pagination ? this.listOfDataAfterCalc$ : this.listOfData$), map(list => list.length), distinctUntilChanged());\n  updatePageSize(size) {\n    this.pageSize$.next(size);\n  }\n  updateFrontPagination(pagination) {\n    this.frontPagination$.next(pagination);\n  }\n  updatePageIndex(index) {\n    this.pageIndex$.next(index);\n  }\n  updateListOfData(list) {\n    this.listOfData$.next(list);\n  }\n  updateListOfCustomColumn(list) {\n    this.listOfCustomColumn$.next(list);\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzTableDataService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableDataService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzTableDataService,\n    factory: NzTableDataService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableDataService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCustomColumnDirective {\n  el;\n  renderer;\n  nzTableDataService;\n  nzCellControl = null;\n  destroy$ = new Subject();\n  constructor(el, renderer, nzTableDataService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.nzTableDataService = nzTableDataService;\n  }\n  ngOnInit() {\n    this.nzTableDataService.listOfCustomColumn$.pipe(takeUntil(this.destroy$)).subscribe(item => {\n      if (item.length) {\n        item.forEach((v, i) => {\n          if (v.value === this.nzCellControl) {\n            if (!v.default) {\n              this.renderer.setStyle(this.el.nativeElement, 'display', 'none');\n            } else {\n              this.renderer.setStyle(this.el.nativeElement, 'display', 'block');\n            }\n            this.renderer.setStyle(this.el.nativeElement, 'order', i);\n            if (!v?.fixWidth) {\n              this.renderer.setStyle(this.el.nativeElement, 'flex', `1 1 ${v.width}px`);\n            } else {\n              this.renderer.setStyle(this.el.nativeElement, 'flex', `1 0 ${v.width}px`);\n            }\n          }\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzCustomColumnDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzCustomColumnDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(NzTableDataService));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzCustomColumnDirective,\n    selectors: [[\"td\", \"nzCellControl\", \"\"], [\"th\", \"nzCellControl\", \"\"]],\n    inputs: {\n      nzCellControl: \"nzCellControl\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCustomColumnDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'td[nzCellControl],th[nzCellControl]'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: NzTableDataService\n  }], {\n    nzCellControl: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTdAddOnComponent {\n  nzChecked = false;\n  nzDisabled = false;\n  nzIndeterminate = false;\n  nzLabel = null;\n  nzIndentSize = 0;\n  nzShowExpand = false;\n  nzShowCheckbox = false;\n  nzExpand = false;\n  nzExpandIcon = null;\n  nzCheckedChange = new EventEmitter();\n  nzExpandChange = new EventEmitter();\n  isNzShowExpandChanged = false;\n  isNzShowCheckboxChanged = false;\n  onCheckedChange(checked) {\n    this.nzChecked = checked;\n    this.nzCheckedChange.emit(checked);\n  }\n  onExpandChange(expand) {\n    this.nzExpand = expand;\n    this.nzExpandChange.emit(expand);\n  }\n  ngOnChanges(changes) {\n    const isFirstChange = value => value && value.firstChange && value.currentValue !== undefined;\n    const {\n      nzExpand,\n      nzChecked,\n      nzShowExpand,\n      nzShowCheckbox\n    } = changes;\n    if (nzShowExpand) {\n      this.isNzShowExpandChanged = true;\n    }\n    if (nzShowCheckbox) {\n      this.isNzShowCheckboxChanged = true;\n    }\n    if (isFirstChange(nzExpand) && !this.isNzShowExpandChanged) {\n      this.nzShowExpand = true;\n    }\n    if (isFirstChange(nzChecked) && !this.isNzShowCheckboxChanged) {\n      this.nzShowCheckbox = true;\n    }\n  }\n  static ɵfac = function NzTdAddOnComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTdAddOnComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTdAddOnComponent,\n    selectors: [[\"td\", \"nzChecked\", \"\"], [\"td\", \"nzDisabled\", \"\"], [\"td\", \"nzIndeterminate\", \"\"], [\"td\", \"nzIndentSize\", \"\"], [\"td\", \"nzExpand\", \"\"], [\"td\", \"nzShowExpand\", \"\"], [\"td\", \"nzShowCheckbox\", \"\"]],\n    hostVars: 4,\n    hostBindings: function NzTdAddOnComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-table-cell-with-append\", ctx.nzShowExpand || ctx.nzIndentSize > 0)(\"ant-table-selection-column\", ctx.nzShowCheckbox);\n      }\n    },\n    inputs: {\n      nzChecked: \"nzChecked\",\n      nzDisabled: \"nzDisabled\",\n      nzIndeterminate: \"nzIndeterminate\",\n      nzLabel: \"nzLabel\",\n      nzIndentSize: \"nzIndentSize\",\n      nzShowExpand: [2, \"nzShowExpand\", \"nzShowExpand\", booleanAttribute],\n      nzShowCheckbox: [2, \"nzShowCheckbox\", \"nzShowCheckbox\", booleanAttribute],\n      nzExpand: [2, \"nzExpand\", \"nzExpand\", booleanAttribute],\n      nzExpandIcon: \"nzExpandIcon\"\n    },\n    outputs: {\n      nzCheckedChange: \"nzCheckedChange\",\n      nzExpandChange: \"nzExpandChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    attrs: _c1,\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 2,\n    consts: [[\"nz-checkbox\", \"\", 3, \"nzDisabled\", \"ngModel\", \"nzIndeterminate\"], [3, \"indentSize\"], [3, \"ngTemplateOutlet\"], [\"nz-row-expand-button\", \"\", 3, \"expand\", \"spaceMode\"], [\"nz-row-expand-button\", \"\", 3, \"expandChange\", \"expand\", \"spaceMode\"], [\"nz-checkbox\", \"\", 3, \"ngModelChange\", \"nzDisabled\", \"ngModel\", \"nzIndeterminate\"]],\n    template: function NzTdAddOnComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NzTdAddOnComponent_Conditional_0_Template, 3, 2)(1, NzTdAddOnComponent_Conditional_1_Template, 1, 4, \"label\", 0);\n        i0.ɵɵprojection(2);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.nzShowExpand || ctx.nzIndentSize > 0 ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.nzShowCheckbox ? 1 : -1);\n      }\n    },\n    dependencies: [NzRowIndentDirective, NzRowExpandButtonDirective, NgTemplateOutlet, NzCheckboxModule, i5.NzCheckboxComponent, FormsModule, i6.NgControlStatus, i6.NgModel],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTdAddOnComponent, [{\n    type: Component,\n    args: [{\n      selector: 'td[nzChecked], td[nzDisabled], td[nzIndeterminate], td[nzIndentSize], td[nzExpand], td[nzShowExpand], td[nzShowCheckbox]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @if (nzShowExpand || nzIndentSize > 0) {\n      <nz-row-indent [indentSize]=\"nzIndentSize\"></nz-row-indent>\n      @if (nzExpandIcon) {\n        <ng-template [ngTemplateOutlet]=\"nzExpandIcon\"></ng-template>\n      } @else {\n        <button\n          nz-row-expand-button\n          [expand]=\"nzExpand\"\n          (expandChange)=\"onExpandChange($event)\"\n          [spaceMode]=\"!nzShowExpand\"\n        ></button>\n      }\n    }\n    @if (nzShowCheckbox) {\n      <label\n        nz-checkbox\n        [nzDisabled]=\"nzDisabled\"\n        [ngModel]=\"nzChecked\"\n        [nzIndeterminate]=\"nzIndeterminate\"\n        [attr.aria-label]=\"nzLabel\"\n        (ngModelChange)=\"onCheckedChange($event)\"\n      ></label>\n    }\n    <ng-content></ng-content>\n  `,\n      host: {\n        '[class.ant-table-cell-with-append]': `nzShowExpand || nzIndentSize > 0`,\n        '[class.ant-table-selection-column]': `nzShowCheckbox`\n      },\n      imports: [NzRowIndentDirective, NzRowExpandButtonDirective, NgTemplateOutlet, NzCheckboxModule, FormsModule]\n    }]\n  }], null, {\n    nzChecked: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzIndeterminate: [{\n      type: Input\n    }],\n    nzLabel: [{\n      type: Input\n    }],\n    nzIndentSize: [{\n      type: Input\n    }],\n    nzShowExpand: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzShowCheckbox: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzExpand: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzExpandIcon: [{\n      type: Input\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }],\n    nzExpandChange: [{\n      type: Output\n    }]\n  });\n})();\nconst NZ_CONFIG_MODULE_NAME$1 = 'table';\nlet NzThAddOnComponent = (() => {\n  let _nzSortDirections_decorators;\n  let _nzSortDirections_initializers = [];\n  let _nzSortDirections_extraInitializers = [];\n  return class NzThAddOnComponent {\n    static {\n      const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(null) : void 0;\n      _nzSortDirections_decorators = [WithConfig()];\n      __esDecorate(null, null, _nzSortDirections_decorators, {\n        kind: \"field\",\n        name: \"nzSortDirections\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzSortDirections\" in obj,\n          get: obj => obj.nzSortDirections,\n          set: (obj, value) => {\n            obj.nzSortDirections = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzSortDirections_initializers, _nzSortDirections_extraInitializers);\n      if (_metadata) Object.defineProperty(this, Symbol.metadata, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value: _metadata\n      });\n    }\n    nzConfigService;\n    host;\n    cdr;\n    ngZone;\n    destroy$;\n    _nzModuleName = NZ_CONFIG_MODULE_NAME$1;\n    manualClickOrder$ = new Subject();\n    calcOperatorChange$ = new Subject();\n    nzFilterValue = null;\n    sortOrder = null;\n    sortDirections = ['ascend', 'descend', null];\n    sortOrderChange$ = new Subject();\n    isNzShowSortChanged = false;\n    isNzShowFilterChanged = false;\n    nzColumnKey;\n    nzFilterMultiple = true;\n    nzSortOrder = null;\n    nzSortPriority = false;\n    nzSortDirections = __runInitializers(this, _nzSortDirections_initializers, ['ascend', 'descend', null]);\n    nzFilters = (__runInitializers(this, _nzSortDirections_extraInitializers), []);\n    nzSortFn = null;\n    nzFilterFn = null;\n    nzShowSort = false;\n    nzShowFilter = false;\n    nzCustomFilter = false;\n    nzCheckedChange = new EventEmitter();\n    nzSortOrderChange = new EventEmitter();\n    nzFilterChange = new EventEmitter();\n    getNextSortDirection(sortDirections, current) {\n      const index = sortDirections.indexOf(current);\n      if (index === sortDirections.length - 1) {\n        return sortDirections[0];\n      } else {\n        return sortDirections[index + 1];\n      }\n    }\n    setSortOrder(order) {\n      this.sortOrderChange$.next(order);\n    }\n    clearSortOrder() {\n      if (this.sortOrder !== null) {\n        this.setSortOrder(null);\n      }\n    }\n    onFilterValueChange(value) {\n      this.nzFilterChange.emit(value);\n      this.nzFilterValue = value;\n      this.updateCalcOperator();\n    }\n    updateCalcOperator() {\n      this.calcOperatorChange$.next();\n    }\n    constructor(nzConfigService, host, cdr, ngZone, destroy$) {\n      this.nzConfigService = nzConfigService;\n      this.host = host;\n      this.cdr = cdr;\n      this.ngZone = ngZone;\n      this.destroy$ = destroy$;\n    }\n    ngOnInit() {\n      fromEventOutsideAngular(this.host.nativeElement, 'click').pipe(filter(() => this.nzShowSort), takeUntil(this.destroy$)).subscribe(() => {\n        const nextOrder = this.getNextSortDirection(this.sortDirections, this.sortOrder);\n        this.ngZone.run(() => {\n          this.setSortOrder(nextOrder);\n          this.manualClickOrder$.next(this);\n        });\n      });\n      this.sortOrderChange$.pipe(takeUntil(this.destroy$)).subscribe(order => {\n        if (this.sortOrder !== order) {\n          this.sortOrder = order;\n          this.nzSortOrderChange.emit(order);\n        }\n        this.updateCalcOperator();\n        this.cdr.markForCheck();\n      });\n    }\n    ngOnChanges(changes) {\n      const {\n        nzSortDirections,\n        nzFilters,\n        nzSortOrder,\n        nzSortFn,\n        nzFilterFn,\n        nzSortPriority,\n        nzFilterMultiple,\n        nzShowSort,\n        nzShowFilter\n      } = changes;\n      if (nzSortDirections) {\n        if (this.nzSortDirections && this.nzSortDirections.length) {\n          this.sortDirections = this.nzSortDirections;\n        }\n      }\n      if (nzSortOrder) {\n        this.sortOrder = this.nzSortOrder;\n        this.setSortOrder(this.nzSortOrder);\n      }\n      if (nzShowSort) {\n        this.isNzShowSortChanged = true;\n      }\n      if (nzShowFilter) {\n        this.isNzShowFilterChanged = true;\n      }\n      const isFirstChange = value => value && value.firstChange && value.currentValue !== undefined;\n      if ((isFirstChange(nzSortOrder) || isFirstChange(nzSortFn)) && !this.isNzShowSortChanged) {\n        this.nzShowSort = true;\n      }\n      if (isFirstChange(nzFilters) && !this.isNzShowFilterChanged) {\n        this.nzShowFilter = true;\n      }\n      if ((nzFilters || nzFilterMultiple) && this.nzShowFilter) {\n        const listOfValue = this.nzFilters.filter(item => item.byDefault).map(item => item.value);\n        this.nzFilterValue = this.nzFilterMultiple ? listOfValue : listOfValue[0] || null;\n      }\n      if (nzSortFn || nzFilterFn || nzSortPriority || nzFilters) {\n        this.updateCalcOperator();\n      }\n    }\n    static ɵfac = function NzThAddOnComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NzThAddOnComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.NzDestroyService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzThAddOnComponent,\n      selectors: [[\"th\", \"nzColumnKey\", \"\"], [\"th\", \"nzSortFn\", \"\"], [\"th\", \"nzSortOrder\", \"\"], [\"th\", \"nzFilters\", \"\"], [\"th\", \"nzShowSort\", \"\"], [\"th\", \"nzShowFilter\", \"\"], [\"th\", \"nzCustomFilter\", \"\"]],\n      hostVars: 4,\n      hostBindings: function NzThAddOnComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-column-has-sorters\", ctx.nzShowSort)(\"ant-table-column-sort\", ctx.sortOrder === \"descend\" || ctx.sortOrder === \"ascend\");\n        }\n      },\n      inputs: {\n        nzColumnKey: \"nzColumnKey\",\n        nzFilterMultiple: \"nzFilterMultiple\",\n        nzSortOrder: \"nzSortOrder\",\n        nzSortPriority: \"nzSortPriority\",\n        nzSortDirections: \"nzSortDirections\",\n        nzFilters: \"nzFilters\",\n        nzSortFn: \"nzSortFn\",\n        nzFilterFn: \"nzFilterFn\",\n        nzShowSort: [2, \"nzShowSort\", \"nzShowSort\", booleanAttribute],\n        nzShowFilter: [2, \"nzShowFilter\", \"nzShowFilter\", booleanAttribute],\n        nzCustomFilter: [2, \"nzCustomFilter\", \"nzCustomFilter\", booleanAttribute]\n      },\n      outputs: {\n        nzCheckedChange: \"nzCheckedChange\",\n        nzSortOrderChange: \"nzSortOrderChange\",\n        nzFilterChange: \"nzFilterChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([NzDestroyService]), i0.ɵɵNgOnChangesFeature],\n      attrs: _c2,\n      ngContentSelectors: _c4,\n      decls: 10,\n      vars: 1,\n      consts: [[\"notFilterTemplate\", \"\"], [\"extraTemplate\", \"\"], [\"sortTemplate\", \"\"], [\"contentTemplate\", \"\"], [3, \"contentTemplate\", \"extraTemplate\", \"customFilter\", \"filterMultiple\", \"listOfFilter\"], [3, \"ngTemplateOutlet\"], [3, \"filterChange\", \"contentTemplate\", \"extraTemplate\", \"customFilter\", \"filterMultiple\", \"listOfFilter\"], [3, \"sortOrder\", \"sortDirections\", \"contentTemplate\"]],\n      template: function NzThAddOnComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵtemplate(0, NzThAddOnComponent_Conditional_0_Template, 1, 5, \"nz-table-filter\", 4)(1, NzThAddOnComponent_Conditional_1_Template, 1, 1, \"ng-container\", 5)(2, NzThAddOnComponent_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, NzThAddOnComponent_ng_template_4_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(6, NzThAddOnComponent_ng_template_6_Template, 1, 3, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(8, NzThAddOnComponent_ng_template_8_Template, 1, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.nzShowFilter || ctx.nzCustomFilter ? 0 : 1);\n        }\n      },\n      dependencies: [NzTableFilterComponent, NgTemplateOutlet, NzTableSortersComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  };\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzThAddOnComponent, [{\n    type: Component,\n    args: [{\n      selector: 'th[nzColumnKey], th[nzSortFn], th[nzSortOrder], th[nzFilters], th[nzShowSort], th[nzShowFilter], th[nzCustomFilter]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (nzShowFilter || nzCustomFilter) {\n      <nz-table-filter\n        [contentTemplate]=\"notFilterTemplate\"\n        [extraTemplate]=\"extraTemplate\"\n        [customFilter]=\"nzCustomFilter\"\n        [filterMultiple]=\"nzFilterMultiple\"\n        [listOfFilter]=\"nzFilters\"\n        (filterChange)=\"onFilterValueChange($event)\"\n      ></nz-table-filter>\n    } @else {\n      <ng-container [ngTemplateOutlet]=\"notFilterTemplate\"></ng-container>\n    }\n    <ng-template #notFilterTemplate>\n      <ng-template [ngTemplateOutlet]=\"nzShowSort ? sortTemplate : contentTemplate\"></ng-template>\n    </ng-template>\n    <ng-template #extraTemplate>\n      <ng-content select=\"[nz-th-extra]\"></ng-content>\n      <ng-content select=\"nz-filter-trigger\"></ng-content>\n    </ng-template>\n    <ng-template #sortTemplate>\n      <nz-table-sorters\n        [sortOrder]=\"sortOrder\"\n        [sortDirections]=\"sortDirections\"\n        [contentTemplate]=\"contentTemplate\"\n      ></nz-table-sorters>\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      host: {\n        '[class.ant-table-column-has-sorters]': 'nzShowSort',\n        '[class.ant-table-column-sort]': `sortOrder === 'descend' || sortOrder === 'ascend'`\n      },\n      providers: [NzDestroyService],\n      imports: [NzTableFilterComponent, NgTemplateOutlet, NzTableSortersComponent]\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i2.NzDestroyService\n  }], {\n    nzColumnKey: [{\n      type: Input\n    }],\n    nzFilterMultiple: [{\n      type: Input\n    }],\n    nzSortOrder: [{\n      type: Input\n    }],\n    nzSortPriority: [{\n      type: Input\n    }],\n    nzSortDirections: [{\n      type: Input\n    }],\n    nzFilters: [{\n      type: Input\n    }],\n    nzSortFn: [{\n      type: Input\n    }],\n    nzFilterFn: [{\n      type: Input\n    }],\n    nzShowSort: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzShowFilter: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzCustomFilter: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }],\n    nzSortOrderChange: [{\n      type: Output\n    }],\n    nzFilterChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzThMeasureDirective {\n  renderer;\n  elementRef;\n  changes$ = new Subject();\n  nzWidth = null;\n  colspan = null;\n  colSpan = null;\n  rowspan = null;\n  rowSpan = null;\n  constructor(renderer, elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzWidth,\n      colspan,\n      rowspan,\n      colSpan,\n      rowSpan\n    } = changes;\n    if (colspan || colSpan) {\n      const col = this.colspan || this.colSpan;\n      if (!isNil(col)) {\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'colspan', `${col}`);\n      } else {\n        this.renderer.removeAttribute(this.elementRef.nativeElement, 'colspan');\n      }\n    }\n    if (rowspan || rowSpan) {\n      const row = this.rowspan || this.rowSpan;\n      if (!isNil(row)) {\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'rowspan', `${row}`);\n      } else {\n        this.renderer.removeAttribute(this.elementRef.nativeElement, 'rowspan');\n      }\n    }\n    if (nzWidth || colspan) {\n      this.changes$.next();\n    }\n  }\n  static ɵfac = function NzThMeasureDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzThMeasureDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzThMeasureDirective,\n    selectors: [[\"th\"]],\n    inputs: {\n      nzWidth: \"nzWidth\",\n      colspan: \"colspan\",\n      colSpan: \"colSpan\",\n      rowspan: \"rowspan\",\n      rowSpan: \"rowSpan\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzThMeasureDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th'\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzWidth: [{\n      type: Input\n    }],\n    colspan: [{\n      type: Input\n    }],\n    colSpan: [{\n      type: Input\n    }],\n    rowspan: [{\n      type: Input\n    }],\n    rowSpan: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzThSelectionComponent {\n  nzSelections = [];\n  nzChecked = false;\n  nzDisabled = false;\n  nzIndeterminate = false;\n  nzLabel = null;\n  nzShowCheckbox = false;\n  nzShowRowSelection = false;\n  nzCheckedChange = new EventEmitter();\n  isNzShowExpandChanged = false;\n  isNzShowCheckboxChanged = false;\n  onCheckedChange(checked) {\n    this.nzChecked = checked;\n    this.nzCheckedChange.emit(checked);\n  }\n  ngOnChanges(changes) {\n    const isFirstChange = value => value && value.firstChange && value.currentValue !== undefined;\n    const {\n      nzChecked,\n      nzSelections,\n      nzShowExpand,\n      nzShowCheckbox\n    } = changes;\n    if (nzShowExpand) {\n      this.isNzShowExpandChanged = true;\n    }\n    if (nzShowCheckbox) {\n      this.isNzShowCheckboxChanged = true;\n    }\n    if (isFirstChange(nzSelections) && !this.isNzShowExpandChanged) {\n      this.nzShowRowSelection = true;\n    }\n    if (isFirstChange(nzChecked) && !this.isNzShowCheckboxChanged) {\n      this.nzShowCheckbox = true;\n    }\n  }\n  static ɵfac = function NzThSelectionComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzThSelectionComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzThSelectionComponent,\n    selectors: [[\"th\", \"nzSelections\", \"\"], [\"th\", \"nzChecked\", \"\"], [\"th\", \"nzShowCheckbox\", \"\"], [\"th\", \"nzShowRowSelection\", \"\"]],\n    hostAttrs: [1, \"ant-table-selection-column\"],\n    inputs: {\n      nzSelections: \"nzSelections\",\n      nzChecked: [2, \"nzChecked\", \"nzChecked\", booleanAttribute],\n      nzDisabled: [2, \"nzDisabled\", \"nzDisabled\", booleanAttribute],\n      nzIndeterminate: \"nzIndeterminate\",\n      nzLabel: \"nzLabel\",\n      nzShowCheckbox: [2, \"nzShowCheckbox\", \"nzShowCheckbox\", booleanAttribute],\n      nzShowRowSelection: [2, \"nzShowRowSelection\", \"nzShowRowSelection\", booleanAttribute]\n    },\n    outputs: {\n      nzCheckedChange: \"nzCheckedChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    attrs: _c5,\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 7,\n    consts: [[3, \"checkedChange\", \"checked\", \"disabled\", \"indeterminate\", \"label\", \"listOfSelections\", \"showCheckbox\", \"showRowSelection\"]],\n    template: function NzThSelectionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"nz-table-selection\", 0);\n        i0.ɵɵlistener(\"checkedChange\", function NzThSelectionComponent_Template_nz_table_selection_checkedChange_0_listener($event) {\n          return ctx.onCheckedChange($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"checked\", ctx.nzChecked)(\"disabled\", ctx.nzDisabled)(\"indeterminate\", ctx.nzIndeterminate)(\"label\", ctx.nzLabel)(\"listOfSelections\", ctx.nzSelections)(\"showCheckbox\", ctx.nzShowCheckbox)(\"showRowSelection\", ctx.nzShowRowSelection);\n      }\n    },\n    dependencies: [NzTableSelectionComponent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzThSelectionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'th[nzSelections],th[nzChecked],th[nzShowCheckbox],th[nzShowRowSelection]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <nz-table-selection\n      [checked]=\"nzChecked\"\n      [disabled]=\"nzDisabled\"\n      [indeterminate]=\"nzIndeterminate\"\n      [label]=\"nzLabel\"\n      [listOfSelections]=\"nzSelections\"\n      [showCheckbox]=\"nzShowCheckbox\"\n      [showRowSelection]=\"nzShowRowSelection\"\n      (checkedChange)=\"onCheckedChange($event)\"\n    ></nz-table-selection>\n    <ng-content></ng-content>\n  `,\n      host: {\n        class: 'ant-table-selection-column'\n      },\n      imports: [NzTableSelectionComponent]\n    }]\n  }], null, {\n    nzSelections: [{\n      type: Input\n    }],\n    nzChecked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzIndeterminate: [{\n      type: Input\n    }],\n    nzLabel: [{\n      type: Input\n    }],\n    nzShowCheckbox: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzShowRowSelection: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCellAlignDirective {\n  nzAlign = null;\n  static ɵfac = function NzCellAlignDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzCellAlignDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzCellAlignDirective,\n    selectors: [[\"th\", \"nzAlign\", \"\"], [\"td\", \"nzAlign\", \"\"]],\n    hostVars: 2,\n    hostBindings: function NzCellAlignDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"text-align\", ctx.nzAlign);\n      }\n    },\n    inputs: {\n      nzAlign: \"nzAlign\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellAlignDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th[nzAlign],td[nzAlign]',\n      host: {\n        '[style.text-align]': 'nzAlign'\n      }\n    }]\n  }], null, {\n    nzAlign: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCellEllipsisDirective {\n  nzEllipsis = true;\n  static ɵfac = function NzCellEllipsisDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzCellEllipsisDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzCellEllipsisDirective,\n    selectors: [[\"th\", \"nzEllipsis\", \"\"], [\"td\", \"nzEllipsis\", \"\"]],\n    hostVars: 2,\n    hostBindings: function NzCellEllipsisDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-table-cell-ellipsis\", ctx.nzEllipsis);\n      }\n    },\n    inputs: {\n      nzEllipsis: [2, \"nzEllipsis\", \"nzEllipsis\", booleanAttribute]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellEllipsisDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th[nzEllipsis],td[nzEllipsis]',\n      host: {\n        '[class.ant-table-cell-ellipsis]': 'nzEllipsis'\n      }\n    }]\n  }], null, {\n    nzEllipsis: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCellBreakWordDirective {\n  nzBreakWord = true;\n  static ɵfac = function NzCellBreakWordDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzCellBreakWordDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzCellBreakWordDirective,\n    selectors: [[\"th\", \"nzBreakWord\", \"\"], [\"td\", \"nzBreakWord\", \"\"]],\n    hostVars: 2,\n    hostBindings: function NzCellBreakWordDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"word-break\", ctx.nzBreakWord ? \"break-all\" : \"\");\n      }\n    },\n    inputs: {\n      nzBreakWord: [2, \"nzBreakWord\", \"nzBreakWord\", booleanAttribute]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellBreakWordDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th[nzBreakWord],td[nzBreakWord]',\n      host: {\n        '[style.word-break]': `nzBreakWord ? 'break-all' : ''`\n      }\n    }]\n  }], null, {\n    nzBreakWord: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableContentComponent {\n  tableLayout = 'auto';\n  theadTemplate = null;\n  contentTemplate = null;\n  tfootTemplate = null;\n  listOfColWidth = [];\n  scrollX = null;\n  static ɵfac = function NzTableContentComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableContentComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTableContentComponent,\n    selectors: [[\"table\", \"nz-table-content\", \"\"]],\n    hostVars: 8,\n    hostBindings: function NzTableContentComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"table-layout\", ctx.tableLayout)(\"width\", ctx.scrollX)(\"min-width\", ctx.scrollX ? \"100%\" : null);\n        i0.ɵɵclassProp(\"ant-table-fixed\", ctx.scrollX);\n      }\n    },\n    inputs: {\n      tableLayout: \"tableLayout\",\n      theadTemplate: \"theadTemplate\",\n      contentTemplate: \"contentTemplate\",\n      tfootTemplate: \"tfootTemplate\",\n      listOfColWidth: \"listOfColWidth\",\n      scrollX: \"scrollX\"\n    },\n    attrs: _c6,\n    ngContentSelectors: _c0,\n    decls: 5,\n    vars: 4,\n    consts: [[1, \"ant-table-thead\"], [3, \"ngTemplateOutlet\"], [1, \"ant-table-summary\"], [3, \"width\", \"minWidth\"]],\n    template: function NzTableContentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NzTableContentComponent_Conditional_0_Template, 3, 0, \"colgroup\")(1, NzTableContentComponent_Conditional_1_Template, 2, 1, \"thead\", 0)(2, NzTableContentComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n        i0.ɵɵprojection(3);\n        i0.ɵɵtemplate(4, NzTableContentComponent_Conditional_4_Template, 2, 1, \"tfoot\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.listOfColWidth.length > 0 ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.theadTemplate ? 1 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx.tfootTemplate ? 4 : -1);\n      }\n    },\n    dependencies: [NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableContentComponent, [{\n    type: Component,\n    args: [{\n      selector: 'table[nz-table-content]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @if (listOfColWidth.length > 0) {\n      <colgroup>\n        @for (width of listOfColWidth; track $index) {\n          <col [style.width]=\"width\" [style.minWidth]=\"width\" />\n        }\n      </colgroup>\n    }\n    @if (theadTemplate) {\n      <thead class=\"ant-table-thead\">\n        <ng-template [ngTemplateOutlet]=\"theadTemplate\"></ng-template>\n      </thead>\n    }\n    <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    <ng-content></ng-content>\n    @if (tfootTemplate) {\n      <tfoot class=\"ant-table-summary\">\n        <ng-template [ngTemplateOutlet]=\"tfootTemplate\"></ng-template>\n      </tfoot>\n    }\n  `,\n      host: {\n        '[style.table-layout]': 'tableLayout',\n        '[class.ant-table-fixed]': 'scrollX',\n        '[style.width]': 'scrollX',\n        '[style.min-width]': `scrollX ? '100%' : null`\n      },\n      imports: [NgTemplateOutlet]\n    }]\n  }], null, {\n    tableLayout: [{\n      type: Input\n    }],\n    theadTemplate: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }],\n    tfootTemplate: [{\n      type: Input\n    }],\n    listOfColWidth: [{\n      type: Input\n    }],\n    scrollX: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableFixedRowComponent {\n  nzTableStyleService;\n  renderer;\n  tdElement;\n  hostWidth$ = new BehaviorSubject(null);\n  enableAutoMeasure$ = new BehaviorSubject(false);\n  destroy$ = new Subject();\n  constructor(nzTableStyleService, renderer) {\n    this.nzTableStyleService = nzTableStyleService;\n    this.renderer = renderer;\n  }\n  ngOnInit() {\n    if (this.nzTableStyleService) {\n      const {\n        enableAutoMeasure$,\n        hostWidth$\n      } = this.nzTableStyleService;\n      enableAutoMeasure$.pipe(takeUntil(this.destroy$)).subscribe(this.enableAutoMeasure$);\n      hostWidth$.pipe(takeUntil(this.destroy$)).subscribe(this.hostWidth$);\n    }\n  }\n  ngAfterViewInit() {\n    this.nzTableStyleService.columnCount$.pipe(takeUntil(this.destroy$)).subscribe(count => {\n      this.renderer.setAttribute(this.tdElement.nativeElement, 'colspan', `${count}`);\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzTableFixedRowComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableFixedRowComponent)(i0.ɵɵdirectiveInject(NzTableStyleService), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTableFixedRowComponent,\n    selectors: [[\"tr\", \"nz-table-fixed-row\", \"\"], [\"tr\", \"nzExpand\", \"\"]],\n    viewQuery: function NzTableFixedRowComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c7, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tdElement = _t.first);\n      }\n    },\n    attrs: _c8,\n    ngContentSelectors: _c0,\n    decls: 7,\n    vars: 3,\n    consts: [[\"tdElement\", \"\"], [\"contentTemplate\", \"\"], [1, \"nz-disable-td\", \"ant-table-cell\"], [1, \"ant-table-expanded-row-fixed\", 2, \"position\", \"sticky\", \"left\", \"0\", \"overflow\", \"hidden\", 3, \"width\"], [3, \"ngTemplateOutlet\"], [1, \"ant-table-expanded-row-fixed\", 2, \"position\", \"sticky\", \"left\", \"0\", \"overflow\", \"hidden\"]],\n    template: function NzTableFixedRowComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"td\", 2, 0);\n        i0.ɵɵtemplate(2, NzTableFixedRowComponent_Conditional_2_Template, 3, 5, \"div\", 3);\n        i0.ɵɵpipe(3, \"async\");\n        i0.ɵɵtemplate(4, NzTableFixedRowComponent_Conditional_4_Template, 1, 1, null, 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, NzTableFixedRowComponent_ng_template_5_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(i0.ɵɵpipeBind1(3, 1, ctx.enableAutoMeasure$) ? 2 : 4);\n      }\n    },\n    dependencies: [AsyncPipe, NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableFixedRowComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tr[nz-table-fixed-row], tr[nzExpand]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <td class=\"nz-disable-td ant-table-cell\" #tdElement>\n      @if (enableAutoMeasure$ | async) {\n        <div\n          class=\"ant-table-expanded-row-fixed\"\n          style=\"position: sticky; left: 0; overflow: hidden;\"\n          [style.width.px]=\"hostWidth$ | async\"\n        >\n          <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n        </div>\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n      }\n    </td>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      imports: [AsyncPipe, NgTemplateOutlet]\n    }]\n  }], () => [{\n    type: NzTableStyleService\n  }, {\n    type: i0.Renderer2\n  }], {\n    tdElement: [{\n      type: ViewChild,\n      args: ['tdElement', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableInnerDefaultComponent {\n  tableLayout = 'auto';\n  listOfColWidth = [];\n  theadTemplate = null;\n  contentTemplate = null;\n  tfootTemplate = null;\n  static ɵfac = function NzTableInnerDefaultComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableInnerDefaultComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTableInnerDefaultComponent,\n    selectors: [[\"nz-table-inner-default\"]],\n    hostAttrs: [1, \"ant-table-container\"],\n    inputs: {\n      tableLayout: \"tableLayout\",\n      listOfColWidth: \"listOfColWidth\",\n      theadTemplate: \"theadTemplate\",\n      contentTemplate: \"contentTemplate\",\n      tfootTemplate: \"tfootTemplate\"\n    },\n    decls: 2,\n    vars: 5,\n    consts: [[1, \"ant-table-content\"], [\"nz-table-content\", \"\", 3, \"contentTemplate\", \"tableLayout\", \"listOfColWidth\", \"theadTemplate\", \"tfootTemplate\"]],\n    template: function NzTableInnerDefaultComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"table\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"contentTemplate\", ctx.contentTemplate)(\"tableLayout\", ctx.tableLayout)(\"listOfColWidth\", ctx.listOfColWidth)(\"theadTemplate\", ctx.theadTemplate)(\"tfootTemplate\", ctx.tfootTemplate);\n      }\n    },\n    dependencies: [NzTableContentComponent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableInnerDefaultComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-inner-default',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div class=\"ant-table-content\">\n      <table\n        nz-table-content\n        [contentTemplate]=\"contentTemplate\"\n        [tableLayout]=\"tableLayout\"\n        [listOfColWidth]=\"listOfColWidth\"\n        [theadTemplate]=\"theadTemplate\"\n        [tfootTemplate]=\"tfootTemplate\"\n      ></table>\n    </div>\n  `,\n      host: {\n        class: 'ant-table-container'\n      },\n      imports: [NzTableContentComponent]\n    }]\n  }], null, {\n    tableLayout: [{\n      type: Input\n    }],\n    listOfColWidth: [{\n      type: Input\n    }],\n    theadTemplate: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }],\n    tfootTemplate: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrMeasureComponent {\n  nzResizeObserver;\n  ngZone;\n  listOfMeasureColumn = [];\n  listOfAutoWidth = new EventEmitter();\n  listOfTdElement;\n  destroy$ = new Subject();\n  constructor(nzResizeObserver, ngZone) {\n    this.nzResizeObserver = nzResizeObserver;\n    this.ngZone = ngZone;\n  }\n  ngAfterViewInit() {\n    this.listOfTdElement.changes.pipe(startWith(this.listOfTdElement)).pipe(switchMap(list => combineLatest(list.toArray().map(item => this.nzResizeObserver.observe(item).pipe(map(([entry]) => {\n      const {\n        width\n      } = entry.target.getBoundingClientRect();\n      return Math.floor(width);\n    }))))), debounceTime(16), takeUntil(this.destroy$)).subscribe(data => {\n      // Caretaker note: we don't have to re-enter the Angular zone each time the stream emits.\n      // The below check is necessary to be sure that zone is not nooped through `BootstrapOptions`\n      // (`bootstrapModule(AppModule, { ngZone: 'noop' }))`. The `ngZone instanceof NgZone` may return\n      // `false` if zone is nooped, since `ngZone` will be an instance of the `NoopNgZone`.\n      // The `ResizeObserver` might be also patched through `zone.js/dist/zone-patch-resize-observer`,\n      // thus calling `ngZone.run` again will cause another change detection.\n      if (this.ngZone instanceof NgZone && NgZone.isInAngularZone()) {\n        this.listOfAutoWidth.next(data);\n      } else {\n        this.ngZone.run(() => this.listOfAutoWidth.next(data));\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzTrMeasureComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTrMeasureComponent)(i0.ɵɵdirectiveInject(i1$2.NzResizeObserver), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTrMeasureComponent,\n    selectors: [[\"tr\", \"nz-table-measure-row\", \"\"]],\n    viewQuery: function NzTrMeasureComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c7, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTdElement = _t);\n      }\n    },\n    hostAttrs: [1, \"ant-table-measure-now\"],\n    inputs: {\n      listOfMeasureColumn: \"listOfMeasureColumn\"\n    },\n    outputs: {\n      listOfAutoWidth: \"listOfAutoWidth\"\n    },\n    attrs: _c9,\n    decls: 2,\n    vars: 0,\n    consts: [[\"tdElement\", \"\"], [1, \"nz-disable-td\", 2, \"padding\", \"0\", \"border\", \"0\", \"height\", \"0\"]],\n    template: function NzTrMeasureComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵrepeaterCreate(0, NzTrMeasureComponent_For_1_Template, 2, 0, \"td\", 1, i0.ɵɵrepeaterTrackByIndex);\n      }\n      if (rf & 2) {\n        i0.ɵɵrepeater(ctx.listOfMeasureColumn);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrMeasureComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tr[nz-table-measure-row]',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @for (th of listOfMeasureColumn; track $index) {\n      <td #tdElement class=\"nz-disable-td\" style=\"padding: 0; border: 0; height: 0;\"></td>\n    }\n  `,\n      host: {\n        class: 'ant-table-measure-now'\n      }\n    }]\n  }], () => [{\n    type: i1$2.NzResizeObserver\n  }, {\n    type: i0.NgZone\n  }], {\n    listOfMeasureColumn: [{\n      type: Input\n    }],\n    listOfAutoWidth: [{\n      type: Output\n    }],\n    listOfTdElement: [{\n      type: ViewChildren,\n      args: ['tdElement']\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTbodyComponent {\n  isInsideTable = false;\n  showEmpty$ = new BehaviorSubject(false);\n  noResult$ = new BehaviorSubject(undefined);\n  listOfMeasureColumn$ = new BehaviorSubject([]);\n  destroy$ = new Subject();\n  nzTableStyleService = inject(NzTableStyleService, {\n    optional: true\n  });\n  constructor() {\n    this.isInsideTable = !!this.nzTableStyleService;\n    if (this.nzTableStyleService) {\n      const {\n        showEmpty$,\n        noResult$,\n        listOfMeasureColumn$\n      } = this.nzTableStyleService;\n      noResult$.pipe(takeUntil(this.destroy$)).subscribe(this.noResult$);\n      listOfMeasureColumn$.pipe(takeUntil(this.destroy$)).subscribe(this.listOfMeasureColumn$);\n      showEmpty$.pipe(takeUntil(this.destroy$)).subscribe(this.showEmpty$);\n    }\n  }\n  onListOfAutoWidthChange(listOfAutoWidth) {\n    this.nzTableStyleService?.setListOfAutoWidth(listOfAutoWidth);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzTbodyComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTbodyComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTbodyComponent,\n    selectors: [[\"tbody\"]],\n    hostVars: 2,\n    hostBindings: function NzTbodyComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-table-tbody\", ctx.isInsideTable);\n      }\n    },\n    ngContentSelectors: _c0,\n    decls: 5,\n    vars: 6,\n    consts: [[\"nz-table-fixed-row\", \"\", 1, \"ant-table-placeholder\"], [\"nz-table-measure-row\", \"\", 3, \"listOfMeasureColumn\"], [\"nz-table-measure-row\", \"\", 3, \"listOfAutoWidth\", \"listOfMeasureColumn\"], [\"nzComponentName\", \"table\", 3, \"specificContent\"]],\n    template: function NzTbodyComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NzTbodyComponent_Conditional_0_Template, 1, 1);\n        i0.ɵɵpipe(1, \"async\");\n        i0.ɵɵprojection(2);\n        i0.ɵɵtemplate(3, NzTbodyComponent_Conditional_3_Template, 3, 3, \"tr\", 0);\n        i0.ɵɵpipe(4, \"async\");\n      }\n      if (rf & 2) {\n        let tmp_0_0;\n        i0.ɵɵconditional((tmp_0_0 = i0.ɵɵpipeBind1(1, 2, ctx.listOfMeasureColumn$)) ? 0 : -1, tmp_0_0);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(i0.ɵɵpipeBind1(4, 4, ctx.showEmpty$) ? 3 : -1);\n      }\n    },\n    dependencies: [AsyncPipe, NzTrMeasureComponent, NzTableFixedRowComponent, NzEmptyModule, i1$3.NzEmbedEmptyComponent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTbodyComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tbody',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @if (listOfMeasureColumn$ | async; as listOfMeasureColumn) {\n      @if (isInsideTable && listOfMeasureColumn.length) {\n        <tr\n          nz-table-measure-row\n          [listOfMeasureColumn]=\"listOfMeasureColumn\"\n          (listOfAutoWidth)=\"onListOfAutoWidthChange($event)\"\n        ></tr>\n      }\n    }\n    <ng-content></ng-content>\n    @if (showEmpty$ | async) {\n      <tr class=\"ant-table-placeholder\" nz-table-fixed-row>\n        <nz-embed-empty nzComponentName=\"table\" [specificContent]=\"(noResult$ | async)!\"></nz-embed-empty>\n      </tr>\n    }\n  `,\n      host: {\n        '[class.ant-table-tbody]': 'isInsideTable'\n      },\n      imports: [AsyncPipe, NzTrMeasureComponent, NzTableFixedRowComponent, NzEmptyModule]\n    }]\n  }], () => [], null);\n})();\nclass NzTableInnerScrollComponent {\n  renderer;\n  ngZone;\n  platform;\n  resizeService;\n  data = [];\n  scrollX = null;\n  scrollY = null;\n  contentTemplate = null;\n  widthConfig = [];\n  listOfColWidth = [];\n  theadTemplate = null;\n  tfootTemplate = null;\n  tfootFixed = null;\n  virtualTemplate = null;\n  virtualItemSize = 0;\n  virtualMaxBufferPx = 200;\n  virtualMinBufferPx = 100;\n  tableMainElement;\n  virtualForTrackBy = index => index;\n  tableHeaderElement;\n  tableBodyElement;\n  tableFootElement;\n  cdkVirtualScrollViewport;\n  headerStyleMap = {};\n  bodyStyleMap = {};\n  verticalScrollBarWidth = 0;\n  noDataVirtualHeight = '182px';\n  data$ = new Subject();\n  scroll$ = new Subject();\n  destroy$ = new Subject();\n  setScrollPositionClassName(clear = false) {\n    const {\n      scrollWidth,\n      scrollLeft,\n      clientWidth\n    } = this.tableBodyElement.nativeElement;\n    const leftClassName = 'ant-table-ping-left';\n    const rightClassName = 'ant-table-ping-right';\n    if (scrollWidth === clientWidth && scrollWidth !== 0 || clear) {\n      this.renderer.removeClass(this.tableMainElement, leftClassName);\n      this.renderer.removeClass(this.tableMainElement, rightClassName);\n    } else if (scrollLeft === 0) {\n      this.renderer.removeClass(this.tableMainElement, leftClassName);\n      this.renderer.addClass(this.tableMainElement, rightClassName);\n    } else if (scrollWidth === scrollLeft + clientWidth) {\n      this.renderer.removeClass(this.tableMainElement, rightClassName);\n      this.renderer.addClass(this.tableMainElement, leftClassName);\n    } else {\n      this.renderer.addClass(this.tableMainElement, leftClassName);\n      this.renderer.addClass(this.tableMainElement, rightClassName);\n    }\n  }\n  constructor(renderer, ngZone, platform, resizeService) {\n    this.renderer = renderer;\n    this.ngZone = ngZone;\n    this.platform = platform;\n    this.resizeService = resizeService;\n  }\n  ngOnChanges(changes) {\n    const {\n      scrollX,\n      scrollY,\n      data\n    } = changes;\n    if (scrollX || scrollY) {\n      const hasVerticalScrollBar = this.verticalScrollBarWidth !== 0;\n      this.headerStyleMap = {\n        overflowX: 'hidden',\n        overflowY: this.scrollY && hasVerticalScrollBar ? 'scroll' : 'hidden'\n      };\n      this.bodyStyleMap = {\n        overflowY: this.scrollY ? 'scroll' : 'hidden',\n        overflowX: this.scrollX ? 'auto' : null,\n        maxHeight: this.scrollY\n      };\n      // Caretaker note: we have to emit the value outside the Angular zone, thus DOM timer (`delay(0)`) and `scroll`\n      // event listener will be also added outside the Angular zone.\n      this.ngZone.runOutsideAngular(() => this.scroll$.next());\n    }\n    if (data) {\n      // See the comment above.\n      this.ngZone.runOutsideAngular(() => this.data$.next());\n    }\n  }\n  ngAfterViewInit() {\n    if (this.platform.isBrowser) {\n      this.ngZone.runOutsideAngular(() => {\n        const scrollEvent$ = this.scroll$.pipe(startWith(null), delay(0), switchMap(() => fromEventOutsideAngular(this.tableBodyElement.nativeElement, 'scroll').pipe(startWith(true))), takeUntil(this.destroy$));\n        const resize$ = this.resizeService.subscribe().pipe(takeUntil(this.destroy$));\n        const data$ = this.data$.pipe(takeUntil(this.destroy$));\n        const setClassName$ = merge(scrollEvent$, resize$, data$, this.scroll$).pipe(startWith(true), delay(0), takeUntil(this.destroy$));\n        setClassName$.subscribe(() => this.setScrollPositionClassName());\n        scrollEvent$.pipe(filter(() => !!this.scrollY)).subscribe(() => {\n          this.tableHeaderElement.nativeElement.scrollLeft = this.tableBodyElement.nativeElement.scrollLeft;\n          if (this.tableFootElement) {\n            this.tableFootElement.nativeElement.scrollLeft = this.tableBodyElement.nativeElement.scrollLeft;\n          }\n        });\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.setScrollPositionClassName(true);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzTableInnerScrollComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableInnerScrollComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$4.Platform), i0.ɵɵdirectiveInject(i2.NzResizeService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTableInnerScrollComponent,\n    selectors: [[\"nz-table-inner-scroll\"]],\n    viewQuery: function NzTableInnerScrollComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c10, 5, ElementRef);\n        i0.ɵɵviewQuery(_c11, 5, ElementRef);\n        i0.ɵɵviewQuery(_c12, 5, ElementRef);\n        i0.ɵɵviewQuery(CdkVirtualScrollViewport, 5, CdkVirtualScrollViewport);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableHeaderElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableBodyElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableFootElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkVirtualScrollViewport = _t.first);\n      }\n    },\n    hostAttrs: [1, \"ant-table-container\"],\n    inputs: {\n      data: \"data\",\n      scrollX: \"scrollX\",\n      scrollY: \"scrollY\",\n      contentTemplate: \"contentTemplate\",\n      widthConfig: \"widthConfig\",\n      listOfColWidth: \"listOfColWidth\",\n      theadTemplate: \"theadTemplate\",\n      tfootTemplate: \"tfootTemplate\",\n      tfootFixed: \"tfootFixed\",\n      virtualTemplate: \"virtualTemplate\",\n      virtualItemSize: \"virtualItemSize\",\n      virtualMaxBufferPx: \"virtualMaxBufferPx\",\n      virtualMinBufferPx: \"virtualMinBufferPx\",\n      tableMainElement: \"tableMainElement\",\n      virtualForTrackBy: \"virtualForTrackBy\",\n      verticalScrollBarWidth: \"verticalScrollBarWidth\",\n      noDataVirtualHeight: \"noDataVirtualHeight\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 2,\n    vars: 1,\n    consts: [[\"tableHeaderElement\", \"\"], [\"tableBodyElement\", \"\"], [\"tableFootElement\", \"\"], [1, \"ant-table-content\", 3, \"style\"], [1, \"ant-table-header\", \"nz-table-hide-scrollbar\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\", \"theadTemplate\", \"tfootTemplate\"], [1, \"ant-table-body\", 3, \"style\"], [3, \"itemSize\", \"maxBufferPx\", \"minBufferPx\", \"height\"], [1, \"ant-table-summary\", 3, \"style\"], [1, \"ant-table-body\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\", \"contentTemplate\"], [3, \"itemSize\", \"maxBufferPx\", \"minBufferPx\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\"], [4, \"cdkVirtualFor\", \"cdkVirtualForOf\", \"cdkVirtualForTrackBy\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"ant-table-summary\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\", \"tfootTemplate\"], [1, \"ant-table-content\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\", \"theadTemplate\", \"contentTemplate\", \"tfootTemplate\"]],\n    template: function NzTableInnerScrollComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzTableInnerScrollComponent_Conditional_0_Template, 6, 8)(1, NzTableInnerScrollComponent_Conditional_1_Template, 3, 7, \"div\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.scrollY ? 0 : 1);\n      }\n    },\n    dependencies: [NzTableContentComponent, ScrollingModule, i3$1.CdkFixedSizeVirtualScroll, i3$1.CdkVirtualForOf, i3$1.CdkVirtualScrollViewport, NgTemplateOutlet, NzTbodyComponent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableInnerScrollComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-inner-scroll',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @if (scrollY) {\n      <div #tableHeaderElement [style]=\"headerStyleMap\" class=\"ant-table-header nz-table-hide-scrollbar\">\n        <table\n          nz-table-content\n          tableLayout=\"fixed\"\n          [scrollX]=\"scrollX\"\n          [listOfColWidth]=\"listOfColWidth\"\n          [theadTemplate]=\"theadTemplate\"\n          [tfootTemplate]=\"tfootFixed === 'top' ? tfootTemplate : null\"\n        ></table>\n      </div>\n      @if (!virtualTemplate) {\n        <div #tableBodyElement class=\"ant-table-body\" [style]=\"bodyStyleMap\">\n          <table\n            nz-table-content\n            tableLayout=\"fixed\"\n            [scrollX]=\"scrollX\"\n            [listOfColWidth]=\"listOfColWidth\"\n            [contentTemplate]=\"contentTemplate\"\n          ></table>\n        </div>\n      } @else {\n        <cdk-virtual-scroll-viewport\n          #tableBodyElement\n          [itemSize]=\"virtualItemSize\"\n          [maxBufferPx]=\"virtualMaxBufferPx\"\n          [minBufferPx]=\"virtualMinBufferPx\"\n          [style.height]=\"data.length ? scrollY : noDataVirtualHeight\"\n        >\n          <table nz-table-content tableLayout=\"fixed\" [scrollX]=\"scrollX\" [listOfColWidth]=\"listOfColWidth\">\n            <tbody>\n              <ng-container *cdkVirtualFor=\"let item of data; let i = index; trackBy: virtualForTrackBy\">\n                <ng-template\n                  [ngTemplateOutlet]=\"virtualTemplate\"\n                  [ngTemplateOutletContext]=\"{ $implicit: item, index: i }\"\n                ></ng-template>\n              </ng-container>\n            </tbody>\n          </table>\n        </cdk-virtual-scroll-viewport>\n      }\n      @if (tfootFixed === 'bottom') {\n        <div #tableFootElement class=\"ant-table-summary\" [style]=\"headerStyleMap\">\n          <table\n            nz-table-content\n            tableLayout=\"fixed\"\n            [scrollX]=\"scrollX\"\n            [listOfColWidth]=\"listOfColWidth\"\n            [tfootTemplate]=\"tfootTemplate\"\n          ></table>\n        </div>\n      }\n    } @else {\n      <div class=\"ant-table-content\" #tableBodyElement [style]=\"bodyStyleMap\">\n        <table\n          nz-table-content\n          tableLayout=\"fixed\"\n          [scrollX]=\"scrollX\"\n          [listOfColWidth]=\"listOfColWidth\"\n          [theadTemplate]=\"theadTemplate\"\n          [contentTemplate]=\"contentTemplate\"\n          [tfootTemplate]=\"tfootTemplate\"\n        ></table>\n      </div>\n    }\n  `,\n      host: {\n        class: 'ant-table-container'\n      },\n      imports: [NzTableContentComponent, ScrollingModule, NgTemplateOutlet, NzTbodyComponent]\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$4.Platform\n  }, {\n    type: i2.NzResizeService\n  }], {\n    data: [{\n      type: Input\n    }],\n    scrollX: [{\n      type: Input\n    }],\n    scrollY: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }],\n    widthConfig: [{\n      type: Input\n    }],\n    listOfColWidth: [{\n      type: Input\n    }],\n    theadTemplate: [{\n      type: Input\n    }],\n    tfootTemplate: [{\n      type: Input\n    }],\n    tfootFixed: [{\n      type: Input\n    }],\n    virtualTemplate: [{\n      type: Input\n    }],\n    virtualItemSize: [{\n      type: Input\n    }],\n    virtualMaxBufferPx: [{\n      type: Input\n    }],\n    virtualMinBufferPx: [{\n      type: Input\n    }],\n    tableMainElement: [{\n      type: Input\n    }],\n    virtualForTrackBy: [{\n      type: Input\n    }],\n    tableHeaderElement: [{\n      type: ViewChild,\n      args: ['tableHeaderElement', {\n        read: ElementRef\n      }]\n    }],\n    tableBodyElement: [{\n      type: ViewChild,\n      args: ['tableBodyElement', {\n        read: ElementRef\n      }]\n    }],\n    tableFootElement: [{\n      type: ViewChild,\n      args: ['tableFootElement', {\n        read: ElementRef\n      }]\n    }],\n    cdkVirtualScrollViewport: [{\n      type: ViewChild,\n      args: [CdkVirtualScrollViewport, {\n        read: CdkVirtualScrollViewport\n      }]\n    }],\n    verticalScrollBarWidth: [{\n      type: Input\n    }],\n    noDataVirtualHeight: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableVirtualScrollDirective {\n  templateRef;\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n  static ngTemplateContextGuard(_dir, _ctx) {\n    return true;\n  }\n  static ɵfac = function NzTableVirtualScrollDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableVirtualScrollDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTableVirtualScrollDirective,\n    selectors: [[\"\", \"nz-virtual-scroll\", \"\"]],\n    exportAs: [\"nzVirtualScroll\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableVirtualScrollDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-virtual-scroll]',\n      exportAs: 'nzVirtualScroll'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableTitleFooterComponent {\n  title = null;\n  footer = null;\n  static ɵfac = function NzTableTitleFooterComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableTitleFooterComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTableTitleFooterComponent,\n    selectors: [[\"nz-table-title-footer\"]],\n    hostVars: 4,\n    hostBindings: function NzTableTitleFooterComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-table-title\", ctx.title !== null)(\"ant-table-footer\", ctx.footer !== null);\n      }\n    },\n    inputs: {\n      title: \"title\",\n      footer: \"footer\"\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[4, \"nzStringTemplateOutlet\"]],\n    template: function NzTableTitleFooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzTableTitleFooterComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0)(1, NzTableTitleFooterComponent_ng_container_1_Template, 2, 1, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.title);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.footer);\n      }\n    },\n    dependencies: [NzOutletModule, i1$5.NzStringTemplateOutletDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableTitleFooterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-title-footer',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-container *nzStringTemplateOutlet=\"title\">{{ title }}</ng-container>\n    <ng-container *nzStringTemplateOutlet=\"footer\">{{ footer }}</ng-container>\n  `,\n      host: {\n        '[class.ant-table-title]': `title !== null`,\n        '[class.ant-table-footer]': `footer !== null`\n      },\n      imports: [NzOutletModule]\n    }]\n  }], null, {\n    title: [{\n      type: Input\n    }],\n    footer: [{\n      type: Input\n    }]\n  });\n})();\nconst NZ_CONFIG_MODULE_NAME = 'table';\nlet NzTableComponent = (() => {\n  let _nzLoadingIndicator_decorators;\n  let _nzLoadingIndicator_initializers = [];\n  let _nzLoadingIndicator_extraInitializers = [];\n  let _nzBordered_decorators;\n  let _nzBordered_initializers = [];\n  let _nzBordered_extraInitializers = [];\n  let _nzSize_decorators;\n  let _nzSize_initializers = [];\n  let _nzSize_extraInitializers = [];\n  let _nzShowSizeChanger_decorators;\n  let _nzShowSizeChanger_initializers = [];\n  let _nzShowSizeChanger_extraInitializers = [];\n  let _nzHideOnSinglePage_decorators;\n  let _nzHideOnSinglePage_initializers = [];\n  let _nzHideOnSinglePage_extraInitializers = [];\n  let _nzShowQuickJumper_decorators;\n  let _nzShowQuickJumper_initializers = [];\n  let _nzShowQuickJumper_extraInitializers = [];\n  let _nzSimple_decorators;\n  let _nzSimple_initializers = [];\n  let _nzSimple_extraInitializers = [];\n  return class NzTableComponent {\n    static {\n      const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(null) : void 0;\n      _nzLoadingIndicator_decorators = [WithConfig()];\n      _nzBordered_decorators = [WithConfig()];\n      _nzSize_decorators = [WithConfig()];\n      _nzShowSizeChanger_decorators = [WithConfig()];\n      _nzHideOnSinglePage_decorators = [WithConfig()];\n      _nzShowQuickJumper_decorators = [WithConfig()];\n      _nzSimple_decorators = [WithConfig()];\n      __esDecorate(null, null, _nzLoadingIndicator_decorators, {\n        kind: \"field\",\n        name: \"nzLoadingIndicator\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzLoadingIndicator\" in obj,\n          get: obj => obj.nzLoadingIndicator,\n          set: (obj, value) => {\n            obj.nzLoadingIndicator = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzLoadingIndicator_initializers, _nzLoadingIndicator_extraInitializers);\n      __esDecorate(null, null, _nzBordered_decorators, {\n        kind: \"field\",\n        name: \"nzBordered\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzBordered\" in obj,\n          get: obj => obj.nzBordered,\n          set: (obj, value) => {\n            obj.nzBordered = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzBordered_initializers, _nzBordered_extraInitializers);\n      __esDecorate(null, null, _nzSize_decorators, {\n        kind: \"field\",\n        name: \"nzSize\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzSize\" in obj,\n          get: obj => obj.nzSize,\n          set: (obj, value) => {\n            obj.nzSize = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzSize_initializers, _nzSize_extraInitializers);\n      __esDecorate(null, null, _nzShowSizeChanger_decorators, {\n        kind: \"field\",\n        name: \"nzShowSizeChanger\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzShowSizeChanger\" in obj,\n          get: obj => obj.nzShowSizeChanger,\n          set: (obj, value) => {\n            obj.nzShowSizeChanger = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzShowSizeChanger_initializers, _nzShowSizeChanger_extraInitializers);\n      __esDecorate(null, null, _nzHideOnSinglePage_decorators, {\n        kind: \"field\",\n        name: \"nzHideOnSinglePage\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzHideOnSinglePage\" in obj,\n          get: obj => obj.nzHideOnSinglePage,\n          set: (obj, value) => {\n            obj.nzHideOnSinglePage = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzHideOnSinglePage_initializers, _nzHideOnSinglePage_extraInitializers);\n      __esDecorate(null, null, _nzShowQuickJumper_decorators, {\n        kind: \"field\",\n        name: \"nzShowQuickJumper\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzShowQuickJumper\" in obj,\n          get: obj => obj.nzShowQuickJumper,\n          set: (obj, value) => {\n            obj.nzShowQuickJumper = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzShowQuickJumper_initializers, _nzShowQuickJumper_extraInitializers);\n      __esDecorate(null, null, _nzSimple_decorators, {\n        kind: \"field\",\n        name: \"nzSimple\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzSimple\" in obj,\n          get: obj => obj.nzSimple,\n          set: (obj, value) => {\n            obj.nzSimple = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzSimple_initializers, _nzSimple_extraInitializers);\n      if (_metadata) Object.defineProperty(this, Symbol.metadata, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value: _metadata\n      });\n    }\n    elementRef;\n    nzResizeObserver;\n    nzConfigService;\n    cdr;\n    nzTableStyleService;\n    nzTableDataService;\n    directionality;\n    _nzModuleName = NZ_CONFIG_MODULE_NAME;\n    nzTableLayout = 'auto';\n    nzShowTotal = null;\n    nzItemRender = null;\n    nzTitle = null;\n    nzFooter = null;\n    nzNoResult = undefined;\n    nzPageSizeOptions = [10, 20, 30, 40, 50];\n    nzVirtualItemSize = 0;\n    nzVirtualMaxBufferPx = 200;\n    nzVirtualMinBufferPx = 100;\n    nzVirtualForTrackBy = index => index;\n    nzLoadingDelay = 0;\n    nzPageIndex = 1;\n    nzPageSize = 10;\n    nzTotal = 0;\n    nzWidthConfig = [];\n    nzData = [];\n    nzCustomColumn = [];\n    nzPaginationPosition = 'bottom';\n    nzScroll = {\n      x: null,\n      y: null\n    };\n    noDataVirtualHeight = '182px';\n    nzPaginationType = 'default';\n    nzFrontPagination = true;\n    nzTemplateMode = false;\n    nzShowPagination = true;\n    nzLoading = false;\n    nzOuterBordered = false;\n    nzLoadingIndicator = __runInitializers(this, _nzLoadingIndicator_initializers, null);\n    nzBordered = (__runInitializers(this, _nzLoadingIndicator_extraInitializers), __runInitializers(this, _nzBordered_initializers, false));\n    nzSize = (__runInitializers(this, _nzBordered_extraInitializers), __runInitializers(this, _nzSize_initializers, 'default'));\n    nzShowSizeChanger = (__runInitializers(this, _nzSize_extraInitializers), __runInitializers(this, _nzShowSizeChanger_initializers, false));\n    nzHideOnSinglePage = (__runInitializers(this, _nzShowSizeChanger_extraInitializers), __runInitializers(this, _nzHideOnSinglePage_initializers, false));\n    nzShowQuickJumper = (__runInitializers(this, _nzHideOnSinglePage_extraInitializers), __runInitializers(this, _nzShowQuickJumper_initializers, false));\n    nzSimple = (__runInitializers(this, _nzShowQuickJumper_extraInitializers), __runInitializers(this, _nzSimple_initializers, false));\n    nzPageSizeChange = (__runInitializers(this, _nzSimple_extraInitializers), new EventEmitter());\n    nzPageIndexChange = new EventEmitter();\n    nzQueryParams = new EventEmitter();\n    nzCurrentPageDataChange = new EventEmitter();\n    nzCustomColumnChange = new EventEmitter();\n    /** public data for ngFor tr */\n    data = [];\n    cdkVirtualScrollViewport;\n    scrollX = null;\n    scrollY = null;\n    theadTemplate = null;\n    tfootTemplate = null;\n    tfootFixed = null;\n    listOfAutoColWidth = [];\n    listOfManualColWidth = [];\n    hasFixLeft = false;\n    hasFixRight = false;\n    showPagination = true;\n    destroy$ = new Subject();\n    templateMode$ = new BehaviorSubject(false);\n    dir = 'ltr';\n    nzVirtualScrollDirective;\n    nzTableInnerScrollComponent;\n    verticalScrollBarWidth = 0;\n    onPageSizeChange(size) {\n      this.nzTableDataService.updatePageSize(size);\n    }\n    onPageIndexChange(index) {\n      this.nzTableDataService.updatePageIndex(index);\n    }\n    constructor(elementRef, nzResizeObserver, nzConfigService, cdr, nzTableStyleService, nzTableDataService, directionality) {\n      this.elementRef = elementRef;\n      this.nzResizeObserver = nzResizeObserver;\n      this.nzConfigService = nzConfigService;\n      this.cdr = cdr;\n      this.nzTableStyleService = nzTableStyleService;\n      this.nzTableDataService = nzTableDataService;\n      this.directionality = directionality;\n      this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.cdr.markForCheck();\n      });\n    }\n    ngOnInit() {\n      const {\n        pageIndexDistinct$,\n        pageSizeDistinct$,\n        listOfCurrentPageData$,\n        total$,\n        queryParams$,\n        listOfCustomColumn$\n      } = this.nzTableDataService;\n      const {\n        theadTemplate$,\n        tfootTemplate$,\n        tfootFixed$,\n        hasFixLeft$,\n        hasFixRight$\n      } = this.nzTableStyleService;\n      this.dir = this.directionality.value;\n      this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n        this.dir = direction;\n        this.cdr.detectChanges();\n      });\n      queryParams$.pipe(takeUntil(this.destroy$)).subscribe(this.nzQueryParams);\n      pageIndexDistinct$.pipe(takeUntil(this.destroy$)).subscribe(pageIndex => {\n        if (pageIndex !== this.nzPageIndex) {\n          this.nzPageIndex = pageIndex;\n          this.nzPageIndexChange.next(pageIndex);\n        }\n      });\n      pageSizeDistinct$.pipe(takeUntil(this.destroy$)).subscribe(pageSize => {\n        if (pageSize !== this.nzPageSize) {\n          this.nzPageSize = pageSize;\n          this.nzPageSizeChange.next(pageSize);\n        }\n      });\n      total$.pipe(takeUntil(this.destroy$), filter(() => this.nzFrontPagination)).subscribe(total => {\n        if (total !== this.nzTotal) {\n          this.nzTotal = total;\n          this.cdr.markForCheck();\n        }\n      });\n      listOfCurrentPageData$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n        this.data = data;\n        this.nzCurrentPageDataChange.next(data);\n        this.cdr.markForCheck();\n      });\n      listOfCustomColumn$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n        this.nzCustomColumn = data;\n        this.nzCustomColumnChange.next(data);\n        this.cdr.markForCheck();\n      });\n      theadTemplate$.pipe(takeUntil(this.destroy$)).subscribe(theadTemplate => {\n        this.theadTemplate = theadTemplate;\n        this.cdr.markForCheck();\n      });\n      combineLatest([tfootTemplate$, tfootFixed$]).pipe(takeUntil(this.destroy$)).subscribe(([tfootTemplate, tfootFixed]) => {\n        this.tfootTemplate = tfootTemplate;\n        this.tfootFixed = tfootFixed;\n        this.cdr.markForCheck();\n      });\n      hasFixLeft$.pipe(takeUntil(this.destroy$)).subscribe(hasFixLeft => {\n        this.hasFixLeft = hasFixLeft;\n        this.cdr.markForCheck();\n      });\n      hasFixRight$.pipe(takeUntil(this.destroy$)).subscribe(hasFixRight => {\n        this.hasFixRight = hasFixRight;\n        this.cdr.markForCheck();\n      });\n      combineLatest([total$, this.templateMode$]).pipe(map(([total, templateMode]) => total === 0 && !templateMode), takeUntil(this.destroy$)).subscribe(empty => {\n        this.nzTableStyleService.setShowEmpty(empty);\n      });\n      this.verticalScrollBarWidth = measureScrollbar('vertical');\n      this.nzTableStyleService.listOfListOfThWidthPx$.pipe(takeUntil(this.destroy$)).subscribe(listOfWidth => {\n        this.listOfAutoColWidth = listOfWidth;\n        this.cdr.markForCheck();\n      });\n      this.nzTableStyleService.manualWidthConfigPx$.pipe(takeUntil(this.destroy$)).subscribe(listOfWidth => {\n        this.listOfManualColWidth = listOfWidth;\n        this.cdr.markForCheck();\n      });\n    }\n    ngOnChanges(changes) {\n      const {\n        nzScroll,\n        nzPageIndex,\n        nzPageSize,\n        nzFrontPagination,\n        nzData,\n        nzCustomColumn,\n        nzWidthConfig,\n        nzNoResult,\n        nzTemplateMode\n      } = changes;\n      if (nzPageIndex) {\n        this.nzTableDataService.updatePageIndex(this.nzPageIndex);\n      }\n      if (nzPageSize) {\n        this.nzTableDataService.updatePageSize(this.nzPageSize);\n      }\n      if (nzData) {\n        this.nzData = this.nzData || [];\n        this.nzTableDataService.updateListOfData(this.nzData);\n      }\n      if (nzCustomColumn) {\n        this.nzCustomColumn = this.nzCustomColumn || [];\n        this.nzTableDataService.updateListOfCustomColumn(this.nzCustomColumn);\n      }\n      if (nzFrontPagination) {\n        this.nzTableDataService.updateFrontPagination(this.nzFrontPagination);\n      }\n      if (nzScroll) {\n        this.setScrollOnChanges();\n      }\n      if (nzWidthConfig) {\n        this.nzTableStyleService.setTableWidthConfig(this.nzWidthConfig);\n      }\n      if (nzTemplateMode) {\n        this.templateMode$.next(this.nzTemplateMode);\n      }\n      if (nzNoResult) {\n        this.nzTableStyleService.setNoResult(this.nzNoResult);\n      }\n      this.updateShowPagination();\n    }\n    ngAfterViewInit() {\n      this.nzResizeObserver.observe(this.elementRef).pipe(map(([entry]) => {\n        const {\n          width\n        } = entry.target.getBoundingClientRect();\n        const scrollBarWidth = this.scrollY ? this.verticalScrollBarWidth : 0;\n        return Math.floor(width - scrollBarWidth);\n      }), takeUntil(this.destroy$)).subscribe(this.nzTableStyleService.hostWidth$);\n      if (this.nzTableInnerScrollComponent && this.nzTableInnerScrollComponent.cdkVirtualScrollViewport) {\n        this.cdkVirtualScrollViewport = this.nzTableInnerScrollComponent.cdkVirtualScrollViewport;\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    setScrollOnChanges() {\n      this.scrollX = this.nzScroll && this.nzScroll.x || null;\n      this.scrollY = this.nzScroll && this.nzScroll.y || null;\n      this.nzTableStyleService.setScroll(this.scrollX, this.scrollY);\n    }\n    updateShowPagination() {\n      this.showPagination = this.nzHideOnSinglePage && this.nzData.length > this.nzPageSize || this.nzData.length > 0 && !this.nzHideOnSinglePage || !this.nzFrontPagination && this.nzTotal > this.nzPageSize;\n    }\n    static ɵfac = function NzTableComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NzTableComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$2.NzResizeObserver), i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzTableStyleService), i0.ɵɵdirectiveInject(NzTableDataService), i0.ɵɵdirectiveInject(i5$1.Directionality));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableComponent,\n      selectors: [[\"nz-table\"]],\n      contentQueries: function NzTableComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzTableVirtualScrollDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzVirtualScrollDirective = _t.first);\n        }\n      },\n      viewQuery: function NzTableComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NzTableInnerScrollComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzTableInnerScrollComponent = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-table-wrapper\"],\n      hostVars: 4,\n      hostBindings: function NzTableComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-wrapper-rtl\", ctx.dir === \"rtl\")(\"ant-table-custom-column\", ctx.nzCustomColumn.length);\n        }\n      },\n      inputs: {\n        nzTableLayout: \"nzTableLayout\",\n        nzShowTotal: \"nzShowTotal\",\n        nzItemRender: \"nzItemRender\",\n        nzTitle: \"nzTitle\",\n        nzFooter: \"nzFooter\",\n        nzNoResult: \"nzNoResult\",\n        nzPageSizeOptions: \"nzPageSizeOptions\",\n        nzVirtualItemSize: \"nzVirtualItemSize\",\n        nzVirtualMaxBufferPx: \"nzVirtualMaxBufferPx\",\n        nzVirtualMinBufferPx: \"nzVirtualMinBufferPx\",\n        nzVirtualForTrackBy: \"nzVirtualForTrackBy\",\n        nzLoadingDelay: \"nzLoadingDelay\",\n        nzPageIndex: \"nzPageIndex\",\n        nzPageSize: \"nzPageSize\",\n        nzTotal: \"nzTotal\",\n        nzWidthConfig: \"nzWidthConfig\",\n        nzData: \"nzData\",\n        nzCustomColumn: \"nzCustomColumn\",\n        nzPaginationPosition: \"nzPaginationPosition\",\n        nzScroll: \"nzScroll\",\n        noDataVirtualHeight: \"noDataVirtualHeight\",\n        nzPaginationType: \"nzPaginationType\",\n        nzFrontPagination: [2, \"nzFrontPagination\", \"nzFrontPagination\", booleanAttribute],\n        nzTemplateMode: [2, \"nzTemplateMode\", \"nzTemplateMode\", booleanAttribute],\n        nzShowPagination: [2, \"nzShowPagination\", \"nzShowPagination\", booleanAttribute],\n        nzLoading: [2, \"nzLoading\", \"nzLoading\", booleanAttribute],\n        nzOuterBordered: [2, \"nzOuterBordered\", \"nzOuterBordered\", booleanAttribute],\n        nzLoadingIndicator: \"nzLoadingIndicator\",\n        nzBordered: [2, \"nzBordered\", \"nzBordered\", booleanAttribute],\n        nzSize: \"nzSize\",\n        nzShowSizeChanger: [2, \"nzShowSizeChanger\", \"nzShowSizeChanger\", booleanAttribute],\n        nzHideOnSinglePage: [2, \"nzHideOnSinglePage\", \"nzHideOnSinglePage\", booleanAttribute],\n        nzShowQuickJumper: [2, \"nzShowQuickJumper\", \"nzShowQuickJumper\", booleanAttribute],\n        nzSimple: [2, \"nzSimple\", \"nzSimple\", booleanAttribute]\n      },\n      outputs: {\n        nzPageSizeChange: \"nzPageSizeChange\",\n        nzPageIndexChange: \"nzPageIndexChange\",\n        nzQueryParams: \"nzQueryParams\",\n        nzCurrentPageDataChange: \"nzCurrentPageDataChange\",\n        nzCustomColumnChange: \"nzCustomColumnChange\"\n      },\n      exportAs: [\"nzTable\"],\n      features: [i0.ɵɵProvidersFeature([NzTableStyleService, NzTableDataService]), i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c0,\n      decls: 13,\n      vars: 26,\n      consts: [[\"tableMainElement\", \"\"], [\"paginationTemplate\", \"\"], [\"contentTemplate\", \"\"], [3, \"nzDelay\", \"nzSpinning\", \"nzIndicator\"], [3, \"ngTemplateOutlet\"], [1, \"ant-table\"], [3, \"title\"], [3, \"data\", \"scrollX\", \"scrollY\", \"contentTemplate\", \"listOfColWidth\", \"theadTemplate\", \"tfootTemplate\", \"tfootFixed\", \"verticalScrollBarWidth\", \"virtualTemplate\", \"virtualItemSize\", \"virtualMaxBufferPx\", \"virtualMinBufferPx\", \"tableMainElement\", \"virtualForTrackBy\", \"noDataVirtualHeight\"], [3, \"tableLayout\", \"listOfColWidth\", \"theadTemplate\", \"contentTemplate\", \"tfootTemplate\"], [3, \"footer\"], [1, \"ant-table-pagination\", \"ant-table-pagination-right\", 3, \"hidden\", \"nzShowSizeChanger\", \"nzPageSizeOptions\", \"nzItemRender\", \"nzShowQuickJumper\", \"nzHideOnSinglePage\", \"nzShowTotal\", \"nzSize\", \"nzPageSize\", \"nzTotal\", \"nzSimple\", \"nzPageIndex\"], [1, \"ant-table-pagination\", \"ant-table-pagination-right\", 3, \"nzPageSizeChange\", \"nzPageIndexChange\", \"hidden\", \"nzShowSizeChanger\", \"nzPageSizeOptions\", \"nzItemRender\", \"nzShowQuickJumper\", \"nzHideOnSinglePage\", \"nzShowTotal\", \"nzSize\", \"nzPageSize\", \"nzTotal\", \"nzSimple\", \"nzPageIndex\"]],\n      template: function NzTableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"nz-spin\", 3);\n          i0.ɵɵtemplate(1, NzTableComponent_Conditional_1_Template, 1, 1, null, 4);\n          i0.ɵɵelementStart(2, \"div\", 5, 0);\n          i0.ɵɵtemplate(4, NzTableComponent_Conditional_4_Template, 1, 1, \"nz-table-title-footer\", 6)(5, NzTableComponent_Conditional_5_Template, 1, 16, \"nz-table-inner-scroll\", 7)(6, NzTableComponent_Conditional_6_Template, 1, 5, \"nz-table-inner-default\", 8)(7, NzTableComponent_Conditional_7_Template, 1, 1, \"nz-table-title-footer\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, NzTableComponent_Conditional_8_Template, 1, 1, null, 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, NzTableComponent_ng_template_9_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(11, NzTableComponent_ng_template_11_Template, 1, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzDelay\", ctx.nzLoadingDelay)(\"nzSpinning\", ctx.nzLoading)(\"nzIndicator\", ctx.nzLoadingIndicator);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.nzPaginationPosition === \"both\" || ctx.nzPaginationPosition === \"top\" ? 1 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"ant-table-rtl\", ctx.dir === \"rtl\")(\"ant-table-fixed-header\", ctx.nzData.length && ctx.scrollY)(\"ant-table-fixed-column\", ctx.scrollX)(\"ant-table-has-fix-left\", ctx.hasFixLeft)(\"ant-table-has-fix-right\", ctx.hasFixRight)(\"ant-table-bordered\", ctx.nzBordered)(\"nz-table-out-bordered\", ctx.nzOuterBordered && !ctx.nzBordered)(\"ant-table-middle\", ctx.nzSize === \"middle\")(\"ant-table-small\", ctx.nzSize === \"small\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.nzTitle ? 4 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.scrollY || ctx.scrollX ? 5 : 6);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.nzFooter ? 7 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.nzPaginationPosition === \"both\" || ctx.nzPaginationPosition === \"bottom\" ? 8 : -1);\n        }\n      },\n      dependencies: [NzSpinComponent, NgTemplateOutlet, NzTableTitleFooterComponent, NzTableInnerScrollComponent, NzTableInnerDefaultComponent, NzPaginationModule, i6$1.NzPaginationComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  };\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table',\n      exportAs: 'nzTable',\n      providers: [NzTableStyleService, NzTableDataService],\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <nz-spin [nzDelay]=\"nzLoadingDelay\" [nzSpinning]=\"nzLoading\" [nzIndicator]=\"nzLoadingIndicator\">\n      @if (nzPaginationPosition === 'both' || nzPaginationPosition === 'top') {\n        <ng-template [ngTemplateOutlet]=\"paginationTemplate\"></ng-template>\n      }\n      <div\n        #tableMainElement\n        class=\"ant-table\"\n        [class.ant-table-rtl]=\"dir === 'rtl'\"\n        [class.ant-table-fixed-header]=\"nzData.length && scrollY\"\n        [class.ant-table-fixed-column]=\"scrollX\"\n        [class.ant-table-has-fix-left]=\"hasFixLeft\"\n        [class.ant-table-has-fix-right]=\"hasFixRight\"\n        [class.ant-table-bordered]=\"nzBordered\"\n        [class.nz-table-out-bordered]=\"nzOuterBordered && !nzBordered\"\n        [class.ant-table-middle]=\"nzSize === 'middle'\"\n        [class.ant-table-small]=\"nzSize === 'small'\"\n      >\n        @if (nzTitle) {\n          <nz-table-title-footer [title]=\"nzTitle\"></nz-table-title-footer>\n        }\n        @if (scrollY || scrollX) {\n          <nz-table-inner-scroll\n            [data]=\"data\"\n            [scrollX]=\"scrollX\"\n            [scrollY]=\"scrollY\"\n            [contentTemplate]=\"contentTemplate\"\n            [listOfColWidth]=\"listOfAutoColWidth\"\n            [theadTemplate]=\"theadTemplate\"\n            [tfootTemplate]=\"tfootTemplate\"\n            [tfootFixed]=\"tfootFixed\"\n            [verticalScrollBarWidth]=\"verticalScrollBarWidth\"\n            [virtualTemplate]=\"nzVirtualScrollDirective ? nzVirtualScrollDirective.templateRef : null\"\n            [virtualItemSize]=\"nzVirtualItemSize\"\n            [virtualMaxBufferPx]=\"nzVirtualMaxBufferPx\"\n            [virtualMinBufferPx]=\"nzVirtualMinBufferPx\"\n            [tableMainElement]=\"tableMainElement\"\n            [virtualForTrackBy]=\"nzVirtualForTrackBy\"\n            [noDataVirtualHeight]=\"noDataVirtualHeight\"\n          ></nz-table-inner-scroll>\n        } @else {\n          <nz-table-inner-default\n            [tableLayout]=\"nzTableLayout\"\n            [listOfColWidth]=\"listOfManualColWidth\"\n            [theadTemplate]=\"theadTemplate\"\n            [contentTemplate]=\"contentTemplate\"\n            [tfootTemplate]=\"tfootTemplate\"\n          ></nz-table-inner-default>\n        }\n        @if (nzFooter) {\n          <nz-table-title-footer [footer]=\"nzFooter\"></nz-table-title-footer>\n        }\n      </div>\n      @if (nzPaginationPosition === 'both' || nzPaginationPosition === 'bottom') {\n        <ng-template [ngTemplateOutlet]=\"paginationTemplate\"></ng-template>\n      }\n    </nz-spin>\n    <ng-template #paginationTemplate>\n      @if (nzShowPagination && data.length) {\n        <nz-pagination\n          [hidden]=\"!showPagination\"\n          class=\"ant-table-pagination ant-table-pagination-right\"\n          [nzShowSizeChanger]=\"nzShowSizeChanger\"\n          [nzPageSizeOptions]=\"nzPageSizeOptions\"\n          [nzItemRender]=\"nzItemRender!\"\n          [nzShowQuickJumper]=\"nzShowQuickJumper\"\n          [nzHideOnSinglePage]=\"nzHideOnSinglePage\"\n          [nzShowTotal]=\"nzShowTotal\"\n          [nzSize]=\"nzPaginationType === 'small' ? 'small' : nzSize === 'default' ? 'default' : 'small'\"\n          [nzPageSize]=\"nzPageSize\"\n          [nzTotal]=\"nzTotal\"\n          [nzSimple]=\"nzSimple\"\n          [nzPageIndex]=\"nzPageIndex\"\n          (nzPageSizeChange)=\"onPageSizeChange($event)\"\n          (nzPageIndexChange)=\"onPageIndexChange($event)\"\n        ></nz-pagination>\n      }\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      host: {\n        class: 'ant-table-wrapper',\n        '[class.ant-table-wrapper-rtl]': 'dir === \"rtl\"',\n        '[class.ant-table-custom-column]': `nzCustomColumn.length`\n      },\n      imports: [NzSpinComponent, NgTemplateOutlet, NzTableTitleFooterComponent, NzTableInnerScrollComponent, NzTableInnerDefaultComponent, NzPaginationModule]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1$2.NzResizeObserver\n  }, {\n    type: i1.NzConfigService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzTableStyleService\n  }, {\n    type: NzTableDataService\n  }, {\n    type: i5$1.Directionality\n  }], {\n    nzTableLayout: [{\n      type: Input\n    }],\n    nzShowTotal: [{\n      type: Input\n    }],\n    nzItemRender: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzFooter: [{\n      type: Input\n    }],\n    nzNoResult: [{\n      type: Input\n    }],\n    nzPageSizeOptions: [{\n      type: Input\n    }],\n    nzVirtualItemSize: [{\n      type: Input\n    }],\n    nzVirtualMaxBufferPx: [{\n      type: Input\n    }],\n    nzVirtualMinBufferPx: [{\n      type: Input\n    }],\n    nzVirtualForTrackBy: [{\n      type: Input\n    }],\n    nzLoadingDelay: [{\n      type: Input\n    }],\n    nzPageIndex: [{\n      type: Input\n    }],\n    nzPageSize: [{\n      type: Input\n    }],\n    nzTotal: [{\n      type: Input\n    }],\n    nzWidthConfig: [{\n      type: Input\n    }],\n    nzData: [{\n      type: Input\n    }],\n    nzCustomColumn: [{\n      type: Input\n    }],\n    nzPaginationPosition: [{\n      type: Input\n    }],\n    nzScroll: [{\n      type: Input\n    }],\n    noDataVirtualHeight: [{\n      type: Input\n    }],\n    nzPaginationType: [{\n      type: Input\n    }],\n    nzFrontPagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzTemplateMode: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzShowPagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzLoading: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzOuterBordered: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzLoadingIndicator: [{\n      type: Input\n    }],\n    nzBordered: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzShowSizeChanger: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzHideOnSinglePage: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzShowQuickJumper: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzSimple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzPageSizeChange: [{\n      type: Output\n    }],\n    nzPageIndexChange: [{\n      type: Output\n    }],\n    nzQueryParams: [{\n      type: Output\n    }],\n    nzCurrentPageDataChange: [{\n      type: Output\n    }],\n    nzCustomColumnChange: [{\n      type: Output\n    }],\n    nzVirtualScrollDirective: [{\n      type: ContentChild,\n      args: [NzTableVirtualScrollDirective, {\n        static: false\n      }]\n    }],\n    nzTableInnerScrollComponent: [{\n      type: ViewChild,\n      args: [NzTableInnerScrollComponent]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction fixedAttribute(value) {\n  return value === 'top' || value === 'bottom' ? value : booleanAttribute(value) ? 'bottom' : null;\n}\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTfootSummaryComponent {\n  nzFixed = null;\n  templateRef;\n  nzTableStyleService = inject(NzTableStyleService, {\n    optional: true\n  });\n  isInsideTable = !!this.nzTableStyleService;\n  ngOnInit() {\n    this.nzTableStyleService?.setTfootTemplate(this.templateRef);\n  }\n  ngOnChanges(changes) {\n    const {\n      nzFixed\n    } = changes;\n    this.nzTableStyleService?.setTfootFixed(nzFixed.currentValue);\n  }\n  static ɵfac = function NzTfootSummaryComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTfootSummaryComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTfootSummaryComponent,\n    selectors: [[\"tfoot\", \"nzSummary\", \"\"]],\n    viewQuery: function NzTfootSummaryComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c14, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n      }\n    },\n    hostVars: 2,\n    hostBindings: function NzTfootSummaryComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-table-summary\", !ctx.isInsideTable || !ctx.nzFixed);\n      }\n    },\n    inputs: {\n      nzFixed: [2, \"nzFixed\", \"nzFixed\", fixedAttribute]\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    attrs: _c15,\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 1,\n    consts: [[\"contentTemplate\", \"\"], [3, \"ngTemplateOutlet\"]],\n    template: function NzTfootSummaryComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NzTfootSummaryComponent_ng_template_0_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzTfootSummaryComponent_Conditional_2_Template, 1, 1, null, 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(!ctx.isInsideTable || !ctx.nzFixed ? 2 : -1);\n      }\n    },\n    dependencies: [NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTfootSummaryComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tfoot[nzSummary]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n    @if (!isInsideTable || !nzFixed) {\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    }\n  `,\n      imports: [NgTemplateOutlet],\n      host: {\n        '[class.ant-table-summary]': '!isInsideTable || !nzFixed'\n      }\n    }]\n  }], null, {\n    nzFixed: [{\n      type: Input,\n      args: [{\n        transform: fixedAttribute\n      }]\n    }],\n    templateRef: [{\n      type: ViewChild,\n      args: ['contentTemplate', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrDirective {\n  listOfNzThDirective;\n  listOfCellFixedDirective;\n  destroy$ = new Subject();\n  listOfFixedColumns$ = new ReplaySubject(1);\n  listOfColumns$ = new ReplaySubject(1);\n  listOfFixedColumnsChanges$ = this.listOfFixedColumns$.pipe(switchMap(list => merge(...[this.listOfFixedColumns$, ...list.map(c => c.changes$)]).pipe(mergeMap(() => this.listOfFixedColumns$))), takeUntil(this.destroy$));\n  listOfFixedLeftColumnChanges$ = this.listOfFixedColumnsChanges$.pipe(map(list => list.filter(item => item.nzLeft !== false)));\n  listOfFixedRightColumnChanges$ = this.listOfFixedColumnsChanges$.pipe(map(list => list.filter(item => item.nzRight !== false)));\n  listOfColumnsChanges$ = this.listOfColumns$.pipe(switchMap(list => merge(...[this.listOfColumns$, ...list.map(c => c.changes$)]).pipe(mergeMap(() => this.listOfColumns$))), takeUntil(this.destroy$));\n  nzTableStyleService = inject(NzTableStyleService, {\n    optional: true\n  });\n  isInsideTable = !!this.nzTableStyleService;\n  ngAfterContentInit() {\n    if (this.nzTableStyleService) {\n      this.listOfCellFixedDirective.changes.pipe(startWith(this.listOfCellFixedDirective), takeUntil(this.destroy$)).subscribe(this.listOfFixedColumns$);\n      this.listOfNzThDirective.changes.pipe(startWith(this.listOfNzThDirective), takeUntil(this.destroy$)).subscribe(this.listOfColumns$);\n      /** set last left and first right **/\n      this.listOfFixedLeftColumnChanges$.subscribe(listOfFixedLeft => {\n        listOfFixedLeft.forEach(cell => cell.setIsLastLeft(cell === listOfFixedLeft[listOfFixedLeft.length - 1]));\n      });\n      this.listOfFixedRightColumnChanges$.subscribe(listOfFixedRight => {\n        listOfFixedRight.forEach(cell => cell.setIsFirstRight(cell === listOfFixedRight[0]));\n      });\n      /** calculate fixed nzLeft and nzRight **/\n      combineLatest([this.nzTableStyleService.listOfListOfThWidth$, this.listOfFixedLeftColumnChanges$]).pipe(takeUntil(this.destroy$)).subscribe(([listOfAutoWidth, listOfLeftCell]) => {\n        listOfLeftCell.forEach((cell, index) => {\n          if (cell.isAutoLeft) {\n            const currentArray = listOfLeftCell.slice(0, index);\n            const count = currentArray.reduce((pre, cur) => pre + (cur.colspan || cur.colSpan || 1), 0);\n            const width = listOfAutoWidth.slice(0, count).reduce((pre, cur) => pre + cur, 0);\n            cell.setAutoLeftWidth(`${width}px`);\n          }\n        });\n      });\n      combineLatest([this.nzTableStyleService.listOfListOfThWidth$, this.listOfFixedRightColumnChanges$]).pipe(takeUntil(this.destroy$)).subscribe(([listOfAutoWidth, listOfRightCell]) => {\n        listOfRightCell.forEach((_, index) => {\n          const cell = listOfRightCell[listOfRightCell.length - index - 1];\n          if (cell.isAutoRight) {\n            const currentArray = listOfRightCell.slice(listOfRightCell.length - index, listOfRightCell.length);\n            const count = currentArray.reduce((pre, cur) => pre + (cur.colspan || cur.colSpan || 1), 0);\n            const width = listOfAutoWidth.slice(listOfAutoWidth.length - count, listOfAutoWidth.length).reduce((pre, cur) => pre + cur, 0);\n            cell.setAutoRightWidth(`${width}px`);\n          }\n        });\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzTrDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTrDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTrDirective,\n    selectors: [[\"tr\", 3, \"mat-row\", \"\", 3, \"mat-header-row\", \"\", 3, \"nz-table-measure-row\", \"\", 3, \"nzExpand\", \"\", 3, \"nz-table-fixed-row\", \"\"]],\n    contentQueries: function NzTrDirective_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, NzThMeasureDirective, 4);\n        i0.ɵɵcontentQuery(dirIndex, NzCellFixedDirective, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzThDirective = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfCellFixedDirective = _t);\n      }\n    },\n    hostVars: 2,\n    hostBindings: function NzTrDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-table-row\", ctx.isInsideTable);\n      }\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'tr:not([mat-row]):not([mat-header-row]):not([nz-table-measure-row]):not([nzExpand]):not([nz-table-fixed-row])',\n      host: {\n        '[class.ant-table-row]': 'isInsideTable'\n      }\n    }]\n  }], null, {\n    listOfNzThDirective: [{\n      type: ContentChildren,\n      args: [NzThMeasureDirective]\n    }],\n    listOfCellFixedDirective: [{\n      type: ContentChildren,\n      args: [NzCellFixedDirective]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTheadComponent {\n  elementRef;\n  renderer;\n  destroy$ = new Subject();\n  isInsideTable = false;\n  templateRef;\n  listOfNzTrDirective;\n  listOfNzThAddOnComponent;\n  nzSortOrderChange = new EventEmitter();\n  nzTableStyleService = inject(NzTableStyleService, {\n    optional: true\n  });\n  nzTableDataService = inject(NzTableDataService, {\n    optional: true\n  });\n  constructor(elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.isInsideTable = !!this.nzTableStyleService;\n  }\n  ngOnInit() {\n    if (this.nzTableStyleService) {\n      this.nzTableStyleService.setTheadTemplate(this.templateRef);\n    }\n  }\n  ngAfterContentInit() {\n    if (this.nzTableStyleService) {\n      const firstTableRow$ = this.listOfNzTrDirective.changes.pipe(startWith(this.listOfNzTrDirective), map(item => item && item.first));\n      const listOfColumnsChanges$ = firstTableRow$.pipe(switchMap(firstTableRow => firstTableRow ? firstTableRow.listOfColumnsChanges$ : EMPTY), takeUntil(this.destroy$));\n      listOfColumnsChanges$.subscribe(data => this.nzTableStyleService.setListOfTh(data));\n      /** TODO: need reset the measure row when scrollX change **/\n      this.nzTableStyleService.enableAutoMeasure$.pipe(switchMap(enable => enable ? listOfColumnsChanges$ : of([]))).pipe(takeUntil(this.destroy$)).subscribe(data => this.nzTableStyleService.setListOfMeasureColumn(data));\n      const listOfFixedLeftColumnChanges$ = firstTableRow$.pipe(switchMap(firstTr => firstTr ? firstTr.listOfFixedLeftColumnChanges$ : EMPTY), takeUntil(this.destroy$));\n      const listOfFixedRightColumnChanges$ = firstTableRow$.pipe(switchMap(firstTr => firstTr ? firstTr.listOfFixedRightColumnChanges$ : EMPTY), takeUntil(this.destroy$));\n      listOfFixedLeftColumnChanges$.subscribe(listOfFixedLeftColumn => {\n        this.nzTableStyleService.setHasFixLeft(listOfFixedLeftColumn.length !== 0);\n      });\n      listOfFixedRightColumnChanges$.subscribe(listOfFixedRightColumn => {\n        this.nzTableStyleService.setHasFixRight(listOfFixedRightColumn.length !== 0);\n      });\n    }\n    if (this.nzTableDataService) {\n      const listOfColumn$ = this.listOfNzThAddOnComponent.changes.pipe(startWith(this.listOfNzThAddOnComponent));\n      const manualSort$ = listOfColumn$.pipe(switchMap(() => merge(...this.listOfNzThAddOnComponent.map(th => th.manualClickOrder$))), takeUntil(this.destroy$));\n      manualSort$.subscribe(data => {\n        const emitValue = {\n          key: data.nzColumnKey,\n          value: data.sortOrder\n        };\n        this.nzSortOrderChange.emit(emitValue);\n        if (data.nzSortFn && data.nzSortPriority === false) {\n          this.listOfNzThAddOnComponent.filter(th => th !== data).forEach(th => th.clearSortOrder());\n        }\n      });\n      const listOfCalcOperator$ = listOfColumn$.pipe(switchMap(list => merge(...[listOfColumn$, ...list.map(c => c.calcOperatorChange$)]).pipe(mergeMap(() => listOfColumn$))), map(list => list.filter(item => !!item.nzSortFn || !!item.nzFilterFn).map(item => {\n        const {\n          nzSortFn,\n          sortOrder,\n          nzFilterFn,\n          nzFilterValue,\n          nzSortPriority,\n          nzColumnKey\n        } = item;\n        return {\n          key: nzColumnKey,\n          sortFn: nzSortFn,\n          sortPriority: nzSortPriority,\n          sortOrder: sortOrder,\n          filterFn: nzFilterFn,\n          filterValue: nzFilterValue\n        };\n      })),\n      // TODO: after checked error here\n      delay(0), takeUntil(this.destroy$));\n      listOfCalcOperator$.subscribe(list => {\n        this.nzTableDataService?.listOfCalcOperator$.next(list);\n      });\n    }\n  }\n  ngAfterViewInit() {\n    if (this.nzTableStyleService) {\n      this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzTheadComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTheadComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzTheadComponent,\n    selectors: [[\"thead\", 9, \"ant-table-thead\"]],\n    contentQueries: function NzTheadComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, NzTrDirective, 5);\n        i0.ɵɵcontentQuery(dirIndex, NzThAddOnComponent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzTrDirective = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzThAddOnComponent = _t);\n      }\n    },\n    viewQuery: function NzTheadComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c14, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n      }\n    },\n    outputs: {\n      nzSortOrderChange: \"nzSortOrderChange\"\n    },\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 1,\n    consts: [[\"contentTemplate\", \"\"], [3, \"ngTemplateOutlet\"]],\n    template: function NzTheadComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NzTheadComponent_ng_template_0_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzTheadComponent_Conditional_2_Template, 1, 1, null, 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(!ctx.isInsideTable ? 2 : -1);\n      }\n    },\n    dependencies: [NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTheadComponent, [{\n    type: Component,\n    args: [{\n      selector: 'thead:not(.ant-table-thead)',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n    @if (!isInsideTable) {\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    }\n  `,\n      imports: [NgTemplateOutlet]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }], {\n    templateRef: [{\n      type: ViewChild,\n      args: ['contentTemplate', {\n        static: true\n      }]\n    }],\n    listOfNzTrDirective: [{\n      type: ContentChildren,\n      args: [NzTrDirective, {\n        descendants: true\n      }]\n    }],\n    listOfNzThAddOnComponent: [{\n      type: ContentChildren,\n      args: [NzThAddOnComponent, {\n        descendants: true\n      }]\n    }],\n    nzSortOrderChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrExpandDirective {\n  nzExpand = true;\n  static ɵfac = function NzTrExpandDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTrExpandDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTrExpandDirective,\n    selectors: [[\"tr\", \"nzExpand\", \"\"]],\n    hostAttrs: [1, \"ant-table-expanded-row\"],\n    hostVars: 1,\n    hostBindings: function NzTrExpandDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"hidden\", !ctx.nzExpand);\n      }\n    },\n    inputs: {\n      nzExpand: \"nzExpand\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrExpandDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'tr[nzExpand]',\n      host: {\n        class: 'ant-table-expanded-row',\n        '[hidden]': `!nzExpand`\n      }\n    }]\n  }], null, {\n    nzExpand: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableModule {\n  static ɵfac = function NzTableModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTableModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzTableModule,\n    imports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTrExpandDirective, NzTfootSummaryComponent, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzTableContentComponent, NzTableTitleFooterComponent, NzTableInnerDefaultComponent, NzTableInnerScrollComponent, NzTrMeasureComponent, NzRowIndentDirective, NzRowExpandButtonDirective, NzCellBreakWordDirective, NzCellAlignDirective, NzTableSortersComponent, NzTableFilterComponent, NzTableSelectionComponent, NzCellEllipsisDirective, NzFilterTriggerComponent, NzTableFixedRowComponent, NzThSelectionComponent],\n    exports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzFilterTriggerComponent, NzTrExpandDirective, NzTfootSummaryComponent, NzCellBreakWordDirective, NzCellAlignDirective, NzCellEllipsisDirective, NzTableFixedRowComponent, NzThSelectionComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzTableComponent, NzThAddOnComponent, NzTdAddOnComponent, NzTbodyComponent, NzTableTitleFooterComponent, NzTableInnerScrollComponent, NzTableSortersComponent, NzTableFilterComponent, NzTableSelectionComponent, NzFilterTriggerComponent, NzThSelectionComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTrExpandDirective, NzTfootSummaryComponent, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzTableContentComponent, NzTableTitleFooterComponent, NzTableInnerDefaultComponent, NzTableInnerScrollComponent, NzTrMeasureComponent, NzRowIndentDirective, NzRowExpandButtonDirective, NzCellBreakWordDirective, NzCellAlignDirective, NzTableSortersComponent, NzTableFilterComponent, NzTableSelectionComponent, NzCellEllipsisDirective, NzFilterTriggerComponent, NzTableFixedRowComponent, NzThSelectionComponent],\n      exports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzFilterTriggerComponent, NzTrExpandDirective, NzTfootSummaryComponent, NzCellBreakWordDirective, NzCellAlignDirective, NzCellEllipsisDirective, NzTableFixedRowComponent, NzThSelectionComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzCellAlignDirective, NzCellBreakWordDirective, NzCellEllipsisDirective, NzCellFixedDirective, NzCustomColumnDirective, NzFilterTriggerComponent, NzRowExpandButtonDirective, NzRowIndentDirective, NzTableCellDirective, NzTableComponent, NzTableContentComponent, NzTableDataService, NzTableFilterComponent, NzTableFixedRowComponent, NzTableInnerDefaultComponent, NzTableInnerScrollComponent, NzTableModule, NzTableSelectionComponent, NzTableSortersComponent, NzTableStyleService, NzTableTitleFooterComponent, NzTableVirtualScrollDirective, NzTbodyComponent, NzTdAddOnComponent, NzTfootSummaryComponent, NzThAddOnComponent, NzThMeasureDirective, NzThSelectionComponent, NzTheadComponent, NzTrDirective, NzTrExpandDirective, NzTrMeasureComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,YAAY,EAAE;AAC3B,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,IAAI,cAAc,CAAC;AAAA,EAC/B,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,IAAI,cAAc,CAAC;AAAA,EAC/B,QAAQ,IAAI,cAAc,CAAC;AAAA,EAC3B,QAAQ;AACN,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,OAAO,OAAO;AACZ,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,MAAM,KAAK,KAAK;AAAA,EACvB;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,WAAW,IAAI,QAAQ;AAAA,EACvB,yBAAyB;AAAA,EACzB,WAAW,MAAM;AAAA,EAAC;AAAA,EAClB,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,MAAM;AAAA,EACN,YAAY,KAAK,gBAAgB,gBAAgB;AAC/C,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,eAAe,UAAU,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC9E,UAAI,KAAK,UAAU,OAAO;AACxB,aAAK,QAAQ;AACb,aAAK,SAAS,KAAK,KAAK;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,SAAK,eAAe,SAAS,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1E,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,UAAU,CAAC;AAAA,IAC/C,CAAC;AACD,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY;AACd,WAAK,eAAe,YAAY,KAAK,UAAU;AAAA,IACjD;AACA,QAAI,QAAQ;AACV,WAAK,eAAe,QAAQ,KAAK,MAAM;AAAA,IACzC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,QAAQ;AACb,SAAK,eAAe,OAAO,KAAK;AAChC,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,YAAY;AAC3B,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,eAAe,YAAY,KAAK,UAAU;AAC/C,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAA0B,kBAAqB,iBAAiB,GAAM,kBAAkB,cAAc,GAAM,kBAAqB,cAAc,CAAC;AAAA,EACnL;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,WAAW,CAAC,GAAG,iBAAiB;AAAA,IAChC,UAAU;AAAA,IACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,yBAAyB,IAAI,WAAW,OAAO,EAAE,yBAAyB,IAAI,WAAW,OAAO,EAAE,yBAAyB,IAAI,kBAAkB,OAAO,EAAE,uBAAuB,IAAI,QAAQ,KAAK;AAAA,MACnN;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,eAAe;AAAA,MACf,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,mBAAmB,CAAC,gBAAgB;AAAA,MAChD,SAAS;AAAA,MACT,aAAa,WAAW,MAAM,sBAAqB;AAAA,MACnD,OAAO;AAAA,IACT,CAAC,CAAC,GAAM,oBAAoB;AAAA,IAC5B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC,gBAAgB;AAAA,QAC1B,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,qBAAqB;AAAA,QACnD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,iCAAiC;AAAA,QACjC,iCAAiC;AAAA,QACjC,iCAAiC;AAAA,QACjC,+BAA+B;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,WAAW,IAAI,QAAQ;AAAA,EACvB,yBAAyB;AAAA,EACzB,iBAAiB,OAAO,cAAc;AAAA,EACtC,iBAAiB,OAAO,gBAAgB;AAAA,IACtC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,sBAAsB,OAAO,qBAAqB;AAAA,IAChD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW,MAAM;AAAA,EAAC;AAAA,EAClB,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB;AAAA,EACA,UAAU;AAAA,EACV,aAAa;AAAA,EACb,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,QAAQ;AACN,SAAK,aAAa,SAAS,KAAK,cAAc,UAAU;AAAA,EAC1D;AAAA,EACA,OAAO;AACL,SAAK,aAAa,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,YAAY,QAAQ,YAAY,KAAK,cAAc;AACjD,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,YAAY;AACjB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,WAAW;AACT,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AACzE,aAAK,OAAO;AACZ,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,WAAK,eAAe,UAAU,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AACjF,aAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,aAAK,yBAAyB;AAC9B,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,WAAK,eAAe,UAAU,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC9E,cAAM,YAAY,KAAK;AACvB,aAAK,YAAY,KAAK,YAAY;AAKlC,YAAI,KAAK,aAAa,cAAc,KAAK;AAAA;AAAA,QAGzC,KAAK,cAAc,OAAO;AACxB,eAAK,SAAS,KAAK;AAAA,QACrB;AACA,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AACA,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACvG,UAAI,CAAC,aAAa;AAChB,gBAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,UAAU,CAAC;AAC7C,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe,MAAM;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,eAAe,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAC/E,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,aAAa;AACpB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AACvB,SAAK,aAAa,eAAe,KAAK,UAAU;AAAA,EAClD;AAAA,EACA,qBAAqB;AACnB,4BAAwB,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAEhH,YAAM,gBAAgB;AACtB,YAAM,eAAe;AACrB,UAAI,KAAK,cAAc,KAAK,WAAW;AACrC;AAAA,MACF;AACA,WAAK,OAAO,IAAI,MAAM;AACpB,aAAK,MAAM;AACX,aAAK,gBAAgB,OAAO,KAAK,OAAO;AACxC,YAAI,KAAK,WAAW;AAClB,eAAK,YAAY;AACjB,eAAK,SAAS,IAAI;AAAA,QACpB;AACA,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,YAAY,CAAC;AAAA,EAC5M;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IAC7D,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,MACrE;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,kCAAkC,CAAC,CAAC,IAAI,mBAAmB,EAAE,qBAAqB,CAAC,IAAI,aAAa,EAAE,4BAA4B,IAAI,aAAa,EAAE,6BAA6B,IAAI,aAAa,CAAC,IAAI,aAAa,EAAE,oCAAoC,IAAI,aAAa,IAAI,aAAa,EAAE,8BAA8B,IAAI,cAAc,CAAC,IAAI,aAAa,EAAE,qCAAqC,IAAI,cAAc,IAAI,aAAa,EAAE,yBAAyB,CAAC,IAAI,iBAAiB,IAAI,QAAQ,KAAK,EAAE,gCAAgC,IAAI,iBAAiB,IAAI,QAAQ,KAAK;AAAA,MACtkB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,MAC/D,eAAe,CAAC,GAAG,mBAAmB,iBAAiB,gBAAgB;AAAA,IACzE;AAAA,IACA,UAAU,CAAC,SAAS;AAAA,IACpB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa,WAAW,MAAM,iBAAgB;AAAA,MAC9C,OAAO;AAAA,IACT,CAAC,CAAC,CAAC;AAAA,IACH,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,QAAQ,SAAS,GAAG,YAAY,SAAS,CAAC;AAAA,IAC1E,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,MAAM;AAC3B,QAAG,UAAU,GAAG,SAAS,GAAG,CAAC,EAAE,GAAG,MAAM;AACxC,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,MAAM;AAC3B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,aAAa,CAAC,IAAI,aAAa,EAAE,qBAAqB,IAAI,aAAa,CAAC,IAAI,aAAa,EAAE,sBAAsB,IAAI,cAAc,CAAC,IAAI,aAAa,EAAE,oBAAoB,IAAI,aAAa,EAAE,4BAA4B,IAAI,aAAa,IAAI,aAAa,EAAE,6BAA6B,IAAI,cAAc,IAAI,aAAa;AAC7U,QAAG,UAAU;AACb,QAAG,YAAY,mBAAmB,CAAC,IAAI,aAAa,EAAE,0BAA0B,IAAI,aAAa;AACjG,QAAG,WAAW,YAAY,IAAI,UAAU,EAAE,WAAW,IAAI,SAAS;AAClE,QAAG,YAAY,aAAa,IAAI,cAAc,cAAc,IAAI,EAAE,QAAQ,IAAI,IAAI;AAClF,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,mBAAmB,CAAC,IAAI,aAAa,EAAE,0BAA0B,IAAI,aAAa;AAAA,MACnG;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,gBAAgB;AAAA,QAC9C,OAAO;AAAA,MACT,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,0CAA0C;AAAA,QAC1C,6BAA6B;AAAA,QAC7B,oCAAoC;AAAA,QACpC,qCAAqC;AAAA,QACrC,4CAA4C;AAAA,QAC5C,sCAAsC;AAAA,QACtC,6CAA6C;AAAA,QAC7C,iCAAiC;AAAA,QACjC,wCAAwC;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,IACjD,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,EACnD,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,MACjD,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,IACnD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC1dH,IAAMA,OAAM,CAAC,sBAAsB,EAAE;AACrC,IAAMC,OAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,MAAM;AACR;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,GAAG;AACxB,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO;AAAA,EAC9B;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,WAAW,CAAC,EAAE,GAAG,uEAAuE,GAAG,GAAG,WAAW,CAAC;AACxM,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,OAAO,QAAQ;AACzC,IAAG,YAAY,SAAS,OAAO,OAAO,SAAS;AAC/C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,cAAc,QAAQ,IAAI,CAAC;AAAA,EACrD;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,WAAW,CAAC,EAAE,GAAG,uEAAuE,GAAG,GAAG,WAAW,CAAC;AACxM,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,OAAO,QAAQ;AACzC,IAAG,YAAY,SAAS,OAAO,OAAO,SAAS;AAC/C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,cAAc,QAAQ,IAAI,CAAC;AAAA,EACrD;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8EAA8E,GAAG,GAAG,WAAW,CAAC,EAAE,GAAG,8EAA8E,GAAG,GAAG,WAAW,CAAC;AAAA,EACxN;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,OAAO,cAAc,QAAQ,IAAI,CAAC;AAAA,EACrD;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8EAA8E,GAAG,GAAG,WAAW,CAAC,EAAE,GAAG,8EAA8E,GAAG,GAAG,WAAW,CAAC;AAAA,EACxN;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,OAAO,cAAc,QAAQ,IAAI,CAAC;AAAA,EACrD;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACxC,IAAG,WAAW,GAAG,gEAAgE,GAAG,CAAC,EAAE,GAAG,gEAAgE,GAAG,CAAC;AAC9J,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,GAAG,KAAoB;AACjC,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,UAAU,CAAC;AACd,IAAG,eAAe,UAAU,aAAa,WAAW,IAAI,YAAY,WAAW,IAAI,EAAE;AAAA,EACvF;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,GAAG,EAAE,GAAG,yDAAyD,GAAG,GAAG,UAAU,CAAC,EAAE,GAAG,yDAAyD,GAAG,GAAG,UAAU,CAAC,EAAE,GAAG,yDAAyD,GAAG,GAAG,KAAK,CAAC;AAAA,EAC7T;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,UAAU,IAAI;AACpB,IAAG,eAAe,UAAU,aAAa,SAAS,IAAI,YAAY,SAAS,IAAI,YAAY,SAAS,IAAI,CAAC;AAAA,EAC3G;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AAAC;AACpE,IAAMC,OAAM,CAAC,yBAAyB,EAAE;AACxC,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,CAAC;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,WAAW,UAAU,KAAK,EAAE,WAAW,UAAU,KAAK;AAAA,EACtE;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,aAAa,CAAC;AACnC,IAAG,WAAW,iBAAiB,SAAS,uFAAuF,QAAQ;AACrI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,iBAAiB,GAAG,2DAA2D,GAAG,GAAG,aAAa,GAAG,UAAU;AAClH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,QAAQ,EAAE,UAAU,OAAO,MAAM,EAAE,WAAW,OAAO,QAAQ;AAChG,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,oBAAoB;AAAA,EAC3C;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,iBAAiB,SAAS,mFAAmF,QAAQ;AACjI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,SAAS,GAAG;AACrD,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,OAAO,QAAQ;AACzC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,MAAM,GAAG;AAAA,EACpD;AACF;AACA,IAAM,MAAM,CAAC,mBAAmB;AAChC,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,OAAO;AACT;AACA,SAAS,gFAAgF,IAAI,KAAK;AAAC;AACnG,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,iFAAiF,GAAG,GAAG,eAAe,CAAC;AACxH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,OAAO,OAAO,MAAM,CAAC;AAAA,EACxI;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,aAAa,SAAS,kFAAkF,QAAQ;AAC5H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC,EAAE,aAAa,SAAS,kFAAkF,QAAQ;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,QAAQ,QAAQ,IAAI,EAAE,SAAS,QAAQ,KAAK,EAAE,YAAY,CAAC,CAAC,QAAQ,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,UAAU,OAAO,cAAc,QAAQ,KAAK,EAAE,aAAa,OAAO,GAAG;AAAA,EAC7N;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,mBAAmB,SAAS,gGAAgG,QAAQ;AAChJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC,EAAE,kBAAkB,SAAS,+FAA+F,QAAQ;AACnI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,SAAS,OAAO,KAAK,EAAE,UAAU,OAAO,MAAM,EAAE,YAAY,OAAO,QAAQ,EAAE,UAAU,OAAO,MAAM,EAAE,mBAAmB,OAAO,eAAe,EAAE,mBAAmB,OAAO,eAAe,EAAE,aAAa,OAAO,SAAS,EAAE,YAAY,OAAO,QAAQ,EAAE,mBAAmB,OAAO,eAAe;AAAA,EACjT;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,IAAI;AACzB,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,MAAM,CAAC;AACjG,IAAG,iBAAiB,GAAG,2DAA2D,GAAG,GAAG,MAAM,GAAM,oBAAoB,EAAE,iBAAiB,IAAI;AAC/I,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,MAAM,CAAC;AACjG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,YAAY,IAAI,EAAE;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,cAAc;AACnC,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,mBAAmB,OAAO,kBAAkB,IAAI,EAAE;AAAA,EAC5E;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;AACrC,IAAG,WAAW,SAAS,SAAS,yEAAyE;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,CAAC;AAAA,IACxC,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,MAAM,CAAC,EAAE,GAAG,SAAS,CAAC;AAC3C,IAAG,WAAW,iBAAiB,SAAS,kFAAkF,QAAQ;AAChI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,GAAG,GAAG;AAChB,IAAG,aAAa;AAChB,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,SAAS,SAAS,yEAAyE;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,CAAC;AAAA,IACzC,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,YAAY,OAAO,YAAY,EAAE,aAAa,OAAO,GAAG,EAAE,cAAc,OAAO,UAAU;AAChI,IAAG,YAAY,SAAS,OAAO,OAAO,SAAS;AAC/C,IAAG,UAAU;AACb,IAAG,YAAY,SAAS,OAAO,YAAY,MAAM,OAAO,SAAS;AACjE,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,SAAS,OAAO,SAAS;AACpE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,OAAO,WAAW,GAAG;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,YAAY,OAAO,WAAW,EAAE,aAAa,OAAO,GAAG,EAAE,cAAc,OAAO,UAAU;AAC/H,IAAG,YAAY,SAAS,OAAO,UAAU,OAAO,OAAO,OAAO,OAAO,SAAS;AAAA,EAChF;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,eAAe,CAAC;AAAA,EACnH;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,sBAAyB,YAAY,CAAC;AAC5C,IAAG,WAAW,oBAAoB,oBAAoB,QAAQ;AAAA,EAChE;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,eAAe,CAAC;AAAA,EACnH;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,uBAA0B,YAAY,CAAC;AAC7C,IAAG,WAAW,oBAAoB,qBAAqB,QAAQ;AAAA,EACjE;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,4DAA4D,GAAG,GAAG,MAAM,CAAC;AAAA,EAC1K;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,WAAW,IAAI,CAAC;AAAA,EAC1C;AACF;AACA,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,SAAS;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,aAAa;AAAA,EACb,YAAY,IAAI,aAAa;AAAA,EAC7B,YAAY,IAAI,aAAa;AAAA,EAC7B,QAAQ;AAAA,EACR,YAAY;AACV,QAAI,CAAC,KAAK,UAAU;AAClB,UAAI,KAAK,SAAS,QAAQ;AACxB,aAAK,UAAU,KAAK,KAAK,KAAK;AAAA,MAChC,OAAO;AACL,aAAK,UAAU,KAAK;AAAA,UAClB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV,EAAE,KAAK,IAAI,CAAC;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU,SAAS,MAAM;AAC3B,WAAK,QAAQ;AAAA,QACX,MAAM,GAAG,KAAK,KAAK;AAAA,QACnB,MAAM,KAAK,QAAQ;AAAA,QACnB,MAAM,KAAK,QAAQ;AAAA,QACnB,QAAQ,KAAK,QAAQ;AAAA,QACrB,QAAQ,KAAK,QAAQ;AAAA,MACvB,EAAE,KAAK,IAAI;AAAA,IACb;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,sBAAsB,EAAE,CAAC;AAAA,IAC5C,UAAU;AAAA,IACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,qDAAqD;AACnF,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,SAAS,IAAI,KAAK;AACjC,QAAG,YAAY,uBAAuB,IAAI,SAAS,MAAM,EAAE,uBAAuB,IAAI,SAAS,MAAM,EAAE,uBAAuB,IAAI,SAAS,MAAM,EAAE,4BAA4B,IAAI,SAAS,QAAQ,EAAE,wCAAwC,IAAI,SAAS,QAAQ,EAAE,4BAA4B,IAAI,SAAS,QAAQ,EAAE,wCAAwC,IAAI,SAAS,QAAQ,EAAE,2BAA2B,IAAI,QAAQ,EAAE,8BAA8B,IAAI,MAAM;AAAA,MAC1c;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACX,MAAM;AAAA,MACN,YAAY;AAAA,IACd;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAOF;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,QAAQ,UAAU,GAAG,4BAA4B,GAAG,UAAU,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,UAAU,OAAO,GAAG,CAAC,UAAU,MAAM,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,UAAU,gBAAgB,GAAG,+BAA+B,GAAG,CAAC,UAAU,eAAe,GAAG,+BAA+B,CAAC;AAAA,IAC7a,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,kDAAkD,GAAG,GAAG,eAAe,CAAC;AAAA,MACzM;AACA,UAAI,KAAK,GAAG;AACV,cAAM,wBAA2B,YAAY,CAAC;AAC9C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,IAAI,cAAc,qBAAqB,EAAE,2BAA8B,gBAAgB,GAAGC,MAAK,IAAI,MAAM,IAAI,KAAK,CAAC;AAAA,MACvJ;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,iBAAiB,gBAAgB;AAAA,IACjE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsDV,MAAM;AAAA,QACJ,+BAA+B;AAAA,QAC/B,+BAA+B;AAAA,QAC/B,+BAA+B;AAAA,QAC/B,oCAAoC;AAAA,QACpC,gDAAgD;AAAA,QAChD,oCAAoC;AAAA,QACpC,gDAAgD;AAAA,QAChD,mCAAmC;AAAA,QACnC,sCAAsC;AAAA,QACtC,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACb;AAAA,MACA,SAAS,CAAC,cAAc,gBAAgB;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,SAAS;AAAA,EACT,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB;AAAA,EACA,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,kBAAkB,CAAC;AAAA,EACnB,kBAAkB,IAAI,aAAa;AAAA,EACnC,iBAAiB,IAAI,aAAa;AAAA,EAClC,uBAAuB,CAAC;AAAA,EACxB,iBAAiB,MAAM;AACrB,QAAI,KAAK,aAAa,MAAM;AAC1B,WAAK,eAAe,KAAK,IAAI;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,mBAAmB,QAAQ;AACzB,UAAM,SAAS,OAAO;AACtB,UAAM,QAAQ,KAAK,MAAM,SAAS,OAAO,OAAO,KAAK,SAAS,CAAC;AAC/D,SAAK,gBAAgB,KAAK,KAAK;AAC/B,WAAO,QAAQ;AAAA,EACjB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY,mBAAmB,QAAQ;AACzC,WAAK,uBAAuB,CAAC,GAAG,oBAAI,IAAI,CAAC,GAAG,KAAK,iBAAiB,KAAK,QAAQ,CAAC,CAAC,EAAE,IAAI,WAAS;AAAA,QAC9F,OAAO;AAAA,QACP,OAAO,GAAG,IAAI,IAAI,KAAK,OAAO,cAAc;AAAA,MAC9C,EAAE;AAAA,IACJ;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,yBAAyB,EAAE,CAAC;AAAA,IAC/C,WAAW,CAAC,GAAG,wBAAwB;AAAA,IACvC,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAOC;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,uCAAuC,GAAG,cAAc,UAAU,SAAS,GAAG,CAAC,GAAG,qCAAqC,GAAG,CAAC,GAAG,uCAAuC,GAAG,iBAAiB,cAAc,UAAU,SAAS,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,iBAAiB,UAAU,CAAC;AAAA,IACrS,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,aAAa,CAAC,EAAE,GAAG,qDAAqD,GAAG,GAAG,OAAO,CAAC;AAAA,MACpK;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,kBAAkB,IAAI,EAAE;AAC7C,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,kBAAkB,IAAI,EAAE;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAqB,mBAAwB,mBAAmB,aAAgB,iBAAoB,OAAO;AAAA,IAC1H,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,gBAAgB,WAAW;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,WAAW;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,kBAAkB,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,EACjC,kBAAkB,IAAI,aAAa;AAAA,EACnC,iBAAiB,IAAI,aAAa;AAAA,EAClC,SAAS,CAAC,GAAG,CAAC;AAAA,EACd,iBAAiB,CAAC;AAAA,EAClB,MAAM;AAAA,EACN,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,KAAK,UAAU,YAAY,gBAAgB;AACrD,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,aAAS,YAAY,SAAS,WAAW,WAAW,aAAa,GAAG,WAAW,aAAa;AAAA,EAC9F;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,eAAe;AACpB,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,QAAQ,OAAO;AACtB,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,oBAAoB;AAAA,IAC5E,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,WAAW,eAAe,oBAAoB;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA,EACA,SAAS,MAAM;AACb,SAAK,SAAS,KAAK,YAAY,IAAI;AAAA,EACrC;AAAA,EACA,gBAAgB,GAAG,OAAO;AACxB,WAAO,GAAG,MAAM,IAAI,IAAI,MAAM,KAAK;AAAA,EACrC;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,gBAAgB,KAAK,KAAK;AAAA,EACjC;AAAA,EACA,iBAAiB,MAAM;AACrB,SAAK,eAAe,KAAK,IAAI;AAAA,EAC/B;AAAA,EACA,aAAa,OAAO,UAAU;AAC5B,WAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,EACnC;AAAA,EACA,eAAe;AACb,UAAM,YAAY,KAAK,aAAa,KAAK,OAAO,KAAK,QAAQ;AAC7D,SAAK,iBAAiB,KAAK,kBAAkB,KAAK,WAAW,SAAS;AAAA,EACxE;AAAA,EACA,kBAAkB,WAAW,WAAW;AAEtC,UAAM,qBAAqB,gBAAc;AACvC,YAAM,WAAW;AAAA,QACf,MAAM;AAAA,QACN,UAAU,cAAc;AAAA,MAC1B;AACA,YAAM,WAAW;AAAA,QACf,MAAM;AAAA,QACN,UAAU,cAAc;AAAA,MAC1B;AACA,aAAO,CAAC,UAAU,GAAG,YAAY,QAAQ;AAAA,IAC3C;AACA,UAAM,eAAe,CAAC,OAAO,QAAQ;AACnC,YAAM,OAAO,CAAC;AACd,eAAS,IAAI,OAAO,KAAK,KAAK,KAAK;AACjC,aAAK,KAAK;AAAA,UACR,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AACA,QAAI,aAAa,GAAG;AAClB,aAAO,mBAAmB,aAAa,GAAG,SAAS,CAAC;AAAA,IACtD,OAAO;AAEL,YAAM,oBAAoB,CAAC,UAAU,SAAS;AAC5C,YAAI,cAAc,CAAC;AACnB,cAAM,eAAe;AAAA,UACnB,MAAM;AAAA,QACR;AACA,cAAM,eAAe;AAAA,UACnB,MAAM;AAAA,QACR;AACA,cAAM,gBAAgB,aAAa,GAAG,CAAC;AACvC,cAAM,eAAe,aAAa,WAAW,SAAS;AACtD,YAAI,WAAW,GAAG;AAEhB,gBAAM,UAAU,aAAa,IAAI,IAAI;AACrC,wBAAc,CAAC,GAAG,aAAa,GAAG,OAAO,GAAG,YAAY;AAAA,QAC1D,WAAW,WAAW,OAAO,GAAG;AAC9B,wBAAc,CAAC,cAAc,GAAG,aAAa,WAAW,GAAG,WAAW,CAAC,GAAG,YAAY;AAAA,QACxF,OAAO;AAEL,gBAAM,WAAW,aAAa,OAAO,IAAI,OAAO,IAAI,OAAO;AAC3D,wBAAc,CAAC,cAAc,GAAG,aAAa,UAAU,OAAO,CAAC,CAAC;AAAA,QAClE;AACA,eAAO,CAAC,GAAG,eAAe,GAAG,aAAa,GAAG,YAAY;AAAA,MAC3D;AACA,aAAO,mBAAmB,kBAAkB,WAAW,SAAS,CAAC;AAAA,IACnE;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,aAAa,YAAY,OAAO;AAClC,WAAK,SAAS,EAAE,KAAK,YAAY,KAAK,KAAK,WAAW,GAAG,KAAK,IAAI,KAAK,YAAY,KAAK,UAAU,KAAK,KAAK,CAAC;AAC7G,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAAiC,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAuB,cAAc,CAAC;AAAA,EAC/N;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,WAAW,SAAS,mCAAmC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,MACjE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,sBAAsB,IAAI,GAAG,UAAU,QAAQ,SAAS,YAAY,cAAc,UAAU,WAAW,GAAG,CAAC,yBAAyB,IAAI,GAAG,SAAS,UAAU,YAAY,UAAU,mBAAmB,mBAAmB,aAAa,YAAY,iBAAiB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,sBAAsB,IAAI,GAAG,aAAa,aAAa,UAAU,QAAQ,SAAS,YAAY,cAAc,UAAU,WAAW,GAAG,CAAC,yBAAyB,IAAI,GAAG,mBAAmB,kBAAkB,SAAS,UAAU,YAAY,UAAU,mBAAmB,mBAAmB,aAAa,YAAY,iBAAiB,CAAC;AAAA,IACvsB,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,MAC/H;AAAA,IACF;AAAA,IACA,cAAc,CAAC,kBAAkB,2BAA2B,4BAA4B;AAAA,IACxF,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8CV,SAAS,CAAC,kBAAkB,2BAA2B,4BAA4B;AAAA,IACrF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,WAAW;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,kBAAkB,IAAI,aAAa;AAAA,EACnC,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,cAAc;AAAA,EACd,MAAM;AAAA,EACN,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,KAAK,UAAU,YAAY,gBAAgB;AACrD,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,aAAS,YAAY,SAAS,WAAW,WAAW,aAAa,GAAG,WAAW,aAAa;AAAA,EAC9F;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,eAAe;AACpB,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,QAAQ,OAAO;AACtB,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,oBAAoB;AAAA,IAC5E,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,WAAW,eAAe,oBAAoB;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,mBAAmB,QAAQ;AACzB,UAAM,SAAS,OAAO;AACtB,UAAM,QAAQ,SAAS,OAAO,OAAO,KAAK,SAAS;AACnD,SAAK,kBAAkB,KAAK;AAC5B,WAAO,QAAQ,GAAG,KAAK,SAAS;AAAA,EAClC;AAAA,EACA,UAAU;AACR,SAAK,kBAAkB,KAAK,YAAY,CAAC;AAAA,EAC3C;AAAA,EACA,WAAW;AACT,SAAK,kBAAkB,KAAK,YAAY,CAAC;AAAA,EAC3C;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,gBAAgB,KAAK,KAAK;AAAA,EACjC;AAAA,EACA,qBAAqB;AACnB,SAAK,YAAY,KAAK,KAAK,KAAK,QAAQ,KAAK,QAAQ;AACrD,SAAK,eAAe,KAAK,cAAc;AACvC,SAAK,cAAc,KAAK,cAAc,KAAK;AAAA,EAC7C;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,aAAa,SAAS,UAAU;AAClC,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oCAAoC,mBAAmB;AAC5E,WAAO,KAAK,qBAAqB,8BAAgC,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAuB,cAAc,CAAC;AAAA,EAC9N;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,WAAW,SAAS,kCAAkC,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,MACjE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,sBAAsB,IAAI,QAAQ,QAAQ,GAAG,SAAS,UAAU,YAAY,aAAa,YAAY,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,QAAQ,KAAK,GAAG,iBAAiB,YAAY,OAAO,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,sBAAsB,IAAI,QAAQ,QAAQ,GAAG,SAAS,UAAU,YAAY,aAAa,YAAY,CAAC;AAAA,IAC7W,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,oDAAoD,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB;AAAA,MAC/H;AAAA,IACF;AAAA,IACA,cAAc,CAAC,yBAAyB;AAAA,IACxC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA+BV,SAAS,CAAC,yBAAyB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB;AAC9B,IAAI,yBAAyB,MAAM;AACjC,MAAI;AACJ,MAAI,uBAAuB,CAAC;AAC5B,MAAI,4BAA4B,CAAC;AACjC,MAAI;AACJ,MAAI,kCAAkC,CAAC;AACvC,MAAI,uCAAuC,CAAC;AAC5C,MAAI;AACJ,MAAI,kCAAkC,CAAC;AACvC,MAAI,uCAAuC,CAAC;AAC5C,MAAI;AACJ,MAAI,kCAAkC,CAAC;AACvC,MAAI,uCAAuC,CAAC;AAC5C,MAAI;AACJ,MAAI,yBAAyB,CAAC;AAC9B,MAAI,8BAA8B,CAAC;AACnC,SAAO,MAAMC,uBAAsB;AAAA,IACjC,OAAO;AACL,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,uBAAO,OAAO,IAAI,IAAI;AAC1F,2BAAqB,CAAC,WAAW,CAAC;AAClC,sCAAgC,CAAC,WAAW,CAAC;AAC7C,sCAAgC,CAAC,WAAW,CAAC;AAC7C,sCAAgC,CAAC,WAAW,CAAC;AAC7C,6BAAuB,CAAC,WAAW,CAAC;AACpC,mBAAa,MAAM,MAAM,oBAAoB;AAAA,QAC3C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,YAAY;AAAA,UACxB,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,SAAS;AAAA,UACf;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,sBAAsB,yBAAyB;AAClD,mBAAa,MAAM,MAAM,+BAA+B;AAAA,QACtD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,uBAAuB;AAAA,UACnC,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,oBAAoB;AAAA,UAC1B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,iCAAiC,oCAAoC;AACxE,mBAAa,MAAM,MAAM,+BAA+B;AAAA,QACtD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,uBAAuB;AAAA,UACnC,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,oBAAoB;AAAA,UAC1B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,iCAAiC,oCAAoC;AACxE,mBAAa,MAAM,MAAM,+BAA+B;AAAA,QACtD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,uBAAuB;AAAA,UACnC,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,oBAAoB;AAAA,UAC1B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,iCAAiC,oCAAoC;AACxE,mBAAa,MAAM,MAAM,sBAAsB;AAAA,QAC7C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,cAAc;AAAA,UAC1B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,WAAW;AAAA,UACjB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,wBAAwB,2BAA2B;AACtD,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU;AAAA,QAC1D,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,mBAAmB,IAAI,aAAa;AAAA,IACpC,oBAAoB,IAAI,aAAa;AAAA,IACrC,cAAc;AAAA,IACd,eAAe;AAAA,IACf,SAAS,kBAAkB,MAAM,sBAAsB,SAAS;AAAA,IAChE,qBAAqB,kBAAkB,MAAM,yBAAyB,GAAG,kBAAkB,MAAM,iCAAiC,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;AAAA,IAClJ,qBAAqB,kBAAkB,MAAM,oCAAoC,GAAG,kBAAkB,MAAM,iCAAiC,KAAK;AAAA,IAClJ,qBAAqB,kBAAkB,MAAM,oCAAoC,GAAG,kBAAkB,MAAM,iCAAiC,KAAK;AAAA,IAClJ,YAAY,kBAAkB,MAAM,oCAAoC,GAAG,kBAAkB,MAAM,wBAAwB,KAAK;AAAA,IAChI,cAAc,kBAAkB,MAAM,2BAA2B,GAAG;AAAA,IACpE,eAAe;AAAA,IACf,qBAAqB;AAAA,IACrB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,WAAW,IAAI,QAAQ;AAAA,IACvB,SAAS,IAAI,cAAc,CAAC;AAAA,IAC5B,kBAAkB,OAAO,WAAW;AAClC,UAAI,QAAQ,WAAW;AACrB,eAAO;AAAA,MACT,WAAW,QAAQ,GAAG;AACpB,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,kBAAkB,OAAO;AACvB,YAAM,YAAY,KAAK,aAAa,KAAK,SAAS,KAAK,UAAU;AACjE,YAAM,aAAa,KAAK,kBAAkB,OAAO,SAAS;AAC1D,UAAI,eAAe,KAAK,eAAe,CAAC,KAAK,YAAY;AACvD,aAAK,cAAc;AACnB,aAAK,kBAAkB,KAAK,KAAK,WAAW;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,iBAAiB,MAAM;AACrB,WAAK,aAAa;AAClB,WAAK,iBAAiB,KAAK,IAAI;AAC/B,YAAM,YAAY,KAAK,aAAa,KAAK,SAAS,KAAK,UAAU;AACjE,UAAI,KAAK,cAAc,WAAW;AAChC,aAAK,kBAAkB,SAAS;AAAA,MAClC;AAAA,IACF;AAAA,IACA,cAAc,OAAO;AACnB,YAAM,YAAY,KAAK,aAAa,OAAO,KAAK,UAAU;AAC1D,UAAI,KAAK,cAAc,WAAW;AAChC,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,eAAK,kBAAkB,SAAS;AAChC,eAAK,IAAI,aAAa;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,aAAa,OAAO,UAAU;AAC5B,aAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,IACnC;AAAA,IACA,YAAY,MAAM,KAAK,mBAAmB,iBAAiB,gBAAgB;AACzE,WAAK,OAAO;AACZ,WAAK,MAAM;AACX,WAAK,oBAAoB;AACzB,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AAAA,IACxB;AAAA,IACA,WAAW;AACT,WAAK,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpE,aAAK,SAAS,KAAK,KAAK,cAAc,YAAY;AAClD,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,WAAK,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC5D,aAAK,cAAc,KAAK;AAAA,MAC1B,CAAC;AACD,WAAK,kBAAkB,UAAU,iBAAiB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,QAAM;AACjG,YAAI,KAAK,cAAc;AACrB,eAAK,OAAO,OAAO,iBAAiB,KAAK,UAAU;AACnD,eAAK,IAAI,aAAa;AAAA,QACxB;AAAA,MACF,CAAC;AACD,WAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,aAAK,MAAM;AACX,aAAK,IAAI,cAAc;AAAA,MACzB,CAAC;AACD,WAAK,MAAM,KAAK,eAAe;AAAA,IACjC;AAAA,IACA,cAAc;AACZ,WAAK,SAAS,KAAK;AACnB,WAAK,SAAS,SAAS;AAAA,IACzB;AAAA,IACA,YAAY,SAAS;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,SAAS;AACX,aAAK,OAAO,KAAK,KAAK,OAAO;AAAA,MAC/B;AACA,UAAI,sBAAsB,WAAW,YAAY;AAC/C,aAAK,iBAAiB,KAAK,sBAAsB,KAAK,UAAU,KAAK,cAAc,KAAK,UAAU,KAAK,CAAC,KAAK;AAAA,MAC/G;AACA,UAAI,QAAQ;AACV,aAAK,OAAO,OAAO;AAAA,MACrB;AAAA,IACF;AAAA,IACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,aAAO,KAAK,qBAAqBA,wBAA0B,kBAAuB,aAAa,GAAM,kBAAqB,iBAAiB,GAAM,kBAAuB,mBAAmB,GAAM,kBAAqB,eAAe,GAAM,kBAAuB,cAAc,CAAC;AAAA,IACnR;AAAA,IACA,OAAO,OAAyB,kBAAkB;AAAA,MAChD,MAAMA;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,CAAC,GAAG,gBAAgB;AAAA,MAC/B,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,yBAAyB,IAAI,QAAQ,EAAE,2BAA2B,IAAI,UAAU,EAAE,uBAAuB,CAAC,IAAI,YAAY,IAAI,SAAS,OAAO,EAAE,sBAAsB,IAAI,QAAQ,KAAK;AAAA,QACxM;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,QACjF,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,QACjF,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,QACtD,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,QAC5D,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,QAClE,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,QACpF,SAAS,CAAC,GAAG,WAAW,WAAW,eAAe;AAAA,QAClD,aAAa,CAAC,GAAG,eAAe,eAAe,eAAe;AAAA,QAC9D,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC7D;AAAA,MACA,SAAS;AAAA,QACP,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,MACrB;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,UAAU,CAAI,oBAAoB;AAAA,MAClC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,GAAG,mBAAmB,YAAY,cAAc,UAAU,YAAY,SAAS,WAAW,GAAG,CAAC,GAAG,mBAAmB,kBAAkB,UAAU,cAAc,aAAa,YAAY,UAAU,mBAAmB,mBAAmB,SAAS,aAAa,YAAY,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MACnX,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,WAAW,GAAG,8CAA8C,GAAG,CAAC;AACnE,UAAG,eAAe,GAAG,wBAAwB,GAAG,CAAC;AACjD,UAAG,WAAW,mBAAmB,SAAS,+EAA+E,QAAQ;AAC/H,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,kBAAkB,MAAM,CAAC;AAAA,UACrD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,yBAAyB,GAAG,CAAC;AAClD,UAAG,WAAW,mBAAmB,SAAS,gFAAgF,QAAQ;AAChI,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,kBAAkB,MAAM,CAAC;AAAA,UACrD,CAAC,EAAE,kBAAkB,SAAS,+EAA+E,QAAQ;AACnH,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,UACpD,CAAC;AACD,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,iBAAiB,IAAI,EAAE;AAC5C,UAAG,UAAU;AACb,UAAG,WAAW,YAAY,IAAI,UAAU,EAAE,cAAc,IAAI,YAAY,EAAE,UAAU,IAAI,MAAM,EAAE,YAAY,IAAI,UAAU,EAAE,SAAS,IAAI,OAAO,EAAE,aAAa,IAAI,WAAW;AAC9K,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,UAAU,IAAI,IAAI,EAAE,cAAc,IAAI,YAAY,EAAE,aAAa,IAAI,WAAW,EAAE,YAAY,IAAI,UAAU,EAAE,UAAU,IAAI,MAAM,EAAE,mBAAmB,IAAI,iBAAiB,EAAE,mBAAmB,IAAI,iBAAiB,EAAE,SAAS,IAAI,OAAO,EAAE,aAAa,IAAI,WAAW,EAAE,YAAY,IAAI,UAAU,EAAE,mBAAmB,IAAI,iBAAiB;AAAA,QAChW;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,6BAA6B,4BAA4B;AAAA,MAC1F,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF,GAAG;AAAA,CACF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoCV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,iCAAiC;AAAA,QACjC,mCAAmC;AAAA,QACnC,+BAA+B;AAAA,QAC/B,8BAA8B;AAAA,MAChC;AAAA,MACA,SAAS,CAAC,kBAAkB,6BAA6B,4BAA4B;AAAA,IACvF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,uBAAuB,6BAA6B,8BAA8B,2BAA2B,4BAA4B;AAAA,IACnJ,SAAS,CAAC,qBAAqB;AAAA,EACjC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,uBAAuB,6BAA6B,8BAA8B,2BAA2B,4BAA4B;AAAA,EACrJ,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,uBAAuB,6BAA6B,8BAA8B,2BAA2B,4BAA4B;AAAA,MACnJ,SAAS,CAAC,qBAAqB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACllDH,IAAMC,OAAM,CAAC,GAAG;AAChB,IAAMC,cAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,8CAA8C,IAAI,KAAK;AAAC;AACjE,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,EAAE;AAChC,IAAG,WAAW,iBAAiB,SAAS,mGAAmG;AACzI,MAAG,cAAc,GAAG;AACpB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,IAAI,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,cAAc,EAAE;AAChC,IAAG,WAAW,WAAW,KAAK,OAAO;AAAA,EACvC;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,EAAE;AAChC,IAAG,WAAW,iBAAiB,SAAS,mGAAmG;AACzI,MAAG,cAAc,GAAG;AACpB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,IAAI,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,cAAc,EAAE;AAChC,IAAG,WAAW,WAAW,KAAK,OAAO;AAAA,EACvC;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,SAAS,SAAS,0EAA0E;AACxG,YAAM,OAAU,cAAc,GAAG,EAAE;AACnC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,IAAI,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,SAAS,EAAE,EAAE,GAAG,mEAAmE,GAAG,GAAG,SAAS,EAAE;AAC9L,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,KAAK,OAAO;AACxC,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,OAAO,iBAAiB,IAAI,CAAC;AAC/C,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,KAAK,IAAI;AAAA,EAChC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,qBAAqB,CAAC;AAC3C,IAAG,WAAW,mBAAmB,SAAS,2FAA2F,QAAQ;AAC3I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,UAAU,GAAG,WAAW,CAAC;AAC5B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,oBAAoB,MAAM,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC;AACzE,IAAG,iBAAiB,GAAG,qDAAqD,GAAG,GAAG,MAAM,GAAGA,WAAU;AACrG,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,UAAU,CAAC;AAC7C,IAAG,WAAW,SAAS,SAAS,wEAAwE;AACtG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,MAAM,CAAC;AAAA,IACtC,CAAC;AACD,IAAG,OAAO,EAAE;AACZ,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,WAAW,SAAS,SAAS,yEAAyE;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,CAAC;AAAA,IACxC,CAAC;AACD,IAAG,OAAO,EAAE;AACZ,IAAG,aAAa,EAAE,EAAE,EAAE;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,gBAAmB,YAAY,CAAC;AACtC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,SAAS,EAAE,YAAY,OAAO,SAAS,EAAE,kBAAkB,aAAa;AAC1G,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,OAAO,kBAAkB;AACvC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,CAAC,OAAO,SAAS;AAC3C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,aAAa,GAAG;AACzD,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,OAAO,aAAa;AAAA,EAClD;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,aAAa;AAAA,EACxD;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,iBAAiB,SAAS,gFAAgF,QAAQ;AAC9H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,yCAAyC,OAAO,gBAAgB;AAC/E,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,cAAc,OAAO,QAAQ,EAAE,mBAAmB,OAAO,aAAa;AAC/G,IAAG,YAAY,cAAc,OAAO,KAAK;AAAA,EAC3C;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,SAAS,SAAS,6EAA6E;AAC3G,YAAM,eAAkB,cAAc,GAAG,EAAE;AAC3C,aAAU,YAAY,aAAa,SAAS,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,aAAa,MAAM,GAAG;AAAA,EACnD;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC3C,IAAG,UAAU,GAAG,WAAW,CAAC;AAC5B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,oBAAoB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC;AAC5D,IAAG,iBAAiB,GAAG,wDAAwD,GAAG,GAAG,MAAM,GAAM,yBAAyB;AAC1H,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,YAAY,CAAC;AACzC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,kBAAkB,gBAAgB;AAChD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,OAAO,gBAAgB;AAAA,EACvC;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAAC;AAClE,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,UAAU,OAAO,cAAc,QAAQ;AAAA,EACxD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,UAAU,OAAO,cAAc,SAAS;AAAA,EACzD;AACF;AACA,IAAMC,OAAM,CAAC,aAAa,EAAE;AAC5B,SAAS,sEAAsE,IAAI,KAAK;AAAC;AACzF,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,eAAe,CAAC;AAAA,EAChH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,YAAY;AAAA,EACvD;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,gBAAgB,SAAS,uFAAuF,QAAQ;AACpI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,QAAQ,EAAE,aAAa,CAAC,OAAO,YAAY;AAAA,EAC5E;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,CAAC;AAClC,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,yDAAyD,GAAG,GAAG,UAAU,CAAC;AAAA,EACxK;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,YAAY;AAC/C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,eAAe,IAAI,CAAC;AAAA,EAC9C;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,iBAAiB,SAAS,yEAAyE,QAAQ;AACvH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,UAAU,EAAE,WAAW,OAAO,SAAS,EAAE,mBAAmB,OAAO,eAAe;AACrH,IAAG,YAAY,cAAc,OAAO,OAAO;AAAA,EAC7C;AACF;AACA,IAAMC,OAAM,CAAC,eAAe,EAAE;AAC9B,IAAMC,OAAM,CAAC,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,CAAC,mBAAmB,CAAC,GAAG,GAAG;AACpE,IAAMC,OAAM,CAAC,iBAAiB,qBAAqB,GAAG;AACtD,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,mBAAmB,CAAC;AACzC,IAAG,WAAW,gBAAgB,SAAS,kFAAkF,QAAQ;AAC/H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,uBAA0B,YAAY,CAAC;AAC7C,UAAM,mBAAsB,YAAY,CAAC;AACzC,IAAG,WAAW,mBAAmB,oBAAoB,EAAE,iBAAiB,gBAAgB,EAAE,gBAAgB,OAAO,cAAc,EAAE,kBAAkB,OAAO,gBAAgB,EAAE,gBAAgB,OAAO,SAAS;AAAA,EAC9M;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,uBAA0B,YAAY,CAAC;AAC7C,IAAG,WAAW,oBAAoB,oBAAoB;AAAA,EACxD;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AAAC;AAC3E,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,eAAe,CAAC;AAAA,EAClG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,kBAAqB,YAAY,CAAC;AACxC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,oBAAoB,OAAO,aAAa,kBAAkB,kBAAkB;AAAA,EAC5F;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,oBAAoB,CAAC;AAAA,EACvC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,aAAa,OAAO,SAAS,EAAE,kBAAkB,OAAO,cAAc,EAAE,mBAAmB,kBAAkB;AAAA,EAC7H;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,IAAM,MAAM,CAAC,gBAAgB,EAAE;AAC/B,IAAM,MAAM,CAAC,oBAAoB,EAAE;AACnC,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK;AAAA,EACvB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,YAAY,SAAS,QAAQ,EAAE,aAAa,QAAQ;AAAA,EACzD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU;AAC/B,IAAG,iBAAiB,GAAG,sDAAsD,GAAG,GAAG,OAAO,GAAM,sBAAsB;AACtH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,cAAc;AAAA,EACrC;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAAC;AAChF,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,eAAe,CAAC;AACrG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,aAAa;AAAA,EACxD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAAC;AAClE,SAAS,6DAA6D,IAAI,KAAK;AAAC;AAChF,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,eAAe,CAAC;AACrG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,aAAa;AAAA,EACxD;AACF;AACA,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,sBAAsB,EAAE;AACrC,SAAS,8DAA8D,IAAI,KAAK;AAAC;AACjF,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,eAAe,CAAC;AACtG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,YAAY,SAAY,YAAY,GAAG,GAAG,OAAO,UAAU,GAAG,IAAI;AACrE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,kBAAkB;AAAA,EACtD;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAAC;AACjF,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,eAAe,CAAC;AAAA,EACxG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,oBAAoB,kBAAkB;AAAA,EACtD;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,IAAM,MAAM,CAAC,wBAAwB,EAAE;AACvC,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,GAAG,CAAC;AAAA,EAC5B;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,mBAAmB,SAAS,oFAAoF,QAAQ;AACpI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,wBAAwB,MAAM,CAAC;AAAA,IAC9D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,yBAA4B,cAAc;AAChD,IAAG,WAAW,uBAAuB,sBAAsB;AAAA,EAC7D;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,MAAM,CAAC;AAAA,EACvF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,iBAAiB,IAAI,SAAS,IAAI,EAAE;AAAA,EAC9D;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,UAAU,GAAG,kBAAkB,CAAC;AACnC,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,mBAAsB,YAAY,GAAG,GAAG,OAAO,SAAS,CAAC;AAAA,EACzE;AACF;AACA,IAAM,OAAO,CAAC,oBAAoB;AAClC,IAAM,OAAO,CAAC,kBAAkB;AAChC,IAAMC,QAAO,CAAC,kBAAkB;AAChC,IAAMC,QAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,WAAW;AAAA,EACX,OAAO;AACT;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,UAAU,GAAG,SAAS,EAAE;AAC3B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,YAAY;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,kBAAkB,OAAO,cAAc,EAAE,mBAAmB,OAAO,eAAe;AAAA,EAC7H;AACF;AACA,SAAS,8FAA8F,IAAI,KAAK;AAAC;AACjH,SAAS,gFAAgF,IAAI,KAAK;AAChG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+FAA+F,GAAG,GAAG,eAAe,EAAE;AACvI,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,eAAe,EAAE,2BAA8B,gBAAgB,GAAGA,OAAM,SAAS,IAAI,CAAC;AAAA,EACjI;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,+BAA+B,IAAI,CAAC,EAAE,GAAG,SAAS,EAAE,EAAE,GAAG,OAAO;AACrF,IAAG,WAAW,GAAG,iFAAiF,GAAG,GAAG,gBAAgB,EAAE;AAC1H,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,UAAU,OAAO,KAAK,SAAS,OAAO,UAAU,OAAO,mBAAmB;AACzF,IAAG,WAAW,YAAY,OAAO,eAAe,EAAE,eAAe,OAAO,kBAAkB,EAAE,eAAe,OAAO,kBAAkB;AACpI,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,kBAAkB,OAAO,cAAc;AAChF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,mBAAmB,OAAO,IAAI,EAAE,wBAAwB,OAAO,iBAAiB;AAAA,EAChG;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,UAAU,GAAG,SAAS,EAAE;AAC3B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,cAAc;AACnC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,kBAAkB,OAAO,cAAc,EAAE,iBAAiB,OAAO,aAAa;AAAA,EACzH;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,kEAAkE,GAAG,GAAG,+BAA+B,CAAC,EAAE,GAAG,kEAAkE,GAAG,GAAG,OAAO,CAAC;AAAA,EACrS;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,cAAc;AACnC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,kBAAkB,OAAO,cAAc,EAAE,iBAAiB,OAAO,aAAa,EAAE,iBAAiB,OAAO,eAAe,QAAQ,OAAO,gBAAgB,IAAI;AACnM,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,OAAO,kBAAkB,IAAI,CAAC;AAChD,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,eAAe,WAAW,IAAI,EAAE;AAAA,EAC1D;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,UAAU,GAAG,SAAS,EAAE;AAC3B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,YAAY;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,kBAAkB,OAAO,cAAc,EAAE,iBAAiB,OAAO,aAAa,EAAE,mBAAmB,OAAO,eAAe,EAAE,iBAAiB,OAAO,aAAa;AAAA,EAC3M;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AAAC;AACzE,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,eAAe,CAAC;AAAA,EAChG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,wBAA2B,YAAY,EAAE;AAC/C,IAAG,WAAW,oBAAoB,qBAAqB;AAAA,EACzD;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,CAAC;AAAA,EAC5C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAS,OAAO,OAAO;AAAA,EACvC;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,CAAC;AAAA,EAC5C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,sBAAyB,YAAY,CAAC;AAC5C,UAAM,qBAAwB,YAAY,EAAE;AAC5C,IAAG,WAAW,QAAQ,OAAO,IAAI,EAAE,WAAW,OAAO,OAAO,EAAE,WAAW,OAAO,OAAO,EAAE,mBAAmB,kBAAkB,EAAE,kBAAkB,OAAO,kBAAkB,EAAE,iBAAiB,OAAO,aAAa,EAAE,iBAAiB,OAAO,aAAa,EAAE,cAAc,OAAO,UAAU,EAAE,0BAA0B,OAAO,sBAAsB,EAAE,mBAAmB,OAAO,2BAA2B,OAAO,yBAAyB,cAAc,IAAI,EAAE,mBAAmB,OAAO,iBAAiB,EAAE,sBAAsB,OAAO,oBAAoB,EAAE,sBAAsB,OAAO,oBAAoB,EAAE,oBAAoB,mBAAmB,EAAE,qBAAqB,OAAO,mBAAmB,EAAE,uBAAuB,OAAO,mBAAmB;AAAA,EAC9tB;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,0BAA0B,CAAC;AAAA,EAC7C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,EAAE;AAC5C,IAAG,WAAW,eAAe,OAAO,aAAa,EAAE,kBAAkB,OAAO,oBAAoB,EAAE,iBAAiB,OAAO,aAAa,EAAE,mBAAmB,kBAAkB,EAAE,iBAAiB,OAAO,aAAa;AAAA,EACvN;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,CAAC;AAAA,EAC5C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,QAAQ;AAAA,EACzC;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AAAC;AACzE,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,eAAe,CAAC;AAAA,EAChG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,wBAA2B,YAAY,EAAE;AAC/C,IAAG,WAAW,oBAAoB,qBAAqB;AAAA,EACzD;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,iBAAiB,EAAE;AACxC,IAAG,WAAW,oBAAoB,SAAS,gGAAgG,QAAQ;AACjJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,qBAAqB,SAAS,iGAAiG,QAAQ;AACxI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,CAAC,OAAO,cAAc,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,gBAAgB,OAAO,YAAY,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,sBAAsB,OAAO,kBAAkB,EAAE,eAAe,OAAO,WAAW,EAAE,UAAU,OAAO,qBAAqB,UAAU,UAAU,OAAO,WAAW,YAAY,YAAY,OAAO,EAAE,cAAc,OAAO,UAAU,EAAE,WAAW,OAAO,OAAO,EAAE,YAAY,OAAO,QAAQ,EAAE,eAAe,OAAO,WAAW;AAAA,EAC/hB;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uDAAuD,GAAG,IAAI,iBAAiB,EAAE;AAAA,EACpG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,oBAAoB,OAAO,KAAK,SAAS,IAAI,EAAE;AAAA,EACzE;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,IAAM,OAAO,CAAC,iBAAiB;AAC/B,IAAM,OAAO,CAAC,aAAa,EAAE;AAC7B,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAAC;AAChF,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,eAAe,CAAC;AAAA,EACvG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,oBAAoB,kBAAkB;AAAA,EACtD;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AAAC;AACzE,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,eAAe,CAAC;AAAA,EAChG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,oBAAoB,kBAAkB;AAAA,EACtD;AACF;AACA,IAAM,0BAA0B;AAChC,IAAI,4BAA4B,MAAM;AACpC,MAAI;AACJ,MAAI,2BAA2B,CAAC;AAChC,MAAI,gCAAgC,CAAC;AACrC,SAAO,MAAMC,0BAAyB;AAAA,IACpC,OAAO;AACL,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,uBAAO,OAAO,IAAI,IAAI;AAC1F,+BAAyB,CAAC,WAAW,CAAC;AACtC,mBAAa,MAAM,MAAM,wBAAwB;AAAA,QAC/C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,gBAAgB;AAAA,UAC5B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,aAAa;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,0BAA0B,6BAA6B;AAC1D,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU;AAAA,QAC1D,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX;AAAA,IACA,YAAY;AAAA,IACZ,aAAa,kBAAkB,MAAM,0BAA0B,KAAK;AAAA,IACpE,mBAAmB,kBAAkB,MAAM,6BAA6B,GAAG,IAAI,aAAa;AAAA,IAC5F;AAAA,IACA,gBAAgB,SAAS;AACvB,WAAK,YAAY;AACjB,WAAK,gBAAgB,KAAK,OAAO;AAAA,IACnC;AAAA,IACA,OAAO;AACL,WAAK,YAAY;AACjB,WAAK,IAAI,aAAa;AAAA,IACxB;AAAA,IACA,OAAO;AACL,WAAK,YAAY;AACjB,WAAK,IAAI,aAAa;AAAA,IACxB;AAAA,IACA,YAAY,iBAAiB,KAAK,UAAU;AAC1C,WAAK,kBAAkB;AACvB,WAAK,MAAM;AACX,WAAK,WAAW;AAAA,IAClB;AAAA,IACA,WAAW;AACT,8BAAwB,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAChH,cAAM,gBAAgB;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,IACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,aAAO,KAAK,qBAAqBA,2BAA6B,kBAAqB,eAAe,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IAC5L;AAAA,IACA,OAAO,OAAyB,kBAAkB;AAAA,MAChD,MAAMA;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,WAAW,SAAS,+BAA+B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qBAAqB,GAAG,UAAU;AAAA,QACnD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AAAA,QACnE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC9D;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,iBAAiB;AAAA,MAC5B,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AAAA,MACpD,oBAAoBR;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,eAAe,IAAI,aAAa,SAAS,eAAe,eAAe,GAAG,4BAA4B,GAAG,mBAAmB,cAAc,eAAe,kBAAkB,WAAW,CAAC;AAAA,MACjM,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,WAAW,mBAAmB,SAAS,kEAAkE,QAAQ;AAClH,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC;AACD,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,UAAU,IAAI,QAAQ,EAAE,yBAAyB,IAAI,SAAS;AAC7E,UAAG,WAAW,cAAc,IAAI,UAAU,EAAE,eAAe,KAAK,EAAE,kBAAkB,IAAI,cAAc,EAAE,aAAa,IAAI,SAAS;AAAA,QACpI;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAqB,mBAAmB;AAAA,MACvD,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF,GAAG;AAAA,CACF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiBV,WAAW,CAAC,gBAAgB;AAAA,MAC5B,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,eAAe,CAAC;AAAA,EAChB,eAAe,IAAI,aAAa;AAAA,EAChC,WAAW,IAAI,QAAQ;AAAA,EACvB;AAAA,EACA,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,qBAAqB,CAAC;AAAA,EACtB,gBAAgB,CAAC;AAAA,EACjB,MAAMS,SAAQ;AACZ,QAAI,KAAK,gBAAgB;AACvB,WAAK,qBAAqB,KAAK,mBAAmB,IAAI,UAAQ;AAC5D,YAAI,SAASA,SAAQ;AACnB,iBAAO,iCACF,OADE;AAAA,YAEL,SAAS,CAACA,QAAO;AAAA,UACnB;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,MAAAA,QAAO,UAAU,CAACA,QAAO;AAAA,IAC3B,OAAO;AACL,WAAK,qBAAqB,KAAK,mBAAmB,IAAI,UAAS,iCAC1D,OAD0D;AAAA,QAE7D,SAAS,SAASA;AAAA,MACpB,EAAE;AAAA,IACJ;AACA,SAAK,YAAY,KAAK,iBAAiB,KAAK,kBAAkB;AAAA,EAChE;AAAA,EACA,UAAU;AACR,SAAK,YAAY;AACjB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,QAAQ;AACN,SAAK,YAAY;AACjB,SAAK,qBAAqB,KAAK,kBAAkB,KAAK,cAAc,IAAI;AACxE,SAAK,YAAY,KAAK,iBAAiB,KAAK,kBAAkB;AAC9D,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,YAAY;AACjB,QAAI,CAAC,OAAO;AACV,WAAK,eAAe;AAAA,IACtB,OAAO;AACL,WAAK,gBAAgB,KAAK,mBAAmB,OAAO,UAAQ,KAAK,OAAO,EAAE,IAAI,UAAQ,KAAK,KAAK;AAAA,IAClG;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,UAAM,gBAAgB,KAAK,mBAAmB,OAAO,UAAQ,KAAK,OAAO,EAAE,IAAI,UAAQ,KAAK,KAAK;AACjG,QAAI,CAAC,YAAY,KAAK,eAAe,aAAa,GAAG;AACnD,UAAI,KAAK,gBAAgB;AACvB,aAAK,aAAa,KAAK,aAAa;AAAA,MACtC,OAAO;AACL,aAAK,aAAa,KAAK,cAAc,SAAS,IAAI,cAAc,CAAC,IAAI,IAAI;AAAA,MAC3E;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB,cAAc,OAAO;AACrC,WAAO,aAAa,IAAI,UAAQ;AAC9B,YAAM,UAAU,QAAQ,QAAQ,CAAC,CAAC,KAAK;AACvC,aAAO;AAAA,QACL,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,QACZ;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,oBAAoB;AACnC,WAAO,mBAAmB,KAAK,UAAQ,KAAK,OAAO;AAAA,EACrD;AAAA,EACA,YAAY,KAAK,MAAM;AACrB,SAAK,MAAM;AACX,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW;AACT,SAAK,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpE,WAAK,SAAS,KAAK,KAAK,cAAc,OAAO;AAC7C,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB,KAAK,gBAAgB,KAAK,aAAa,QAAQ;AACjE,WAAK,qBAAqB,KAAK,kBAAkB,KAAK,YAAY;AAClE,WAAK,YAAY,KAAK,iBAAiB,KAAK,kBAAkB;AAAA,IAChE;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAA2B,kBAAqB,iBAAiB,GAAM,kBAAuB,aAAa,CAAC;AAAA,EAC/I;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,WAAW,CAAC,GAAG,yBAAyB;AAAA,IACxC,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,IAChB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,cAAc,gBAAgB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,mBAAmB,aAAa,YAAY,gBAAgB,GAAG,CAAC,UAAU,UAAU,WAAW,MAAM,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,GAAG,YAAY,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,aAAa,IAAI,UAAU,QAAQ,UAAU,SAAS,GAAG,SAAS,UAAU,GAAG,CAAC,aAAa,IAAI,UAAU,WAAW,UAAU,SAAS,GAAG,OAAO,GAAG,CAAC,gBAAgB,IAAI,GAAG,SAAS,YAAY,GAAG,CAAC,YAAY,IAAI,GAAG,SAAS,GAAG,CAAC,eAAe,IAAI,GAAG,SAAS,GAAG,CAAC,YAAY,IAAI,GAAG,iBAAiB,SAAS,GAAG,CAAC,eAAe,IAAI,GAAG,iBAAiB,SAAS,CAAC;AAAA,IAC7rB,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,eAAe,CAAC;AACtF,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,+CAA+C,IAAI,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,gBAAgB,CAAC;AAAA,MAClJ;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU;AACb,QAAG,WAAW,oBAAoB,IAAI,eAAe;AACrD,QAAG,UAAU;AACb,QAAG,cAAc,CAAC,IAAI,eAAe,IAAI,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,kBAAkB,0BAA0B,cAAmB,iBAAiB,kBAAqB,iBAAoB,qBAAwB,yBAAyB,kBAAkB,kBAAqB,qBAAqB,aAAgB,iBAAoB,SAAS,gBAAmB,mBAAsB,4BAAgC,eAAe;AAAA,IAC1X,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuCV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,kBAAkB,0BAA0B,cAAc,kBAAkB,kBAAkB,kBAAkB,aAAa,cAAc;AAAA,IACvJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,eAAe,IAAI,aAAa;AAAA,EAChC,cAAc;AACZ,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,SAAS,CAAC,KAAK;AACpB,WAAK,aAAa,KAAK,KAAK,MAAM;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,wBAAwB,EAAE,CAAC;AAAA,IAClD,WAAW,CAAC,GAAG,2BAA2B;AAAA,IAC1C,UAAU;AAAA,IACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,sDAAsD;AACpF,iBAAO,IAAI,YAAY;AAAA,QACzB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,QAAQ,QAAQ;AAClC,QAAG,YAAY,sCAAsC,CAAC,IAAI,aAAa,IAAI,WAAW,IAAI,EAAE,uCAAuC,CAAC,IAAI,aAAa,IAAI,WAAW,KAAK,EAAE,oCAAoC,IAAI,SAAS;AAAA,MAC9N;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,IACb;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,8CAA8C;AAAA,QAC9C,+CAA+C;AAAA,QAC/C,4CAA4C;AAAA,QAC5C,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,aAAa;AAAA,EACb,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,WAAW,CAAC,GAAG,sBAAsB;AAAA,IACrC,UAAU;AAAA,IACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,IAAI,YAAY,IAAI;AAAA,MACrD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,IACd;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,2BAA2B;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,mBAAmB,CAAC;AAAA,EACpB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,gBAAgB,IAAI,aAAa;AAAA,EACjC,gBAAgB,SAAS;AACvB,SAAK,UAAU;AACf,SAAK,cAAc,KAAK,OAAO;AAAA,EACjC;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,IAClC,WAAW,CAAC,GAAG,qBAAqB;AAAA,IACpC,QAAQ;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,eAAe;AAAA,MACf,OAAO;AAAA,MACP,cAAc;AAAA,MACd,kBAAkB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,gBAAgB,GAAG,CAAC,eAAe,IAAI,GAAG,yCAAyC,WAAW,cAAc,iBAAiB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,eAAe,IAAI,GAAG,iBAAiB,WAAW,cAAc,iBAAiB,GAAG,CAAC,eAAe,IAAI,eAAe,cAAc,GAAG,4BAA4B,GAAG,gBAAgB,GAAG,CAAC,UAAU,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,0BAA0B,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,gBAAgB,IAAI,GAAG,OAAO,CAAC;AAAA,IAClf,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,SAAS,CAAC,EAAE,GAAG,kDAAkD,GAAG,GAAG,OAAO,CAAC;AAAA,MAC1J;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,eAAe,IAAI,EAAE;AAC1C,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,mBAAmB,IAAI,EAAE;AAAA,MAChD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAgB,iBAAoB,SAAS,kBAAqB,qBAAqB,kBAAqB,iBAAoB,qBAAwB,qBAAwB,yBAAyB,cAAmB,eAAe;AAAA,IAC1P,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA6BV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,aAAa,kBAAkB,kBAAkB,YAAY;AAAA,IACzE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,iBAAiB,CAAC,UAAU,WAAW,IAAI;AAAA,EAC3C,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB;AAClB,WAAK,OAAO,KAAK,eAAe,QAAQ,QAAQ,MAAM;AACtD,WAAK,SAAS,KAAK,eAAe,QAAQ,SAAS,MAAM;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,IAChC,WAAW,CAAC,GAAG,0BAA0B;AAAA,IACzC,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,iBAAiB;AAAA,IACnB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,UAAU,YAAY,GAAG,8BAA8B,GAAG,QAAQ,GAAG,CAAC,UAAU,cAAc,GAAG,gCAAgC,GAAG,QAAQ,GAAG,CAAC,UAAU,YAAY,GAAG,4BAA4B,GAAG,CAAC,UAAU,cAAc,GAAG,8BAA8B,CAAC;AAAA,IAC3Y,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,eAAe,CAAC;AACvF,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC5C,QAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,WAAW,CAAC,EAAE,GAAG,gDAAgD,GAAG,GAAG,WAAW,CAAC;AAC1J,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU;AACb,QAAG,WAAW,oBAAoB,IAAI,eAAe;AACrD,QAAG,UAAU;AACb,QAAG,YAAY,gCAAgC,IAAI,UAAU,IAAI,IAAI;AACrE,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,OAAO,IAAI,EAAE;AAClC,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,SAAS,IAAI,EAAE;AAAA,MACtC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAmB,iBAAiB,gBAAgB;AAAA,IACnE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,cAAc,gBAAgB;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW,IAAI,QAAQ;AAAA,EACvB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,eAAe;AAAA,EACf,UAAU;AAAA,EACV,iBAAiB,UAAU;AACzB,SAAK,SAAS,SAAS,KAAK,WAAW,eAAe,QAAQ,QAAQ;AAAA,EACxE;AAAA,EACA,kBAAkB,WAAW;AAC3B,SAAK,SAAS,SAAS,KAAK,WAAW,eAAe,SAAS,SAAS;AAAA,EAC1E;AAAA,EACA,gBAAgB,cAAc;AAC5B,SAAK,YAAY,cAAc,gCAAgC;AAAA,EACjE;AAAA,EACA,cAAc,YAAY;AACxB,SAAK,YAAY,YAAY,8BAA8B;AAAA,EAC7D;AAAA,EACA,YAAY,MAAM,WAAW;AAE3B,SAAK,SAAS,YAAY,KAAK,WAAW,eAAe,SAAS;AAClE,QAAI,MAAM;AACR,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,SAAS;AAAA,IACjE;AAAA,EACF;AAAA,EACA,YAAY,UAAU,YAAY;AAChC,SAAK,WAAW;AAChB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,KAAK;AAC1B,SAAK,cAAc,KAAK;AACxB,SAAK,aAAa,KAAK,WAAW,MAAM,KAAK,WAAW;AACxD,SAAK,cAAc,KAAK,YAAY,MAAM,KAAK,YAAY;AAC3D,SAAK,cAAc,KAAK,WAAW;AACnC,SAAK,eAAe,KAAK,YAAY;AACrC,SAAK,UAAU,KAAK,eAAe,KAAK;AACxC,UAAM,aAAa,WAAS;AAC1B,UAAI,OAAO,UAAU,YAAY,UAAU,IAAI;AAC7C,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAK,iBAAiB,WAAW,KAAK,MAAM,CAAC;AAC7C,SAAK,kBAAkB,WAAW,KAAK,OAAO,CAAC;AAC/C,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,CAAC;AAAA,EAChI;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,WAAW,EAAE,GAAG,CAAC,MAAM,WAAW,EAAE,GAAG,CAAC,MAAM,UAAU,EAAE,GAAG,CAAC,MAAM,UAAU,EAAE,CAAC;AAAA,IACpG,UAAU;AAAA,IACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,YAAY,IAAI,UAAU,WAAW,IAAI;AACxD,QAAG,YAAY,4BAA4B,IAAI,YAAY,EAAE,2BAA2B,IAAI,WAAW;AAAA,MACzG;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,oCAAoC;AAAA,QACpC,mCAAmC;AAAA,QACnC,oBAAoB;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,iBAAiB,IAAI,cAAc,CAAC;AAAA,EACpC,iBAAiB,IAAI,cAAc,CAAC;AAAA,EACpC,cAAc,IAAI,cAAc,CAAC;AAAA,EACjC,cAAc,IAAI,cAAc,CAAC;AAAA,EACjC,eAAe,IAAI,cAAc,CAAC;AAAA,EAClC,aAAa,IAAI,cAAc,CAAC;AAAA,EAChC,eAAe,IAAI,cAAc,CAAC;AAAA,EAClC,aAAa,IAAI,cAAc,CAAC;AAAA,EAChC,YAAY,IAAI,cAAc,CAAC;AAAA,EAC/B,yBAAyB,IAAI,gBAAgB,CAAC,CAAC;AAAA,EAC/C,sBAAsB,IAAI,gBAAgB,CAAC,CAAC;AAAA,EAC5C,uBAAuB,cAAc,CAAC,KAAK,qBAAqB,KAAK,sBAAsB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,aAAa,WAAW,MAAM,YAAY,SAAS,cAAc,WAAW,CAAC;AAAA,EACtL,qBAAqB,IAAI,cAAc,CAAC;AAAA,EACxC,yBAAyB;AAAA;AAAA,IACzB,KAAK;AAAA,IAAsB,cAAc,CAAC,KAAK,oBAAoB,KAAK,oBAAoB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,WAAW,WAAW,MAAM;AAEpI,UAAI,UAAU,WAAW,YAAY,QAAQ;AAC3C,eAAO,UAAU,IAAI,CAAC,OAAO,UAAU;AACrC,cAAI,UAAU,OAAO;AACnB,mBAAO,YAAY,KAAK,KAAK;AAAA,UAC/B,OAAO;AACL,mBAAO,YAAY,KAAK,KAAK;AAAA,UAC/B;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC,CAAC;AAAA,EAAC;AAAA,EACH,uBAAuB,IAAI,cAAc,CAAC;AAAA,EAC1C,uBAAuB,KAAK,mBAAmB,KAAK,IAAI,UAAQ,KAAK,IAAI,WAAS,SAAS,OAAO,EAAE,CAAC,CAAC,CAAC;AAAA,EACvG,qBAAqB,IAAI,cAAc,CAAC;AAAA,EACxC,iBAAiB,UAAU;AACzB,SAAK,eAAe,KAAK,QAAQ;AAAA,EACnC;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,eAAe,KAAK,QAAQ;AAAA,EACnC;AAAA,EACA,cAAc,OAAO;AACnB,SAAK,YAAY,KAAK,KAAK;AAAA,EAC7B;AAAA,EACA,cAAc,YAAY;AACxB,SAAK,YAAY,KAAK,UAAU;AAAA,EAClC;AAAA,EACA,eAAe,aAAa;AAC1B,SAAK,aAAa,KAAK,WAAW;AAAA,EACpC;AAAA,EACA,oBAAoB,aAAa;AAC/B,SAAK,oBAAoB,KAAK,WAAW;AAAA,EAC3C;AAAA,EACA,YAAY,UAAU;AACpB,QAAI,cAAc;AAClB,aAAS,QAAQ,QAAM;AACrB,qBAAe,GAAG,WAAW,CAAC,GAAG,WAAW,GAAG,WAAW,CAAC,GAAG,WAAW;AAAA,IAC3E,CAAC;AACD,UAAM,aAAa,SAAS,IAAI,UAAQ,KAAK,OAAO;AACpD,SAAK,aAAa,KAAK,WAAW;AAClC,SAAK,uBAAuB,KAAK,UAAU;AAAA,EAC7C;AAAA,EACA,uBAAuB,UAAU;AAC/B,UAAM,aAAa,CAAC;AACpB,aAAS,QAAQ,QAAM;AACrB,YAAM,SAAS,GAAG,WAAW,CAAC,GAAG,WAAW,GAAG,WAAW,CAAC,GAAG,WAAW;AACzE,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,mBAAW,KAAK,eAAe,CAAC,EAAE;AAAA,MACpC;AAAA,IACF,CAAC;AACD,SAAK,qBAAqB,KAAK,UAAU;AAAA,EAC3C;AAAA,EACA,mBAAmB,iBAAiB;AAClC,SAAK,mBAAmB,KAAK,gBAAgB,IAAI,WAAS,GAAG,KAAK,IAAI,CAAC;AAAA,EACzE;AAAA,EACA,aAAa,WAAW;AACtB,SAAK,WAAW,KAAK,SAAS;AAAA,EAChC;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,UAAU,KAAK,QAAQ;AAAA,EAC9B;AAAA,EACA,UAAU,SAAS,SAAS;AAC1B,UAAM,oBAAoB,CAAC,EAAE,WAAW;AACxC,QAAI,CAAC,mBAAmB;AACtB,WAAK,mBAAmB,CAAC,CAAC;AAAA,IAC5B;AACA,SAAK,mBAAmB,KAAK,iBAAiB;AAAA,EAChD;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,gBAAgB,CAAC,CAAC,OAAO,qBAAqB;AAAA,IAC5C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,GAAG,iBAAiB,GAAG,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,iBAAiB,GAAG,YAAY,EAAE,CAAC;AAAA,IACxG,UAAU;AAAA,IACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,kBAAkB,IAAI,aAAa;AAAA,MACpD;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,0BAA0B;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,WAAW,IAAI,QAAQ;AAAA,EACvB,aAAa,IAAI,gBAAgB,CAAC;AAAA,EAClC,mBAAmB,IAAI,gBAAgB,IAAI;AAAA,EAC3C,YAAY,IAAI,gBAAgB,EAAE;AAAA,EAClC,cAAc,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACpC,sBAAsB,IAAI,gBAAgB,CAAC,CAAC;AAAA,EAC5C,qBAAqB,KAAK,WAAW,KAAK,qBAAqB,CAAC;AAAA,EAChE,oBAAoB,KAAK,UAAU,KAAK,qBAAqB,CAAC;AAAA,EAC9D,sBAAsB,IAAI,gBAAgB,CAAC,CAAC;AAAA,EAC5C,eAAe,cAAc,CAAC,KAAK,oBAAoB,KAAK,mBAAmB,KAAK,mBAAmB,CAAC,EAAE,KAAK,aAAa,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,UAAU,UAAU,OAAO;AAAA,IACnL;AAAA,IACA;AAAA,IACA,MAAM,WAAW,OAAO,UAAQ,KAAK,MAAM,EAAE,IAAI,WAAS;AAAA,MACxD,KAAK,KAAK;AAAA,MACV,OAAO,KAAK;AAAA,IACd,EAAE;AAAA,IACF,QAAQ,WAAW,OAAO,UAAQ,KAAK,QAAQ,EAAE,IAAI,WAAS;AAAA,MAC5D,KAAK,KAAK;AAAA,MACV,OAAO,KAAK;AAAA,IACd,EAAE;AAAA,EACJ,EAAE,CAAC;AAAA,EACH,uBAAuB,cAAc,CAAC,KAAK,aAAa,KAAK,mBAAmB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,YAAY,kBAAkB,MAAM;AAChI,QAAI,sBAAsB,CAAC,GAAG,UAAU;AACxC,UAAM,uBAAuB,mBAAmB,OAAO,UAAQ;AAC7D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,UAAU,gBAAgB,QAAQ,gBAAgB,UAAa,MAAM,QAAQ,WAAW,KAAK,YAAY,WAAW;AAC1H,aAAO,CAAC,WAAW,OAAO,aAAa;AAAA,IACzC,CAAC;AACD,eAAW,QAAQ,sBAAsB;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,4BAAsB,oBAAoB,OAAO,UAAQ,SAAS,aAAa,IAAI,CAAC;AAAA,IACtF;AACA,UAAM,qBAAqB,mBAAmB,OAAO,UAAQ,KAAK,cAAc,QAAQ,OAAO,KAAK,WAAW,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,EAAE,eAAe,CAAC,EAAE,YAAY;AAC3K,QAAI,mBAAmB,QAAQ;AAC7B,0BAAoB,KAAK,CAAC,SAAS,YAAY;AAC7C,mBAAW,QAAQ,oBAAoB;AACrC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,UAAU,WAAW;AACvB,kBAAM,gBAAgB,OAAO,SAAS,SAAS,SAAS;AACxD,gBAAI,kBAAkB,GAAG;AACvB,qBAAO,cAAc,WAAW,gBAAgB,CAAC;AAAA,YACnD;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC,CAAC;AAAA,EACF,iCAAiC,cAAc,CAAC,KAAK,oBAAoB,KAAK,mBAAmB,KAAK,oBAAoB,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,WAAS;AAC1K,UAAM,CAAC,WAAW,UAAU,UAAU,IAAI;AAC1C,UAAM,eAAe,KAAK,KAAK,WAAW,SAAS,QAAQ,KAAK;AAChE,WAAO,aAAa;AAAA,EACtB,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,UAAU,UAAU,MAAM,WAAW,OAAO,YAAY,KAAK,UAAU,YAAY,QAAQ,CAAC,CAAC;AAAA,EAClH,yBAAyB,KAAK,iBAAiB,KAAK,UAAU,gBAAc,aAAa,KAAK,iCAAiC,KAAK,oBAAoB,CAAC;AAAA,EACzJ,SAAS,KAAK,iBAAiB,KAAK,UAAU,gBAAc,aAAa,KAAK,uBAAuB,KAAK,WAAW,GAAG,IAAI,UAAQ,KAAK,MAAM,GAAG,qBAAqB,CAAC;AAAA,EACxK,eAAe,MAAM;AACnB,SAAK,UAAU,KAAK,IAAI;AAAA,EAC1B;AAAA,EACA,sBAAsB,YAAY;AAChC,SAAK,iBAAiB,KAAK,UAAU;AAAA,EACvC;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,WAAW,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA,iBAAiB,MAAM;AACrB,SAAK,YAAY,KAAK,IAAI;AAAA,EAC5B;AAAA,EACA,yBAAyB,MAAM;AAC7B,SAAK,oBAAoB,KAAK,IAAI;AAAA,EACpC;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,oBAAmB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,IAAI,UAAU,oBAAoB;AAC5C,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,WAAW;AACT,SAAK,mBAAmB,oBAAoB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC3F,UAAI,KAAK,QAAQ;AACf,aAAK,QAAQ,CAAC,GAAG,MAAM;AACrB,cAAI,EAAE,UAAU,KAAK,eAAe;AAClC,gBAAI,CAAC,EAAE,SAAS;AACd,mBAAK,SAAS,SAAS,KAAK,GAAG,eAAe,WAAW,MAAM;AAAA,YACjE,OAAO;AACL,mBAAK,SAAS,SAAS,KAAK,GAAG,eAAe,WAAW,OAAO;AAAA,YAClE;AACA,iBAAK,SAAS,SAAS,KAAK,GAAG,eAAe,SAAS,CAAC;AACxD,gBAAI,CAAC,GAAG,UAAU;AAChB,mBAAK,SAAS,SAAS,KAAK,GAAG,eAAe,QAAQ,OAAO,EAAE,KAAK,IAAI;AAAA,YAC1E,OAAO;AACL,mBAAK,SAAS,SAAS,KAAK,GAAG,eAAe,QAAQ,OAAO,EAAE,KAAK,IAAI;AAAA,YAC1E;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,kBAAkB,CAAC;AAAA,EAC7K;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,iBAAiB,EAAE,GAAG,CAAC,MAAM,iBAAiB,EAAE,CAAC;AAAA,IACpE,QAAQ;AAAA,MACN,eAAe;AAAA,IACjB;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,eAAe;AAAA,EACf,kBAAkB,IAAI,aAAa;AAAA,EACnC,iBAAiB,IAAI,aAAa;AAAA,EAClC,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,gBAAgB,SAAS;AACvB,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,OAAO;AAAA,EACnC;AAAA,EACA,eAAe,QAAQ;AACrB,SAAK,WAAW;AAChB,SAAK,eAAe,KAAK,MAAM;AAAA,EACjC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,gBAAgB,WAAS,SAAS,MAAM,eAAe,MAAM,iBAAiB;AACpF,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,cAAc;AAChB,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,gBAAgB;AAClB,WAAK,0BAA0B;AAAA,IACjC;AACA,QAAI,cAAc,QAAQ,KAAK,CAAC,KAAK,uBAAuB;AAC1D,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,cAAc,SAAS,KAAK,CAAC,KAAK,yBAAyB;AAC7D,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,aAAa,EAAE,GAAG,CAAC,MAAM,cAAc,EAAE,GAAG,CAAC,MAAM,mBAAmB,EAAE,GAAG,CAAC,MAAM,gBAAgB,EAAE,GAAG,CAAC,MAAM,YAAY,EAAE,GAAG,CAAC,MAAM,gBAAgB,EAAE,GAAG,CAAC,MAAM,kBAAkB,EAAE,CAAC;AAAA,IAC1M,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,8BAA8B,IAAI,gBAAgB,IAAI,eAAe,CAAC,EAAE,8BAA8B,IAAI,cAAc;AAAA,MACzI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,MAClE,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MACxE,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,cAAc;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAOP;AAAA,IACP,oBAAoBF;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,eAAe,IAAI,GAAG,cAAc,WAAW,iBAAiB,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,wBAAwB,IAAI,GAAG,UAAU,WAAW,GAAG,CAAC,wBAAwB,IAAI,GAAG,gBAAgB,UAAU,WAAW,GAAG,CAAC,eAAe,IAAI,GAAG,iBAAiB,cAAc,WAAW,iBAAiB,CAAC;AAAA,IAC5U,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,2CAA2C,GAAG,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,SAAS,CAAC;AAChI,QAAG,aAAa,CAAC;AAAA,MACnB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,gBAAgB,IAAI,eAAe,IAAI,IAAI,EAAE;AAClE,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,iBAAiB,IAAI,EAAE;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,sBAAsB,4BAA4B,kBAAkB,kBAAqB,qBAAqB,aAAgB,iBAAoB,OAAO;AAAA,IACxK,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0BV,MAAM;AAAA,QACJ,sCAAsC;AAAA,QACtC,sCAAsC;AAAA,MACxC;AAAA,MACA,SAAS,CAAC,sBAAsB,4BAA4B,kBAAkB,kBAAkB,WAAW;AAAA,IAC7G,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAA0B;AAChC,IAAI,sBAAsB,MAAM;AAC9B,MAAI;AACJ,MAAI,iCAAiC,CAAC;AACtC,MAAI,sCAAsC,CAAC;AAC3C,SAAO,MAAMU,oBAAmB;AAAA,IAC9B,OAAO;AACL,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,uBAAO,OAAO,IAAI,IAAI;AAC1F,qCAA+B,CAAC,WAAW,CAAC;AAC5C,mBAAa,MAAM,MAAM,8BAA8B;AAAA,QACrD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,sBAAsB;AAAA,UAClC,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,mBAAmB;AAAA,UACzB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,gCAAgC,mCAAmC;AACtE,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU;AAAA,QAC1D,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,oBAAoB,IAAI,QAAQ;AAAA,IAChC,sBAAsB,IAAI,QAAQ;AAAA,IAClC,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,iBAAiB,CAAC,UAAU,WAAW,IAAI;AAAA,IAC3C,mBAAmB,IAAI,QAAQ;AAAA,IAC/B,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB;AAAA,IACA,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,mBAAmB,kBAAkB,MAAM,gCAAgC,CAAC,UAAU,WAAW,IAAI,CAAC;AAAA,IACtG,aAAa,kBAAkB,MAAM,mCAAmC,GAAG,CAAC;AAAA,IAC5E,WAAW;AAAA,IACX,aAAa;AAAA,IACb,aAAa;AAAA,IACb,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,kBAAkB,IAAI,aAAa;AAAA,IACnC,oBAAoB,IAAI,aAAa;AAAA,IACrC,iBAAiB,IAAI,aAAa;AAAA,IAClC,qBAAqB,gBAAgB,SAAS;AAC5C,YAAM,QAAQ,eAAe,QAAQ,OAAO;AAC5C,UAAI,UAAU,eAAe,SAAS,GAAG;AACvC,eAAO,eAAe,CAAC;AAAA,MACzB,OAAO;AACL,eAAO,eAAe,QAAQ,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,IACA,aAAa,OAAO;AAClB,WAAK,iBAAiB,KAAK,KAAK;AAAA,IAClC;AAAA,IACA,iBAAiB;AACf,UAAI,KAAK,cAAc,MAAM;AAC3B,aAAK,aAAa,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,IACA,oBAAoB,OAAO;AACzB,WAAK,eAAe,KAAK,KAAK;AAC9B,WAAK,gBAAgB;AACrB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,IACA,qBAAqB;AACnB,WAAK,oBAAoB,KAAK;AAAA,IAChC;AAAA,IACA,YAAY,iBAAiB,MAAM,KAAK,QAAQ,UAAU;AACxD,WAAK,kBAAkB;AACvB,WAAK,OAAO;AACZ,WAAK,MAAM;AACX,WAAK,SAAS;AACd,WAAK,WAAW;AAAA,IAClB;AAAA,IACA,WAAW;AACT,8BAAwB,KAAK,KAAK,eAAe,OAAO,EAAE,KAAK,OAAO,MAAM,KAAK,UAAU,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACtI,cAAM,YAAY,KAAK,qBAAqB,KAAK,gBAAgB,KAAK,SAAS;AAC/E,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,aAAa,SAAS;AAC3B,eAAK,kBAAkB,KAAK,IAAI;AAAA,QAClC,CAAC;AAAA,MACH,CAAC;AACD,WAAK,iBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AACtE,YAAI,KAAK,cAAc,OAAO;AAC5B,eAAK,YAAY;AACjB,eAAK,kBAAkB,KAAK,KAAK;AAAA,QACnC;AACA,aAAK,mBAAmB;AACxB,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,IACA,YAAY,SAAS;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,kBAAkB;AACpB,YAAI,KAAK,oBAAoB,KAAK,iBAAiB,QAAQ;AACzD,eAAK,iBAAiB,KAAK;AAAA,QAC7B;AAAA,MACF;AACA,UAAI,aAAa;AACf,aAAK,YAAY,KAAK;AACtB,aAAK,aAAa,KAAK,WAAW;AAAA,MACpC;AACA,UAAI,YAAY;AACd,aAAK,sBAAsB;AAAA,MAC7B;AACA,UAAI,cAAc;AAChB,aAAK,wBAAwB;AAAA,MAC/B;AACA,YAAM,gBAAgB,WAAS,SAAS,MAAM,eAAe,MAAM,iBAAiB;AACpF,WAAK,cAAc,WAAW,KAAK,cAAc,QAAQ,MAAM,CAAC,KAAK,qBAAqB;AACxF,aAAK,aAAa;AAAA,MACpB;AACA,UAAI,cAAc,SAAS,KAAK,CAAC,KAAK,uBAAuB;AAC3D,aAAK,eAAe;AAAA,MACtB;AACA,WAAK,aAAa,qBAAqB,KAAK,cAAc;AACxD,cAAM,cAAc,KAAK,UAAU,OAAO,UAAQ,KAAK,SAAS,EAAE,IAAI,UAAQ,KAAK,KAAK;AACxF,aAAK,gBAAgB,KAAK,mBAAmB,cAAc,YAAY,CAAC,KAAK;AAAA,MAC/E;AACA,UAAI,YAAY,cAAc,kBAAkB,WAAW;AACzD,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,aAAO,KAAK,qBAAqBA,qBAAuB,kBAAqB,eAAe,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IAC5P;AAAA,IACA,OAAO,OAAyB,kBAAkB;AAAA,MAChD,MAAMA;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,eAAe,EAAE,GAAG,CAAC,MAAM,YAAY,EAAE,GAAG,CAAC,MAAM,eAAe,EAAE,GAAG,CAAC,MAAM,aAAa,EAAE,GAAG,CAAC,MAAM,cAAc,EAAE,GAAG,CAAC,MAAM,gBAAgB,EAAE,GAAG,CAAC,MAAM,kBAAkB,EAAE,CAAC;AAAA,MACrM,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gCAAgC,IAAI,UAAU,EAAE,yBAAyB,IAAI,cAAc,aAAa,IAAI,cAAc,QAAQ;AAAA,QACnJ;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,QAC5D,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,QAClE,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MAC1E;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,MAClB;AAAA,MACA,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,oBAAoB;AAAA,MAC7E,OAAOP;AAAA,MACP,oBAAoBE;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,mBAAmB,iBAAiB,gBAAgB,kBAAkB,cAAc,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,gBAAgB,mBAAmB,iBAAiB,gBAAgB,kBAAkB,cAAc,GAAG,CAAC,GAAG,aAAa,kBAAkB,iBAAiB,CAAC;AAAA,MAC9X,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgBD,IAAG;AACtB,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,mBAAmB,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QAC3jB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,gBAAgB,IAAI,iBAAiB,IAAI,CAAC;AAAA,QACjE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,wBAAwB,kBAAkB,uBAAuB;AAAA,MAChF,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF,GAAG;AAAA,CACF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA+BV,MAAM;AAAA,QACJ,wCAAwC;AAAA,QACxC,iCAAiC;AAAA,MACnC;AAAA,MACA,WAAW,CAAC,gBAAgB;AAAA,MAC5B,SAAS,CAAC,wBAAwB,kBAAkB,uBAAuB;AAAA,IAC7E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,WAAW,IAAI,QAAQ;AAAA,EACvB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY,UAAU,YAAY;AAChC,SAAK,WAAW;AAChB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,SAAS;AACtB,YAAM,MAAM,KAAK,WAAW,KAAK;AACjC,UAAI,CAAC,MAAM,GAAG,GAAG;AACf,aAAK,SAAS,aAAa,KAAK,WAAW,eAAe,WAAW,GAAG,GAAG,EAAE;AAAA,MAC/E,OAAO;AACL,aAAK,SAAS,gBAAgB,KAAK,WAAW,eAAe,SAAS;AAAA,MACxE;AAAA,IACF;AACA,QAAI,WAAW,SAAS;AACtB,YAAM,MAAM,KAAK,WAAW,KAAK;AACjC,UAAI,CAAC,MAAM,GAAG,GAAG;AACf,aAAK,SAAS,aAAa,KAAK,WAAW,eAAe,WAAW,GAAG,GAAG,EAAE;AAAA,MAC/E,OAAO;AACL,aAAK,SAAS,gBAAgB,KAAK,WAAW,eAAe,SAAS;AAAA,MACxE;AAAA,IACF;AACA,QAAI,WAAW,SAAS;AACtB,WAAK,SAAS,KAAK;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,CAAC;AAAA,EAChI;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,CAAC;AAAA,IAClB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,eAAe,CAAC;AAAA,EAChB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,qBAAqB;AAAA,EACrB,kBAAkB,IAAI,aAAa;AAAA,EACnC,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,gBAAgB,SAAS;AACvB,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,OAAO;AAAA,EACnC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,gBAAgB,WAAS,SAAS,MAAM,eAAe,MAAM,iBAAiB;AACpF,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,cAAc;AAChB,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,gBAAgB;AAClB,WAAK,0BAA0B;AAAA,IACjC;AACA,QAAI,cAAc,YAAY,KAAK,CAAC,KAAK,uBAAuB;AAC9D,WAAK,qBAAqB;AAAA,IAC5B;AACA,QAAI,cAAc,SAAS,KAAK,CAAC,KAAK,yBAAyB;AAC7D,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,gBAAgB,EAAE,GAAG,CAAC,MAAM,aAAa,EAAE,GAAG,CAAC,MAAM,kBAAkB,EAAE,GAAG,CAAC,MAAM,sBAAsB,EAAE,CAAC;AAAA,IAC/H,WAAW,CAAC,GAAG,4BAA4B;AAAA,IAC3C,QAAQ;AAAA,MACN,cAAc;AAAA,MACd,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MACxE,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,IACtF;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,oBAAoBJ;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,WAAW,YAAY,iBAAiB,SAAS,oBAAoB,gBAAgB,kBAAkB,CAAC;AAAA,IACtI,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,sBAAsB,CAAC;AAC5C,QAAG,WAAW,iBAAiB,SAAS,4EAA4E,QAAQ;AAC1H,iBAAO,IAAI,gBAAgB,MAAM;AAAA,QACnC,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,aAAa,CAAC;AAAA,MACnB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,IAAI,SAAS,EAAE,YAAY,IAAI,UAAU,EAAE,iBAAiB,IAAI,eAAe,EAAE,SAAS,IAAI,OAAO,EAAE,oBAAoB,IAAI,YAAY,EAAE,gBAAgB,IAAI,cAAc,EAAE,oBAAoB,IAAI,kBAAkB;AAAA,MACtP;AAAA,IACF;AAAA,IACA,cAAc,CAAC,yBAAyB;AAAA,IACxC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,yBAAyB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,UAAU;AAAA,EACV,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,WAAW,EAAE,GAAG,CAAC,MAAM,WAAW,EAAE,CAAC;AAAA,IACxD,UAAU;AAAA,IACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,cAAc,IAAI,OAAO;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,sBAAsB;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,aAAa;AAAA,EACb,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,cAAc,EAAE,GAAG,CAAC,MAAM,cAAc,EAAE,CAAC;AAAA,IAC9D,UAAU;AAAA,IACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,2BAA2B,IAAI,UAAU;AAAA,MAC1D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,IAC9D;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,mCAAmC;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AAAA,EACd,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,eAAe,EAAE,GAAG,CAAC,MAAM,eAAe,EAAE,CAAC;AAAA,IAChE,UAAU;AAAA,IACV,cAAc,SAAS,sCAAsC,IAAI,KAAK;AACpE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,cAAc,IAAI,cAAc,cAAc,EAAE;AAAA,MACjE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,IACjE;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,sBAAsB;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,iBAAiB,CAAC;AAAA,EAClB,UAAU;AAAA,EACV,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,oBAAoB,EAAE,CAAC;AAAA,IAC7C,UAAU;AAAA,IACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,IAAI,WAAW,EAAE,SAAS,IAAI,OAAO,EAAE,aAAa,IAAI,UAAU,SAAS,IAAI;AAC9G,QAAG,YAAY,mBAAmB,IAAI,OAAO;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,IACP,oBAAoBA;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,SAAS,UAAU,CAAC;AAAA,IAC5G,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,UAAU,EAAE,GAAG,gDAAgD,GAAG,GAAG,SAAS,CAAC,EAAE,GAAG,gDAAgD,GAAG,GAAG,eAAe,CAAC;AACjO,QAAG,aAAa,CAAC;AACjB,QAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,SAAS,CAAC;AAAA,MACnF;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,eAAe,SAAS,IAAI,IAAI,EAAE;AACvD,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,gBAAgB,IAAI,EAAE;AAC3C,QAAG,UAAU;AACb,QAAG,WAAW,oBAAoB,IAAI,eAAe;AACrD,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,gBAAgB,IAAI,EAAE;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAgB;AAAA,IAC/B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBV,MAAM;AAAA,QACJ,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,MACvB;AAAA,MACA,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa,IAAI,gBAAgB,IAAI;AAAA,EACrC,qBAAqB,IAAI,gBAAgB,KAAK;AAAA,EAC9C,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,qBAAqB,UAAU;AACzC,SAAK,sBAAsB;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AACT,QAAI,KAAK,qBAAqB;AAC5B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,yBAAmB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,kBAAkB;AACnF,iBAAW,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,UAAU;AAAA,IACrE;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,oBAAoB,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AACtF,WAAK,SAAS,aAAa,KAAK,UAAU,eAAe,WAAW,GAAG,KAAK,EAAE;AAAA,IAChF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA6B,kBAAkB,mBAAmB,GAAM,kBAAqB,SAAS,CAAC;AAAA,EAC1I;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,sBAAsB,EAAE,GAAG,CAAC,MAAM,YAAY,EAAE,CAAC;AAAA,IACpE,WAAW,SAAS,+BAA+B,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,MAClE;AAAA,IACF;AAAA,IACA,OAAO;AAAA,IACP,oBAAoBA;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,iBAAiB,gBAAgB,GAAG,CAAC,GAAG,gCAAgC,GAAG,YAAY,UAAU,QAAQ,KAAK,YAAY,UAAU,GAAG,OAAO,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,gCAAgC,GAAG,YAAY,UAAU,QAAQ,KAAK,YAAY,QAAQ,CAAC;AAAA,IAClU,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,QAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,OAAO,CAAC;AAChF,QAAG,OAAO,GAAG,OAAO;AACpB,QAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,MAAM,CAAC;AAC/E,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,MAC3H;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,cAAiB,YAAY,GAAG,GAAG,IAAI,kBAAkB,IAAI,IAAI,CAAC;AAAA,MACvE;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,gBAAgB;AAAA,IAC1C,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBV,SAAS,CAAC,WAAW,gBAAgB;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,cAAc;AAAA,EACd,iBAAiB,CAAC;AAAA,EAClB,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,wBAAwB,CAAC;AAAA,IACtC,WAAW,CAAC,GAAG,qBAAqB;AAAA,IACpC,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACjB;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,mBAAmB,GAAG,CAAC,oBAAoB,IAAI,GAAG,mBAAmB,eAAe,kBAAkB,iBAAiB,eAAe,CAAC;AAAA,IACpJ,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU;AACb,QAAG,WAAW,mBAAmB,IAAI,eAAe,EAAE,eAAe,IAAI,WAAW,EAAE,kBAAkB,IAAI,cAAc,EAAE,iBAAiB,IAAI,aAAa,EAAE,iBAAiB,IAAI,aAAa;AAAA,MACpM;AAAA,IACF;AAAA,IACA,cAAc,CAAC,uBAAuB;AAAA,IACtC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,uBAAuB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,sBAAsB,CAAC;AAAA,EACvB,kBAAkB,IAAI,aAAa;AAAA,EACnC;AAAA,EACA,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,kBAAkB,QAAQ;AACpC,SAAK,mBAAmB;AACxB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,kBAAkB;AAChB,SAAK,gBAAgB,QAAQ,KAAK,UAAU,KAAK,eAAe,CAAC,EAAE,KAAK,UAAU,UAAQ,cAAc,KAAK,QAAQ,EAAE,IAAI,UAAQ,KAAK,iBAAiB,QAAQ,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM;AAC3L,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,MAAM,OAAO,sBAAsB;AACvC,aAAO,KAAK,MAAM,KAAK;AAAA,IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,EAAE,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAOpE,UAAI,KAAK,kBAAkB,UAAU,OAAO,gBAAgB,GAAG;AAC7D,aAAK,gBAAgB,KAAK,IAAI;AAAA,MAChC,OAAO;AACL,aAAK,OAAO,IAAI,MAAM,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,kBAAuB,gBAAgB,GAAM,kBAAqB,MAAM,CAAC;AAAA,EACrI;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,wBAAwB,EAAE,CAAC;AAAA,IAC9C,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB;AAAA,MACrE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,uBAAuB;AAAA,IACtC,QAAQ;AAAA,MACN,qBAAqB;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,iBAAiB,GAAG,WAAW,KAAK,UAAU,KAAK,UAAU,GAAG,CAAC;AAAA,IACjG,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,iBAAiB,GAAG,qCAAqC,GAAG,GAAG,MAAM,GAAM,sBAAsB;AAAA,MACtG;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,mBAAmB;AAAA,MACvC;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,gBAAgB;AAAA,EAChB,aAAa,IAAI,gBAAgB,KAAK;AAAA,EACtC,YAAY,IAAI,gBAAgB,MAAS;AAAA,EACzC,uBAAuB,IAAI,gBAAgB,CAAC,CAAC;AAAA,EAC7C,WAAW,IAAI,QAAQ;AAAA,EACvB,sBAAsB,OAAO,qBAAqB;AAAA,IAChD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc;AACZ,SAAK,gBAAgB,CAAC,CAAC,KAAK;AAC5B,QAAI,KAAK,qBAAqB;AAC5B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,gBAAU,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,SAAS;AACjE,2BAAqB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,oBAAoB;AACvF,iBAAW,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,UAAU;AAAA,IACrE;AAAA,EACF;AAAA,EACA,wBAAwB,iBAAiB;AACvC,SAAK,qBAAqB,mBAAmB,eAAe;AAAA,EAC9D;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,OAAO,CAAC;AAAA,IACrB,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,mBAAmB,IAAI,aAAa;AAAA,MACrD;AAAA,IACF;AAAA,IACA,oBAAoBA;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,sBAAsB,IAAI,GAAG,uBAAuB,GAAG,CAAC,wBAAwB,IAAI,GAAG,qBAAqB,GAAG,CAAC,wBAAwB,IAAI,GAAG,mBAAmB,qBAAqB,GAAG,CAAC,mBAAmB,SAAS,GAAG,iBAAiB,CAAC;AAAA,IACtP,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,yCAAyC,GAAG,CAAC;AAC9D,QAAG,OAAO,GAAG,OAAO;AACpB,QAAG,aAAa,CAAC;AACjB,QAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,MAAM,CAAC;AACvE,QAAG,OAAO,GAAG,OAAO;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,UAAa,YAAY,GAAG,GAAG,IAAI,oBAAoB,KAAK,IAAI,IAAI,OAAO;AAC7F,QAAG,UAAU,CAAC;AACd,QAAG,cAAiB,YAAY,GAAG,GAAG,IAAI,UAAU,IAAI,IAAI,EAAE;AAAA,MAChE;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,sBAAsB,0BAA0B,eAAoB,qBAAqB;AAAA,IACnH,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiBV,MAAM;AAAA,QACJ,2BAA2B;AAAA,MAC7B;AAAA,MACA,SAAS,CAAC,WAAW,sBAAsB,0BAA0B,aAAa;AAAA,IACpF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,CAAC;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,kBAAkB;AAAA,EAClB,cAAc,CAAC;AAAA,EACf,iBAAiB,CAAC;AAAA,EAClB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB;AAAA,EACA,oBAAoB,WAAS;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB,CAAC;AAAA,EAClB,eAAe,CAAC;AAAA,EAChB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,QAAQ,IAAI,QAAQ;AAAA,EACpB,UAAU,IAAI,QAAQ;AAAA,EACtB,WAAW,IAAI,QAAQ;AAAA,EACvB,2BAA2B,QAAQ,OAAO;AACxC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,iBAAiB;AAC1B,UAAM,gBAAgB;AACtB,UAAM,iBAAiB;AACvB,QAAI,gBAAgB,eAAe,gBAAgB,KAAK,OAAO;AAC7D,WAAK,SAAS,YAAY,KAAK,kBAAkB,aAAa;AAC9D,WAAK,SAAS,YAAY,KAAK,kBAAkB,cAAc;AAAA,IACjE,WAAW,eAAe,GAAG;AAC3B,WAAK,SAAS,YAAY,KAAK,kBAAkB,aAAa;AAC9D,WAAK,SAAS,SAAS,KAAK,kBAAkB,cAAc;AAAA,IAC9D,WAAW,gBAAgB,aAAa,aAAa;AACnD,WAAK,SAAS,YAAY,KAAK,kBAAkB,cAAc;AAC/D,WAAK,SAAS,SAAS,KAAK,kBAAkB,aAAa;AAAA,IAC7D,OAAO;AACL,WAAK,SAAS,SAAS,KAAK,kBAAkB,aAAa;AAC3D,WAAK,SAAS,SAAS,KAAK,kBAAkB,cAAc;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,YAAY,UAAU,QAAQ,UAAU,eAAe;AACrD,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,SAAS;AACtB,YAAM,uBAAuB,KAAK,2BAA2B;AAC7D,WAAK,iBAAiB;AAAA,QACpB,WAAW;AAAA,QACX,WAAW,KAAK,WAAW,uBAAuB,WAAW;AAAA,MAC/D;AACA,WAAK,eAAe;AAAA,QAClB,WAAW,KAAK,UAAU,WAAW;AAAA,QACrC,WAAW,KAAK,UAAU,SAAS;AAAA,QACnC,WAAW,KAAK;AAAA,MAClB;AAGA,WAAK,OAAO,kBAAkB,MAAM,KAAK,QAAQ,KAAK,CAAC;AAAA,IACzD;AACA,QAAI,MAAM;AAER,WAAK,OAAO,kBAAkB,MAAM,KAAK,MAAM,KAAK,CAAC;AAAA,IACvD;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,SAAS,WAAW;AAC3B,WAAK,OAAO,kBAAkB,MAAM;AAClC,cAAM,eAAe,KAAK,QAAQ,KAAK,UAAU,IAAI,GAAG,MAAM,CAAC,GAAG,UAAU,MAAM,wBAAwB,KAAK,iBAAiB,eAAe,QAAQ,EAAE,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AACzM,cAAM,UAAU,KAAK,cAAc,UAAU,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC5E,cAAM,QAAQ,KAAK,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC;AACtD,cAAM,gBAAgB,MAAM,cAAc,SAAS,OAAO,KAAK,OAAO,EAAE,KAAK,UAAU,IAAI,GAAG,MAAM,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AAChI,sBAAc,UAAU,MAAM,KAAK,2BAA2B,CAAC;AAC/D,qBAAa,KAAK,OAAO,MAAM,CAAC,CAAC,KAAK,OAAO,CAAC,EAAE,UAAU,MAAM;AAC9D,eAAK,mBAAmB,cAAc,aAAa,KAAK,iBAAiB,cAAc;AACvF,cAAI,KAAK,kBAAkB;AACzB,iBAAK,iBAAiB,cAAc,aAAa,KAAK,iBAAiB,cAAc;AAAA,UACvF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,2BAA2B,IAAI;AACpC,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,oCAAoC,mBAAmB;AAC5E,WAAO,KAAK,qBAAqB,8BAAgC,kBAAqB,SAAS,GAAM,kBAAqB,MAAM,GAAM,kBAAuB,QAAQ,GAAM,kBAAqB,eAAe,CAAC;AAAA,EAClN;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,WAAW,SAAS,kCAAkC,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,GAAG,UAAU;AAClC,QAAG,YAAY,MAAM,GAAG,UAAU;AAClC,QAAG,YAAYM,OAAM,GAAG,UAAU;AAClC,QAAG,YAAY,0BAA0B,GAAG,wBAAwB;AAAA,MACtE;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B,GAAG;AAAA,MACjF;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,qBAAqB;AAAA,IACpC,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,wBAAwB;AAAA,MACxB,qBAAqB;AAAA,IACvB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,GAAG,qBAAqB,GAAG,OAAO,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,oBAAoB,IAAI,eAAe,SAAS,GAAG,WAAW,kBAAkB,iBAAiB,eAAe,GAAG,CAAC,GAAG,kBAAkB,GAAG,OAAO,GAAG,CAAC,GAAG,YAAY,eAAe,eAAe,QAAQ,GAAG,CAAC,GAAG,qBAAqB,GAAG,OAAO,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,oBAAoB,IAAI,eAAe,SAAS,GAAG,WAAW,kBAAkB,iBAAiB,GAAG,CAAC,GAAG,YAAY,eAAe,aAAa,GAAG,CAAC,oBAAoB,IAAI,eAAe,SAAS,GAAG,WAAW,gBAAgB,GAAG,CAAC,GAAG,iBAAiB,mBAAmB,sBAAsB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,oBAAoB,IAAI,eAAe,SAAS,GAAG,WAAW,kBAAkB,eAAe,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,oBAAoB,IAAI,eAAe,SAAS,GAAG,WAAW,kBAAkB,iBAAiB,mBAAmB,eAAe,CAAC;AAAA,IACvjC,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,oDAAoD,GAAG,CAAC,EAAE,GAAG,oDAAoD,GAAG,GAAG,OAAO,CAAC;AAAA,MAClJ;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,UAAU,IAAI,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,yBAAyB,iBAAsB,2BAAgC,iBAAsB,0BAA0B,kBAAkB,gBAAgB;AAAA,IAChL,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmEV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,yBAAyB,iBAAiB,kBAAkB,gBAAgB;AAAA,IACxF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,QAC3B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,QAC/B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC;AAAA,EACA,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,uBAAuB,MAAM,MAAM;AACxC,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,sCAAsC,mBAAmB;AAC9E,WAAO,KAAK,qBAAqB,gCAAkC,kBAAqB,WAAW,CAAC;AAAA,EACtG;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,UAAU,CAAC,iBAAiB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,OAAO,OAAO,SAAS,oCAAoC,mBAAmB;AAC5E,WAAO,KAAK,qBAAqB,8BAA6B;AAAA,EAChE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,UAAU;AAAA,IACV,cAAc,SAAS,yCAAyC,IAAI,KAAK;AACvE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,mBAAmB,IAAI,UAAU,IAAI,EAAE,oBAAoB,IAAI,WAAW,IAAI;AAAA,MAC/F;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,CAAC;AAAA,IACtC,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC;AAAA,MAChL;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,0BAA0B,IAAI,KAAK;AACjD,QAAG,UAAU;AACb,QAAG,WAAW,0BAA0B,IAAI,MAAM;AAAA,MACpD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAqB,+BAA+B;AAAA,IACnE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA,MAIV,MAAM;AAAA,QACJ,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,MAC9B;AAAA,MACA,SAAS,CAAC,cAAc;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAMK,yBAAwB;AAC9B,IAAI,oBAAoB,MAAM;AAC5B,MAAI;AACJ,MAAI,mCAAmC,CAAC;AACxC,MAAI,wCAAwC,CAAC;AAC7C,MAAI;AACJ,MAAI,2BAA2B,CAAC;AAChC,MAAI,gCAAgC,CAAC;AACrC,MAAI;AACJ,MAAI,uBAAuB,CAAC;AAC5B,MAAI,4BAA4B,CAAC;AACjC,MAAI;AACJ,MAAI,kCAAkC,CAAC;AACvC,MAAI,uCAAuC,CAAC;AAC5C,MAAI;AACJ,MAAI,mCAAmC,CAAC;AACxC,MAAI,wCAAwC,CAAC;AAC7C,MAAI;AACJ,MAAI,kCAAkC,CAAC;AACvC,MAAI,uCAAuC,CAAC;AAC5C,MAAI;AACJ,MAAI,yBAAyB,CAAC;AAC9B,MAAI,8BAA8B,CAAC;AACnC,SAAO,MAAMC,kBAAiB;AAAA,IAC5B,OAAO;AACL,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,uBAAO,OAAO,IAAI,IAAI;AAC1F,uCAAiC,CAAC,WAAW,CAAC;AAC9C,+BAAyB,CAAC,WAAW,CAAC;AACtC,2BAAqB,CAAC,WAAW,CAAC;AAClC,sCAAgC,CAAC,WAAW,CAAC;AAC7C,uCAAiC,CAAC,WAAW,CAAC;AAC9C,sCAAgC,CAAC,WAAW,CAAC;AAC7C,6BAAuB,CAAC,WAAW,CAAC;AACpC,mBAAa,MAAM,MAAM,gCAAgC;AAAA,QACvD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,wBAAwB;AAAA,UACpC,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,qBAAqB;AAAA,UAC3B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,kCAAkC,qCAAqC;AAC1E,mBAAa,MAAM,MAAM,wBAAwB;AAAA,QAC/C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,gBAAgB;AAAA,UAC5B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,aAAa;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,0BAA0B,6BAA6B;AAC1D,mBAAa,MAAM,MAAM,oBAAoB;AAAA,QAC3C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,YAAY;AAAA,UACxB,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,SAAS;AAAA,UACf;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,sBAAsB,yBAAyB;AAClD,mBAAa,MAAM,MAAM,+BAA+B;AAAA,QACtD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,uBAAuB;AAAA,UACnC,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,oBAAoB;AAAA,UAC1B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,iCAAiC,oCAAoC;AACxE,mBAAa,MAAM,MAAM,gCAAgC;AAAA,QACvD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,wBAAwB;AAAA,UACpC,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,qBAAqB;AAAA,UAC3B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,kCAAkC,qCAAqC;AAC1E,mBAAa,MAAM,MAAM,+BAA+B;AAAA,QACtD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,uBAAuB;AAAA,UACnC,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,oBAAoB;AAAA,UAC1B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,iCAAiC,oCAAoC;AACxE,mBAAa,MAAM,MAAM,sBAAsB;AAAA,QAC7C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,cAAc;AAAA,UAC1B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,WAAW;AAAA,UACjB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,wBAAwB,2BAA2B;AACtD,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU;AAAA,QAC1D,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgBD;AAAA,IAChB,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,eAAe;AAAA,IACf,UAAU;AAAA,IACV,WAAW;AAAA,IACX,aAAa;AAAA,IACb,oBAAoB,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACvC,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,sBAAsB,WAAS;AAAA,IAC/B,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,UAAU;AAAA,IACV,gBAAgB,CAAC;AAAA,IACjB,SAAS,CAAC;AAAA,IACV,iBAAiB,CAAC;AAAA,IAClB,uBAAuB;AAAA,IACvB,WAAW;AAAA,MACT,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,sBAAsB;AAAA,IACtB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,qBAAqB,kBAAkB,MAAM,kCAAkC,IAAI;AAAA,IACnF,cAAc,kBAAkB,MAAM,qCAAqC,GAAG,kBAAkB,MAAM,0BAA0B,KAAK;AAAA,IACrI,UAAU,kBAAkB,MAAM,6BAA6B,GAAG,kBAAkB,MAAM,sBAAsB,SAAS;AAAA,IACzH,qBAAqB,kBAAkB,MAAM,yBAAyB,GAAG,kBAAkB,MAAM,iCAAiC,KAAK;AAAA,IACvI,sBAAsB,kBAAkB,MAAM,oCAAoC,GAAG,kBAAkB,MAAM,kCAAkC,KAAK;AAAA,IACpJ,qBAAqB,kBAAkB,MAAM,qCAAqC,GAAG,kBAAkB,MAAM,iCAAiC,KAAK;AAAA,IACnJ,YAAY,kBAAkB,MAAM,oCAAoC,GAAG,kBAAkB,MAAM,wBAAwB,KAAK;AAAA,IAChI,oBAAoB,kBAAkB,MAAM,2BAA2B,GAAG,IAAI,aAAa;AAAA,IAC3F,oBAAoB,IAAI,aAAa;AAAA,IACrC,gBAAgB,IAAI,aAAa;AAAA,IACjC,0BAA0B,IAAI,aAAa;AAAA,IAC3C,uBAAuB,IAAI,aAAa;AAAA;AAAA,IAExC,OAAO,CAAC;AAAA,IACR;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,qBAAqB,CAAC;AAAA,IACtB,uBAAuB,CAAC;AAAA,IACxB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,WAAW,IAAI,QAAQ;AAAA,IACvB,gBAAgB,IAAI,gBAAgB,KAAK;AAAA,IACzC,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,yBAAyB;AAAA,IACzB,iBAAiB,MAAM;AACrB,WAAK,mBAAmB,eAAe,IAAI;AAAA,IAC7C;AAAA,IACA,kBAAkB,OAAO;AACvB,WAAK,mBAAmB,gBAAgB,KAAK;AAAA,IAC/C;AAAA,IACA,YAAY,YAAY,kBAAkB,iBAAiB,KAAK,qBAAqB,oBAAoB,gBAAgB;AACvH,WAAK,aAAa;AAClB,WAAK,mBAAmB;AACxB,WAAK,kBAAkB;AACvB,WAAK,MAAM;AACX,WAAK,sBAAsB;AAC3B,WAAK,qBAAqB;AAC1B,WAAK,iBAAiB;AACtB,WAAK,gBAAgB,iCAAiCA,sBAAqB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1H,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,IACA,WAAW;AACT,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,WAAK,MAAM,KAAK,eAAe;AAC/B,WAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,aAAK,MAAM;AACX,aAAK,IAAI,cAAc;AAAA,MACzB,CAAC;AACD,mBAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,aAAa;AACxE,yBAAmB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AACvE,YAAI,cAAc,KAAK,aAAa;AAClC,eAAK,cAAc;AACnB,eAAK,kBAAkB,KAAK,SAAS;AAAA,QACvC;AAAA,MACF,CAAC;AACD,wBAAkB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AACrE,YAAI,aAAa,KAAK,YAAY;AAChC,eAAK,aAAa;AAClB,eAAK,iBAAiB,KAAK,QAAQ;AAAA,QACrC;AAAA,MACF,CAAC;AACD,aAAO,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,MAAM,KAAK,iBAAiB,CAAC,EAAE,UAAU,WAAS;AAC7F,YAAI,UAAU,KAAK,SAAS;AAC1B,eAAK,UAAU;AACf,eAAK,IAAI,aAAa;AAAA,QACxB;AAAA,MACF,CAAC;AACD,6BAAuB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AACtE,aAAK,OAAO;AACZ,aAAK,wBAAwB,KAAK,IAAI;AACtC,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,0BAAoB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AACnE,aAAK,iBAAiB;AACtB,aAAK,qBAAqB,KAAK,IAAI;AACnC,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,qBAAe,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,mBAAiB;AACvE,aAAK,gBAAgB;AACrB,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,oBAAc,CAAC,gBAAgB,WAAW,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,eAAe,UAAU,MAAM;AACrH,aAAK,gBAAgB;AACrB,aAAK,aAAa;AAClB,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,kBAAY,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,gBAAc;AACjE,aAAK,aAAa;AAClB,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,mBAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACnE,aAAK,cAAc;AACnB,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,oBAAc,CAAC,QAAQ,KAAK,aAAa,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,OAAO,YAAY,MAAM,UAAU,KAAK,CAAC,YAAY,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC1J,aAAK,oBAAoB,aAAa,KAAK;AAAA,MAC7C,CAAC;AACD,WAAK,yBAAyB,iBAAiB,UAAU;AACzD,WAAK,oBAAoB,uBAAuB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACtG,aAAK,qBAAqB;AAC1B,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,WAAK,oBAAoB,qBAAqB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACpG,aAAK,uBAAuB;AAC5B,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,IACA,YAAY,SAAS;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,aAAa;AACf,aAAK,mBAAmB,gBAAgB,KAAK,WAAW;AAAA,MAC1D;AACA,UAAI,YAAY;AACd,aAAK,mBAAmB,eAAe,KAAK,UAAU;AAAA,MACxD;AACA,UAAI,QAAQ;AACV,aAAK,SAAS,KAAK,UAAU,CAAC;AAC9B,aAAK,mBAAmB,iBAAiB,KAAK,MAAM;AAAA,MACtD;AACA,UAAI,gBAAgB;AAClB,aAAK,iBAAiB,KAAK,kBAAkB,CAAC;AAC9C,aAAK,mBAAmB,yBAAyB,KAAK,cAAc;AAAA,MACtE;AACA,UAAI,mBAAmB;AACrB,aAAK,mBAAmB,sBAAsB,KAAK,iBAAiB;AAAA,MACtE;AACA,UAAI,UAAU;AACZ,aAAK,mBAAmB;AAAA,MAC1B;AACA,UAAI,eAAe;AACjB,aAAK,oBAAoB,oBAAoB,KAAK,aAAa;AAAA,MACjE;AACA,UAAI,gBAAgB;AAClB,aAAK,cAAc,KAAK,KAAK,cAAc;AAAA,MAC7C;AACA,UAAI,YAAY;AACd,aAAK,oBAAoB,YAAY,KAAK,UAAU;AAAA,MACtD;AACA,WAAK,qBAAqB;AAAA,IAC5B;AAAA,IACA,kBAAkB;AAChB,WAAK,iBAAiB,QAAQ,KAAK,UAAU,EAAE,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM;AACnE,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,MAAM,OAAO,sBAAsB;AACvC,cAAM,iBAAiB,KAAK,UAAU,KAAK,yBAAyB;AACpE,eAAO,KAAK,MAAM,QAAQ,cAAc;AAAA,MAC1C,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,oBAAoB,UAAU;AAC3E,UAAI,KAAK,+BAA+B,KAAK,4BAA4B,0BAA0B;AACjG,aAAK,2BAA2B,KAAK,4BAA4B;AAAA,MACnE;AAAA,IACF;AAAA,IACA,cAAc;AACZ,WAAK,SAAS,KAAK;AACnB,WAAK,SAAS,SAAS;AAAA,IACzB;AAAA,IACA,qBAAqB;AACnB,WAAK,UAAU,KAAK,YAAY,KAAK,SAAS,KAAK;AACnD,WAAK,UAAU,KAAK,YAAY,KAAK,SAAS,KAAK;AACnD,WAAK,oBAAoB,UAAU,KAAK,SAAS,KAAK,OAAO;AAAA,IAC/D;AAAA,IACA,uBAAuB;AACrB,WAAK,iBAAiB,KAAK,sBAAsB,KAAK,OAAO,SAAS,KAAK,cAAc,KAAK,OAAO,SAAS,KAAK,CAAC,KAAK,sBAAsB,CAAC,KAAK,qBAAqB,KAAK,UAAU,KAAK;AAAA,IAChM;AAAA,IACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,aAAO,KAAK,qBAAqBC,mBAAqB,kBAAqB,UAAU,GAAM,kBAAuB,gBAAgB,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,mBAAmB,GAAM,kBAAkB,kBAAkB,GAAM,kBAAuB,cAAc,CAAC;AAAA,IAC3V;AAAA,IACA,OAAO,OAAyB,kBAAkB;AAAA,MAChD,MAAMA;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,+BAA+B,CAAC;AAAA,QAC9D;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B,GAAG;AAAA,QACjF;AAAA,MACF;AAAA,MACA,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,6BAA6B,CAAC;AAAA,QAC/C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,8BAA8B,GAAG;AAAA,QACpF;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,mBAAmB;AAAA,MAClC,UAAU;AAAA,MACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,yBAAyB,IAAI,QAAQ,KAAK,EAAE,2BAA2B,IAAI,eAAe,MAAM;AAAA,QACjH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,eAAe;AAAA,QACf,aAAa;AAAA,QACb,cAAc;AAAA,QACd,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,sBAAsB;AAAA,QACtB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,sBAAsB;AAAA,QACtB,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,kBAAkB;AAAA,QAClB,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,QACjF,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,QACxE,kBAAkB,CAAC,GAAG,oBAAoB,oBAAoB,gBAAgB;AAAA,QAC9E,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,QACzD,iBAAiB,CAAC,GAAG,mBAAmB,mBAAmB,gBAAgB;AAAA,QAC3E,oBAAoB;AAAA,QACpB,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,QAC5D,QAAQ;AAAA,QACR,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,QACjF,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,QACpF,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,QACjF,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACxD;AAAA,MACA,SAAS;AAAA,QACP,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,yBAAyB;AAAA,QACzB,sBAAsB;AAAA,MACxB;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,UAAU,CAAI,mBAAmB,CAAC,qBAAqB,kBAAkB,CAAC,GAAM,oBAAoB;AAAA,MACpG,oBAAoBZ;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,WAAW,cAAc,aAAa,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,QAAQ,WAAW,WAAW,mBAAmB,kBAAkB,iBAAiB,iBAAiB,cAAc,0BAA0B,mBAAmB,mBAAmB,sBAAsB,sBAAsB,oBAAoB,qBAAqB,qBAAqB,GAAG,CAAC,GAAG,eAAe,kBAAkB,iBAAiB,mBAAmB,eAAe,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,wBAAwB,8BAA8B,GAAG,UAAU,qBAAqB,qBAAqB,gBAAgB,qBAAqB,sBAAsB,eAAe,UAAU,cAAc,WAAW,YAAY,aAAa,GAAG,CAAC,GAAG,wBAAwB,8BAA8B,GAAG,oBAAoB,qBAAqB,UAAU,qBAAqB,qBAAqB,gBAAgB,qBAAqB,sBAAsB,eAAe,UAAU,cAAc,WAAW,YAAY,aAAa,CAAC;AAAA,MACvmC,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,WAAW,CAAC;AACjC,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,MAAM,CAAC;AACvE,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,yBAAyB,CAAC,EAAE,GAAG,yCAAyC,GAAG,IAAI,yBAAyB,CAAC,EAAE,GAAG,yCAAyC,GAAG,GAAG,0BAA0B,CAAC,EAAE,GAAG,yCAAyC,GAAG,GAAG,yBAAyB,CAAC;AACtU,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,MAAM,CAAC;AACvE,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QAC1N;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,cAAc,EAAE,cAAc,IAAI,SAAS,EAAE,eAAe,IAAI,kBAAkB;AAC/G,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,yBAAyB,UAAU,IAAI,yBAAyB,QAAQ,IAAI,EAAE;AACnG,UAAG,UAAU;AACb,UAAG,YAAY,iBAAiB,IAAI,QAAQ,KAAK,EAAE,0BAA0B,IAAI,OAAO,UAAU,IAAI,OAAO,EAAE,0BAA0B,IAAI,OAAO,EAAE,0BAA0B,IAAI,UAAU,EAAE,2BAA2B,IAAI,WAAW,EAAE,sBAAsB,IAAI,UAAU,EAAE,yBAAyB,IAAI,mBAAmB,CAAC,IAAI,UAAU,EAAE,oBAAoB,IAAI,WAAW,QAAQ,EAAE,mBAAmB,IAAI,WAAW,OAAO;AACza,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AACrC,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,WAAW,IAAI,UAAU,IAAI,CAAC;AACnD,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AACtC,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,yBAAyB,UAAU,IAAI,yBAAyB,WAAW,IAAI,EAAE;AAAA,QACxG;AAAA,MACF;AAAA,MACA,cAAc,CAAC,iBAAiB,kBAAkB,6BAA6B,6BAA6B,8BAA8B,oBAAyB,qBAAqB;AAAA,MACxL,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF,GAAG;AAAA,CACF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,qBAAqB,kBAAkB;AAAA,MACnD,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkFV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,iCAAiC;AAAA,QACjC,mCAAmC;AAAA,MACrC;AAAA,MACA,SAAS,CAAC,iBAAiB,kBAAkB,6BAA6B,6BAA6B,8BAA8B,kBAAkB;AAAA,IACzJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,QACpC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,SAAS,eAAe,OAAO;AAC7B,SAAO,UAAU,SAAS,UAAU,WAAW,QAAQ,iBAAiB,KAAK,IAAI,WAAW;AAC9F;AAEA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,UAAU;AAAA,EACV;AAAA,EACA,sBAAsB,OAAO,qBAAqB;AAAA,IAChD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,gBAAgB,CAAC,CAAC,KAAK;AAAA,EACvB,WAAW;AACT,SAAK,qBAAqB,iBAAiB,KAAK,WAAW;AAAA,EAC7D;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,SAAK,qBAAqB,cAAc,QAAQ,YAAY;AAAA,EAC9D;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,aAAa,EAAE,CAAC;AAAA,IACtC,WAAW,SAAS,8BAA8B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,CAAC;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,MACpE;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,qBAAqB,CAAC,IAAI,iBAAiB,CAAC,IAAI,OAAO;AAAA,MACxE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,WAAW,WAAW,cAAc;AAAA,IACnD;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,oBAAoBA;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,IACzD,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,gDAAgD,GAAG,GAAG,MAAM,CAAC;AAAA,MAC5L;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,iBAAiB,CAAC,IAAI,UAAU,IAAI,EAAE;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAgB;AAAA,IAC/B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQV,SAAS,CAAC,gBAAgB;AAAA,MAC1B,MAAM;AAAA,QACJ,6BAA6B;AAAA,MAC/B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA,WAAW,IAAI,QAAQ;AAAA,EACvB,sBAAsB,IAAI,cAAc,CAAC;AAAA,EACzC,iBAAiB,IAAI,cAAc,CAAC;AAAA,EACpC,6BAA6B,KAAK,oBAAoB,KAAK,UAAU,UAAQ,MAAM,GAAG,CAAC,KAAK,qBAAqB,GAAG,KAAK,IAAI,OAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,SAAS,MAAM,KAAK,mBAAmB,CAAC,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AAAA,EACzN,gCAAgC,KAAK,2BAA2B,KAAK,IAAI,UAAQ,KAAK,OAAO,UAAQ,KAAK,WAAW,KAAK,CAAC,CAAC;AAAA,EAC5H,iCAAiC,KAAK,2BAA2B,KAAK,IAAI,UAAQ,KAAK,OAAO,UAAQ,KAAK,YAAY,KAAK,CAAC,CAAC;AAAA,EAC9H,wBAAwB,KAAK,eAAe,KAAK,UAAU,UAAQ,MAAM,GAAG,CAAC,KAAK,gBAAgB,GAAG,KAAK,IAAI,OAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,SAAS,MAAM,KAAK,cAAc,CAAC,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AAAA,EACrM,sBAAsB,OAAO,qBAAqB;AAAA,IAChD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,gBAAgB,CAAC,CAAC,KAAK;AAAA,EACvB,qBAAqB;AACnB,QAAI,KAAK,qBAAqB;AAC5B,WAAK,yBAAyB,QAAQ,KAAK,UAAU,KAAK,wBAAwB,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,mBAAmB;AACjJ,WAAK,oBAAoB,QAAQ,KAAK,UAAU,KAAK,mBAAmB,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,cAAc;AAElI,WAAK,8BAA8B,UAAU,qBAAmB;AAC9D,wBAAgB,QAAQ,UAAQ,KAAK,cAAc,SAAS,gBAAgB,gBAAgB,SAAS,CAAC,CAAC,CAAC;AAAA,MAC1G,CAAC;AACD,WAAK,+BAA+B,UAAU,sBAAoB;AAChE,yBAAiB,QAAQ,UAAQ,KAAK,gBAAgB,SAAS,iBAAiB,CAAC,CAAC,CAAC;AAAA,MACrF,CAAC;AAED,oBAAc,CAAC,KAAK,oBAAoB,sBAAsB,KAAK,6BAA6B,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,iBAAiB,cAAc,MAAM;AACjL,uBAAe,QAAQ,CAAC,MAAM,UAAU;AACtC,cAAI,KAAK,YAAY;AACnB,kBAAM,eAAe,eAAe,MAAM,GAAG,KAAK;AAClD,kBAAM,QAAQ,aAAa,OAAO,CAAC,KAAK,QAAQ,OAAO,IAAI,WAAW,IAAI,WAAW,IAAI,CAAC;AAC1F,kBAAM,QAAQ,gBAAgB,MAAM,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,CAAC;AAC/E,iBAAK,iBAAiB,GAAG,KAAK,IAAI;AAAA,UACpC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,oBAAc,CAAC,KAAK,oBAAoB,sBAAsB,KAAK,8BAA8B,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,iBAAiB,eAAe,MAAM;AACnL,wBAAgB,QAAQ,CAAC,GAAG,UAAU;AACpC,gBAAM,OAAO,gBAAgB,gBAAgB,SAAS,QAAQ,CAAC;AAC/D,cAAI,KAAK,aAAa;AACpB,kBAAM,eAAe,gBAAgB,MAAM,gBAAgB,SAAS,OAAO,gBAAgB,MAAM;AACjG,kBAAM,QAAQ,aAAa,OAAO,CAAC,KAAK,QAAQ,OAAO,IAAI,WAAW,IAAI,WAAW,IAAI,CAAC;AAC1F,kBAAM,QAAQ,gBAAgB,MAAM,gBAAgB,SAAS,OAAO,gBAAgB,MAAM,EAAE,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,CAAC;AAC7H,iBAAK,kBAAkB,GAAG,KAAK,IAAI;AAAA,UACrC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,GAAG,WAAW,IAAI,GAAG,kBAAkB,IAAI,GAAG,wBAAwB,IAAI,GAAG,YAAY,IAAI,GAAG,sBAAsB,EAAE,CAAC;AAAA,IAC5I,gBAAgB,SAAS,6BAA6B,IAAI,KAAK,UAAU;AACvE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,sBAAsB,CAAC;AACnD,QAAG,eAAe,UAAU,sBAAsB,CAAC;AAAA,MACrD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB;AACvE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,MAC9E;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,IAAI,aAAa;AAAA,MACnD;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,yBAAyB;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA,WAAW,IAAI,QAAQ;AAAA,EACvB,gBAAgB;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAoB,IAAI,aAAa;AAAA,EACrC,sBAAsB,OAAO,qBAAqB;AAAA,IAChD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,qBAAqB,OAAO,oBAAoB;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,YAAY,UAAU;AAChC,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,gBAAgB,CAAC,CAAC,KAAK;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,iBAAiB,KAAK,WAAW;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,qBAAqB;AAC5B,YAAM,iBAAiB,KAAK,oBAAoB,QAAQ,KAAK,UAAU,KAAK,mBAAmB,GAAG,IAAI,UAAQ,QAAQ,KAAK,KAAK,CAAC;AACjI,YAAM,wBAAwB,eAAe,KAAK,UAAU,mBAAiB,gBAAgB,cAAc,wBAAwB,KAAK,GAAG,UAAU,KAAK,QAAQ,CAAC;AACnK,4BAAsB,UAAU,UAAQ,KAAK,oBAAoB,YAAY,IAAI,CAAC;AAElF,WAAK,oBAAoB,mBAAmB,KAAK,UAAU,YAAU,SAAS,wBAAwB,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ,KAAK,oBAAoB,uBAAuB,IAAI,CAAC;AACrN,YAAM,gCAAgC,eAAe,KAAK,UAAU,aAAW,UAAU,QAAQ,gCAAgC,KAAK,GAAG,UAAU,KAAK,QAAQ,CAAC;AACjK,YAAM,iCAAiC,eAAe,KAAK,UAAU,aAAW,UAAU,QAAQ,iCAAiC,KAAK,GAAG,UAAU,KAAK,QAAQ,CAAC;AACnK,oCAA8B,UAAU,2BAAyB;AAC/D,aAAK,oBAAoB,cAAc,sBAAsB,WAAW,CAAC;AAAA,MAC3E,CAAC;AACD,qCAA+B,UAAU,4BAA0B;AACjE,aAAK,oBAAoB,eAAe,uBAAuB,WAAW,CAAC;AAAA,MAC7E,CAAC;AAAA,IACH;AACA,QAAI,KAAK,oBAAoB;AAC3B,YAAM,gBAAgB,KAAK,yBAAyB,QAAQ,KAAK,UAAU,KAAK,wBAAwB,CAAC;AACzG,YAAM,cAAc,cAAc,KAAK,UAAU,MAAM,MAAM,GAAG,KAAK,yBAAyB,IAAI,QAAM,GAAG,iBAAiB,CAAC,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AACzJ,kBAAY,UAAU,UAAQ;AAC5B,cAAM,YAAY;AAAA,UAChB,KAAK,KAAK;AAAA,UACV,OAAO,KAAK;AAAA,QACd;AACA,aAAK,kBAAkB,KAAK,SAAS;AACrC,YAAI,KAAK,YAAY,KAAK,mBAAmB,OAAO;AAClD,eAAK,yBAAyB,OAAO,QAAM,OAAO,IAAI,EAAE,QAAQ,QAAM,GAAG,eAAe,CAAC;AAAA,QAC3F;AAAA,MACF,CAAC;AACD,YAAM,sBAAsB,cAAc;AAAA,QAAK,UAAU,UAAQ,MAAM,GAAG,CAAC,eAAe,GAAG,KAAK,IAAI,OAAK,EAAE,mBAAmB,CAAC,CAAC,EAAE,KAAK,SAAS,MAAM,aAAa,CAAC,CAAC;AAAA,QAAG,IAAI,UAAQ,KAAK,OAAO,UAAQ,CAAC,CAAC,KAAK,YAAY,CAAC,CAAC,KAAK,UAAU,EAAE,IAAI,UAAQ;AAC1P,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,iBAAO;AAAA,YACL,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,cAAc;AAAA,YACd;AAAA,YACA,UAAU;AAAA,YACV,aAAa;AAAA,UACf;AAAA,QACF,CAAC,CAAC;AAAA;AAAA,QAEF,MAAM,CAAC;AAAA,QAAG,UAAU,KAAK,QAAQ;AAAA,MAAC;AAClC,0BAAoB,UAAU,UAAQ;AACpC,aAAK,oBAAoB,oBAAoB,KAAK,IAAI;AAAA,MACxD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,qBAAqB;AAC5B,WAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,KAAK,WAAW,aAAa;AAAA,IAClH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAAA,EAC5H;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,GAAG,iBAAiB,CAAC;AAAA,IAC3C,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAC5C,QAAG,eAAe,UAAU,oBAAoB,CAAC;AAAA,MACnD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB;AACvE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,MAC9E;AAAA,IACF;AAAA,IACA,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,CAAC;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,MACpE;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,mBAAmB;AAAA,IACrB;AAAA,IACA,oBAAoBA;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,IACzD,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,yCAAyC,GAAG,GAAG,MAAM,CAAC;AAAA,MAC9K;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,gBAAgB,IAAI,EAAE;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAgB;AAAA,IAC/B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQV,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,WAAW;AAAA,EACX,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,MAAM,YAAY,EAAE,CAAC;AAAA,IAClC,WAAW,CAAC,GAAG,wBAAwB;AAAA,IACvC,UAAU;AAAA,IACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,CAAC,IAAI,QAAQ;AAAA,MAC3C;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,kBAAkB,oBAAoB,sBAAsB,sBAAsB,oBAAoB,kBAAkB,kBAAkB,eAAe,qBAAqB,yBAAyB,+BAA+B,sBAAsB,yBAAyB,yBAAyB,6BAA6B,8BAA8B,6BAA6B,sBAAsB,sBAAsB,4BAA4B,0BAA0B,sBAAsB,yBAAyB,wBAAwB,2BAA2B,yBAAyB,0BAA0B,0BAA0B,sBAAsB;AAAA,IACvrB,SAAS,CAAC,kBAAkB,oBAAoB,sBAAsB,sBAAsB,oBAAoB,kBAAkB,kBAAkB,eAAe,+BAA+B,sBAAsB,yBAAyB,0BAA0B,qBAAqB,yBAAyB,0BAA0B,sBAAsB,yBAAyB,0BAA0B,sBAAsB;AAAA,EACpb,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,kBAAkB,oBAAoB,oBAAoB,kBAAkB,6BAA6B,6BAA6B,yBAAyB,wBAAwB,2BAA2B,0BAA0B,sBAAsB;AAAA,EAC9Q,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,oBAAoB,sBAAsB,sBAAsB,oBAAoB,kBAAkB,kBAAkB,eAAe,qBAAqB,yBAAyB,+BAA+B,sBAAsB,yBAAyB,yBAAyB,6BAA6B,8BAA8B,6BAA6B,sBAAsB,sBAAsB,4BAA4B,0BAA0B,sBAAsB,yBAAyB,wBAAwB,2BAA2B,yBAAyB,0BAA0B,0BAA0B,sBAAsB;AAAA,MACvrB,SAAS,CAAC,kBAAkB,oBAAoB,sBAAsB,sBAAsB,oBAAoB,kBAAkB,kBAAkB,eAAe,+BAA+B,sBAAsB,yBAAyB,0BAA0B,qBAAqB,yBAAyB,0BAA0B,sBAAsB,yBAAyB,0BAA0B,sBAAsB;AAAA,IACpb,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["_c0", "_c1", "_c2", "NzPaginationComponent", "_c0", "_forTrack0", "_c1", "_c2", "_c3", "_c4", "_c12", "_c13", "NzFilterTriggerComponent", "filter", "NzThAddOnComponent", "NZ_CONFIG_MODULE_NAME", "NzTableComponent"]}