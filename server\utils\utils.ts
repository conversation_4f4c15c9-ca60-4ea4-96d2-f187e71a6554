import * as crypto from 'crypto';
import * as os from 'os';

// 返回本机ip地址
export function getIPAddresses(): string[] {
  const ipAddresses: string[] = [];
  const interfaces = os.networkInterfaces();
  for (const devName in interfaces) {
    const iface = interfaces[devName];
    if (!iface) {
      continue;
    }
    for (let i = 0; i < iface.length; i++) {
      const alias = iface[i];
      if (
        alias.family === 'IPv4' &&
        alias.address !== '127.0.0.1' &&
        !alias.internal
      ) {
        ipAddresses.push(alias.address);
      }
    }
  }
  return ipAddresses;
}

export function HMAC256(key: string, data: string) {
  return crypto.createHmac('sha256', key).update(data).digest('hex');
}

/** 获取min~max之间的随机整数 */
export function getRandomInt(min: number, max: number) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

export function randomString(length?: number) {
  const len = length || 32;
  const $chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const maxPos = $chars.length;
  let Str = '';
  for (let i = 0; i < len; i++) {
    Str += $chars.charAt(Math.floor(Math.random() * (maxPos + 1)));
  }
  return Str;
}

export function guid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
