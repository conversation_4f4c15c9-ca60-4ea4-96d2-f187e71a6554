import {
  IHttpProject,
  IHttpSchedule,
  SessionStatus,
} from './../../core/http/exam.service';
import { CommonModule } from '@angular/common';
import { Component, OnDestroy, type OnInit } from '@angular/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTableModule } from 'ng-zorro-antd/table';
import { Router } from '@angular/router';
import { IHttpForm } from '../../core/http/form.service';
import { CustomDatePipe } from '../../shared/pipes/custom-date.pipe';
import { UploadButtonComponent } from '../../shared/components/upload-button.component';
import { ModalService } from '../../core/services/modal.service';
import { StatusTransformPipe } from '../../shared/pipes/status-transform.pipe';
import { ServerTimeService } from '../../core/services/server-time.service';
import {
  FormDataService,
  ProjectDataService,
  ScheduleDataService,
} from '../../core/data';
import { Subject, takeUntil, tap, switchMap, timer } from 'rxjs';
import { parseDateTime } from '@app/utils/parseDate';

type ScheduleStatus = 'not-start' | 'active' | 'completed';
interface ISchedule {
  id: string;
  date: string;
  test: 0 | 1;
  subject_name: string[];
  room_total: number;
  entry_total: number;
  schedule_status: ScheduleStatus;
}
@Component({
    selector: 'app-schedule',
    imports: [
        CommonModule,
        NzTableModule,
        NzButtonModule,
        NzIconModule,
        CustomDatePipe,
        UploadButtonComponent,
        StatusTransformPipe,
    ],
    templateUrl: './schedule.component.html',
    styleUrls: ['./schedule.component.scss']
})
export class ScheduleComponent implements OnInit, OnDestroy {
  is_form_expanded = false;

  test_project: IHttpProject | null = null;
  formal_project: IHttpProject | null = null;
  form_data_set: IHttpForm[] = [];
  schedule_data_set: ISchedule[] = [];
  schedule_test: IHttpSchedule = {} as IHttpSchedule;

  unsubscribe$ = new Subject();

  constructor(
    private router: Router,
    private modalService: ModalService,
    private timeService: ServerTimeService,
    private projectData: ProjectDataService,
    private scheduleData: ScheduleDataService,
    private formData: FormDataService
  ) {}
  ngOnInit(): void {
    this.projectData.formalProject$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((p) => (this.formal_project = p));
    this.projectData.testProject$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((p) => (this.test_project = p));

    this.scheduleData.test_schedule$
      .pipe(
        tap((schedule) => (this.schedule_test = schedule)),
        switchMap((schedule) =>
          this.formData.selectScheduleForms(schedule.schedule_id)
        ),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((forms) => {
        this.form_data_set = forms;
        if (!this.form_data_set.length) {
          this.is_form_expanded = false;
        }
      });
    this.scheduleData.formal_schedule$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((schedules) => {
        this.schedule_data_set = schedules
          .sort(
            (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()
          )
          .map((s) => {
            const date = this.formatTimeRange(
              new Date(s.start),
              new Date(s.end)
            );
            const entry_total = s.sessions.reduce((pre, cur) => {
              return pre + cur.entry_status.total;
            }, 0);
            const schedule_status = s.sessions.every(s => s.status === SessionStatus.notEnter) ? 'not-start' : 
                                    s.sessions.every(s => s.status === SessionStatus.centerUploaded)  ? "completed" : 'active';
            return {
              id: s.schedule_id,
              schedule_status,
              start: s.start,
              date,
              test: s.test,
              subject_name: Array.from(new Set(s.subjects.map(s => s.name))),
              room_total: s.sessions.length,
              entry_total: entry_total,
            };
          });
      });

    timer(0, 10000).pipe(
      switchMap(() => this.scheduleData.reqSchedules()),
      takeUntil(this.unsubscribe$)
    ).subscribe()
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(1);
    this.unsubscribe$.complete();
  }

  publishForm() {
    this.modalService.confirm('确认发布试卷吗？', () => {
      this.formData.reqPublishForm(this.schedule_test.schedule_id).subscribe();
    });
  }

  publishPassword() {
    this.modalService.confirm('确认发布密码吗？', () => {
      this.formData
        .reqPublishPassword(this.schedule_test.schedule_id)
        .subscribe();
    });
  }

  delForm(id: string) {
    this.modalService.confirm('确认删除该试卷吗？', () => {
      this.formData.reqDelForm(this.schedule_test.schedule_id, id).subscribe();
    });
  }

  getScheduleFormsList() {
    this.formData.reqForms(this.schedule_test.schedule_id).subscribe();
  }

  goScheduleDetail(id: string) {
    this.router.navigate(['/dashboard/schedule', id]);
  }

  formatTimeRange(start: Date, end: Date) {
    const startTime = parseDateTime(start);
    const endTime = parseDateTime(end);
    return `${startTime.YYYY}.${startTime.MM}.${startTime.DD} ${startTime.HH}:${startTime.FF} - ${endTime.HH}:${endTime.FF}`;
  }

}
