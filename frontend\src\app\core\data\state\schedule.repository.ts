import { Injectable } from '@angular/core';
import { createStore } from '@ngneat/elf';
import {
  UIEntitiesRef,
  deleteEntities,
  selectAllEntities,
  selectEntity,
  selectManyByPredicate,
  upsertEntities,
  upsertEntitiesById,
  withEntities,
  withUIEntities,
} from '@ngneat/elf-entities';
import { ExamHttpService, IHttpSchedule } from '../../http/exam.service';
import { filter, map, Observable, shareReplay, tap } from 'rxjs';

export type IScheduleData = IHttpSchedule;

export const scheduleStore = createStore(
  { name: 'schedules' },
  withEntities<IScheduleData, 'schedule_id'>({ idKey: 'schedule_id' }),
  withUIEntities<ScheduleUiState, "schedule_id">({ idKey: "schedule_id" })
);

export interface ScheduleUiState {
  schedule_id: string;
  form_expanded: boolean;
  stat_expanded: boolean;
}

export function createSessionUi(params?: Partial<ScheduleUiState>) {
  return {
    form_expanded: true,
    stat_expanded: true,
    ...params,
  } as ScheduleUiState;
}


@Injectable({ providedIn: 'root' })
export class ScheduleDataService {
  schedules$ = scheduleStore.pipe(selectAllEntities());

  formal_schedule$ = scheduleStore.pipe(
    selectManyByPredicate((schedule) => schedule.test === 0),
    filter((schedule) => !!schedule.length),
    shareReplay({ refCount: true })
  ) as Observable<IScheduleData[]>;

  test_schedule$ = scheduleStore.pipe(
    selectManyByPredicate((schedule) => schedule.test === 1),
    filter((s) => !!s.length),
    map((s) => s[0]),
    shareReplay({ refCount: true })
  );

  constructor(private examHttp: ExamHttpService) {}


  /** 更新界面ui信息 */
  updateScheduleUi(id: string, ui: Partial<ScheduleUiState>) {
    scheduleStore.update(
      upsertEntitiesById(id, {
        updater: ui,
        creator: (id) => createSessionUi({ ...ui, schedule_id: id }),
        ref: UIEntitiesRef,
      })
    );
  }

  /** 清除界面ui信息 */
  clearScheduleUi(id: string) {
    scheduleStore.update(deleteEntities(id, { ref: UIEntitiesRef }));
  }

  scheduleUi$(id: string) {
    return scheduleStore.pipe(
      selectEntity(id, { ref: UIEntitiesRef }),
      map((ui) => ui || createSessionUi({ schedule_id: id }))
    );
  }

  reqSchedules() {
    return this.examHttp.getSchedules().pipe(
      tap((res) => {
        if (res.status === 'success') {
          scheduleStore.update(upsertEntities(res.data));
        }
      })
    );
  }

  reqSchedule(schedule_id: string) {
    return this.examHttp.getSchedule(schedule_id).pipe(
      tap((res) => {
        if (res.status === 'success') {
          scheduleStore.update(upsertEntities(res.data));
        }
      })
    );
  }

  selectSchedule(schedule_id: string) {
    return scheduleStore.pipe(selectEntity(schedule_id));
  }
}
