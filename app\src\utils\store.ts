import * as path from "path";
import * as fs from "fs";

import * as crypt from "./crypt";

interface StoreOptions {
  configName?: string;
  configPath: string;
  file?: string;
  defaults?: unknown;
  password: string;
  secretKeys: string[];
}

// Simple Key-Value store
export class Store {
  path: string;
  private password: string;
  private secretKeys: string[];
  private data: { [key: string]: any };

  constructor(opts: StoreOptions) {
    const userDataPath = opts.configPath;

    // We'll use the `configName` property to set the file name and path.join to bring it all together as a string
    this.path = opts.file || path.join(userDataPath, opts.configName + ".json");
    this.secretKeys = opts.secretKeys || [];
    this.password = opts.password;
    this.data = parseDataFile.call(this, this.path, opts) || {};

    if (this.password) {
      for (const k of this.secretKeys) {
        if (typeof this.data["$" + k] === "string") {
          try {
            this.data[k] = JSON.parse(crypt.decrypt(this.data["$" + k], this.password));
          } catch (_e) {
            /* handle error */
          }
        }
      }
    }
  }

  // This will just return the property on the `data` object
  get(key: string) {
    return this.data[key];
  }

  // Check if key exists
  has(key: string): boolean {
    return key in this.data;
  }

  // ...and this will set it
  set(key: string, val: any) {
    this.data[key] = val;
  }

  // remove the key
  unset(key: string) {
    delete this.data[key];
  }

  save(callback?: (err: Error) => void) {
    try {
      const data = JSON.stringify(this.getDataForSave(), null, 2);
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      fs.writeFile(this.path, data, (callback || function (err: any) {} as any));
    } catch (e) {
      console.error("store: save", e);
    }
  }

  saveSync() {
    try {
      const data = JSON.stringify(this.getDataForSave(), null, 2);
      fs.writeFileSync(this.path, data, { encoding: "utf8" });
    } catch (e) {
      console.error("store: saveSync", e);
    }
  }

  getData() {
    if (this.password && this.secretKeys) {
      const data = JSON.parse(JSON.stringify(this.data));
      for (const k of this.secretKeys) {
        delete data["$" + k];
      }
      return data;
    } else {
      return this.data;
    }
  }

  getDataForSave() {
    if (this.password && this.secretKeys) {
      const data = JSON.parse(JSON.stringify(this.data));
      for (const k of this.secretKeys) {
        if (data[k]) {
          try {
            data["$" + k] = crypt.encrypt(JSON.stringify(data[k]), this.password);
          } catch (_e) {
            /* handle error */
            delete data["$" + k];
          }
          delete data[k];
        } else {
          delete data["$" + k];
        }
      }
      return data;
    } else {
      return this.data;
    }
  }
}

function parseDataFile(filePath: string, opts: StoreOptions) {
  // We'll try/catch it in case the file doesn't exist yet, which will be the case on the first application run.
  // `fs.readFileSync` will return a JSON string which we then parse into a Javascript object
  try {
    const text = fs.readFileSync(filePath, { encoding: "utf8" });
    if (text.startsWith("{")) {
      return JSON.parse(text);
    } else {
      return opts.defaults || {};
    }
  } catch (_e) {
    // if there was some kind of error, return the passed in defaults instead.
    return opts.defaults;
  }
}
