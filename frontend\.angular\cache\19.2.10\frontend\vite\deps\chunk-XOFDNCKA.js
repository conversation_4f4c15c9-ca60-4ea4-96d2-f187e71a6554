import {
  asapScheduler,
  firstValueFrom,
  isObservable
} from "./chunk-7OW3M5NO.js";
import {
  BehaviorSubject,
  Observable,
  Subject,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  pipe,
  take
} from "./chunk-XEUTWJEE.js";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-XWLXMCJQ.js";

// node_modules/.pnpm/@ngneat+elf@2.5.1_rxjs@7.8.1/node_modules/@ngneat/elf/index.esm.js
function createState(...propsFactories) {
  const result = {
    config: {},
    state: {}
  };
  for (const {
    config,
    props
  } of propsFactories) {
    Object.assign(result.config, config);
    Object.assign(result.state, props);
  }
  return result;
}
var asyncBatchesInProgress = 0;
var batchInProgress = new BehaviorSubject(false);
var batchDone$ = batchInProgress.asObservable().pipe(filter((inProgress) => !inProgress), take(1));
function emitOnce(cb) {
  if (!batchInProgress.getValue()) {
    batchInProgress.next(true);
    const value = cb();
    if (asyncBatchesInProgress === 0) {
      batchInProgress.next(false);
    }
    return value;
  }
  return cb();
}
function emitOnceAsync(cb) {
  return __async(this, null, function* () {
    asyncBatchesInProgress++;
    if (!batchInProgress.getValue()) {
      batchInProgress.next(true);
    }
    const callbackReturnValue = cb();
    const value = yield isObservable(callbackReturnValue) ? firstValueFrom(callbackReturnValue) : callbackReturnValue;
    if (--asyncBatchesInProgress === 0) {
      batchInProgress.next(false);
    }
    return value;
  });
}
var elfHooksRegistry = {};
var ElfHooks = class {
  registerPreStoreUpdate(fn) {
    elfHooksRegistry.preStoreUpdate = fn;
  }
  registerPreStateInit(fn) {
    elfHooksRegistry.preStateInit = fn;
  }
};
var elfHooks = new ElfHooks();
var registry = /* @__PURE__ */ new Map();
var registryActions = new Subject();
var registry$ = registryActions.asObservable();
function addStore(store) {
  registry.set(store.name, store);
  registryActions.next({
    type: "add",
    store
  });
}
function removeStore(store) {
  registry.delete(store.name);
  registryActions.next({
    type: "remove",
    store
  });
}
function getStore(name) {
  return registry.get(name);
}
function getRegistry() {
  return registry;
}
function getStoresSnapshot() {
  const stores = {};
  registry.forEach((store, key) => {
    stores[key] = store.getValue();
  });
  return stores;
}
var events = [];
function _setEvent(e) {
  events.push(e);
}
function emitEvents(source) {
  if (events.length) {
    events.forEach((e) => source.next(e));
  }
  events = [];
}
var Store = class extends BehaviorSubject {
  constructor(storeDef) {
    super(storeDef.state);
    this.storeDef = storeDef;
    this.initialState = void 0;
    this.state = void 0;
    this.batchInProgress = false;
    this.events = new Subject();
    this.context = {
      config: this.getConfig(),
      setEvent: (action) => {
        _setEvent(action);
      }
    };
    this.events$ = this.events.asObservable();
    this.state = this.getInitialState(storeDef.state);
    this.initialState = this.getValue();
    addStore(this);
  }
  get name() {
    return this.storeDef.name;
  }
  getInitialState(state) {
    if (elfHooksRegistry.preStateInit) {
      return elfHooksRegistry.preStateInit(state, this.name);
    }
    return state;
  }
  getConfig() {
    return this.storeDef.config;
  }
  query(selector) {
    return selector(this.getValue());
  }
  update(...reducers) {
    const currentState = this.getValue();
    let nextState = reducers.reduce((value, reducer) => {
      value = reducer(value, this.context);
      return value;
    }, currentState);
    if (elfHooksRegistry.preStoreUpdate) {
      nextState = elfHooksRegistry.preStoreUpdate(currentState, nextState, this.name);
    }
    if (nextState !== currentState) {
      this.state = nextState;
      if (batchInProgress.getValue()) {
        if (!this.batchInProgress) {
          this.batchInProgress = true;
          batchDone$.subscribe(() => {
            super.next(this.state);
            emitEvents(this.events);
            this.batchInProgress = false;
          });
        }
      } else {
        super.next(this.state);
        emitEvents(this.events);
      }
    }
  }
  getValue() {
    return this.state;
  }
  reset() {
    this.update(() => this.initialState);
  }
  combine(observables) {
    let hasChange = true;
    const buffer = {};
    return new Observable((observer) => {
      for (const [key, query] of Object.entries(observables)) {
        observer.add(query.subscribe((value) => {
          buffer[key] = value;
          hasChange = true;
        }));
      }
      return this.subscribe({
        next() {
          if (hasChange) {
            observer.next(__spreadValues({}, buffer));
            hasChange = false;
          }
        },
        error(e) {
          observer.error(e);
        },
        complete() {
          observer.complete();
        }
      });
    });
  }
  destroy() {
    removeStore(this);
    this.reset();
  }
  next(value) {
    this.update(() => value);
  }
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  error() {
  }
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  complete() {
  }
};
function createStore(storeConfig, ...propsFactories) {
  const {
    state,
    config
  } = createState(...propsFactories);
  const {
    name
  } = storeConfig;
  return new Store({
    name,
    state,
    config
  });
}
function coerceArray(value) {
  return Array.isArray(value) ? value : [value];
}
function isFunction(value) {
  return typeof value === "function";
}
function isUndefined(value) {
  return value === void 0;
}
function isString(value) {
  return typeof value === "string";
}
function capitalize(key) {
  return key.charAt(0).toUpperCase() + key.slice(1);
}
function isObject(item) {
  return typeof item === "object" && !Array.isArray(item) && item !== null;
}
function deepFreeze(o) {
  Object.freeze(o);
  const oIsFunction = typeof o === "function";
  const hasOwnProp = Object.prototype.hasOwnProperty;
  Object.getOwnPropertyNames(o).forEach(function(prop) {
    if (hasOwnProp.call(o, prop) && (oIsFunction ? prop !== "caller" && prop !== "callee" && prop !== "arguments" : true) && o[prop] !== null && (typeof o[prop] === "object" || typeof o[prop] === "function") && !Object.isFrozen(o[prop])) {
      deepFreeze(o[prop]);
    }
  });
  return o;
}
function setProp(key, value) {
  return function(state) {
    return __spreadProps(__spreadValues({}, state), {
      [key]: isFunction(value) ? value(state[key]) : value
    });
  };
}
function setProps(props) {
  return function(state) {
    return __spreadValues(__spreadValues({}, state), isFunction(props) ? props(state) : props);
  };
}
function select(mapFn) {
  return pipe(map(mapFn), distinctUntilChanged());
}
function head() {
  return map((arr) => arr[0]);
}
function distinctUntilArrayItemChanged() {
  return distinctUntilChanged((prevCollection, currentCollection) => {
    if (prevCollection === currentCollection) {
      return true;
    }
    if (prevCollection.length !== currentCollection.length) {
      return false;
    }
    const isOneOfItemReferenceChanged = currentCollection.some((item, i) => {
      return prevCollection[i] !== item;
    });
    return !isOneOfItemReferenceChanged;
  });
}
var asap = () => debounceTime(0, asapScheduler);
function filterNil() {
  return filter((value) => value !== null && value !== void 0);
}
function propsFactory(key, {
  initialValue: propsFactoryInitialValue,
  config
}) {
  let initialValue = propsFactoryInitialValue;
  const normalizedKey = capitalize(key);
  return {
    [`with${normalizedKey}`](value = initialValue) {
      return {
        props: {
          [key]: value
        },
        config
      };
    },
    [`set${normalizedKey}InitialValue`](value) {
      initialValue = value;
    },
    [`set${normalizedKey}`](value) {
      return function(state) {
        const newVal = isFunction(value) ? value(state) : value;
        if (newVal === state[key]) {
          return state;
        }
        return __spreadProps(__spreadValues({}, state), {
          [key]: newVal
        });
      };
    },
    [`update${normalizedKey}`](value) {
      return function(state) {
        const newVal = isFunction(value) ? value(state) : value;
        if (newVal === state[key]) {
          return state;
        }
        return __spreadProps(__spreadValues({}, state), {
          [key]: isObject(newVal) ? __spreadValues(__spreadValues({}, state[key]), newVal) : newVal
        });
      };
    },
    [`reset${normalizedKey}`]() {
      return function(state) {
        return __spreadProps(__spreadValues({}, state), {
          [key]: initialValue
        });
      };
    },
    [`select${normalizedKey}`]() {
      return select((state) => state[key]);
    },
    [`get${normalizedKey}`](state) {
      return state[key];
    }
  };
}
function propsArrayFactory(key, options) {
  const normalizedKey = capitalize(key);
  const base = propsFactory(key, options);
  return __spreadProps(__spreadValues({}, base), {
    [`add${normalizedKey}`](items) {
      return function(state) {
        return __spreadProps(__spreadValues({}, state), {
          [key]: arrayAdd(state[key], items)
        });
      };
    },
    [`remove${normalizedKey}`](items) {
      return function(state) {
        return __spreadProps(__spreadValues({}, state), {
          [key]: arrayRemove(state[key], items)
        });
      };
    },
    [`toggle${normalizedKey}`](items) {
      return function(state) {
        return __spreadProps(__spreadValues({}, state), {
          [key]: arrayToggle(state[key], items)
        });
      };
    },
    [`update${normalizedKey}`](predicateOrIds, obj) {
      return function(state) {
        return __spreadProps(__spreadValues({}, state), {
          [key]: arrayUpdate(state[key], predicateOrIds, obj)
        });
      };
    },
    [`in${normalizedKey}`](item) {
      return (state) => inArray(state[key], item);
    }
  });
}
function arrayAdd(arr, items) {
  return [...arr, ...coerceArray(items)];
}
function arrayRemove(arr, items) {
  const toArray = coerceArray(items);
  return arr.filter((current) => !toArray.includes(current));
}
function arrayToggle(arr, items) {
  const toArray = coerceArray(items);
  const result = [...arr];
  toArray.forEach((item) => {
    const i = result.indexOf(item);
    i > -1 ? result.splice(i, 1) : result.push(item);
  });
  return result;
}
function inArray(arr, item) {
  return arr.includes(item);
}
function arrayUpdate(arr, item, newItem) {
  return arr.map((current) => {
    return current === item ? newItem : current;
  });
}
function withProps(props) {
  return {
    props,
    config: void 0
  };
}
var __DEV__ = true;
function enableElfProdMode() {
  __DEV__ = false;
}
function isDev() {
  return __DEV__;
}

export {
  createState,
  emitOnce,
  emitOnceAsync,
  elfHooks,
  registry$,
  getStore,
  getRegistry,
  getStoresSnapshot,
  _setEvent,
  Store,
  createStore,
  coerceArray,
  isFunction,
  isUndefined,
  isString,
  capitalize,
  isObject,
  deepFreeze,
  setProp,
  setProps,
  select,
  head,
  distinctUntilArrayItemChanged,
  asap,
  filterNil,
  propsFactory,
  propsArrayFactory,
  withProps,
  enableElfProdMode,
  isDev
};
//# sourceMappingURL=chunk-XOFDNCKA.js.map
