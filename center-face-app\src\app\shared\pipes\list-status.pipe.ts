import { Pipe, PipeTransform } from '@angular/core';
import { SessionStatus } from '@app/core/http/exam.service';
import { RoomData } from '@app/exam/room-monitor/room-monitor.component';

@Pipe({ name: 'listStatus', standalone: true })
export class ListStatusPipe implements PipeTransform {
  transform(rooms: RoomData[], statusFilter: Map<SessionStatus, boolean>): any {
    const checkedStatus = Array.from(statusFilter)
      .filter((item) => item[1])
      .map((item) => +item[0]);
    return rooms.filter((room) => checkedStatus.includes(room.status));
  }
}
