# Performance Tests

This directory contains performance tests for the testcenter application, written in TypeScript and executed by [k6](https://k6.io/).

## Prerequisites

- [k6](https://k6.io/docs/get-started/installation/) must be installed on your system.
- [Node.js](https://nodejs.org/) and npm are required for managing development dependencies (like TypeScript types for IDE support).

## Setup

Install the necessary type definitions for editor autocompletion and type-checking:

```bash
# Navigate to the performance tests directory
cd performance-tests

# Install dev dependencies
npm install
```

## Running Tests

Thanks to k6's native TypeScript support, there is no build step required. You can run the tests directly.

### Basic Test Run

To run the tests with the default options defined in `src/test.k6.ts`:

```bash
# from the performance-tests directory
npm test

# or directly with k6:
k6 run src/test.k6.ts
```

### Customizing the Test Run

You can override the options from the command line.

```bash
# Run with 20 virtual users for 60 seconds
k6 run --vus 20 --duration 60s src/test.k6.ts

# Run in debug mode to see HTTP request/response details
k6 run --http-debug="full" src/test.k6.ts
```

## Project Structure

```
performance-tests/
├── src/
│   ├── test.k6.ts          # Main k6 test script
│   └── data-generator.ts   # Utilities for generating test data
├── package.json            # Node.js dependencies for IDE support
└── README.md               # This file
```

## Important Notes

- **Local Imports**: When importing one local TypeScript module from another, you **must** include the `.ts` file extension in the import path. k6 requires this to resolve the module correctly.
  ```typescript
  // Correct:
  import { generateSessionData } from './data-generator.ts';

  // Incorrect (will fail at runtime):
  import { generateSessionData } from './data-generator';
  ```
- **IDE Errors**: Your IDE might show an error like "An import path can only end with a '.ts' extension when 'allowImportingTsExtensions' is enabled." This is a limitation of the standard TypeScript server settings. It can be safely ignored, as k6 will execute the script correctly. You could optionally add `"allowImportingTsExtensions": true` to your `compilerOptions` in `tsconfig.json` to resolve this editor-specific warning, but it's not required for k6 to function. 