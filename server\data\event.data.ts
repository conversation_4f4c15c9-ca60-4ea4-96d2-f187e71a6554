import { DbConnection } from './types';
import globalWriteQueue from '../utils/writeQueue';

export interface IEventData {
  id: string;
  session_id: string;
  type: string;
  content: string;
  created_at: number;
  is_deleted: 0 | 1;
  version: number;
}

export interface IGetEventsOptions {
  limit?: number;
  sort?: {
    field: keyof IEventData;
    order: 'ASC' | 'DESC';
  };
}

export interface IEventDataAccess {
  getEvents(
    session_ids?: string[],
    options?: IGetEventsOptions,
  ): Promise<IEventData[]>;
  findEvents(whereFields?: Partial<IEventData>): Promise<IEventData[]>;
  addOrUpdateEvents(events: IEventData[]): Promise<void>;
  getMaxVersion(session_id: string): Promise<number>;
  deleteEvents(session_id: string, id: number[]): Promise<void>;
}

export class EventDataAccess implements IEventDataAccess {
  db: DbConnection;
  versionCache: Map<string, number>;

  constructor(db: DbConnection) {
    this.db = db;
    this.versionCache = new Map();
  }

  async getEvents(
    session_ids: string[],
    options?: IGetEventsOptions,
  ): Promise<IEventData[]> {
    const { limit = 1000, sort = { field: 'created_at', order: 'DESC' } } =
      options || {};

    return this.db.event.findAll({
      where: { session_id: session_ids },
      order: [[sort.field, sort.order]],
      limit: limit,
      raw: true,
    });
  }

  async findEvents(whereFields: Partial<IEventData>): Promise<IEventData[]> {
    const opts = whereFields
      ? { where: whereFields, raw: true }
      : { raw: true };
    const events = await this.db.event.findAll(opts);
    return events;
  }

  async addOrUpdateEvents(events: IEventData[]): Promise<void> {
    await globalWriteQueue.enqueue(() =>
      this.db.event.bulkCreate(events, {
        updateOnDuplicate: [
          'type',
          'content',
          'created_at',
          'is_deleted',
          'version',
        ],
      }),
    );
    this._updateVersionCache(events);
  }

  private _updateVersionCache(events: IEventData[]): void {
    if (!events || events.length === 0) {
      return;
    }

    const sessionMaxVersions = new Map<string, number>();

    for (const event of events) {
      const currentMax = sessionMaxVersions.get(event.session_id) || 0;
      sessionMaxVersions.set(
        event.session_id,
        Math.max(currentMax, event.version),
      );
    }

    for (const [sessionId, maxVersion] of sessionMaxVersions) {
      const cachedVersion = this.versionCache.get(sessionId) || 0;
      this.versionCache.set(sessionId, Math.max(cachedVersion, maxVersion));
    }
  }

  async getMaxVersion(session_id: string): Promise<number> {
    const cachedVersion = this.versionCache.get(session_id) || 0;
    if (cachedVersion > 0) {
      return cachedVersion;
    }

    try {
      const result = (await this.db.event.findOne({
        where: { session_id },
        attributes: [
          [
            this.db.sequelize.fn('MAX', this.db.sequelize.col('version')),
            'maxVersion',
          ],
        ],
        raw: true,
      })) as any;

      const maxVersion = result?.maxVersion || 0;
      this.versionCache.set(session_id, maxVersion);
      return maxVersion;
    } catch (error) {
      console.error(
        `Error getting max version for session ${session_id}:`,
        error,
      );
      return 0;
    }
  }

  async deleteEvents(session_id: string, id: number[]): Promise<void> {
    await globalWriteQueue.enqueue(() =>
      this.db.event.destroy({ where: { session_id, id } }),
    );
  }
}
