import {
  capitalize,
  coerceArray,
  distinctUntilArrayItemChanged,
  isDev,
  isFunction,
  isUndefined,
  propsArrayFactory,
  propsFactory,
  select
} from "./chunk-XOFDNCKA.js";
import "./chunk-VCKIMKJI.js";
import "./chunk-7OW3M5NO.js";
import {
  distinctUntilChanged,
  map,
  pipe,
  switchMap
} from "./chunk-XEUTWJEE.js";
import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "./chunk-XWLXMCJQ.js";

// node_modules/.pnpm/@ngneat+elf-entities@5.0.1_@ngneat+elf@2.5.1_rxjs@7.8.1__rxjs@7.8.1/node_modules/@ngneat/elf-entities/index.esm.js
function buildEntities(entities, idKey) {
  const asObject = {};
  const ids = [];
  for (const entity of entities) {
    const id = entity[idKey];
    ids.push(id);
    asObject[id] = entity;
  }
  return {
    ids,
    asObject
  };
}
function findIdsByPredicate(state, ref, predicate) {
  const {
    idsKey,
    entitiesKey
  } = ref;
  const entities = state[entitiesKey];
  return state[idsKey].filter((id) => predicate(entities[id]));
}
function findEntityByPredicate(state, ref, predicate) {
  const {
    idsKey,
    entitiesKey
  } = ref;
  const entities = state[entitiesKey];
  const id = state[idsKey].find((id2) => {
    return predicate(entities[id2]);
  });
  return entities[id];
}
function checkPluck(entity, pluck) {
  if (entity && pluck) {
    return isFunction(pluck) ? pluck(entity) : entity[pluck];
  } else {
    return entity;
  }
}
function getIdKey(context, ref) {
  return context.config[ref.idKeyRef];
}
var EntitiesRef = class {
  constructor(config) {
    this.entitiesKey = void 0;
    this.idsKey = void 0;
    this.idKeyRef = "idKey";
    this.entitiesKey = config.entitiesKey;
    this.idsKey = config.idsKey;
    this.idKeyRef = config.idKeyRef;
  }
};
function entitiesPropsFactory(feature) {
  const idKeyRef = feature ? `idKey${capitalize(feature)}` : "idKey";
  const ref = new EntitiesRef({
    entitiesKey: feature ? `${feature}Entities` : "entities",
    idsKey: feature ? `${feature}Ids` : "ids",
    idKeyRef
  });
  function propsFactory2(config) {
    let entities = {};
    let ids = [];
    const idKey = config?.idKey || "id";
    if (config?.initialValue) {
      ({
        ids,
        asObject: entities
      } = buildEntities(config.initialValue, idKey));
    }
    return {
      props: {
        [ref.entitiesKey]: entities,
        [ref.idsKey]: ids
      },
      config: {
        [idKeyRef]: idKey
      }
    };
  }
  return {
    [`${feature}EntitiesRef`]: ref,
    [`with${capitalize(feature)}Entities`]: propsFactory2
  };
}
var {
  withEntities,
  EntitiesRef: defaultEntitiesRef
} = entitiesPropsFactory("");
var {
  UIEntitiesRef,
  withUIEntities
} = entitiesPropsFactory("UI");
function deleteEntities(ids, options = {}) {
  return function(state, ctx) {
    const {
      ref: {
        idsKey,
        entitiesKey
      } = defaultEntitiesRef
    } = options;
    const idsToRemove = coerceArray(ids);
    const newEntities = __spreadValues({}, state[entitiesKey]);
    const newIds = state[idsKey].filter((id) => !idsToRemove.includes(id));
    for (const id of idsToRemove) {
      Reflect.deleteProperty(newEntities, id);
    }
    ctx.setEvent({
      type: "delete",
      ids: idsToRemove
    });
    return __spreadProps(__spreadValues({}, state), {
      [entitiesKey]: newEntities,
      [idsKey]: newIds
    });
  };
}
function deleteEntitiesByPredicate(predicate, options = {}) {
  return function reducer(state, ctx) {
    const ids = findIdsByPredicate(state, options.ref || defaultEntitiesRef, predicate);
    if (ids.length) {
      ctx.setEvent({
        type: "delete",
        ids
      });
      return deleteEntities(ids, options)(state, ctx);
    }
    return state;
  };
}
function deleteAllEntities(options = {}) {
  return function reducer(state, ctx) {
    const {
      ref: {
        idsKey,
        entitiesKey
      } = defaultEntitiesRef
    } = options;
    ctx.setEvent({
      type: "delete",
      ids: []
    });
    return __spreadProps(__spreadValues({}, state), {
      [entitiesKey]: {},
      [idsKey]: []
    });
  };
}
function addEntities(entities, options = {}) {
  return function(state, ctx) {
    const {
      prepend = false,
      ref = defaultEntitiesRef
    } = options;
    const {
      entitiesKey,
      idsKey
    } = ref;
    const idKey = getIdKey(ctx, ref);
    const asArray = coerceArray(entities);
    if (!asArray.length) return state;
    if (isDev()) {
      throwIfEntityExists(asArray, idKey, state, entitiesKey);
      throwIfDuplicateIdKey(asArray, idKey);
    }
    const {
      ids,
      asObject
    } = buildEntities(asArray, idKey);
    ctx.setEvent({
      type: "add",
      ids
    });
    return __spreadProps(__spreadValues({}, state), {
      [entitiesKey]: __spreadValues(__spreadValues({}, state[entitiesKey]), asObject),
      [idsKey]: prepend ? [...ids, ...state[idsKey]] : [...state[idsKey], ...ids]
    });
  };
}
function addEntitiesFifo(entities, options) {
  return function(state, ctx) {
    const {
      ref = defaultEntitiesRef,
      limit
    } = options;
    const {
      entitiesKey,
      idsKey
    } = ref;
    const currentIds = state[idsKey];
    let normalizedEntities = coerceArray(entities);
    let newState = state;
    if (normalizedEntities.length > limit) {
      normalizedEntities = normalizedEntities.slice(normalizedEntities.length - limit);
    }
    const total = currentIds.length + normalizedEntities.length;
    if (total > limit) {
      const idsRemove = currentIds.slice(0, total - limit);
      newState = deleteEntities(idsRemove)(state, ctx);
    }
    const {
      ids,
      asObject
    } = buildEntities(normalizedEntities, getIdKey(ctx, ref));
    ctx.setEvent({
      type: "add",
      ids
    });
    return __spreadProps(__spreadValues({}, state), {
      [entitiesKey]: __spreadValues(__spreadValues({}, newState[entitiesKey]), asObject),
      [idsKey]: [...newState[idsKey], ...ids]
    });
  };
}
function throwIfEntityExists(entities, idKey, state, entitiesKey) {
  entities.forEach((entity) => {
    const id = entity[idKey];
    if (state[entitiesKey][id]) {
      throw Error(`Entity already exists. ${idKey} ${id}`);
    }
  });
}
function throwIfDuplicateIdKey(entities, idKey) {
  const check = /* @__PURE__ */ new Set();
  entities.forEach((entity) => {
    const id = entity[idKey];
    if (check.has(id)) {
      throw Error(`Duplicate entity id provided. ${idKey} ${id}`);
    }
    check.add(id);
  });
}
function setEntities(entities, options = {}) {
  return function(state, ctx) {
    const {
      ref = defaultEntitiesRef
    } = options;
    const {
      entitiesKey,
      idsKey
    } = ref;
    const {
      ids,
      asObject
    } = buildEntities(entities, getIdKey(ctx, ref));
    ctx.setEvent({
      type: "set",
      ids
    });
    return __spreadProps(__spreadValues({}, state), {
      [entitiesKey]: asObject,
      [idsKey]: ids
    });
  };
}
function setEntitiesMap(entities, options = {}) {
  return setEntities(Object.values(entities), options);
}
function getAllEntities(options = {}) {
  const {
    ref: {
      entitiesKey,
      idsKey
    } = defaultEntitiesRef
  } = options;
  return function(state) {
    return state[idsKey].map((id) => state[entitiesKey][id]);
  };
}
function getAllEntitiesApply(options) {
  const {
    ref: {
      entitiesKey,
      idsKey
    } = defaultEntitiesRef,
    filterEntity = () => true,
    mapEntity = (e) => e
  } = options;
  return function(state) {
    const result = [];
    for (const id of state[idsKey]) {
      const entity = state[entitiesKey][id];
      if (filterEntity(entity)) {
        result.push(mapEntity(entity));
      }
    }
    return result;
  };
}
function getEntity$1(id, options = {}) {
  return function(state) {
    const {
      ref: {
        entitiesKey
      } = defaultEntitiesRef
    } = options;
    return state[entitiesKey][id];
  };
}
function getEntityByPredicate(predicate, options = {}) {
  return function(state) {
    const {
      ref: {
        entitiesKey,
        idsKey
      } = defaultEntitiesRef
    } = options;
    const entities = state[entitiesKey];
    const id = state[idsKey].find((id2) => {
      return predicate(entities[id2]);
    });
    return entities[id];
  };
}
function hasEntity(id, options = {}) {
  return function(state) {
    const {
      ref: {
        entitiesKey
      } = defaultEntitiesRef
    } = options;
    return Reflect.has(state[entitiesKey], id);
  };
}
function getEntitiesIds(options = {}) {
  return function(state) {
    const {
      ref: {
        idsKey
      } = defaultEntitiesRef
    } = options;
    return state[idsKey];
  };
}
function toModel(updater, entity) {
  if (isFunction(updater)) {
    return updater(entity);
  }
  return __spreadValues(__spreadValues({}, entity), updater);
}
function updateEntities(ids, updater, options = {}) {
  return function(state, ctx) {
    const coerceIds = coerceArray(ids);
    if (!coerceIds.length) return state;
    const {
      ref: {
        entitiesKey
      } = defaultEntitiesRef
    } = options;
    const updatedEntities = {};
    for (const id of coerceIds) {
      if (hasEntity(id, options)(state)) {
        updatedEntities[id] = toModel(updater, getEntity$1(id, options)(state));
      }
    }
    ctx.setEvent({
      type: "update",
      ids: coerceIds
    });
    return __spreadProps(__spreadValues({}, state), {
      [entitiesKey]: __spreadValues(__spreadValues({}, state[entitiesKey]), updatedEntities)
    });
  };
}
function updateEntitiesByPredicate(predicate, updater, options = {}) {
  return function(state, context) {
    const ids = findIdsByPredicate(state, options.ref || defaultEntitiesRef, predicate);
    if (ids.length) {
      return updateEntities(ids, updater, options)(state, context);
    }
    return state;
  };
}
function updateAllEntities(updater, options = {}) {
  return function(state, context) {
    const {
      ref: {
        idsKey
      } = defaultEntitiesRef
    } = options;
    return updateEntities(state[idsKey], updater, options)(state, context);
  };
}
function upsertEntitiesById(ids, _a) {
  var _b = _a, {
    updater,
    creator
  } = _b, options = __objRest(_b, [
    "updater",
    "creator"
  ]);
  return function(state, ctx) {
    const updatedEntitiesIds = [];
    const newEntities = [];
    const asArray = coerceArray(ids);
    if (!asArray.length) return state;
    for (const id of asArray) {
      if (hasEntity(id, options)(state)) {
        updatedEntitiesIds.push(id);
      } else {
        let newEntity = creator(id);
        if (options.mergeUpdaterWithCreator) {
          newEntity = toModel(updater, newEntity);
        }
        newEntities.push(newEntity);
      }
    }
    const newState = updateEntities(updatedEntitiesIds, updater, options)(state, ctx);
    return addEntities(newEntities, options)(newState, ctx);
  };
}
function upsertEntities(entities, options = {}) {
  return function(state, ctx) {
    const {
      prepend = false,
      ref = defaultEntitiesRef
    } = options;
    const {
      entitiesKey,
      idsKey
    } = ref;
    const idKey = getIdKey(ctx, ref);
    const asObject = {};
    const ids = [];
    const updatedEntitiesId = [];
    const entitiesArray = coerceArray(entities);
    if (!entitiesArray.length) {
      return state;
    }
    for (const entity of entitiesArray) {
      const id = entity[idKey];
      if (hasEntity(id, options)(state)) {
        asObject[id] = __spreadValues(__spreadValues({}, state[entitiesKey][id]), entity);
        updatedEntitiesId.push(id);
      } else {
        ids.push(id);
        asObject[id] = entity;
      }
    }
    const updatedIds = !ids.length ? {} : {
      [idsKey]: prepend ? [...ids, ...state[idsKey]] : [...state[idsKey], ...ids]
    };
    if (ids.length) {
      ctx.setEvent({
        type: "add",
        ids
      });
    }
    if (updatedEntitiesId.length) {
      ctx.setEvent({
        type: "update",
        ids: updatedEntitiesId
      });
    }
    return __spreadProps(__spreadValues(__spreadValues({}, state), updatedIds), {
      [entitiesKey]: __spreadValues(__spreadValues({}, state[entitiesKey]), asObject)
    });
  };
}
function updateEntitiesIds(oldId, newId, options = {}) {
  return function(state, ctx) {
    const oldIds = coerceArray(oldId);
    const newIds = coerceArray(newId);
    if (oldIds.length !== newIds.length) {
      throw new Error("The number of old and new ids must be equal");
    }
    if (!oldIds.length || !newIds.length) return state;
    const {
      ref = defaultEntitiesRef
    } = options;
    const idProp = getIdKey(ctx, ref);
    const updatedEntities = __spreadValues({}, state[ref.entitiesKey]);
    for (let i = 0; i < oldIds.length; i++) {
      const oldVal = oldIds[i];
      const newVal = newIds[i];
      if (state[ref.entitiesKey][newVal]) {
        throw new Error(`Updating id "${oldVal}". The new id "${newVal}" already exists`);
      }
      const oldEntity = state[ref.entitiesKey][oldVal];
      const updated = __spreadProps(__spreadValues({}, oldEntity), {
        [idProp]: newVal
      });
      updatedEntities[newVal] = updated;
      Reflect.deleteProperty(updatedEntities, oldVal);
    }
    const updatedStateIds = state[ref.idsKey].slice();
    let processedIds = 0;
    for (let i = 0; i < updatedStateIds.length; i++) {
      const currentId = updatedStateIds[i];
      for (let j = 0; j < oldIds.length; j++) {
        const oldVal = oldIds[j];
        const newVal = newIds[j];
        if (currentId === oldVal) {
          updatedStateIds[i] = newVal;
          processedIds++;
          break;
        }
      }
      if (processedIds === oldIds.length) {
        break;
      }
    }
    ctx.setEvent({
      type: "update",
      ids: newIds
    });
    return __spreadProps(__spreadValues({}, state), {
      [ref.entitiesKey]: updatedEntities,
      [ref.idsKey]: updatedStateIds
    });
  };
}
function moveEntity(options) {
  return function(state) {
    const {
      fromIndex,
      toIndex,
      ref: {
        idsKey,
        entitiesKey
      } = defaultEntitiesRef
    } = options;
    const ids = state[idsKey].slice();
    ids.splice(toIndex < 0 ? ids.length + toIndex : toIndex, 0, ids.splice(fromIndex, 1)[0]);
    return __spreadProps(__spreadValues({}, state), {
      [entitiesKey]: __spreadValues({}, state[entitiesKey]),
      [idsKey]: ids
    });
  };
}
function untilEntitiesChanges(key) {
  return distinctUntilChanged((prev, current) => {
    return prev[key] === current[key];
  });
}
function selectAllEntities(options = {}) {
  const {
    ref: {
      entitiesKey,
      idsKey
    } = defaultEntitiesRef
  } = options;
  return pipe(untilEntitiesChanges(entitiesKey), map((state) => state[idsKey].map((id) => state[entitiesKey][id])));
}
function selectEntities(options = {}) {
  const {
    ref: {
      entitiesKey
    } = defaultEntitiesRef
  } = options;
  return select((state) => state[entitiesKey]);
}
function selectAllEntitiesApply(options) {
  const {
    ref: {
      entitiesKey,
      idsKey
    } = defaultEntitiesRef,
    filterEntity = () => true,
    mapEntity = (e) => e
  } = options;
  return pipe(untilEntitiesChanges(entitiesKey), map((state) => {
    const result = [];
    for (const id of state[idsKey]) {
      const entity = state[entitiesKey][id];
      if (filterEntity(entity)) {
        result.push(mapEntity(entity));
      }
    }
    return result;
  }));
}
function selectEntity(id, options = {}) {
  const {
    ref: {
      entitiesKey
    } = defaultEntitiesRef,
    pluck
  } = options;
  return pipe(untilEntitiesChanges(entitiesKey), select((state) => getEntity(state[entitiesKey], id, pluck)));
}
function getEntity(entities, id, pluck) {
  const entity = entities[id];
  if (isUndefined(entity)) {
    return void 0;
  }
  if (!pluck) {
    return entity;
  }
  return checkPluck(entity, pluck);
}
function selectEntityByPredicate(predicate, options) {
  const {
    ref = defaultEntitiesRef,
    pluck,
    idKey = "id"
  } = options || {};
  const {
    entitiesKey
  } = ref;
  let id;
  return pipe(select((state) => {
    if (isUndefined(id)) {
      const entity = findEntityByPredicate(state, ref, predicate);
      id = entity && entity[idKey];
    }
    return state[entitiesKey][id];
  }), map((entity) => entity ? checkPluck(entity, pluck) : void 0), distinctUntilChanged());
}
function selectFirst(options = {}) {
  const {
    ref: {
      entitiesKey,
      idsKey
    } = defaultEntitiesRef
  } = options;
  return select((state) => state[entitiesKey][state[idsKey][0]]);
}
function selectLast(options = {}) {
  const {
    ref: {
      entitiesKey,
      idsKey
    } = defaultEntitiesRef
  } = options;
  return select((state) => state[entitiesKey][state[idsKey][state[idsKey].length - 1]]);
}
function selectMany(ids, options = {}) {
  const {
    ref: {
      entitiesKey
    } = defaultEntitiesRef,
    pluck
  } = options;
  return pipe(select((state) => state[entitiesKey]), map((entities) => {
    if (!ids.length) return [];
    const filtered = [];
    for (const id of ids) {
      const entity = getEntity(entities, id, pluck);
      if (!isUndefined(entity)) filtered.push(entity);
    }
    return filtered;
  }), distinctUntilArrayItemChanged());
}
function selectManyByPredicate(predicate, options) {
  const {
    ref: {
      entitiesKey,
      idsKey
    } = defaultEntitiesRef,
    pluck
  } = options || {};
  return pipe(untilEntitiesChanges(entitiesKey), select((state) => {
    const filteredEntities = [];
    state[idsKey].forEach((id, index) => {
      const entity = state[entitiesKey][id];
      if (predicate(entity, index)) {
        filteredEntities.push(checkPluck(entity, pluck));
      }
    });
    return filteredEntities;
  }), distinctUntilArrayItemChanged());
}
function selectEntitiesCount(options = {}) {
  const {
    ref: {
      idsKey
    } = defaultEntitiesRef
  } = options;
  return select((state) => state[idsKey].length);
}
function selectEntitiesCountByPredicate(predicate, options = {}) {
  const ref = options.ref || defaultEntitiesRef;
  return pipe(untilEntitiesChanges(ref.entitiesKey), map((state) => findIdsByPredicate(state, ref, predicate).length), distinctUntilChanged());
}
function getEntitiesCount(options = {}) {
  return function(state) {
    const {
      ref: {
        idsKey
      } = defaultEntitiesRef
    } = options;
    return state[idsKey].length;
  };
}
function getEntitiesCountByPredicate(predicate, options = {}) {
  return function(state) {
    const ref = options.ref || defaultEntitiesRef;
    return findIdsByPredicate(state, ref, predicate).length;
  };
}
function unionEntities(idKey = "id") {
  return map((state) => {
    return state.entities.map((entity) => {
      return __spreadValues(__spreadValues({}, entity), state.UIEntities[entity[idKey]]);
    });
  });
}
function unionEntitiesAsMap(idKey = "id") {
  return map((state) => {
    return Object.fromEntries(state.entities.map((entity) => {
      return [entity[idKey], __spreadValues(__spreadValues({}, entity), state.UIEntities[entity[idKey]])];
    }));
  });
}
var {
  selectActiveId,
  setActiveId,
  withActiveId,
  resetActiveId,
  getActiveId
} = propsFactory("activeId", {
  initialValue: void 0
});
function selectActiveEntity(options = {}) {
  const {
    ref = defaultEntitiesRef
  } = options;
  return function(source) {
    return source.pipe(selectActiveId()).pipe(switchMap((id) => source.pipe(selectEntity(id, {
      ref
    }))));
  };
}
function getActiveEntity(options = {}) {
  const {
    ref: {
      entitiesKey
    } = defaultEntitiesRef
  } = options;
  return function(state) {
    return state[entitiesKey][getActiveId(state)];
  };
}
var {
  setActiveIds,
  resetActiveIds,
  withActiveIds,
  selectActiveIds,
  toggleActiveIds,
  removeActiveIds,
  addActiveIds,
  getActiveIds
} = propsArrayFactory("activeIds", {
  initialValue: []
});
function selectActiveEntities(options = {}) {
  const {
    ref = defaultEntitiesRef
  } = options;
  return function(source) {
    return source.pipe(selectActiveIds()).pipe(switchMap((ids) => source.pipe(selectMany(ids, {
      ref
    }))));
  };
}
function getActiveEntities(options = {}) {
  const {
    ref: {
      entitiesKey
    } = defaultEntitiesRef
  } = options;
  return function(state) {
    const result = [];
    for (const id of getActiveIds(state)) {
      const entity = state[entitiesKey][id];
      if (entity) {
        result.push(entity);
      }
    }
    return result;
  };
}
export {
  EntitiesRef,
  UIEntitiesRef,
  addActiveIds,
  addEntities,
  addEntitiesFifo,
  deleteAllEntities,
  deleteEntities,
  deleteEntitiesByPredicate,
  entitiesPropsFactory,
  getActiveEntities,
  getActiveEntity,
  getActiveId,
  getActiveIds,
  getAllEntities,
  getAllEntitiesApply,
  getEntitiesCount,
  getEntitiesCountByPredicate,
  getEntitiesIds,
  getEntity$1 as getEntity,
  getEntityByPredicate,
  hasEntity,
  moveEntity,
  removeActiveIds,
  resetActiveId,
  resetActiveIds,
  selectActiveEntities,
  selectActiveEntity,
  selectActiveId,
  selectActiveIds,
  selectAllEntities,
  selectAllEntitiesApply,
  selectEntities,
  selectEntitiesCount,
  selectEntitiesCountByPredicate,
  selectEntity,
  selectEntityByPredicate,
  selectFirst,
  selectLast,
  selectMany,
  selectManyByPredicate,
  setActiveId,
  setActiveIds,
  setEntities,
  setEntitiesMap,
  toggleActiveIds,
  unionEntities,
  unionEntitiesAsMap,
  updateAllEntities,
  updateEntities,
  updateEntitiesByPredicate,
  updateEntitiesIds,
  upsertEntities,
  upsertEntitiesById,
  withActiveId,
  withActiveIds,
  withEntities,
  withUIEntities
};
//# sourceMappingURL=@ngneat_elf-entities.js.map
