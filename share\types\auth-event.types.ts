

export type AuthEventType = keyof AuthEventReq;
export type EventConfirmType = "waiting" | "confirmed" | "agreed" | "disagreed";


export interface AuthEventReq {
  timing: SyncTime;
}

export interface AuthEventRes {
  status: 'success' | 'failed';
  info?: {
    event_id: string;
    type: AuthEventType;
    data?: string;
  };
  err?: string;
}

// 考点校时
export interface SyncTime {
  schedule_id: string;
}

export enum EAuthEventTypes {
  SyncTime = 601,
}
export type EventTypeNum = keyof typeof EventTypeMap;

export const EventTypeMap = {
  601:"校时"
}

