{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_@angular+animations@19.2.9_@angular+common@19.2.9_@angular+core@19.2.9_r_i5xlsxr3thsqcwddfzjvckvf54/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-dropdown.mjs"], "sourcesContent": ["import { __esDecorate, __runInitializers } from 'tslib';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i1$2 from '@angular/cdk/overlay';\nimport { Overlay, ConnectionPositionPair } from '@angular/cdk/overlay';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { inject, ElementRef, EventEmitter, booleanAttribute, Output, Input, Directive, NgModule, TemplateRef, ViewChild, ChangeDetectionStrategy, ViewEncapsulation, Component, Injectable } from '@angular/core';\nimport { Subject, BehaviorSubject, merge, fromEvent, EMPTY, combineLatest, Subscription } from 'rxjs';\nimport { map, switchMap, filter, auditTime, distinctUntilChanged, takeUntil, first } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { POSITION_MAP } from 'ng-zorro-antd/core/overlay';\nimport * as i2 from '@angular/cdk/platform';\nimport { MenuService, NzIsMenuInsideDropDownToken, NzMenuModule } from 'ng-zorro-antd/menu';\nimport { NzButtonGroupComponent } from 'ng-zorro-antd/button';\nimport { slideMotion } from 'ng-zorro-antd/core/animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport * as i1$1 from '@angular/cdk/bidi';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\nconst _c0 = [\"*\"];\nfunction NzDropdownMenuComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"@slideMotion.done\", function NzDropdownMenuComponent_ng_template_0_Template_div_animation_slideMotion_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAnimationEvent($event));\n    })(\"mouseenter\", function NzDropdownMenuComponent_ng_template_0_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setMouseState(true));\n    })(\"mouseleave\", function NzDropdownMenuComponent_ng_template_0_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setMouseState(false));\n    });\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r1.nzOverlayStyle);\n    i0.ɵɵclassMap(ctx_r1.nzOverlayClassName);\n    i0.ɵɵclassProp(\"ant-dropdown-rtl\", ctx_r1.dir === \"rtl\");\n    i0.ɵɵproperty(\"@slideMotion\", undefined)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation);\n  }\n}\nconst NZ_CONFIG_MODULE_NAME = 'dropDown';\nconst listOfPositions = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];\nlet NzDropDownDirective = (() => {\n  let _nzBackdrop_decorators;\n  let _nzBackdrop_initializers = [];\n  let _nzBackdrop_extraInitializers = [];\n  return class NzDropDownDirective {\n    static {\n      const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(null) : void 0;\n      _nzBackdrop_decorators = [WithConfig()];\n      __esDecorate(null, null, _nzBackdrop_decorators, {\n        kind: \"field\",\n        name: \"nzBackdrop\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzBackdrop\" in obj,\n          get: obj => obj.nzBackdrop,\n          set: (obj, value) => {\n            obj.nzBackdrop = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzBackdrop_initializers, _nzBackdrop_extraInitializers);\n      if (_metadata) Object.defineProperty(this, Symbol.metadata, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value: _metadata\n      });\n    }\n    nzConfigService;\n    renderer;\n    viewContainerRef;\n    platform;\n    _nzModuleName = NZ_CONFIG_MODULE_NAME;\n    elementRef = inject(ElementRef);\n    overlay = inject(Overlay);\n    portal;\n    overlayRef = null;\n    destroy$ = new Subject();\n    positionStrategy = this.overlay.position().flexibleConnectedTo(this.elementRef.nativeElement).withLockedPosition().withTransformOriginOn('.ant-dropdown');\n    inputVisible$ = new BehaviorSubject(false);\n    nzTrigger$ = new BehaviorSubject('hover');\n    overlayClose$ = new Subject();\n    nzDropdownMenu = null;\n    nzTrigger = 'hover';\n    nzMatchWidthElement = null;\n    nzBackdrop = __runInitializers(this, _nzBackdrop_initializers, false);\n    nzClickHide = (__runInitializers(this, _nzBackdrop_extraInitializers), true);\n    nzDisabled = false;\n    nzVisible = false;\n    nzOverlayClassName = '';\n    nzOverlayStyle = {};\n    nzPlacement = 'bottomLeft';\n    nzVisibleChange = new EventEmitter();\n    setDropdownMenuValue(key, value) {\n      if (this.nzDropdownMenu) {\n        this.nzDropdownMenu.setValue(key, value);\n      }\n    }\n    constructor(nzConfigService, renderer, viewContainerRef, platform) {\n      this.nzConfigService = nzConfigService;\n      this.renderer = renderer;\n      this.viewContainerRef = viewContainerRef;\n      this.platform = platform;\n    }\n    ngAfterViewInit() {\n      if (this.nzDropdownMenu) {\n        const nativeElement = this.elementRef.nativeElement;\n        /** host mouse state **/\n        const hostMouseState$ = merge(fromEvent(nativeElement, 'mouseenter').pipe(map(() => true)), fromEvent(nativeElement, 'mouseleave').pipe(map(() => false)));\n        /** menu mouse state **/\n        const menuMouseState$ = this.nzDropdownMenu.mouseState$;\n        /** merged mouse state **/\n        const mergedMouseState$ = merge(menuMouseState$, hostMouseState$);\n        /** host click state **/\n        const hostClickState$ = fromEvent(nativeElement, 'click').pipe(map(() => !this.nzVisible));\n        /** visible state switch by nzTrigger **/\n        const visibleStateByTrigger$ = this.nzTrigger$.pipe(switchMap(trigger => {\n          if (trigger === 'hover') {\n            return mergedMouseState$;\n          } else if (trigger === 'click') {\n            return hostClickState$;\n          } else {\n            return EMPTY;\n          }\n        }));\n        const descendantMenuItemClick$ = this.nzDropdownMenu.descendantMenuItemClick$.pipe(filter(() => this.nzClickHide), map(() => false));\n        const domTriggerVisible$ = merge(visibleStateByTrigger$, descendantMenuItemClick$, this.overlayClose$).pipe(filter(() => !this.nzDisabled));\n        const visible$ = merge(this.inputVisible$, domTriggerVisible$);\n        combineLatest([visible$, this.nzDropdownMenu.isChildSubMenuOpen$]).pipe(map(([visible, sub]) => visible || sub), auditTime(150), distinctUntilChanged(), filter(() => this.platform.isBrowser), takeUntil(this.destroy$)).subscribe(visible => {\n          const element = this.nzMatchWidthElement ? this.nzMatchWidthElement.nativeElement : nativeElement;\n          const triggerWidth = element.getBoundingClientRect().width;\n          if (this.nzVisible !== visible) {\n            this.nzVisibleChange.emit(visible);\n          }\n          this.nzVisible = visible;\n          if (visible) {\n            /** set up overlayRef **/\n            if (!this.overlayRef) {\n              /** new overlay **/\n              this.overlayRef = this.overlay.create({\n                positionStrategy: this.positionStrategy,\n                minWidth: triggerWidth,\n                disposeOnNavigation: true,\n                hasBackdrop: this.nzBackdrop && this.nzTrigger === 'click',\n                scrollStrategy: this.overlay.scrollStrategies.reposition()\n              });\n              merge(this.overlayRef.backdropClick(), this.overlayRef.detachments(), this.overlayRef.outsidePointerEvents().pipe(filter(e => !this.elementRef.nativeElement.contains(e.target))), this.overlayRef.keydownEvents().pipe(filter(e => e.keyCode === ESCAPE && !hasModifierKey(e)))).pipe(takeUntil(this.destroy$)).subscribe(() => {\n                this.overlayClose$.next(false);\n              });\n            } else {\n              /** update overlay config **/\n              const overlayConfig = this.overlayRef.getConfig();\n              overlayConfig.minWidth = triggerWidth;\n            }\n            /** open dropdown with animation **/\n            this.positionStrategy.withPositions([POSITION_MAP[this.nzPlacement], ...listOfPositions]);\n            /** reset portal if needed **/\n            if (!this.portal || this.portal.templateRef !== this.nzDropdownMenu.templateRef) {\n              this.portal = new TemplatePortal(this.nzDropdownMenu.templateRef, this.viewContainerRef);\n            }\n            this.overlayRef.attach(this.portal);\n          } else {\n            /** detach overlayRef if needed **/\n            if (this.overlayRef) {\n              this.overlayRef.detach();\n            }\n          }\n        });\n        this.nzDropdownMenu.animationStateChange$.pipe(takeUntil(this.destroy$)).subscribe(event => {\n          if (event.toState === 'void') {\n            if (this.overlayRef) {\n              this.overlayRef.dispose();\n            }\n            this.overlayRef = null;\n          }\n        });\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next(true);\n      this.destroy$.complete();\n      if (this.overlayRef) {\n        this.overlayRef.dispose();\n        this.overlayRef = null;\n      }\n    }\n    ngOnChanges(changes) {\n      const {\n        nzVisible,\n        nzDisabled,\n        nzOverlayClassName,\n        nzOverlayStyle,\n        nzTrigger\n      } = changes;\n      if (nzTrigger) {\n        this.nzTrigger$.next(this.nzTrigger);\n      }\n      if (nzVisible) {\n        this.inputVisible$.next(this.nzVisible);\n      }\n      if (nzDisabled) {\n        const nativeElement = this.elementRef.nativeElement;\n        if (this.nzDisabled) {\n          this.renderer.setAttribute(nativeElement, 'disabled', '');\n          this.inputVisible$.next(false);\n        } else {\n          this.renderer.removeAttribute(nativeElement, 'disabled');\n        }\n      }\n      if (nzOverlayClassName) {\n        this.setDropdownMenuValue('nzOverlayClassName', this.nzOverlayClassName);\n      }\n      if (nzOverlayStyle) {\n        this.setDropdownMenuValue('nzOverlayStyle', this.nzOverlayStyle);\n      }\n    }\n    static ɵfac = function NzDropDownDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NzDropDownDirective)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2.Platform));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzDropDownDirective,\n      selectors: [[\"\", \"nz-dropdown\", \"\"]],\n      hostAttrs: [1, \"ant-dropdown-trigger\"],\n      inputs: {\n        nzDropdownMenu: \"nzDropdownMenu\",\n        nzTrigger: \"nzTrigger\",\n        nzMatchWidthElement: \"nzMatchWidthElement\",\n        nzBackdrop: [2, \"nzBackdrop\", \"nzBackdrop\", booleanAttribute],\n        nzClickHide: [2, \"nzClickHide\", \"nzClickHide\", booleanAttribute],\n        nzDisabled: [2, \"nzDisabled\", \"nzDisabled\", booleanAttribute],\n        nzVisible: [2, \"nzVisible\", \"nzVisible\", booleanAttribute],\n        nzOverlayClassName: \"nzOverlayClassName\",\n        nzOverlayStyle: \"nzOverlayStyle\",\n        nzPlacement: \"nzPlacement\"\n      },\n      outputs: {\n        nzVisibleChange: \"nzVisibleChange\"\n      },\n      exportAs: [\"nzDropdown\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  };\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropDownDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-dropdown]',\n      exportAs: 'nzDropdown',\n      host: {\n        class: 'ant-dropdown-trigger'\n      }\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i2.Platform\n  }], {\n    nzDropdownMenu: [{\n      type: Input\n    }],\n    nzTrigger: [{\n      type: Input\n    }],\n    nzMatchWidthElement: [{\n      type: Input\n    }],\n    nzBackdrop: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzClickHide: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzVisible: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzOverlayClassName: [{\n      type: Input\n    }],\n    nzOverlayStyle: [{\n      type: Input\n    }],\n    nzPlacement: [{\n      type: Input\n    }],\n    nzVisibleChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzContextMenuServiceModule {\n  static ɵfac = function NzContextMenuServiceModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzContextMenuServiceModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzContextMenuServiceModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzContextMenuServiceModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropDownADirective {\n  static ɵfac = function NzDropDownADirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzDropDownADirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzDropDownADirective,\n    selectors: [[\"a\", \"nz-dropdown\", \"\"]],\n    hostAttrs: [1, \"ant-dropdown-link\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropDownADirective, [{\n    type: Directive,\n    args: [{\n      selector: 'a[nz-dropdown]',\n      host: {\n        class: 'ant-dropdown-link'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropdownButtonDirective {\n  renderer;\n  elementRef;\n  nzButtonGroupComponent = inject(NzButtonGroupComponent, {\n    host: true,\n    optional: true\n  });\n  constructor(renderer, elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n  }\n  ngAfterViewInit() {\n    const parentElement = this.renderer.parentNode(this.elementRef.nativeElement);\n    if (this.nzButtonGroupComponent && parentElement) {\n      this.renderer.addClass(parentElement, 'ant-dropdown-button');\n    }\n  }\n  static ɵfac = function NzDropdownButtonDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzDropdownButtonDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzDropdownButtonDirective,\n    selectors: [[\"\", \"nz-button\", \"\", \"nz-dropdown\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropdownButtonDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-button][nz-dropdown]'\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\nclass NzDropdownMenuComponent {\n  cdr;\n  elementRef;\n  renderer;\n  viewContainerRef;\n  directionality;\n  mouseState$ = new BehaviorSubject(false);\n  nzMenuService = inject(MenuService);\n  isChildSubMenuOpen$ = this.nzMenuService.isChildSubMenuOpen$;\n  descendantMenuItemClick$ = this.nzMenuService.descendantMenuItemClick$;\n  animationStateChange$ = new EventEmitter();\n  nzOverlayClassName = '';\n  nzOverlayStyle = {};\n  templateRef;\n  dir = 'ltr';\n  destroy$ = new Subject();\n  onAnimationEvent(event) {\n    this.animationStateChange$.emit(event);\n  }\n  setMouseState(visible) {\n    this.mouseState$.next(visible);\n  }\n  setValue(key, value) {\n    this[key] = value;\n    this.cdr.markForCheck();\n  }\n  noAnimation = inject(NzNoAnimationDirective, {\n    host: true,\n    optional: true\n  });\n  constructor(cdr, elementRef, renderer, viewContainerRef, directionality) {\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.viewContainerRef = viewContainerRef;\n    this.directionality = directionality;\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngAfterContentInit() {\n    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzDropdownMenuComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzDropdownMenuComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1$1.Directionality));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzDropdownMenuComponent,\n    selectors: [[\"nz-dropdown-menu\"]],\n    viewQuery: function NzDropdownMenuComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(TemplateRef, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n      }\n    },\n    exportAs: [\"nzDropdownMenu\"],\n    features: [i0.ɵɵProvidersFeature([MenuService, /** menu is inside dropdown-menu component **/\n    {\n      provide: NzIsMenuInsideDropDownToken,\n      useValue: true\n    }])],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    consts: [[1, \"ant-dropdown\", 3, \"mouseenter\", \"mouseleave\", \"nzNoAnimation\"]],\n    template: function NzDropdownMenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NzDropdownMenuComponent_ng_template_0_Template, 2, 9, \"ng-template\");\n      }\n    },\n    dependencies: [NzNoAnimationDirective],\n    encapsulation: 2,\n    data: {\n      animation: [slideMotion]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropdownMenuComponent, [{\n    type: Component,\n    args: [{\n      selector: `nz-dropdown-menu`,\n      exportAs: `nzDropdownMenu`,\n      animations: [slideMotion],\n      providers: [MenuService, /** menu is inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useValue: true\n      }],\n      template: `\n    <ng-template>\n      <div\n        class=\"ant-dropdown\"\n        [class.ant-dropdown-rtl]=\"dir === 'rtl'\"\n        [class]=\"nzOverlayClassName\"\n        [style]=\"nzOverlayStyle\"\n        @slideMotion\n        (@slideMotion.done)=\"onAnimationEvent($event)\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        (mouseenter)=\"setMouseState(true)\"\n        (mouseleave)=\"setMouseState(false)\"\n      >\n        <ng-content></ng-content>\n      </div>\n    </ng-template>\n  `,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NzNoAnimationDirective]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i1$1.Directionality\n  }], {\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropDownModule {\n  static ɵfac = function NzDropDownModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzDropDownModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzDropDownModule,\n    imports: [NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective, NzContextMenuServiceModule],\n    exports: [NzMenuModule, NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzContextMenuServiceModule, NzMenuModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropDownModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective, NzContextMenuServiceModule],\n      exports: [NzMenuModule, NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst LIST_OF_POSITIONS = [new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'start',\n  overlayY: 'top'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'start',\n  overlayY: 'bottom'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'end',\n  overlayY: 'bottom'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'end',\n  overlayY: 'top'\n})];\nclass NzContextMenuService {\n  ngZone;\n  overlay;\n  overlayRef = null;\n  closeSubscription = Subscription.EMPTY;\n  constructor(ngZone, overlay) {\n    this.ngZone = ngZone;\n    this.overlay = overlay;\n  }\n  create($event, nzDropdownMenuComponent) {\n    this.close(true);\n    const {\n      x,\n      y\n    } = $event;\n    if ($event instanceof MouseEvent) {\n      $event.preventDefault();\n    }\n    const positionStrategy = this.overlay.position().flexibleConnectedTo({\n      x,\n      y\n    }).withPositions(LIST_OF_POSITIONS).withTransformOriginOn('.ant-dropdown');\n    this.overlayRef = this.overlay.create({\n      positionStrategy,\n      disposeOnNavigation: true,\n      scrollStrategy: this.overlay.scrollStrategies.close()\n    });\n    this.closeSubscription = new Subscription();\n    this.closeSubscription.add(nzDropdownMenuComponent.descendantMenuItemClick$.subscribe(() => this.close()));\n    this.closeSubscription.add(merge(fromEventOutsideAngular(document, 'click').pipe(filter(event => !!this.overlayRef && !this.overlayRef.overlayElement.contains(event.target)), /** handle firefox contextmenu event **/\n    filter(event => event.button !== 2)), fromEventOutsideAngular(document, 'keydown').pipe(filter(event => event.key === 'Escape'))).pipe(first()).subscribe(() => this.ngZone.run(() => this.close())));\n    return this.overlayRef.attach(new TemplatePortal(nzDropdownMenuComponent.templateRef, nzDropdownMenuComponent.viewContainerRef));\n  }\n  close(clear = false) {\n    if (this.overlayRef) {\n      this.overlayRef.detach();\n      if (clear) {\n        this.overlayRef.dispose();\n      }\n      this.overlayRef = null;\n      this.closeSubscription.unsubscribe();\n    }\n  }\n  static ɵfac = function NzContextMenuService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzContextMenuService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1$2.Overlay));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzContextMenuService,\n    factory: NzContextMenuService.ɵfac,\n    providedIn: NzContextMenuServiceModule\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzContextMenuService, [{\n    type: Injectable,\n    args: [{\n      providedIn: NzContextMenuServiceModule\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1$2.Overlay\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzContextMenuService, NzContextMenuServiceModule, NzDropDownADirective, NzDropDownDirective, NzDropDownModule, NzDropdownButtonDirective, NzDropdownMenuComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,qBAAqB,SAAS,yFAAyF,QAAQ;AAC3I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,cAAc,SAAS,2EAA2E;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,IAAI,CAAC;AAAA,IAClD,CAAC,EAAE,cAAc,SAAS,2EAA2E;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,KAAK,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,cAAc;AACnC,IAAG,WAAW,OAAO,kBAAkB;AACvC,IAAG,YAAY,oBAAoB,OAAO,QAAQ,KAAK;AACvD,IAAG,WAAW,gBAAgB,MAAS,EAAE,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa;AAAA,EACxN;AACF;AACA,IAAM,wBAAwB;AAC9B,IAAM,kBAAkB,CAAC,aAAa,YAAY,aAAa,aAAa,aAAa,UAAU,aAAa,OAAO;AACvH,IAAI,uBAAuB,MAAM;AAlDjC;AAmDE,MAAI;AACJ,MAAI,2BAA2B,CAAC;AAChC,MAAI,gCAAgC,CAAC;AACrC,SAAO,WAA0B;AAAA,IAyB/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,aAAa,OAAO,UAAU;AAAA,IAC9B,UAAU,OAAO,OAAO;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,IACb,WAAW,IAAI,QAAQ;AAAA,IACvB,mBAAmB,KAAK,QAAQ,SAAS,EAAE,oBAAoB,KAAK,WAAW,aAAa,EAAE,mBAAmB,EAAE,sBAAsB,eAAe;AAAA,IACxJ,gBAAgB,IAAI,gBAAgB,KAAK;AAAA,IACzC,aAAa,IAAI,gBAAgB,OAAO;AAAA,IACxC,gBAAgB,IAAI,QAAQ;AAAA,IAC5B,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,sBAAsB;AAAA,IACtB,aAAa,kBAAkB,MAAM,0BAA0B,KAAK;AAAA,IACpE,eAAe,kBAAkB,MAAM,6BAA6B,GAAG;AAAA,IACvE,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,iBAAiB,CAAC;AAAA,IAClB,cAAc;AAAA,IACd,kBAAkB,IAAI,aAAa;AAAA,IACnC,qBAAqB,KAAK,OAAO;AAC/B,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,SAAS,KAAK,KAAK;AAAA,MACzC;AAAA,IACF;AAAA,IACA,YAAY,iBAAiB,UAAU,kBAAkB,UAAU;AACjE,WAAK,kBAAkB;AACvB,WAAK,WAAW;AAChB,WAAK,mBAAmB;AACxB,WAAK,WAAW;AAAA,IAClB;AAAA,IACA,kBAAkB;AAChB,UAAI,KAAK,gBAAgB;AACvB,cAAM,gBAAgB,KAAK,WAAW;AAEtC,cAAM,kBAAkB,MAAM,UAAU,eAAe,YAAY,EAAE,KAAK,IAAI,MAAM,IAAI,CAAC,GAAG,UAAU,eAAe,YAAY,EAAE,KAAK,IAAI,MAAM,KAAK,CAAC,CAAC;AAEzJ,cAAM,kBAAkB,KAAK,eAAe;AAE5C,cAAM,oBAAoB,MAAM,iBAAiB,eAAe;AAEhE,cAAM,kBAAkB,UAAU,eAAe,OAAO,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,SAAS,CAAC;AAEzF,cAAM,yBAAyB,KAAK,WAAW,KAAK,UAAU,aAAW;AACvE,cAAI,YAAY,SAAS;AACvB,mBAAO;AAAA,UACT,WAAW,YAAY,SAAS;AAC9B,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,CAAC,CAAC;AACF,cAAM,2BAA2B,KAAK,eAAe,yBAAyB,KAAK,OAAO,MAAM,KAAK,WAAW,GAAG,IAAI,MAAM,KAAK,CAAC;AACnI,cAAM,qBAAqB,MAAM,wBAAwB,0BAA0B,KAAK,aAAa,EAAE,KAAK,OAAO,MAAM,CAAC,KAAK,UAAU,CAAC;AAC1I,cAAM,WAAW,MAAM,KAAK,eAAe,kBAAkB;AAC7D,sBAAc,CAAC,UAAU,KAAK,eAAe,mBAAmB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,MAAM,WAAW,GAAG,GAAG,UAAU,GAAG,GAAG,qBAAqB,GAAG,OAAO,MAAM,KAAK,SAAS,SAAS,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,aAAW;AAC7O,gBAAM,UAAU,KAAK,sBAAsB,KAAK,oBAAoB,gBAAgB;AACpF,gBAAM,eAAe,QAAQ,sBAAsB,EAAE;AACrD,cAAI,KAAK,cAAc,SAAS;AAC9B,iBAAK,gBAAgB,KAAK,OAAO;AAAA,UACnC;AACA,eAAK,YAAY;AACjB,cAAI,SAAS;AAEX,gBAAI,CAAC,KAAK,YAAY;AAEpB,mBAAK,aAAa,KAAK,QAAQ,OAAO;AAAA,gBACpC,kBAAkB,KAAK;AAAA,gBACvB,UAAU;AAAA,gBACV,qBAAqB;AAAA,gBACrB,aAAa,KAAK,cAAc,KAAK,cAAc;AAAA,gBACnD,gBAAgB,KAAK,QAAQ,iBAAiB,WAAW;AAAA,cAC3D,CAAC;AACD,oBAAM,KAAK,WAAW,cAAc,GAAG,KAAK,WAAW,YAAY,GAAG,KAAK,WAAW,qBAAqB,EAAE,KAAK,OAAO,OAAK,CAAC,KAAK,WAAW,cAAc,SAAS,EAAE,MAAM,CAAC,CAAC,GAAG,KAAK,WAAW,cAAc,EAAE,KAAK,OAAO,OAAK,EAAE,YAAY,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC/T,qBAAK,cAAc,KAAK,KAAK;AAAA,cAC/B,CAAC;AAAA,YACH,OAAO;AAEL,oBAAM,gBAAgB,KAAK,WAAW,UAAU;AAChD,4BAAc,WAAW;AAAA,YAC3B;AAEA,iBAAK,iBAAiB,cAAc,CAAC,aAAa,KAAK,WAAW,GAAG,GAAG,eAAe,CAAC;AAExF,gBAAI,CAAC,KAAK,UAAU,KAAK,OAAO,gBAAgB,KAAK,eAAe,aAAa;AAC/E,mBAAK,SAAS,IAAI,eAAe,KAAK,eAAe,aAAa,KAAK,gBAAgB;AAAA,YACzF;AACA,iBAAK,WAAW,OAAO,KAAK,MAAM;AAAA,UACpC,OAAO;AAEL,gBAAI,KAAK,YAAY;AACnB,mBAAK,WAAW,OAAO;AAAA,YACzB;AAAA,UACF;AAAA,QACF,CAAC;AACD,aAAK,eAAe,sBAAsB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC1F,cAAI,MAAM,YAAY,QAAQ;AAC5B,gBAAI,KAAK,YAAY;AACnB,mBAAK,WAAW,QAAQ;AAAA,YAC1B;AACA,iBAAK,aAAa;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,cAAc;AACZ,WAAK,SAAS,KAAK,IAAI;AACvB,WAAK,SAAS,SAAS;AACvB,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,QAAQ;AACxB,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAAA,IACA,YAAY,SAAS;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,WAAW;AACb,aAAK,WAAW,KAAK,KAAK,SAAS;AAAA,MACrC;AACA,UAAI,WAAW;AACb,aAAK,cAAc,KAAK,KAAK,SAAS;AAAA,MACxC;AACA,UAAI,YAAY;AACd,cAAM,gBAAgB,KAAK,WAAW;AACtC,YAAI,KAAK,YAAY;AACnB,eAAK,SAAS,aAAa,eAAe,YAAY,EAAE;AACxD,eAAK,cAAc,KAAK,KAAK;AAAA,QAC/B,OAAO;AACL,eAAK,SAAS,gBAAgB,eAAe,UAAU;AAAA,QACzD;AAAA,MACF;AACA,UAAI,oBAAoB;AACtB,aAAK,qBAAqB,sBAAsB,KAAK,kBAAkB;AAAA,MACzE;AACA,UAAI,gBAAgB;AAClB,aAAK,qBAAqB,kBAAkB,KAAK,cAAc;AAAA,MACjE;AAAA,IACF;AAAA,EA0BF,IArME,MAAO;AACL,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,uBAAO,OAAO,IAAI,IAAI;AAC1F,6BAAyB,CAAC,WAAW,CAAC;AACtC,iBAAa,MAAM,MAAM,wBAAwB;AAAA,MAC/C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,KAAK,SAAO,gBAAgB;AAAA,QAC5B,KAAK,SAAO,IAAI;AAAA,QAChB,KAAK,CAAC,KAAK,UAAU;AACnB,cAAI,aAAa;AAAA,QACnB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,IACZ,GAAG,0BAA0B,6BAA6B;AAC1D,QAAI,UAAW,QAAO,eAAe,IAAM,OAAO,UAAU;AAAA,MAC1D,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT,CAAC;AAAA,EACH,MAqJA,cA7KK,IA6KE,QAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,IAAwB,kBAAqB,eAAe,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,QAAQ,CAAC;AAAA,EAClN,IACA,cAhLK,IAgLE,QAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IACnC,WAAW,CAAC,GAAG,sBAAsB;AAAA,IACrC,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,qBAAqB;AAAA,MACrB,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,MAC/D,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,IACvB,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC,IArMI;AAuMT,GAAG;AAAA,CACF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,8BAAN,MAAM,4BAA2B;AAQjC;AAPE,cADI,6BACG,QAAO,SAAS,mCAAmC,mBAAmB;AAC3E,SAAO,KAAK,qBAAqB,6BAA4B;AAC/D;AACA,cAJI,6BAIG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AACR,CAAC;AACD,cAPI,6BAOG,QAAyB,iBAAiB,CAAC,CAAC;AAPrD,IAAM,6BAAN;AAAA,CASC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,wBAAN,MAAM,sBAAqB;AAS3B;AARE,cADI,uBACG,QAAO,SAAS,6BAA6B,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,uBAAsB;AACzD;AACA,cAJI,uBAIG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,KAAK,eAAe,EAAE,CAAC;AAAA,EACpC,WAAW,CAAC,GAAG,mBAAmB;AACpC,CAAC;AARH,IAAM,uBAAN;AAAA,CAUC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,6BAAN,MAAM,2BAA0B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,yBAAyB,OAAO,wBAAwB;AAAA,IACtD,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,UAAU,YAAY;AAChC,SAAK,WAAW;AAChB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,kBAAkB;AAChB,UAAM,gBAAgB,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa;AAC5E,QAAI,KAAK,0BAA0B,eAAe;AAChD,WAAK,SAAS,SAAS,eAAe,qBAAqB;AAAA,IAC7D;AAAA,EACF;AAQF;AAPE,cAjBI,4BAiBG,QAAO,SAAS,kCAAkC,mBAAmB;AAC1E,SAAO,KAAK,qBAAqB,4BAA8B,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,CAAC;AACrI;AACA,cApBI,4BAoBG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,aAAa,IAAI,eAAe,EAAE,CAAC;AACtD,CAAC;AAvBH,IAAM,4BAAN;AAAA,CAyBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAAN,MAAM,yBAAwB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc,IAAI,gBAAgB,KAAK;AAAA,EACvC,gBAAgB,OAAO,WAAW;AAAA,EAClC,sBAAsB,KAAK,cAAc;AAAA,EACzC,2BAA2B,KAAK,cAAc;AAAA,EAC9C,wBAAwB,IAAI,aAAa;AAAA,EACzC,qBAAqB;AAAA,EACrB,iBAAiB,CAAC;AAAA,EAClB;AAAA,EACA,MAAM;AAAA,EACN,WAAW,IAAI,QAAQ;AAAA,EACvB,iBAAiB,OAAO;AACtB,SAAK,sBAAsB,KAAK,KAAK;AAAA,EACvC;AAAA,EACA,cAAc,SAAS;AACrB,SAAK,YAAY,KAAK,OAAO;AAAA,EAC/B;AAAA,EACA,SAAS,KAAK,OAAO;AACnB,SAAK,GAAG,IAAI;AACZ,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,cAAc,OAAO,wBAAwB;AAAA,IAC3C,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,KAAK,YAAY,UAAU,kBAAkB,gBAAgB;AACvE,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,qBAAqB;AACnB,SAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,KAAK,WAAW,aAAa;AAAA,EAClH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAuCF;AAtCE,cAnDI,0BAmDG,QAAO,SAAS,gCAAgC,mBAAmB;AACxE,SAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,GAAM,kBAAuB,cAAc,CAAC;AACrQ;AACA,cAtDI,0BAsDG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,EAChC,WAAW,SAAS,8BAA8B,IAAI,KAAK;AACzD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,aAAa,CAAC;AAAA,IAC/B;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,IACpE;AAAA,EACF;AAAA,EACA,UAAU,CAAC,gBAAgB;AAAA,EAC3B,UAAU,CAAI,mBAAmB;AAAA,IAAC;AAAA;AAAA,IAClC;AAAA,MACE,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,EAAC,CAAC,CAAC;AAAA,EACH,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,cAAc,cAAc,eAAe,CAAC;AAAA,EAC5E,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,aAAa;AAAA,IACtF;AAAA,EACF;AAAA,EACA,cAAc,CAAC,sBAAsB;AAAA,EACrC,eAAe;AAAA,EACf,MAAM;AAAA,IACJ,WAAW,CAAC,WAAW;AAAA,EACzB;AAAA,EACA,iBAAiB;AACnB,CAAC;AAxFH,IAAM,0BAAN;AAAA,CA0FC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY,CAAC,WAAW;AAAA,MACxB,WAAW;AAAA,QAAC;AAAA;AAAA,QACZ;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MAAC;AAAA,MACD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,sBAAsB;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,oBAAN,MAAM,kBAAiB;AAYvB;AAXE,cADI,mBACG,QAAO,SAAS,yBAAyB,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,mBAAkB;AACrD;AACA,cAJI,mBAIG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AAAA,EACN,SAAS,CAAC,qBAAqB,sBAAsB,yBAAyB,2BAA2B,0BAA0B;AAAA,EACnI,SAAS,CAAC,cAAc,qBAAqB,sBAAsB,yBAAyB,yBAAyB;AACvH,CAAC;AACD,cATI,mBASG,QAAyB,iBAAiB;AAAA,EAC/C,SAAS,CAAC,4BAA4B,YAAY;AACpD,CAAC;AAXH,IAAM,mBAAN;AAAA,CAaC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,qBAAqB,sBAAsB,yBAAyB,2BAA2B,0BAA0B;AAAA,MACnI,SAAS,CAAC,cAAc,qBAAqB,sBAAsB,yBAAyB,yBAAyB;AAAA,IACvH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,oBAAoB,CAAC,IAAI,uBAAuB;AAAA,EACpD,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,CAAC;AACF,IAAM,wBAAN,MAAM,sBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,oBAAoB,aAAa;AAAA,EACjC,YAAY,QAAQ,SAAS;AAC3B,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO,QAAQ,yBAAyB;AACtC,SAAK,MAAM,IAAI;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,kBAAkB,YAAY;AAChC,aAAO,eAAe;AAAA,IACxB;AACA,UAAM,mBAAmB,KAAK,QAAQ,SAAS,EAAE,oBAAoB;AAAA,MACnE;AAAA,MACA;AAAA,IACF,CAAC,EAAE,cAAc,iBAAiB,EAAE,sBAAsB,eAAe;AACzE,SAAK,aAAa,KAAK,QAAQ,OAAO;AAAA,MACpC;AAAA,MACA,qBAAqB;AAAA,MACrB,gBAAgB,KAAK,QAAQ,iBAAiB,MAAM;AAAA,IACtD,CAAC;AACD,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,kBAAkB,IAAI,wBAAwB,yBAAyB,UAAU,MAAM,KAAK,MAAM,CAAC,CAAC;AACzG,SAAK,kBAAkB,IAAI,MAAM,wBAAwB,UAAU,OAAO,EAAE;AAAA,MAAK,OAAO,WAAS,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK,WAAW,eAAe,SAAS,MAAM,MAAM,CAAC;AAAA;AAAA,MAC5K,OAAO,WAAS,MAAM,WAAW,CAAC;AAAA,IAAC,GAAG,wBAAwB,UAAU,SAAS,EAAE,KAAK,OAAO,WAAS,MAAM,QAAQ,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,UAAU,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC;AACpM,WAAO,KAAK,WAAW,OAAO,IAAI,eAAe,wBAAwB,aAAa,wBAAwB,gBAAgB,CAAC;AAAA,EACjI;AAAA,EACA,MAAM,QAAQ,OAAO;AACnB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,OAAO;AACvB,UAAI,OAAO;AACT,aAAK,WAAW,QAAQ;AAAA,MAC1B;AACA,WAAK,aAAa;AAClB,WAAK,kBAAkB,YAAY;AAAA,IACrC;AAAA,EACF;AASF;AARE,cA3CI,uBA2CG,QAAO,SAAS,6BAA6B,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,uBAAyB,SAAY,MAAM,GAAM,SAAc,OAAO,CAAC;AAC1G;AACA,cA9CI,uBA8CG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,sBAAqB;AAAA,EAC9B,YAAY;AACd,CAAC;AAlDH,IAAM,uBAAN;AAAA,CAoDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;", "names": []}