import * as path from "path";
import * as fs from "fs";

global["JOYTEST_ON"]=1;
import nodeRequire from "./native-require";

import { MZ<PERSON>, OSExt, AudioAddonInterface } from "@share-types/addon.types";

const isElectron = process.versions["electron"];
const isDEV = !!process.env.JOY_DEV_SERVER_PATH

export function getAddon(name: "osext"): OSExt;
export function getAddon(name: "osext.node"): any; // TODO
export function getAddon(name: "mzip"): MZip;
// export function getAddon(name: "audio"): AudioAddonInterface;
// export function getAddon(name: "winreg"): any;

// 只加载.node插件
export function getAddon(name: string) {
  try {
    if (!name.endsWith(".node")) {
      name += ".node";
    }

    let rp = "";
    if (isDEV) {
      rp = path.resolve(`./addon/${process.arch === "x64" ? "win64" : "win32"}`)
      return nodeRequire(path.join(rp, name));
    }
    if (isElectron) {
      rp = process["resourcesPath"];
      if (fs.existsSync(path.join(rp, name))) {
        return nodeRequire(path.join(rp, name));
      }

      rp = path.dirname(process.execPath);
      return nodeRequire(path.join(rp, name));
    } else {
      rp = path.resolve(`./app/addon/${process.arch === "x64" ? "win64" : "win32"}`);
      return nodeRequire(path.join(rp, name));
    }
  } catch (error: any) {
    if (error.code === "MODULE_NOT_FOUND") {
      throw new Error(`Module ${name} not found`);
    }
    throw error;
  }
}
