import { EventEmitter } from 'events';
import { DataAccess } from '@data/types';
import { IProjectData } from './cloud/cloud.api';
import { RoomService } from './room/room.service';

interface ICenter {
  center_id: string;
  center_name: string;
  center_address: string;
  projects: {
    formal: IProjectData;
    test: IProjectData;
  };
}
export interface ICenterInfo {
  center_id: string;
  center_name: string;
  center_address: string;
  project_id: string;
  project_name: string;
  start: string;
  end: string;
  is_active: 0 | 1;
  test: IProjectData;
}

export interface IEnv {
  getUserConfig: <T>(key: string) => T;
  setUserConfig: <T>(key: string, val: T) => void;
}

export class CenterService extends EventEmitter {
  center: ICenterInfo;

  private env: IEnv;
  private dataAccess: DataAccess;
  private roomService: RoomService;
  constructor(dataAccess: DataAccess, roomService: RoomService, env: IEnv) {
    super();
    this.env = env;
    this.dataAccess = dataAccess;
    this.roomService = roomService;
  }
  getCenter(): Readonly<ICenterInfo> {
    return this.center || null;
  }

  initCenter(): Readonly<ICenterInfo> | null {
    const center_data = this.env.getUserConfig<ICenterInfo>('center');
    if (!center_data) {
      return null;
    }
    console.log(
      'load center data:' +
        JSON.stringify({
          center_name: center_data.center_name,
          project_name: center_data.project_name,
          is_active: center_data.is_active,
        }),
    );
    this.center = center_data;
    return center_data;
  }

  async login(center: ICenter) {
    const { center_id, center_name, center_address, projects } = center;
    this.center = {
      center_id,
      center_name,
      center_address,
      is_active: 1,
      project_id: projects.formal.project_id,
      project_name: projects.formal.name,
      start: projects.formal.start,
      end: projects.formal.end,
      test: projects.test,
    };
    this.env.setUserConfig('center', this.center);
    try {
      await this.dataAccess.loadDb();
      await this.roomService.loadAllRooms();
    } catch (error) {
      console.error('center: load db error: ', error);
      throw error;
    }
    this.emit('center:login', this.center);
  }

  logout() {
    this.center.is_active = 0;
    const center_data = this.env.getUserConfig<ICenterInfo>('center');
    center_data.is_active = 0;
    this.env.setUserConfig('center', center_data);
    this.emit('center:logout');
  }
}
