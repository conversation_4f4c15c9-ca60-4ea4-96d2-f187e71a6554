{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@angular+common@19.2.9_@angular+core@19.2.9_rxjs@7.8.1_zone.js@0.15.0__rxjs@7.8.1/node_modules/@angular/common/fesm2022/module-BHk9jdTn.mjs", "../../../../../../node_modules/.pnpm/@angular+common@19.2.9_@angular+core@19.2.9_rxjs@7.8.1_zone.js@0.15.0__rxjs@7.8.1/node_modules/@angular/common/fesm2022/http.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, Injectable, inject, NgZone, InjectionToken, ɵPendingTasksInternal as _PendingTasksInternal, PLATFORM_ID, ɵConsole as _Console, ɵformatRuntimeError as _formatRuntimeError, runInInjectionContext, Inject, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { concatMap, filter, map, finalize, switchMap } from 'rxjs/operators';\nimport { of, Observable, from } from 'rxjs';\nimport { isPlatformServer, XhrFactory, parseCookieValue } from './xhr-BfNfxNDv.mjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n  /**\n   * Internal map of lowercase header names to values.\n   */\n  headers;\n  /**\n   * Internal map of lowercased header names to the normalized\n   * form of the name (the form seen first).\n   */\n  normalizedNames = new Map();\n  /**\n   * Complete the lazy initialization of this object (needed before reading).\n   */\n  lazyInit;\n  /**\n   * Queued updates to be materialized the next initialization.\n   */\n  lazyUpdate = null;\n  /**  Constructs a new HTTP header object with the given values.*/\n  constructor(headers) {\n    if (!headers) {\n      this.headers = new Map();\n    } else if (typeof headers === 'string') {\n      this.lazyInit = () => {\n        this.headers = new Map();\n        headers.split('\\n').forEach(line => {\n          const index = line.indexOf(':');\n          if (index > 0) {\n            const name = line.slice(0, index);\n            const value = line.slice(index + 1).trim();\n            this.addHeaderEntry(name, value);\n          }\n        });\n      };\n    } else if (typeof Headers !== 'undefined' && headers instanceof Headers) {\n      this.headers = new Map();\n      headers.forEach((value, name) => {\n        this.addHeaderEntry(name, value);\n      });\n    } else {\n      this.lazyInit = () => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          assertValidHeaders(headers);\n        }\n        this.headers = new Map();\n        Object.entries(headers).forEach(([name, values]) => {\n          this.setHeaderEntries(name, values);\n        });\n      };\n    }\n  }\n  /**\n   * Checks for existence of a given header.\n   *\n   * @param name The header name to check for existence.\n   *\n   * @returns True if the header exists, false otherwise.\n   */\n  has(name) {\n    this.init();\n    return this.headers.has(name.toLowerCase());\n  }\n  /**\n   * Retrieves the first value of a given header.\n   *\n   * @param name The header name.\n   *\n   * @returns The value string if the header exists, null otherwise\n   */\n  get(name) {\n    this.init();\n    const values = this.headers.get(name.toLowerCase());\n    return values && values.length > 0 ? values[0] : null;\n  }\n  /**\n   * Retrieves the names of the headers.\n   *\n   * @returns A list of header names.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.normalizedNames.values());\n  }\n  /**\n   * Retrieves a list of values for a given header.\n   *\n   * @param name The header name from which to retrieve values.\n   *\n   * @returns A string of values if the header exists, null otherwise.\n   */\n  getAll(name) {\n    this.init();\n    return this.headers.get(name.toLowerCase()) || null;\n  }\n  /**\n   * Appends a new value to the existing set of values for a header\n   * and returns them in a clone of the original instance.\n   *\n   * @param name The header name for which to append the values.\n   * @param value The value to append.\n   *\n   * @returns A clone of the HTTP headers object with the value appended to the given header.\n   */\n  append(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Sets or modifies a value for a given header in a clone of the original instance.\n   * If the header already exists, its value is replaced with the given value\n   * in the returned object.\n   *\n   * @param name The header name.\n   * @param value The value or values to set or override for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the newly set header value.\n   */\n  set(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Deletes values for a given header in a clone of the original instance.\n   *\n   * @param name The header name.\n   * @param value The value or values to delete for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the given value deleted.\n   */\n  delete(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'd'\n    });\n  }\n  maybeSetNormalizedName(name, lcName) {\n    if (!this.normalizedNames.has(lcName)) {\n      this.normalizedNames.set(lcName, name);\n    }\n  }\n  init() {\n    if (!!this.lazyInit) {\n      if (this.lazyInit instanceof HttpHeaders) {\n        this.copyFrom(this.lazyInit);\n      } else {\n        this.lazyInit();\n      }\n      this.lazyInit = null;\n      if (!!this.lazyUpdate) {\n        this.lazyUpdate.forEach(update => this.applyUpdate(update));\n        this.lazyUpdate = null;\n      }\n    }\n  }\n  copyFrom(other) {\n    other.init();\n    Array.from(other.headers.keys()).forEach(key => {\n      this.headers.set(key, other.headers.get(key));\n      this.normalizedNames.set(key, other.normalizedNames.get(key));\n    });\n  }\n  clone(update) {\n    const clone = new HttpHeaders();\n    clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n    clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n    return clone;\n  }\n  applyUpdate(update) {\n    const key = update.name.toLowerCase();\n    switch (update.op) {\n      case 'a':\n      case 's':\n        let value = update.value;\n        if (typeof value === 'string') {\n          value = [value];\n        }\n        if (value.length === 0) {\n          return;\n        }\n        this.maybeSetNormalizedName(update.name, key);\n        const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n        base.push(...value);\n        this.headers.set(key, base);\n        break;\n      case 'd':\n        const toDelete = update.value;\n        if (!toDelete) {\n          this.headers.delete(key);\n          this.normalizedNames.delete(key);\n        } else {\n          let existing = this.headers.get(key);\n          if (!existing) {\n            return;\n          }\n          existing = existing.filter(value => toDelete.indexOf(value) === -1);\n          if (existing.length === 0) {\n            this.headers.delete(key);\n            this.normalizedNames.delete(key);\n          } else {\n            this.headers.set(key, existing);\n          }\n        }\n        break;\n    }\n  }\n  addHeaderEntry(name, value) {\n    const key = name.toLowerCase();\n    this.maybeSetNormalizedName(name, key);\n    if (this.headers.has(key)) {\n      this.headers.get(key).push(value);\n    } else {\n      this.headers.set(key, [value]);\n    }\n  }\n  setHeaderEntries(name, values) {\n    const headerValues = (Array.isArray(values) ? values : [values]).map(value => value.toString());\n    const key = name.toLowerCase();\n    this.headers.set(key, headerValues);\n    this.maybeSetNormalizedName(name, key);\n  }\n  /**\n   * @internal\n   */\n  forEach(fn) {\n    this.init();\n    Array.from(this.normalizedNames.keys()).forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n  }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n  for (const [key, value] of Object.entries(headers)) {\n    if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n      throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` + `Expecting either a string, a number or an array, but got: \\`${value}\\`.`);\n    }\n  }\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n  /**\n   * Encodes a key name for a URL parameter or query-string.\n   * @param key The key name.\n   * @returns The encoded key name.\n   */\n  encodeKey(key) {\n    return standardEncoding(key);\n  }\n  /**\n   * Encodes the value of a URL parameter or query-string.\n   * @param value The value.\n   * @returns The encoded value.\n   */\n  encodeValue(value) {\n    return standardEncoding(value);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string key.\n   * @param key The encoded key name.\n   * @returns The decoded key name.\n   */\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string value.\n   * @param value The encoded value.\n   * @returns The decoded value.\n   */\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n}\nfunction paramParser(rawParams, codec) {\n  const map = new Map();\n  if (rawParams.length > 0) {\n    // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n    // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n    // may start with the `?` char, so we strip it if it's present.\n    const params = rawParams.replace(/^\\?/, '').split('&');\n    params.forEach(param => {\n      const eqIdx = param.indexOf('=');\n      const [key, val] = eqIdx == -1 ? [codec.decodeKey(param), ''] : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n      const list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n  return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n  '40': '@',\n  '3A': ':',\n  '24': '$',\n  '2C': ',',\n  '3B': ';',\n  '3D': '=',\n  '3F': '?',\n  '2F': '/'\n};\nfunction standardEncoding(v) {\n  return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n  return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n  map;\n  encoder;\n  updates = null;\n  cloneFrom = null;\n  constructor(options = {}) {\n    this.encoder = options.encoder || new HttpUrlEncodingCodec();\n    if (options.fromString) {\n      if (options.fromObject) {\n        throw new _RuntimeError(2805 /* RuntimeErrorCode.CANNOT_SPECIFY_BOTH_FROM_STRING_AND_FROM_OBJECT */, ngDevMode && 'Cannot specify both fromString and fromObject.');\n      }\n      this.map = paramParser(options.fromString, this.encoder);\n    } else if (!!options.fromObject) {\n      this.map = new Map();\n      Object.keys(options.fromObject).forEach(key => {\n        const value = options.fromObject[key];\n        // convert the values to strings\n        const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n        this.map.set(key, values);\n      });\n    } else {\n      this.map = null;\n    }\n  }\n  /**\n   * Reports whether the body includes one or more values for a given parameter.\n   * @param param The parameter name.\n   * @returns True if the parameter has one or more values,\n   * false if it has no value or is not present.\n   */\n  has(param) {\n    this.init();\n    return this.map.has(param);\n  }\n  /**\n   * Retrieves the first value for a parameter.\n   * @param param The parameter name.\n   * @returns The first value of the given parameter,\n   * or `null` if the parameter is not present.\n   */\n  get(param) {\n    this.init();\n    const res = this.map.get(param);\n    return !!res ? res[0] : null;\n  }\n  /**\n   * Retrieves all values for a  parameter.\n   * @param param The parameter name.\n   * @returns All values in a string array,\n   * or `null` if the parameter not present.\n   */\n  getAll(param) {\n    this.init();\n    return this.map.get(param) || null;\n  }\n  /**\n   * Retrieves all the parameters for this body.\n   * @returns The parameter names in a string array.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.map.keys());\n  }\n  /**\n   * Appends a new value to existing values for a parameter.\n   * @param param The parameter name.\n   * @param value The new value to add.\n   * @return A new body with the appended value.\n   */\n  append(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Constructs a new body with appended values for the given parameter name.\n   * @param params parameters and values\n   * @return A new body with the new value.\n   */\n  appendAll(params) {\n    const updates = [];\n    Object.keys(params).forEach(param => {\n      const value = params[param];\n      if (Array.isArray(value)) {\n        value.forEach(_value => {\n          updates.push({\n            param,\n            value: _value,\n            op: 'a'\n          });\n        });\n      } else {\n        updates.push({\n          param,\n          value: value,\n          op: 'a'\n        });\n      }\n    });\n    return this.clone(updates);\n  }\n  /**\n   * Replaces the value for a parameter.\n   * @param param The parameter name.\n   * @param value The new value.\n   * @return A new body with the new value.\n   */\n  set(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Removes a given value or all values from a parameter.\n   * @param param The parameter name.\n   * @param value The value to remove, if provided.\n   * @return A new body with the given value removed, or with all values\n   * removed if no value is specified.\n   */\n  delete(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'd'\n    });\n  }\n  /**\n   * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n   * separated by `&`s.\n   */\n  toString() {\n    this.init();\n    return this.keys().map(key => {\n      const eKey = this.encoder.encodeKey(key);\n      // `a: ['1']` produces `'a=1'`\n      // `b: []` produces `''`\n      // `c: ['1', '2']` produces `'c=1&c=2'`\n      return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value)).join('&');\n    })\n    // filter out empty values because `b: []` produces `''`\n    // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n    .filter(param => param !== '').join('&');\n  }\n  clone(update) {\n    const clone = new HttpParams({\n      encoder: this.encoder\n    });\n    clone.cloneFrom = this.cloneFrom || this;\n    clone.updates = (this.updates || []).concat(update);\n    return clone;\n  }\n  init() {\n    if (this.map === null) {\n      this.map = new Map();\n    }\n    if (this.cloneFrom !== null) {\n      this.cloneFrom.init();\n      this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n      this.updates.forEach(update => {\n        switch (update.op) {\n          case 'a':\n          case 's':\n            const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n            base.push(valueToString(update.value));\n            this.map.set(update.param, base);\n            break;\n          case 'd':\n            if (update.value !== undefined) {\n              let base = this.map.get(update.param) || [];\n              const idx = base.indexOf(valueToString(update.value));\n              if (idx !== -1) {\n                base.splice(idx, 1);\n              }\n              if (base.length > 0) {\n                this.map.set(update.param, base);\n              } else {\n                this.map.delete(update.param);\n              }\n            } else {\n              this.map.delete(update.param);\n              break;\n            }\n        }\n      });\n      this.cloneFrom = this.updates = null;\n    }\n  }\n}\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n  defaultValue;\n  constructor(defaultValue) {\n    this.defaultValue = defaultValue;\n  }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```ts\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n  map = new Map();\n  /**\n   * Store a value in the context. If a value is already present it will be overwritten.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   * @param value The value to store.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  set(token, value) {\n    this.map.set(token, value);\n    return this;\n  }\n  /**\n   * Retrieve the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns The stored value or default if one is defined.\n   */\n  get(token) {\n    if (!this.map.has(token)) {\n      this.map.set(token, token.defaultValue());\n    }\n    return this.map.get(token);\n  }\n  /**\n   * Delete the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  delete(token) {\n    this.map.delete(token);\n    return this;\n  }\n  /**\n   * Checks for existence of a given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns True if the token exists, false otherwise.\n   */\n  has(token) {\n    return this.map.has(token);\n  }\n  /**\n   * @returns a list of tokens currently stored in the context.\n   */\n  keys() {\n    return this.map.keys();\n  }\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n  switch (method) {\n    case 'DELETE':\n    case 'GET':\n    case 'HEAD':\n    case 'OPTIONS':\n    case 'JSONP':\n      return false;\n    default:\n      return true;\n  }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n  return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n  return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n  return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n  return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * `Content-Type` is an HTTP header used to indicate the media type\n * (also known as MIME type) of the resource being sent to the client\n * or received from the server.\n */\nconst CONTENT_TYPE_HEADER = 'Content-Type';\n/**\n * The `Accept` header is an HTTP request header that indicates the media types\n * (or content types) the client is willing to receive from the server.\n */\nconst ACCEPT_HEADER = 'Accept';\n/**\n * `X-Request-URL` is a custom HTTP header used in older browser versions,\n * including Firefox (< 32), Chrome (< 37), Safari (< 8), and Internet Explorer,\n * to include the full URL of the request in cross-origin requests.\n */\nconst X_REQUEST_URL_HEADER = 'X-Request-URL';\n/**\n * `text/plain` is a content type used to indicate that the content being\n * sent is plain text with no special formatting or structured data\n * like HTML, XML, or JSON.\n */\nconst TEXT_CONTENT_TYPE = 'text/plain';\n/**\n * `application/json` is a content type used to indicate that the content\n * being sent is in the JSON format.\n */\nconst JSON_CONTENT_TYPE = 'application/json';\n/**\n * `application/json, text/plain, *\\/*` is a content negotiation string often seen in the\n * Accept header of HTTP requests. It indicates the types of content the client is willing\n * to accept from the server, with a preference for `application/json` and `text/plain`,\n * but also accepting any other type (*\\/*).\n */\nconst ACCEPT_HEADER_VALUE = `${JSON_CONTENT_TYPE}, ${TEXT_CONTENT_TYPE}, */*`;\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n  url;\n  /**\n   * The request body, or `null` if one isn't set.\n   *\n   * Bodies are not enforced to be immutable, as they can include a reference to any\n   * user-defined data type. However, interceptors should take care to preserve\n   * idempotence by treating them as such.\n   */\n  body = null;\n  /**\n   * Outgoing headers for this request.\n   */\n  headers;\n  /**\n   * Shared and mutable context that can be used by interceptors\n   */\n  context;\n  /**\n   * Whether this request should be made in a way that exposes progress events.\n   *\n   * Progress events are expensive (change detection runs on each event) and so\n   * they should only be requested if the consumer intends to monitor them.\n   *\n   * Note: The `FetchBackend` doesn't support progress report on uploads.\n   */\n  reportProgress = false;\n  /**\n   * Whether this request should be sent with outgoing credentials (cookies).\n   */\n  withCredentials = false;\n  /**\n   * The expected response type of the server.\n   *\n   * This is used to parse the response appropriately before returning it to\n   * the requestee.\n   */\n  responseType = 'json';\n  /**\n   * The outgoing HTTP request method.\n   */\n  method;\n  /**\n   * Outgoing URL parameters.\n   *\n   * To pass a string representation of HTTP parameters in the URL-query-string format,\n   * the `HttpParamsOptions`' `fromString` may be used. For example:\n   *\n   * ```ts\n   * new HttpParams({fromString: 'angular=awesome'})\n   * ```\n   */\n  params;\n  /**\n   * The outgoing URL with all URL parameters set.\n   */\n  urlWithParams;\n  /**\n   * The HttpTransferCache option for the request\n   */\n  transferCache;\n  constructor(method, url, third, fourth) {\n    this.url = url;\n    this.method = method.toUpperCase();\n    // Next, need to figure out which argument holds the HttpRequestInit\n    // options, if any.\n    let options;\n    // Check whether a body argument is expected. The only valid way to omit\n    // the body argument is to use a known no-body method like GET.\n    if (mightHaveBody(this.method) || !!fourth) {\n      // Body is the third argument, options are the fourth.\n      this.body = third !== undefined ? third : null;\n      options = fourth;\n    } else {\n      // No body required, options are the third argument. The body stays null.\n      options = third;\n    }\n    // If options have been passed, interpret them.\n    if (options) {\n      // Normalize reportProgress and withCredentials.\n      this.reportProgress = !!options.reportProgress;\n      this.withCredentials = !!options.withCredentials;\n      // Override default response type of 'json' if one is provided.\n      if (!!options.responseType) {\n        this.responseType = options.responseType;\n      }\n      // Override headers if they're provided.\n      if (!!options.headers) {\n        this.headers = options.headers;\n      }\n      if (!!options.context) {\n        this.context = options.context;\n      }\n      if (!!options.params) {\n        this.params = options.params;\n      }\n      // We do want to assign transferCache even if it's falsy (false is valid value)\n      this.transferCache = options.transferCache;\n    }\n    // If no headers have been passed in, construct a new HttpHeaders instance.\n    this.headers ??= new HttpHeaders();\n    // If no context have been passed in, construct a new HttpContext instance.\n    this.context ??= new HttpContext();\n    // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n    if (!this.params) {\n      this.params = new HttpParams();\n      this.urlWithParams = url;\n    } else {\n      // Encode the parameters to a string in preparation for inclusion in the URL.\n      const params = this.params.toString();\n      if (params.length === 0) {\n        // No parameters, the visible URL is just the URL given at creation time.\n        this.urlWithParams = url;\n      } else {\n        // Does the URL already have query parameters? Look for '?'.\n        const qIdx = url.indexOf('?');\n        // There are 3 cases to handle:\n        // 1) No existing parameters -> append '?' followed by params.\n        // 2) '?' exists and is followed by existing query string ->\n        //    append '&' followed by params.\n        // 3) '?' exists at the end of the url -> append params directly.\n        // This basically amounts to determining the character, if any, with\n        // which to join the URL and parameters.\n        const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n        this.urlWithParams = url + sep + params;\n      }\n    }\n  }\n  /**\n   * Transform the free-form body into a serialized format suitable for\n   * transmission to the server.\n   */\n  serializeBody() {\n    // If no body is present, no need to serialize it.\n    if (this.body === null) {\n      return null;\n    }\n    // Check whether the body is already in a serialized form. If so,\n    // it can just be returned directly.\n    if (typeof this.body === 'string' || isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) || isUrlSearchParams(this.body)) {\n      return this.body;\n    }\n    // Check whether the body is an instance of HttpUrlEncodedParams.\n    if (this.body instanceof HttpParams) {\n      return this.body.toString();\n    }\n    // Check whether the body is an object or array, and serialize with JSON if so.\n    if (typeof this.body === 'object' || typeof this.body === 'boolean' || Array.isArray(this.body)) {\n      return JSON.stringify(this.body);\n    }\n    // Fall back on toString() for everything else.\n    return this.body.toString();\n  }\n  /**\n   * Examine the body and attempt to infer an appropriate MIME type\n   * for it.\n   *\n   * If no such type can be inferred, this method will return `null`.\n   */\n  detectContentTypeHeader() {\n    // An empty body has no content type.\n    if (this.body === null) {\n      return null;\n    }\n    // FormData bodies rely on the browser's content type assignment.\n    if (isFormData(this.body)) {\n      return null;\n    }\n    // Blobs usually have their own content type. If it doesn't, then\n    // no type can be inferred.\n    if (isBlob(this.body)) {\n      return this.body.type || null;\n    }\n    // Array buffers have unknown contents and thus no type can be inferred.\n    if (isArrayBuffer(this.body)) {\n      return null;\n    }\n    // Technically, strings could be a form of JSON data, but it's safe enough\n    // to assume they're plain strings.\n    if (typeof this.body === 'string') {\n      return TEXT_CONTENT_TYPE;\n    }\n    // `HttpUrlEncodedParams` has its own content-type.\n    if (this.body instanceof HttpParams) {\n      return 'application/x-www-form-urlencoded;charset=UTF-8';\n    }\n    // Arrays, objects, boolean and numbers will be encoded as JSON.\n    if (typeof this.body === 'object' || typeof this.body === 'number' || typeof this.body === 'boolean') {\n      return JSON_CONTENT_TYPE;\n    }\n    // No type could be inferred.\n    return null;\n  }\n  clone(update = {}) {\n    // For method, url, and responseType, take the current value unless\n    // it is overridden in the update hash.\n    const method = update.method || this.method;\n    const url = update.url || this.url;\n    const responseType = update.responseType || this.responseType;\n    // Carefully handle the transferCache to differentiate between\n    // `false` and `undefined` in the update args.\n    const transferCache = update.transferCache ?? this.transferCache;\n    // The body is somewhat special - a `null` value in update.body means\n    // whatever current body is present is being overridden with an empty\n    // body, whereas an `undefined` value in update.body implies no\n    // override.\n    const body = update.body !== undefined ? update.body : this.body;\n    // Carefully handle the boolean options to differentiate between\n    // `false` and `undefined` in the update args.\n    const withCredentials = update.withCredentials ?? this.withCredentials;\n    const reportProgress = update.reportProgress ?? this.reportProgress;\n    // Headers and params may be appended to if `setHeaders` or\n    // `setParams` are used.\n    let headers = update.headers || this.headers;\n    let params = update.params || this.params;\n    // Pass on context if needed\n    const context = update.context ?? this.context;\n    // Check whether the caller has asked to add headers.\n    if (update.setHeaders !== undefined) {\n      // Set every requested header.\n      headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n    }\n    // Check whether the caller has asked to set params.\n    if (update.setParams) {\n      // Set every requested param.\n      params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n    }\n    // Finally, construct the new HttpRequest using the pieces from above.\n    return new HttpRequest(method, url, body, {\n      params,\n      headers,\n      context,\n      reportProgress,\n      responseType,\n      withCredentials,\n      transferCache\n    });\n  }\n}\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n  /**\n   * The request was sent out over the wire.\n   */\n  HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n  /**\n   * An upload progress event was received.\n   *\n   * Note: The `FetchBackend` doesn't support progress report on uploads.\n   */\n  HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n  /**\n   * The response status code and headers were received.\n   */\n  HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n  /**\n   * A download progress event was received.\n   */\n  HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n  /**\n   * The full response including the body was received.\n   */\n  HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n  /**\n   * A custom event from an interceptor or a backend.\n   */\n  HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n  /**\n   * All response headers.\n   */\n  headers;\n  /**\n   * Response status code.\n   */\n  status;\n  /**\n   * Textual description of response status code, defaults to OK.\n   *\n   * Do not depend on this.\n   */\n  statusText;\n  /**\n   * URL of the resource retrieved, or null if not available.\n   */\n  url;\n  /**\n   * Whether the status code falls in the 2xx range.\n   */\n  ok;\n  /**\n   * Type of the response, narrowed to either the full response or the header.\n   */\n  type;\n  /**\n   * Super-constructor for all responses.\n   *\n   * The single parameter accepted is an initialization hash. Any properties\n   * of the response passed there will override the default values.\n   */\n  constructor(init, defaultStatus = 200, defaultStatusText = 'OK') {\n    // If the hash has values passed, use them to initialize the response.\n    // Otherwise use the default values.\n    this.headers = init.headers || new HttpHeaders();\n    this.status = init.status !== undefined ? init.status : defaultStatus;\n    this.statusText = init.statusText || defaultStatusText;\n    this.url = init.url || null;\n    // Cache the ok value to avoid defining a getter.\n    this.ok = this.status >= 200 && this.status < 300;\n  }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n  /**\n   * Create a new `HttpHeaderResponse` with the given parameters.\n   */\n  constructor(init = {}) {\n    super(init);\n  }\n  type = HttpEventType.ResponseHeader;\n  /**\n   * Copy this `HttpHeaderResponse`, overriding its contents with the\n   * given parameter hash.\n   */\n  clone(update = {}) {\n    // Perform a straightforward initialization of the new HttpHeaderResponse,\n    // overriding the current parameters with new ones if given.\n    return new HttpHeaderResponse({\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n  /**\n   * The response body, or `null` if one was not returned.\n   */\n  body;\n  /**\n   * Construct a new `HttpResponse`.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.body = init.body !== undefined ? init.body : null;\n  }\n  type = HttpEventType.Response;\n  clone(update = {}) {\n    return new HttpResponse({\n      body: update.body !== undefined ? update.body : this.body,\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n  name = 'HttpErrorResponse';\n  message;\n  error;\n  /**\n   * Errors are never okay, even when the status code is in the 2xx success range.\n   */\n  ok = false;\n  constructor(init) {\n    // Initialize with a default status of 0 / Unknown Error.\n    super(init, 0, 'Unknown Error');\n    // If the response was successful, then this was a parse error. Otherwise, it was\n    // a protocol-level failure of some sort. Either the request failed in transit\n    // or the server returned an unsuccessful status code.\n    if (this.status >= 200 && this.status < 300) {\n      this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n    } else {\n      this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n    }\n    this.error = init.error || null;\n  }\n}\n/**\n * We use these constant to prevent pulling the whole HttpStatusCode enum\n * Those are the only ones referenced directly by the framework\n */\nconst HTTP_STATUS_CODE_OK = 200;\nconst HTTP_STATUS_CODE_NO_CONTENT = 204;\n/**\n * Http status codes.\n * As per https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml\n * @publicApi\n */\nvar HttpStatusCode;\n(function (HttpStatusCode) {\n  HttpStatusCode[HttpStatusCode[\"Continue\"] = 100] = \"Continue\";\n  HttpStatusCode[HttpStatusCode[\"SwitchingProtocols\"] = 101] = \"SwitchingProtocols\";\n  HttpStatusCode[HttpStatusCode[\"Processing\"] = 102] = \"Processing\";\n  HttpStatusCode[HttpStatusCode[\"EarlyHints\"] = 103] = \"EarlyHints\";\n  HttpStatusCode[HttpStatusCode[\"Ok\"] = 200] = \"Ok\";\n  HttpStatusCode[HttpStatusCode[\"Created\"] = 201] = \"Created\";\n  HttpStatusCode[HttpStatusCode[\"Accepted\"] = 202] = \"Accepted\";\n  HttpStatusCode[HttpStatusCode[\"NonAuthoritativeInformation\"] = 203] = \"NonAuthoritativeInformation\";\n  HttpStatusCode[HttpStatusCode[\"NoContent\"] = 204] = \"NoContent\";\n  HttpStatusCode[HttpStatusCode[\"ResetContent\"] = 205] = \"ResetContent\";\n  HttpStatusCode[HttpStatusCode[\"PartialContent\"] = 206] = \"PartialContent\";\n  HttpStatusCode[HttpStatusCode[\"MultiStatus\"] = 207] = \"MultiStatus\";\n  HttpStatusCode[HttpStatusCode[\"AlreadyReported\"] = 208] = \"AlreadyReported\";\n  HttpStatusCode[HttpStatusCode[\"ImUsed\"] = 226] = \"ImUsed\";\n  HttpStatusCode[HttpStatusCode[\"MultipleChoices\"] = 300] = \"MultipleChoices\";\n  HttpStatusCode[HttpStatusCode[\"MovedPermanently\"] = 301] = \"MovedPermanently\";\n  HttpStatusCode[HttpStatusCode[\"Found\"] = 302] = \"Found\";\n  HttpStatusCode[HttpStatusCode[\"SeeOther\"] = 303] = \"SeeOther\";\n  HttpStatusCode[HttpStatusCode[\"NotModified\"] = 304] = \"NotModified\";\n  HttpStatusCode[HttpStatusCode[\"UseProxy\"] = 305] = \"UseProxy\";\n  HttpStatusCode[HttpStatusCode[\"Unused\"] = 306] = \"Unused\";\n  HttpStatusCode[HttpStatusCode[\"TemporaryRedirect\"] = 307] = \"TemporaryRedirect\";\n  HttpStatusCode[HttpStatusCode[\"PermanentRedirect\"] = 308] = \"PermanentRedirect\";\n  HttpStatusCode[HttpStatusCode[\"BadRequest\"] = 400] = \"BadRequest\";\n  HttpStatusCode[HttpStatusCode[\"Unauthorized\"] = 401] = \"Unauthorized\";\n  HttpStatusCode[HttpStatusCode[\"PaymentRequired\"] = 402] = \"PaymentRequired\";\n  HttpStatusCode[HttpStatusCode[\"Forbidden\"] = 403] = \"Forbidden\";\n  HttpStatusCode[HttpStatusCode[\"NotFound\"] = 404] = \"NotFound\";\n  HttpStatusCode[HttpStatusCode[\"MethodNotAllowed\"] = 405] = \"MethodNotAllowed\";\n  HttpStatusCode[HttpStatusCode[\"NotAcceptable\"] = 406] = \"NotAcceptable\";\n  HttpStatusCode[HttpStatusCode[\"ProxyAuthenticationRequired\"] = 407] = \"ProxyAuthenticationRequired\";\n  HttpStatusCode[HttpStatusCode[\"RequestTimeout\"] = 408] = \"RequestTimeout\";\n  HttpStatusCode[HttpStatusCode[\"Conflict\"] = 409] = \"Conflict\";\n  HttpStatusCode[HttpStatusCode[\"Gone\"] = 410] = \"Gone\";\n  HttpStatusCode[HttpStatusCode[\"LengthRequired\"] = 411] = \"LengthRequired\";\n  HttpStatusCode[HttpStatusCode[\"PreconditionFailed\"] = 412] = \"PreconditionFailed\";\n  HttpStatusCode[HttpStatusCode[\"PayloadTooLarge\"] = 413] = \"PayloadTooLarge\";\n  HttpStatusCode[HttpStatusCode[\"UriTooLong\"] = 414] = \"UriTooLong\";\n  HttpStatusCode[HttpStatusCode[\"UnsupportedMediaType\"] = 415] = \"UnsupportedMediaType\";\n  HttpStatusCode[HttpStatusCode[\"RangeNotSatisfiable\"] = 416] = \"RangeNotSatisfiable\";\n  HttpStatusCode[HttpStatusCode[\"ExpectationFailed\"] = 417] = \"ExpectationFailed\";\n  HttpStatusCode[HttpStatusCode[\"ImATeapot\"] = 418] = \"ImATeapot\";\n  HttpStatusCode[HttpStatusCode[\"MisdirectedRequest\"] = 421] = \"MisdirectedRequest\";\n  HttpStatusCode[HttpStatusCode[\"UnprocessableEntity\"] = 422] = \"UnprocessableEntity\";\n  HttpStatusCode[HttpStatusCode[\"Locked\"] = 423] = \"Locked\";\n  HttpStatusCode[HttpStatusCode[\"FailedDependency\"] = 424] = \"FailedDependency\";\n  HttpStatusCode[HttpStatusCode[\"TooEarly\"] = 425] = \"TooEarly\";\n  HttpStatusCode[HttpStatusCode[\"UpgradeRequired\"] = 426] = \"UpgradeRequired\";\n  HttpStatusCode[HttpStatusCode[\"PreconditionRequired\"] = 428] = \"PreconditionRequired\";\n  HttpStatusCode[HttpStatusCode[\"TooManyRequests\"] = 429] = \"TooManyRequests\";\n  HttpStatusCode[HttpStatusCode[\"RequestHeaderFieldsTooLarge\"] = 431] = \"RequestHeaderFieldsTooLarge\";\n  HttpStatusCode[HttpStatusCode[\"UnavailableForLegalReasons\"] = 451] = \"UnavailableForLegalReasons\";\n  HttpStatusCode[HttpStatusCode[\"InternalServerError\"] = 500] = \"InternalServerError\";\n  HttpStatusCode[HttpStatusCode[\"NotImplemented\"] = 501] = \"NotImplemented\";\n  HttpStatusCode[HttpStatusCode[\"BadGateway\"] = 502] = \"BadGateway\";\n  HttpStatusCode[HttpStatusCode[\"ServiceUnavailable\"] = 503] = \"ServiceUnavailable\";\n  HttpStatusCode[HttpStatusCode[\"GatewayTimeout\"] = 504] = \"GatewayTimeout\";\n  HttpStatusCode[HttpStatusCode[\"HttpVersionNotSupported\"] = 505] = \"HttpVersionNotSupported\";\n  HttpStatusCode[HttpStatusCode[\"VariantAlsoNegotiates\"] = 506] = \"VariantAlsoNegotiates\";\n  HttpStatusCode[HttpStatusCode[\"InsufficientStorage\"] = 507] = \"InsufficientStorage\";\n  HttpStatusCode[HttpStatusCode[\"LoopDetected\"] = 508] = \"LoopDetected\";\n  HttpStatusCode[HttpStatusCode[\"NotExtended\"] = 510] = \"NotExtended\";\n  HttpStatusCode[HttpStatusCode[\"NetworkAuthenticationRequired\"] = 511] = \"NetworkAuthenticationRequired\";\n})(HttpStatusCode || (HttpStatusCode = {}));\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n  return {\n    body,\n    headers: options.headers,\n    context: options.context,\n    observe: options.observe,\n    params: options.params,\n    reportProgress: options.reportProgress,\n    responseType: options.responseType,\n    withCredentials: options.withCredentials,\n    transferCache: options.transferCache\n  };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n * @usageNotes\n *\n * ### HTTP Request Example\n *\n * ```ts\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```ts\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```ts\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```ts\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n  handler;\n  constructor(handler) {\n    this.handler = handler;\n  }\n  /**\n   * Constructs an observable for a generic HTTP request that, when subscribed,\n   * fires the request through the chain of registered interceptors and on to the\n   * server.\n   *\n   * You can pass an `HttpRequest` directly as the only parameter. In this case,\n   * the call returns an observable of the raw `HttpEvent` stream.\n   *\n   * Alternatively you can pass an HTTP method as the first parameter,\n   * a URL string as the second, and an options hash containing the request body as the third.\n   * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n   * type of returned observable.\n   *   * The `responseType` value determines how a successful response body is parsed.\n   *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n   * object as a type parameter to the call.\n   *\n   * The `observe` value determines the return type, according to what you are interested in\n   * observing.\n   *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n   * progress events by default.\n   *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n   * where the `T` parameter depends on the `responseType` and any optionally provided type\n   * parameter.\n   *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n   *\n   */\n  request(first, url, options = {}) {\n    let req;\n    // First, check whether the primary argument is an instance of `HttpRequest`.\n    if (first instanceof HttpRequest) {\n      // It is. The other arguments must be undefined (per the signatures) and can be\n      // ignored.\n      req = first;\n    } else {\n      // It's a string, so it represents a URL. Construct a request based on it,\n      // and incorporate the remaining arguments (assuming `GET` unless a method is\n      // provided.\n      // Figure out the headers.\n      let headers = undefined;\n      if (options.headers instanceof HttpHeaders) {\n        headers = options.headers;\n      } else {\n        headers = new HttpHeaders(options.headers);\n      }\n      // Sort out parameters.\n      let params = undefined;\n      if (!!options.params) {\n        if (options.params instanceof HttpParams) {\n          params = options.params;\n        } else {\n          params = new HttpParams({\n            fromObject: options.params\n          });\n        }\n      }\n      // Construct the request.\n      req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n        headers,\n        context: options.context,\n        params,\n        reportProgress: options.reportProgress,\n        // By default, JSON is assumed to be returned for all calls.\n        responseType: options.responseType || 'json',\n        withCredentials: options.withCredentials,\n        transferCache: options.transferCache\n      });\n    }\n    // Start with an Observable.of() the initial request, and run the handler (which\n    // includes all interceptors) inside a concatMap(). This way, the handler runs\n    // inside an Observable chain, which causes interceptors to be re-run on every\n    // subscription (this also makes retries re-run the handler, including interceptors).\n    const events$ = of(req).pipe(concatMap(req => this.handler.handle(req)));\n    // If coming via the API signature which accepts a previously constructed HttpRequest,\n    // the only option is to get the event stream. Otherwise, return the event stream if\n    // that is what was requested.\n    if (first instanceof HttpRequest || options.observe === 'events') {\n      return events$;\n    }\n    // The requested stream contains either the full response or the body. In either\n    // case, the first step is to filter the event stream to extract a stream of\n    // responses(s).\n    const res$ = events$.pipe(filter(event => event instanceof HttpResponse));\n    // Decide which stream to return.\n    switch (options.observe || 'body') {\n      case 'body':\n        // The requested stream is the body. Map the response stream to the response\n        // body. This could be done more simply, but a misbehaving interceptor might\n        // transform the response body into a different format and ignore the requested\n        // responseType. Guard against this by validating that the response is of the\n        // requested type.\n        switch (req.responseType) {\n          case 'arraybuffer':\n            return res$.pipe(map(res => {\n              // Validate that the body is an ArrayBuffer.\n              if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                throw new _RuntimeError(2806 /* RuntimeErrorCode.RESPONSE_IS_NOT_AN_ARRAY_BUFFER */, ngDevMode && 'Response is not an ArrayBuffer.');\n              }\n              return res.body;\n            }));\n          case 'blob':\n            return res$.pipe(map(res => {\n              // Validate that the body is a Blob.\n              if (res.body !== null && !(res.body instanceof Blob)) {\n                throw new _RuntimeError(2807 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_BLOB */, ngDevMode && 'Response is not a Blob.');\n              }\n              return res.body;\n            }));\n          case 'text':\n            return res$.pipe(map(res => {\n              // Validate that the body is a string.\n              if (res.body !== null && typeof res.body !== 'string') {\n                throw new _RuntimeError(2808 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_STRING */, ngDevMode && 'Response is not a string.');\n              }\n              return res.body;\n            }));\n          case 'json':\n          default:\n            // No validation needed for JSON responses, as they can be of any type.\n            return res$.pipe(map(res => res.body));\n        }\n      case 'response':\n        // The response stream was requested directly, so return it.\n        return res$;\n      default:\n        // Guard against new future observe types being added.\n        throw new _RuntimeError(2809 /* RuntimeErrorCode.UNHANDLED_OBSERVE_TYPE */, ngDevMode && `Unreachable: unhandled observe type ${options.observe}}`);\n    }\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `DELETE` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   */\n  delete(url, options = {}) {\n    return this.request('DELETE', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `GET` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  get(url, options = {}) {\n    return this.request('GET', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `HEAD` request to execute on the server. The `HEAD` method returns\n   * meta information about the resource without transferring the\n   * resource itself. See the individual overloads for\n   * details on the return type.\n   */\n  head(url, options = {}) {\n    return this.request('HEAD', url, options);\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes a request with the special method\n   * `JSONP` to be dispatched via the interceptor pipeline.\n   * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n   * API endpoints that don't support newer,\n   * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n   * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n   * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n   * application making the request.\n   * The endpoint API must support JSONP callback for JSONP requests to work.\n   * The resource API returns the JSON response wrapped in a callback function.\n   * You can pass the callback function name as one of the query parameters.\n   * Note that JSONP requests can only be used with `GET` requests.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   */\n  jsonp(url, callbackParam) {\n    return this.request('JSONP', url, {\n      params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n      observe: 'body',\n      responseType: 'json'\n    });\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes the configured\n   * `OPTIONS` request to execute on the server. This method allows the client\n   * to determine the supported HTTP methods and other capabilities of an endpoint,\n   * without implying a resource action. See the individual overloads for\n   * details on the return type.\n   */\n  options(url, options = {}) {\n    return this.request('OPTIONS', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PATCH` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  patch(url, body, options = {}) {\n    return this.request('PATCH', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `POST` request to execute on the server. The server responds with the location of\n   * the replaced resource. See the individual overloads for\n   * details on the return type.\n   */\n  post(url, body, options = {}) {\n    return this.request('POST', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n   * with a new set of values.\n   * See the individual overloads for details on the return type.\n   */\n  put(url, body, options = {}) {\n    return this.request('PUT', url, addBody(options, body));\n  }\n  static ɵfac = function HttpClient_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClient)(i0.ɵɵinject(HttpHandler));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpClient,\n    factory: HttpClient.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClient, [{\n    type: Injectable\n  }], () => [{\n    type: HttpHandler\n  }], null);\n})();\nconst XSSI_PREFIX$1 = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * response url or the X-Request-URL header.\n */\nfunction getResponseUrl$1(response) {\n  if (response.url) {\n    return response.url;\n  }\n  // stored as lowercase in the map\n  const xRequestUrl = X_REQUEST_URL_HEADER.toLocaleLowerCase();\n  return response.headers.get(xRequestUrl);\n}\n/**\n * An internal injection token to reference `FetchBackend` implementation\n * in a tree-shakable way.\n */\nconst FETCH_BACKEND = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'FETCH_BACKEND' : '');\n/**\n * Uses `fetch` to send requests to a backend server.\n *\n * This `FetchBackend` requires the support of the\n * [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) which is available on all\n * supported browsers and on Node.js v18 or later.\n *\n * @see {@link HttpHandler}\n *\n * @publicApi\n */\nclass FetchBackend {\n  // We use an arrow function to always reference the current global implementation of `fetch`.\n  // This is helpful for cases when the global `fetch` implementation is modified by external code,\n  // see https://github.com/angular/angular/issues/57527.\n  fetchImpl = inject(FetchFactory, {\n    optional: true\n  })?.fetch ?? ((...args) => globalThis.fetch(...args));\n  ngZone = inject(NgZone);\n  handle(request) {\n    return new Observable(observer => {\n      const aborter = new AbortController();\n      this.doRequest(request, aborter.signal, observer).then(noop, error => observer.error(new HttpErrorResponse({\n        error\n      })));\n      return () => aborter.abort();\n    });\n  }\n  async doRequest(request, signal, observer) {\n    const init = this.createRequestInit(request);\n    let response;\n    try {\n      // Run fetch outside of Angular zone.\n      // This is due to Node.js fetch implementation (Undici) which uses a number of setTimeouts to check if\n      // the response should eventually timeout which causes extra CD cycles every 500ms\n      const fetchPromise = this.ngZone.runOutsideAngular(() => this.fetchImpl(request.urlWithParams, {\n        signal,\n        ...init\n      }));\n      // Make sure Zone.js doesn't trigger false-positive unhandled promise\n      // error in case the Promise is rejected synchronously. See function\n      // description for additional information.\n      silenceSuperfluousUnhandledPromiseRejection(fetchPromise);\n      // Send the `Sent` event before awaiting the response.\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      response = await fetchPromise;\n    } catch (error) {\n      observer.error(new HttpErrorResponse({\n        error,\n        status: error.status ?? 0,\n        statusText: error.statusText,\n        url: request.urlWithParams,\n        headers: error.headers\n      }));\n      return;\n    }\n    const headers = new HttpHeaders(response.headers);\n    const statusText = response.statusText;\n    const url = getResponseUrl$1(response) ?? request.urlWithParams;\n    let status = response.status;\n    let body = null;\n    if (request.reportProgress) {\n      observer.next(new HttpHeaderResponse({\n        headers,\n        status,\n        statusText,\n        url\n      }));\n    }\n    if (response.body) {\n      // Read Progress\n      const contentLength = response.headers.get('content-length');\n      const chunks = [];\n      const reader = response.body.getReader();\n      let receivedLength = 0;\n      let decoder;\n      let partialText;\n      // We have to check whether the Zone is defined in the global scope because this may be called\n      // when the zone is nooped.\n      const reqZone = typeof Zone !== 'undefined' && Zone.current;\n      // Perform response processing outside of Angular zone to\n      // ensure no excessive change detection runs are executed\n      // Here calling the async ReadableStreamDefaultReader.read() is responsible for triggering CD\n      await this.ngZone.runOutsideAngular(async () => {\n        while (true) {\n          const {\n            done,\n            value\n          } = await reader.read();\n          if (done) {\n            break;\n          }\n          chunks.push(value);\n          receivedLength += value.length;\n          if (request.reportProgress) {\n            partialText = request.responseType === 'text' ? (partialText ?? '') + (decoder ??= new TextDecoder()).decode(value, {\n              stream: true\n            }) : undefined;\n            const reportProgress = () => observer.next({\n              type: HttpEventType.DownloadProgress,\n              total: contentLength ? +contentLength : undefined,\n              loaded: receivedLength,\n              partialText\n            });\n            reqZone ? reqZone.run(reportProgress) : reportProgress();\n          }\n        }\n      });\n      // Combine all chunks.\n      const chunksAll = this.concatChunks(chunks, receivedLength);\n      try {\n        const contentType = response.headers.get(CONTENT_TYPE_HEADER) ?? '';\n        body = this.parseBody(request, chunksAll, contentType);\n      } catch (error) {\n        // Body loading or parsing failed\n        observer.error(new HttpErrorResponse({\n          error,\n          headers: new HttpHeaders(response.headers),\n          status: response.status,\n          statusText: response.statusText,\n          url: getResponseUrl$1(response) ?? request.urlWithParams\n        }));\n        return;\n      }\n    }\n    // Same behavior as the XhrBackend\n    if (status === 0) {\n      status = body ? HTTP_STATUS_CODE_OK : 0;\n    }\n    // ok determines whether the response will be transmitted on the event or\n    // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n    // but a successful status code can still result in an error if the user\n    // asked for JSON data and the body cannot be parsed as such.\n    const ok = status >= 200 && status < 300;\n    if (ok) {\n      observer.next(new HttpResponse({\n        body,\n        headers,\n        status,\n        statusText,\n        url\n      }));\n      // The full body has been received and delivered, no further events\n      // are possible. This request is complete.\n      observer.complete();\n    } else {\n      observer.error(new HttpErrorResponse({\n        error: body,\n        headers,\n        status,\n        statusText,\n        url\n      }));\n    }\n  }\n  parseBody(request, binContent, contentType) {\n    switch (request.responseType) {\n      case 'json':\n        // stripping the XSSI when present\n        const text = new TextDecoder().decode(binContent).replace(XSSI_PREFIX$1, '');\n        return text === '' ? null : JSON.parse(text);\n      case 'text':\n        return new TextDecoder().decode(binContent);\n      case 'blob':\n        return new Blob([binContent], {\n          type: contentType\n        });\n      case 'arraybuffer':\n        return binContent.buffer;\n    }\n  }\n  createRequestInit(req) {\n    // We could share some of this logic with the XhrBackend\n    const headers = {};\n    const credentials = req.withCredentials ? 'include' : undefined;\n    // Setting all the requested headers.\n    req.headers.forEach((name, values) => headers[name] = values.join(','));\n    // Add an Accept header if one isn't present already.\n    if (!req.headers.has(ACCEPT_HEADER)) {\n      headers[ACCEPT_HEADER] = ACCEPT_HEADER_VALUE;\n    }\n    // Auto-detect the Content-Type header if one isn't present already.\n    if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n      const detectedType = req.detectContentTypeHeader();\n      // Sometimes Content-Type detection fails.\n      if (detectedType !== null) {\n        headers[CONTENT_TYPE_HEADER] = detectedType;\n      }\n    }\n    return {\n      body: req.serializeBody(),\n      method: req.method,\n      headers,\n      credentials\n    };\n  }\n  concatChunks(chunks, totalLength) {\n    const chunksAll = new Uint8Array(totalLength);\n    let position = 0;\n    for (const chunk of chunks) {\n      chunksAll.set(chunk, position);\n      position += chunk.length;\n    }\n    return chunksAll;\n  }\n  static ɵfac = function FetchBackend_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FetchBackend)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FetchBackend,\n    factory: FetchBackend.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FetchBackend, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Abstract class to provide a mocked implementation of `fetch()`\n */\nclass FetchFactory {}\nfunction noop() {}\n/**\n * Zone.js treats a rejected promise that has not yet been awaited\n * as an unhandled error. This function adds a noop `.then` to make\n * sure that Zone.js doesn't throw an error if the Promise is rejected\n * synchronously.\n */\nfunction silenceSuperfluousUnhandledPromiseRejection(promise) {\n  promise.then(noop, noop);\n}\nfunction interceptorChainEndFn(req, finalHandlerFn) {\n  return finalHandlerFn(req);\n}\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(chainTailFn, interceptor) {\n  return (initialRequest, finalHandlerFn) => interceptor.intercept(initialRequest, {\n    handle: downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)\n  });\n}\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(chainTailFn, interceptorFn, injector) {\n  return (initialRequest, finalHandlerFn) => runInInjectionContext(injector, () => interceptorFn(initialRequest, downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)));\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTORS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nconst HTTP_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTOR_FNS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s that are only set in root.\n */\nconst HTTP_ROOT_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_ROOT_INTERCEPTOR_FNS' : '');\n// TODO(atscott): We need a larger discussion about stability and what should contribute to stability.\n// Should the whole interceptor chain contribute to stability or just the backend request #55075?\n// Should HttpClient contribute to stability automatically at all?\nconst REQUESTS_CONTRIBUTE_TO_STABILITY = new InjectionToken(ngDevMode ? 'REQUESTS_CONTRIBUTE_TO_STABILITY' : '', {\n  providedIn: 'root',\n  factory: () => true\n});\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nfunction legacyInterceptorFnFactory() {\n  let chain = null;\n  return (req, handler) => {\n    if (chain === null) {\n      const interceptors = inject(HTTP_INTERCEPTORS, {\n        optional: true\n      }) ?? [];\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      chain = interceptors.reduceRight(adaptLegacyInterceptorToChain, interceptorChainEndFn);\n    }\n    const pendingTasks = inject(_PendingTasksInternal);\n    const contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n    if (contributeToStability) {\n      const taskId = pendingTasks.add();\n      return chain(req, handler).pipe(finalize(() => pendingTasks.remove(taskId)));\n    } else {\n      return chain(req, handler);\n    }\n  };\n}\nlet fetchBackendWarningDisplayed = false;\nclass HttpInterceptorHandler extends HttpHandler {\n  backend;\n  injector;\n  chain = null;\n  pendingTasks = inject(_PendingTasksInternal);\n  contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n  constructor(backend, injector) {\n    super();\n    this.backend = backend;\n    this.injector = injector;\n    // We strongly recommend using fetch backend for HTTP calls when SSR is used\n    // for an application. The logic below checks if that's the case and produces\n    // a warning otherwise.\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !fetchBackendWarningDisplayed) {\n      const isServer = isPlatformServer(injector.get(PLATFORM_ID));\n      // This flag is necessary because provideHttpClientTesting() overrides the backend\n      // even if `withFetch()` is used within the test. When the testing HTTP backend is provided,\n      // no HTTP calls are actually performed during the test, so producing a warning would be\n      // misleading.\n      const isTestingBackend = this.backend.isTestingBackend;\n      if (isServer && !(this.backend instanceof FetchBackend) && !isTestingBackend) {\n        fetchBackendWarningDisplayed = true;\n        injector.get(_Console).warn(_formatRuntimeError(2801 /* RuntimeErrorCode.NOT_USING_FETCH_BACKEND_IN_SSR */, 'Angular detected that `HttpClient` is not configured ' + \"to use `fetch` APIs. It's strongly recommended to \" + 'enable `fetch` for applications that use Server-Side Rendering ' + 'for better performance and compatibility. ' + 'To enable `fetch`, add the `withFetch()` to the `provideHttpClient()` ' + 'call at the root of the application.'));\n      }\n    }\n  }\n  handle(initialRequest) {\n    if (this.chain === null) {\n      const dedupedInterceptorFns = Array.from(new Set([...this.injector.get(HTTP_INTERCEPTOR_FNS), ...this.injector.get(HTTP_ROOT_INTERCEPTOR_FNS, [])]));\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      this.chain = dedupedInterceptorFns.reduceRight((nextSequencedFn, interceptorFn) => chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector), interceptorChainEndFn);\n    }\n    if (this.contributeToStability) {\n      const taskId = this.pendingTasks.add();\n      return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest)).pipe(finalize(() => this.pendingTasks.remove(taskId)));\n    } else {\n      return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest));\n    }\n  }\n  static ɵfac = function HttpInterceptorHandler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpInterceptorHandler)(i0.ɵɵinject(HttpBackend), i0.ɵɵinject(i0.EnvironmentInjector));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpInterceptorHandler,\n    factory: HttpInterceptorHandler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpInterceptorHandler, [{\n    type: Injectable\n  }], () => [{\n    type: HttpBackend\n  }, {\n    type: i0.EnvironmentInjector\n  }], null);\n})();\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n  if (typeof window === 'object') {\n    return window;\n  }\n  return {};\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n  callbackMap;\n  document;\n  /**\n   * A resolved promise that can be used to schedule microtasks in the event handlers.\n   */\n  resolvedPromise = Promise.resolve();\n  constructor(callbackMap, document) {\n    this.callbackMap = callbackMap;\n    this.document = document;\n  }\n  /**\n   * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n   */\n  nextCallback() {\n    return `ng_jsonp_callback_${nextRequestId++}`;\n  }\n  /**\n   * Processes a JSONP request and returns an event stream of the results.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   *\n   */\n  handle(req) {\n    // Firstly, check both the method and response type. If either doesn't match\n    // then the request was improperly routed here and cannot be handled.\n    if (req.method !== 'JSONP') {\n      throw new Error(JSONP_ERR_WRONG_METHOD);\n    } else if (req.responseType !== 'json') {\n      throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n    }\n    // Check the request headers. JSONP doesn't support headers and\n    // cannot set any that were supplied.\n    if (req.headers.keys().length > 0) {\n      throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n    }\n    // Everything else happens inside the Observable boundary.\n    return new Observable(observer => {\n      // The first step to make a request is to generate the callback name, and replace the\n      // callback placeholder in the URL with the name. Care has to be taken here to ensure\n      // a trailing &, if matched, gets inserted back into the URL in the correct place.\n      const callback = this.nextCallback();\n      const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n      // Construct the <script> tag and point it at the URL.\n      const node = this.document.createElement('script');\n      node.src = url;\n      // A JSONP request requires waiting for multiple callbacks. These variables\n      // are closed over and track state across those callbacks.\n      // The response object, if one has been received, or null otherwise.\n      let body = null;\n      // Whether the response callback has been called.\n      let finished = false;\n      // Set the response callback in this.callbackMap (which will be the window\n      // object in the browser. The script being loaded via the <script> tag will\n      // eventually call this callback.\n      this.callbackMap[callback] = data => {\n        // Data has been received from the JSONP script. Firstly, delete this callback.\n        delete this.callbackMap[callback];\n        // Set state to indicate data was received.\n        body = data;\n        finished = true;\n      };\n      // cleanup() is a utility closure that removes the <script> from the page and\n      // the response callback from the window. This logic is used in both the\n      // success, error, and cancellation paths, so it's extracted out for convenience.\n      const cleanup = () => {\n        node.removeEventListener('load', onLoad);\n        node.removeEventListener('error', onError);\n        // Remove the <script> tag if it's still on the page.\n        node.remove();\n        // Remove the response callback from the callbackMap (window object in the\n        // browser).\n        delete this.callbackMap[callback];\n      };\n      // onLoad() is the success callback which runs after the response callback\n      // if the JSONP script loads successfully. The event itself is unimportant.\n      // If something went wrong, onLoad() may run without the response callback\n      // having been invoked.\n      const onLoad = event => {\n        // We wrap it in an extra Promise, to ensure the microtask\n        // is scheduled after the loaded endpoint has executed any potential microtask itself,\n        // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n        this.resolvedPromise.then(() => {\n          // Cleanup the page.\n          cleanup();\n          // Check whether the response callback has run.\n          if (!finished) {\n            // It hasn't, something went wrong with the request. Return an error via\n            // the Observable error path. All JSONP errors have status 0.\n            observer.error(new HttpErrorResponse({\n              url,\n              status: 0,\n              statusText: 'JSONP Error',\n              error: new Error(JSONP_ERR_NO_CALLBACK)\n            }));\n            return;\n          }\n          // Success. body either contains the response body or null if none was\n          // returned.\n          observer.next(new HttpResponse({\n            body,\n            status: HTTP_STATUS_CODE_OK,\n            statusText: 'OK',\n            url\n          }));\n          // Complete the stream, the response is over.\n          observer.complete();\n        });\n      };\n      // onError() is the error callback, which runs if the script returned generates\n      // a Javascript error. It emits the error via the Observable error channel as\n      // a HttpErrorResponse.\n      const onError = error => {\n        cleanup();\n        // Wrap the error in a HttpErrorResponse.\n        observer.error(new HttpErrorResponse({\n          error,\n          status: 0,\n          statusText: 'JSONP Error',\n          url\n        }));\n      };\n      // Subscribe to both the success (load) and error events on the <script> tag,\n      // and add it to the page.\n      node.addEventListener('load', onLoad);\n      node.addEventListener('error', onError);\n      this.document.body.appendChild(node);\n      // The request has now been successfully sent.\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      // Cancellation handler.\n      return () => {\n        if (!finished) {\n          this.removeListeners(node);\n        }\n        // And finally, clean up the page.\n        cleanup();\n      };\n    });\n  }\n  removeListeners(script) {\n    // Issue #34818\n    // Changing <script>'s ownerDocument will prevent it from execution.\n    // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n    foreignDocument ??= this.document.implementation.createHTMLDocument();\n    foreignDocument.adoptNode(script);\n  }\n  static ɵfac = function JsonpClientBackend_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || JsonpClientBackend)(i0.ɵɵinject(JsonpCallbackContext), i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: JsonpClientBackend,\n    factory: JsonpClientBackend.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpClientBackend, [{\n    type: Injectable\n  }], () => [{\n    type: JsonpCallbackContext\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nfunction jsonpInterceptorFn(req, next) {\n  if (req.method === 'JSONP') {\n    return inject(JsonpClientBackend).handle(req);\n  }\n  // Fall through for normal HTTP requests.\n  return next(req);\n}\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n  injector;\n  constructor(injector) {\n    this.injector = injector;\n  }\n  /**\n   * Identifies and handles a given JSONP request.\n   * @param initialRequest The outgoing request object to handle.\n   * @param next The next interceptor in the chain, or the backend\n   * if no interceptors remain in the chain.\n   * @returns An observable of the event stream.\n   */\n  intercept(initialRequest, next) {\n    return runInInjectionContext(this.injector, () => jsonpInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n  static ɵfac = function JsonpInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || JsonpInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: JsonpInterceptor,\n    factory: JsonpInterceptor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\nconst X_REQUEST_URL_REGEXP = RegExp(`^${X_REQUEST_URL_HEADER}:`, 'm');\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n  if ('responseURL' in xhr && xhr.responseURL) {\n    return xhr.responseURL;\n  }\n  if (X_REQUEST_URL_REGEXP.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader(X_REQUEST_URL_HEADER);\n  }\n  return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n  xhrFactory;\n  constructor(xhrFactory) {\n    this.xhrFactory = xhrFactory;\n  }\n  /**\n   * Processes a request and returns a stream of response events.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   */\n  handle(req) {\n    // Quick check to give a better error message when a user attempts to use\n    // HttpClient.jsonp() without installing the HttpClientJsonpModule\n    if (req.method === 'JSONP') {\n      throw new _RuntimeError(-2800 /* RuntimeErrorCode.MISSING_JSONP_MODULE */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Cannot make a JSONP request without JSONP support. To fix the problem, either add the \\`withJsonpSupport()\\` call (if \\`provideHttpClient()\\` is used) or import the \\`HttpClientJsonpModule\\` in the root NgModule.`);\n    }\n    // Check whether this factory has a special function to load an XHR implementation\n    // for various non-browser environments. We currently limit it to only `ServerXhr`\n    // class, which needs to load an XHR implementation.\n    const xhrFactory = this.xhrFactory;\n    const source = xhrFactory.ɵloadImpl ? from(xhrFactory.ɵloadImpl()) : of(null);\n    return source.pipe(switchMap(() => {\n      // Everything happens on Observable subscription.\n      return new Observable(observer => {\n        // Start by setting up the XHR object with request method, URL, and withCredentials\n        // flag.\n        const xhr = xhrFactory.build();\n        xhr.open(req.method, req.urlWithParams);\n        if (req.withCredentials) {\n          xhr.withCredentials = true;\n        }\n        // Add all the requested headers.\n        req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n        // Add an Accept header if one isn't present already.\n        if (!req.headers.has(ACCEPT_HEADER)) {\n          xhr.setRequestHeader(ACCEPT_HEADER, ACCEPT_HEADER_VALUE);\n        }\n        // Auto-detect the Content-Type header if one isn't present already.\n        if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n          const detectedType = req.detectContentTypeHeader();\n          // Sometimes Content-Type detection fails.\n          if (detectedType !== null) {\n            xhr.setRequestHeader(CONTENT_TYPE_HEADER, detectedType);\n          }\n        }\n        // Set the responseType if one was requested.\n        if (req.responseType) {\n          const responseType = req.responseType.toLowerCase();\n          // JSON responses need to be processed as text. This is because if the server\n          // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n          // xhr.response will be null, and xhr.responseText cannot be accessed to\n          // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n          // is parsed by first requesting text and then applying JSON.parse.\n          xhr.responseType = responseType !== 'json' ? responseType : 'text';\n        }\n        // Serialize the request body if one is present. If not, this will be set to null.\n        const reqBody = req.serializeBody();\n        // If progress events are enabled, response headers will be delivered\n        // in two events - the HttpHeaderResponse event and the full HttpResponse\n        // event. However, since response headers don't change in between these\n        // two events, it doesn't make sense to parse them twice. So headerResponse\n        // caches the data extracted from the response whenever it's first parsed,\n        // to ensure parsing isn't duplicated.\n        let headerResponse = null;\n        // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n        // state, and memoizes it into headerResponse.\n        const partialFromXhr = () => {\n          if (headerResponse !== null) {\n            return headerResponse;\n          }\n          const statusText = xhr.statusText || 'OK';\n          // Parse headers from XMLHttpRequest - this step is lazy.\n          const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n          // Read the response URL from the XMLHttpResponse instance and fall back on the\n          // request URL.\n          const url = getResponseUrl(xhr) || req.url;\n          // Construct the HttpHeaderResponse and memoize it.\n          headerResponse = new HttpHeaderResponse({\n            headers,\n            status: xhr.status,\n            statusText,\n            url\n          });\n          return headerResponse;\n        };\n        // Next, a few closures are defined for the various events which XMLHttpRequest can\n        // emit. This allows them to be unregistered as event listeners later.\n        // First up is the load event, which represents a response being fully available.\n        const onLoad = () => {\n          // Read response state from the memoized partial data.\n          let {\n            headers,\n            status,\n            statusText,\n            url\n          } = partialFromXhr();\n          // The body will be read out if present.\n          let body = null;\n          if (status !== HTTP_STATUS_CODE_NO_CONTENT) {\n            // Use XMLHttpRequest.response if set, responseText otherwise.\n            body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n          }\n          // Normalize another potential bug (this one comes from CORS).\n          if (status === 0) {\n            status = !!body ? HTTP_STATUS_CODE_OK : 0;\n          }\n          // ok determines whether the response will be transmitted on the event or\n          // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n          // but a successful status code can still result in an error if the user\n          // asked for JSON data and the body cannot be parsed as such.\n          let ok = status >= 200 && status < 300;\n          // Check whether the body needs to be parsed as JSON (in many cases the browser\n          // will have done that already).\n          if (req.responseType === 'json' && typeof body === 'string') {\n            // Save the original body, before attempting XSSI prefix stripping.\n            const originalBody = body;\n            body = body.replace(XSSI_PREFIX, '');\n            try {\n              // Attempt the parse. If it fails, a parse error should be delivered to the\n              // user.\n              body = body !== '' ? JSON.parse(body) : null;\n            } catch (error) {\n              // Since the JSON.parse failed, it's reasonable to assume this might not have\n              // been a JSON response. Restore the original body (including any XSSI prefix)\n              // to deliver a better error response.\n              body = originalBody;\n              // If this was an error request to begin with, leave it as a string, it\n              // probably just isn't JSON. Otherwise, deliver the parsing error to the user.\n              if (ok) {\n                // Even though the response status was 2xx, this is still an error.\n                ok = false;\n                // The parse error contains the text of the body that failed to parse.\n                body = {\n                  error,\n                  text: body\n                };\n              }\n            }\n          }\n          if (ok) {\n            // A successful response is delivered on the event stream.\n            observer.next(new HttpResponse({\n              body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n            // The full body has been received and delivered, no further events\n            // are possible. This request is complete.\n            observer.complete();\n          } else {\n            // An unsuccessful request is delivered on the error channel.\n            observer.error(new HttpErrorResponse({\n              // The error in this case is the response body (error from the server).\n              error: body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n          }\n        };\n        // The onError callback is called when something goes wrong at the network level.\n        // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n        // transmitted on the error channel.\n        const onError = error => {\n          const {\n            url\n          } = partialFromXhr();\n          const res = new HttpErrorResponse({\n            error,\n            status: xhr.status || 0,\n            statusText: xhr.statusText || 'Unknown Error',\n            url: url || undefined\n          });\n          observer.error(res);\n        };\n        // The sentHeaders flag tracks whether the HttpResponseHeaders event\n        // has been sent on the stream. This is necessary to track if progress\n        // is enabled since the event will be sent on only the first download\n        // progress event.\n        let sentHeaders = false;\n        // The download progress event handler, which is only registered if\n        // progress events are enabled.\n        const onDownProgress = event => {\n          // Send the HttpResponseHeaders event if it hasn't been sent already.\n          if (!sentHeaders) {\n            observer.next(partialFromXhr());\n            sentHeaders = true;\n          }\n          // Start building the download progress event to deliver on the response\n          // event stream.\n          let progressEvent = {\n            type: HttpEventType.DownloadProgress,\n            loaded: event.loaded\n          };\n          // Set the total number of bytes in the event if it's available.\n          if (event.lengthComputable) {\n            progressEvent.total = event.total;\n          }\n          // If the request was for text content and a partial response is\n          // available on XMLHttpRequest, include it in the progress event\n          // to allow for streaming reads.\n          if (req.responseType === 'text' && !!xhr.responseText) {\n            progressEvent.partialText = xhr.responseText;\n          }\n          // Finally, fire the event.\n          observer.next(progressEvent);\n        };\n        // The upload progress event handler, which is only registered if\n        // progress events are enabled.\n        const onUpProgress = event => {\n          // Upload progress events are simpler. Begin building the progress\n          // event.\n          let progress = {\n            type: HttpEventType.UploadProgress,\n            loaded: event.loaded\n          };\n          // If the total number of bytes being uploaded is available, include\n          // it.\n          if (event.lengthComputable) {\n            progress.total = event.total;\n          }\n          // Send the event.\n          observer.next(progress);\n        };\n        // By default, register for load and error events.\n        xhr.addEventListener('load', onLoad);\n        xhr.addEventListener('error', onError);\n        xhr.addEventListener('timeout', onError);\n        xhr.addEventListener('abort', onError);\n        // Progress events are only enabled if requested.\n        if (req.reportProgress) {\n          // Download progress is always enabled if requested.\n          xhr.addEventListener('progress', onDownProgress);\n          // Upload progress depends on whether there is a body to upload.\n          if (reqBody !== null && xhr.upload) {\n            xhr.upload.addEventListener('progress', onUpProgress);\n          }\n        }\n        // Fire the request, and notify the event stream that it was fired.\n        xhr.send(reqBody);\n        observer.next({\n          type: HttpEventType.Sent\n        });\n        // This is the return from the Observable function, which is the\n        // request cancellation handler.\n        return () => {\n          // On a cancellation, remove all registered event listeners.\n          xhr.removeEventListener('error', onError);\n          xhr.removeEventListener('abort', onError);\n          xhr.removeEventListener('load', onLoad);\n          xhr.removeEventListener('timeout', onError);\n          if (req.reportProgress) {\n            xhr.removeEventListener('progress', onDownProgress);\n            if (reqBody !== null && xhr.upload) {\n              xhr.upload.removeEventListener('progress', onUpProgress);\n            }\n          }\n          // Finally, abort the in-flight request.\n          if (xhr.readyState !== xhr.DONE) {\n            xhr.abort();\n          }\n        };\n      });\n    }));\n  }\n  static ɵfac = function HttpXhrBackend_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpXhrBackend)(i0.ɵɵinject(XhrFactory));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXhrBackend,\n    factory: HttpXhrBackend.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXhrBackend, [{\n    type: Injectable\n  }], () => [{\n    type: XhrFactory\n  }], null);\n})();\nconst XSRF_ENABLED = new InjectionToken(ngDevMode ? 'XSRF_ENABLED' : '');\nconst XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nconst XSRF_COOKIE_NAME = new InjectionToken(ngDevMode ? 'XSRF_COOKIE_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_COOKIE_NAME\n});\nconst XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nconst XSRF_HEADER_NAME = new InjectionToken(ngDevMode ? 'XSRF_HEADER_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_HEADER_NAME\n});\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n  doc;\n  platform;\n  cookieName;\n  lastCookieString = '';\n  lastToken = null;\n  /**\n   * @internal for testing\n   */\n  parseCount = 0;\n  constructor(doc, platform, cookieName) {\n    this.doc = doc;\n    this.platform = platform;\n    this.cookieName = cookieName;\n  }\n  getToken() {\n    if (this.platform === 'server') {\n      return null;\n    }\n    const cookieString = this.doc.cookie || '';\n    if (cookieString !== this.lastCookieString) {\n      this.parseCount++;\n      this.lastToken = parseCookieValue(cookieString, this.cookieName);\n      this.lastCookieString = cookieString;\n    }\n    return this.lastToken;\n  }\n  static ɵfac = function HttpXsrfCookieExtractor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpXsrfCookieExtractor)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(XSRF_COOKIE_NAME));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXsrfCookieExtractor,\n    factory: HttpXsrfCookieExtractor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfCookieExtractor, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [XSRF_COOKIE_NAME]\n    }]\n  }], null);\n})();\nfunction xsrfInterceptorFn(req, next) {\n  const lcUrl = req.url.toLowerCase();\n  // Skip both non-mutating requests and absolute URLs.\n  // Non-mutating requests don't require a token, and absolute URLs require special handling\n  // anyway as the cookie set\n  // on our origin is not the same as the token expected by another origin.\n  if (!inject(XSRF_ENABLED) || req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') || lcUrl.startsWith('https://')) {\n    return next(req);\n  }\n  const token = inject(HttpXsrfTokenExtractor).getToken();\n  const headerName = inject(XSRF_HEADER_NAME);\n  // Be careful not to overwrite an existing header of the same name.\n  if (token != null && !req.headers.has(headerName)) {\n    req = req.clone({\n      headers: req.headers.set(headerName, token)\n    });\n  }\n  return next(req);\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n  injector;\n  constructor(injector) {\n    this.injector = injector;\n  }\n  intercept(initialRequest, next) {\n    return runInInjectionContext(this.injector, () => xsrfInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n  static ɵfac = function HttpXsrfInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpXsrfInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXsrfInterceptor,\n    factory: HttpXsrfInterceptor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nvar HttpFeatureKind;\n(function (HttpFeatureKind) {\n  HttpFeatureKind[HttpFeatureKind[\"Interceptors\"] = 0] = \"Interceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"LegacyInterceptors\"] = 1] = \"LegacyInterceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"CustomXsrfConfiguration\"] = 2] = \"CustomXsrfConfiguration\";\n  HttpFeatureKind[HttpFeatureKind[\"NoXsrfProtection\"] = 3] = \"NoXsrfProtection\";\n  HttpFeatureKind[HttpFeatureKind[\"JsonpSupport\"] = 4] = \"JsonpSupport\";\n  HttpFeatureKind[HttpFeatureKind[\"RequestsMadeViaParent\"] = 5] = \"RequestsMadeViaParent\";\n  HttpFeatureKind[HttpFeatureKind[\"Fetch\"] = 6] = \"Fetch\";\n})(HttpFeatureKind || (HttpFeatureKind = {}));\nfunction makeHttpFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * It's strongly recommended to enable\n * [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) for applications that use\n * Server-Side Rendering for better performance and compatibility. To enable `fetch`, add\n * `withFetch()` feature to the `provideHttpClient()` call at the root of the application:\n *\n * ```ts\n * provideHttpClient(withFetch());\n * ```\n *\n * </div>\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n * @see {@link withFetch}\n */\nfunction provideHttpClient(...features) {\n  if (ngDevMode) {\n    const featureKinds = new Set(features.map(f => f.ɵkind));\n    if (featureKinds.has(HttpFeatureKind.NoXsrfProtection) && featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)) {\n      throw new Error(ngDevMode ? `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.` : '');\n    }\n  }\n  const providers = [HttpClient, HttpXhrBackend, HttpInterceptorHandler, {\n    provide: HttpHandler,\n    useExisting: HttpInterceptorHandler\n  }, {\n    provide: HttpBackend,\n    useFactory: () => {\n      return inject(FETCH_BACKEND, {\n        optional: true\n      }) ?? inject(HttpXhrBackend);\n    }\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: xsrfInterceptorFn,\n    multi: true\n  }, {\n    provide: XSRF_ENABLED,\n    useValue: true\n  }, {\n    provide: HttpXsrfTokenExtractor,\n    useClass: HttpXsrfCookieExtractor\n  }];\n  for (const feature of features) {\n    providers.push(...feature.ɵproviders);\n  }\n  return makeEnvironmentProviders(providers);\n}\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withInterceptors(interceptorFns) {\n  return makeHttpFeature(HttpFeatureKind.Interceptors, interceptorFns.map(interceptorFn => {\n    return {\n      provide: HTTP_INTERCEPTOR_FNS,\n      useValue: interceptorFn,\n      multi: true\n    };\n  }));\n}\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken(ngDevMode ? 'LEGACY_INTERCEPTOR_FN' : '');\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nfunction withInterceptorsFromDi() {\n  // Note: the legacy interceptor function is provided here via an intermediate token\n  // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n  // included multiple times, all of the multi-provider entries will have the same instance of the\n  // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n  // interceptors will not run multiple times.\n  return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [{\n    provide: LEGACY_INTERCEPTOR_FN,\n    useFactory: legacyInterceptorFnFactory\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useExisting: LEGACY_INTERCEPTOR_FN,\n    multi: true\n  }]);\n}\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withXsrfConfiguration({\n  cookieName,\n  headerName\n}) {\n  const providers = [];\n  if (cookieName !== undefined) {\n    providers.push({\n      provide: XSRF_COOKIE_NAME,\n      useValue: cookieName\n    });\n  }\n  if (headerName !== undefined) {\n    providers.push({\n      provide: XSRF_HEADER_NAME,\n      useValue: headerName\n    });\n  }\n  return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withNoXsrfProtection() {\n  return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [{\n    provide: XSRF_ENABLED,\n    useValue: false\n  }]);\n}\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nfunction withJsonpSupport() {\n  return makeHttpFeature(HttpFeatureKind.JsonpSupport, [JsonpClientBackend, {\n    provide: JsonpCallbackContext,\n    useFactory: jsonpCallbackContext\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: jsonpInterceptorFn,\n    multi: true\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withRequestsMadeViaParent() {\n  return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [{\n    provide: HttpBackend,\n    useFactory: () => {\n      const handlerFromParent = inject(HttpHandler, {\n        skipSelf: true,\n        optional: true\n      });\n      if (ngDevMode && handlerFromParent === null) {\n        throw new Error('withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient');\n      }\n      return handlerFromParent;\n    }\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests using the fetch API.\n *\n * Note: The Fetch API doesn't support progress report on uploads.\n *\n * @publicApi\n */\nfunction withFetch() {\n  return makeHttpFeature(HttpFeatureKind.Fetch, [FetchBackend, {\n    provide: FETCH_BACKEND,\n    useExisting: FetchBackend\n  }, {\n    provide: HttpBackend,\n    useExisting: FetchBackend\n  }]);\n}\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n * @deprecated Use withXsrfConfiguration({cookieName: 'XSRF-TOKEN', headerName: 'X-XSRF-TOKEN'}) as\n *     providers instead or `withNoXsrfProtection` if you want to disabled XSRF protection.\n */\nclass HttpClientXsrfModule {\n  /**\n   * Disable the default XSRF protection.\n   */\n  static disable() {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [withNoXsrfProtection().ɵproviders]\n    };\n  }\n  /**\n   * Configure XSRF protection.\n   * @param options An object that can specify either or both\n   * cookie name or header name.\n   * - Cookie name default is `XSRF-TOKEN`.\n   * - Header name default is `X-XSRF-TOKEN`.\n   *\n   */\n  static withOptions(options = {}) {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: withXsrfConfiguration(options).ɵproviders\n    };\n  }\n  static ɵfac = function HttpClientXsrfModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientXsrfModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientXsrfModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [HttpXsrfInterceptor, {\n      provide: HTTP_INTERCEPTORS,\n      useExisting: HttpXsrfInterceptor,\n      multi: true\n    }, {\n      provide: HttpXsrfTokenExtractor,\n      useClass: HttpXsrfCookieExtractor\n    }, withXsrfConfiguration({\n      cookieName: XSRF_DEFAULT_COOKIE_NAME,\n      headerName: XSRF_DEFAULT_HEADER_NAME\n    }).ɵproviders, {\n      provide: XSRF_ENABLED,\n      useValue: true\n    }]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientXsrfModule, [{\n    type: NgModule,\n    args: [{\n      providers: [HttpXsrfInterceptor, {\n        provide: HTTP_INTERCEPTORS,\n        useExisting: HttpXsrfInterceptor,\n        multi: true\n      }, {\n        provide: HttpXsrfTokenExtractor,\n        useClass: HttpXsrfCookieExtractor\n      }, withXsrfConfiguration({\n        cookieName: XSRF_DEFAULT_COOKIE_NAME,\n        headerName: XSRF_DEFAULT_HEADER_NAME\n      }).ɵproviders, {\n        provide: XSRF_ENABLED,\n        useValue: true\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in DI token `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n * @deprecated use `provideHttpClient(withInterceptorsFromDi())` as providers instead\n */\nclass HttpClientModule {\n  static ɵfac = function HttpClientModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [provideHttpClient(withInterceptorsFromDi())]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientModule, [{\n    type: NgModule,\n    args: [{\n      /**\n       * Configures the dependency injector where it is imported\n       * with supporting services for HTTP communications.\n       */\n      providers: [provideHttpClient(withInterceptorsFromDi())]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n * @deprecated `withJsonpSupport()` as providers instead\n */\nclass HttpClientJsonpModule {\n  static ɵfac = function HttpClientJsonpModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientJsonpModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientJsonpModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [withJsonpSupport().ɵproviders]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientJsonpModule, [{\n    type: NgModule,\n    args: [{\n      providers: [withJsonpSupport().ɵproviders]\n    }]\n  }], null, null);\n})();\nexport { FetchBackend, HTTP_INTERCEPTORS, HTTP_ROOT_INTERCEPTOR_FNS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpInterceptorHandler, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, REQUESTS_CONTRIBUTE_TO_STABILITY, provideHttpClient, withFetch, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration };\n", "/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { HttpErrorResponse, HttpEventType, HttpClient, HttpHeaders, HttpParams, HttpRequest, HTTP_ROOT_INTERCEPTOR_FNS, HttpResponse } from './module-BHk9jdTn.mjs';\nexport { FetchBackend, HTTP_INTERCEPTORS, HttpBackend, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, <PERSON>sonpInterceptor, provideHttpClient, withFet<PERSON>, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration, HttpInterceptorHandler as ɵHttpInterceptingHandler, HttpInterceptorHandler as ɵHttpInterceptorHandler, REQUESTS_CONTRIBUTE_TO_STABILITY as ɵREQUESTS_CONTRIBUTE_TO_STABILITY } from './module-BHk9jdTn.mjs';\nimport { assertInInjectionContext, inject, Injector, ɵResourceImpl as _ResourceImpl, linkedSignal, computed, ResourceStatus, signal, InjectionToken, APP_BOOTSTRAP_LISTENER, ɵperformanceMarkFeature as _performanceMarkFeature, ApplicationRef, TransferState, ɵRuntimeError as _RuntimeError, makeStateKey, ɵtruncateMiddle as _truncateMiddle, ɵformatRuntimeError as _formatRuntimeError } from '@angular/core';\nimport { of } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport './xhr-BfNfxNDv.mjs';\nimport './dom_tokens-rA0ACyx7.mjs';\n\n/**\n * `httpResource` makes a reactive HTTP request and exposes the request status and response value as\n * a `WritableResource`. By default, it assumes that the backend will return JSON data. To make a\n * request that expects a different kind of data, you can use a sub-constructor of `httpResource`,\n * such as `httpResource.text`.\n *\n * @experimental\n * @initializerApiFunction\n */\nconst httpResource = (() => {\n  const jsonFn = makeHttpResourceFn('json');\n  jsonFn.arrayBuffer = makeHttpResourceFn('arraybuffer');\n  jsonFn.blob = makeHttpResourceFn('blob');\n  jsonFn.text = makeHttpResourceFn('text');\n  return jsonFn;\n})();\nfunction makeHttpResourceFn(responseType) {\n  return function httpResourceRef(request, options) {\n    options?.injector || assertInInjectionContext(httpResource);\n    const injector = options?.injector ?? inject(Injector);\n    return new HttpResourceImpl(injector, () => normalizeRequest(request, responseType), options?.defaultValue, options?.parse, options?.equal);\n  };\n}\nfunction normalizeRequest(request, responseType) {\n  let unwrappedRequest = typeof request === 'function' ? request() : request;\n  if (unwrappedRequest === undefined) {\n    return undefined;\n  } else if (typeof unwrappedRequest === 'string') {\n    unwrappedRequest = {\n      url: unwrappedRequest\n    };\n  }\n  const headers = unwrappedRequest.headers instanceof HttpHeaders ? unwrappedRequest.headers : new HttpHeaders(unwrappedRequest.headers);\n  const params = unwrappedRequest.params instanceof HttpParams ? unwrappedRequest.params : new HttpParams({\n    fromObject: unwrappedRequest.params\n  });\n  return new HttpRequest(unwrappedRequest.method ?? 'GET', unwrappedRequest.url, unwrappedRequest.body ?? null, {\n    headers,\n    params,\n    reportProgress: unwrappedRequest.reportProgress,\n    withCredentials: unwrappedRequest.withCredentials,\n    responseType,\n    context: unwrappedRequest.context,\n    transferCache: unwrappedRequest.transferCache\n  });\n}\nclass HttpResourceImpl extends _ResourceImpl {\n  client;\n  _headers = linkedSignal({\n    source: this.extRequest,\n    computation: () => undefined\n  });\n  _progress = linkedSignal({\n    source: this.extRequest,\n    computation: () => undefined\n  });\n  _statusCode = linkedSignal({\n    source: this.extRequest,\n    computation: () => undefined\n  });\n  headers = computed(() => this.status() === ResourceStatus.Resolved || this.status() === ResourceStatus.Error ? this._headers() : undefined);\n  progress = this._progress.asReadonly();\n  statusCode = this._statusCode.asReadonly();\n  constructor(injector, request, defaultValue, parse, equal) {\n    super(request, ({\n      request,\n      abortSignal\n    }) => {\n      let sub;\n      // Track the abort listener so it can be removed if the Observable completes (as a memory\n      // optimization).\n      const onAbort = () => sub.unsubscribe();\n      abortSignal.addEventListener('abort', onAbort);\n      // Start off stream as undefined.\n      const stream = signal({\n        value: undefined\n      });\n      let resolve;\n      const promise = new Promise(r => resolve = r);\n      const send = value => {\n        stream.set(value);\n        resolve?.(stream);\n        resolve = undefined;\n      };\n      sub = this.client.request(request).subscribe({\n        next: event => {\n          switch (event.type) {\n            case HttpEventType.Response:\n              this._headers.set(event.headers);\n              this._statusCode.set(event.status);\n              try {\n                send({\n                  value: parse ? parse(event.body) : event.body\n                });\n              } catch (error) {\n                send({\n                  error\n                });\n              }\n              break;\n            case HttpEventType.DownloadProgress:\n              this._progress.set(event);\n              break;\n          }\n        },\n        error: error => {\n          if (error instanceof HttpErrorResponse) {\n            this._headers.set(error.headers);\n            this._statusCode.set(error.status);\n          }\n          send({\n            error\n          });\n        },\n        complete: () => {\n          if (resolve) {\n            send({\n              error: new Error('Resource completed before producing a value')\n            });\n          }\n          abortSignal.removeEventListener('abort', onAbort);\n        }\n      });\n      return promise;\n    }, defaultValue, equal, injector);\n    this.client = injector.get(HttpClient);\n  }\n}\n\n/**\n * If your application uses different HTTP origins to make API calls (via `HttpClient`) on the server and\n * on the client, the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token allows you to establish a mapping\n * between those origins, so that `HttpTransferCache` feature can recognize those requests as the same\n * ones and reuse the data cached on the server during hydration on the client.\n *\n * **Important note**: the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token should *only* be provided in\n * the *server* code of your application (typically in the `app.server.config.ts` script). Angular throws an\n * error if it detects that the token is defined while running on the client.\n *\n * @usageNotes\n *\n * When the same API endpoint is accessed via `http://internal-domain.com:8080` on the server and\n * via `https://external-domain.com` on the client, you can use the following configuration:\n * ```ts\n * // in app.server.config.ts\n * {\n *     provide: HTTP_TRANSFER_CACHE_ORIGIN_MAP,\n *     useValue: {\n *         'http://internal-domain.com:8080': 'https://external-domain.com'\n *     }\n * }\n * ```\n *\n * @publicApi\n */\nconst HTTP_TRANSFER_CACHE_ORIGIN_MAP = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_CACHE_ORIGIN_MAP' : '');\n/**\n * Keys within cached response data structure.\n */\nconst BODY = 'b';\nconst HEADERS = 'h';\nconst STATUS = 's';\nconst STATUS_TEXT = 'st';\nconst REQ_URL = 'u';\nconst RESPONSE_TYPE = 'rt';\nconst CACHE_OPTIONS = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_STATE_CACHE_OPTIONS' : '');\n/**\n * A list of allowed HTTP methods to cache.\n */\nconst ALLOWED_METHODS = ['GET', 'HEAD'];\nfunction transferCacheInterceptorFn(req, next) {\n  const {\n    isCacheActive,\n    ...globalOptions\n  } = inject(CACHE_OPTIONS);\n  const {\n    transferCache: requestOptions,\n    method: requestMethod\n  } = req;\n  // In the following situations we do not want to cache the request\n  if (!isCacheActive || requestOptions === false ||\n  // POST requests are allowed either globally or at request level\n  requestMethod === 'POST' && !globalOptions.includePostRequests && !requestOptions || requestMethod !== 'POST' && !ALLOWED_METHODS.includes(requestMethod) ||\n  // Do not cache request that require authorization when includeRequestsWithAuthHeaders is falsey\n  !globalOptions.includeRequestsWithAuthHeaders && hasAuthHeaders(req) || globalOptions.filter?.(req) === false) {\n    return next(req);\n  }\n  const transferState = inject(TransferState);\n  const originMap = inject(HTTP_TRANSFER_CACHE_ORIGIN_MAP, {\n    optional: true\n  });\n  if (typeof ngServerMode !== 'undefined' && !ngServerMode && originMap) {\n    throw new _RuntimeError(2803 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_USED_IN_CLIENT */, ngDevMode && 'Angular detected that the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token is configured and ' + 'present in the client side code. Please ensure that this token is only provided in the ' + 'server code of the application.');\n  }\n  const requestUrl = typeof ngServerMode !== 'undefined' && ngServerMode && originMap ? mapRequestOriginUrl(req.url, originMap) : req.url;\n  const storeKey = makeCacheKey(req, requestUrl);\n  const response = transferState.get(storeKey, null);\n  let headersToInclude = globalOptions.includeHeaders;\n  if (typeof requestOptions === 'object' && requestOptions.includeHeaders) {\n    // Request-specific config takes precedence over the global config.\n    headersToInclude = requestOptions.includeHeaders;\n  }\n  if (response) {\n    const {\n      [BODY]: undecodedBody,\n      [RESPONSE_TYPE]: responseType,\n      [HEADERS]: httpHeaders,\n      [STATUS]: status,\n      [STATUS_TEXT]: statusText,\n      [REQ_URL]: url\n    } = response;\n    // Request found in cache. Respond using it.\n    let body = undecodedBody;\n    switch (responseType) {\n      case 'arraybuffer':\n        body = new TextEncoder().encode(undecodedBody).buffer;\n        break;\n      case 'blob':\n        body = new Blob([undecodedBody]);\n        break;\n    }\n    // We want to warn users accessing a header provided from the cache\n    // That HttpTransferCache alters the headers\n    // The warning will be logged a single time by HttpHeaders instance\n    let headers = new HttpHeaders(httpHeaders);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Append extra logic in dev mode to produce a warning when a header\n      // that was not transferred to the client is accessed in the code via `get`\n      // and `has` calls.\n      headers = appendMissingHeadersDetection(req.url, headers, headersToInclude ?? []);\n    }\n    return of(new HttpResponse({\n      body,\n      headers,\n      status,\n      statusText,\n      url\n    }));\n  }\n  // Request not found in cache. Make the request and cache it if on the server.\n  return next(req).pipe(tap(event => {\n    if (event instanceof HttpResponse && typeof ngServerMode !== 'undefined' && ngServerMode) {\n      transferState.set(storeKey, {\n        [BODY]: event.body,\n        [HEADERS]: getFilteredHeaders(event.headers, headersToInclude),\n        [STATUS]: event.status,\n        [STATUS_TEXT]: event.statusText,\n        [REQ_URL]: requestUrl,\n        [RESPONSE_TYPE]: req.responseType\n      });\n    }\n  }));\n}\n/** @returns true when the requests contains autorization related headers. */\nfunction hasAuthHeaders(req) {\n  return req.headers.has('authorization') || req.headers.has('proxy-authorization');\n}\nfunction getFilteredHeaders(headers, includeHeaders) {\n  if (!includeHeaders) {\n    return {};\n  }\n  const headersMap = {};\n  for (const key of includeHeaders) {\n    const values = headers.getAll(key);\n    if (values !== null) {\n      headersMap[key] = values;\n    }\n  }\n  return headersMap;\n}\nfunction sortAndConcatParams(params) {\n  return [...params.keys()].sort().map(k => `${k}=${params.getAll(k)}`).join('&');\n}\nfunction makeCacheKey(request, mappedRequestUrl) {\n  // make the params encoded same as a url so it's easy to identify\n  const {\n    params,\n    method,\n    responseType\n  } = request;\n  const encodedParams = sortAndConcatParams(params);\n  let serializedBody = request.serializeBody();\n  if (serializedBody instanceof URLSearchParams) {\n    serializedBody = sortAndConcatParams(serializedBody);\n  } else if (typeof serializedBody !== 'string') {\n    serializedBody = '';\n  }\n  const key = [method, responseType, mappedRequestUrl, serializedBody, encodedParams].join('|');\n  const hash = generateHash(key);\n  return makeStateKey(hash);\n}\n/**\n * A method that returns a hash representation of a string using a variant of DJB2 hash\n * algorithm.\n *\n * This is the same hashing logic that is used to generate component ids.\n */\nfunction generateHash(value) {\n  let hash = 0;\n  for (const char of value) {\n    hash = Math.imul(31, hash) + char.charCodeAt(0) << 0;\n  }\n  // Force positive number hash.\n  // ********** = equivalent of Integer.MAX_VALUE.\n  hash += ********** + 1;\n  return hash.toString();\n}\n/**\n * Returns the DI providers needed to enable HTTP transfer cache.\n *\n * By default, when using server rendering, requests are performed twice: once on the server and\n * other one on the browser.\n *\n * When these providers are added, requests performed on the server are cached and reused during the\n * bootstrapping of the application in the browser thus avoiding duplicate requests and reducing\n * load time.\n *\n */\nfunction withHttpTransferCache(cacheOptions) {\n  return [{\n    provide: CACHE_OPTIONS,\n    useFactory: () => {\n      _performanceMarkFeature('NgHttpTransferCache');\n      return {\n        isCacheActive: true,\n        ...cacheOptions\n      };\n    }\n  }, {\n    provide: HTTP_ROOT_INTERCEPTOR_FNS,\n    useValue: transferCacheInterceptorFn,\n    multi: true\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useFactory: () => {\n      const appRef = inject(ApplicationRef);\n      const cacheState = inject(CACHE_OPTIONS);\n      return () => {\n        appRef.whenStable().then(() => {\n          cacheState.isCacheActive = false;\n        });\n      };\n    }\n  }];\n}\n/**\n * This function will add a proxy to an HttpHeader to intercept calls to get/has\n * and log a warning if the header entry requested has been removed\n */\nfunction appendMissingHeadersDetection(url, headers, headersToInclude) {\n  const warningProduced = new Set();\n  return new Proxy(headers, {\n    get(target, prop) {\n      const value = Reflect.get(target, prop);\n      const methods = new Set(['get', 'has', 'getAll']);\n      if (typeof value !== 'function' || !methods.has(prop)) {\n        return value;\n      }\n      return headerName => {\n        // We log when the key has been removed and a warning hasn't been produced for the header\n        const key = (prop + ':' + headerName).toLowerCase(); // e.g. `get:cache-control`\n        if (!headersToInclude.includes(headerName) && !warningProduced.has(key)) {\n          warningProduced.add(key);\n          const truncatedUrl = _truncateMiddle(url);\n          // TODO: create Error guide for this warning\n          console.warn(_formatRuntimeError(2802 /* RuntimeErrorCode.HEADERS_ALTERED_BY_TRANSFER_CACHE */, `Angular detected that the \\`${headerName}\\` header is accessed, but the value of the header ` + `was not transferred from the server to the client by the HttpTransferCache. ` + `To include the value of the \\`${headerName}\\` header for the \\`${truncatedUrl}\\` request, ` + `use the \\`includeHeaders\\` list. The \\`includeHeaders\\` can be defined either ` + `on a request level by adding the \\`transferCache\\` parameter, or on an application ` + `level by adding the \\`httpCacheTransfer.includeHeaders\\` argument to the ` + `\\`provideClientHydration()\\` call. `));\n        }\n        // invoking the original method\n        return value.apply(target, [headerName]);\n      };\n    }\n  });\n}\nfunction mapRequestOriginUrl(url, originMap) {\n  const origin = new URL(url, 'resolve://').origin;\n  const mappedOrigin = originMap[origin];\n  if (!mappedOrigin) {\n    return url;\n  }\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    verifyMappedOrigin(mappedOrigin);\n  }\n  return url.replace(origin, mappedOrigin);\n}\nfunction verifyMappedOrigin(url) {\n  if (new URL(url, 'resolve://').pathname !== '/') {\n    throw new _RuntimeError(2804 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_CONTAINS_PATH */, 'Angular detected a URL with a path segment in the value provided for the ' + `\\`HTTP_TRANSFER_CACHE_ORIGIN_MAP\\` token: ${url}. The map should only contain origins ` + 'without any other segments.');\n  }\n}\nexport { HTTP_TRANSFER_CACHE_ORIGIN_MAP, HttpClient, HttpErrorResponse, HttpEventType, HttpHeaders, HttpParams, HttpRequest, HttpResponse, httpResource, HTTP_ROOT_INTERCEPTOR_FNS as ɵHTTP_ROOT_INTERCEPTOR_FNS, withHttpTransferCache as ɵwithHttpTransferCache };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAM,cAAN,MAAkB;AAAC;AAWnB,IAAM,cAAN,MAAkB;AAAC;AASnB,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA,EAIhB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAI1B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA;AAAA,EAEb,YAAY,SAAS;AACnB,QAAI,CAAC,SAAS;AACZ,WAAK,UAAU,oBAAI,IAAI;AAAA,IACzB,WAAW,OAAO,YAAY,UAAU;AACtC,WAAK,WAAW,MAAM;AACpB,aAAK,UAAU,oBAAI,IAAI;AACvB,gBAAQ,MAAM,IAAI,EAAE,QAAQ,UAAQ;AAClC,gBAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,cAAI,QAAQ,GAAG;AACb,kBAAM,OAAO,KAAK,MAAM,GAAG,KAAK;AAChC,kBAAM,QAAQ,KAAK,MAAM,QAAQ,CAAC,EAAE,KAAK;AACzC,iBAAK,eAAe,MAAM,KAAK;AAAA,UACjC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,WAAW,OAAO,YAAY,eAAe,mBAAmB,SAAS;AACvE,WAAK,UAAU,oBAAI,IAAI;AACvB,cAAQ,QAAQ,CAAC,OAAO,SAAS;AAC/B,aAAK,eAAe,MAAM,KAAK;AAAA,MACjC,CAAC;AAAA,IACH,OAAO;AACL,WAAK,WAAW,MAAM;AACpB,YAAI,OAAO,cAAc,eAAe,WAAW;AACjD,6BAAmB,OAAO;AAAA,QAC5B;AACA,aAAK,UAAU,oBAAI,IAAI;AACvB,eAAO,QAAQ,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAM,MAAM,MAAM;AAClD,eAAK,iBAAiB,MAAM,MAAM;AAAA,QACpC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM;AACR,SAAK,KAAK;AACV,WAAO,KAAK,QAAQ,IAAI,KAAK,YAAY,CAAC;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM;AACR,SAAK,KAAK;AACV,UAAM,SAAS,KAAK,QAAQ,IAAI,KAAK,YAAY,CAAC;AAClD,WAAO,UAAU,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACL,SAAK,KAAK;AACV,WAAO,MAAM,KAAK,KAAK,gBAAgB,OAAO,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,MAAM;AACX,SAAK,KAAK;AACV,WAAO,KAAK,QAAQ,IAAI,KAAK,YAAY,CAAC,KAAK;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,MAAM,OAAO;AAClB,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IAAI,MAAM,OAAO;AACf,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,MAAM,OAAO;AAClB,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB,MAAM,QAAQ;AACnC,QAAI,CAAC,KAAK,gBAAgB,IAAI,MAAM,GAAG;AACrC,WAAK,gBAAgB,IAAI,QAAQ,IAAI;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,CAAC,CAAC,KAAK,UAAU;AACnB,UAAI,KAAK,oBAAoB,cAAa;AACxC,aAAK,SAAS,KAAK,QAAQ;AAAA,MAC7B,OAAO;AACL,aAAK,SAAS;AAAA,MAChB;AACA,WAAK,WAAW;AAChB,UAAI,CAAC,CAAC,KAAK,YAAY;AACrB,aAAK,WAAW,QAAQ,YAAU,KAAK,YAAY,MAAM,CAAC;AAC1D,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,OAAO;AACd,UAAM,KAAK;AACX,UAAM,KAAK,MAAM,QAAQ,KAAK,CAAC,EAAE,QAAQ,SAAO;AAC9C,WAAK,QAAQ,IAAI,KAAK,MAAM,QAAQ,IAAI,GAAG,CAAC;AAC5C,WAAK,gBAAgB,IAAI,KAAK,MAAM,gBAAgB,IAAI,GAAG,CAAC;AAAA,IAC9D,CAAC;AAAA,EACH;AAAA,EACA,MAAM,QAAQ;AACZ,UAAM,QAAQ,IAAI,aAAY;AAC9B,UAAM,WAAW,CAAC,CAAC,KAAK,YAAY,KAAK,oBAAoB,eAAc,KAAK,WAAW;AAC3F,UAAM,cAAc,KAAK,cAAc,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;AAC1D,WAAO;AAAA,EACT;AAAA,EACA,YAAY,QAAQ;AAClB,UAAM,MAAM,OAAO,KAAK,YAAY;AACpC,YAAQ,OAAO,IAAI;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AACH,YAAI,QAAQ,OAAO;AACnB,YAAI,OAAO,UAAU,UAAU;AAC7B,kBAAQ,CAAC,KAAK;AAAA,QAChB;AACA,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,aAAK,uBAAuB,OAAO,MAAM,GAAG;AAC5C,cAAM,QAAQ,OAAO,OAAO,MAAM,KAAK,QAAQ,IAAI,GAAG,IAAI,WAAc,CAAC;AACzE,aAAK,KAAK,GAAG,KAAK;AAClB,aAAK,QAAQ,IAAI,KAAK,IAAI;AAC1B;AAAA,MACF,KAAK;AACH,cAAM,WAAW,OAAO;AACxB,YAAI,CAAC,UAAU;AACb,eAAK,QAAQ,OAAO,GAAG;AACvB,eAAK,gBAAgB,OAAO,GAAG;AAAA,QACjC,OAAO;AACL,cAAI,WAAW,KAAK,QAAQ,IAAI,GAAG;AACnC,cAAI,CAAC,UAAU;AACb;AAAA,UACF;AACA,qBAAW,SAAS,OAAO,CAAAA,WAAS,SAAS,QAAQA,MAAK,MAAM,EAAE;AAClE,cAAI,SAAS,WAAW,GAAG;AACzB,iBAAK,QAAQ,OAAO,GAAG;AACvB,iBAAK,gBAAgB,OAAO,GAAG;AAAA,UACjC,OAAO;AACL,iBAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,UAChC;AAAA,QACF;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe,MAAM,OAAO;AAC1B,UAAM,MAAM,KAAK,YAAY;AAC7B,SAAK,uBAAuB,MAAM,GAAG;AACrC,QAAI,KAAK,QAAQ,IAAI,GAAG,GAAG;AACzB,WAAK,QAAQ,IAAI,GAAG,EAAE,KAAK,KAAK;AAAA,IAClC,OAAO;AACL,WAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,iBAAiB,MAAM,QAAQ;AAC7B,UAAM,gBAAgB,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG,IAAI,WAAS,MAAM,SAAS,CAAC;AAC9F,UAAM,MAAM,KAAK,YAAY;AAC7B,SAAK,QAAQ,IAAI,KAAK,YAAY;AAClC,SAAK,uBAAuB,MAAM,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,IAAI;AACV,SAAK,KAAK;AACV,UAAM,KAAK,KAAK,gBAAgB,KAAK,CAAC,EAAE,QAAQ,SAAO,GAAG,KAAK,gBAAgB,IAAI,GAAG,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,CAAC;AAAA,EACjH;AACF;AAMA,SAAS,mBAAmB,SAAS;AACnC,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,OAAO,GAAG;AAClD,QAAI,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,aAAa,CAAC,MAAM,QAAQ,KAAK,GAAG;AACtF,YAAM,IAAI,MAAM,6BAA6B,GAAG,mFAAwF,KAAK,KAAK;AAAA,IACpJ;AAAA,EACF;AACF;AAYA,IAAM,uBAAN,MAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,UAAU,KAAK;AACb,WAAO,iBAAiB,GAAG;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,WAAO,iBAAiB,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,KAAK;AACb,WAAO,mBAAmB,GAAG;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,WAAO,mBAAmB,KAAK;AAAA,EACjC;AACF;AACA,SAAS,YAAY,WAAW,OAAO;AACrC,QAAMC,OAAM,oBAAI,IAAI;AACpB,MAAI,UAAU,SAAS,GAAG;AAIxB,UAAM,SAAS,UAAU,QAAQ,OAAO,EAAE,EAAE,MAAM,GAAG;AACrD,WAAO,QAAQ,WAAS;AACtB,YAAM,QAAQ,MAAM,QAAQ,GAAG;AAC/B,YAAM,CAAC,KAAK,GAAG,IAAI,SAAS,KAAK,CAAC,MAAM,UAAU,KAAK,GAAG,EAAE,IAAI,CAAC,MAAM,UAAU,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,MAAM,YAAY,MAAM,MAAM,QAAQ,CAAC,CAAC,CAAC;AAClJ,YAAM,OAAOA,KAAI,IAAI,GAAG,KAAK,CAAC;AAC9B,WAAK,KAAK,GAAG;AACb,MAAAA,KAAI,IAAI,KAAK,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AACA,SAAOA;AACT;AAIA,IAAM,0BAA0B;AAChC,IAAM,iCAAiC;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AACA,SAAS,iBAAiB,GAAG;AAC3B,SAAO,mBAAmB,CAAC,EAAE,QAAQ,yBAAyB,CAAC,GAAG,MAAM,+BAA+B,CAAC,KAAK,CAAC;AAChH;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,GAAG,KAAK;AACjB;AASA,IAAM,aAAN,MAAM,YAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY,UAAU,CAAC,GAAG;AACxB,SAAK,UAAU,QAAQ,WAAW,IAAI,qBAAqB;AAC3D,QAAI,QAAQ,YAAY;AACtB,UAAI,QAAQ,YAAY;AACtB,cAAM,IAAI,aAAc,MAA6E,aAAa,gDAAgD;AAAA,MACpK;AACA,WAAK,MAAM,YAAY,QAAQ,YAAY,KAAK,OAAO;AAAA,IACzD,WAAW,CAAC,CAAC,QAAQ,YAAY;AAC/B,WAAK,MAAM,oBAAI,IAAI;AACnB,aAAO,KAAK,QAAQ,UAAU,EAAE,QAAQ,SAAO;AAC7C,cAAM,QAAQ,QAAQ,WAAW,GAAG;AAEpC,cAAM,SAAS,MAAM,QAAQ,KAAK,IAAI,MAAM,IAAI,aAAa,IAAI,CAAC,cAAc,KAAK,CAAC;AACtF,aAAK,IAAI,IAAI,KAAK,MAAM;AAAA,MAC1B,CAAC;AAAA,IACH,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,SAAK,KAAK;AACV,WAAO,KAAK,IAAI,IAAI,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,SAAK,KAAK;AACV,UAAM,MAAM,KAAK,IAAI,IAAI,KAAK;AAC9B,WAAO,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO;AACZ,SAAK,KAAK;AACV,WAAO,KAAK,IAAI,IAAI,KAAK,KAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,SAAK,KAAK;AACV,WAAO,MAAM,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,OAAO;AACnB,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,QAAQ;AAChB,UAAM,UAAU,CAAC;AACjB,WAAO,KAAK,MAAM,EAAE,QAAQ,WAAS;AACnC,YAAM,QAAQ,OAAO,KAAK;AAC1B,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,cAAM,QAAQ,YAAU;AACtB,kBAAQ,KAAK;AAAA,YACX;AAAA,YACA,OAAO;AAAA,YACP,IAAI;AAAA,UACN,CAAC;AAAA,QACH,CAAC;AAAA,MACH,OAAO;AACL,gBAAQ,KAAK;AAAA,UACX;AAAA,UACA;AAAA,UACA,IAAI;AAAA,QACN,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO,KAAK,MAAM,OAAO;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO,OAAO;AAChB,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO,OAAO;AACnB,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,SAAK,KAAK;AACV,WAAO,KAAK,KAAK,EAAE,IAAI,SAAO;AAC5B,YAAM,OAAO,KAAK,QAAQ,UAAU,GAAG;AAIvC,aAAO,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,WAAS,OAAO,MAAM,KAAK,QAAQ,YAAY,KAAK,CAAC,EAAE,KAAK,GAAG;AAAA,IAC9F,CAAC,EAGA,OAAO,WAAS,UAAU,EAAE,EAAE,KAAK,GAAG;AAAA,EACzC;AAAA,EACA,MAAM,QAAQ;AACZ,UAAM,QAAQ,IAAI,YAAW;AAAA,MAC3B,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,UAAM,YAAY,KAAK,aAAa;AACpC,UAAM,WAAW,KAAK,WAAW,CAAC,GAAG,OAAO,MAAM;AAClD,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,oBAAI,IAAI;AAAA,IACrB;AACA,QAAI,KAAK,cAAc,MAAM;AAC3B,WAAK,UAAU,KAAK;AACpB,WAAK,UAAU,KAAK,EAAE,QAAQ,SAAO,KAAK,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,GAAG,CAAC,CAAC;AACnF,WAAK,QAAQ,QAAQ,YAAU;AAC7B,gBAAQ,OAAO,IAAI;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AACH,kBAAM,QAAQ,OAAO,OAAO,MAAM,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,WAAc,CAAC;AAC9E,iBAAK,KAAK,cAAc,OAAO,KAAK,CAAC;AACrC,iBAAK,IAAI,IAAI,OAAO,OAAO,IAAI;AAC/B;AAAA,UACF,KAAK;AACH,gBAAI,OAAO,UAAU,QAAW;AAC9B,kBAAIC,QAAO,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC;AAC1C,oBAAM,MAAMA,MAAK,QAAQ,cAAc,OAAO,KAAK,CAAC;AACpD,kBAAI,QAAQ,IAAI;AACd,gBAAAA,MAAK,OAAO,KAAK,CAAC;AAAA,cACpB;AACA,kBAAIA,MAAK,SAAS,GAAG;AACnB,qBAAK,IAAI,IAAI,OAAO,OAAOA,KAAI;AAAA,cACjC,OAAO;AACL,qBAAK,IAAI,OAAO,OAAO,KAAK;AAAA,cAC9B;AAAA,YACF,OAAO;AACL,mBAAK,IAAI,OAAO,OAAO,KAAK;AAC5B;AAAA,YACF;AAAA,QACJ;AAAA,MACF,CAAC;AACD,WAAK,YAAY,KAAK,UAAU;AAAA,IAClC;AAAA,EACF;AACF;AAOA,IAAM,mBAAN,MAAuB;AAAA,EACrB;AAAA,EACA,YAAY,cAAc;AACxB,SAAK,eAAe;AAAA,EACtB;AACF;AAkCA,IAAM,cAAN,MAAkB;AAAA,EAChB,MAAM,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASd,IAAI,OAAO,OAAO;AAChB,SAAK,IAAI,IAAI,OAAO,KAAK;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO;AACT,QAAI,CAAC,KAAK,IAAI,IAAI,KAAK,GAAG;AACxB,WAAK,IAAI,IAAI,OAAO,MAAM,aAAa,CAAC;AAAA,IAC1C;AACA,WAAO,KAAK,IAAI,IAAI,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO;AACZ,SAAK,IAAI,OAAO,KAAK;AACrB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO;AACT,WAAO,KAAK,IAAI,IAAI,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,WAAO,KAAK,IAAI,KAAK;AAAA,EACvB;AACF;AAKA,SAAS,cAAc,QAAQ;AAC7B,UAAQ,QAAQ;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAMA,SAAS,cAAc,OAAO;AAC5B,SAAO,OAAO,gBAAgB,eAAe,iBAAiB;AAChE;AAMA,SAAS,OAAO,OAAO;AACrB,SAAO,OAAO,SAAS,eAAe,iBAAiB;AACzD;AAMA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,aAAa,eAAe,iBAAiB;AAC7D;AAMA,SAAS,kBAAkB,OAAO;AAChC,SAAO,OAAO,oBAAoB,eAAe,iBAAiB;AACpE;AAMA,IAAM,sBAAsB;AAK5B,IAAM,gBAAgB;AAMtB,IAAM,uBAAuB;AAM7B,IAAM,oBAAoB;AAK1B,IAAM,oBAAoB;AAO1B,IAAM,sBAAsB,GAAG,iBAAiB,KAAK,iBAAiB;AAWtE,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAIjB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,eAAe;AAAA;AAAA;AAAA;AAAA,EAIf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA,YAAY,QAAQ,KAAK,OAAO,QAAQ;AACtC,SAAK,MAAM;AACX,SAAK,SAAS,OAAO,YAAY;AAGjC,QAAI;AAGJ,QAAI,cAAc,KAAK,MAAM,KAAK,CAAC,CAAC,QAAQ;AAE1C,WAAK,OAAO,UAAU,SAAY,QAAQ;AAC1C,gBAAU;AAAA,IACZ,OAAO;AAEL,gBAAU;AAAA,IACZ;AAEA,QAAI,SAAS;AAEX,WAAK,iBAAiB,CAAC,CAAC,QAAQ;AAChC,WAAK,kBAAkB,CAAC,CAAC,QAAQ;AAEjC,UAAI,CAAC,CAAC,QAAQ,cAAc;AAC1B,aAAK,eAAe,QAAQ;AAAA,MAC9B;AAEA,UAAI,CAAC,CAAC,QAAQ,SAAS;AACrB,aAAK,UAAU,QAAQ;AAAA,MACzB;AACA,UAAI,CAAC,CAAC,QAAQ,SAAS;AACrB,aAAK,UAAU,QAAQ;AAAA,MACzB;AACA,UAAI,CAAC,CAAC,QAAQ,QAAQ;AACpB,aAAK,SAAS,QAAQ;AAAA,MACxB;AAEA,WAAK,gBAAgB,QAAQ;AAAA,IAC/B;AAEA,SAAK,YAAY,IAAI,YAAY;AAEjC,SAAK,YAAY,IAAI,YAAY;AAEjC,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS,IAAI,WAAW;AAC7B,WAAK,gBAAgB;AAAA,IACvB,OAAO;AAEL,YAAM,SAAS,KAAK,OAAO,SAAS;AACpC,UAAI,OAAO,WAAW,GAAG;AAEvB,aAAK,gBAAgB;AAAA,MACvB,OAAO;AAEL,cAAM,OAAO,IAAI,QAAQ,GAAG;AAQ5B,cAAM,MAAM,SAAS,KAAK,MAAM,OAAO,IAAI,SAAS,IAAI,MAAM;AAC9D,aAAK,gBAAgB,MAAM,MAAM;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AAEd,QAAI,KAAK,SAAS,MAAM;AACtB,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,KAAK,SAAS,YAAY,cAAc,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,kBAAkB,KAAK,IAAI,GAAG;AAC3I,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,KAAK,gBAAgB,YAAY;AACnC,aAAO,KAAK,KAAK,SAAS;AAAA,IAC5B;AAEA,QAAI,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,SAAS,aAAa,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC/F,aAAO,KAAK,UAAU,KAAK,IAAI;AAAA,IACjC;AAEA,WAAO,KAAK,KAAK,SAAS;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,0BAA0B;AAExB,QAAI,KAAK,SAAS,MAAM;AACtB,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,KAAK,IAAI,GAAG;AACzB,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,KAAK,IAAI,GAAG;AACrB,aAAO,KAAK,KAAK,QAAQ;AAAA,IAC3B;AAEA,QAAI,cAAc,KAAK,IAAI,GAAG;AAC5B,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,KAAK,SAAS,UAAU;AACjC,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,gBAAgB,YAAY;AACnC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,SAAS,WAAW;AACpG,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,SAAS,CAAC,GAAG;AAGjB,UAAM,SAAS,OAAO,UAAU,KAAK;AACrC,UAAM,MAAM,OAAO,OAAO,KAAK;AAC/B,UAAM,eAAe,OAAO,gBAAgB,KAAK;AAGjD,UAAM,gBAAgB,OAAO,iBAAiB,KAAK;AAKnD,UAAM,OAAO,OAAO,SAAS,SAAY,OAAO,OAAO,KAAK;AAG5D,UAAM,kBAAkB,OAAO,mBAAmB,KAAK;AACvD,UAAM,iBAAiB,OAAO,kBAAkB,KAAK;AAGrD,QAAI,UAAU,OAAO,WAAW,KAAK;AACrC,QAAI,SAAS,OAAO,UAAU,KAAK;AAEnC,UAAM,UAAU,OAAO,WAAW,KAAK;AAEvC,QAAI,OAAO,eAAe,QAAW;AAEnC,gBAAU,OAAO,KAAK,OAAO,UAAU,EAAE,OAAO,CAACC,UAAS,SAASA,SAAQ,IAAI,MAAM,OAAO,WAAW,IAAI,CAAC,GAAG,OAAO;AAAA,IACxH;AAEA,QAAI,OAAO,WAAW;AAEpB,eAAS,OAAO,KAAK,OAAO,SAAS,EAAE,OAAO,CAACC,SAAQ,UAAUA,QAAO,IAAI,OAAO,OAAO,UAAU,KAAK,CAAC,GAAG,MAAM;AAAA,IACrH;AAEA,WAAO,IAAI,aAAY,QAAQ,KAAK,MAAM;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAOA,IAAI;AAAA,CACH,SAAUC,gBAAe;AAIxB,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAM3C,EAAAA,eAAcA,eAAc,gBAAgB,IAAI,CAAC,IAAI;AAIrD,EAAAA,eAAcA,eAAc,gBAAgB,IAAI,CAAC,IAAI;AAIrD,EAAAA,eAAcA,eAAc,kBAAkB,IAAI,CAAC,IAAI;AAIvD,EAAAA,eAAcA,eAAc,UAAU,IAAI,CAAC,IAAI;AAI/C,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAC7C,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAMxC,IAAM,mBAAN,MAAuB;AAAA;AAAA;AAAA;AAAA,EAIrB;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM,gBAAgB,KAAK,oBAAoB,MAAM;AAG/D,SAAK,UAAU,KAAK,WAAW,IAAI,YAAY;AAC/C,SAAK,SAAS,KAAK,WAAW,SAAY,KAAK,SAAS;AACxD,SAAK,aAAa,KAAK,cAAc;AACrC,SAAK,MAAM,KAAK,OAAO;AAEvB,SAAK,KAAK,KAAK,UAAU,OAAO,KAAK,SAAS;AAAA,EAChD;AACF;AAUA,IAAM,qBAAN,MAAM,4BAA2B,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAIhD,YAAY,OAAO,CAAC,GAAG;AACrB,UAAM,IAAI;AAAA,EACZ;AAAA,EACA,OAAO,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,MAAM,SAAS,CAAC,GAAG;AAGjB,WAAO,IAAI,oBAAmB;AAAA,MAC5B,SAAS,OAAO,WAAW,KAAK;AAAA,MAChC,QAAQ,OAAO,WAAW,SAAY,OAAO,SAAS,KAAK;AAAA,MAC3D,YAAY,OAAO,cAAc,KAAK;AAAA,MACtC,KAAK,OAAO,OAAO,KAAK,OAAO;AAAA,IACjC,CAAC;AAAA,EACH;AACF;AAUA,IAAM,eAAN,MAAM,sBAAqB,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAI1C;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO,CAAC,GAAG;AACrB,UAAM,IAAI;AACV,SAAK,OAAO,KAAK,SAAS,SAAY,KAAK,OAAO;AAAA,EACpD;AAAA,EACA,OAAO,cAAc;AAAA,EACrB,MAAM,SAAS,CAAC,GAAG;AACjB,WAAO,IAAI,cAAa;AAAA,MACtB,MAAM,OAAO,SAAS,SAAY,OAAO,OAAO,KAAK;AAAA,MACrD,SAAS,OAAO,WAAW,KAAK;AAAA,MAChC,QAAQ,OAAO,WAAW,SAAY,OAAO,SAAS,KAAK;AAAA,MAC3D,YAAY,OAAO,cAAc,KAAK;AAAA,MACtC,KAAK,OAAO,OAAO,KAAK,OAAO;AAAA,IACjC,CAAC;AAAA,EACH;AACF;AAcA,IAAM,oBAAN,cAAgC,iBAAiB;AAAA,EAC/C,OAAO;AAAA,EACP;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK;AAAA,EACL,YAAY,MAAM;AAEhB,UAAM,MAAM,GAAG,eAAe;AAI9B,QAAI,KAAK,UAAU,OAAO,KAAK,SAAS,KAAK;AAC3C,WAAK,UAAU,mCAAmC,KAAK,OAAO,eAAe;AAAA,IAC/E,OAAO;AACL,WAAK,UAAU,6BAA6B,KAAK,OAAO,eAAe,KAAK,KAAK,MAAM,IAAI,KAAK,UAAU;AAAA,IAC5G;AACA,SAAK,QAAQ,KAAK,SAAS;AAAA,EAC7B;AACF;AAKA,IAAM,sBAAsB;AAC5B,IAAM,8BAA8B;AAMpC,IAAI;AAAA,CACH,SAAUC,iBAAgB;AACzB,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,oBAAoB,IAAI,GAAG,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,GAAG,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,GAAG,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,IAAI,IAAI,GAAG,IAAI;AAC7C,EAAAA,gBAAeA,gBAAe,SAAS,IAAI,GAAG,IAAI;AAClD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,6BAA6B,IAAI,GAAG,IAAI;AACtE,EAAAA,gBAAeA,gBAAe,WAAW,IAAI,GAAG,IAAI;AACpD,EAAAA,gBAAeA,gBAAe,cAAc,IAAI,GAAG,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,GAAG,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,aAAa,IAAI,GAAG,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,GAAG,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,GAAG,IAAI;AAC3D,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,GAAG,IAAI;AAChD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,aAAa,IAAI,GAAG,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,GAAG,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,mBAAmB,IAAI,GAAG,IAAI;AAC5D,EAAAA,gBAAeA,gBAAe,mBAAmB,IAAI,GAAG,IAAI;AAC5D,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,GAAG,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,cAAc,IAAI,GAAG,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,WAAW,IAAI,GAAG,IAAI;AACpD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,GAAG,IAAI;AAC3D,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,GAAG,IAAI;AACxD,EAAAA,gBAAeA,gBAAe,6BAA6B,IAAI,GAAG,IAAI;AACtE,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,GAAG,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,GAAG,IAAI;AAC/C,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,GAAG,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,oBAAoB,IAAI,GAAG,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,GAAG,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,sBAAsB,IAAI,GAAG,IAAI;AAC/D,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,GAAG,IAAI;AAC9D,EAAAA,gBAAeA,gBAAe,mBAAmB,IAAI,GAAG,IAAI;AAC5D,EAAAA,gBAAeA,gBAAe,WAAW,IAAI,GAAG,IAAI;AACpD,EAAAA,gBAAeA,gBAAe,oBAAoB,IAAI,GAAG,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,GAAG,IAAI;AAC9D,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,GAAG,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,GAAG,IAAI;AAC3D,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,sBAAsB,IAAI,GAAG,IAAI;AAC/D,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,6BAA6B,IAAI,GAAG,IAAI;AACtE,EAAAA,gBAAeA,gBAAe,4BAA4B,IAAI,GAAG,IAAI;AACrE,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,GAAG,IAAI;AAC9D,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,GAAG,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,GAAG,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,oBAAoB,IAAI,GAAG,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,GAAG,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,yBAAyB,IAAI,GAAG,IAAI;AAClE,EAAAA,gBAAeA,gBAAe,uBAAuB,IAAI,GAAG,IAAI;AAChE,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,GAAG,IAAI;AAC9D,EAAAA,gBAAeA,gBAAe,cAAc,IAAI,GAAG,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,aAAa,IAAI,GAAG,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,+BAA+B,IAAI,GAAG,IAAI;AAC1E,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAY1C,SAAS,QAAQ,SAAS,MAAM;AAC9B,SAAO;AAAA,IACL;AAAA,IACA,SAAS,QAAQ;AAAA,IACjB,SAAS,QAAQ;AAAA,IACjB,SAAS,QAAQ;AAAA,IACjB,QAAQ,QAAQ;AAAA,IAChB,gBAAgB,QAAQ;AAAA,IACxB,cAAc,QAAQ;AAAA,IACtB,iBAAiB,QAAQ;AAAA,IACzB,eAAe,QAAQ;AAAA,EACzB;AACF;AAsDA,IAAM,cAAN,MAAM,YAAW;AAAA,EACf;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BA,QAAQ,OAAO,KAAK,UAAU,CAAC,GAAG;AAChC,QAAI;AAEJ,QAAI,iBAAiB,aAAa;AAGhC,YAAM;AAAA,IACR,OAAO;AAKL,UAAI,UAAU;AACd,UAAI,QAAQ,mBAAmB,aAAa;AAC1C,kBAAU,QAAQ;AAAA,MACpB,OAAO;AACL,kBAAU,IAAI,YAAY,QAAQ,OAAO;AAAA,MAC3C;AAEA,UAAI,SAAS;AACb,UAAI,CAAC,CAAC,QAAQ,QAAQ;AACpB,YAAI,QAAQ,kBAAkB,YAAY;AACxC,mBAAS,QAAQ;AAAA,QACnB,OAAO;AACL,mBAAS,IAAI,WAAW;AAAA,YACtB,YAAY,QAAQ;AAAA,UACtB,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,IAAI,YAAY,OAAO,KAAK,QAAQ,SAAS,SAAY,QAAQ,OAAO,MAAM;AAAA,QAClF;AAAA,QACA,SAAS,QAAQ;AAAA,QACjB;AAAA,QACA,gBAAgB,QAAQ;AAAA;AAAA,QAExB,cAAc,QAAQ,gBAAgB;AAAA,QACtC,iBAAiB,QAAQ;AAAA,QACzB,eAAe,QAAQ;AAAA,MACzB,CAAC;AAAA,IACH;AAKA,UAAM,UAAU,GAAG,GAAG,EAAE,KAAK,UAAU,CAAAC,SAAO,KAAK,QAAQ,OAAOA,IAAG,CAAC,CAAC;AAIvE,QAAI,iBAAiB,eAAe,QAAQ,YAAY,UAAU;AAChE,aAAO;AAAA,IACT;AAIA,UAAM,OAAO,QAAQ,KAAK,OAAO,WAAS,iBAAiB,YAAY,CAAC;AAExE,YAAQ,QAAQ,WAAW,QAAQ;AAAA,MACjC,KAAK;AAMH,gBAAQ,IAAI,cAAc;AAAA,UACxB,KAAK;AACH,mBAAO,KAAK,KAAK,IAAI,SAAO;AAE1B,kBAAI,IAAI,SAAS,QAAQ,EAAE,IAAI,gBAAgB,cAAc;AAC3D,sBAAM,IAAI,aAAc,MAA6D,aAAa,iCAAiC;AAAA,cACrI;AACA,qBAAO,IAAI;AAAA,YACb,CAAC,CAAC;AAAA,UACJ,KAAK;AACH,mBAAO,KAAK,KAAK,IAAI,SAAO;AAE1B,kBAAI,IAAI,SAAS,QAAQ,EAAE,IAAI,gBAAgB,OAAO;AACpD,sBAAM,IAAI,aAAc,MAAoD,aAAa,yBAAyB;AAAA,cACpH;AACA,qBAAO,IAAI;AAAA,YACb,CAAC,CAAC;AAAA,UACJ,KAAK;AACH,mBAAO,KAAK,KAAK,IAAI,SAAO;AAE1B,kBAAI,IAAI,SAAS,QAAQ,OAAO,IAAI,SAAS,UAAU;AACrD,sBAAM,IAAI,aAAc,MAAsD,aAAa,2BAA2B;AAAA,cACxH;AACA,qBAAO,IAAI;AAAA,YACb,CAAC,CAAC;AAAA,UACJ,KAAK;AAAA,UACL;AAEE,mBAAO,KAAK,KAAK,IAAI,SAAO,IAAI,IAAI,CAAC;AAAA,QACzC;AAAA,MACF,KAAK;AAEH,eAAO;AAAA,MACT;AAEE,cAAM,IAAI,aAAc,MAAoD,aAAa,uCAAuC,QAAQ,OAAO,GAAG;AAAA,IACtJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,KAAK,UAAU,CAAC,GAAG;AACxB,WAAO,KAAK,QAAQ,UAAU,KAAK,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,KAAK,UAAU,CAAC,GAAG;AACrB,WAAO,KAAK,QAAQ,OAAO,KAAK,OAAO;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,KAAK,UAAU,CAAC,GAAG;AACtB,WAAO,KAAK,QAAQ,QAAQ,KAAK,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,KAAK,eAAe;AACxB,WAAO,KAAK,QAAQ,SAAS,KAAK;AAAA,MAChC,QAAQ,IAAI,WAAW,EAAE,OAAO,eAAe,gBAAgB;AAAA,MAC/D,SAAS;AAAA,MACT,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,KAAK,UAAU,CAAC,GAAG;AACzB,WAAO,KAAK,QAAQ,WAAW,KAAK,OAAO;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,KAAK,MAAM,UAAU,CAAC,GAAG;AAC7B,WAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,SAAS,IAAI,CAAC;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,KAAK,MAAM,UAAU,CAAC,GAAG;AAC5B,WAAO,KAAK,QAAQ,QAAQ,KAAK,QAAQ,SAAS,IAAI,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,KAAK,MAAM,UAAU,CAAC,GAAG;AAC3B,WAAO,KAAK,QAAQ,OAAO,KAAK,QAAQ,SAAS,IAAI,CAAC;AAAA,EACxD;AAQF;AAPE,cAhOI,aAgOG,QAAO,SAAS,mBAAmB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,aAAe,SAAS,WAAW,CAAC;AACvE;AACA,cAnOI,aAmOG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,YAAW;AACtB,CAAC;AAtOH,IAAM,aAAN;AAAA,CAwOC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,gBAAgB;AAKtB,SAAS,iBAAiB,UAAU;AAClC,MAAI,SAAS,KAAK;AAChB,WAAO,SAAS;AAAA,EAClB;AAEA,QAAM,cAAc,qBAAqB,kBAAkB;AAC3D,SAAO,SAAS,QAAQ,IAAI,WAAW;AACzC;AAKA,IAAM,gBAAgB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,kBAAkB,EAAE;AAY7G,IAAM,gBAAN,MAAM,cAAa;AAAA;AAAA;AAAA;AAAA,EAIjB,YAAY,OAAO,cAAc;AAAA,IAC/B,UAAU;AAAA,EACZ,CAAC,GAAG,UAAU,IAAI,SAAS,WAAW,MAAM,GAAG,IAAI;AAAA,EACnD,SAAS,OAAO,MAAM;AAAA,EACtB,OAAO,SAAS;AACd,WAAO,IAAI,WAAW,cAAY;AAChC,YAAM,UAAU,IAAI,gBAAgB;AACpC,WAAK,UAAU,SAAS,QAAQ,QAAQ,QAAQ,EAAE,KAAK,MAAM,WAAS,SAAS,MAAM,IAAI,kBAAkB;AAAA,QACzG;AAAA,MACF,CAAC,CAAC,CAAC;AACH,aAAO,MAAM,QAAQ,MAAM;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACM,UAAU,SAASC,SAAQ,UAAU;AAAA;AACzC,YAAM,OAAO,KAAK,kBAAkB,OAAO;AAC3C,UAAI;AACJ,UAAI;AAIF,cAAM,eAAe,KAAK,OAAO,kBAAkB,MAAM,KAAK,UAAU,QAAQ,eAAe;AAAA,UAC7F,QAAAA;AAAA,WACG,KACJ,CAAC;AAIF,oDAA4C,YAAY;AAExD,iBAAS,KAAK;AAAA,UACZ,MAAM,cAAc;AAAA,QACtB,CAAC;AACD,mBAAW,MAAM;AAAA,MACnB,SAAS,OAAO;AACd,iBAAS,MAAM,IAAI,kBAAkB;AAAA,UACnC;AAAA,UACA,QAAQ,MAAM,UAAU;AAAA,UACxB,YAAY,MAAM;AAAA,UAClB,KAAK,QAAQ;AAAA,UACb,SAAS,MAAM;AAAA,QACjB,CAAC,CAAC;AACF;AAAA,MACF;AACA,YAAM,UAAU,IAAI,YAAY,SAAS,OAAO;AAChD,YAAM,aAAa,SAAS;AAC5B,YAAM,MAAM,iBAAiB,QAAQ,KAAK,QAAQ;AAClD,UAAI,SAAS,SAAS;AACtB,UAAI,OAAO;AACX,UAAI,QAAQ,gBAAgB;AAC1B,iBAAS,KAAK,IAAI,mBAAmB;AAAA,UACnC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AACA,UAAI,SAAS,MAAM;AAEjB,cAAM,gBAAgB,SAAS,QAAQ,IAAI,gBAAgB;AAC3D,cAAM,SAAS,CAAC;AAChB,cAAM,SAAS,SAAS,KAAK,UAAU;AACvC,YAAI,iBAAiB;AACrB,YAAI;AACJ,YAAI;AAGJ,cAAM,UAAU,OAAO,SAAS,eAAe,KAAK;AAIpD,cAAM,KAAK,OAAO,kBAAkB,MAAY;AAC9C,iBAAO,MAAM;AACX,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI,MAAM,OAAO,KAAK;AACtB,gBAAI,MAAM;AACR;AAAA,YACF;AACA,mBAAO,KAAK,KAAK;AACjB,8BAAkB,MAAM;AACxB,gBAAI,QAAQ,gBAAgB;AAC1B,4BAAc,QAAQ,iBAAiB,UAAU,eAAe,OAAO,YAAY,IAAI,YAAY,GAAG,OAAO,OAAO;AAAA,gBAClH,QAAQ;AAAA,cACV,CAAC,IAAI;AACL,oBAAM,iBAAiB,MAAM,SAAS,KAAK;AAAA,gBACzC,MAAM,cAAc;AAAA,gBACpB,OAAO,gBAAgB,CAAC,gBAAgB;AAAA,gBACxC,QAAQ;AAAA,gBACR;AAAA,cACF,CAAC;AACD,wBAAU,QAAQ,IAAI,cAAc,IAAI,eAAe;AAAA,YACzD;AAAA,UACF;AAAA,QACF,EAAC;AAED,cAAM,YAAY,KAAK,aAAa,QAAQ,cAAc;AAC1D,YAAI;AACF,gBAAM,cAAc,SAAS,QAAQ,IAAI,mBAAmB,KAAK;AACjE,iBAAO,KAAK,UAAU,SAAS,WAAW,WAAW;AAAA,QACvD,SAAS,OAAO;AAEd,mBAAS,MAAM,IAAI,kBAAkB;AAAA,YACnC;AAAA,YACA,SAAS,IAAI,YAAY,SAAS,OAAO;AAAA,YACzC,QAAQ,SAAS;AAAA,YACjB,YAAY,SAAS;AAAA,YACrB,KAAK,iBAAiB,QAAQ,KAAK,QAAQ;AAAA,UAC7C,CAAC,CAAC;AACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,WAAW,GAAG;AAChB,iBAAS,OAAO,sBAAsB;AAAA,MACxC;AAKA,YAAM,KAAK,UAAU,OAAO,SAAS;AACrC,UAAI,IAAI;AACN,iBAAS,KAAK,IAAI,aAAa;AAAA,UAC7B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAGF,iBAAS,SAAS;AAAA,MACpB,OAAO;AACL,iBAAS,MAAM,IAAI,kBAAkB;AAAA,UACnC,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AAAA;AAAA,EACA,UAAU,SAAS,YAAY,aAAa;AAC1C,YAAQ,QAAQ,cAAc;AAAA,MAC5B,KAAK;AAEH,cAAM,OAAO,IAAI,YAAY,EAAE,OAAO,UAAU,EAAE,QAAQ,eAAe,EAAE;AAC3E,eAAO,SAAS,KAAK,OAAO,KAAK,MAAM,IAAI;AAAA,MAC7C,KAAK;AACH,eAAO,IAAI,YAAY,EAAE,OAAO,UAAU;AAAA,MAC5C,KAAK;AACH,eAAO,IAAI,KAAK,CAAC,UAAU,GAAG;AAAA,UAC5B,MAAM;AAAA,QACR,CAAC;AAAA,MACH,KAAK;AACH,eAAO,WAAW;AAAA,IACtB;AAAA,EACF;AAAA,EACA,kBAAkB,KAAK;AAErB,UAAM,UAAU,CAAC;AACjB,UAAM,cAAc,IAAI,kBAAkB,YAAY;AAEtD,QAAI,QAAQ,QAAQ,CAAC,MAAM,WAAW,QAAQ,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC;AAEtE,QAAI,CAAC,IAAI,QAAQ,IAAI,aAAa,GAAG;AACnC,cAAQ,aAAa,IAAI;AAAA,IAC3B;AAEA,QAAI,CAAC,IAAI,QAAQ,IAAI,mBAAmB,GAAG;AACzC,YAAM,eAAe,IAAI,wBAAwB;AAEjD,UAAI,iBAAiB,MAAM;AACzB,gBAAQ,mBAAmB,IAAI;AAAA,MACjC;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM,IAAI,cAAc;AAAA,MACxB,QAAQ,IAAI;AAAA,MACZ;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,QAAQ,aAAa;AAChC,UAAM,YAAY,IAAI,WAAW,WAAW;AAC5C,QAAI,WAAW;AACf,eAAW,SAAS,QAAQ;AAC1B,gBAAU,IAAI,OAAO,QAAQ;AAC7B,kBAAY,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAQF;AAPE,cApMI,eAoMG,QAAO,SAAS,qBAAqB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,eAAc;AACjD;AACA,cAvMI,eAuMG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,cAAa;AACxB,CAAC;AA1MH,IAAM,eAAN;AAAA,CA4MC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,eAAN,MAAmB;AAAC;AACpB,SAAS,OAAO;AAAC;AAOjB,SAAS,4CAA4C,SAAS;AAC5D,UAAQ,KAAK,MAAM,IAAI;AACzB;AACA,SAAS,sBAAsB,KAAK,gBAAgB;AAClD,SAAO,eAAe,GAAG;AAC3B;AAKA,SAAS,8BAA8B,aAAa,aAAa;AAC/D,SAAO,CAAC,gBAAgB,mBAAmB,YAAY,UAAU,gBAAgB;AAAA,IAC/E,QAAQ,uBAAqB,YAAY,mBAAmB,cAAc;AAAA,EAC5E,CAAC;AACH;AAKA,SAAS,qBAAqB,aAAa,eAAe,UAAU;AAClE,SAAO,CAAC,gBAAgB,mBAAmB,sBAAsB,UAAU,MAAM,cAAc,gBAAgB,uBAAqB,YAAY,mBAAmB,cAAc,CAAC,CAAC;AACrL;AAOA,IAAM,oBAAoB,IAAI,eAAe,YAAY,sBAAsB,EAAE;AAIjF,IAAM,uBAAuB,IAAI,eAAe,YAAY,yBAAyB,EAAE;AAIvF,IAAM,4BAA4B,IAAI,eAAe,YAAY,8BAA8B,EAAE;AAIjG,IAAM,mCAAmC,IAAI,eAAe,YAAY,qCAAqC,IAAI;AAAA,EAC/G,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AAKD,SAAS,6BAA6B;AACpC,MAAI,QAAQ;AACZ,SAAO,CAAC,KAAK,YAAY;AACvB,QAAI,UAAU,MAAM;AAClB,YAAM,eAAe,OAAO,mBAAmB;AAAA,QAC7C,UAAU;AAAA,MACZ,CAAC,KAAK,CAAC;AAKP,cAAQ,aAAa,YAAY,+BAA+B,qBAAqB;AAAA,IACvF;AACA,UAAM,eAAe,OAAO,oBAAqB;AACjD,UAAM,wBAAwB,OAAO,gCAAgC;AACrE,QAAI,uBAAuB;AACzB,YAAM,SAAS,aAAa,IAAI;AAChC,aAAO,MAAM,KAAK,OAAO,EAAE,KAAK,SAAS,MAAM,aAAa,OAAO,MAAM,CAAC,CAAC;AAAA,IAC7E,OAAO;AACL,aAAO,MAAM,KAAK,OAAO;AAAA,IAC3B;AAAA,EACF;AACF;AACA,IAAI,+BAA+B;AACnC,IAAM,0BAAN,MAAM,gCAA+B,YAAY;AAAA,EAC/C;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,eAAe,OAAO,oBAAqB;AAAA,EAC3C,wBAAwB,OAAO,gCAAgC;AAAA,EAC/D,YAAY,SAAS,UAAU;AAC7B,UAAM;AACN,SAAK,UAAU;AACf,SAAK,WAAW;AAIhB,SAAK,OAAO,cAAc,eAAe,cAAc,CAAC,8BAA8B;AACpF,YAAM,WAAW,iBAAiB,SAAS,IAAI,WAAW,CAAC;AAK3D,YAAM,mBAAmB,KAAK,QAAQ;AACtC,UAAI,YAAY,EAAE,KAAK,mBAAmB,iBAAiB,CAAC,kBAAkB;AAC5E,uCAA+B;AAC/B,iBAAS,IAAI,OAAQ,EAAE,KAAK,mBAAoB,MAA4D,4TAAqV,CAAC;AAAA,MACpc;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,gBAAgB;AACrB,QAAI,KAAK,UAAU,MAAM;AACvB,YAAM,wBAAwB,MAAM,KAAK,oBAAI,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,oBAAoB,GAAG,GAAG,KAAK,SAAS,IAAI,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;AAKnJ,WAAK,QAAQ,sBAAsB,YAAY,CAAC,iBAAiB,kBAAkB,qBAAqB,iBAAiB,eAAe,KAAK,QAAQ,GAAG,qBAAqB;AAAA,IAC/K;AACA,QAAI,KAAK,uBAAuB;AAC9B,YAAM,SAAS,KAAK,aAAa,IAAI;AACrC,aAAO,KAAK,MAAM,gBAAgB,uBAAqB,KAAK,QAAQ,OAAO,iBAAiB,CAAC,EAAE,KAAK,SAAS,MAAM,KAAK,aAAa,OAAO,MAAM,CAAC,CAAC;AAAA,IACtJ,OAAO;AACL,aAAO,KAAK,MAAM,gBAAgB,uBAAqB,KAAK,QAAQ,OAAO,iBAAiB,CAAC;AAAA,IAC/F;AAAA,EACF;AAQF;AAPE,cA1CI,yBA0CG,QAAO,SAAS,+BAA+B,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,yBAA2B,SAAS,WAAW,GAAM,SAAY,mBAAmB,CAAC;AACxH;AACA,cA7CI,yBA6CG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,wBAAuB;AAClC,CAAC;AAhDH,IAAM,yBAAN;AAAA,CAkDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAI,gBAAgB;AAKpB,IAAI;AAGJ,IAAM,wBAAwB;AAG9B,IAAM,yBAAyB;AAC/B,IAAM,gCAAgC;AAGtC,IAAM,kCAAkC;AAQxC,IAAM,uBAAN,MAA2B;AAAC;AAS5B,SAAS,uBAAuB;AAC9B,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,CAAC;AACV;AASA,IAAM,sBAAN,MAAM,oBAAmB;AAAA,EACvB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,QAAQ,QAAQ;AAAA,EAClC,YAAY,aAAa,UAAU;AACjC,SAAK,cAAc;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,qBAAqB,eAAe;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AAGV,QAAI,IAAI,WAAW,SAAS;AAC1B,YAAM,IAAI,MAAM,sBAAsB;AAAA,IACxC,WAAW,IAAI,iBAAiB,QAAQ;AACtC,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AAGA,QAAI,IAAI,QAAQ,KAAK,EAAE,SAAS,GAAG;AACjC,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACjD;AAEA,WAAO,IAAI,WAAW,cAAY;AAIhC,YAAM,WAAW,KAAK,aAAa;AACnC,YAAM,MAAM,IAAI,cAAc,QAAQ,wBAAwB,IAAI,QAAQ,IAAI;AAE9E,YAAM,OAAO,KAAK,SAAS,cAAc,QAAQ;AACjD,WAAK,MAAM;AAIX,UAAI,OAAO;AAEX,UAAI,WAAW;AAIf,WAAK,YAAY,QAAQ,IAAI,UAAQ;AAEnC,eAAO,KAAK,YAAY,QAAQ;AAEhC,eAAO;AACP,mBAAW;AAAA,MACb;AAIA,YAAM,UAAU,MAAM;AACpB,aAAK,oBAAoB,QAAQ,MAAM;AACvC,aAAK,oBAAoB,SAAS,OAAO;AAEzC,aAAK,OAAO;AAGZ,eAAO,KAAK,YAAY,QAAQ;AAAA,MAClC;AAKA,YAAM,SAAS,WAAS;AAItB,aAAK,gBAAgB,KAAK,MAAM;AAE9B,kBAAQ;AAER,cAAI,CAAC,UAAU;AAGb,qBAAS,MAAM,IAAI,kBAAkB;AAAA,cACnC;AAAA,cACA,QAAQ;AAAA,cACR,YAAY;AAAA,cACZ,OAAO,IAAI,MAAM,qBAAqB;AAAA,YACxC,CAAC,CAAC;AACF;AAAA,UACF;AAGA,mBAAS,KAAK,IAAI,aAAa;AAAA,YAC7B;AAAA,YACA,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ;AAAA,UACF,CAAC,CAAC;AAEF,mBAAS,SAAS;AAAA,QACpB,CAAC;AAAA,MACH;AAIA,YAAM,UAAU,WAAS;AACvB,gBAAQ;AAER,iBAAS,MAAM,IAAI,kBAAkB;AAAA,UACnC;AAAA,UACA,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAGA,WAAK,iBAAiB,QAAQ,MAAM;AACpC,WAAK,iBAAiB,SAAS,OAAO;AACtC,WAAK,SAAS,KAAK,YAAY,IAAI;AAEnC,eAAS,KAAK;AAAA,QACZ,MAAM,cAAc;AAAA,MACtB,CAAC;AAED,aAAO,MAAM;AACX,YAAI,CAAC,UAAU;AACb,eAAK,gBAAgB,IAAI;AAAA,QAC3B;AAEA,gBAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,QAAQ;AAItB,wBAAoB,KAAK,SAAS,eAAe,mBAAmB;AACpE,oBAAgB,UAAU,MAAM;AAAA,EAClC;AAQF;AAPE,cApJI,qBAoJG,QAAO,SAAS,2BAA2B,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,qBAAuB,SAAS,oBAAoB,GAAM,SAAS,QAAQ,CAAC;AAC/G;AACA,cAvJI,qBAuJG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,oBAAmB;AAC9B,CAAC;AA1JH,IAAM,qBAAN;AAAA,CA4JC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAIH,SAAS,mBAAmB,KAAK,MAAM;AACrC,MAAI,IAAI,WAAW,SAAS;AAC1B,WAAO,OAAO,kBAAkB,EAAE,OAAO,GAAG;AAAA,EAC9C;AAEA,SAAO,KAAK,GAAG;AACjB;AASA,IAAM,oBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,gBAAgB,MAAM;AAC9B,WAAO,sBAAsB,KAAK,UAAU,MAAM,mBAAmB,gBAAgB,uBAAqB,KAAK,OAAO,iBAAiB,CAAC,CAAC;AAAA,EAC3I;AAQF;AAPE,cAfI,mBAeG,QAAO,SAAS,yBAAyB,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,mBAAqB,SAAY,mBAAmB,CAAC;AACxF;AACA,cAlBI,mBAkBG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,kBAAiB;AAC5B,CAAC;AArBH,IAAM,mBAAN;AAAA,CAuBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,cAAc;AACpB,IAAM,uBAAuB,OAAO,IAAI,oBAAoB,KAAK,GAAG;AAKpE,SAAS,eAAe,KAAK;AAC3B,MAAI,iBAAiB,OAAO,IAAI,aAAa;AAC3C,WAAO,IAAI;AAAA,EACb;AACA,MAAI,qBAAqB,KAAK,IAAI,sBAAsB,CAAC,GAAG;AAC1D,WAAO,IAAI,kBAAkB,oBAAoB;AAAA,EACnD;AACA,SAAO;AACT;AAQA,IAAM,kBAAN,MAAM,gBAAe;AAAA,EACnB;AAAA,EACA,YAAY,YAAY;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,KAAK;AAGV,QAAI,IAAI,WAAW,SAAS;AAC1B,YAAM,IAAI,aAAc,QAAoD,OAAO,cAAc,eAAe,cAAc,sNAAsN;AAAA,IACtV;AAIA,UAAM,aAAa,KAAK;AACxB,UAAM,SAAS,WAAW,YAAY,KAAK,WAAW,UAAU,CAAC,IAAI,GAAG,IAAI;AAC5E,WAAO,OAAO,KAAK,UAAU,MAAM;AAEjC,aAAO,IAAI,WAAW,cAAY;AAGhC,cAAM,MAAM,WAAW,MAAM;AAC7B,YAAI,KAAK,IAAI,QAAQ,IAAI,aAAa;AACtC,YAAI,IAAI,iBAAiB;AACvB,cAAI,kBAAkB;AAAA,QACxB;AAEA,YAAI,QAAQ,QAAQ,CAAC,MAAM,WAAW,IAAI,iBAAiB,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC;AAElF,YAAI,CAAC,IAAI,QAAQ,IAAI,aAAa,GAAG;AACnC,cAAI,iBAAiB,eAAe,mBAAmB;AAAA,QACzD;AAEA,YAAI,CAAC,IAAI,QAAQ,IAAI,mBAAmB,GAAG;AACzC,gBAAM,eAAe,IAAI,wBAAwB;AAEjD,cAAI,iBAAiB,MAAM;AACzB,gBAAI,iBAAiB,qBAAqB,YAAY;AAAA,UACxD;AAAA,QACF;AAEA,YAAI,IAAI,cAAc;AACpB,gBAAM,eAAe,IAAI,aAAa,YAAY;AAMlD,cAAI,eAAe,iBAAiB,SAAS,eAAe;AAAA,QAC9D;AAEA,cAAM,UAAU,IAAI,cAAc;AAOlC,YAAI,iBAAiB;AAGrB,cAAM,iBAAiB,MAAM;AAC3B,cAAI,mBAAmB,MAAM;AAC3B,mBAAO;AAAA,UACT;AACA,gBAAM,aAAa,IAAI,cAAc;AAErC,gBAAM,UAAU,IAAI,YAAY,IAAI,sBAAsB,CAAC;AAG3D,gBAAM,MAAM,eAAe,GAAG,KAAK,IAAI;AAEvC,2BAAiB,IAAI,mBAAmB;AAAA,YACtC;AAAA,YACA,QAAQ,IAAI;AAAA,YACZ;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,QACT;AAIA,cAAM,SAAS,MAAM;AAEnB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,eAAe;AAEnB,cAAI,OAAO;AACX,cAAI,WAAW,6BAA6B;AAE1C,mBAAO,OAAO,IAAI,aAAa,cAAc,IAAI,eAAe,IAAI;AAAA,UACtE;AAEA,cAAI,WAAW,GAAG;AAChB,qBAAS,CAAC,CAAC,OAAO,sBAAsB;AAAA,UAC1C;AAKA,cAAI,KAAK,UAAU,OAAO,SAAS;AAGnC,cAAI,IAAI,iBAAiB,UAAU,OAAO,SAAS,UAAU;AAE3D,kBAAM,eAAe;AACrB,mBAAO,KAAK,QAAQ,aAAa,EAAE;AACnC,gBAAI;AAGF,qBAAO,SAAS,KAAK,KAAK,MAAM,IAAI,IAAI;AAAA,YAC1C,SAAS,OAAO;AAId,qBAAO;AAGP,kBAAI,IAAI;AAEN,qBAAK;AAEL,uBAAO;AAAA,kBACL;AAAA,kBACA,MAAM;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,IAAI;AAEN,qBAAS,KAAK,IAAI,aAAa;AAAA,cAC7B;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK,OAAO;AAAA,YACd,CAAC,CAAC;AAGF,qBAAS,SAAS;AAAA,UACpB,OAAO;AAEL,qBAAS,MAAM,IAAI,kBAAkB;AAAA;AAAA,cAEnC,OAAO;AAAA,cACP;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK,OAAO;AAAA,YACd,CAAC,CAAC;AAAA,UACJ;AAAA,QACF;AAIA,cAAM,UAAU,WAAS;AACvB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,eAAe;AACnB,gBAAM,MAAM,IAAI,kBAAkB;AAAA,YAChC;AAAA,YACA,QAAQ,IAAI,UAAU;AAAA,YACtB,YAAY,IAAI,cAAc;AAAA,YAC9B,KAAK,OAAO;AAAA,UACd,CAAC;AACD,mBAAS,MAAM,GAAG;AAAA,QACpB;AAKA,YAAI,cAAc;AAGlB,cAAM,iBAAiB,WAAS;AAE9B,cAAI,CAAC,aAAa;AAChB,qBAAS,KAAK,eAAe,CAAC;AAC9B,0BAAc;AAAA,UAChB;AAGA,cAAI,gBAAgB;AAAA,YAClB,MAAM,cAAc;AAAA,YACpB,QAAQ,MAAM;AAAA,UAChB;AAEA,cAAI,MAAM,kBAAkB;AAC1B,0BAAc,QAAQ,MAAM;AAAA,UAC9B;AAIA,cAAI,IAAI,iBAAiB,UAAU,CAAC,CAAC,IAAI,cAAc;AACrD,0BAAc,cAAc,IAAI;AAAA,UAClC;AAEA,mBAAS,KAAK,aAAa;AAAA,QAC7B;AAGA,cAAM,eAAe,WAAS;AAG5B,cAAI,WAAW;AAAA,YACb,MAAM,cAAc;AAAA,YACpB,QAAQ,MAAM;AAAA,UAChB;AAGA,cAAI,MAAM,kBAAkB;AAC1B,qBAAS,QAAQ,MAAM;AAAA,UACzB;AAEA,mBAAS,KAAK,QAAQ;AAAA,QACxB;AAEA,YAAI,iBAAiB,QAAQ,MAAM;AACnC,YAAI,iBAAiB,SAAS,OAAO;AACrC,YAAI,iBAAiB,WAAW,OAAO;AACvC,YAAI,iBAAiB,SAAS,OAAO;AAErC,YAAI,IAAI,gBAAgB;AAEtB,cAAI,iBAAiB,YAAY,cAAc;AAE/C,cAAI,YAAY,QAAQ,IAAI,QAAQ;AAClC,gBAAI,OAAO,iBAAiB,YAAY,YAAY;AAAA,UACtD;AAAA,QACF;AAEA,YAAI,KAAK,OAAO;AAChB,iBAAS,KAAK;AAAA,UACZ,MAAM,cAAc;AAAA,QACtB,CAAC;AAGD,eAAO,MAAM;AAEX,cAAI,oBAAoB,SAAS,OAAO;AACxC,cAAI,oBAAoB,SAAS,OAAO;AACxC,cAAI,oBAAoB,QAAQ,MAAM;AACtC,cAAI,oBAAoB,WAAW,OAAO;AAC1C,cAAI,IAAI,gBAAgB;AACtB,gBAAI,oBAAoB,YAAY,cAAc;AAClD,gBAAI,YAAY,QAAQ,IAAI,QAAQ;AAClC,kBAAI,OAAO,oBAAoB,YAAY,YAAY;AAAA,YACzD;AAAA,UACF;AAEA,cAAI,IAAI,eAAe,IAAI,MAAM;AAC/B,gBAAI,MAAM;AAAA,UACZ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ;AAQF;AAPE,cA5QI,iBA4QG,QAAO,SAAS,uBAAuB,mBAAmB;AAC/D,SAAO,KAAK,qBAAqB,iBAAmB,SAAS,UAAU,CAAC;AAC1E;AACA,cA/QI,iBA+QG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,gBAAe;AAC1B,CAAC;AAlRH,IAAM,iBAAN;AAAA,CAoRC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,eAAe,IAAI,eAAe,YAAY,iBAAiB,EAAE;AACvE,IAAM,2BAA2B;AACjC,IAAM,mBAAmB,IAAI,eAAe,YAAY,qBAAqB,IAAI;AAAA,EAC/E,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AACD,IAAM,2BAA2B;AACjC,IAAM,mBAAmB,IAAI,eAAe,YAAY,qBAAqB,IAAI;AAAA,EAC/E,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AAMD,IAAM,yBAAN,MAA6B;AAAC;AAI9B,IAAM,2BAAN,MAAM,yBAAwB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB;AAAA,EACnB,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,aAAa;AAAA,EACb,YAAY,KAAK,UAAU,YAAY;AACrC,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW;AACT,QAAI,KAAK,aAAa,UAAU;AAC9B,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,IAAI,UAAU;AACxC,QAAI,iBAAiB,KAAK,kBAAkB;AAC1C,WAAK;AACL,WAAK,YAAY,iBAAiB,cAAc,KAAK,UAAU;AAC/D,WAAK,mBAAmB;AAAA,IAC1B;AACA,WAAO,KAAK;AAAA,EACd;AAQF;AAPE,cA3BI,0BA2BG,QAAO,SAAS,gCAAgC,mBAAmB;AACxE,SAAO,KAAK,qBAAqB,0BAA4B,SAAS,QAAQ,GAAM,SAAS,WAAW,GAAM,SAAS,gBAAgB,CAAC;AAC1I;AACA,cA9BI,0BA8BG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,yBAAwB;AACnC,CAAC;AAjCH,IAAM,0BAAN;AAAA,CAmCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,kBAAkB,KAAK,MAAM;AACpC,QAAM,QAAQ,IAAI,IAAI,YAAY;AAKlC,MAAI,CAAC,OAAO,YAAY,KAAK,IAAI,WAAW,SAAS,IAAI,WAAW,UAAU,MAAM,WAAW,SAAS,KAAK,MAAM,WAAW,UAAU,GAAG;AACzI,WAAO,KAAK,GAAG;AAAA,EACjB;AACA,QAAM,QAAQ,OAAO,sBAAsB,EAAE,SAAS;AACtD,QAAM,aAAa,OAAO,gBAAgB;AAE1C,MAAI,SAAS,QAAQ,CAAC,IAAI,QAAQ,IAAI,UAAU,GAAG;AACjD,UAAM,IAAI,MAAM;AAAA,MACd,SAAS,IAAI,QAAQ,IAAI,YAAY,KAAK;AAAA,IAC5C,CAAC;AAAA,EACH;AACA,SAAO,KAAK,GAAG;AACjB;AAIA,IAAM,uBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,UAAU,gBAAgB,MAAM;AAC9B,WAAO,sBAAsB,KAAK,UAAU,MAAM,kBAAkB,gBAAgB,uBAAqB,KAAK,OAAO,iBAAiB,CAAC,CAAC;AAAA,EAC1I;AAQF;AAPE,cARI,sBAQG,QAAO,SAAS,4BAA4B,mBAAmB;AACpE,SAAO,KAAK,qBAAqB,sBAAwB,SAAY,mBAAmB,CAAC;AAC3F;AACA,cAXI,sBAWG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,qBAAoB;AAC/B,CAAC;AAdH,IAAM,sBAAN;AAAA,CAgBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAOH,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAC1B,EAAAA,iBAAgBA,iBAAgB,cAAc,IAAI,CAAC,IAAI;AACvD,EAAAA,iBAAgBA,iBAAgB,oBAAoB,IAAI,CAAC,IAAI;AAC7D,EAAAA,iBAAgBA,iBAAgB,yBAAyB,IAAI,CAAC,IAAI;AAClE,EAAAA,iBAAgBA,iBAAgB,kBAAkB,IAAI,CAAC,IAAI;AAC3D,EAAAA,iBAAgBA,iBAAgB,cAAc,IAAI,CAAC,IAAI;AACvD,EAAAA,iBAAgBA,iBAAgB,uBAAuB,IAAI,CAAC,IAAI;AAChE,EAAAA,iBAAgBA,iBAAgB,OAAO,IAAI,CAAC,IAAI;AAClD,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,SAAS,gBAAgB,MAAM,WAAW;AACxC,SAAO;AAAA,IACL,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AACF;AA8BA,SAAS,qBAAqB,UAAU;AACtC,MAAI,WAAW;AACb,UAAM,eAAe,IAAI,IAAI,SAAS,IAAI,OAAK,EAAE,KAAK,CAAC;AACvD,QAAI,aAAa,IAAI,gBAAgB,gBAAgB,KAAK,aAAa,IAAI,gBAAgB,uBAAuB,GAAG;AACnH,YAAM,IAAI,MAAM,YAAY,0JAA0J,EAAE;AAAA,IAC1L;AAAA,EACF;AACA,QAAM,YAAY,CAAC,YAAY,gBAAgB,wBAAwB;AAAA,IACrE,SAAS;AAAA,IACT,aAAa;AAAA,EACf,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY,MAAM;AAChB,aAAO,OAAO,eAAe;AAAA,QAC3B,UAAU;AAAA,MACZ,CAAC,KAAK,OAAO,cAAc;AAAA,IAC7B;AAAA,EACF,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACD,aAAW,WAAW,UAAU;AAC9B,cAAU,KAAK,GAAG,QAAQ,UAAU;AAAA,EACtC;AACA,SAAO,yBAAyB,SAAS;AAC3C;AASA,SAAS,iBAAiB,gBAAgB;AACxC,SAAO,gBAAgB,gBAAgB,cAAc,eAAe,IAAI,mBAAiB;AACvF,WAAO;AAAA,MACL,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF,CAAC,CAAC;AACJ;AACA,IAAM,wBAAwB,IAAI,eAAe,YAAY,0BAA0B,EAAE;AAYzF,SAAS,yBAAyB;AAMhC,SAAO,gBAAgB,gBAAgB,oBAAoB,CAAC;AAAA,IAC1D,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,aAAa;AAAA,IACb,OAAO;AAAA,EACT,CAAC,CAAC;AACJ;AAQA,SAAS,sBAAsB;AAAA,EAC7B;AAAA,EACA;AACF,GAAG;AACD,QAAM,YAAY,CAAC;AACnB,MAAI,eAAe,QAAW;AAC5B,cAAU,KAAK;AAAA,MACb,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,MAAI,eAAe,QAAW;AAC5B,cAAU,KAAK;AAAA,MACb,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,SAAO,gBAAgB,gBAAgB,yBAAyB,SAAS;AAC3E;AAQA,SAAS,uBAAuB;AAC9B,SAAO,gBAAgB,gBAAgB,kBAAkB,CAAC;AAAA,IACxD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AAMA,SAAS,mBAAmB;AAC1B,SAAO,gBAAgB,gBAAgB,cAAc,CAAC,oBAAoB;AAAA,IACxE,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT,CAAC,CAAC;AACJ;AAqBA,SAAS,4BAA4B;AACnC,SAAO,gBAAgB,gBAAgB,uBAAuB,CAAC;AAAA,IAC7D,SAAS;AAAA,IACT,YAAY,MAAM;AAChB,YAAM,oBAAoB,OAAO,aAAa;AAAA,QAC5C,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,CAAC;AACD,UAAI,aAAa,sBAAsB,MAAM;AAC3C,cAAM,IAAI,MAAM,kGAAkG;AAAA,MACpH;AACA,aAAO;AAAA,IACT;AAAA,EACF,CAAC,CAAC;AACJ;AAQA,SAAS,YAAY;AACnB,SAAO,gBAAgB,gBAAgB,OAAO,CAAC,cAAc;AAAA,IAC3D,SAAS;AAAA,IACT,aAAa;AAAA,EACf,GAAG;AAAA,IACD,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC;AACJ;AAgBA,IAAM,wBAAN,MAAM,sBAAqB;AAAA;AAAA;AAAA;AAAA,EAIzB,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,qBAAqB,EAAE,UAAU;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,YAAY,UAAU,CAAC,GAAG;AAC/B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,sBAAsB,OAAO,EAAE;AAAA,IAC5C;AAAA,EACF;AAuBF;AAtBE,cAxBI,uBAwBG,QAAO,SAAS,6BAA6B,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,uBAAsB;AACzD;AACA,cA3BI,uBA2BG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AACR,CAAC;AACD,cA9BI,uBA8BG,QAAyB,iBAAiB;AAAA,EAC/C,WAAW,CAAC,qBAAqB;AAAA,IAC/B,SAAS;AAAA,IACT,aAAa;AAAA,IACb,OAAO;AAAA,EACT,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG,sBAAsB;AAAA,IACvB,YAAY;AAAA,IACZ,YAAY;AAAA,EACd,CAAC,EAAE,YAAY;AAAA,IACb,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACH,CAAC;AA7CH,IAAM,uBAAN;AAAA,CA+CC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,qBAAqB;AAAA,QAC/B,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,sBAAsB;AAAA,QACvB,YAAY;AAAA,QACZ,YAAY;AAAA,MACd,CAAC,EAAE,YAAY;AAAA,QACb,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAWH,IAAM,oBAAN,MAAM,kBAAiB;AAUvB;AATE,cADI,mBACG,QAAO,SAAS,yBAAyB,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,mBAAkB;AACrD;AACA,cAJI,mBAIG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AACR,CAAC;AACD,cAPI,mBAOG,QAAyB,iBAAiB;AAAA,EAC/C,WAAW,CAAC,kBAAkB,uBAAuB,CAAC,CAAC;AACzD,CAAC;AATH,IAAM,mBAAN;AAAA,CAWC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKL,WAAW,CAAC,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IACzD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,yBAAN,MAAM,uBAAsB;AAU5B;AATE,cADI,wBACG,QAAO,SAAS,8BAA8B,mBAAmB;AACtE,SAAO,KAAK,qBAAqB,wBAAuB;AAC1D;AACA,cAJI,wBAIG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AACR,CAAC;AACD,cAPI,wBAOG,QAAyB,iBAAiB;AAAA,EAC/C,WAAW,CAAC,iBAAiB,EAAE,UAAU;AAC3C,CAAC;AATH,IAAM,wBAAN;AAAA,CAWC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,iBAAiB,EAAE,UAAU;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACn8FH,IAAM,gBAAgB,MAAM;AAC1B,QAAM,SAAS,mBAAmB,MAAM;AACxC,SAAO,cAAc,mBAAmB,aAAa;AACrD,SAAO,OAAO,mBAAmB,MAAM;AACvC,SAAO,OAAO,mBAAmB,MAAM;AACvC,SAAO;AACT,GAAG;AACH,SAAS,mBAAmB,cAAc;AACxC,SAAO,SAAS,gBAAgB,SAAS,SAAS;AAChD,aAAS,YAAY,yBAAyB,YAAY;AAC1D,UAAM,WAAW,SAAS,YAAY,OAAO,QAAQ;AACrD,WAAO,IAAI,iBAAiB,UAAU,MAAM,iBAAiB,SAAS,YAAY,GAAG,SAAS,cAAc,SAAS,OAAO,SAAS,KAAK;AAAA,EAC5I;AACF;AACA,SAAS,iBAAiB,SAAS,cAAc;AAC/C,MAAI,mBAAmB,OAAO,YAAY,aAAa,QAAQ,IAAI;AACnE,MAAI,qBAAqB,QAAW;AAClC,WAAO;AAAA,EACT,WAAW,OAAO,qBAAqB,UAAU;AAC/C,uBAAmB;AAAA,MACjB,KAAK;AAAA,IACP;AAAA,EACF;AACA,QAAM,UAAU,iBAAiB,mBAAmB,cAAc,iBAAiB,UAAU,IAAI,YAAY,iBAAiB,OAAO;AACrI,QAAM,SAAS,iBAAiB,kBAAkB,aAAa,iBAAiB,SAAS,IAAI,WAAW;AAAA,IACtG,YAAY,iBAAiB;AAAA,EAC/B,CAAC;AACD,SAAO,IAAI,YAAY,iBAAiB,UAAU,OAAO,iBAAiB,KAAK,iBAAiB,QAAQ,MAAM;AAAA,IAC5G;AAAA,IACA;AAAA,IACA,gBAAgB,iBAAiB;AAAA,IACjC,iBAAiB,iBAAiB;AAAA,IAClC;AAAA,IACA,SAAS,iBAAiB;AAAA,IAC1B,eAAe,iBAAiB;AAAA,EAClC,CAAC;AACH;AACA,IAAM,mBAAN,cAA+B,aAAc;AAAA,EAC3C;AAAA,EACA,WAAW,aAAa;AAAA,IACtB,QAAQ,KAAK;AAAA,IACb,aAAa,MAAM;AAAA,EACrB,CAAC;AAAA,EACD,YAAY,aAAa;AAAA,IACvB,QAAQ,KAAK;AAAA,IACb,aAAa,MAAM;AAAA,EACrB,CAAC;AAAA,EACD,cAAc,aAAa;AAAA,IACzB,QAAQ,KAAK;AAAA,IACb,aAAa,MAAM;AAAA,EACrB,CAAC;AAAA,EACD,UAAU,SAAS,MAAM,KAAK,OAAO,MAAM,eAAe,YAAY,KAAK,OAAO,MAAM,eAAe,QAAQ,KAAK,SAAS,IAAI,MAAS;AAAA,EAC1I,WAAW,KAAK,UAAU,WAAW;AAAA,EACrC,aAAa,KAAK,YAAY,WAAW;AAAA,EACzC,YAAY,UAAU,SAAS,cAAc,OAAO,OAAO;AACzD,UAAM,SAAS,CAAC;AAAA,MACd,SAAAC;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI;AAGJ,YAAM,UAAU,MAAM,IAAI,YAAY;AACtC,kBAAY,iBAAiB,SAAS,OAAO;AAE7C,YAAM,SAAS,OAAO;AAAA,QACpB,OAAO;AAAA,MACT,CAAC;AACD,UAAI;AACJ,YAAM,UAAU,IAAI,QAAQ,OAAK,UAAU,CAAC;AAC5C,YAAM,OAAO,WAAS;AACpB,eAAO,IAAI,KAAK;AAChB,kBAAU,MAAM;AAChB,kBAAU;AAAA,MACZ;AACA,YAAM,KAAK,OAAO,QAAQA,QAAO,EAAE,UAAU;AAAA,QAC3C,MAAM,WAAS;AACb,kBAAQ,MAAM,MAAM;AAAA,YAClB,KAAK,cAAc;AACjB,mBAAK,SAAS,IAAI,MAAM,OAAO;AAC/B,mBAAK,YAAY,IAAI,MAAM,MAAM;AACjC,kBAAI;AACF,qBAAK;AAAA,kBACH,OAAO,QAAQ,MAAM,MAAM,IAAI,IAAI,MAAM;AAAA,gBAC3C,CAAC;AAAA,cACH,SAAS,OAAO;AACd,qBAAK;AAAA,kBACH;AAAA,gBACF,CAAC;AAAA,cACH;AACA;AAAA,YACF,KAAK,cAAc;AACjB,mBAAK,UAAU,IAAI,KAAK;AACxB;AAAA,UACJ;AAAA,QACF;AAAA,QACA,OAAO,WAAS;AACd,cAAI,iBAAiB,mBAAmB;AACtC,iBAAK,SAAS,IAAI,MAAM,OAAO;AAC/B,iBAAK,YAAY,IAAI,MAAM,MAAM;AAAA,UACnC;AACA,eAAK;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,UAAU,MAAM;AACd,cAAI,SAAS;AACX,iBAAK;AAAA,cACH,OAAO,IAAI,MAAM,6CAA6C;AAAA,YAChE,CAAC;AAAA,UACH;AACA,sBAAY,oBAAoB,SAAS,OAAO;AAAA,QAClD;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT,GAAG,cAAc,OAAO,QAAQ;AAChC,SAAK,SAAS,SAAS,IAAI,UAAU;AAAA,EACvC;AACF;AA4BA,IAAM,iCAAiC,IAAI,eAAe,YAAY,mCAAmC,EAAE;AAI3G,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,UAAU;AAChB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB,IAAI,eAAe,YAAY,sCAAsC,EAAE;AAI7F,IAAM,kBAAkB,CAAC,OAAO,MAAM;AACtC,SAAS,2BAA2B,KAAK,MAAM;AAC7C,QAGI,YAAO,aAAa,GAFtB;AAAA;AAAA,EA1LJ,IA4LM,IADC,0BACD,IADC;AAAA,IADH;AAAA;AAGF,QAAM;AAAA,IACJ,eAAe;AAAA,IACf,QAAQ;AAAA,EACV,IAAI;AAEJ,MAAI,CAAC,iBAAiB,mBAAmB;AAAA,EAEzC,kBAAkB,UAAU,CAAC,cAAc,uBAAuB,CAAC,kBAAkB,kBAAkB,UAAU,CAAC,gBAAgB,SAAS,aAAa;AAAA,EAExJ,CAAC,cAAc,kCAAkC,eAAe,GAAG,KAAK,cAAc,SAAS,GAAG,MAAM,OAAO;AAC7G,WAAO,KAAK,GAAG;AAAA,EACjB;AACA,QAAM,gBAAgB,OAAO,aAAa;AAC1C,QAAM,YAAY,OAAO,gCAAgC;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AACD,MAA4D,WAAW;AACrE,UAAM,IAAI,aAAc,MAA4D,aAAa,2MAAqN;AAAA,EACxT;AACA,QAAM,aAAoD,QAA4B,oBAAoB,IAAI,KAAK,SAAS,IAAI,IAAI;AACpI,QAAM,WAAW,aAAa,KAAK,UAAU;AAC7C,QAAM,WAAW,cAAc,IAAI,UAAU,IAAI;AACjD,MAAI,mBAAmB,cAAc;AACrC,MAAI,OAAO,mBAAmB,YAAY,eAAe,gBAAgB;AAEvE,uBAAmB,eAAe;AAAA,EACpC;AACA,MAAI,UAAU;AACZ,UAAM;AAAA,MACJ,CAAC,IAAI,GAAG;AAAA,MACR,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,OAAO,GAAG;AAAA,MACX,CAAC,MAAM,GAAG;AAAA,MACV,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,OAAO,GAAG;AAAA,IACb,IAAI;AAEJ,QAAI,OAAO;AACX,YAAQ,cAAc;AAAA,MACpB,KAAK;AACH,eAAO,IAAI,YAAY,EAAE,OAAO,aAAa,EAAE;AAC/C;AAAA,MACF,KAAK;AACH,eAAO,IAAI,KAAK,CAAC,aAAa,CAAC;AAC/B;AAAA,IACJ;AAIA,QAAI,UAAU,IAAI,YAAY,WAAW;AACzC,QAAI,OAAO,cAAc,eAAe,WAAW;AAIjD,gBAAU,8BAA8B,IAAI,KAAK,SAAS,oBAAoB,CAAC,CAAC;AAAA,IAClF;AACA,WAAO,GAAG,IAAI,aAAa;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAEA,SAAO,KAAK,GAAG,EAAE,KAAK,IAAI,WAAS;AACjC,QAAI,iBAAiB,gBAAgB,QAAuC,OAAc;AACxF,oBAAc,IAAI,UAAU;AAAA,QAC1B,CAAC,IAAI,GAAG,MAAM;AAAA,QACd,CAAC,OAAO,GAAG,mBAAmB,MAAM,SAAS,gBAAgB;AAAA,QAC7D,CAAC,MAAM,GAAG,MAAM;AAAA,QAChB,CAAC,WAAW,GAAG,MAAM;AAAA,QACrB,CAAC,OAAO,GAAG;AAAA,QACX,CAAC,aAAa,GAAG,IAAI;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC;AACJ;AAEA,SAAS,eAAe,KAAK;AAC3B,SAAO,IAAI,QAAQ,IAAI,eAAe,KAAK,IAAI,QAAQ,IAAI,qBAAqB;AAClF;AAcA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,CAAC,GAAG,OAAO,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,OAAK,GAAG,CAAC,IAAI,OAAO,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG;AAChF;AACA,SAAS,aAAa,SAAS,kBAAkB;AAE/C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,gBAAgB,oBAAoB,MAAM;AAChD,MAAI,iBAAiB,QAAQ,cAAc;AAC3C,MAAI,0BAA0B,iBAAiB;AAC7C,qBAAiB,oBAAoB,cAAc;AAAA,EACrD,WAAW,OAAO,mBAAmB,UAAU;AAC7C,qBAAiB;AAAA,EACnB;AACA,QAAM,MAAM,CAAC,QAAQ,cAAc,kBAAkB,gBAAgB,aAAa,EAAE,KAAK,GAAG;AAC5F,QAAM,OAAO,aAAa,GAAG;AAC7B,SAAO,aAAa,IAAI;AAC1B;AAOA,SAAS,aAAa,OAAO;AAC3B,MAAI,OAAO;AACX,aAAW,QAAQ,OAAO;AACxB,WAAO,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,WAAW,CAAC,KAAK;AAAA,EACrD;AAGA,UAAQ,aAAa;AACrB,SAAO,KAAK,SAAS;AACvB;AAYA,SAAS,sBAAsB,cAAc;AAC3C,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,YAAY,MAAM;AAChB,6BAAwB,qBAAqB;AAC7C,aAAO;AAAA,QACL,eAAe;AAAA,SACZ;AAAA,IAEP;AAAA,EACF,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,YAAY,MAAM;AAChB,YAAM,SAAS,OAAO,cAAc;AACpC,YAAM,aAAa,OAAO,aAAa;AACvC,aAAO,MAAM;AACX,eAAO,WAAW,EAAE,KAAK,MAAM;AAC7B,qBAAW,gBAAgB;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAKA,SAAS,8BAA8B,KAAK,SAAS,kBAAkB;AACrE,QAAM,kBAAkB,oBAAI,IAAI;AAChC,SAAO,IAAI,MAAM,SAAS;AAAA,IACxB,IAAI,QAAQ,MAAM;AAChB,YAAM,QAAQ,QAAQ,IAAI,QAAQ,IAAI;AACtC,YAAM,UAAU,oBAAI,IAAI,CAAC,OAAO,OAAO,QAAQ,CAAC;AAChD,UAAI,OAAO,UAAU,cAAc,CAAC,QAAQ,IAAI,IAAI,GAAG;AACrD,eAAO;AAAA,MACT;AACA,aAAO,gBAAc;AAEnB,cAAM,OAAO,OAAO,MAAM,YAAY,YAAY;AAClD,YAAI,CAAC,iBAAiB,SAAS,UAAU,KAAK,CAAC,gBAAgB,IAAI,GAAG,GAAG;AACvE,0BAAgB,IAAI,GAAG;AACvB,gBAAM,eAAe,eAAgB,GAAG;AAExC,kBAAQ,KAAK,mBAAoB,MAA+D,+BAA+B,UAAU,gKAA0K,UAAU,uBAAuB,YAAY,2RAA+S,CAAC;AAAA,QAClpB;AAEA,eAAO,MAAM,MAAM,QAAQ,CAAC,UAAU,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF,CAAC;AACH;", "names": ["value", "map", "base", "headers", "params", "HttpEventType", "HttpStatusCode", "req", "signal", "HttpFeatureKind", "request"]}