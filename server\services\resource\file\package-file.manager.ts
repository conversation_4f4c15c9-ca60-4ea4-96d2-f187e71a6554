import path from 'node:path';
import * as fs from 'fs-extra';

import { FileManager, IFile, IManagerFile, ITime } from './file.manager';
import { guid } from '@server/utils/utils';
import { PackageType } from '../package/package.type';

interface PackageFileData {
  schedule_id: string;
  type: PackageType;
}

class PackageFile implements IFile {
  id = guid();
  _path: string;
  deleted: boolean;

  created_at: number;
  name: string;
  data: PackageFileData;

  fileManager: IManagerFile;
  constructor(name: string, data: PackageFileData, fileManager: IManagerFile) {
    this.name = name;
    this.data = data;
    this.fileManager = fileManager;
    this.created_at = this.fileManager.serverTime.now();
    this._path = path.join(this.data?.schedule_id, this.name);
    return new Proxy(this, {
      set(target, p, value) {
        target[p] = value;
        fileManager.markWrite();
        return true;
      },
    });
  }

  get path(): string {
    const p = this._path || path.join(this.data?.schedule_id, this.name);
    return path.join(this.fileManager.file_base_dir, p);
  }

  get isExist() {
    const is_exist = fs.existsSync(this.path);
    return is_exist;
  }

  toJSON(): object {
    return {
      id: this.id,
      _path: this._path,
      deleted: this.deleted,
      created_at: this.created_at,
      name: this.name,
      data: this.data,
    };
  }

  static load(fileData: PackageFile, fileManager: IManagerFile) {
    const file = new PackageFile(fileData.name, fileData.data, fileManager);
    Object.assign(file, fileData);
    return file;
  }
}

export class PackageFileManager extends FileManager<PackageFile> {
  serverTime: ITime;
  constructor(packageBasePath: string, serverTime: ITime) {
    super(packageBasePath);
    this.serverTime = serverTime;
    this.init().catch((err) =>
      console.error('PackageFile: init error:\n' + err),
    );
  }

  async init() {
    const config = await this.readConfig();
    if (config) {
      console.log('PackageFile: read package file config');
      this.config = config;
    } else {
      console.log('PackageFile: no package file config');
      this.config = { update_at: 0, file_list: [] };
    }
    console.log(
      'PackageFile: load package file #' + this.config.file_list.length,
    );
    for (const fileData of this.config.file_list) {
      this.files.push(PackageFile.load(fileData, this));
    }
  }

  create(file_name: string, data: PackageFileData) {
    const file = this.getFile(file_name, data.schedule_id);
    if (file) {
      console.warn(
        'PackageFile: file already exist',
        file_name,
        JSON.stringify(data),
      );
      return file;
    }
    const packageFile = new PackageFile(file_name, data, this);
    this.pushFile(packageFile);
    return packageFile;
  }

  getFile(name: string, schedule_id: string) {
    return this.files.find(
      (file) => file.name === name && file.data.schedule_id === schedule_id,
    );
  }
}
