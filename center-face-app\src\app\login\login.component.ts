import { CommonModule } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';

@Component({
    selector: 'app-login',
    imports: [CommonModule, RouterOutlet],
    template: `
    <main>
      <div class="card-container">
        <div class="header">
          <img class="logo" src="assets/images/logo.png" alt="logo" />
          <div class="title">
            悦考·智签
          </div>
        </div>
        <router-outlet></router-outlet>
      </div>
    </main>
  `,
    styles: [
        `
    @use 'mixins' as *;
      :host {
        display: block;
        width: 100%;
        height: 100%;
      }
      main {
        width: 100%;
        height: 100%;
        background: url("/assets/images/login_bg.png")  no-repeat center;
        background-size: cover;
        @include flex-box(column, center, flex-start);
        .card-container {
          margin-left: 10%;
        }
        .header {
          @include flex-box(row, space-evenly, center);
          width: 400px;
          margin-bottom: 30px;
          .logo {
            width: 120px;
          }
          .title {
            font-size: 32px;
            font-weight: 500;
          }
        }
      }
      @media (max-width: 500px) {
        main {
          align-items: center !important;
        }
        .header {
          width: 100% !important;
        }
        .card-container {
          margin-left: 0 !important;
        }
      }
    `
    ]
})
export class LoginComponent implements OnInit {
  ngOnInit(): void {}
}
