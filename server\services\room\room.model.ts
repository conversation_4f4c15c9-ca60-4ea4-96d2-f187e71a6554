import serverTime from '@services/serverTime';
interface IRoom {
  id: string;
  name: string;
  sn_code: string;
  token: string;
  host: string;
  address: string;
  app_version: string;
}

export class Room {
  public id: string;
  public name: string;
  public sn_code: string;
  public token: string;
  public host: string;
  public address: string;
  public app_version: string;
  public last_sync_time = 0;

  constructor(room: IRoom) {
    Object.assign(this, room);
    this.last_sync_time = 0;
  }

  isOnline() {
    if (this.last_sync_time === 0) {
      return false;
    }
    const now = serverTime.now();
    if (this.last_sync_time + 5 * 60 + 5 < now) {
      return false;
    }
    return true;
  }
}
