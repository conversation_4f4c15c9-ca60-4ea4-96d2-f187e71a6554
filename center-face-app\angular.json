{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"center-face": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/center-face", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, "src/assets", "src/favicon.ico"], "styles": ["./node_modules/ng-zorro-antd/ng-zorro-antd.min.css", "src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["src/styles"]}, "allowedCommonJsDependencies": ["@mediapipe/face_detection", "@vladmandic/human"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2.0MB", "maximumError": "3.0MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all", "aot": true, "optimization": true, "sourceMap": false, "namedChunks": false, "serviceWorker": "ngsw-config.json"}, "stage1": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage1.ts"}], "budgets": [{"type": "initial", "maximumWarning": "2.0MB", "maximumError": "3.0MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all", "aot": true, "optimization": true, "sourceMap": false, "namedChunks": false}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "center-face:build:production", "proxyConfig": "proxy.conf.json"}, "development": {"buildTarget": "center-face:build:development", "proxyConfig": "proxy.conf.json"}}, "options": {"headers": {"X-Content-Type-Options": ""}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["./node_modules/ng-zorro-antd/ng-zorro-antd.min.css", "src/styles.scss"], "scripts": []}}}}}}