@use "variables" as *;
@use "mixins" as *;

:host {
  display: block;
}
.container {
  background: #f6f7f9;
  padding: 0 24px 24px 24px;
}
.card {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 10px;
}
.title {
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
.sub-title {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
.mark {
  display: inline-block;
  width: 3px;
  height: 16px;
  background-color: #007aff;
  border-radius: 2px;
  margin-right: 8px;
}
.schedule-card {
  @extend .card;
  @include flex-box(row, space-between);
  width: 100%;
  margin-bottom: 10px;
  .time-range {
    flex: 0 1 auto;
    min-width: fit-content;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
  }
  .subjects {
    @include text-overflow-ellipsis();
    flex: 1 1 auto;
    margin: 0 10px;
    font-size: 16px;
    font-weight: bold;
  }
  .project {
    flex: 0 1 auto;
    min-width: fit-content;
    color: rgba(0, 0, 0, 0.45);
  }
}

.form-card {
  @extend .card;
  margin-top: 0;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
  .action-buttons {
    display: flex;
    justify-content: flex-end;
    button {
      margin-left: 8px;
    }
    .published {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: #30a46c;
      span {
        margin: 0 2px;
      }
      margin-right: 5px;
    }
  }
  .upload-button {
    text-align: center;
  }
}
.ant-segmented {
  // background-color: #fff !important;
  margin-bottom: 10px;
}
.ant-divider {
  margin: 10px 0;
}
.exam-card {
  @extend .card;
  margin-top: 20px;
  .header {
    display: flex;
    justify-content: space-between;
    .stat-info {
      span {
        margin: 0 5px;
        width: 87px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        img {
          vertical-align: sub;
          margin-right: 5px;
        }
      }
    }
  }
  .exam-content-wrap {
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    grid-template-rows: 1.5fr 1.8fr;
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    justify-items: stretch;
    > div {
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.13);
      min-height: 200px;
      padding: 12px;
    }
    .stat-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
.data-buttons {
  display: flex;
  justify-content: center;
  button {
    margin: 5px;
  }
}
.room-stat {
  .content {
    margin-top: 30px;
  }
}
.test-stat {
  .content {
    display: flex;
    justify-content: center;
  }
}
.entry-stat {
  .content {
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
  .note {
    font-size: $font-size-small;
    color: $color-ligth-grey;
  }
}
.sub-title-wrap {
  display: flex;
  justify-content: space-between;
}
.event-stat {
  .content {
    .event-list {
      margin-top: 10px;
      background: rgba(0, 0, 0, 0.02);
      height: 250px;
      overflow-y: scroll;
      padding: 10px;
      .row {
        @include flex-box(row, flex-start);
        margin: 5px;
        font-size: 14px;
        span {
          margin-left: 6px;
        }
        .tag {
          border-radius: 4px;
          min-width: fit-content;
          padding: 2px 8px;
          margin: 0 4px;
          color: #fff;
          background: #e5484d;
        }
        .warning {
          background-color: #fb9a0e;
        }
        .room {
          min-width: fit-content;
        }
        .event-content {
          flex: 1;
          @include text-overflow-ellipsis();
        }
        .time {
          margin-left: auto;
          min-width: fit-content;
        }
      }
    }
  }
  .empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 250px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    padding: 16px;
    background-color: rgba(0, 0, 0, 0.02);
  }
}
.status-upload {
  .content {
    display: flex;
    .qrcode {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 30px;
    }
    .record {
      padding: 20px;
      .record-title {
        margin-bottom: 20px;
      }
      flex-grow: 1;
    }
  }
}
