import { app, BrowserWindow, ipcMain } from 'electron';
import * as main from './main/main-window';
import * as server from './server/index';
import * as config from "./config";

import "./ipc"; // IPC handlers


process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = "true";

app.on('ready', onAppReady);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on("render-process-gone", (event, webContents, details) => {
  console.error("render-process-gone", webContents.getURL(), details.reason);
});

app.on("child-process-gone", (event, details) => {
  console.error("child-process-gone", details.type, details.reason);
});

console.info("app_version: " + app.getVersion());

function onAppReady(){
  main.init();
  server.init();

  if (!config.settings.LANG) {
    config.settings.LANG = app.getLocale();
  }

  main.win.once("ready-to-show", () => {
    setTimeout(() => {
      main.win.show();
      if (!process.env.DEBUG_LEVEL) {
        server.win.hide();
      } else {
        server.win.minimize()
      }
    }, 500);
  });
}


ipcMain.on("ipc-server-started", () => {
  console.log("ipc-server-started");
  main.loadFile(`${__dirname}/frontend/browser/index.html`);
});