import {
  Sequelize,
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
} from 'sequelize';

export default function (sequelize: Sequelize) {
  const Schedule = sequelize.define<ScheduleModelFields>(
    'schedule',
    {
      id: {
        type: DataTypes.STRING,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
      },
      project_id: {
        type: DataTypes.STRING,
      },
      start: {
        type: DataTypes.STRING,
      },
      end: {
        type: DataTypes.STRING,
      },
      test: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      form_published: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      password_published: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      created_at: {
        type: DataTypes.INTEGER,
      },
    },
    {
      timestamps: false,
      underscored: true,
      freezeTableName: true,
      hooks: {
        beforeUpdate: (schedule: ScheduleModelFields, opt) => {},
        beforeCreate: (schedule: ScheduleModelFields, opt) => {},
      },
    },
  );
  return Schedule;
}

export interface ScheduleModelFields
  extends Model<
    InferAttributes<ScheduleModelFields>,
    InferCreationAttributes<ScheduleModelFields>
  > {
  id: string;
  project_id: string;
  name: string;
  start: string;
  end: string;
  test: 0 | 1;
  form_published: 0 | 1;
  password_published: 0 | 1;
  created_at: number;
}
