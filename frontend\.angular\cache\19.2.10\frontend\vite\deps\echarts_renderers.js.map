{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/svg/SVGPathRebuilder.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/svg/mapStyleToAttrs.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/svg/core.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/svg/cssAnimation.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/svg/graphic.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/svg/domapi.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/svg/patch.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/svg/Painter.js", "../../../../../../node_modules/.pnpm/echarts@5.4.3/node_modules/echarts/lib/renderer/installSVGRenderer.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/canvas/Layer.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/canvas/Painter.js", "../../../../../../node_modules/.pnpm/echarts@5.4.3/node_modules/echarts/lib/renderer/installCanvasRenderer.js"], "sourcesContent": ["import { isAroundZero } from './helper.js';\nvar mathSin = Math.sin;\nvar mathCos = Math.cos;\nvar PI = Math.PI;\nvar PI2 = Math.PI * 2;\nvar degree = 180 / PI;\nvar SVGPathRebuilder = function () {\n  function SVGPathRebuilder() {}\n  SVGPathRebuilder.prototype.reset = function (precision) {\n    this._start = true;\n    this._d = [];\n    this._str = '';\n    this._p = Math.pow(10, precision || 4);\n  };\n  SVGPathRebuilder.prototype.moveTo = function (x, y) {\n    this._add('M', x, y);\n  };\n  SVGPathRebuilder.prototype.lineTo = function (x, y) {\n    this._add('L', x, y);\n  };\n  SVGPathRebuilder.prototype.bezierCurveTo = function (x, y, x2, y2, x3, y3) {\n    this._add('C', x, y, x2, y2, x3, y3);\n  };\n  SVGPathRebuilder.prototype.quadraticCurveTo = function (x, y, x2, y2) {\n    this._add('Q', x, y, x2, y2);\n  };\n  SVGPathRebuilder.prototype.arc = function (cx, cy, r, startAngle, endAngle, anticlockwise) {\n    this.ellipse(cx, cy, r, r, 0, startAngle, endAngle, anticlockwise);\n  };\n  SVGPathRebuilder.prototype.ellipse = function (cx, cy, rx, ry, psi, startAngle, endAngle, anticlockwise) {\n    var dTheta = endAngle - startAngle;\n    var clockwise = !anticlockwise;\n    var dThetaPositive = Math.abs(dTheta);\n    var isCircle = isAroundZero(dThetaPositive - PI2) || (clockwise ? dTheta >= PI2 : -dTheta >= PI2);\n    var unifiedTheta = dTheta > 0 ? dTheta % PI2 : dTheta % PI2 + PI2;\n    var large = false;\n    if (isCircle) {\n      large = true;\n    } else if (isAroundZero(dThetaPositive)) {\n      large = false;\n    } else {\n      large = unifiedTheta >= PI === !!clockwise;\n    }\n    var x0 = cx + rx * mathCos(startAngle);\n    var y0 = cy + ry * mathSin(startAngle);\n    if (this._start) {\n      this._add('M', x0, y0);\n    }\n    var xRot = Math.round(psi * degree);\n    if (isCircle) {\n      var p = 1 / this._p;\n      var dTheta_1 = (clockwise ? 1 : -1) * (PI2 - p);\n      this._add('A', rx, ry, xRot, 1, +clockwise, cx + rx * mathCos(startAngle + dTheta_1), cy + ry * mathSin(startAngle + dTheta_1));\n      if (p > 1e-2) {\n        this._add('A', rx, ry, xRot, 0, +clockwise, x0, y0);\n      }\n    } else {\n      var x = cx + rx * mathCos(endAngle);\n      var y = cy + ry * mathSin(endAngle);\n      this._add('A', rx, ry, xRot, +large, +clockwise, x, y);\n    }\n  };\n  SVGPathRebuilder.prototype.rect = function (x, y, w, h) {\n    this._add('M', x, y);\n    this._add('l', w, 0);\n    this._add('l', 0, h);\n    this._add('l', -w, 0);\n    this._add('Z');\n  };\n  SVGPathRebuilder.prototype.closePath = function () {\n    if (this._d.length > 0) {\n      this._add('Z');\n    }\n  };\n  SVGPathRebuilder.prototype._add = function (cmd, a, b, c, d, e, f, g, h) {\n    var vals = [];\n    var p = this._p;\n    for (var i = 1; i < arguments.length; i++) {\n      var val = arguments[i];\n      if (isNaN(val)) {\n        this._invalid = true;\n        return;\n      }\n      vals.push(Math.round(val * p) / p);\n    }\n    this._d.push(cmd + vals.join(' '));\n    this._start = cmd === 'Z';\n  };\n  SVGPathRebuilder.prototype.generateStr = function () {\n    this._str = this._invalid ? '' : this._d.join('');\n    this._d = [];\n  };\n  SVGPathRebuilder.prototype.getStr = function () {\n    return this._str;\n  };\n  return SVGPathRebuilder;\n}();\nexport default SVGPathRebuilder;", "import { DEFAULT_PATH_STYLE } from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineDash } from '../canvas/dashStyle.js';\nimport { map } from '../core/util.js';\nimport { normalizeColor } from './helper.js';\nvar NONE = 'none';\nvar mathRound = Math.round;\nfunction pathHasFill(style) {\n  var fill = style.fill;\n  return fill != null && fill !== NONE;\n}\nfunction pathHasStroke(style) {\n  var stroke = style.stroke;\n  return stroke != null && stroke !== NONE;\n}\nvar strokeProps = ['lineCap', 'miterLimit', 'lineJoin'];\nvar svgStrokeProps = map(strokeProps, function (prop) {\n  return \"stroke-\" + prop.toLowerCase();\n});\nexport default function mapStyleToAttrs(updateAttr, style, el, forceUpdate) {\n  var opacity = style.opacity == null ? 1 : style.opacity;\n  if (el instanceof ZRImage) {\n    updateAttr('opacity', opacity);\n    return;\n  }\n  if (pathHasFill(style)) {\n    var fill = normalizeColor(style.fill);\n    updateAttr('fill', fill.color);\n    var fillOpacity = style.fillOpacity != null ? style.fillOpacity * fill.opacity * opacity : fill.opacity * opacity;\n    if (forceUpdate || fillOpacity < 1) {\n      updateAttr('fill-opacity', fillOpacity);\n    }\n  } else {\n    updateAttr('fill', NONE);\n  }\n  if (pathHasStroke(style)) {\n    var stroke = normalizeColor(style.stroke);\n    updateAttr('stroke', stroke.color);\n    var strokeScale = style.strokeNoScale ? el.getLineScale() : 1;\n    var strokeWidth = strokeScale ? (style.lineWidth || 0) / strokeScale : 0;\n    var strokeOpacity = style.strokeOpacity != null ? style.strokeOpacity * stroke.opacity * opacity : stroke.opacity * opacity;\n    var strokeFirst = style.strokeFirst;\n    if (forceUpdate || strokeWidth !== 1) {\n      updateAttr('stroke-width', strokeWidth);\n    }\n    if (forceUpdate || strokeFirst) {\n      updateAttr('paint-order', strokeFirst ? 'stroke' : 'fill');\n    }\n    if (forceUpdate || strokeOpacity < 1) {\n      updateAttr('stroke-opacity', strokeOpacity);\n    }\n    if (style.lineDash) {\n      var _a = getLineDash(el),\n        lineDash = _a[0],\n        lineDashOffset = _a[1];\n      if (lineDash) {\n        lineDashOffset = mathRound(lineDashOffset || 0);\n        updateAttr('stroke-dasharray', lineDash.join(','));\n        if (lineDashOffset || forceUpdate) {\n          updateAttr('stroke-dashoffset', lineDashOffset);\n        }\n      }\n    } else if (forceUpdate) {\n      updateAttr('stroke-dasharray', NONE);\n    }\n    for (var i = 0; i < strokeProps.length; i++) {\n      var propName = strokeProps[i];\n      if (forceUpdate || style[propName] !== DEFAULT_PATH_STYLE[propName]) {\n        var val = style[propName] || DEFAULT_PATH_STYLE[propName];\n        val && updateAttr(svgStrokeProps[i], val);\n      }\n    }\n  } else if (forceUpdate) {\n    updateAttr('stroke', NONE);\n  }\n}", "import { keys, map } from '../core/util.js';\nimport { encodeHTML } from '../core/dom.js';\nexport var SVGNS = 'http://www.w3.org/2000/svg';\nexport var XLINKNS = 'http://www.w3.org/1999/xlink';\nexport var XMLNS = 'http://www.w3.org/2000/xmlns/';\nexport var XML_NAMESPACE = 'http://www.w3.org/XML/1998/namespace';\nexport function createElement(name) {\n  return document.createElementNS(SVGNS, name);\n}\n;\nexport function createVNode(tag, key, attrs, children, text) {\n  return {\n    tag: tag,\n    attrs: attrs || {},\n    children: children,\n    text: text,\n    key: key\n  };\n}\nfunction createElementOpen(name, attrs) {\n  var attrsStr = [];\n  if (attrs) {\n    for (var key in attrs) {\n      var val = attrs[key];\n      var part = key;\n      if (val === false) {\n        continue;\n      } else if (val !== true && val != null) {\n        part += \"=\\\"\" + val + \"\\\"\";\n      }\n      attrsStr.push(part);\n    }\n  }\n  return \"<\" + name + \" \" + attrsStr.join(' ') + \">\";\n}\nfunction createElementClose(name) {\n  return \"</\" + name + \">\";\n}\nexport function vNodeToString(el, opts) {\n  opts = opts || {};\n  var S = opts.newline ? '\\n' : '';\n  function convertElToString(el) {\n    var children = el.children,\n      tag = el.tag,\n      attrs = el.attrs,\n      text = el.text;\n    return createElementOpen(tag, attrs) + (tag !== 'style' ? encodeHTML(text) : text || '') + (children ? \"\" + S + map(children, function (child) {\n      return convertElToString(child);\n    }).join(S) + S : '') + createElementClose(tag);\n  }\n  return convertElToString(el);\n}\nexport function getCssString(selectorNodes, animationNodes, opts) {\n  opts = opts || {};\n  var S = opts.newline ? '\\n' : '';\n  var bracketBegin = \" {\" + S;\n  var bracketEnd = S + \"}\";\n  var selectors = map(keys(selectorNodes), function (className) {\n    return className + bracketBegin + map(keys(selectorNodes[className]), function (attrName) {\n      return attrName + \":\" + selectorNodes[className][attrName] + \";\";\n    }).join(S) + bracketEnd;\n  }).join(S);\n  var animations = map(keys(animationNodes), function (animationName) {\n    return \"@keyframes \" + animationName + bracketBegin + map(keys(animationNodes[animationName]), function (percent) {\n      return percent + bracketBegin + map(keys(animationNodes[animationName][percent]), function (attrName) {\n        var val = animationNodes[animationName][percent][attrName];\n        if (attrName === 'd') {\n          val = \"path(\\\"\" + val + \"\\\")\";\n        }\n        return attrName + \":\" + val + \";\";\n      }).join(S) + bracketEnd;\n    }).join(S) + bracketEnd;\n  }).join(S);\n  if (!selectors && !animations) {\n    return '';\n  }\n  return ['<![CDATA[', selectors, animations, ']]>'].join(S);\n}\nexport function createBrushScope(zrId) {\n  return {\n    zrId: zrId,\n    shadowCache: {},\n    patternCache: {},\n    gradientCache: {},\n    clipPathCache: {},\n    defs: {},\n    cssNodes: {},\n    cssAnims: {},\n    cssClassIdx: 0,\n    cssAnimIdx: 0,\n    shadowIdx: 0,\n    gradientIdx: 0,\n    patternIdx: 0,\n    clipPathIdx: 0\n  };\n}\nexport function createSVGVNode(width, height, children, useViewBox) {\n  return createVNode('svg', 'root', {\n    'width': width,\n    'height': height,\n    'xmlns': SVGNS,\n    'xmlns:xlink': XLINKNS,\n    'version': '1.1',\n    'baseProfile': 'full',\n    'viewBox': useViewBox ? \"0 0 \" + width + \" \" + height : false\n  }, children);\n}", "import { copyTransform } from '../core/Transformable.js';\nimport { createBrushScope } from './core.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport PathProxy from '../core/PathProxy.js';\nimport { getPathPrecision, getSRTTransformString } from './helper.js';\nimport { each, extend, filter, isNumber, isString, keys } from '../core/util.js';\nimport CompoundPath from '../graphic/CompoundPath.js';\nimport { createCubicEasingFunc } from '../animation/cubicEasing.js';\nexport var EASING_MAP = {\n  cubicIn: '0.32,0,0.67,0',\n  cubicOut: '0.33,1,0.68,1',\n  cubicInOut: '0.65,0,0.35,1',\n  quadraticIn: '0.11,0,0.5,0',\n  quadraticOut: '0.5,1,0.89,1',\n  quadraticInOut: '0.45,0,0.55,1',\n  quarticIn: '0.5,0,0.75,0',\n  quarticOut: '0.25,1,0.5,1',\n  quarticInOut: '0.76,0,0.24,1',\n  quinticIn: '0.64,0,0.78,0',\n  quinticOut: '0.22,1,0.36,1',\n  quinticInOut: '0.83,0,0.17,1',\n  sinusoidalIn: '0.12,0,0.39,0',\n  sinusoidalOut: '0.61,1,0.88,1',\n  sinusoidalInOut: '0.37,0,0.63,1',\n  exponentialIn: '0.7,0,0.84,0',\n  exponentialOut: '0.16,1,0.3,1',\n  exponentialInOut: '0.87,0,0.13,1',\n  circularIn: '0.55,0,1,0.45',\n  circularOut: '0,0.55,0.45,1',\n  circularInOut: '0.85,0,0.15,1'\n};\nvar transformOriginKey = 'transform-origin';\nfunction buildPathString(el, kfShape, path) {\n  var shape = extend({}, el.shape);\n  extend(shape, kfShape);\n  el.buildPath(path, shape);\n  var svgPathBuilder = new SVGPathRebuilder();\n  svgPathBuilder.reset(getPathPrecision(el));\n  path.rebuildPath(svgPathBuilder, 1);\n  svgPathBuilder.generateStr();\n  return svgPathBuilder.getStr();\n}\nfunction setTransformOrigin(target, transform) {\n  var originX = transform.originX,\n    originY = transform.originY;\n  if (originX || originY) {\n    target[transformOriginKey] = originX + \"px \" + originY + \"px\";\n  }\n}\nexport var ANIMATE_STYLE_MAP = {\n  fill: 'fill',\n  opacity: 'opacity',\n  lineWidth: 'stroke-width',\n  lineDashOffset: 'stroke-dashoffset'\n};\nfunction addAnimation(cssAnim, scope) {\n  var animationName = scope.zrId + '-ani-' + scope.cssAnimIdx++;\n  scope.cssAnims[animationName] = cssAnim;\n  return animationName;\n}\nfunction createCompoundPathCSSAnimation(el, attrs, scope) {\n  var paths = el.shape.paths;\n  var composedAnim = {};\n  var cssAnimationCfg;\n  var cssAnimationName;\n  each(paths, function (path) {\n    var subScope = createBrushScope(scope.zrId);\n    subScope.animation = true;\n    createCSSAnimation(path, {}, subScope, true);\n    var cssAnims = subScope.cssAnims;\n    var cssNodes = subScope.cssNodes;\n    var animNames = keys(cssAnims);\n    var len = animNames.length;\n    if (!len) {\n      return;\n    }\n    cssAnimationName = animNames[len - 1];\n    var lastAnim = cssAnims[cssAnimationName];\n    for (var percent in lastAnim) {\n      var kf = lastAnim[percent];\n      composedAnim[percent] = composedAnim[percent] || {\n        d: ''\n      };\n      composedAnim[percent].d += kf.d || '';\n    }\n    for (var className in cssNodes) {\n      var val = cssNodes[className].animation;\n      if (val.indexOf(cssAnimationName) >= 0) {\n        cssAnimationCfg = val;\n      }\n    }\n  });\n  if (!cssAnimationCfg) {\n    return;\n  }\n  attrs.d = false;\n  var animationName = addAnimation(composedAnim, scope);\n  return cssAnimationCfg.replace(cssAnimationName, animationName);\n}\nfunction getEasingFunc(easing) {\n  return isString(easing) ? EASING_MAP[easing] ? \"cubic-bezier(\" + EASING_MAP[easing] + \")\" : createCubicEasingFunc(easing) ? easing : '' : '';\n}\nexport function createCSSAnimation(el, attrs, scope, onlyShape) {\n  var animators = el.animators;\n  var len = animators.length;\n  var cssAnimations = [];\n  if (el instanceof CompoundPath) {\n    var animationCfg = createCompoundPathCSSAnimation(el, attrs, scope);\n    if (animationCfg) {\n      cssAnimations.push(animationCfg);\n    } else if (!len) {\n      return;\n    }\n  } else if (!len) {\n    return;\n  }\n  var groupAnimators = {};\n  for (var i = 0; i < len; i++) {\n    var animator = animators[i];\n    var cfgArr = [animator.getMaxTime() / 1000 + 's'];\n    var easing = getEasingFunc(animator.getClip().easing);\n    var delay = animator.getDelay();\n    if (easing) {\n      cfgArr.push(easing);\n    } else {\n      cfgArr.push('linear');\n    }\n    if (delay) {\n      cfgArr.push(delay / 1000 + 's');\n    }\n    if (animator.getLoop()) {\n      cfgArr.push('infinite');\n    }\n    var cfg = cfgArr.join(' ');\n    groupAnimators[cfg] = groupAnimators[cfg] || [cfg, []];\n    groupAnimators[cfg][1].push(animator);\n  }\n  function createSingleCSSAnimation(groupAnimator) {\n    var animators = groupAnimator[1];\n    var len = animators.length;\n    var transformKfs = {};\n    var shapeKfs = {};\n    var finalKfs = {};\n    var animationTimingFunctionAttrName = 'animation-timing-function';\n    function saveAnimatorTrackToCssKfs(animator, cssKfs, toCssAttrName) {\n      var tracks = animator.getTracks();\n      var maxTime = animator.getMaxTime();\n      for (var k = 0; k < tracks.length; k++) {\n        var track = tracks[k];\n        if (track.needsAnimate()) {\n          var kfs = track.keyframes;\n          var attrName = track.propName;\n          toCssAttrName && (attrName = toCssAttrName(attrName));\n          if (attrName) {\n            for (var i = 0; i < kfs.length; i++) {\n              var kf = kfs[i];\n              var percent = Math.round(kf.time / maxTime * 100) + '%';\n              var kfEasing = getEasingFunc(kf.easing);\n              var rawValue = kf.rawValue;\n              if (isString(rawValue) || isNumber(rawValue)) {\n                cssKfs[percent] = cssKfs[percent] || {};\n                cssKfs[percent][attrName] = kf.rawValue;\n                if (kfEasing) {\n                  cssKfs[percent][animationTimingFunctionAttrName] = kfEasing;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    for (var i = 0; i < len; i++) {\n      var animator = animators[i];\n      var targetProp = animator.targetName;\n      if (!targetProp) {\n        !onlyShape && saveAnimatorTrackToCssKfs(animator, transformKfs);\n      } else if (targetProp === 'shape') {\n        saveAnimatorTrackToCssKfs(animator, shapeKfs);\n      }\n    }\n    for (var percent in transformKfs) {\n      var transform = {};\n      copyTransform(transform, el);\n      extend(transform, transformKfs[percent]);\n      var str = getSRTTransformString(transform);\n      var timingFunction = transformKfs[percent][animationTimingFunctionAttrName];\n      finalKfs[percent] = str ? {\n        transform: str\n      } : {};\n      setTransformOrigin(finalKfs[percent], transform);\n      if (timingFunction) {\n        finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n      }\n    }\n    ;\n    var path;\n    var canAnimateShape = true;\n    for (var percent in shapeKfs) {\n      finalKfs[percent] = finalKfs[percent] || {};\n      var isFirst = !path;\n      var timingFunction = shapeKfs[percent][animationTimingFunctionAttrName];\n      if (isFirst) {\n        path = new PathProxy();\n      }\n      var len_1 = path.len();\n      path.reset();\n      finalKfs[percent].d = buildPathString(el, shapeKfs[percent], path);\n      var newLen = path.len();\n      if (!isFirst && len_1 !== newLen) {\n        canAnimateShape = false;\n        break;\n      }\n      if (timingFunction) {\n        finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n      }\n    }\n    ;\n    if (!canAnimateShape) {\n      for (var percent in finalKfs) {\n        delete finalKfs[percent].d;\n      }\n    }\n    if (!onlyShape) {\n      for (var i = 0; i < len; i++) {\n        var animator = animators[i];\n        var targetProp = animator.targetName;\n        if (targetProp === 'style') {\n          saveAnimatorTrackToCssKfs(animator, finalKfs, function (propName) {\n            return ANIMATE_STYLE_MAP[propName];\n          });\n        }\n      }\n    }\n    var percents = keys(finalKfs);\n    var allTransformOriginSame = true;\n    var transformOrigin;\n    for (var i = 1; i < percents.length; i++) {\n      var p0 = percents[i - 1];\n      var p1 = percents[i];\n      if (finalKfs[p0][transformOriginKey] !== finalKfs[p1][transformOriginKey]) {\n        allTransformOriginSame = false;\n        break;\n      }\n      transformOrigin = finalKfs[p0][transformOriginKey];\n    }\n    if (allTransformOriginSame && transformOrigin) {\n      for (var percent in finalKfs) {\n        if (finalKfs[percent][transformOriginKey]) {\n          delete finalKfs[percent][transformOriginKey];\n        }\n      }\n      attrs[transformOriginKey] = transformOrigin;\n    }\n    if (filter(percents, function (percent) {\n      return keys(finalKfs[percent]).length > 0;\n    }).length) {\n      var animationName = addAnimation(finalKfs, scope);\n      return animationName + \" \" + groupAnimator[0] + \" both\";\n    }\n  }\n  for (var key in groupAnimators) {\n    var animationCfg = createSingleCSSAnimation(groupAnimators[key]);\n    if (animationCfg) {\n      cssAnimations.push(animationCfg);\n    }\n  }\n  if (cssAnimations.length) {\n    var className = scope.zrId + '-cls-' + scope.cssClassIdx++;\n    scope.cssNodes['.' + className] = {\n      animation: cssAnimations.join(',')\n    };\n    attrs[\"class\"] = className;\n  }\n}", "import { adjustTextY, getIdURL, getMatrixStr, getPathPrecision, getShadow<PERSON>ey, getSRTTransformString, hasShadow, isAroundZero, isGradient, isImagePattern, isLinearGradient, isPattern, isRadialGradient, normalizeColor, round4, TEXT_ALIGN_TO_ANCHOR } from './helper.js';\nimport Path from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineHeight } from '../contain/text.js';\nimport TSpan from '../graphic/TSpan.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport mapStyleToAttrs from './mapStyleToAttrs.js';\nimport { createVNode, vNodeToString } from './core.js';\nimport { assert, clone, isFunction, isString, logError, map, retrieve2 } from '../core/util.js';\nimport { createOrUpdateImage } from '../graphic/helper/image.js';\nimport { createCSSAnimation } from './cssAnimation.js';\nimport { hasSeparateFont, parseFontSize } from '../graphic/Text.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_FAMILY } from '../core/platform.js';\nvar round = Math.round;\nfunction isImageLike(val) {\n  return val && isString(val.src);\n}\nfunction isCanvasLike(val) {\n  return val && isFunction(val.toDataURL);\n}\nfunction setStyleAttrs(attrs, style, el, scope) {\n  mapStyleToAttrs(function (key, val) {\n    var isFillStroke = key === 'fill' || key === 'stroke';\n    if (isFillStroke && isGradient(val)) {\n      setGradient(style, attrs, key, scope);\n    } else if (isFillStroke && isPattern(val)) {\n      setPattern(el, attrs, key, scope);\n    } else {\n      attrs[key] = val;\n    }\n  }, style, el, false);\n  setShadow(el, attrs, scope);\n}\nfunction noRotateScale(m) {\n  return isAroundZero(m[0] - 1) && isAroundZero(m[1]) && isAroundZero(m[2]) && isAroundZero(m[3] - 1);\n}\nfunction noTranslate(m) {\n  return isAroundZero(m[4]) && isAroundZero(m[5]);\n}\nfunction setTransform(attrs, m, compress) {\n  if (m && !(noTranslate(m) && noRotateScale(m))) {\n    var mul = compress ? 10 : 1e4;\n    attrs.transform = noRotateScale(m) ? \"translate(\" + round(m[4] * mul) / mul + \" \" + round(m[5] * mul) / mul + \")\" : getMatrixStr(m);\n  }\n}\nfunction convertPolyShape(shape, attrs, mul) {\n  var points = shape.points;\n  var strArr = [];\n  for (var i = 0; i < points.length; i++) {\n    strArr.push(round(points[i][0] * mul) / mul);\n    strArr.push(round(points[i][1] * mul) / mul);\n  }\n  attrs.points = strArr.join(' ');\n}\nfunction validatePolyShape(shape) {\n  return !shape.smooth;\n}\nfunction createAttrsConvert(desc) {\n  var normalizedDesc = map(desc, function (item) {\n    return typeof item === 'string' ? [item, item] : item;\n  });\n  return function (shape, attrs, mul) {\n    for (var i = 0; i < normalizedDesc.length; i++) {\n      var item = normalizedDesc[i];\n      var val = shape[item[0]];\n      if (val != null) {\n        attrs[item[1]] = round(val * mul) / mul;\n      }\n    }\n  };\n}\nvar builtinShapesDef = {\n  circle: [createAttrsConvert(['cx', 'cy', 'r'])],\n  polyline: [convertPolyShape, validatePolyShape],\n  polygon: [convertPolyShape, validatePolyShape]\n};\nfunction hasShapeAnimation(el) {\n  var animators = el.animators;\n  for (var i = 0; i < animators.length; i++) {\n    if (animators[i].targetName === 'shape') {\n      return true;\n    }\n  }\n  return false;\n}\nexport function brushSVGPath(el, scope) {\n  var style = el.style;\n  var shape = el.shape;\n  var builtinShpDef = builtinShapesDef[el.type];\n  var attrs = {};\n  var needsAnimate = scope.animation;\n  var svgElType = 'path';\n  var strokePercent = el.style.strokePercent;\n  var precision = scope.compress && getPathPrecision(el) || 4;\n  if (builtinShpDef && !scope.willUpdate && !(builtinShpDef[1] && !builtinShpDef[1](shape)) && !(needsAnimate && hasShapeAnimation(el)) && !(strokePercent < 1)) {\n    svgElType = el.type;\n    var mul = Math.pow(10, precision);\n    builtinShpDef[0](shape, attrs, mul);\n  } else {\n    var needBuildPath = !el.path || el.shapeChanged();\n    if (!el.path) {\n      el.createPathProxy();\n    }\n    var path = el.path;\n    if (needBuildPath) {\n      path.beginPath();\n      el.buildPath(path, el.shape);\n      el.pathUpdated();\n    }\n    var pathVersion = path.getVersion();\n    var elExt = el;\n    var svgPathBuilder = elExt.__svgPathBuilder;\n    if (elExt.__svgPathVersion !== pathVersion || !svgPathBuilder || strokePercent !== elExt.__svgPathStrokePercent) {\n      if (!svgPathBuilder) {\n        svgPathBuilder = elExt.__svgPathBuilder = new SVGPathRebuilder();\n      }\n      svgPathBuilder.reset(precision);\n      path.rebuildPath(svgPathBuilder, strokePercent);\n      svgPathBuilder.generateStr();\n      elExt.__svgPathVersion = pathVersion;\n      elExt.__svgPathStrokePercent = strokePercent;\n    }\n    attrs.d = svgPathBuilder.getStr();\n  }\n  setTransform(attrs, el.transform);\n  setStyleAttrs(attrs, style, el, scope);\n  scope.animation && createCSSAnimation(el, attrs, scope);\n  return createVNode(svgElType, el.id + '', attrs);\n}\nexport function brushSVGImage(el, scope) {\n  var style = el.style;\n  var image = style.image;\n  if (image && !isString(image)) {\n    if (isImageLike(image)) {\n      image = image.src;\n    } else if (isCanvasLike(image)) {\n      image = image.toDataURL();\n    }\n  }\n  if (!image) {\n    return;\n  }\n  var x = style.x || 0;\n  var y = style.y || 0;\n  var dw = style.width;\n  var dh = style.height;\n  var attrs = {\n    href: image,\n    width: dw,\n    height: dh\n  };\n  if (x) {\n    attrs.x = x;\n  }\n  if (y) {\n    attrs.y = y;\n  }\n  setTransform(attrs, el.transform);\n  setStyleAttrs(attrs, style, el, scope);\n  scope.animation && createCSSAnimation(el, attrs, scope);\n  return createVNode('image', el.id + '', attrs);\n}\n;\nexport function brushSVGTSpan(el, scope) {\n  var style = el.style;\n  var text = style.text;\n  text != null && (text += '');\n  if (!text || isNaN(style.x) || isNaN(style.y)) {\n    return;\n  }\n  var font = style.font || DEFAULT_FONT;\n  var x = style.x || 0;\n  var y = adjustTextY(style.y || 0, getLineHeight(font), style.textBaseline);\n  var textAlign = TEXT_ALIGN_TO_ANCHOR[style.textAlign] || style.textAlign;\n  var attrs = {\n    'dominant-baseline': 'central',\n    'text-anchor': textAlign\n  };\n  if (hasSeparateFont(style)) {\n    var separatedFontStr = '';\n    var fontStyle = style.fontStyle;\n    var fontSize = parseFontSize(style.fontSize);\n    if (!parseFloat(fontSize)) {\n      return;\n    }\n    var fontFamily = style.fontFamily || DEFAULT_FONT_FAMILY;\n    var fontWeight = style.fontWeight;\n    separatedFontStr += \"font-size:\" + fontSize + \";font-family:\" + fontFamily + \";\";\n    if (fontStyle && fontStyle !== 'normal') {\n      separatedFontStr += \"font-style:\" + fontStyle + \";\";\n    }\n    if (fontWeight && fontWeight !== 'normal') {\n      separatedFontStr += \"font-weight:\" + fontWeight + \";\";\n    }\n    attrs.style = separatedFontStr;\n  } else {\n    attrs.style = \"font: \" + font;\n  }\n  if (text.match(/\\s/)) {\n    attrs['xml:space'] = 'preserve';\n  }\n  if (x) {\n    attrs.x = x;\n  }\n  if (y) {\n    attrs.y = y;\n  }\n  setTransform(attrs, el.transform);\n  setStyleAttrs(attrs, style, el, scope);\n  scope.animation && createCSSAnimation(el, attrs, scope);\n  return createVNode('text', el.id + '', attrs, undefined, text);\n}\nexport function brush(el, scope) {\n  if (el instanceof Path) {\n    return brushSVGPath(el, scope);\n  } else if (el instanceof ZRImage) {\n    return brushSVGImage(el, scope);\n  } else if (el instanceof TSpan) {\n    return brushSVGTSpan(el, scope);\n  }\n}\nfunction setShadow(el, attrs, scope) {\n  var style = el.style;\n  if (hasShadow(style)) {\n    var shadowKey = getShadowKey(el);\n    var shadowCache = scope.shadowCache;\n    var shadowId = shadowCache[shadowKey];\n    if (!shadowId) {\n      var globalScale = el.getGlobalScale();\n      var scaleX = globalScale[0];\n      var scaleY = globalScale[1];\n      if (!scaleX || !scaleY) {\n        return;\n      }\n      var offsetX = style.shadowOffsetX || 0;\n      var offsetY = style.shadowOffsetY || 0;\n      var blur_1 = style.shadowBlur;\n      var _a = normalizeColor(style.shadowColor),\n        opacity = _a.opacity,\n        color = _a.color;\n      var stdDx = blur_1 / 2 / scaleX;\n      var stdDy = blur_1 / 2 / scaleY;\n      var stdDeviation = stdDx + ' ' + stdDy;\n      shadowId = scope.zrId + '-s' + scope.shadowIdx++;\n      scope.defs[shadowId] = createVNode('filter', shadowId, {\n        'id': shadowId,\n        'x': '-100%',\n        'y': '-100%',\n        'width': '300%',\n        'height': '300%'\n      }, [createVNode('feDropShadow', '', {\n        'dx': offsetX / scaleX,\n        'dy': offsetY / scaleY,\n        'stdDeviation': stdDeviation,\n        'flood-color': color,\n        'flood-opacity': opacity\n      })]);\n      shadowCache[shadowKey] = shadowId;\n    }\n    attrs.filter = getIdURL(shadowId);\n  }\n}\nexport function setGradient(style, attrs, target, scope) {\n  var val = style[target];\n  var gradientTag;\n  var gradientAttrs = {\n    'gradientUnits': val.global ? 'userSpaceOnUse' : 'objectBoundingBox'\n  };\n  if (isLinearGradient(val)) {\n    gradientTag = 'linearGradient';\n    gradientAttrs.x1 = val.x;\n    gradientAttrs.y1 = val.y;\n    gradientAttrs.x2 = val.x2;\n    gradientAttrs.y2 = val.y2;\n  } else if (isRadialGradient(val)) {\n    gradientTag = 'radialGradient';\n    gradientAttrs.cx = retrieve2(val.x, 0.5);\n    gradientAttrs.cy = retrieve2(val.y, 0.5);\n    gradientAttrs.r = retrieve2(val.r, 0.5);\n  } else {\n    if (process.env.NODE_ENV !== 'production') {\n      logError('Illegal gradient type.');\n    }\n    return;\n  }\n  var colors = val.colorStops;\n  var colorStops = [];\n  for (var i = 0, len = colors.length; i < len; ++i) {\n    var offset = round4(colors[i].offset) * 100 + '%';\n    var stopColor = colors[i].color;\n    var _a = normalizeColor(stopColor),\n      color = _a.color,\n      opacity = _a.opacity;\n    var stopsAttrs = {\n      'offset': offset\n    };\n    stopsAttrs['stop-color'] = color;\n    if (opacity < 1) {\n      stopsAttrs['stop-opacity'] = opacity;\n    }\n    colorStops.push(createVNode('stop', i + '', stopsAttrs));\n  }\n  var gradientVNode = createVNode(gradientTag, '', gradientAttrs, colorStops);\n  var gradientKey = vNodeToString(gradientVNode);\n  var gradientCache = scope.gradientCache;\n  var gradientId = gradientCache[gradientKey];\n  if (!gradientId) {\n    gradientId = scope.zrId + '-g' + scope.gradientIdx++;\n    gradientCache[gradientKey] = gradientId;\n    gradientAttrs.id = gradientId;\n    scope.defs[gradientId] = createVNode(gradientTag, gradientId, gradientAttrs, colorStops);\n  }\n  attrs[target] = getIdURL(gradientId);\n}\nexport function setPattern(el, attrs, target, scope) {\n  var val = el.style[target];\n  var boundingRect = el.getBoundingRect();\n  var patternAttrs = {};\n  var repeat = val.repeat;\n  var noRepeat = repeat === 'no-repeat';\n  var repeatX = repeat === 'repeat-x';\n  var repeatY = repeat === 'repeat-y';\n  var child;\n  if (isImagePattern(val)) {\n    var imageWidth_1 = val.imageWidth;\n    var imageHeight_1 = val.imageHeight;\n    var imageSrc = void 0;\n    var patternImage = val.image;\n    if (isString(patternImage)) {\n      imageSrc = patternImage;\n    } else if (isImageLike(patternImage)) {\n      imageSrc = patternImage.src;\n    } else if (isCanvasLike(patternImage)) {\n      imageSrc = patternImage.toDataURL();\n    }\n    if (typeof Image === 'undefined') {\n      var errMsg = 'Image width/height must been given explictly in svg-ssr renderer.';\n      assert(imageWidth_1, errMsg);\n      assert(imageHeight_1, errMsg);\n    } else if (imageWidth_1 == null || imageHeight_1 == null) {\n      var setSizeToVNode_1 = function (vNode, img) {\n        if (vNode) {\n          var svgEl = vNode.elm;\n          var width = imageWidth_1 || img.width;\n          var height = imageHeight_1 || img.height;\n          if (vNode.tag === 'pattern') {\n            if (repeatX) {\n              height = 1;\n              width /= boundingRect.width;\n            } else if (repeatY) {\n              width = 1;\n              height /= boundingRect.height;\n            }\n          }\n          vNode.attrs.width = width;\n          vNode.attrs.height = height;\n          if (svgEl) {\n            svgEl.setAttribute('width', width);\n            svgEl.setAttribute('height', height);\n          }\n        }\n      };\n      var createdImage = createOrUpdateImage(imageSrc, null, el, function (img) {\n        noRepeat || setSizeToVNode_1(patternVNode, img);\n        setSizeToVNode_1(child, img);\n      });\n      if (createdImage && createdImage.width && createdImage.height) {\n        imageWidth_1 = imageWidth_1 || createdImage.width;\n        imageHeight_1 = imageHeight_1 || createdImage.height;\n      }\n    }\n    child = createVNode('image', 'img', {\n      href: imageSrc,\n      width: imageWidth_1,\n      height: imageHeight_1\n    });\n    patternAttrs.width = imageWidth_1;\n    patternAttrs.height = imageHeight_1;\n  } else if (val.svgElement) {\n    child = clone(val.svgElement);\n    patternAttrs.width = val.svgWidth;\n    patternAttrs.height = val.svgHeight;\n  }\n  if (!child) {\n    return;\n  }\n  var patternWidth;\n  var patternHeight;\n  if (noRepeat) {\n    patternWidth = patternHeight = 1;\n  } else if (repeatX) {\n    patternHeight = 1;\n    patternWidth = patternAttrs.width / boundingRect.width;\n  } else if (repeatY) {\n    patternWidth = 1;\n    patternHeight = patternAttrs.height / boundingRect.height;\n  } else {\n    patternAttrs.patternUnits = 'userSpaceOnUse';\n  }\n  if (patternWidth != null && !isNaN(patternWidth)) {\n    patternAttrs.width = patternWidth;\n  }\n  if (patternHeight != null && !isNaN(patternHeight)) {\n    patternAttrs.height = patternHeight;\n  }\n  var patternTransform = getSRTTransformString(val);\n  patternTransform && (patternAttrs.patternTransform = patternTransform);\n  var patternVNode = createVNode('pattern', '', patternAttrs, [child]);\n  var patternKey = vNodeToString(patternVNode);\n  var patternCache = scope.patternCache;\n  var patternId = patternCache[patternKey];\n  if (!patternId) {\n    patternId = scope.zrId + '-p' + scope.patternIdx++;\n    patternCache[patternKey] = patternId;\n    patternAttrs.id = patternId;\n    patternVNode = scope.defs[patternId] = createVNode('pattern', patternId, patternAttrs, [child]);\n  }\n  attrs[target] = getIdURL(patternId);\n}\nexport function setClipPath(clipPath, attrs, scope) {\n  var clipPathCache = scope.clipPathCache,\n    defs = scope.defs;\n  var clipPathId = clipPathCache[clipPath.id];\n  if (!clipPathId) {\n    clipPathId = scope.zrId + '-c' + scope.clipPathIdx++;\n    var clipPathAttrs = {\n      id: clipPathId\n    };\n    clipPathCache[clipPath.id] = clipPathId;\n    defs[clipPathId] = createVNode('clipPath', clipPathId, clipPathAttrs, [brushSVGPath(clipPath, scope)]);\n  }\n  attrs['clip-path'] = getIdURL(clipPathId);\n}", "export function createTextNode(text) {\n  return document.createTextNode(text);\n}\nexport function createComment(text) {\n  return document.createComment(text);\n}\nexport function insertBefore(parentNode, newNode, referenceNode) {\n  parentNode.insertBefore(newNode, referenceNode);\n}\nexport function removeChild(node, child) {\n  node.removeChild(child);\n}\nexport function appendChild(node, child) {\n  node.appendChild(child);\n}\nexport function parentNode(node) {\n  return node.parentNode;\n}\nexport function nextSibling(node) {\n  return node.nextSibling;\n}\nexport function tagName(elm) {\n  return elm.tagName;\n}\nexport function setTextContent(node, text) {\n  node.textContent = text;\n}\nexport function getTextContent(node) {\n  return node.textContent;\n}\nexport function isElement(node) {\n  return node.nodeType === 1;\n}\nexport function isText(node) {\n  return node.nodeType === 3;\n}\nexport function isComment(node) {\n  return node.nodeType === 8;\n}", "import { isArray, isObject } from '../core/util.js';\nimport { createElement, createVNode, XMLNS, XML_NAMESPACE, XLINKNS } from './core.js';\nimport * as api from './domapi.js';\nvar colonChar = 58;\nvar xChar = 120;\nvar emptyNode = createVNode('', '');\nfunction isUndef(s) {\n  return s === undefined;\n}\nfunction isDef(s) {\n  return s !== undefined;\n}\nfunction createKeyToOldIdx(children, beginIdx, endIdx) {\n  var map = {};\n  for (var i = beginIdx; i <= endIdx; ++i) {\n    var key = children[i].key;\n    if (key !== undefined) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (map[key] != null) {\n          console.error(\"Duplicate key \" + key);\n        }\n      }\n      map[key] = i;\n    }\n  }\n  return map;\n}\nfunction sameVnode(vnode1, vnode2) {\n  var isSameKey = vnode1.key === vnode2.key;\n  var isSameTag = vnode1.tag === vnode2.tag;\n  return isSameTag && isSameKey;\n}\nfunction createElm(vnode) {\n  var i;\n  var children = vnode.children;\n  var tag = vnode.tag;\n  if (isDef(tag)) {\n    var elm = vnode.elm = createElement(tag);\n    updateAttrs(emptyNode, vnode);\n    if (isArray(children)) {\n      for (i = 0; i < children.length; ++i) {\n        var ch = children[i];\n        if (ch != null) {\n          api.appendChild(elm, createElm(ch));\n        }\n      }\n    } else if (isDef(vnode.text) && !isObject(vnode.text)) {\n      api.appendChild(elm, api.createTextNode(vnode.text));\n    }\n  } else {\n    vnode.elm = api.createTextNode(vnode.text);\n  }\n  return vnode.elm;\n}\nfunction addVnodes(parentElm, before, vnodes, startIdx, endIdx) {\n  for (; startIdx <= endIdx; ++startIdx) {\n    var ch = vnodes[startIdx];\n    if (ch != null) {\n      api.insertBefore(parentElm, createElm(ch), before);\n    }\n  }\n}\nfunction removeVnodes(parentElm, vnodes, startIdx, endIdx) {\n  for (; startIdx <= endIdx; ++startIdx) {\n    var ch = vnodes[startIdx];\n    if (ch != null) {\n      if (isDef(ch.tag)) {\n        var parent_1 = api.parentNode(ch.elm);\n        api.removeChild(parent_1, ch.elm);\n      } else {\n        api.removeChild(parentElm, ch.elm);\n      }\n    }\n  }\n}\nexport function updateAttrs(oldVnode, vnode) {\n  var key;\n  var elm = vnode.elm;\n  var oldAttrs = oldVnode && oldVnode.attrs || {};\n  var attrs = vnode.attrs || {};\n  if (oldAttrs === attrs) {\n    return;\n  }\n  for (key in attrs) {\n    var cur = attrs[key];\n    var old = oldAttrs[key];\n    if (old !== cur) {\n      if (cur === true) {\n        elm.setAttribute(key, '');\n      } else if (cur === false) {\n        elm.removeAttribute(key);\n      } else {\n        if (key.charCodeAt(0) !== xChar) {\n          elm.setAttribute(key, cur);\n        } else if (key === 'xmlns:xlink' || key === 'xmlns') {\n          elm.setAttributeNS(XMLNS, key, cur);\n        } else if (key.charCodeAt(3) === colonChar) {\n          elm.setAttributeNS(XML_NAMESPACE, key, cur);\n        } else if (key.charCodeAt(5) === colonChar) {\n          elm.setAttributeNS(XLINKNS, key, cur);\n        } else {\n          elm.setAttribute(key, cur);\n        }\n      }\n    }\n  }\n  for (key in oldAttrs) {\n    if (!(key in attrs)) {\n      elm.removeAttribute(key);\n    }\n  }\n}\nfunction updateChildren(parentElm, oldCh, newCh) {\n  var oldStartIdx = 0;\n  var newStartIdx = 0;\n  var oldEndIdx = oldCh.length - 1;\n  var oldStartVnode = oldCh[0];\n  var oldEndVnode = oldCh[oldEndIdx];\n  var newEndIdx = newCh.length - 1;\n  var newStartVnode = newCh[0];\n  var newEndVnode = newCh[newEndIdx];\n  var oldKeyToIdx;\n  var idxInOld;\n  var elmToMove;\n  var before;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (sameVnode(oldStartVnode, newStartVnode)) {\n      patchVnode(oldStartVnode, newStartVnode);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (sameVnode(oldEndVnode, newEndVnode)) {\n      patchVnode(oldEndVnode, newEndVnode);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (sameVnode(oldStartVnode, newEndVnode)) {\n      patchVnode(oldStartVnode, newEndVnode);\n      api.insertBefore(parentElm, oldStartVnode.elm, api.nextSibling(oldEndVnode.elm));\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (sameVnode(oldEndVnode, newStartVnode)) {\n      patchVnode(oldEndVnode, newStartVnode);\n      api.insertBefore(parentElm, oldEndVnode.elm, oldStartVnode.elm);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      if (isUndef(oldKeyToIdx)) {\n        oldKeyToIdx = createKeyToOldIdx(oldCh, oldStartIdx, oldEndIdx);\n      }\n      idxInOld = oldKeyToIdx[newStartVnode.key];\n      if (isUndef(idxInOld)) {\n        api.insertBefore(parentElm, createElm(newStartVnode), oldStartVnode.elm);\n      } else {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.tag !== newStartVnode.tag) {\n          api.insertBefore(parentElm, createElm(newStartVnode), oldStartVnode.elm);\n        } else {\n          patchVnode(elmToMove, newStartVnode);\n          oldCh[idxInOld] = undefined;\n          api.insertBefore(parentElm, elmToMove.elm, oldStartVnode.elm);\n        }\n      }\n      newStartVnode = newCh[++newStartIdx];\n    }\n  }\n  if (oldStartIdx <= oldEndIdx || newStartIdx <= newEndIdx) {\n    if (oldStartIdx > oldEndIdx) {\n      before = newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].elm;\n      addVnodes(parentElm, before, newCh, newStartIdx, newEndIdx);\n    } else {\n      removeVnodes(parentElm, oldCh, oldStartIdx, oldEndIdx);\n    }\n  }\n}\nfunction patchVnode(oldVnode, vnode) {\n  var elm = vnode.elm = oldVnode.elm;\n  var oldCh = oldVnode.children;\n  var ch = vnode.children;\n  if (oldVnode === vnode) {\n    return;\n  }\n  updateAttrs(oldVnode, vnode);\n  if (isUndef(vnode.text)) {\n    if (isDef(oldCh) && isDef(ch)) {\n      if (oldCh !== ch) {\n        updateChildren(elm, oldCh, ch);\n      }\n    } else if (isDef(ch)) {\n      if (isDef(oldVnode.text)) {\n        api.setTextContent(elm, '');\n      }\n      addVnodes(elm, null, ch, 0, ch.length - 1);\n    } else if (isDef(oldCh)) {\n      removeVnodes(elm, oldCh, 0, oldCh.length - 1);\n    } else if (isDef(oldVnode.text)) {\n      api.setTextContent(elm, '');\n    }\n  } else if (oldVnode.text !== vnode.text) {\n    if (isDef(oldCh)) {\n      removeVnodes(elm, oldCh, 0, oldCh.length - 1);\n    }\n    api.setTextContent(elm, vnode.text);\n  }\n}\nexport default function patch(oldVnode, vnode) {\n  if (sameVnode(oldVnode, vnode)) {\n    patchVnode(oldVnode, vnode);\n  } else {\n    var elm = oldVnode.elm;\n    var parent_2 = api.parentNode(elm);\n    createElm(vnode);\n    if (parent_2 !== null) {\n      api.insertBefore(parent_2, vnode.elm, api.nextSibling(elm));\n      removeVnodes(parent_2, [oldVnode], 0, 0);\n    }\n  }\n  return vnode;\n}", "import { brush, setClipPath, setGradient, setPattern } from './graphic.js';\nimport { createElement, createVNode, vNodeToString, getCssString, createBrushScope, createSVGVNode } from './core.js';\nimport { normalizeColor, encodeBase64, isGradient, isPattern } from './helper.js';\nimport { extend, keys, logError, map, noop, retrieve2 } from '../core/util.js';\nimport patch, { updateAttrs } from './patch.js';\nimport { getSize } from '../canvas/helper.js';\nvar svgId = 0;\nvar SVGPainter = function () {\n  function SVGPainter(root, storage, opts) {\n    this.type = 'svg';\n    this.refreshHover = createMethodNotSupport('refreshHover');\n    this.configLayer = createMethodNotSupport('configLayer');\n    this.storage = storage;\n    this._opts = opts = extend({}, opts);\n    this.root = root;\n    this._id = 'zr' + svgId++;\n    this._oldVNode = createSVGVNode(opts.width, opts.height);\n    if (root && !opts.ssr) {\n      var viewport = this._viewport = document.createElement('div');\n      viewport.style.cssText = 'position:relative;overflow:hidden';\n      var svgDom = this._svgDom = this._oldVNode.elm = createElement('svg');\n      updateAttrs(null, this._oldVNode);\n      viewport.appendChild(svgDom);\n      root.appendChild(viewport);\n    }\n    this.resize(opts.width, opts.height);\n  }\n  SVGPainter.prototype.getType = function () {\n    return this.type;\n  };\n  SVGPainter.prototype.getViewportRoot = function () {\n    return this._viewport;\n  };\n  SVGPainter.prototype.getViewportRootOffset = function () {\n    var viewportRoot = this.getViewportRoot();\n    if (viewportRoot) {\n      return {\n        offsetLeft: viewportRoot.offsetLeft || 0,\n        offsetTop: viewportRoot.offsetTop || 0\n      };\n    }\n  };\n  SVGPainter.prototype.getSvgDom = function () {\n    return this._svgDom;\n  };\n  SVGPainter.prototype.refresh = function () {\n    if (this.root) {\n      var vnode = this.renderToVNode({\n        willUpdate: true\n      });\n      vnode.attrs.style = 'position:absolute;left:0;top:0;user-select:none';\n      patch(this._oldVNode, vnode);\n      this._oldVNode = vnode;\n    }\n  };\n  SVGPainter.prototype.renderOneToVNode = function (el) {\n    return brush(el, createBrushScope(this._id));\n  };\n  SVGPainter.prototype.renderToVNode = function (opts) {\n    opts = opts || {};\n    var list = this.storage.getDisplayList(true);\n    var width = this._width;\n    var height = this._height;\n    var scope = createBrushScope(this._id);\n    scope.animation = opts.animation;\n    scope.willUpdate = opts.willUpdate;\n    scope.compress = opts.compress;\n    var children = [];\n    var bgVNode = this._bgVNode = createBackgroundVNode(width, height, this._backgroundColor, scope);\n    bgVNode && children.push(bgVNode);\n    var mainVNode = !opts.compress ? this._mainVNode = createVNode('g', 'main', {}, []) : null;\n    this._paintList(list, scope, mainVNode ? mainVNode.children : children);\n    mainVNode && children.push(mainVNode);\n    var defs = map(keys(scope.defs), function (id) {\n      return scope.defs[id];\n    });\n    if (defs.length) {\n      children.push(createVNode('defs', 'defs', {}, defs));\n    }\n    if (opts.animation) {\n      var animationCssStr = getCssString(scope.cssNodes, scope.cssAnims, {\n        newline: true\n      });\n      if (animationCssStr) {\n        var styleNode = createVNode('style', 'stl', {}, [], animationCssStr);\n        children.push(styleNode);\n      }\n    }\n    return createSVGVNode(width, height, children, opts.useViewBox);\n  };\n  SVGPainter.prototype.renderToString = function (opts) {\n    opts = opts || {};\n    return vNodeToString(this.renderToVNode({\n      animation: retrieve2(opts.cssAnimation, true),\n      willUpdate: false,\n      compress: true,\n      useViewBox: retrieve2(opts.useViewBox, true)\n    }), {\n      newline: true\n    });\n  };\n  SVGPainter.prototype.setBackgroundColor = function (backgroundColor) {\n    this._backgroundColor = backgroundColor;\n  };\n  SVGPainter.prototype.getSvgRoot = function () {\n    return this._mainVNode && this._mainVNode.elm;\n  };\n  SVGPainter.prototype._paintList = function (list, scope, out) {\n    var listLen = list.length;\n    var clipPathsGroupsStack = [];\n    var clipPathsGroupsStackDepth = 0;\n    var currentClipPathGroup;\n    var prevClipPaths;\n    var clipGroupNodeIdx = 0;\n    for (var i = 0; i < listLen; i++) {\n      var displayable = list[i];\n      if (!displayable.invisible) {\n        var clipPaths = displayable.__clipPaths;\n        var len = clipPaths && clipPaths.length || 0;\n        var prevLen = prevClipPaths && prevClipPaths.length || 0;\n        var lca = void 0;\n        for (lca = Math.max(len - 1, prevLen - 1); lca >= 0; lca--) {\n          if (clipPaths && prevClipPaths && clipPaths[lca] === prevClipPaths[lca]) {\n            break;\n          }\n        }\n        for (var i_1 = prevLen - 1; i_1 > lca; i_1--) {\n          clipPathsGroupsStackDepth--;\n          currentClipPathGroup = clipPathsGroupsStack[clipPathsGroupsStackDepth - 1];\n        }\n        for (var i_2 = lca + 1; i_2 < len; i_2++) {\n          var groupAttrs = {};\n          setClipPath(clipPaths[i_2], groupAttrs, scope);\n          var g = createVNode('g', 'clip-g-' + clipGroupNodeIdx++, groupAttrs, []);\n          (currentClipPathGroup ? currentClipPathGroup.children : out).push(g);\n          clipPathsGroupsStack[clipPathsGroupsStackDepth++] = g;\n          currentClipPathGroup = g;\n        }\n        prevClipPaths = clipPaths;\n        var ret = brush(displayable, scope);\n        if (ret) {\n          (currentClipPathGroup ? currentClipPathGroup.children : out).push(ret);\n        }\n      }\n    }\n  };\n  SVGPainter.prototype.resize = function (width, height) {\n    var opts = this._opts;\n    var root = this.root;\n    var viewport = this._viewport;\n    width != null && (opts.width = width);\n    height != null && (opts.height = height);\n    if (root && viewport) {\n      viewport.style.display = 'none';\n      width = getSize(root, 0, opts);\n      height = getSize(root, 1, opts);\n      viewport.style.display = '';\n    }\n    if (this._width !== width || this._height !== height) {\n      this._width = width;\n      this._height = height;\n      if (viewport) {\n        var viewportStyle = viewport.style;\n        viewportStyle.width = width + 'px';\n        viewportStyle.height = height + 'px';\n      }\n      if (!isPattern(this._backgroundColor)) {\n        var svgDom = this._svgDom;\n        if (svgDom) {\n          svgDom.setAttribute('width', width);\n          svgDom.setAttribute('height', height);\n        }\n        var bgEl = this._bgVNode && this._bgVNode.elm;\n        if (bgEl) {\n          bgEl.setAttribute('width', width);\n          bgEl.setAttribute('height', height);\n        }\n      } else {\n        this.refresh();\n      }\n    }\n  };\n  SVGPainter.prototype.getWidth = function () {\n    return this._width;\n  };\n  SVGPainter.prototype.getHeight = function () {\n    return this._height;\n  };\n  SVGPainter.prototype.dispose = function () {\n    if (this.root) {\n      this.root.innerHTML = '';\n    }\n    this._svgDom = this._viewport = this.storage = this._oldVNode = this._bgVNode = this._mainVNode = null;\n  };\n  SVGPainter.prototype.clear = function () {\n    if (this._svgDom) {\n      this._svgDom.innerHTML = null;\n    }\n    this._oldVNode = null;\n  };\n  SVGPainter.prototype.toDataURL = function (base64) {\n    var str = this.renderToString();\n    var prefix = 'data:image/svg+xml;';\n    if (base64) {\n      str = encodeBase64(str);\n      return str && prefix + 'base64,' + str;\n    }\n    return prefix + 'charset=UTF-8,' + encodeURIComponent(str);\n  };\n  return SVGPainter;\n}();\nfunction createMethodNotSupport(method) {\n  return function () {\n    if (process.env.NODE_ENV !== 'production') {\n      logError('In SVG mode painter not support method \"' + method + '\"');\n    }\n  };\n}\nfunction createBackgroundVNode(width, height, backgroundColor, scope) {\n  var bgVNode;\n  if (backgroundColor && backgroundColor !== 'none') {\n    bgVNode = createVNode('rect', 'bg', {\n      width: width,\n      height: height,\n      x: '0',\n      y: '0',\n      id: '0'\n    });\n    if (isGradient(backgroundColor)) {\n      setGradient({\n        fill: backgroundColor\n      }, bgVNode.attrs, 'fill', scope);\n    } else if (isPattern(backgroundColor)) {\n      setPattern({\n        style: {\n          fill: backgroundColor\n        },\n        dirty: noop,\n        getBoundingRect: function () {\n          return {\n            width: width,\n            height: height\n          };\n        }\n      }, bgVNode.attrs, 'fill', scope);\n    } else {\n      var _a = normalizeColor(backgroundColor),\n        color = _a.color,\n        opacity = _a.opacity;\n      bgVNode.attrs.fill = color;\n      opacity < 1 && (bgVNode.attrs['fill-opacity'] = opacity);\n    }\n  }\n  return bgVNode;\n}\nexport default SVGPainter;", "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport SVGPainter from 'zrender/lib/svg/Painter.js';\nexport function install(registers) {\n  registers.registerPainter('svg', SVGPainter);\n}", "import { __extends } from \"tslib\";\nimport * as util from '../core/util.js';\nimport { devicePixelRatio } from '../config.js';\nimport Eventful from '../core/Eventful.js';\nimport { getCanvasGradient } from './helper.js';\nimport { createCanvasPattern } from './graphic.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { platformApi } from '../core/platform.js';\nfunction createDom(id, painter, dpr) {\n  var newDom = platformApi.createCanvas();\n  var width = painter.getWidth();\n  var height = painter.getHeight();\n  var newDomStyle = newDom.style;\n  if (newDomStyle) {\n    newDomStyle.position = 'absolute';\n    newDomStyle.left = '0';\n    newDomStyle.top = '0';\n    newDomStyle.width = width + 'px';\n    newDomStyle.height = height + 'px';\n    newDom.setAttribute('data-zr-dom-id', id);\n  }\n  newDom.width = width * dpr;\n  newDom.height = height * dpr;\n  return newDom;\n}\n;\nvar Layer = function (_super) {\n  __extends(Layer, _super);\n  function Layer(id, painter, dpr) {\n    var _this = _super.call(this) || this;\n    _this.motionBlur = false;\n    _this.lastFrameAlpha = 0.7;\n    _this.dpr = 1;\n    _this.virtual = false;\n    _this.config = {};\n    _this.incremental = false;\n    _this.zlevel = 0;\n    _this.maxRepaintRectCount = 5;\n    _this.__dirty = true;\n    _this.__firstTimePaint = true;\n    _this.__used = false;\n    _this.__drawIndex = 0;\n    _this.__startIndex = 0;\n    _this.__endIndex = 0;\n    _this.__prevStartIndex = null;\n    _this.__prevEndIndex = null;\n    var dom;\n    dpr = dpr || devicePixelRatio;\n    if (typeof id === 'string') {\n      dom = createDom(id, painter, dpr);\n    } else if (util.isObject(id)) {\n      dom = id;\n      id = dom.id;\n    }\n    _this.id = id;\n    _this.dom = dom;\n    var domStyle = dom.style;\n    if (domStyle) {\n      util.disableUserSelect(dom);\n      dom.onselectstart = function () {\n        return false;\n      };\n      domStyle.padding = '0';\n      domStyle.margin = '0';\n      domStyle.borderWidth = '0';\n    }\n    _this.painter = painter;\n    _this.dpr = dpr;\n    return _this;\n  }\n  Layer.prototype.getElementCount = function () {\n    return this.__endIndex - this.__startIndex;\n  };\n  Layer.prototype.afterBrush = function () {\n    this.__prevStartIndex = this.__startIndex;\n    this.__prevEndIndex = this.__endIndex;\n  };\n  Layer.prototype.initContext = function () {\n    this.ctx = this.dom.getContext('2d');\n    this.ctx.dpr = this.dpr;\n  };\n  Layer.prototype.setUnpainted = function () {\n    this.__firstTimePaint = true;\n  };\n  Layer.prototype.createBackBuffer = function () {\n    var dpr = this.dpr;\n    this.domBack = createDom('back-' + this.id, this.painter, dpr);\n    this.ctxBack = this.domBack.getContext('2d');\n    if (dpr !== 1) {\n      this.ctxBack.scale(dpr, dpr);\n    }\n  };\n  Layer.prototype.createRepaintRects = function (displayList, prevList, viewWidth, viewHeight) {\n    if (this.__firstTimePaint) {\n      this.__firstTimePaint = false;\n      return null;\n    }\n    var mergedRepaintRects = [];\n    var maxRepaintRectCount = this.maxRepaintRectCount;\n    var full = false;\n    var pendingRect = new BoundingRect(0, 0, 0, 0);\n    function addRectToMergePool(rect) {\n      if (!rect.isFinite() || rect.isZero()) {\n        return;\n      }\n      if (mergedRepaintRects.length === 0) {\n        var boundingRect = new BoundingRect(0, 0, 0, 0);\n        boundingRect.copy(rect);\n        mergedRepaintRects.push(boundingRect);\n      } else {\n        var isMerged = false;\n        var minDeltaArea = Infinity;\n        var bestRectToMergeIdx = 0;\n        for (var i = 0; i < mergedRepaintRects.length; ++i) {\n          var mergedRect = mergedRepaintRects[i];\n          if (mergedRect.intersect(rect)) {\n            var pendingRect_1 = new BoundingRect(0, 0, 0, 0);\n            pendingRect_1.copy(mergedRect);\n            pendingRect_1.union(rect);\n            mergedRepaintRects[i] = pendingRect_1;\n            isMerged = true;\n            break;\n          } else if (full) {\n            pendingRect.copy(rect);\n            pendingRect.union(mergedRect);\n            var aArea = rect.width * rect.height;\n            var bArea = mergedRect.width * mergedRect.height;\n            var pendingArea = pendingRect.width * pendingRect.height;\n            var deltaArea = pendingArea - aArea - bArea;\n            if (deltaArea < minDeltaArea) {\n              minDeltaArea = deltaArea;\n              bestRectToMergeIdx = i;\n            }\n          }\n        }\n        if (full) {\n          mergedRepaintRects[bestRectToMergeIdx].union(rect);\n          isMerged = true;\n        }\n        if (!isMerged) {\n          var boundingRect = new BoundingRect(0, 0, 0, 0);\n          boundingRect.copy(rect);\n          mergedRepaintRects.push(boundingRect);\n        }\n        if (!full) {\n          full = mergedRepaintRects.length >= maxRepaintRectCount;\n        }\n      }\n    }\n    for (var i = this.__startIndex; i < this.__endIndex; ++i) {\n      var el = displayList[i];\n      if (el) {\n        var shouldPaint = el.shouldBePainted(viewWidth, viewHeight, true, true);\n        var prevRect = el.__isRendered && (el.__dirty & REDRAW_BIT || !shouldPaint) ? el.getPrevPaintRect() : null;\n        if (prevRect) {\n          addRectToMergePool(prevRect);\n        }\n        var curRect = shouldPaint && (el.__dirty & REDRAW_BIT || !el.__isRendered) ? el.getPaintRect() : null;\n        if (curRect) {\n          addRectToMergePool(curRect);\n        }\n      }\n    }\n    for (var i = this.__prevStartIndex; i < this.__prevEndIndex; ++i) {\n      var el = prevList[i];\n      var shouldPaint = el.shouldBePainted(viewWidth, viewHeight, true, true);\n      if (el && (!shouldPaint || !el.__zr) && el.__isRendered) {\n        var prevRect = el.getPrevPaintRect();\n        if (prevRect) {\n          addRectToMergePool(prevRect);\n        }\n      }\n    }\n    var hasIntersections;\n    do {\n      hasIntersections = false;\n      for (var i = 0; i < mergedRepaintRects.length;) {\n        if (mergedRepaintRects[i].isZero()) {\n          mergedRepaintRects.splice(i, 1);\n          continue;\n        }\n        for (var j = i + 1; j < mergedRepaintRects.length;) {\n          if (mergedRepaintRects[i].intersect(mergedRepaintRects[j])) {\n            hasIntersections = true;\n            mergedRepaintRects[i].union(mergedRepaintRects[j]);\n            mergedRepaintRects.splice(j, 1);\n          } else {\n            j++;\n          }\n        }\n        i++;\n      }\n    } while (hasIntersections);\n    this._paintRects = mergedRepaintRects;\n    return mergedRepaintRects;\n  };\n  Layer.prototype.debugGetPaintRects = function () {\n    return (this._paintRects || []).slice();\n  };\n  Layer.prototype.resize = function (width, height) {\n    var dpr = this.dpr;\n    var dom = this.dom;\n    var domStyle = dom.style;\n    var domBack = this.domBack;\n    if (domStyle) {\n      domStyle.width = width + 'px';\n      domStyle.height = height + 'px';\n    }\n    dom.width = width * dpr;\n    dom.height = height * dpr;\n    if (domBack) {\n      domBack.width = width * dpr;\n      domBack.height = height * dpr;\n      if (dpr !== 1) {\n        this.ctxBack.scale(dpr, dpr);\n      }\n    }\n  };\n  Layer.prototype.clear = function (clearAll, clearColor, repaintRects) {\n    var dom = this.dom;\n    var ctx = this.ctx;\n    var width = dom.width;\n    var height = dom.height;\n    clearColor = clearColor || this.clearColor;\n    var haveMotionBLur = this.motionBlur && !clearAll;\n    var lastFrameAlpha = this.lastFrameAlpha;\n    var dpr = this.dpr;\n    var self = this;\n    if (haveMotionBLur) {\n      if (!this.domBack) {\n        this.createBackBuffer();\n      }\n      this.ctxBack.globalCompositeOperation = 'copy';\n      this.ctxBack.drawImage(dom, 0, 0, width / dpr, height / dpr);\n    }\n    var domBack = this.domBack;\n    function doClear(x, y, width, height) {\n      ctx.clearRect(x, y, width, height);\n      if (clearColor && clearColor !== 'transparent') {\n        var clearColorGradientOrPattern = void 0;\n        if (util.isGradientObject(clearColor)) {\n          var shouldCache = clearColor.global || clearColor.__width === width && clearColor.__height === height;\n          clearColorGradientOrPattern = shouldCache && clearColor.__canvasGradient || getCanvasGradient(ctx, clearColor, {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          });\n          clearColor.__canvasGradient = clearColorGradientOrPattern;\n          clearColor.__width = width;\n          clearColor.__height = height;\n        } else if (util.isImagePatternObject(clearColor)) {\n          clearColor.scaleX = clearColor.scaleX || dpr;\n          clearColor.scaleY = clearColor.scaleY || dpr;\n          clearColorGradientOrPattern = createCanvasPattern(ctx, clearColor, {\n            dirty: function () {\n              self.setUnpainted();\n              self.__painter.refresh();\n            }\n          });\n        }\n        ctx.save();\n        ctx.fillStyle = clearColorGradientOrPattern || clearColor;\n        ctx.fillRect(x, y, width, height);\n        ctx.restore();\n      }\n      if (haveMotionBLur) {\n        ctx.save();\n        ctx.globalAlpha = lastFrameAlpha;\n        ctx.drawImage(domBack, x, y, width, height);\n        ctx.restore();\n      }\n    }\n    ;\n    if (!repaintRects || haveMotionBLur) {\n      doClear(0, 0, width, height);\n    } else if (repaintRects.length) {\n      util.each(repaintRects, function (rect) {\n        doClear(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n      });\n    }\n  };\n  return Layer;\n}(Eventful);\nexport default Layer;", "import { devicePixelRatio } from '../config.js';\nimport * as util from '../core/util.js';\nimport Layer from './Layer.js';\nimport requestAnimationFrame from '../animation/requestAnimationFrame.js';\nimport env from '../core/env.js';\nimport { brush, brushSingle } from './graphic.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { getSize } from './helper.js';\nvar HOVER_LAYER_ZLEVEL = 1e5;\nvar CANVAS_ZLEVEL = 314159;\nvar EL_AFTER_INCREMENTAL_INC = 0.01;\nvar INCREMENTAL_INC = 0.001;\nfunction isLayerValid(layer) {\n  if (!layer) {\n    return false;\n  }\n  if (layer.__builtin__) {\n    return true;\n  }\n  if (typeof layer.resize !== 'function' || typeof layer.refresh !== 'function') {\n    return false;\n  }\n  return true;\n}\nfunction createRoot(width, height) {\n  var domRoot = document.createElement('div');\n  domRoot.style.cssText = ['position:relative', 'width:' + width + 'px', 'height:' + height + 'px', 'padding:0', 'margin:0', 'border-width:0'].join(';') + ';';\n  return domRoot;\n}\nvar CanvasPainter = function () {\n  function CanvasPainter(root, storage, opts, id) {\n    this.type = 'canvas';\n    this._zlevelList = [];\n    this._prevDisplayList = [];\n    this._layers = {};\n    this._layerConfig = {};\n    this._needsManuallyCompositing = false;\n    this.type = 'canvas';\n    var singleCanvas = !root.nodeName || root.nodeName.toUpperCase() === 'CANVAS';\n    this._opts = opts = util.extend({}, opts || {});\n    this.dpr = opts.devicePixelRatio || devicePixelRatio;\n    this._singleCanvas = singleCanvas;\n    this.root = root;\n    var rootStyle = root.style;\n    if (rootStyle) {\n      util.disableUserSelect(root);\n      root.innerHTML = '';\n    }\n    this.storage = storage;\n    var zlevelList = this._zlevelList;\n    this._prevDisplayList = [];\n    var layers = this._layers;\n    if (!singleCanvas) {\n      this._width = getSize(root, 0, opts);\n      this._height = getSize(root, 1, opts);\n      var domRoot = this._domRoot = createRoot(this._width, this._height);\n      root.appendChild(domRoot);\n    } else {\n      var rootCanvas = root;\n      var width = rootCanvas.width;\n      var height = rootCanvas.height;\n      if (opts.width != null) {\n        width = opts.width;\n      }\n      if (opts.height != null) {\n        height = opts.height;\n      }\n      this.dpr = opts.devicePixelRatio || 1;\n      rootCanvas.width = width * this.dpr;\n      rootCanvas.height = height * this.dpr;\n      this._width = width;\n      this._height = height;\n      var mainLayer = new Layer(rootCanvas, this, this.dpr);\n      mainLayer.__builtin__ = true;\n      mainLayer.initContext();\n      layers[CANVAS_ZLEVEL] = mainLayer;\n      mainLayer.zlevel = CANVAS_ZLEVEL;\n      zlevelList.push(CANVAS_ZLEVEL);\n      this._domRoot = root;\n    }\n  }\n  CanvasPainter.prototype.getType = function () {\n    return 'canvas';\n  };\n  CanvasPainter.prototype.isSingleCanvas = function () {\n    return this._singleCanvas;\n  };\n  CanvasPainter.prototype.getViewportRoot = function () {\n    return this._domRoot;\n  };\n  CanvasPainter.prototype.getViewportRootOffset = function () {\n    var viewportRoot = this.getViewportRoot();\n    if (viewportRoot) {\n      return {\n        offsetLeft: viewportRoot.offsetLeft || 0,\n        offsetTop: viewportRoot.offsetTop || 0\n      };\n    }\n  };\n  CanvasPainter.prototype.refresh = function (paintAll) {\n    var list = this.storage.getDisplayList(true);\n    var prevList = this._prevDisplayList;\n    var zlevelList = this._zlevelList;\n    this._redrawId = Math.random();\n    this._paintList(list, prevList, paintAll, this._redrawId);\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      var layer = this._layers[z];\n      if (!layer.__builtin__ && layer.refresh) {\n        var clearColor = i === 0 ? this._backgroundColor : null;\n        layer.refresh(clearColor);\n      }\n    }\n    if (this._opts.useDirtyRect) {\n      this._prevDisplayList = list.slice();\n    }\n    return this;\n  };\n  CanvasPainter.prototype.refreshHover = function () {\n    this._paintHoverList(this.storage.getDisplayList(false));\n  };\n  CanvasPainter.prototype._paintHoverList = function (list) {\n    var len = list.length;\n    var hoverLayer = this._hoverlayer;\n    hoverLayer && hoverLayer.clear();\n    if (!len) {\n      return;\n    }\n    var scope = {\n      inHover: true,\n      viewWidth: this._width,\n      viewHeight: this._height\n    };\n    var ctx;\n    for (var i = 0; i < len; i++) {\n      var el = list[i];\n      if (el.__inHover) {\n        if (!hoverLayer) {\n          hoverLayer = this._hoverlayer = this.getLayer(HOVER_LAYER_ZLEVEL);\n        }\n        if (!ctx) {\n          ctx = hoverLayer.ctx;\n          ctx.save();\n        }\n        brush(ctx, el, scope, i === len - 1);\n      }\n    }\n    if (ctx) {\n      ctx.restore();\n    }\n  };\n  CanvasPainter.prototype.getHoverLayer = function () {\n    return this.getLayer(HOVER_LAYER_ZLEVEL);\n  };\n  CanvasPainter.prototype.paintOne = function (ctx, el) {\n    brushSingle(ctx, el);\n  };\n  CanvasPainter.prototype._paintList = function (list, prevList, paintAll, redrawId) {\n    if (this._redrawId !== redrawId) {\n      return;\n    }\n    paintAll = paintAll || false;\n    this._updateLayerStatus(list);\n    var _a = this._doPaintList(list, prevList, paintAll),\n      finished = _a.finished,\n      needsRefreshHover = _a.needsRefreshHover;\n    if (this._needsManuallyCompositing) {\n      this._compositeManually();\n    }\n    if (needsRefreshHover) {\n      this._paintHoverList(list);\n    }\n    if (!finished) {\n      var self_1 = this;\n      requestAnimationFrame(function () {\n        self_1._paintList(list, prevList, paintAll, redrawId);\n      });\n    } else {\n      this.eachLayer(function (layer) {\n        layer.afterBrush && layer.afterBrush();\n      });\n    }\n  };\n  CanvasPainter.prototype._compositeManually = function () {\n    var ctx = this.getLayer(CANVAS_ZLEVEL).ctx;\n    var width = this._domRoot.width;\n    var height = this._domRoot.height;\n    ctx.clearRect(0, 0, width, height);\n    this.eachBuiltinLayer(function (layer) {\n      if (layer.virtual) {\n        ctx.drawImage(layer.dom, 0, 0, width, height);\n      }\n    });\n  };\n  CanvasPainter.prototype._doPaintList = function (list, prevList, paintAll) {\n    var _this = this;\n    var layerList = [];\n    var useDirtyRect = this._opts.useDirtyRect;\n    for (var zi = 0; zi < this._zlevelList.length; zi++) {\n      var zlevel = this._zlevelList[zi];\n      var layer = this._layers[zlevel];\n      if (layer.__builtin__ && layer !== this._hoverlayer && (layer.__dirty || paintAll)) {\n        layerList.push(layer);\n      }\n    }\n    var finished = true;\n    var needsRefreshHover = false;\n    var _loop_1 = function (k) {\n      var layer = layerList[k];\n      var ctx = layer.ctx;\n      var repaintRects = useDirtyRect && layer.createRepaintRects(list, prevList, this_1._width, this_1._height);\n      var start = paintAll ? layer.__startIndex : layer.__drawIndex;\n      var useTimer = !paintAll && layer.incremental && Date.now;\n      var startTime = useTimer && Date.now();\n      var clearColor = layer.zlevel === this_1._zlevelList[0] ? this_1._backgroundColor : null;\n      if (layer.__startIndex === layer.__endIndex) {\n        layer.clear(false, clearColor, repaintRects);\n      } else if (start === layer.__startIndex) {\n        var firstEl = list[start];\n        if (!firstEl.incremental || !firstEl.notClear || paintAll) {\n          layer.clear(false, clearColor, repaintRects);\n        }\n      }\n      if (start === -1) {\n        console.error('For some unknown reason. drawIndex is -1');\n        start = layer.__startIndex;\n      }\n      var i;\n      var repaint = function (repaintRect) {\n        var scope = {\n          inHover: false,\n          allClipped: false,\n          prevEl: null,\n          viewWidth: _this._width,\n          viewHeight: _this._height\n        };\n        for (i = start; i < layer.__endIndex; i++) {\n          var el = list[i];\n          if (el.__inHover) {\n            needsRefreshHover = true;\n          }\n          _this._doPaintEl(el, layer, useDirtyRect, repaintRect, scope, i === layer.__endIndex - 1);\n          if (useTimer) {\n            var dTime = Date.now() - startTime;\n            if (dTime > 15) {\n              break;\n            }\n          }\n        }\n        if (scope.prevElClipPaths) {\n          ctx.restore();\n        }\n      };\n      if (repaintRects) {\n        if (repaintRects.length === 0) {\n          i = layer.__endIndex;\n        } else {\n          var dpr = this_1.dpr;\n          for (var r = 0; r < repaintRects.length; ++r) {\n            var rect = repaintRects[r];\n            ctx.save();\n            ctx.beginPath();\n            ctx.rect(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n            ctx.clip();\n            repaint(rect);\n            ctx.restore();\n          }\n        }\n      } else {\n        ctx.save();\n        repaint();\n        ctx.restore();\n      }\n      layer.__drawIndex = i;\n      if (layer.__drawIndex < layer.__endIndex) {\n        finished = false;\n      }\n    };\n    var this_1 = this;\n    for (var k = 0; k < layerList.length; k++) {\n      _loop_1(k);\n    }\n    if (env.wxa) {\n      util.each(this._layers, function (layer) {\n        if (layer && layer.ctx && layer.ctx.draw) {\n          layer.ctx.draw();\n        }\n      });\n    }\n    return {\n      finished: finished,\n      needsRefreshHover: needsRefreshHover\n    };\n  };\n  CanvasPainter.prototype._doPaintEl = function (el, currentLayer, useDirtyRect, repaintRect, scope, isLast) {\n    var ctx = currentLayer.ctx;\n    if (useDirtyRect) {\n      var paintRect = el.getPaintRect();\n      if (!repaintRect || paintRect && paintRect.intersect(repaintRect)) {\n        brush(ctx, el, scope, isLast);\n        el.setPrevPaintRect(paintRect);\n      }\n    } else {\n      brush(ctx, el, scope, isLast);\n    }\n  };\n  CanvasPainter.prototype.getLayer = function (zlevel, virtual) {\n    if (this._singleCanvas && !this._needsManuallyCompositing) {\n      zlevel = CANVAS_ZLEVEL;\n    }\n    var layer = this._layers[zlevel];\n    if (!layer) {\n      layer = new Layer('zr_' + zlevel, this, this.dpr);\n      layer.zlevel = zlevel;\n      layer.__builtin__ = true;\n      if (this._layerConfig[zlevel]) {\n        util.merge(layer, this._layerConfig[zlevel], true);\n      } else if (this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC]) {\n        util.merge(layer, this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC], true);\n      }\n      if (virtual) {\n        layer.virtual = virtual;\n      }\n      this.insertLayer(zlevel, layer);\n      layer.initContext();\n    }\n    return layer;\n  };\n  CanvasPainter.prototype.insertLayer = function (zlevel, layer) {\n    var layersMap = this._layers;\n    var zlevelList = this._zlevelList;\n    var len = zlevelList.length;\n    var domRoot = this._domRoot;\n    var prevLayer = null;\n    var i = -1;\n    if (layersMap[zlevel]) {\n      if (process.env.NODE_ENV !== 'production') {\n        util.logError('ZLevel ' + zlevel + ' has been used already');\n      }\n      return;\n    }\n    if (!isLayerValid(layer)) {\n      if (process.env.NODE_ENV !== 'production') {\n        util.logError('Layer of zlevel ' + zlevel + ' is not valid');\n      }\n      return;\n    }\n    if (len > 0 && zlevel > zlevelList[0]) {\n      for (i = 0; i < len - 1; i++) {\n        if (zlevelList[i] < zlevel && zlevelList[i + 1] > zlevel) {\n          break;\n        }\n      }\n      prevLayer = layersMap[zlevelList[i]];\n    }\n    zlevelList.splice(i + 1, 0, zlevel);\n    layersMap[zlevel] = layer;\n    if (!layer.virtual) {\n      if (prevLayer) {\n        var prevDom = prevLayer.dom;\n        if (prevDom.nextSibling) {\n          domRoot.insertBefore(layer.dom, prevDom.nextSibling);\n        } else {\n          domRoot.appendChild(layer.dom);\n        }\n      } else {\n        if (domRoot.firstChild) {\n          domRoot.insertBefore(layer.dom, domRoot.firstChild);\n        } else {\n          domRoot.appendChild(layer.dom);\n        }\n      }\n    }\n    layer.__painter = this;\n  };\n  CanvasPainter.prototype.eachLayer = function (cb, context) {\n    var zlevelList = this._zlevelList;\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      cb.call(context, this._layers[z], z);\n    }\n  };\n  CanvasPainter.prototype.eachBuiltinLayer = function (cb, context) {\n    var zlevelList = this._zlevelList;\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      var layer = this._layers[z];\n      if (layer.__builtin__) {\n        cb.call(context, layer, z);\n      }\n    }\n  };\n  CanvasPainter.prototype.eachOtherLayer = function (cb, context) {\n    var zlevelList = this._zlevelList;\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      var layer = this._layers[z];\n      if (!layer.__builtin__) {\n        cb.call(context, layer, z);\n      }\n    }\n  };\n  CanvasPainter.prototype.getLayers = function () {\n    return this._layers;\n  };\n  CanvasPainter.prototype._updateLayerStatus = function (list) {\n    this.eachBuiltinLayer(function (layer, z) {\n      layer.__dirty = layer.__used = false;\n    });\n    function updatePrevLayer(idx) {\n      if (prevLayer) {\n        if (prevLayer.__endIndex !== idx) {\n          prevLayer.__dirty = true;\n        }\n        prevLayer.__endIndex = idx;\n      }\n    }\n    if (this._singleCanvas) {\n      for (var i_1 = 1; i_1 < list.length; i_1++) {\n        var el = list[i_1];\n        if (el.zlevel !== list[i_1 - 1].zlevel || el.incremental) {\n          this._needsManuallyCompositing = true;\n          break;\n        }\n      }\n    }\n    var prevLayer = null;\n    var incrementalLayerCount = 0;\n    var prevZlevel;\n    var i;\n    for (i = 0; i < list.length; i++) {\n      var el = list[i];\n      var zlevel = el.zlevel;\n      var layer = void 0;\n      if (prevZlevel !== zlevel) {\n        prevZlevel = zlevel;\n        incrementalLayerCount = 0;\n      }\n      if (el.incremental) {\n        layer = this.getLayer(zlevel + INCREMENTAL_INC, this._needsManuallyCompositing);\n        layer.incremental = true;\n        incrementalLayerCount = 1;\n      } else {\n        layer = this.getLayer(zlevel + (incrementalLayerCount > 0 ? EL_AFTER_INCREMENTAL_INC : 0), this._needsManuallyCompositing);\n      }\n      if (!layer.__builtin__) {\n        util.logError('ZLevel ' + zlevel + ' has been used by unkown layer ' + layer.id);\n      }\n      if (layer !== prevLayer) {\n        layer.__used = true;\n        if (layer.__startIndex !== i) {\n          layer.__dirty = true;\n        }\n        layer.__startIndex = i;\n        if (!layer.incremental) {\n          layer.__drawIndex = i;\n        } else {\n          layer.__drawIndex = -1;\n        }\n        updatePrevLayer(i);\n        prevLayer = layer;\n      }\n      if (el.__dirty & REDRAW_BIT && !el.__inHover) {\n        layer.__dirty = true;\n        if (layer.incremental && layer.__drawIndex < 0) {\n          layer.__drawIndex = i;\n        }\n      }\n    }\n    updatePrevLayer(i);\n    this.eachBuiltinLayer(function (layer, z) {\n      if (!layer.__used && layer.getElementCount() > 0) {\n        layer.__dirty = true;\n        layer.__startIndex = layer.__endIndex = layer.__drawIndex = 0;\n      }\n      if (layer.__dirty && layer.__drawIndex < 0) {\n        layer.__drawIndex = layer.__startIndex;\n      }\n    });\n  };\n  CanvasPainter.prototype.clear = function () {\n    this.eachBuiltinLayer(this._clearLayer);\n    return this;\n  };\n  CanvasPainter.prototype._clearLayer = function (layer) {\n    layer.clear();\n  };\n  CanvasPainter.prototype.setBackgroundColor = function (backgroundColor) {\n    this._backgroundColor = backgroundColor;\n    util.each(this._layers, function (layer) {\n      layer.setUnpainted();\n    });\n  };\n  CanvasPainter.prototype.configLayer = function (zlevel, config) {\n    if (config) {\n      var layerConfig = this._layerConfig;\n      if (!layerConfig[zlevel]) {\n        layerConfig[zlevel] = config;\n      } else {\n        util.merge(layerConfig[zlevel], config, true);\n      }\n      for (var i = 0; i < this._zlevelList.length; i++) {\n        var _zlevel = this._zlevelList[i];\n        if (_zlevel === zlevel || _zlevel === zlevel + EL_AFTER_INCREMENTAL_INC) {\n          var layer = this._layers[_zlevel];\n          util.merge(layer, layerConfig[zlevel], true);\n        }\n      }\n    }\n  };\n  CanvasPainter.prototype.delLayer = function (zlevel) {\n    var layers = this._layers;\n    var zlevelList = this._zlevelList;\n    var layer = layers[zlevel];\n    if (!layer) {\n      return;\n    }\n    layer.dom.parentNode.removeChild(layer.dom);\n    delete layers[zlevel];\n    zlevelList.splice(util.indexOf(zlevelList, zlevel), 1);\n  };\n  CanvasPainter.prototype.resize = function (width, height) {\n    if (!this._domRoot.style) {\n      if (width == null || height == null) {\n        return;\n      }\n      this._width = width;\n      this._height = height;\n      this.getLayer(CANVAS_ZLEVEL).resize(width, height);\n    } else {\n      var domRoot = this._domRoot;\n      domRoot.style.display = 'none';\n      var opts = this._opts;\n      var root = this.root;\n      width != null && (opts.width = width);\n      height != null && (opts.height = height);\n      width = getSize(root, 0, opts);\n      height = getSize(root, 1, opts);\n      domRoot.style.display = '';\n      if (this._width !== width || height !== this._height) {\n        domRoot.style.width = width + 'px';\n        domRoot.style.height = height + 'px';\n        for (var id in this._layers) {\n          if (this._layers.hasOwnProperty(id)) {\n            this._layers[id].resize(width, height);\n          }\n        }\n        this.refresh(true);\n      }\n      this._width = width;\n      this._height = height;\n    }\n    return this;\n  };\n  CanvasPainter.prototype.clearLayer = function (zlevel) {\n    var layer = this._layers[zlevel];\n    if (layer) {\n      layer.clear();\n    }\n  };\n  CanvasPainter.prototype.dispose = function () {\n    this.root.innerHTML = '';\n    this.root = this.storage = this._domRoot = this._layers = null;\n  };\n  CanvasPainter.prototype.getRenderedCanvas = function (opts) {\n    opts = opts || {};\n    if (this._singleCanvas && !this._compositeManually) {\n      return this._layers[CANVAS_ZLEVEL].dom;\n    }\n    var imageLayer = new Layer('image', this, opts.pixelRatio || this.dpr);\n    imageLayer.initContext();\n    imageLayer.clear(false, opts.backgroundColor || this._backgroundColor);\n    var ctx = imageLayer.ctx;\n    if (opts.pixelRatio <= this.dpr) {\n      this.refresh();\n      var width_1 = imageLayer.dom.width;\n      var height_1 = imageLayer.dom.height;\n      this.eachLayer(function (layer) {\n        if (layer.__builtin__) {\n          ctx.drawImage(layer.dom, 0, 0, width_1, height_1);\n        } else if (layer.renderToCanvas) {\n          ctx.save();\n          layer.renderToCanvas(ctx);\n          ctx.restore();\n        }\n      });\n    } else {\n      var scope = {\n        inHover: false,\n        viewWidth: this._width,\n        viewHeight: this._height\n      };\n      var displayList = this.storage.getDisplayList(true);\n      for (var i = 0, len = displayList.length; i < len; i++) {\n        var el = displayList[i];\n        brush(ctx, el, scope, i === len - 1);\n      }\n    }\n    return imageLayer.dom;\n  };\n  CanvasPainter.prototype.getWidth = function () {\n    return this._width;\n  };\n  CanvasPainter.prototype.getHeight = function () {\n    return this._height;\n  };\n  return CanvasPainter;\n}();\nexport default CanvasPainter;\n;", "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport CanvasPainter from 'zrender/lib/canvas/Painter.js';\nexport function install(registers) {\n  registers.registerPainter('canvas', CanvasPainter);\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAI,UAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,KAAK,KAAK;AACd,IAAI,MAAM,KAAK,KAAK;AACpB,IAAI,SAAS,MAAM;AACnB,IAAI,mBAAmB,WAAY;AACjC,WAASA,oBAAmB;AAAA,EAAC;AAC7B,EAAAA,kBAAiB,UAAU,QAAQ,SAAU,WAAW;AACtD,SAAK,SAAS;AACd,SAAK,KAAK,CAAC;AACX,SAAK,OAAO;AACZ,SAAK,KAAK,KAAK,IAAI,IAAI,aAAa,CAAC;AAAA,EACvC;AACA,EAAAA,kBAAiB,UAAU,SAAS,SAAU,GAAG,GAAG;AAClD,SAAK,KAAK,KAAK,GAAG,CAAC;AAAA,EACrB;AACA,EAAAA,kBAAiB,UAAU,SAAS,SAAU,GAAG,GAAG;AAClD,SAAK,KAAK,KAAK,GAAG,CAAC;AAAA,EACrB;AACA,EAAAA,kBAAiB,UAAU,gBAAgB,SAAU,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI;AACzE,SAAK,KAAK,KAAK,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,EACrC;AACA,EAAAA,kBAAiB,UAAU,mBAAmB,SAAU,GAAG,GAAG,IAAI,IAAI;AACpE,SAAK,KAAK,KAAK,GAAG,GAAG,IAAI,EAAE;AAAA,EAC7B;AACA,EAAAA,kBAAiB,UAAU,MAAM,SAAU,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe;AACzF,SAAK,QAAQ,IAAI,IAAI,GAAG,GAAG,GAAG,YAAY,UAAU,aAAa;AAAA,EACnE;AACA,EAAAA,kBAAiB,UAAU,UAAU,SAAU,IAAI,IAAI,IAAI,IAAI,KAAK,YAAY,UAAU,eAAe;AACvG,QAAI,SAAS,WAAW;AACxB,QAAI,YAAY,CAAC;AACjB,QAAI,iBAAiB,KAAK,IAAI,MAAM;AACpC,QAAI,WAAW,aAAa,iBAAiB,GAAG,MAAM,YAAY,UAAU,MAAM,CAAC,UAAU;AAC7F,QAAI,eAAe,SAAS,IAAI,SAAS,MAAM,SAAS,MAAM;AAC9D,QAAI,QAAQ;AACZ,QAAI,UAAU;AACZ,cAAQ;AAAA,IACV,WAAW,aAAa,cAAc,GAAG;AACvC,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ,gBAAgB,OAAO,CAAC,CAAC;AAAA,IACnC;AACA,QAAI,KAAK,KAAK,KAAK,QAAQ,UAAU;AACrC,QAAI,KAAK,KAAK,KAAK,QAAQ,UAAU;AACrC,QAAI,KAAK,QAAQ;AACf,WAAK,KAAK,KAAK,IAAI,EAAE;AAAA,IACvB;AACA,QAAI,OAAO,KAAK,MAAM,MAAM,MAAM;AAClC,QAAI,UAAU;AACZ,UAAI,IAAI,IAAI,KAAK;AACjB,UAAI,YAAY,YAAY,IAAI,OAAO,MAAM;AAC7C,WAAK,KAAK,KAAK,IAAI,IAAI,MAAM,GAAG,CAAC,WAAW,KAAK,KAAK,QAAQ,aAAa,QAAQ,GAAG,KAAK,KAAK,QAAQ,aAAa,QAAQ,CAAC;AAC9H,UAAI,IAAI,MAAM;AACZ,aAAK,KAAK,KAAK,IAAI,IAAI,MAAM,GAAG,CAAC,WAAW,IAAI,EAAE;AAAA,MACpD;AAAA,IACF,OAAO;AACL,UAAI,IAAI,KAAK,KAAK,QAAQ,QAAQ;AAClC,UAAI,IAAI,KAAK,KAAK,QAAQ,QAAQ;AAClC,WAAK,KAAK,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC;AAAA,IACvD;AAAA,EACF;AACA,EAAAA,kBAAiB,UAAU,OAAO,SAAU,GAAG,GAAG,GAAG,GAAG;AACtD,SAAK,KAAK,KAAK,GAAG,CAAC;AACnB,SAAK,KAAK,KAAK,GAAG,CAAC;AACnB,SAAK,KAAK,KAAK,GAAG,CAAC;AACnB,SAAK,KAAK,KAAK,CAAC,GAAG,CAAC;AACpB,SAAK,KAAK,GAAG;AAAA,EACf;AACA,EAAAA,kBAAiB,UAAU,YAAY,WAAY;AACjD,QAAI,KAAK,GAAG,SAAS,GAAG;AACtB,WAAK,KAAK,GAAG;AAAA,IACf;AAAA,EACF;AACA,EAAAA,kBAAiB,UAAU,OAAO,SAAU,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACvE,QAAI,OAAO,CAAC;AACZ,QAAI,IAAI,KAAK;AACb,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,MAAM,UAAU,CAAC;AACrB,UAAI,MAAM,GAAG,GAAG;AACd,aAAK,WAAW;AAChB;AAAA,MACF;AACA,WAAK,KAAK,KAAK,MAAM,MAAM,CAAC,IAAI,CAAC;AAAA,IACnC;AACA,SAAK,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,CAAC;AACjC,SAAK,SAAS,QAAQ;AAAA,EACxB;AACA,EAAAA,kBAAiB,UAAU,cAAc,WAAY;AACnD,SAAK,OAAO,KAAK,WAAW,KAAK,KAAK,GAAG,KAAK,EAAE;AAChD,SAAK,KAAK,CAAC;AAAA,EACb;AACA,EAAAA,kBAAiB,UAAU,SAAS,WAAY;AAC9C,WAAO,KAAK;AAAA,EACd;AACA,SAAOA;AACT,EAAE;AACF,IAAO,2BAAQ;;;AC5Ff,IAAI,OAAO;AACX,IAAI,YAAY,KAAK;AACrB,SAAS,YAAY,OAAO;AAC1B,MAAI,OAAO,MAAM;AACjB,SAAO,QAAQ,QAAQ,SAAS;AAClC;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,SAAS,MAAM;AACnB,SAAO,UAAU,QAAQ,WAAW;AACtC;AACA,IAAI,cAAc,CAAC,WAAW,cAAc,UAAU;AACtD,IAAI,iBAAiB,IAAI,aAAa,SAAU,MAAM;AACpD,SAAO,YAAY,KAAK,YAAY;AACtC,CAAC;AACc,SAAR,gBAAiC,YAAY,OAAO,IAAI,aAAa;AAC1E,MAAI,UAAU,MAAM,WAAW,OAAO,IAAI,MAAM;AAChD,MAAI,cAAc,eAAS;AACzB,eAAW,WAAW,OAAO;AAC7B;AAAA,EACF;AACA,MAAI,YAAY,KAAK,GAAG;AACtB,QAAI,OAAO,eAAe,MAAM,IAAI;AACpC,eAAW,QAAQ,KAAK,KAAK;AAC7B,QAAI,cAAc,MAAM,eAAe,OAAO,MAAM,cAAc,KAAK,UAAU,UAAU,KAAK,UAAU;AAC1G,QAAI,eAAe,cAAc,GAAG;AAClC,iBAAW,gBAAgB,WAAW;AAAA,IACxC;AAAA,EACF,OAAO;AACL,eAAW,QAAQ,IAAI;AAAA,EACzB;AACA,MAAI,cAAc,KAAK,GAAG;AACxB,QAAI,SAAS,eAAe,MAAM,MAAM;AACxC,eAAW,UAAU,OAAO,KAAK;AACjC,QAAI,cAAc,MAAM,gBAAgB,GAAG,aAAa,IAAI;AAC5D,QAAI,cAAc,eAAe,MAAM,aAAa,KAAK,cAAc;AACvE,QAAI,gBAAgB,MAAM,iBAAiB,OAAO,MAAM,gBAAgB,OAAO,UAAU,UAAU,OAAO,UAAU;AACpH,QAAI,cAAc,MAAM;AACxB,QAAI,eAAe,gBAAgB,GAAG;AACpC,iBAAW,gBAAgB,WAAW;AAAA,IACxC;AACA,QAAI,eAAe,aAAa;AAC9B,iBAAW,eAAe,cAAc,WAAW,MAAM;AAAA,IAC3D;AACA,QAAI,eAAe,gBAAgB,GAAG;AACpC,iBAAW,kBAAkB,aAAa;AAAA,IAC5C;AACA,QAAI,MAAM,UAAU;AAClB,UAAI,KAAK,YAAY,EAAE,GACrB,WAAW,GAAG,CAAC,GACf,iBAAiB,GAAG,CAAC;AACvB,UAAI,UAAU;AACZ,yBAAiB,UAAU,kBAAkB,CAAC;AAC9C,mBAAW,oBAAoB,SAAS,KAAK,GAAG,CAAC;AACjD,YAAI,kBAAkB,aAAa;AACjC,qBAAW,qBAAqB,cAAc;AAAA,QAChD;AAAA,MACF;AAAA,IACF,WAAW,aAAa;AACtB,iBAAW,oBAAoB,IAAI;AAAA,IACrC;AACA,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAI,WAAW,YAAY,CAAC;AAC5B,UAAI,eAAe,MAAM,QAAQ,MAAM,mBAAmB,QAAQ,GAAG;AACnE,YAAI,MAAM,MAAM,QAAQ,KAAK,mBAAmB,QAAQ;AACxD,eAAO,WAAW,eAAe,CAAC,GAAG,GAAG;AAAA,MAC1C;AAAA,IACF;AAAA,EACF,WAAW,aAAa;AACtB,eAAW,UAAU,IAAI;AAAA,EAC3B;AACF;;;ACzEO,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,SAAS,cAAc,MAAM;AAClC,SAAO,SAAS,gBAAgB,OAAO,IAAI;AAC7C;AAEO,SAAS,YAAY,KAAK,KAAK,OAAO,UAAU,MAAM;AAC3D,SAAO;AAAA,IACL;AAAA,IACA,OAAO,SAAS,CAAC;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,MAAM,OAAO;AACtC,MAAI,WAAW,CAAC;AAChB,MAAI,OAAO;AACT,aAAS,OAAO,OAAO;AACrB,UAAI,MAAM,MAAM,GAAG;AACnB,UAAI,OAAO;AACX,UAAI,QAAQ,OAAO;AACjB;AAAA,MACF,WAAW,QAAQ,QAAQ,OAAO,MAAM;AACtC,gBAAQ,OAAQ,MAAM;AAAA,MACxB;AACA,eAAS,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO,MAAM,OAAO,MAAM,SAAS,KAAK,GAAG,IAAI;AACjD;AACA,SAAS,mBAAmB,MAAM;AAChC,SAAO,OAAO,OAAO;AACvB;AACO,SAAS,cAAc,IAAI,MAAM;AACtC,SAAO,QAAQ,CAAC;AAChB,MAAI,IAAI,KAAK,UAAU,OAAO;AAC9B,WAAS,kBAAkBC,KAAI;AAC7B,QAAI,WAAWA,IAAG,UAChB,MAAMA,IAAG,KACT,QAAQA,IAAG,OACX,OAAOA,IAAG;AACZ,WAAO,kBAAkB,KAAK,KAAK,KAAK,QAAQ,UAAU,WAAW,IAAI,IAAI,QAAQ,OAAO,WAAW,KAAK,IAAI,IAAI,UAAU,SAAU,OAAO;AAC7I,aAAO,kBAAkB,KAAK;AAAA,IAChC,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,MAAM,mBAAmB,GAAG;AAAA,EAC/C;AACA,SAAO,kBAAkB,EAAE;AAC7B;AACO,SAAS,aAAa,eAAe,gBAAgB,MAAM;AAChE,SAAO,QAAQ,CAAC;AAChB,MAAI,IAAI,KAAK,UAAU,OAAO;AAC9B,MAAI,eAAe,OAAO;AAC1B,MAAI,aAAa,IAAI;AACrB,MAAI,YAAY,IAAI,KAAK,aAAa,GAAG,SAAU,WAAW;AAC5D,WAAO,YAAY,eAAe,IAAI,KAAK,cAAc,SAAS,CAAC,GAAG,SAAU,UAAU;AACxF,aAAO,WAAW,MAAM,cAAc,SAAS,EAAE,QAAQ,IAAI;AAAA,IAC/D,CAAC,EAAE,KAAK,CAAC,IAAI;AAAA,EACf,CAAC,EAAE,KAAK,CAAC;AACT,MAAI,aAAa,IAAI,KAAK,cAAc,GAAG,SAAU,eAAe;AAClE,WAAO,gBAAgB,gBAAgB,eAAe,IAAI,KAAK,eAAe,aAAa,CAAC,GAAG,SAAU,SAAS;AAChH,aAAO,UAAU,eAAe,IAAI,KAAK,eAAe,aAAa,EAAE,OAAO,CAAC,GAAG,SAAU,UAAU;AACpG,YAAI,MAAM,eAAe,aAAa,EAAE,OAAO,EAAE,QAAQ;AACzD,YAAI,aAAa,KAAK;AACpB,gBAAM,WAAY,MAAM;AAAA,QAC1B;AACA,eAAO,WAAW,MAAM,MAAM;AAAA,MAChC,CAAC,EAAE,KAAK,CAAC,IAAI;AAAA,IACf,CAAC,EAAE,KAAK,CAAC,IAAI;AAAA,EACf,CAAC,EAAE,KAAK,CAAC;AACT,MAAI,CAAC,aAAa,CAAC,YAAY;AAC7B,WAAO;AAAA,EACT;AACA,SAAO,CAAC,aAAa,WAAW,YAAY,KAAK,EAAE,KAAK,CAAC;AAC3D;AACO,SAAS,iBAAiB,MAAM;AACrC,SAAO;AAAA,IACL;AAAA,IACA,aAAa,CAAC;AAAA,IACd,cAAc,CAAC;AAAA,IACf,eAAe,CAAC;AAAA,IAChB,eAAe,CAAC;AAAA,IAChB,MAAM,CAAC;AAAA,IACP,UAAU,CAAC;AAAA,IACX,UAAU,CAAC;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,EACf;AACF;AACO,SAAS,eAAe,OAAO,QAAQ,UAAU,YAAY;AAClE,SAAO,YAAY,OAAO,QAAQ;AAAA,IAChC,SAAS;AAAA,IACT,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,WAAW,aAAa,SAAS,QAAQ,MAAM,SAAS;AAAA,EAC1D,GAAG,QAAQ;AACb;;;AClGO,IAAI,aAAa;AAAA,EACtB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,eAAe;AACjB;AACA,IAAI,qBAAqB;AACzB,SAAS,gBAAgB,IAAI,SAAS,MAAM;AAC1C,MAAI,QAAQ,OAAO,CAAC,GAAG,GAAG,KAAK;AAC/B,SAAO,OAAO,OAAO;AACrB,KAAG,UAAU,MAAM,KAAK;AACxB,MAAI,iBAAiB,IAAI,yBAAiB;AAC1C,iBAAe,MAAM,iBAAiB,EAAE,CAAC;AACzC,OAAK,YAAY,gBAAgB,CAAC;AAClC,iBAAe,YAAY;AAC3B,SAAO,eAAe,OAAO;AAC/B;AACA,SAAS,mBAAmB,QAAQ,WAAW;AAC7C,MAAI,UAAU,UAAU,SACtB,UAAU,UAAU;AACtB,MAAI,WAAW,SAAS;AACtB,WAAO,kBAAkB,IAAI,UAAU,QAAQ,UAAU;AAAA,EAC3D;AACF;AACO,IAAI,oBAAoB;AAAA,EAC7B,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,gBAAgB;AAClB;AACA,SAAS,aAAa,SAAS,OAAO;AACpC,MAAI,gBAAgB,MAAM,OAAO,UAAU,MAAM;AACjD,QAAM,SAAS,aAAa,IAAI;AAChC,SAAO;AACT;AACA,SAAS,+BAA+B,IAAI,OAAO,OAAO;AACxD,MAAI,QAAQ,GAAG,MAAM;AACrB,MAAI,eAAe,CAAC;AACpB,MAAI;AACJ,MAAI;AACJ,OAAK,OAAO,SAAU,MAAM;AAC1B,QAAI,WAAW,iBAAiB,MAAM,IAAI;AAC1C,aAAS,YAAY;AACrB,uBAAmB,MAAM,CAAC,GAAG,UAAU,IAAI;AAC3C,QAAI,WAAW,SAAS;AACxB,QAAI,WAAW,SAAS;AACxB,QAAI,YAAY,KAAK,QAAQ;AAC7B,QAAI,MAAM,UAAU;AACpB,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,uBAAmB,UAAU,MAAM,CAAC;AACpC,QAAI,WAAW,SAAS,gBAAgB;AACxC,aAAS,WAAW,UAAU;AAC5B,UAAI,KAAK,SAAS,OAAO;AACzB,mBAAa,OAAO,IAAI,aAAa,OAAO,KAAK;AAAA,QAC/C,GAAG;AAAA,MACL;AACA,mBAAa,OAAO,EAAE,KAAK,GAAG,KAAK;AAAA,IACrC;AACA,aAAS,aAAa,UAAU;AAC9B,UAAI,MAAM,SAAS,SAAS,EAAE;AAC9B,UAAI,IAAI,QAAQ,gBAAgB,KAAK,GAAG;AACtC,0BAAkB;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,CAAC,iBAAiB;AACpB;AAAA,EACF;AACA,QAAM,IAAI;AACV,MAAI,gBAAgB,aAAa,cAAc,KAAK;AACpD,SAAO,gBAAgB,QAAQ,kBAAkB,aAAa;AAChE;AACA,SAAS,cAAc,QAAQ;AAC7B,SAAO,SAAS,MAAM,IAAI,WAAW,MAAM,IAAI,kBAAkB,WAAW,MAAM,IAAI,MAAM,sBAAsB,MAAM,IAAI,SAAS,KAAK;AAC5I;AACO,SAAS,mBAAmB,IAAI,OAAO,OAAO,WAAW;AAC9D,MAAI,YAAY,GAAG;AACnB,MAAI,MAAM,UAAU;AACpB,MAAI,gBAAgB,CAAC;AACrB,MAAI,cAAc,sBAAc;AAC9B,QAAI,eAAe,+BAA+B,IAAI,OAAO,KAAK;AAClE,QAAI,cAAc;AAChB,oBAAc,KAAK,YAAY;AAAA,IACjC,WAAW,CAAC,KAAK;AACf;AAAA,IACF;AAAA,EACF,WAAW,CAAC,KAAK;AACf;AAAA,EACF;AACA,MAAI,iBAAiB,CAAC;AACtB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,WAAW,UAAU,CAAC;AAC1B,QAAI,SAAS,CAAC,SAAS,WAAW,IAAI,MAAO,GAAG;AAChD,QAAI,SAAS,cAAc,SAAS,QAAQ,EAAE,MAAM;AACpD,QAAI,QAAQ,SAAS,SAAS;AAC9B,QAAI,QAAQ;AACV,aAAO,KAAK,MAAM;AAAA,IACpB,OAAO;AACL,aAAO,KAAK,QAAQ;AAAA,IACtB;AACA,QAAI,OAAO;AACT,aAAO,KAAK,QAAQ,MAAO,GAAG;AAAA,IAChC;AACA,QAAI,SAAS,QAAQ,GAAG;AACtB,aAAO,KAAK,UAAU;AAAA,IACxB;AACA,QAAI,MAAM,OAAO,KAAK,GAAG;AACzB,mBAAe,GAAG,IAAI,eAAe,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AACrD,mBAAe,GAAG,EAAE,CAAC,EAAE,KAAK,QAAQ;AAAA,EACtC;AACA,WAAS,yBAAyB,eAAe;AAC/C,QAAIC,aAAY,cAAc,CAAC;AAC/B,QAAIC,OAAMD,WAAU;AACpB,QAAI,eAAe,CAAC;AACpB,QAAI,WAAW,CAAC;AAChB,QAAI,WAAW,CAAC;AAChB,QAAI,kCAAkC;AACtC,aAAS,0BAA0BE,WAAU,QAAQ,eAAe;AAClE,UAAI,SAASA,UAAS,UAAU;AAChC,UAAI,UAAUA,UAAS,WAAW;AAClC,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC;AACpB,YAAI,MAAM,aAAa,GAAG;AACxB,cAAI,MAAM,MAAM;AAChB,cAAI,WAAW,MAAM;AACrB,4BAAkB,WAAW,cAAc,QAAQ;AACnD,cAAI,UAAU;AACZ,qBAASC,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACnC,kBAAI,KAAK,IAAIA,EAAC;AACd,kBAAIC,WAAU,KAAK,MAAM,GAAG,OAAO,UAAU,GAAG,IAAI;AACpD,kBAAI,WAAW,cAAc,GAAG,MAAM;AACtC,kBAAI,WAAW,GAAG;AAClB,kBAAI,SAAS,QAAQ,KAAK,SAAS,QAAQ,GAAG;AAC5C,uBAAOA,QAAO,IAAI,OAAOA,QAAO,KAAK,CAAC;AACtC,uBAAOA,QAAO,EAAE,QAAQ,IAAI,GAAG;AAC/B,oBAAI,UAAU;AACZ,yBAAOA,QAAO,EAAE,+BAA+B,IAAI;AAAA,gBACrD;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,aAASD,KAAI,GAAGA,KAAIF,MAAKE,MAAK;AAC5B,UAAID,YAAWF,WAAUG,EAAC;AAC1B,UAAI,aAAaD,UAAS;AAC1B,UAAI,CAAC,YAAY;AACf,SAAC,aAAa,0BAA0BA,WAAU,YAAY;AAAA,MAChE,WAAW,eAAe,SAAS;AACjC,kCAA0BA,WAAU,QAAQ;AAAA,MAC9C;AAAA,IACF;AACA,aAAS,WAAW,cAAc;AAChC,UAAI,YAAY,CAAC;AACjB,oBAAc,WAAW,EAAE;AAC3B,aAAO,WAAW,aAAa,OAAO,CAAC;AACvC,UAAI,MAAM,sBAAsB,SAAS;AACzC,UAAI,iBAAiB,aAAa,OAAO,EAAE,+BAA+B;AAC1E,eAAS,OAAO,IAAI,MAAM;AAAA,QACxB,WAAW;AAAA,MACb,IAAI,CAAC;AACL,yBAAmB,SAAS,OAAO,GAAG,SAAS;AAC/C,UAAI,gBAAgB;AAClB,iBAAS,OAAO,EAAE,+BAA+B,IAAI;AAAA,MACvD;AAAA,IACF;AACA;AACA,QAAI;AACJ,QAAI,kBAAkB;AACtB,aAAS,WAAW,UAAU;AAC5B,eAAS,OAAO,IAAI,SAAS,OAAO,KAAK,CAAC;AAC1C,UAAI,UAAU,CAAC;AACf,UAAI,iBAAiB,SAAS,OAAO,EAAE,+BAA+B;AACtE,UAAI,SAAS;AACX,eAAO,IAAI,kBAAU;AAAA,MACvB;AACA,UAAI,QAAQ,KAAK,IAAI;AACrB,WAAK,MAAM;AACX,eAAS,OAAO,EAAE,IAAI,gBAAgB,IAAI,SAAS,OAAO,GAAG,IAAI;AACjE,UAAI,SAAS,KAAK,IAAI;AACtB,UAAI,CAAC,WAAW,UAAU,QAAQ;AAChC,0BAAkB;AAClB;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,iBAAS,OAAO,EAAE,+BAA+B,IAAI;AAAA,MACvD;AAAA,IACF;AACA;AACA,QAAI,CAAC,iBAAiB;AACpB,eAAS,WAAW,UAAU;AAC5B,eAAO,SAAS,OAAO,EAAE;AAAA,MAC3B;AAAA,IACF;AACA,QAAI,CAAC,WAAW;AACd,eAASC,KAAI,GAAGA,KAAIF,MAAKE,MAAK;AAC5B,YAAID,YAAWF,WAAUG,EAAC;AAC1B,YAAI,aAAaD,UAAS;AAC1B,YAAI,eAAe,SAAS;AAC1B,oCAA0BA,WAAU,UAAU,SAAU,UAAU;AAChE,mBAAO,kBAAkB,QAAQ;AAAA,UACnC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW,KAAK,QAAQ;AAC5B,QAAI,yBAAyB;AAC7B,QAAI;AACJ,aAASC,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACxC,UAAI,KAAK,SAASA,KAAI,CAAC;AACvB,UAAI,KAAK,SAASA,EAAC;AACnB,UAAI,SAAS,EAAE,EAAE,kBAAkB,MAAM,SAAS,EAAE,EAAE,kBAAkB,GAAG;AACzE,iCAAyB;AACzB;AAAA,MACF;AACA,wBAAkB,SAAS,EAAE,EAAE,kBAAkB;AAAA,IACnD;AACA,QAAI,0BAA0B,iBAAiB;AAC7C,eAAS,WAAW,UAAU;AAC5B,YAAI,SAAS,OAAO,EAAE,kBAAkB,GAAG;AACzC,iBAAO,SAAS,OAAO,EAAE,kBAAkB;AAAA,QAC7C;AAAA,MACF;AACA,YAAM,kBAAkB,IAAI;AAAA,IAC9B;AACA,QAAI,OAAO,UAAU,SAAUC,UAAS;AACtC,aAAO,KAAK,SAASA,QAAO,CAAC,EAAE,SAAS;AAAA,IAC1C,CAAC,EAAE,QAAQ;AACT,UAAI,gBAAgB,aAAa,UAAU,KAAK;AAChD,aAAO,gBAAgB,MAAM,cAAc,CAAC,IAAI;AAAA,IAClD;AAAA,EACF;AACA,WAAS,OAAO,gBAAgB;AAC9B,QAAI,eAAe,yBAAyB,eAAe,GAAG,CAAC;AAC/D,QAAI,cAAc;AAChB,oBAAc,KAAK,YAAY;AAAA,IACjC;AAAA,EACF;AACA,MAAI,cAAc,QAAQ;AACxB,QAAI,YAAY,MAAM,OAAO,UAAU,MAAM;AAC7C,UAAM,SAAS,MAAM,SAAS,IAAI;AAAA,MAChC,WAAW,cAAc,KAAK,GAAG;AAAA,IACnC;AACA,UAAM,OAAO,IAAI;AAAA,EACnB;AACF;;;ACpQA,IAAI,QAAQ,KAAK;AACjB,SAAS,YAAY,KAAK;AACxB,SAAO,OAAO,SAAS,IAAI,GAAG;AAChC;AACA,SAAS,aAAa,KAAK;AACzB,SAAO,OAAO,WAAW,IAAI,SAAS;AACxC;AACA,SAAS,cAAc,OAAO,OAAO,IAAI,OAAO;AAC9C,kBAAgB,SAAU,KAAK,KAAK;AAClC,QAAI,eAAe,QAAQ,UAAU,QAAQ;AAC7C,QAAI,gBAAgB,WAAW,GAAG,GAAG;AACnC,kBAAY,OAAO,OAAO,KAAK,KAAK;AAAA,IACtC,WAAW,gBAAgB,UAAU,GAAG,GAAG;AACzC,iBAAW,IAAI,OAAO,KAAK,KAAK;AAAA,IAClC,OAAO;AACL,YAAM,GAAG,IAAI;AAAA,IACf;AAAA,EACF,GAAG,OAAO,IAAI,KAAK;AACnB,YAAU,IAAI,OAAO,KAAK;AAC5B;AACA,SAAS,cAAc,GAAG;AACxB,SAAO,aAAa,EAAE,CAAC,IAAI,CAAC,KAAK,aAAa,EAAE,CAAC,CAAC,KAAK,aAAa,EAAE,CAAC,CAAC,KAAK,aAAa,EAAE,CAAC,IAAI,CAAC;AACpG;AACA,SAAS,YAAY,GAAG;AACtB,SAAO,aAAa,EAAE,CAAC,CAAC,KAAK,aAAa,EAAE,CAAC,CAAC;AAChD;AACA,SAAS,aAAa,OAAO,GAAG,UAAU;AACxC,MAAI,KAAK,EAAE,YAAY,CAAC,KAAK,cAAc,CAAC,IAAI;AAC9C,QAAI,MAAM,WAAW,KAAK;AAC1B,UAAM,YAAY,cAAc,CAAC,IAAI,eAAe,MAAM,EAAE,CAAC,IAAI,GAAG,IAAI,MAAM,MAAM,MAAM,EAAE,CAAC,IAAI,GAAG,IAAI,MAAM,MAAM,aAAa,CAAC;AAAA,EACpI;AACF;AACA,SAAS,iBAAiB,OAAO,OAAO,KAAK;AAC3C,MAAI,SAAS,MAAM;AACnB,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAO,KAAK,MAAM,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG;AAC3C,WAAO,KAAK,MAAM,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG;AAAA,EAC7C;AACA,QAAM,SAAS,OAAO,KAAK,GAAG;AAChC;AACA,SAAS,kBAAkB,OAAO;AAChC,SAAO,CAAC,MAAM;AAChB;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI,iBAAiB,IAAI,MAAM,SAAU,MAAM;AAC7C,WAAO,OAAO,SAAS,WAAW,CAAC,MAAM,IAAI,IAAI;AAAA,EACnD,CAAC;AACD,SAAO,SAAU,OAAO,OAAO,KAAK;AAClC,aAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,UAAI,OAAO,eAAe,CAAC;AAC3B,UAAI,MAAM,MAAM,KAAK,CAAC,CAAC;AACvB,UAAI,OAAO,MAAM;AACf,cAAM,KAAK,CAAC,CAAC,IAAI,MAAM,MAAM,GAAG,IAAI;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ,CAAC,mBAAmB,CAAC,MAAM,MAAM,GAAG,CAAC,CAAC;AAAA,EAC9C,UAAU,CAAC,kBAAkB,iBAAiB;AAAA,EAC9C,SAAS,CAAC,kBAAkB,iBAAiB;AAC/C;AACA,SAAS,kBAAkB,IAAI;AAC7B,MAAI,YAAY,GAAG;AACnB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,UAAU,CAAC,EAAE,eAAe,SAAS;AACvC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,aAAa,IAAI,OAAO;AACtC,MAAI,QAAQ,GAAG;AACf,MAAI,QAAQ,GAAG;AACf,MAAI,gBAAgB,iBAAiB,GAAG,IAAI;AAC5C,MAAI,QAAQ,CAAC;AACb,MAAI,eAAe,MAAM;AACzB,MAAI,YAAY;AAChB,MAAI,gBAAgB,GAAG,MAAM;AAC7B,MAAI,YAAY,MAAM,YAAY,iBAAiB,EAAE,KAAK;AAC1D,MAAI,iBAAiB,CAAC,MAAM,cAAc,EAAE,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,KAAK,MAAM,EAAE,gBAAgB,kBAAkB,EAAE,MAAM,EAAE,gBAAgB,IAAI;AAC7J,gBAAY,GAAG;AACf,QAAI,MAAM,KAAK,IAAI,IAAI,SAAS;AAChC,kBAAc,CAAC,EAAE,OAAO,OAAO,GAAG;AAAA,EACpC,OAAO;AACL,QAAI,gBAAgB,CAAC,GAAG,QAAQ,GAAG,aAAa;AAChD,QAAI,CAAC,GAAG,MAAM;AACZ,SAAG,gBAAgB;AAAA,IACrB;AACA,QAAI,OAAO,GAAG;AACd,QAAI,eAAe;AACjB,WAAK,UAAU;AACf,SAAG,UAAU,MAAM,GAAG,KAAK;AAC3B,SAAG,YAAY;AAAA,IACjB;AACA,QAAI,cAAc,KAAK,WAAW;AAClC,QAAI,QAAQ;AACZ,QAAI,iBAAiB,MAAM;AAC3B,QAAI,MAAM,qBAAqB,eAAe,CAAC,kBAAkB,kBAAkB,MAAM,wBAAwB;AAC/G,UAAI,CAAC,gBAAgB;AACnB,yBAAiB,MAAM,mBAAmB,IAAI,yBAAiB;AAAA,MACjE;AACA,qBAAe,MAAM,SAAS;AAC9B,WAAK,YAAY,gBAAgB,aAAa;AAC9C,qBAAe,YAAY;AAC3B,YAAM,mBAAmB;AACzB,YAAM,yBAAyB;AAAA,IACjC;AACA,UAAM,IAAI,eAAe,OAAO;AAAA,EAClC;AACA,eAAa,OAAO,GAAG,SAAS;AAChC,gBAAc,OAAO,OAAO,IAAI,KAAK;AACrC,QAAM,aAAa,mBAAmB,IAAI,OAAO,KAAK;AACtD,SAAO,YAAY,WAAW,GAAG,KAAK,IAAI,KAAK;AACjD;AACO,SAAS,cAAc,IAAI,OAAO;AACvC,MAAI,QAAQ,GAAG;AACf,MAAI,QAAQ,MAAM;AAClB,MAAI,SAAS,CAAC,SAAS,KAAK,GAAG;AAC7B,QAAI,YAAY,KAAK,GAAG;AACtB,cAAQ,MAAM;AAAA,IAChB,WAAW,aAAa,KAAK,GAAG;AAC9B,cAAQ,MAAM,UAAU;AAAA,IAC1B;AAAA,EACF;AACA,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AACA,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,KAAK,MAAM;AACf,MAAI,KAAK,MAAM;AACf,MAAI,QAAQ;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACA,MAAI,GAAG;AACL,UAAM,IAAI;AAAA,EACZ;AACA,MAAI,GAAG;AACL,UAAM,IAAI;AAAA,EACZ;AACA,eAAa,OAAO,GAAG,SAAS;AAChC,gBAAc,OAAO,OAAO,IAAI,KAAK;AACrC,QAAM,aAAa,mBAAmB,IAAI,OAAO,KAAK;AACtD,SAAO,YAAY,SAAS,GAAG,KAAK,IAAI,KAAK;AAC/C;AAEO,SAAS,cAAc,IAAI,OAAO;AACvC,MAAI,QAAQ,GAAG;AACf,MAAI,OAAO,MAAM;AACjB,UAAQ,SAAS,QAAQ;AACzB,MAAI,CAAC,QAAQ,MAAM,MAAM,CAAC,KAAK,MAAM,MAAM,CAAC,GAAG;AAC7C;AAAA,EACF;AACA,MAAI,OAAO,MAAM,QAAQ;AACzB,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,IAAI,YAAY,MAAM,KAAK,GAAG,cAAc,IAAI,GAAG,MAAM,YAAY;AACzE,MAAI,YAAY,qBAAqB,MAAM,SAAS,KAAK,MAAM;AAC/D,MAAI,QAAQ;AAAA,IACV,qBAAqB;AAAA,IACrB,eAAe;AAAA,EACjB;AACA,MAAI,gBAAgB,KAAK,GAAG;AAC1B,QAAI,mBAAmB;AACvB,QAAI,YAAY,MAAM;AACtB,QAAI,WAAW,cAAc,MAAM,QAAQ;AAC3C,QAAI,CAAC,WAAW,QAAQ,GAAG;AACzB;AAAA,IACF;AACA,QAAI,aAAa,MAAM,cAAc;AACrC,QAAI,aAAa,MAAM;AACvB,wBAAoB,eAAe,WAAW,kBAAkB,aAAa;AAC7E,QAAI,aAAa,cAAc,UAAU;AACvC,0BAAoB,gBAAgB,YAAY;AAAA,IAClD;AACA,QAAI,cAAc,eAAe,UAAU;AACzC,0BAAoB,iBAAiB,aAAa;AAAA,IACpD;AACA,UAAM,QAAQ;AAAA,EAChB,OAAO;AACL,UAAM,QAAQ,WAAW;AAAA,EAC3B;AACA,MAAI,KAAK,MAAM,IAAI,GAAG;AACpB,UAAM,WAAW,IAAI;AAAA,EACvB;AACA,MAAI,GAAG;AACL,UAAM,IAAI;AAAA,EACZ;AACA,MAAI,GAAG;AACL,UAAM,IAAI;AAAA,EACZ;AACA,eAAa,OAAO,GAAG,SAAS;AAChC,gBAAc,OAAO,OAAO,IAAI,KAAK;AACrC,QAAM,aAAa,mBAAmB,IAAI,OAAO,KAAK;AACtD,SAAO,YAAY,QAAQ,GAAG,KAAK,IAAI,OAAO,QAAW,IAAI;AAC/D;AACO,SAASC,OAAM,IAAI,OAAO;AAC/B,MAAI,cAAc,cAAM;AACtB,WAAO,aAAa,IAAI,KAAK;AAAA,EAC/B,WAAW,cAAc,eAAS;AAChC,WAAO,cAAc,IAAI,KAAK;AAAA,EAChC,WAAW,cAAc,eAAO;AAC9B,WAAO,cAAc,IAAI,KAAK;AAAA,EAChC;AACF;AACA,SAAS,UAAU,IAAI,OAAO,OAAO;AACnC,MAAI,QAAQ,GAAG;AACf,MAAI,UAAU,KAAK,GAAG;AACpB,QAAI,YAAY,aAAa,EAAE;AAC/B,QAAI,cAAc,MAAM;AACxB,QAAI,WAAW,YAAY,SAAS;AACpC,QAAI,CAAC,UAAU;AACb,UAAI,cAAc,GAAG,eAAe;AACpC,UAAI,SAAS,YAAY,CAAC;AAC1B,UAAI,SAAS,YAAY,CAAC;AAC1B,UAAI,CAAC,UAAU,CAAC,QAAQ;AACtB;AAAA,MACF;AACA,UAAI,UAAU,MAAM,iBAAiB;AACrC,UAAI,UAAU,MAAM,iBAAiB;AACrC,UAAI,SAAS,MAAM;AACnB,UAAI,KAAK,eAAe,MAAM,WAAW,GACvC,UAAU,GAAG,SACb,QAAQ,GAAG;AACb,UAAI,QAAQ,SAAS,IAAI;AACzB,UAAI,QAAQ,SAAS,IAAI;AACzB,UAAI,eAAe,QAAQ,MAAM;AACjC,iBAAW,MAAM,OAAO,OAAO,MAAM;AACrC,YAAM,KAAK,QAAQ,IAAI,YAAY,UAAU,UAAU;AAAA,QACrD,MAAM;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,CAAC,YAAY,gBAAgB,IAAI;AAAA,QAClC,MAAM,UAAU;AAAA,QAChB,MAAM,UAAU;AAAA,QAChB,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,iBAAiB;AAAA,MACnB,CAAC,CAAC,CAAC;AACH,kBAAY,SAAS,IAAI;AAAA,IAC3B;AACA,UAAM,SAAS,SAAS,QAAQ;AAAA,EAClC;AACF;AACO,SAAS,YAAY,OAAO,OAAO,QAAQ,OAAO;AACvD,MAAI,MAAM,MAAM,MAAM;AACtB,MAAI;AACJ,MAAI,gBAAgB;AAAA,IAClB,iBAAiB,IAAI,SAAS,mBAAmB;AAAA,EACnD;AACA,MAAI,iBAAiB,GAAG,GAAG;AACzB,kBAAc;AACd,kBAAc,KAAK,IAAI;AACvB,kBAAc,KAAK,IAAI;AACvB,kBAAc,KAAK,IAAI;AACvB,kBAAc,KAAK,IAAI;AAAA,EACzB,WAAW,iBAAiB,GAAG,GAAG;AAChC,kBAAc;AACd,kBAAc,KAAK,UAAU,IAAI,GAAG,GAAG;AACvC,kBAAc,KAAK,UAAU,IAAI,GAAG,GAAG;AACvC,kBAAc,IAAI,UAAU,IAAI,GAAG,GAAG;AAAA,EACxC,OAAO;AACL,QAAI,MAAuC;AACzC,eAAS,wBAAwB;AAAA,IACnC;AACA;AAAA,EACF;AACA,MAAI,SAAS,IAAI;AACjB,MAAI,aAAa,CAAC;AAClB,WAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,EAAE,GAAG;AACjD,QAAI,SAAS,OAAO,OAAO,CAAC,EAAE,MAAM,IAAI,MAAM;AAC9C,QAAI,YAAY,OAAO,CAAC,EAAE;AAC1B,QAAI,KAAK,eAAe,SAAS,GAC/B,QAAQ,GAAG,OACX,UAAU,GAAG;AACf,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,IACZ;AACA,eAAW,YAAY,IAAI;AAC3B,QAAI,UAAU,GAAG;AACf,iBAAW,cAAc,IAAI;AAAA,IAC/B;AACA,eAAW,KAAK,YAAY,QAAQ,IAAI,IAAI,UAAU,CAAC;AAAA,EACzD;AACA,MAAI,gBAAgB,YAAY,aAAa,IAAI,eAAe,UAAU;AAC1E,MAAI,cAAc,cAAc,aAAa;AAC7C,MAAI,gBAAgB,MAAM;AAC1B,MAAI,aAAa,cAAc,WAAW;AAC1C,MAAI,CAAC,YAAY;AACf,iBAAa,MAAM,OAAO,OAAO,MAAM;AACvC,kBAAc,WAAW,IAAI;AAC7B,kBAAc,KAAK;AACnB,UAAM,KAAK,UAAU,IAAI,YAAY,aAAa,YAAY,eAAe,UAAU;AAAA,EACzF;AACA,QAAM,MAAM,IAAI,SAAS,UAAU;AACrC;AACO,SAAS,WAAW,IAAI,OAAO,QAAQ,OAAO;AACnD,MAAI,MAAM,GAAG,MAAM,MAAM;AACzB,MAAI,eAAe,GAAG,gBAAgB;AACtC,MAAI,eAAe,CAAC;AACpB,MAAI,SAAS,IAAI;AACjB,MAAI,WAAW,WAAW;AAC1B,MAAI,UAAU,WAAW;AACzB,MAAI,UAAU,WAAW;AACzB,MAAI;AACJ,MAAI,eAAe,GAAG,GAAG;AACvB,QAAI,eAAe,IAAI;AACvB,QAAI,gBAAgB,IAAI;AACxB,QAAI,WAAW;AACf,QAAI,eAAe,IAAI;AACvB,QAAI,SAAS,YAAY,GAAG;AAC1B,iBAAW;AAAA,IACb,WAAW,YAAY,YAAY,GAAG;AACpC,iBAAW,aAAa;AAAA,IAC1B,WAAW,aAAa,YAAY,GAAG;AACrC,iBAAW,aAAa,UAAU;AAAA,IACpC;AACA,QAAI,OAAO,UAAU,aAAa;AAChC,UAAI,SAAS;AACb,aAAO,cAAc,MAAM;AAC3B,aAAO,eAAe,MAAM;AAAA,IAC9B,WAAW,gBAAgB,QAAQ,iBAAiB,MAAM;AACxD,UAAI,mBAAmB,SAAU,OAAO,KAAK;AAC3C,YAAI,OAAO;AACT,cAAI,QAAQ,MAAM;AAClB,cAAI,QAAQ,gBAAgB,IAAI;AAChC,cAAI,SAAS,iBAAiB,IAAI;AAClC,cAAI,MAAM,QAAQ,WAAW;AAC3B,gBAAI,SAAS;AACX,uBAAS;AACT,uBAAS,aAAa;AAAA,YACxB,WAAW,SAAS;AAClB,sBAAQ;AACR,wBAAU,aAAa;AAAA,YACzB;AAAA,UACF;AACA,gBAAM,MAAM,QAAQ;AACpB,gBAAM,MAAM,SAAS;AACrB,cAAI,OAAO;AACT,kBAAM,aAAa,SAAS,KAAK;AACjC,kBAAM,aAAa,UAAU,MAAM;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AACA,UAAI,eAAe,oBAAoB,UAAU,MAAM,IAAI,SAAU,KAAK;AACxE,oBAAY,iBAAiB,cAAc,GAAG;AAC9C,yBAAiB,OAAO,GAAG;AAAA,MAC7B,CAAC;AACD,UAAI,gBAAgB,aAAa,SAAS,aAAa,QAAQ;AAC7D,uBAAe,gBAAgB,aAAa;AAC5C,wBAAgB,iBAAiB,aAAa;AAAA,MAChD;AAAA,IACF;AACA,YAAQ,YAAY,SAAS,OAAO;AAAA,MAClC,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,iBAAa,QAAQ;AACrB,iBAAa,SAAS;AAAA,EACxB,WAAW,IAAI,YAAY;AACzB,YAAQ,MAAM,IAAI,UAAU;AAC5B,iBAAa,QAAQ,IAAI;AACzB,iBAAa,SAAS,IAAI;AAAA,EAC5B;AACA,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AACA,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU;AACZ,mBAAe,gBAAgB;AAAA,EACjC,WAAW,SAAS;AAClB,oBAAgB;AAChB,mBAAe,aAAa,QAAQ,aAAa;AAAA,EACnD,WAAW,SAAS;AAClB,mBAAe;AACf,oBAAgB,aAAa,SAAS,aAAa;AAAA,EACrD,OAAO;AACL,iBAAa,eAAe;AAAA,EAC9B;AACA,MAAI,gBAAgB,QAAQ,CAAC,MAAM,YAAY,GAAG;AAChD,iBAAa,QAAQ;AAAA,EACvB;AACA,MAAI,iBAAiB,QAAQ,CAAC,MAAM,aAAa,GAAG;AAClD,iBAAa,SAAS;AAAA,EACxB;AACA,MAAI,mBAAmB,sBAAsB,GAAG;AAChD,uBAAqB,aAAa,mBAAmB;AACrD,MAAI,eAAe,YAAY,WAAW,IAAI,cAAc,CAAC,KAAK,CAAC;AACnE,MAAI,aAAa,cAAc,YAAY;AAC3C,MAAI,eAAe,MAAM;AACzB,MAAI,YAAY,aAAa,UAAU;AACvC,MAAI,CAAC,WAAW;AACd,gBAAY,MAAM,OAAO,OAAO,MAAM;AACtC,iBAAa,UAAU,IAAI;AAC3B,iBAAa,KAAK;AAClB,mBAAe,MAAM,KAAK,SAAS,IAAI,YAAY,WAAW,WAAW,cAAc,CAAC,KAAK,CAAC;AAAA,EAChG;AACA,QAAM,MAAM,IAAI,SAAS,SAAS;AACpC;AACO,SAAS,YAAY,UAAU,OAAO,OAAO;AAClD,MAAI,gBAAgB,MAAM,eACxB,OAAO,MAAM;AACf,MAAI,aAAa,cAAc,SAAS,EAAE;AAC1C,MAAI,CAAC,YAAY;AACf,iBAAa,MAAM,OAAO,OAAO,MAAM;AACvC,QAAI,gBAAgB;AAAA,MAClB,IAAI;AAAA,IACN;AACA,kBAAc,SAAS,EAAE,IAAI;AAC7B,SAAK,UAAU,IAAI,YAAY,YAAY,YAAY,eAAe,CAAC,aAAa,UAAU,KAAK,CAAC,CAAC;AAAA,EACvG;AACA,QAAM,WAAW,IAAI,SAAS,UAAU;AAC1C;;;AChbO,SAAS,eAAe,MAAM;AACnC,SAAO,SAAS,eAAe,IAAI;AACrC;AAIO,SAAS,aAAaC,aAAY,SAAS,eAAe;AAC/D,EAAAA,YAAW,aAAa,SAAS,aAAa;AAChD;AACO,SAAS,YAAY,MAAM,OAAO;AACvC,OAAK,YAAY,KAAK;AACxB;AACO,SAAS,YAAY,MAAM,OAAO;AACvC,OAAK,YAAY,KAAK;AACxB;AACO,SAAS,WAAW,MAAM;AAC/B,SAAO,KAAK;AACd;AACO,SAAS,YAAY,MAAM;AAChC,SAAO,KAAK;AACd;AAIO,SAAS,eAAe,MAAM,MAAM;AACzC,OAAK,cAAc;AACrB;;;ACvBA,IAAI,YAAY;AAChB,IAAI,QAAQ;AACZ,IAAI,YAAY,YAAY,IAAI,EAAE;AAClC,SAAS,QAAQ,GAAG;AAClB,SAAO,MAAM;AACf;AACA,SAAS,MAAM,GAAG;AAChB,SAAO,MAAM;AACf;AACA,SAAS,kBAAkB,UAAU,UAAU,QAAQ;AACrD,MAAIC,OAAM,CAAC;AACX,WAAS,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAG;AACvC,QAAI,MAAM,SAAS,CAAC,EAAE;AACtB,QAAI,QAAQ,QAAW;AACrB,UAAI,MAAuC;AACzC,YAAIA,KAAI,GAAG,KAAK,MAAM;AACpB,kBAAQ,MAAM,mBAAmB,GAAG;AAAA,QACtC;AAAA,MACF;AACA,MAAAA,KAAI,GAAG,IAAI;AAAA,IACb;AAAA,EACF;AACA,SAAOA;AACT;AACA,SAAS,UAAU,QAAQ,QAAQ;AACjC,MAAI,YAAY,OAAO,QAAQ,OAAO;AACtC,MAAI,YAAY,OAAO,QAAQ,OAAO;AACtC,SAAO,aAAa;AACtB;AACA,SAAS,UAAU,OAAO;AACxB,MAAI;AACJ,MAAI,WAAW,MAAM;AACrB,MAAI,MAAM,MAAM;AAChB,MAAI,MAAM,GAAG,GAAG;AACd,QAAI,MAAM,MAAM,MAAM,cAAc,GAAG;AACvC,gBAAY,WAAW,KAAK;AAC5B,QAAI,QAAQ,QAAQ,GAAG;AACrB,WAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACpC,YAAI,KAAK,SAAS,CAAC;AACnB,YAAI,MAAM,MAAM;AACd,UAAI,YAAY,KAAK,UAAU,EAAE,CAAC;AAAA,QACpC;AAAA,MACF;AAAA,IACF,WAAW,MAAM,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,IAAI,GAAG;AACrD,MAAI,YAAY,KAAS,eAAe,MAAM,IAAI,CAAC;AAAA,IACrD;AAAA,EACF,OAAO;AACL,UAAM,MAAU,eAAe,MAAM,IAAI;AAAA,EAC3C;AACA,SAAO,MAAM;AACf;AACA,SAAS,UAAU,WAAW,QAAQ,QAAQ,UAAU,QAAQ;AAC9D,SAAO,YAAY,QAAQ,EAAE,UAAU;AACrC,QAAI,KAAK,OAAO,QAAQ;AACxB,QAAI,MAAM,MAAM;AACd,MAAI,aAAa,WAAW,UAAU,EAAE,GAAG,MAAM;AAAA,IACnD;AAAA,EACF;AACF;AACA,SAAS,aAAa,WAAW,QAAQ,UAAU,QAAQ;AACzD,SAAO,YAAY,QAAQ,EAAE,UAAU;AACrC,QAAI,KAAK,OAAO,QAAQ;AACxB,QAAI,MAAM,MAAM;AACd,UAAI,MAAM,GAAG,GAAG,GAAG;AACjB,YAAI,WAAe,WAAW,GAAG,GAAG;AACpC,QAAI,YAAY,UAAU,GAAG,GAAG;AAAA,MAClC,OAAO;AACL,QAAI,YAAY,WAAW,GAAG,GAAG;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACF;AACO,SAAS,YAAY,UAAU,OAAO;AAC3C,MAAI;AACJ,MAAI,MAAM,MAAM;AAChB,MAAI,WAAW,YAAY,SAAS,SAAS,CAAC;AAC9C,MAAI,QAAQ,MAAM,SAAS,CAAC;AAC5B,MAAI,aAAa,OAAO;AACtB;AAAA,EACF;AACA,OAAK,OAAO,OAAO;AACjB,QAAI,MAAM,MAAM,GAAG;AACnB,QAAI,MAAM,SAAS,GAAG;AACtB,QAAI,QAAQ,KAAK;AACf,UAAI,QAAQ,MAAM;AAChB,YAAI,aAAa,KAAK,EAAE;AAAA,MAC1B,WAAW,QAAQ,OAAO;AACxB,YAAI,gBAAgB,GAAG;AAAA,MACzB,OAAO;AACL,YAAI,IAAI,WAAW,CAAC,MAAM,OAAO;AAC/B,cAAI,aAAa,KAAK,GAAG;AAAA,QAC3B,WAAW,QAAQ,iBAAiB,QAAQ,SAAS;AACnD,cAAI,eAAe,OAAO,KAAK,GAAG;AAAA,QACpC,WAAW,IAAI,WAAW,CAAC,MAAM,WAAW;AAC1C,cAAI,eAAe,eAAe,KAAK,GAAG;AAAA,QAC5C,WAAW,IAAI,WAAW,CAAC,MAAM,WAAW;AAC1C,cAAI,eAAe,SAAS,KAAK,GAAG;AAAA,QACtC,OAAO;AACL,cAAI,aAAa,KAAK,GAAG;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,OAAK,OAAO,UAAU;AACpB,QAAI,EAAE,OAAO,QAAQ;AACnB,UAAI,gBAAgB,GAAG;AAAA,IACzB;AAAA,EACF;AACF;AACA,SAAS,eAAe,WAAW,OAAO,OAAO;AAC/C,MAAI,cAAc;AAClB,MAAI,cAAc;AAClB,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,gBAAgB,MAAM,CAAC;AAC3B,MAAI,cAAc,MAAM,SAAS;AACjC,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,gBAAgB,MAAM,CAAC;AAC3B,MAAI,cAAc,MAAM,SAAS;AACjC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAO,eAAe,aAAa,eAAe,WAAW;AAC3D,QAAI,iBAAiB,MAAM;AACzB,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,WAAW,eAAe,MAAM;AAC9B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,iBAAiB,MAAM;AAChC,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,WAAW,eAAe,MAAM;AAC9B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,UAAU,eAAe,aAAa,GAAG;AAClD,iBAAW,eAAe,aAAa;AACvC,sBAAgB,MAAM,EAAE,WAAW;AACnC,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,WAAW,UAAU,aAAa,WAAW,GAAG;AAC9C,iBAAW,aAAa,WAAW;AACnC,oBAAc,MAAM,EAAE,SAAS;AAC/B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,UAAU,eAAe,WAAW,GAAG;AAChD,iBAAW,eAAe,WAAW;AACrC,MAAI,aAAa,WAAW,cAAc,KAAS,YAAY,YAAY,GAAG,CAAC;AAC/E,sBAAgB,MAAM,EAAE,WAAW;AACnC,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,UAAU,aAAa,aAAa,GAAG;AAChD,iBAAW,aAAa,aAAa;AACrC,MAAI,aAAa,WAAW,YAAY,KAAK,cAAc,GAAG;AAC9D,oBAAc,MAAM,EAAE,SAAS;AAC/B,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,OAAO;AACL,UAAI,QAAQ,WAAW,GAAG;AACxB,sBAAc,kBAAkB,OAAO,aAAa,SAAS;AAAA,MAC/D;AACA,iBAAW,YAAY,cAAc,GAAG;AACxC,UAAI,QAAQ,QAAQ,GAAG;AACrB,QAAI,aAAa,WAAW,UAAU,aAAa,GAAG,cAAc,GAAG;AAAA,MACzE,OAAO;AACL,oBAAY,MAAM,QAAQ;AAC1B,YAAI,UAAU,QAAQ,cAAc,KAAK;AACvC,UAAI,aAAa,WAAW,UAAU,aAAa,GAAG,cAAc,GAAG;AAAA,QACzE,OAAO;AACL,qBAAW,WAAW,aAAa;AACnC,gBAAM,QAAQ,IAAI;AAClB,UAAI,aAAa,WAAW,UAAU,KAAK,cAAc,GAAG;AAAA,QAC9D;AAAA,MACF;AACA,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC;AAAA,EACF;AACA,MAAI,eAAe,aAAa,eAAe,WAAW;AACxD,QAAI,cAAc,WAAW;AAC3B,eAAS,MAAM,YAAY,CAAC,KAAK,OAAO,OAAO,MAAM,YAAY,CAAC,EAAE;AACpE,gBAAU,WAAW,QAAQ,OAAO,aAAa,SAAS;AAAA,IAC5D,OAAO;AACL,mBAAa,WAAW,OAAO,aAAa,SAAS;AAAA,IACvD;AAAA,EACF;AACF;AACA,SAAS,WAAW,UAAU,OAAO;AACnC,MAAI,MAAM,MAAM,MAAM,SAAS;AAC/B,MAAI,QAAQ,SAAS;AACrB,MAAI,KAAK,MAAM;AACf,MAAI,aAAa,OAAO;AACtB;AAAA,EACF;AACA,cAAY,UAAU,KAAK;AAC3B,MAAI,QAAQ,MAAM,IAAI,GAAG;AACvB,QAAI,MAAM,KAAK,KAAK,MAAM,EAAE,GAAG;AAC7B,UAAI,UAAU,IAAI;AAChB,uBAAe,KAAK,OAAO,EAAE;AAAA,MAC/B;AAAA,IACF,WAAW,MAAM,EAAE,GAAG;AACpB,UAAI,MAAM,SAAS,IAAI,GAAG;AACxB,QAAI,eAAe,KAAK,EAAE;AAAA,MAC5B;AACA,gBAAU,KAAK,MAAM,IAAI,GAAG,GAAG,SAAS,CAAC;AAAA,IAC3C,WAAW,MAAM,KAAK,GAAG;AACvB,mBAAa,KAAK,OAAO,GAAG,MAAM,SAAS,CAAC;AAAA,IAC9C,WAAW,MAAM,SAAS,IAAI,GAAG;AAC/B,MAAI,eAAe,KAAK,EAAE;AAAA,IAC5B;AAAA,EACF,WAAW,SAAS,SAAS,MAAM,MAAM;AACvC,QAAI,MAAM,KAAK,GAAG;AAChB,mBAAa,KAAK,OAAO,GAAG,MAAM,SAAS,CAAC;AAAA,IAC9C;AACA,IAAI,eAAe,KAAK,MAAM,IAAI;AAAA,EACpC;AACF;AACe,SAAR,MAAuB,UAAU,OAAO;AAC7C,MAAI,UAAU,UAAU,KAAK,GAAG;AAC9B,eAAW,UAAU,KAAK;AAAA,EAC5B,OAAO;AACL,QAAI,MAAM,SAAS;AACnB,QAAI,WAAe,WAAW,GAAG;AACjC,cAAU,KAAK;AACf,QAAI,aAAa,MAAM;AACrB,MAAI,aAAa,UAAU,MAAM,KAAS,YAAY,GAAG,CAAC;AAC1D,mBAAa,UAAU,CAAC,QAAQ,GAAG,GAAG,CAAC;AAAA,IACzC;AAAA,EACF;AACA,SAAO;AACT;;;AC1NA,IAAI,QAAQ;AACZ,IAAI,aAAa,WAAY;AAC3B,WAASC,YAAW,MAAM,SAAS,MAAM;AACvC,SAAK,OAAO;AACZ,SAAK,eAAe,uBAAuB,cAAc;AACzD,SAAK,cAAc,uBAAuB,aAAa;AACvD,SAAK,UAAU;AACf,SAAK,QAAQ,OAAO,OAAO,CAAC,GAAG,IAAI;AACnC,SAAK,OAAO;AACZ,SAAK,MAAM,OAAO;AAClB,SAAK,YAAY,eAAe,KAAK,OAAO,KAAK,MAAM;AACvD,QAAI,QAAQ,CAAC,KAAK,KAAK;AACrB,UAAI,WAAW,KAAK,YAAY,SAAS,cAAc,KAAK;AAC5D,eAAS,MAAM,UAAU;AACzB,UAAI,SAAS,KAAK,UAAU,KAAK,UAAU,MAAM,cAAc,KAAK;AACpE,kBAAY,MAAM,KAAK,SAAS;AAChC,eAAS,YAAY,MAAM;AAC3B,WAAK,YAAY,QAAQ;AAAA,IAC3B;AACA,SAAK,OAAO,KAAK,OAAO,KAAK,MAAM;AAAA,EACrC;AACA,EAAAA,YAAW,UAAU,UAAU,WAAY;AACzC,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,YAAW,UAAU,kBAAkB,WAAY;AACjD,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,YAAW,UAAU,wBAAwB,WAAY;AACvD,QAAI,eAAe,KAAK,gBAAgB;AACxC,QAAI,cAAc;AAChB,aAAO;AAAA,QACL,YAAY,aAAa,cAAc;AAAA,QACvC,WAAW,aAAa,aAAa;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AACA,EAAAA,YAAW,UAAU,YAAY,WAAY;AAC3C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,YAAW,UAAU,UAAU,WAAY;AACzC,QAAI,KAAK,MAAM;AACb,UAAI,QAAQ,KAAK,cAAc;AAAA,QAC7B,YAAY;AAAA,MACd,CAAC;AACD,YAAM,MAAM,QAAQ;AACpB,YAAM,KAAK,WAAW,KAAK;AAC3B,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AACA,EAAAA,YAAW,UAAU,mBAAmB,SAAU,IAAI;AACpD,WAAOC,OAAM,IAAI,iBAAiB,KAAK,GAAG,CAAC;AAAA,EAC7C;AACA,EAAAD,YAAW,UAAU,gBAAgB,SAAU,MAAM;AACnD,WAAO,QAAQ,CAAC;AAChB,QAAI,OAAO,KAAK,QAAQ,eAAe,IAAI;AAC3C,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,KAAK;AAClB,QAAI,QAAQ,iBAAiB,KAAK,GAAG;AACrC,UAAM,YAAY,KAAK;AACvB,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,KAAK;AACtB,QAAI,WAAW,CAAC;AAChB,QAAI,UAAU,KAAK,WAAW,sBAAsB,OAAO,QAAQ,KAAK,kBAAkB,KAAK;AAC/F,eAAW,SAAS,KAAK,OAAO;AAChC,QAAI,YAAY,CAAC,KAAK,WAAW,KAAK,aAAa,YAAY,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;AACtF,SAAK,WAAW,MAAM,OAAO,YAAY,UAAU,WAAW,QAAQ;AACtE,iBAAa,SAAS,KAAK,SAAS;AACpC,QAAI,OAAO,IAAI,KAAK,MAAM,IAAI,GAAG,SAAU,IAAI;AAC7C,aAAO,MAAM,KAAK,EAAE;AAAA,IACtB,CAAC;AACD,QAAI,KAAK,QAAQ;AACf,eAAS,KAAK,YAAY,QAAQ,QAAQ,CAAC,GAAG,IAAI,CAAC;AAAA,IACrD;AACA,QAAI,KAAK,WAAW;AAClB,UAAI,kBAAkB,aAAa,MAAM,UAAU,MAAM,UAAU;AAAA,QACjE,SAAS;AAAA,MACX,CAAC;AACD,UAAI,iBAAiB;AACnB,YAAI,YAAY,YAAY,SAAS,OAAO,CAAC,GAAG,CAAC,GAAG,eAAe;AACnE,iBAAS,KAAK,SAAS;AAAA,MACzB;AAAA,IACF;AACA,WAAO,eAAe,OAAO,QAAQ,UAAU,KAAK,UAAU;AAAA,EAChE;AACA,EAAAA,YAAW,UAAU,iBAAiB,SAAU,MAAM;AACpD,WAAO,QAAQ,CAAC;AAChB,WAAO,cAAc,KAAK,cAAc;AAAA,MACtC,WAAW,UAAU,KAAK,cAAc,IAAI;AAAA,MAC5C,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY,UAAU,KAAK,YAAY,IAAI;AAAA,IAC7C,CAAC,GAAG;AAAA,MACF,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,EAAAA,YAAW,UAAU,qBAAqB,SAAU,iBAAiB;AACnE,SAAK,mBAAmB;AAAA,EAC1B;AACA,EAAAA,YAAW,UAAU,aAAa,WAAY;AAC5C,WAAO,KAAK,cAAc,KAAK,WAAW;AAAA,EAC5C;AACA,EAAAA,YAAW,UAAU,aAAa,SAAU,MAAM,OAAO,KAAK;AAC5D,QAAI,UAAU,KAAK;AACnB,QAAI,uBAAuB,CAAC;AAC5B,QAAI,4BAA4B;AAChC,QAAI;AACJ,QAAI;AACJ,QAAI,mBAAmB;AACvB,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,UAAI,cAAc,KAAK,CAAC;AACxB,UAAI,CAAC,YAAY,WAAW;AAC1B,YAAI,YAAY,YAAY;AAC5B,YAAI,MAAM,aAAa,UAAU,UAAU;AAC3C,YAAI,UAAU,iBAAiB,cAAc,UAAU;AACvD,YAAI,MAAM;AACV,aAAK,MAAM,KAAK,IAAI,MAAM,GAAG,UAAU,CAAC,GAAG,OAAO,GAAG,OAAO;AAC1D,cAAI,aAAa,iBAAiB,UAAU,GAAG,MAAM,cAAc,GAAG,GAAG;AACvE;AAAA,UACF;AAAA,QACF;AACA,iBAAS,MAAM,UAAU,GAAG,MAAM,KAAK,OAAO;AAC5C;AACA,iCAAuB,qBAAqB,4BAA4B,CAAC;AAAA,QAC3E;AACA,iBAAS,MAAM,MAAM,GAAG,MAAM,KAAK,OAAO;AACxC,cAAI,aAAa,CAAC;AAClB,sBAAY,UAAU,GAAG,GAAG,YAAY,KAAK;AAC7C,cAAI,IAAI,YAAY,KAAK,YAAY,oBAAoB,YAAY,CAAC,CAAC;AACvE,WAAC,uBAAuB,qBAAqB,WAAW,KAAK,KAAK,CAAC;AACnE,+BAAqB,2BAA2B,IAAI;AACpD,iCAAuB;AAAA,QACzB;AACA,wBAAgB;AAChB,YAAI,MAAMC,OAAM,aAAa,KAAK;AAClC,YAAI,KAAK;AACP,WAAC,uBAAuB,qBAAqB,WAAW,KAAK,KAAK,GAAG;AAAA,QACvE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,EAAAD,YAAW,UAAU,SAAS,SAAU,OAAO,QAAQ;AACrD,QAAI,OAAO,KAAK;AAChB,QAAI,OAAO,KAAK;AAChB,QAAI,WAAW,KAAK;AACpB,aAAS,SAAS,KAAK,QAAQ;AAC/B,cAAU,SAAS,KAAK,SAAS;AACjC,QAAI,QAAQ,UAAU;AACpB,eAAS,MAAM,UAAU;AACzB,cAAQ,QAAQ,MAAM,GAAG,IAAI;AAC7B,eAAS,QAAQ,MAAM,GAAG,IAAI;AAC9B,eAAS,MAAM,UAAU;AAAA,IAC3B;AACA,QAAI,KAAK,WAAW,SAAS,KAAK,YAAY,QAAQ;AACpD,WAAK,SAAS;AACd,WAAK,UAAU;AACf,UAAI,UAAU;AACZ,YAAI,gBAAgB,SAAS;AAC7B,sBAAc,QAAQ,QAAQ;AAC9B,sBAAc,SAAS,SAAS;AAAA,MAClC;AACA,UAAI,CAAC,UAAU,KAAK,gBAAgB,GAAG;AACrC,YAAI,SAAS,KAAK;AAClB,YAAI,QAAQ;AACV,iBAAO,aAAa,SAAS,KAAK;AAClC,iBAAO,aAAa,UAAU,MAAM;AAAA,QACtC;AACA,YAAI,OAAO,KAAK,YAAY,KAAK,SAAS;AAC1C,YAAI,MAAM;AACR,eAAK,aAAa,SAAS,KAAK;AAChC,eAAK,aAAa,UAAU,MAAM;AAAA,QACpC;AAAA,MACF,OAAO;AACL,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,EAAAA,YAAW,UAAU,WAAW,WAAY;AAC1C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,YAAW,UAAU,YAAY,WAAY;AAC3C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,YAAW,UAAU,UAAU,WAAY;AACzC,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,YAAY;AAAA,IACxB;AACA,SAAK,UAAU,KAAK,YAAY,KAAK,UAAU,KAAK,YAAY,KAAK,WAAW,KAAK,aAAa;AAAA,EACpG;AACA,EAAAA,YAAW,UAAU,QAAQ,WAAY;AACvC,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,YAAY;AAAA,IAC3B;AACA,SAAK,YAAY;AAAA,EACnB;AACA,EAAAA,YAAW,UAAU,YAAY,SAAU,QAAQ;AACjD,QAAI,MAAM,KAAK,eAAe;AAC9B,QAAI,SAAS;AACb,QAAI,QAAQ;AACV,YAAM,aAAa,GAAG;AACtB,aAAO,OAAO,SAAS,YAAY;AAAA,IACrC;AACA,WAAO,SAAS,mBAAmB,mBAAmB,GAAG;AAAA,EAC3D;AACA,SAAOA;AACT,EAAE;AACF,SAAS,uBAAuB,QAAQ;AACtC,SAAO,WAAY;AACjB,QAAI,MAAuC;AACzC,eAAS,6CAA6C,SAAS,GAAG;AAAA,IACpE;AAAA,EACF;AACF;AACA,SAAS,sBAAsB,OAAO,QAAQ,iBAAiB,OAAO;AACpE,MAAI;AACJ,MAAI,mBAAmB,oBAAoB,QAAQ;AACjD,cAAU,YAAY,QAAQ,MAAM;AAAA,MAClC;AAAA,MACA;AAAA,MACA,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,IACN,CAAC;AACD,QAAI,WAAW,eAAe,GAAG;AAC/B,kBAAY;AAAA,QACV,MAAM;AAAA,MACR,GAAG,QAAQ,OAAO,QAAQ,KAAK;AAAA,IACjC,WAAW,UAAU,eAAe,GAAG;AACrC,iBAAW;AAAA,QACT,OAAO;AAAA,UACL,MAAM;AAAA,QACR;AAAA,QACA,OAAO;AAAA,QACP,iBAAiB,WAAY;AAC3B,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,GAAG,QAAQ,OAAO,QAAQ,KAAK;AAAA,IACjC,OAAO;AACL,UAAI,KAAK,eAAe,eAAe,GACrC,QAAQ,GAAG,OACX,UAAU,GAAG;AACf,cAAQ,MAAM,OAAO;AACrB,gBAAU,MAAM,QAAQ,MAAM,cAAc,IAAI;AAAA,IAClD;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAO,kBAAQ;;;ACrNR,SAAS,QAAQ,WAAW;AACjC,YAAU,gBAAgB,OAAO,eAAU;AAC7C;;;ACnCA,SAAS,UAAU,IAAI,SAAS,KAAK;AACnC,MAAI,SAAS,YAAY,aAAa;AACtC,MAAI,QAAQ,QAAQ,SAAS;AAC7B,MAAI,SAAS,QAAQ,UAAU;AAC/B,MAAI,cAAc,OAAO;AACzB,MAAI,aAAa;AACf,gBAAY,WAAW;AACvB,gBAAY,OAAO;AACnB,gBAAY,MAAM;AAClB,gBAAY,QAAQ,QAAQ;AAC5B,gBAAY,SAAS,SAAS;AAC9B,WAAO,aAAa,kBAAkB,EAAE;AAAA,EAC1C;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,SAAS,SAAS;AACzB,SAAO;AACT;AAEA,IAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAUE,QAAO,MAAM;AACvB,WAASA,OAAM,IAAI,SAAS,KAAK;AAC/B,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,aAAa;AACnB,UAAM,iBAAiB;AACvB,UAAM,MAAM;AACZ,UAAM,UAAU;AAChB,UAAM,SAAS,CAAC;AAChB,UAAM,cAAc;AACpB,UAAM,SAAS;AACf,UAAM,sBAAsB;AAC5B,UAAM,UAAU;AAChB,UAAM,mBAAmB;AACzB,UAAM,SAAS;AACf,UAAM,cAAc;AACpB,UAAM,eAAe;AACrB,UAAM,aAAa;AACnB,UAAM,mBAAmB;AACzB,UAAM,iBAAiB;AACvB,QAAI;AACJ,UAAM,OAAO;AACb,QAAI,OAAO,OAAO,UAAU;AAC1B,YAAM,UAAU,IAAI,SAAS,GAAG;AAAA,IAClC,WAAgB,SAAS,EAAE,GAAG;AAC5B,YAAM;AACN,WAAK,IAAI;AAAA,IACX;AACA,UAAM,KAAK;AACX,UAAM,MAAM;AACZ,QAAI,WAAW,IAAI;AACnB,QAAI,UAAU;AACZ,MAAK,kBAAkB,GAAG;AAC1B,UAAI,gBAAgB,WAAY;AAC9B,eAAO;AAAA,MACT;AACA,eAAS,UAAU;AACnB,eAAS,SAAS;AAClB,eAAS,cAAc;AAAA,IACzB;AACA,UAAM,UAAU;AAChB,UAAM,MAAM;AACZ,WAAO;AAAA,EACT;AACA,EAAAA,OAAM,UAAU,kBAAkB,WAAY;AAC5C,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AACA,EAAAA,OAAM,UAAU,aAAa,WAAY;AACvC,SAAK,mBAAmB,KAAK;AAC7B,SAAK,iBAAiB,KAAK;AAAA,EAC7B;AACA,EAAAA,OAAM,UAAU,cAAc,WAAY;AACxC,SAAK,MAAM,KAAK,IAAI,WAAW,IAAI;AACnC,SAAK,IAAI,MAAM,KAAK;AAAA,EACtB;AACA,EAAAA,OAAM,UAAU,eAAe,WAAY;AACzC,SAAK,mBAAmB;AAAA,EAC1B;AACA,EAAAA,OAAM,UAAU,mBAAmB,WAAY;AAC7C,QAAI,MAAM,KAAK;AACf,SAAK,UAAU,UAAU,UAAU,KAAK,IAAI,KAAK,SAAS,GAAG;AAC7D,SAAK,UAAU,KAAK,QAAQ,WAAW,IAAI;AAC3C,QAAI,QAAQ,GAAG;AACb,WAAK,QAAQ,MAAM,KAAK,GAAG;AAAA,IAC7B;AAAA,EACF;AACA,EAAAA,OAAM,UAAU,qBAAqB,SAAU,aAAa,UAAU,WAAW,YAAY;AAC3F,QAAI,KAAK,kBAAkB;AACzB,WAAK,mBAAmB;AACxB,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB,CAAC;AAC1B,QAAI,sBAAsB,KAAK;AAC/B,QAAI,OAAO;AACX,QAAI,cAAc,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC7C,aAAS,mBAAmB,MAAM;AAChC,UAAI,CAAC,KAAK,SAAS,KAAK,KAAK,OAAO,GAAG;AACrC;AAAA,MACF;AACA,UAAI,mBAAmB,WAAW,GAAG;AACnC,YAAI,eAAe,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC9C,qBAAa,KAAK,IAAI;AACtB,2BAAmB,KAAK,YAAY;AAAA,MACtC,OAAO;AACL,YAAI,WAAW;AACf,YAAI,eAAe;AACnB,YAAI,qBAAqB;AACzB,iBAASC,KAAI,GAAGA,KAAI,mBAAmB,QAAQ,EAAEA,IAAG;AAClD,cAAI,aAAa,mBAAmBA,EAAC;AACrC,cAAI,WAAW,UAAU,IAAI,GAAG;AAC9B,gBAAI,gBAAgB,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC/C,0BAAc,KAAK,UAAU;AAC7B,0BAAc,MAAM,IAAI;AACxB,+BAAmBA,EAAC,IAAI;AACxB,uBAAW;AACX;AAAA,UACF,WAAW,MAAM;AACf,wBAAY,KAAK,IAAI;AACrB,wBAAY,MAAM,UAAU;AAC5B,gBAAI,QAAQ,KAAK,QAAQ,KAAK;AAC9B,gBAAI,QAAQ,WAAW,QAAQ,WAAW;AAC1C,gBAAI,cAAc,YAAY,QAAQ,YAAY;AAClD,gBAAI,YAAY,cAAc,QAAQ;AACtC,gBAAI,YAAY,cAAc;AAC5B,6BAAe;AACf,mCAAqBA;AAAA,YACvB;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM;AACR,6BAAmB,kBAAkB,EAAE,MAAM,IAAI;AACjD,qBAAW;AAAA,QACb;AACA,YAAI,CAAC,UAAU;AACb,cAAI,eAAe,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC9C,uBAAa,KAAK,IAAI;AACtB,6BAAmB,KAAK,YAAY;AAAA,QACtC;AACA,YAAI,CAAC,MAAM;AACT,iBAAO,mBAAmB,UAAU;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AACA,aAAS,IAAI,KAAK,cAAc,IAAI,KAAK,YAAY,EAAE,GAAG;AACxD,UAAI,KAAK,YAAY,CAAC;AACtB,UAAI,IAAI;AACN,YAAI,cAAc,GAAG,gBAAgB,WAAW,YAAY,MAAM,IAAI;AACtE,YAAI,WAAW,GAAG,iBAAiB,GAAG,UAAU,cAAc,CAAC,eAAe,GAAG,iBAAiB,IAAI;AACtG,YAAI,UAAU;AACZ,6BAAmB,QAAQ;AAAA,QAC7B;AACA,YAAI,UAAU,gBAAgB,GAAG,UAAU,cAAc,CAAC,GAAG,gBAAgB,GAAG,aAAa,IAAI;AACjG,YAAI,SAAS;AACX,6BAAmB,OAAO;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AACA,aAAS,IAAI,KAAK,kBAAkB,IAAI,KAAK,gBAAgB,EAAE,GAAG;AAChE,UAAI,KAAK,SAAS,CAAC;AACnB,UAAI,cAAc,GAAG,gBAAgB,WAAW,YAAY,MAAM,IAAI;AACtE,UAAI,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAG,cAAc;AACvD,YAAI,WAAW,GAAG,iBAAiB;AACnC,YAAI,UAAU;AACZ,6BAAmB,QAAQ;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,OAAG;AACD,yBAAmB;AACnB,eAAS,IAAI,GAAG,IAAI,mBAAmB,UAAS;AAC9C,YAAI,mBAAmB,CAAC,EAAE,OAAO,GAAG;AAClC,6BAAmB,OAAO,GAAG,CAAC;AAC9B;AAAA,QACF;AACA,iBAAS,IAAI,IAAI,GAAG,IAAI,mBAAmB,UAAS;AAClD,cAAI,mBAAmB,CAAC,EAAE,UAAU,mBAAmB,CAAC,CAAC,GAAG;AAC1D,+BAAmB;AACnB,+BAAmB,CAAC,EAAE,MAAM,mBAAmB,CAAC,CAAC;AACjD,+BAAmB,OAAO,GAAG,CAAC;AAAA,UAChC,OAAO;AACL;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,IACF,SAAS;AACT,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AACA,EAAAD,OAAM,UAAU,qBAAqB,WAAY;AAC/C,YAAQ,KAAK,eAAe,CAAC,GAAG,MAAM;AAAA,EACxC;AACA,EAAAA,OAAM,UAAU,SAAS,SAAU,OAAO,QAAQ;AAChD,QAAI,MAAM,KAAK;AACf,QAAI,MAAM,KAAK;AACf,QAAI,WAAW,IAAI;AACnB,QAAI,UAAU,KAAK;AACnB,QAAI,UAAU;AACZ,eAAS,QAAQ,QAAQ;AACzB,eAAS,SAAS,SAAS;AAAA,IAC7B;AACA,QAAI,QAAQ,QAAQ;AACpB,QAAI,SAAS,SAAS;AACtB,QAAI,SAAS;AACX,cAAQ,QAAQ,QAAQ;AACxB,cAAQ,SAAS,SAAS;AAC1B,UAAI,QAAQ,GAAG;AACb,aAAK,QAAQ,MAAM,KAAK,GAAG;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACA,EAAAA,OAAM,UAAU,QAAQ,SAAU,UAAU,YAAY,cAAc;AACpE,QAAI,MAAM,KAAK;AACf,QAAI,MAAM,KAAK;AACf,QAAI,QAAQ,IAAI;AAChB,QAAI,SAAS,IAAI;AACjB,iBAAa,cAAc,KAAK;AAChC,QAAI,iBAAiB,KAAK,cAAc,CAAC;AACzC,QAAI,iBAAiB,KAAK;AAC1B,QAAI,MAAM,KAAK;AACf,QAAI,OAAO;AACX,QAAI,gBAAgB;AAClB,UAAI,CAAC,KAAK,SAAS;AACjB,aAAK,iBAAiB;AAAA,MACxB;AACA,WAAK,QAAQ,2BAA2B;AACxC,WAAK,QAAQ,UAAU,KAAK,GAAG,GAAG,QAAQ,KAAK,SAAS,GAAG;AAAA,IAC7D;AACA,QAAI,UAAU,KAAK;AACnB,aAAS,QAAQ,GAAG,GAAGE,QAAOC,SAAQ;AACpC,UAAI,UAAU,GAAG,GAAGD,QAAOC,OAAM;AACjC,UAAI,cAAc,eAAe,eAAe;AAC9C,YAAI,8BAA8B;AAClC,YAAS,iBAAiB,UAAU,GAAG;AACrC,cAAI,cAAc,WAAW,UAAU,WAAW,YAAYD,UAAS,WAAW,aAAaC;AAC/F,wCAA8B,eAAe,WAAW,oBAAoB,kBAAkB,KAAK,YAAY;AAAA,YAC7G,GAAG;AAAA,YACH,GAAG;AAAA,YACH,OAAOD;AAAA,YACP,QAAQC;AAAA,UACV,CAAC;AACD,qBAAW,mBAAmB;AAC9B,qBAAW,UAAUD;AACrB,qBAAW,WAAWC;AAAA,QACxB,WAAgB,qBAAqB,UAAU,GAAG;AAChD,qBAAW,SAAS,WAAW,UAAU;AACzC,qBAAW,SAAS,WAAW,UAAU;AACzC,wCAA8B,oBAAoB,KAAK,YAAY;AAAA,YACjE,OAAO,WAAY;AACjB,mBAAK,aAAa;AAClB,mBAAK,UAAU,QAAQ;AAAA,YACzB;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,KAAK;AACT,YAAI,YAAY,+BAA+B;AAC/C,YAAI,SAAS,GAAG,GAAGD,QAAOC,OAAM;AAChC,YAAI,QAAQ;AAAA,MACd;AACA,UAAI,gBAAgB;AAClB,YAAI,KAAK;AACT,YAAI,cAAc;AAClB,YAAI,UAAU,SAAS,GAAG,GAAGD,QAAOC,OAAM;AAC1C,YAAI,QAAQ;AAAA,MACd;AAAA,IACF;AACA;AACA,QAAI,CAAC,gBAAgB,gBAAgB;AACnC,cAAQ,GAAG,GAAG,OAAO,MAAM;AAAA,IAC7B,WAAW,aAAa,QAAQ;AAC9B,MAAK,KAAK,cAAc,SAAU,MAAM;AACtC,gBAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,SAAS,GAAG;AAAA,MACzE,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAOH;AACT,EAAE,gBAAQ;AACV,IAAO,gBAAQ;;;ACrRf,IAAI,qBAAqB;AACzB,IAAI,gBAAgB;AACpB,IAAI,2BAA2B;AAC/B,IAAI,kBAAkB;AACtB,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,MAAI,MAAM,aAAa;AACrB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,WAAW,cAAc,OAAO,MAAM,YAAY,YAAY;AAC7E,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,WAAW,OAAO,QAAQ;AACjC,MAAI,UAAU,SAAS,cAAc,KAAK;AAC1C,UAAQ,MAAM,UAAU,CAAC,qBAAqB,WAAW,QAAQ,MAAM,YAAY,SAAS,MAAM,aAAa,YAAY,gBAAgB,EAAE,KAAK,GAAG,IAAI;AACzJ,SAAO;AACT;AACA,IAAI,gBAAgB,WAAY;AAC9B,WAASI,eAAc,MAAM,SAAS,MAAM,IAAI;AAC9C,SAAK,OAAO;AACZ,SAAK,cAAc,CAAC;AACpB,SAAK,mBAAmB,CAAC;AACzB,SAAK,UAAU,CAAC;AAChB,SAAK,eAAe,CAAC;AACrB,SAAK,4BAA4B;AACjC,SAAK,OAAO;AACZ,QAAI,eAAe,CAAC,KAAK,YAAY,KAAK,SAAS,YAAY,MAAM;AACrE,SAAK,QAAQ,OAAY,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC9C,SAAK,MAAM,KAAK,oBAAoB;AACpC,SAAK,gBAAgB;AACrB,SAAK,OAAO;AACZ,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACb,MAAK,kBAAkB,IAAI;AAC3B,WAAK,YAAY;AAAA,IACnB;AACA,SAAK,UAAU;AACf,QAAI,aAAa,KAAK;AACtB,SAAK,mBAAmB,CAAC;AACzB,QAAI,SAAS,KAAK;AAClB,QAAI,CAAC,cAAc;AACjB,WAAK,SAAS,QAAQ,MAAM,GAAG,IAAI;AACnC,WAAK,UAAU,QAAQ,MAAM,GAAG,IAAI;AACpC,UAAI,UAAU,KAAK,WAAW,WAAW,KAAK,QAAQ,KAAK,OAAO;AAClE,WAAK,YAAY,OAAO;AAAA,IAC1B,OAAO;AACL,UAAI,aAAa;AACjB,UAAI,QAAQ,WAAW;AACvB,UAAI,SAAS,WAAW;AACxB,UAAI,KAAK,SAAS,MAAM;AACtB,gBAAQ,KAAK;AAAA,MACf;AACA,UAAI,KAAK,UAAU,MAAM;AACvB,iBAAS,KAAK;AAAA,MAChB;AACA,WAAK,MAAM,KAAK,oBAAoB;AACpC,iBAAW,QAAQ,QAAQ,KAAK;AAChC,iBAAW,SAAS,SAAS,KAAK;AAClC,WAAK,SAAS;AACd,WAAK,UAAU;AACf,UAAI,YAAY,IAAI,cAAM,YAAY,MAAM,KAAK,GAAG;AACpD,gBAAU,cAAc;AACxB,gBAAU,YAAY;AACtB,aAAO,aAAa,IAAI;AACxB,gBAAU,SAAS;AACnB,iBAAW,KAAK,aAAa;AAC7B,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACA,EAAAA,eAAc,UAAU,UAAU,WAAY;AAC5C,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,UAAU,iBAAiB,WAAY;AACnD,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,eAAc,UAAU,kBAAkB,WAAY;AACpD,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,eAAc,UAAU,wBAAwB,WAAY;AAC1D,QAAI,eAAe,KAAK,gBAAgB;AACxC,QAAI,cAAc;AAChB,aAAO;AAAA,QACL,YAAY,aAAa,cAAc;AAAA,QACvC,WAAW,aAAa,aAAa;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AACA,EAAAA,eAAc,UAAU,UAAU,SAAU,UAAU;AACpD,QAAI,OAAO,KAAK,QAAQ,eAAe,IAAI;AAC3C,QAAI,WAAW,KAAK;AACpB,QAAI,aAAa,KAAK;AACtB,SAAK,YAAY,KAAK,OAAO;AAC7B,SAAK,WAAW,MAAM,UAAU,UAAU,KAAK,SAAS;AACxD,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAI,IAAI,WAAW,CAAC;AACpB,UAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,UAAI,CAAC,MAAM,eAAe,MAAM,SAAS;AACvC,YAAI,aAAa,MAAM,IAAI,KAAK,mBAAmB;AACnD,cAAM,QAAQ,UAAU;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,KAAK,MAAM,cAAc;AAC3B,WAAK,mBAAmB,KAAK,MAAM;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,UAAU,eAAe,WAAY;AACjD,SAAK,gBAAgB,KAAK,QAAQ,eAAe,KAAK,CAAC;AAAA,EACzD;AACA,EAAAA,eAAc,UAAU,kBAAkB,SAAU,MAAM;AACxD,QAAI,MAAM,KAAK;AACf,QAAI,aAAa,KAAK;AACtB,kBAAc,WAAW,MAAM;AAC/B,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,QAAI,QAAQ;AAAA,MACV,SAAS;AAAA,MACT,WAAW,KAAK;AAAA,MAChB,YAAY,KAAK;AAAA,IACnB;AACA,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAI,KAAK,KAAK,CAAC;AACf,UAAI,GAAG,WAAW;AAChB,YAAI,CAAC,YAAY;AACf,uBAAa,KAAK,cAAc,KAAK,SAAS,kBAAkB;AAAA,QAClE;AACA,YAAI,CAAC,KAAK;AACR,gBAAM,WAAW;AACjB,cAAI,KAAK;AAAA,QACX;AACA,cAAM,KAAK,IAAI,OAAO,MAAM,MAAM,CAAC;AAAA,MACrC;AAAA,IACF;AACA,QAAI,KAAK;AACP,UAAI,QAAQ;AAAA,IACd;AAAA,EACF;AACA,EAAAA,eAAc,UAAU,gBAAgB,WAAY;AAClD,WAAO,KAAK,SAAS,kBAAkB;AAAA,EACzC;AACA,EAAAA,eAAc,UAAU,WAAW,SAAU,KAAK,IAAI;AACpD,gBAAY,KAAK,EAAE;AAAA,EACrB;AACA,EAAAA,eAAc,UAAU,aAAa,SAAU,MAAM,UAAU,UAAU,UAAU;AACjF,QAAI,KAAK,cAAc,UAAU;AAC/B;AAAA,IACF;AACA,eAAW,YAAY;AACvB,SAAK,mBAAmB,IAAI;AAC5B,QAAI,KAAK,KAAK,aAAa,MAAM,UAAU,QAAQ,GACjD,WAAW,GAAG,UACd,oBAAoB,GAAG;AACzB,QAAI,KAAK,2BAA2B;AAClC,WAAK,mBAAmB;AAAA,IAC1B;AACA,QAAI,mBAAmB;AACrB,WAAK,gBAAgB,IAAI;AAAA,IAC3B;AACA,QAAI,CAAC,UAAU;AACb,UAAI,SAAS;AACb,oCAAsB,WAAY;AAChC,eAAO,WAAW,MAAM,UAAU,UAAU,QAAQ;AAAA,MACtD,CAAC;AAAA,IACH,OAAO;AACL,WAAK,UAAU,SAAU,OAAO;AAC9B,cAAM,cAAc,MAAM,WAAW;AAAA,MACvC,CAAC;AAAA,IACH;AAAA,EACF;AACA,EAAAA,eAAc,UAAU,qBAAqB,WAAY;AACvD,QAAI,MAAM,KAAK,SAAS,aAAa,EAAE;AACvC,QAAI,QAAQ,KAAK,SAAS;AAC1B,QAAI,SAAS,KAAK,SAAS;AAC3B,QAAI,UAAU,GAAG,GAAG,OAAO,MAAM;AACjC,SAAK,iBAAiB,SAAU,OAAO;AACrC,UAAI,MAAM,SAAS;AACjB,YAAI,UAAU,MAAM,KAAK,GAAG,GAAG,OAAO,MAAM;AAAA,MAC9C;AAAA,IACF,CAAC;AAAA,EACH;AACA,EAAAA,eAAc,UAAU,eAAe,SAAU,MAAM,UAAU,UAAU;AACzE,QAAI,QAAQ;AACZ,QAAI,YAAY,CAAC;AACjB,QAAI,eAAe,KAAK,MAAM;AAC9B,aAAS,KAAK,GAAG,KAAK,KAAK,YAAY,QAAQ,MAAM;AACnD,UAAI,SAAS,KAAK,YAAY,EAAE;AAChC,UAAI,QAAQ,KAAK,QAAQ,MAAM;AAC/B,UAAI,MAAM,eAAe,UAAU,KAAK,gBAAgB,MAAM,WAAW,WAAW;AAClF,kBAAU,KAAK,KAAK;AAAA,MACtB;AAAA,IACF;AACA,QAAI,WAAW;AACf,QAAI,oBAAoB;AACxB,QAAI,UAAU,SAAUC,IAAG;AACzB,UAAIC,SAAQ,UAAUD,EAAC;AACvB,UAAI,MAAMC,OAAM;AAChB,UAAI,eAAe,gBAAgBA,OAAM,mBAAmB,MAAM,UAAU,OAAO,QAAQ,OAAO,OAAO;AACzG,UAAI,QAAQ,WAAWA,OAAM,eAAeA,OAAM;AAClD,UAAI,WAAW,CAAC,YAAYA,OAAM,eAAe,KAAK;AACtD,UAAI,YAAY,YAAY,KAAK,IAAI;AACrC,UAAI,aAAaA,OAAM,WAAW,OAAO,YAAY,CAAC,IAAI,OAAO,mBAAmB;AACpF,UAAIA,OAAM,iBAAiBA,OAAM,YAAY;AAC3C,QAAAA,OAAM,MAAM,OAAO,YAAY,YAAY;AAAA,MAC7C,WAAW,UAAUA,OAAM,cAAc;AACvC,YAAI,UAAU,KAAK,KAAK;AACxB,YAAI,CAAC,QAAQ,eAAe,CAAC,QAAQ,YAAY,UAAU;AACzD,UAAAA,OAAM,MAAM,OAAO,YAAY,YAAY;AAAA,QAC7C;AAAA,MACF;AACA,UAAI,UAAU,IAAI;AAChB,gBAAQ,MAAM,0CAA0C;AACxD,gBAAQA,OAAM;AAAA,MAChB;AACA,UAAI;AACJ,UAAI,UAAU,SAAU,aAAa;AACnC,YAAI,QAAQ;AAAA,UACV,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,WAAW,MAAM;AAAA,UACjB,YAAY,MAAM;AAAA,QACpB;AACA,aAAK,IAAI,OAAO,IAAIA,OAAM,YAAY,KAAK;AACzC,cAAI,KAAK,KAAK,CAAC;AACf,cAAI,GAAG,WAAW;AAChB,gCAAoB;AAAA,UACtB;AACA,gBAAM,WAAW,IAAIA,QAAO,cAAc,aAAa,OAAO,MAAMA,OAAM,aAAa,CAAC;AACxF,cAAI,UAAU;AACZ,gBAAI,QAAQ,KAAK,IAAI,IAAI;AACzB,gBAAI,QAAQ,IAAI;AACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM,iBAAiB;AACzB,cAAI,QAAQ;AAAA,QACd;AAAA,MACF;AACA,UAAI,cAAc;AAChB,YAAI,aAAa,WAAW,GAAG;AAC7B,cAAIA,OAAM;AAAA,QACZ,OAAO;AACL,cAAI,MAAM,OAAO;AACjB,mBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAC5C,gBAAI,OAAO,aAAa,CAAC;AACzB,gBAAI,KAAK;AACT,gBAAI,UAAU;AACd,gBAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,SAAS,GAAG;AACxE,gBAAI,KAAK;AACT,oBAAQ,IAAI;AACZ,gBAAI,QAAQ;AAAA,UACd;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,KAAK;AACT,gBAAQ;AACR,YAAI,QAAQ;AAAA,MACd;AACA,MAAAA,OAAM,cAAc;AACpB,UAAIA,OAAM,cAAcA,OAAM,YAAY;AACxC,mBAAW;AAAA,MACb;AAAA,IACF;AACA,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAQ,CAAC;AAAA,IACX;AACA,QAAI,YAAI,KAAK;AACX,MAAK,KAAK,KAAK,SAAS,SAAUA,QAAO;AACvC,YAAIA,UAASA,OAAM,OAAOA,OAAM,IAAI,MAAM;AACxC,UAAAA,OAAM,IAAI,KAAK;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,EAAAF,eAAc,UAAU,aAAa,SAAU,IAAI,cAAc,cAAc,aAAa,OAAO,QAAQ;AACzG,QAAI,MAAM,aAAa;AACvB,QAAI,cAAc;AAChB,UAAI,YAAY,GAAG,aAAa;AAChC,UAAI,CAAC,eAAe,aAAa,UAAU,UAAU,WAAW,GAAG;AACjE,cAAM,KAAK,IAAI,OAAO,MAAM;AAC5B,WAAG,iBAAiB,SAAS;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,YAAM,KAAK,IAAI,OAAO,MAAM;AAAA,IAC9B;AAAA,EACF;AACA,EAAAA,eAAc,UAAU,WAAW,SAAU,QAAQ,SAAS;AAC5D,QAAI,KAAK,iBAAiB,CAAC,KAAK,2BAA2B;AACzD,eAAS;AAAA,IACX;AACA,QAAI,QAAQ,KAAK,QAAQ,MAAM;AAC/B,QAAI,CAAC,OAAO;AACV,cAAQ,IAAI,cAAM,QAAQ,QAAQ,MAAM,KAAK,GAAG;AAChD,YAAM,SAAS;AACf,YAAM,cAAc;AACpB,UAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,QAAK,MAAM,OAAO,KAAK,aAAa,MAAM,GAAG,IAAI;AAAA,MACnD,WAAW,KAAK,aAAa,SAAS,wBAAwB,GAAG;AAC/D,QAAK,MAAM,OAAO,KAAK,aAAa,SAAS,wBAAwB,GAAG,IAAI;AAAA,MAC9E;AACA,UAAI,SAAS;AACX,cAAM,UAAU;AAAA,MAClB;AACA,WAAK,YAAY,QAAQ,KAAK;AAC9B,YAAM,YAAY;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,UAAU,cAAc,SAAU,QAAQ,OAAO;AAC7D,QAAI,YAAY,KAAK;AACrB,QAAI,aAAa,KAAK;AACtB,QAAI,MAAM,WAAW;AACrB,QAAI,UAAU,KAAK;AACnB,QAAI,YAAY;AAChB,QAAI,IAAI;AACR,QAAI,UAAU,MAAM,GAAG;AACrB,UAAI,MAAuC;AACzC,QAAK,SAAS,YAAY,SAAS,wBAAwB;AAAA,MAC7D;AACA;AAAA,IACF;AACA,QAAI,CAAC,aAAa,KAAK,GAAG;AACxB,UAAI,MAAuC;AACzC,QAAK,SAAS,qBAAqB,SAAS,eAAe;AAAA,MAC7D;AACA;AAAA,IACF;AACA,QAAI,MAAM,KAAK,SAAS,WAAW,CAAC,GAAG;AACrC,WAAK,IAAI,GAAG,IAAI,MAAM,GAAG,KAAK;AAC5B,YAAI,WAAW,CAAC,IAAI,UAAU,WAAW,IAAI,CAAC,IAAI,QAAQ;AACxD;AAAA,QACF;AAAA,MACF;AACA,kBAAY,UAAU,WAAW,CAAC,CAAC;AAAA,IACrC;AACA,eAAW,OAAO,IAAI,GAAG,GAAG,MAAM;AAClC,cAAU,MAAM,IAAI;AACpB,QAAI,CAAC,MAAM,SAAS;AAClB,UAAI,WAAW;AACb,YAAI,UAAU,UAAU;AACxB,YAAI,QAAQ,aAAa;AACvB,kBAAQ,aAAa,MAAM,KAAK,QAAQ,WAAW;AAAA,QACrD,OAAO;AACL,kBAAQ,YAAY,MAAM,GAAG;AAAA,QAC/B;AAAA,MACF,OAAO;AACL,YAAI,QAAQ,YAAY;AACtB,kBAAQ,aAAa,MAAM,KAAK,QAAQ,UAAU;AAAA,QACpD,OAAO;AACL,kBAAQ,YAAY,MAAM,GAAG;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AACA,UAAM,YAAY;AAAA,EACpB;AACA,EAAAA,eAAc,UAAU,YAAY,SAAU,IAAI,SAAS;AACzD,QAAI,aAAa,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAI,IAAI,WAAW,CAAC;AACpB,SAAG,KAAK,SAAS,KAAK,QAAQ,CAAC,GAAG,CAAC;AAAA,IACrC;AAAA,EACF;AACA,EAAAA,eAAc,UAAU,mBAAmB,SAAU,IAAI,SAAS;AAChE,QAAI,aAAa,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAI,IAAI,WAAW,CAAC;AACpB,UAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,UAAI,MAAM,aAAa;AACrB,WAAG,KAAK,SAAS,OAAO,CAAC;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,EAAAA,eAAc,UAAU,iBAAiB,SAAU,IAAI,SAAS;AAC9D,QAAI,aAAa,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAI,IAAI,WAAW,CAAC;AACpB,UAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,UAAI,CAAC,MAAM,aAAa;AACtB,WAAG,KAAK,SAAS,OAAO,CAAC;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,EAAAA,eAAc,UAAU,YAAY,WAAY;AAC9C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,eAAc,UAAU,qBAAqB,SAAU,MAAM;AAC3D,SAAK,iBAAiB,SAAUE,QAAO,GAAG;AACxC,MAAAA,OAAM,UAAUA,OAAM,SAAS;AAAA,IACjC,CAAC;AACD,aAAS,gBAAgB,KAAK;AAC5B,UAAI,WAAW;AACb,YAAI,UAAU,eAAe,KAAK;AAChC,oBAAU,UAAU;AAAA,QACtB;AACA,kBAAU,aAAa;AAAA,MACzB;AAAA,IACF;AACA,QAAI,KAAK,eAAe;AACtB,eAAS,MAAM,GAAG,MAAM,KAAK,QAAQ,OAAO;AAC1C,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,GAAG,WAAW,KAAK,MAAM,CAAC,EAAE,UAAU,GAAG,aAAa;AACxD,eAAK,4BAA4B;AACjC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,YAAY;AAChB,QAAI,wBAAwB;AAC5B,QAAI;AACJ,QAAI;AACJ,SAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,UAAI,KAAK,KAAK,CAAC;AACf,UAAI,SAAS,GAAG;AAChB,UAAI,QAAQ;AACZ,UAAI,eAAe,QAAQ;AACzB,qBAAa;AACb,gCAAwB;AAAA,MAC1B;AACA,UAAI,GAAG,aAAa;AAClB,gBAAQ,KAAK,SAAS,SAAS,iBAAiB,KAAK,yBAAyB;AAC9E,cAAM,cAAc;AACpB,gCAAwB;AAAA,MAC1B,OAAO;AACL,gBAAQ,KAAK,SAAS,UAAU,wBAAwB,IAAI,2BAA2B,IAAI,KAAK,yBAAyB;AAAA,MAC3H;AACA,UAAI,CAAC,MAAM,aAAa;AACtB,QAAK,SAAS,YAAY,SAAS,oCAAoC,MAAM,EAAE;AAAA,MACjF;AACA,UAAI,UAAU,WAAW;AACvB,cAAM,SAAS;AACf,YAAI,MAAM,iBAAiB,GAAG;AAC5B,gBAAM,UAAU;AAAA,QAClB;AACA,cAAM,eAAe;AACrB,YAAI,CAAC,MAAM,aAAa;AACtB,gBAAM,cAAc;AAAA,QACtB,OAAO;AACL,gBAAM,cAAc;AAAA,QACtB;AACA,wBAAgB,CAAC;AACjB,oBAAY;AAAA,MACd;AACA,UAAI,GAAG,UAAU,cAAc,CAAC,GAAG,WAAW;AAC5C,cAAM,UAAU;AAChB,YAAI,MAAM,eAAe,MAAM,cAAc,GAAG;AAC9C,gBAAM,cAAc;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AACA,oBAAgB,CAAC;AACjB,SAAK,iBAAiB,SAAUA,QAAO,GAAG;AACxC,UAAI,CAACA,OAAM,UAAUA,OAAM,gBAAgB,IAAI,GAAG;AAChD,QAAAA,OAAM,UAAU;AAChB,QAAAA,OAAM,eAAeA,OAAM,aAAaA,OAAM,cAAc;AAAA,MAC9D;AACA,UAAIA,OAAM,WAAWA,OAAM,cAAc,GAAG;AAC1C,QAAAA,OAAM,cAAcA,OAAM;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AACA,EAAAF,eAAc,UAAU,QAAQ,WAAY;AAC1C,SAAK,iBAAiB,KAAK,WAAW;AACtC,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,UAAU,cAAc,SAAU,OAAO;AACrD,UAAM,MAAM;AAAA,EACd;AACA,EAAAA,eAAc,UAAU,qBAAqB,SAAU,iBAAiB;AACtE,SAAK,mBAAmB;AACxB,IAAK,KAAK,KAAK,SAAS,SAAU,OAAO;AACvC,YAAM,aAAa;AAAA,IACrB,CAAC;AAAA,EACH;AACA,EAAAA,eAAc,UAAU,cAAc,SAAU,QAAQ,QAAQ;AAC9D,QAAI,QAAQ;AACV,UAAI,cAAc,KAAK;AACvB,UAAI,CAAC,YAAY,MAAM,GAAG;AACxB,oBAAY,MAAM,IAAI;AAAA,MACxB,OAAO;AACL,QAAK,MAAM,YAAY,MAAM,GAAG,QAAQ,IAAI;AAAA,MAC9C;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,YAAI,UAAU,KAAK,YAAY,CAAC;AAChC,YAAI,YAAY,UAAU,YAAY,SAAS,0BAA0B;AACvE,cAAI,QAAQ,KAAK,QAAQ,OAAO;AAChC,UAAK,MAAM,OAAO,YAAY,MAAM,GAAG,IAAI;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,EAAAA,eAAc,UAAU,WAAW,SAAU,QAAQ;AACnD,QAAI,SAAS,KAAK;AAClB,QAAI,aAAa,KAAK;AACtB,QAAI,QAAQ,OAAO,MAAM;AACzB,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,UAAM,IAAI,WAAW,YAAY,MAAM,GAAG;AAC1C,WAAO,OAAO,MAAM;AACpB,eAAW,OAAY,QAAQ,YAAY,MAAM,GAAG,CAAC;AAAA,EACvD;AACA,EAAAA,eAAc,UAAU,SAAS,SAAU,OAAO,QAAQ;AACxD,QAAI,CAAC,KAAK,SAAS,OAAO;AACxB,UAAI,SAAS,QAAQ,UAAU,MAAM;AACnC;AAAA,MACF;AACA,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,SAAS,aAAa,EAAE,OAAO,OAAO,MAAM;AAAA,IACnD,OAAO;AACL,UAAI,UAAU,KAAK;AACnB,cAAQ,MAAM,UAAU;AACxB,UAAI,OAAO,KAAK;AAChB,UAAI,OAAO,KAAK;AAChB,eAAS,SAAS,KAAK,QAAQ;AAC/B,gBAAU,SAAS,KAAK,SAAS;AACjC,cAAQ,QAAQ,MAAM,GAAG,IAAI;AAC7B,eAAS,QAAQ,MAAM,GAAG,IAAI;AAC9B,cAAQ,MAAM,UAAU;AACxB,UAAI,KAAK,WAAW,SAAS,WAAW,KAAK,SAAS;AACpD,gBAAQ,MAAM,QAAQ,QAAQ;AAC9B,gBAAQ,MAAM,SAAS,SAAS;AAChC,iBAAS,MAAM,KAAK,SAAS;AAC3B,cAAI,KAAK,QAAQ,eAAe,EAAE,GAAG;AACnC,iBAAK,QAAQ,EAAE,EAAE,OAAO,OAAO,MAAM;AAAA,UACvC;AAAA,QACF;AACA,aAAK,QAAQ,IAAI;AAAA,MACnB;AACA,WAAK,SAAS;AACd,WAAK,UAAU;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,UAAU,aAAa,SAAU,QAAQ;AACrD,QAAI,QAAQ,KAAK,QAAQ,MAAM;AAC/B,QAAI,OAAO;AACT,YAAM,MAAM;AAAA,IACd;AAAA,EACF;AACA,EAAAA,eAAc,UAAU,UAAU,WAAY;AAC5C,SAAK,KAAK,YAAY;AACtB,SAAK,OAAO,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU;AAAA,EAC5D;AACA,EAAAA,eAAc,UAAU,oBAAoB,SAAU,MAAM;AAC1D,WAAO,QAAQ,CAAC;AAChB,QAAI,KAAK,iBAAiB,CAAC,KAAK,oBAAoB;AAClD,aAAO,KAAK,QAAQ,aAAa,EAAE;AAAA,IACrC;AACA,QAAI,aAAa,IAAI,cAAM,SAAS,MAAM,KAAK,cAAc,KAAK,GAAG;AACrE,eAAW,YAAY;AACvB,eAAW,MAAM,OAAO,KAAK,mBAAmB,KAAK,gBAAgB;AACrE,QAAI,MAAM,WAAW;AACrB,QAAI,KAAK,cAAc,KAAK,KAAK;AAC/B,WAAK,QAAQ;AACb,UAAI,UAAU,WAAW,IAAI;AAC7B,UAAI,WAAW,WAAW,IAAI;AAC9B,WAAK,UAAU,SAAU,OAAO;AAC9B,YAAI,MAAM,aAAa;AACrB,cAAI,UAAU,MAAM,KAAK,GAAG,GAAG,SAAS,QAAQ;AAAA,QAClD,WAAW,MAAM,gBAAgB;AAC/B,cAAI,KAAK;AACT,gBAAM,eAAe,GAAG;AACxB,cAAI,QAAQ;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,UAAI,QAAQ;AAAA,QACV,SAAS;AAAA,QACT,WAAW,KAAK;AAAA,QAChB,YAAY,KAAK;AAAA,MACnB;AACA,UAAI,cAAc,KAAK,QAAQ,eAAe,IAAI;AAClD,eAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,YAAI,KAAK,YAAY,CAAC;AACtB,cAAM,KAAK,IAAI,OAAO,MAAM,MAAM,CAAC;AAAA,MACrC;AAAA,IACF;AACA,WAAO,WAAW;AAAA,EACpB;AACA,EAAAA,eAAc,UAAU,WAAW,WAAY;AAC7C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,eAAc,UAAU,YAAY,WAAY;AAC9C,WAAO,KAAK;AAAA,EACd;AACA,SAAOA;AACT,EAAE;AACF,IAAOG,mBAAQ;;;ACtjBR,SAASC,SAAQ,WAAW;AACjC,YAAU,gBAAgB,UAAUC,gBAAa;AACnD;", "names": ["SVGPathRebuilder", "el", "animators", "len", "animator", "i", "percent", "brush", "parentNode", "map", "SVGPainter", "brush", "Layer", "i", "width", "height", "CanvasPainter", "k", "layer", "Painter_default", "install", "Painter_default"]}