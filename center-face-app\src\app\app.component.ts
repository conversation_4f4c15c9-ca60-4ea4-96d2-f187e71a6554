import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { ServerTimeService } from './core/service/server-time.service';
import { ModalService } from './core/service/modal.service';
import { PwaInstallButtonComponent } from './shared/components/pwa-install-button/pwa-install-button.component';
import { AuthService } from './core/service/auth.service';
import { PwaInstallService } from './core/service/pwa-install.service';

@Component({
    selector: 'app-root',
    standalone: true,
    imports: [CommonModule, RouterOutlet, PwaInstallButtonComponent],
    template: `
      <router-outlet></router-outlet>
      <app-pwa-install-button></app-pwa-install-button>
    `,
    styles: ``
})
export class AppComponent implements OnInit {

  constructor(
    private serverTimeService: ServerTimeService,
    private modalService: ModalService,
    private router: Router,
    private authService: AuthService,
    private pwaService: PwaInstallService
  ) {}

  ngOnInit(): void {
    if (this.pwaService.isInStandaloneMode && this.authService.isLogin && this.authService.serverCode) {
      this.router.navigate(['/dashboard', this.authService.serverCode], { replaceUrl: true });
    }

    this.checkBrowser();
    this.serverTimeService.getServerTime().subscribe();
    
    // 检查PWA环境
    this.checkPwaEnvironment();

  }

  checkBrowser(): void {
    const isSupportCrypto = window.crypto?.subtle;
    if (!isSupportCrypto) {
      this.modalService.error("错误", '当前浏览器版本过低，无法连接服务器！');
    }
  }
  
  // 调试PWA环境
  private checkPwaEnvironment(): void {
    console.log('PWA环境检查开始');
    
    // 检查Service Worker支持
    if ('serviceWorker' in navigator) {
      console.log('浏览器支持Service Worker');
      
      // 检查是否在HTTPS环境
      if (window.location.protocol === 'https:' || window.location.hostname === 'localhost') {
        console.log('网站通过HTTPS提供或在本地运行，满足PWA要求');
      } else {
        console.warn('网站不是通过HTTPS提供的，PWA功能可能受限');
      }
      
      // 检查是否注册了Service Worker
      navigator.serviceWorker.getRegistrations().then(registrations => {
        console.log(`当前有 ${registrations.length} 个Service Worker注册`);
      });
    } else {
      console.warn('浏览器不支持Service Worker，无法使用PWA功能');
    }
    
    // 检查是否为独立模式
    if (window.matchMedia('(display-mode: standalone)').matches) {
      console.log('应用已经在独立模式下运行 (PWA已安装)');
    }
    
    // 监听beforeinstallprompt全局事件用于调试
    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('捕获到全局beforeinstallprompt事件', e);
    });
  }
}
