/* You can add global styles to this file, and also import other style files */
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  // 左右方向上再给它24px的余量，上下方向上再给它50px的余量以用来放工具栏
  min-height: 718px;
  min-width: 1000px;
  font-family: "Microsoft Yahei", "微软雅黑", verdana, Helvetica, Arial,
    sans-serif;

  // 不能选中文字
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.mark {
  display: inline-block;
  width: 3px;
  height: 16px;
  background-color: #007aff;
  border-radius: 2px;
  margin-right: 8px;
}

.bg-green {
  background: #52c41a;
}
.bg-red {
  background: #fd6464;
}
.dot {
  content: "";
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

/* 定义滚动条的宽度和轨道颜色 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #f5f5f5;
}

/* 定义滚动条的样式 */
::-webkit-scrollbar-thumb {
  background-color: #c6e1fd;
  border-radius: 10px;
}

/* 定义滚动条在悬停状态下的样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: #7db6f4;
}
