import webpack from 'webpack';
import path from 'path';
import nodeExternals from 'webpack-node-externals';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';

const mode =
  (process.env.NODE_ENV as 'development' | 'production') || 'development';

const is_dev = mode === 'development';

export const config: webpack.Configuration = {
  context: path.join(__dirname),
  entry: './app.ts',
  output: {
    path: path.join(__dirname, '..', 'build', 'server'),
    libraryTarget: 'commonjs',
    filename: 'server.js',
  },
  node: {
    __dirname: false,
    __filename: false,
  },
  resolve: {
    extensions: ['.ts', '.js', '.json'],
    plugins: [
      new TsconfigPathsPlugin({
        configFile: path.join(__dirname, 'tsconfig.json'),
      }),
    ],
  },
  module: {
    noParse: /native-require.ts$/,
    rules: [
      {
        test: /\.ts$/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              compilerOptions: {
                sourceMap: is_dev,
              },
            },
          },
        ],
        exclude: /node_modules/,
      },
    ],
  },
  mode,
  devtool: is_dev ? 'source-map' : false,
  externalsPresets: { node: true },
  externals: [nodeExternals()],
};
