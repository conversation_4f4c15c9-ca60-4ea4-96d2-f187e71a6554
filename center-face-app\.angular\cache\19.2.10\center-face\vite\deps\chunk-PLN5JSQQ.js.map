{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-outlet.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Input, Directive, NgModule } from '@angular/core';\nimport { isTemplateRef } from 'ng-zorro-antd/core/util';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzStringTemplateOutletDirective {\n  viewContainer;\n  templateRef;\n  embeddedViewRef = null;\n  context = new NzStringTemplateOutletContext();\n  nzStringTemplateOutletContext = null;\n  nzStringTemplateOutlet = null;\n  static ngTemplateContextGuard(_dir, _ctx) {\n    return true;\n  }\n  recreateView() {\n    this.viewContainer.clear();\n    if (isTemplateRef(this.nzStringTemplateOutlet)) {\n      this.embeddedViewRef = this.viewContainer.createEmbeddedView(this.nzStringTemplateOutlet, this.nzStringTemplateOutletContext);\n    } else {\n      this.embeddedViewRef = this.viewContainer.createEmbeddedView(this.templateRef, this.context);\n    }\n  }\n  updateContext() {\n    const newCtx = isTemplateRef(this.nzStringTemplateOutlet) ? this.nzStringTemplateOutletContext : this.context;\n    const oldCtx = this.embeddedViewRef.context;\n    if (newCtx) {\n      for (const propName of Object.keys(newCtx)) {\n        oldCtx[propName] = newCtx[propName];\n      }\n    }\n  }\n  constructor(viewContainer, templateRef) {\n    this.viewContainer = viewContainer;\n    this.templateRef = templateRef;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzStringTemplateOutletContext,\n      nzStringTemplateOutlet\n    } = changes;\n    const shouldRecreateView = () => {\n      let shouldOutletRecreate = false;\n      if (nzStringTemplateOutlet) {\n        shouldOutletRecreate = nzStringTemplateOutlet.firstChange || isTemplateRef(nzStringTemplateOutlet.previousValue) || isTemplateRef(nzStringTemplateOutlet.currentValue);\n      }\n      const hasContextShapeChanged = ctxChange => {\n        const prevCtxKeys = Object.keys(ctxChange.previousValue || {});\n        const currCtxKeys = Object.keys(ctxChange.currentValue || {});\n        if (prevCtxKeys.length === currCtxKeys.length) {\n          for (const propName of currCtxKeys) {\n            if (prevCtxKeys.indexOf(propName) === -1) {\n              return true;\n            }\n          }\n          return false;\n        } else {\n          return true;\n        }\n      };\n      const shouldContextRecreate = nzStringTemplateOutletContext && hasContextShapeChanged(nzStringTemplateOutletContext);\n      return shouldContextRecreate || shouldOutletRecreate;\n    };\n    if (nzStringTemplateOutlet) {\n      this.context.$implicit = nzStringTemplateOutlet.currentValue;\n    }\n    const recreateView = shouldRecreateView();\n    if (recreateView) {\n      /** recreate view when context shape or outlet change **/\n      this.recreateView();\n    } else {\n      /** update context **/\n      this.updateContext();\n    }\n  }\n  static ɵfac = function NzStringTemplateOutletDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzStringTemplateOutletDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzStringTemplateOutletDirective,\n    selectors: [[\"\", \"nzStringTemplateOutlet\", \"\"]],\n    inputs: {\n      nzStringTemplateOutletContext: \"nzStringTemplateOutletContext\",\n      nzStringTemplateOutlet: \"nzStringTemplateOutlet\"\n    },\n    exportAs: [\"nzStringTemplateOutlet\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzStringTemplateOutletDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzStringTemplateOutlet]',\n      exportAs: 'nzStringTemplateOutlet'\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }], {\n    nzStringTemplateOutletContext: [{\n      type: Input\n    }],\n    nzStringTemplateOutlet: [{\n      type: Input\n    }]\n  });\n})();\nclass NzStringTemplateOutletContext {\n  $implicit;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzOutletModule {\n  static ɵfac = function NzOutletModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzOutletModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzOutletModule,\n    imports: [NzStringTemplateOutletDirective],\n    exports: [NzStringTemplateOutletDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzOutletModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzStringTemplateOutletDirective],\n      exports: [NzStringTemplateOutletDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzOutletModule, NzStringTemplateOutletDirective };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,mCAAN,MAAM,iCAAgC;AAAA,EACpC;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,UAAU,IAAI,8BAA8B;AAAA,EAC5C,gCAAgC;AAAA,EAChC,yBAAyB;AAAA,EACzB,OAAO,uBAAuB,MAAM,MAAM;AACxC,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,SAAK,cAAc,MAAM;AACzB,QAAI,cAAc,KAAK,sBAAsB,GAAG;AAC9C,WAAK,kBAAkB,KAAK,cAAc,mBAAmB,KAAK,wBAAwB,KAAK,6BAA6B;AAAA,IAC9H,OAAO;AACL,WAAK,kBAAkB,KAAK,cAAc,mBAAmB,KAAK,aAAa,KAAK,OAAO;AAAA,IAC7F;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,UAAM,SAAS,cAAc,KAAK,sBAAsB,IAAI,KAAK,gCAAgC,KAAK;AACtG,UAAM,SAAS,KAAK,gBAAgB;AACpC,QAAI,QAAQ;AACV,iBAAW,YAAY,OAAO,KAAK,MAAM,GAAG;AAC1C,eAAO,QAAQ,IAAI,OAAO,QAAQ;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,eAAe,aAAa;AACtC,SAAK,gBAAgB;AACrB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,qBAAqB,MAAM;AAC/B,UAAI,uBAAuB;AAC3B,UAAI,wBAAwB;AAC1B,+BAAuB,uBAAuB,eAAe,cAAc,uBAAuB,aAAa,KAAK,cAAc,uBAAuB,YAAY;AAAA,MACvK;AACA,YAAM,yBAAyB,eAAa;AAC1C,cAAM,cAAc,OAAO,KAAK,UAAU,iBAAiB,CAAC,CAAC;AAC7D,cAAM,cAAc,OAAO,KAAK,UAAU,gBAAgB,CAAC,CAAC;AAC5D,YAAI,YAAY,WAAW,YAAY,QAAQ;AAC7C,qBAAW,YAAY,aAAa;AAClC,gBAAI,YAAY,QAAQ,QAAQ,MAAM,IAAI;AACxC,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,wBAAwB,iCAAiC,uBAAuB,6BAA6B;AACnH,aAAO,yBAAyB;AAAA,IAClC;AACA,QAAI,wBAAwB;AAC1B,WAAK,QAAQ,YAAY,uBAAuB;AAAA,IAClD;AACA,UAAM,eAAe,mBAAmB;AACxC,QAAI,cAAc;AAEhB,WAAK,aAAa;AAAA,IACpB,OAAO;AAEL,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAcF;AAbE,cAtEI,kCAsEG,QAAO,SAAS,wCAAwC,mBAAmB;AAChF,SAAO,KAAK,qBAAqB,kCAAoC,kBAAqB,gBAAgB,GAAM,kBAAqB,WAAW,CAAC;AACnJ;AACA,cAzEI,kCAyEG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,0BAA0B,EAAE,CAAC;AAAA,EAC9C,QAAQ;AAAA,IACN,+BAA+B;AAAA,IAC/B,wBAAwB;AAAA,EAC1B;AAAA,EACA,UAAU,CAAC,wBAAwB;AAAA,EACnC,UAAU,CAAI,oBAAoB;AACpC,CAAC;AAlFH,IAAM,kCAAN;AAAA,CAoFC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,+BAA+B,CAAC;AAAA,MAC9B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gCAAN,MAAoC;AAAA,EAClC;AACF;AAMA,IAAM,kBAAN,MAAM,gBAAe;AAUrB;AATE,cADI,iBACG,QAAO,SAAS,uBAAuB,mBAAmB;AAC/D,SAAO,KAAK,qBAAqB,iBAAgB;AACnD;AACA,cAJI,iBAIG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AAAA,EACN,SAAS,CAAC,+BAA+B;AAAA,EACzC,SAAS,CAAC,+BAA+B;AAC3C,CAAC;AACD,cATI,iBASG,QAAyB,iBAAiB,CAAC,CAAC;AATrD,IAAM,iBAAN;AAAA,CAWC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,+BAA+B;AAAA,MACzC,SAAS,CAAC,+BAA+B;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}