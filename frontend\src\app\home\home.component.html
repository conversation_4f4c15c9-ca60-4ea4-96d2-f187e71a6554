<div class="container">
    <div class="nav">
        <div class="title">{{project.name}}</div>
        <div class="center-name">考点：{{center_name}}</div>
    </div>
    <div class="date">考试日期：{{project.start | customDate:"yyyyMMdd"}} - {{project.end | customDate: "yyyyMMdd"}}</div>
    <div class="stat-container">
        <div class="room-stat stat" routerLink="../room">
            <div class="sub-title">
                <img src="assets/images/room-manage.svg" alt="">
                <span>考场管理</span>
            </div>
            <div class="explain">
                管理当前项目考场管理机，考前提前完成考场管理机与考点服务器的连接
            </div>
            <div class="stat-box">
                <div class="stat-item">
                    <div class="stat-data">{{rooms.toatl}}
                    </div>
                    <div class="stat-name">考场总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-data">{{rooms.toatl}}</div>
                    <div class="stat-name">已连接考场</div>
                </div>
                <div class="stat-item">
                    <div class="stat-data">{{rooms.online}}</div>
                    <div class="stat-name">在线考场</div>
                </div>
            </div>
        </div>
        <div class="exam-stat stat" routerLink="../schedule">
            <div class="sub-title ">
                <img src="assets/images/exam-manage.svg" alt="">
                <span>考试管理</span>
            </div>
            <div class="explain">
                试考卷及密码上传、正式卷及密码上传、正式考试当天管理机所有考场的相关数据
            </div>
            <div class="stat-box">
                <div class="stat-item">
                    <div class="stat-data">{{schedules.length}}</div>
                    <div class="stat-name">日程总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-data">{{subject_total}}</div>
                    <div class="stat-name">科目总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-data">{{entry_total}}</div>
                    <div class="stat-name">考生总数</div>
                </div>

            </div>
        </div>
    </div>
    <div class="exam-list">
        <div class="sub-title exam-nav">
            <div>
                <img src="assets/images/recent-exam.svg" alt="">
                <span>近期考试</span>
            </div>
            <a nz-button nzType="link" routerLink="../schedule">
                全部日程
                <span nz-icon nzType="right-square" nzTheme="fill"></span>
            </a>
        </div>
        <nz-list>
            @for (schedule of schedules; track $index) {
            <nz-list-item>
                <div class="list-item">
                    <div class="content">
                        @if (schedule.tag === "today") {
                        <span class="tag color-grey ">今日</span>
                        } @else if(schedule.tag === "tomorrow") {
                        <span class="tag color-yellow ">明日</span>
                        } @else {
                        <span class="tag color-blue ">近期</span>
                        }
                        <span>
                            {{schedule.start | customDate: 'MMdd'}} {{schedule.time_range}}
                        </span>
                        <span class="subject">{{schedule.subject_name.join(',')}}</span>
                    </div>

                    <div class="room-status">
                        <div class="total-room">考场总数：{{schedule.sessions.length}}</div>
                        <div class="online-room">在线：{{schedule.online_rooms}}</div>
                    </div>
                </div>
            </nz-list-item>
            }
        </nz-list>
    </div>
</div>