import { dataAccess } from '@data/data-access';
import { LogService } from './log.service';
import { ScheduleService } from './schedule.service';
import { CenterService } from './center.service';
import { data_path, getUserConfig, setUserConfig } from '@server/config/env';
import { RoomService } from './room/room.service';
import { AuthEventService } from './event/event.service';
import { FormService } from './form.service';
import { SystemSettingService } from './systemSetting.service';

export const logService = new LogService(dataAccess);
export const scheduleService = new ScheduleService(dataAccess);
export const roomService = new RoomService(dataAccess);
export const centerService = new CenterService(dataAccess, roomService, {
  getUserConfig,
  setUserConfig,
});
export const authEventService = new AuthEventService();
export const formService = new FormService(dataAccess);
export const systemSettingService = new SystemSettingService({ data_path });
