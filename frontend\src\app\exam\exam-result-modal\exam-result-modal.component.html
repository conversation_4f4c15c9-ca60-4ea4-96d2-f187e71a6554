<div class="body">
  <p class="explain">请核对考场数量及数据状态准确无误后进行导出</p>
  <nz-table #basicTable [nzData]="resultList"
  [nzShowPagination]="false"
  [nzFrontPagination]="false"
  nzSize="small"
  [nzScroll]="{ y: '300px' }"
  >
    <thead>
      <tr>
        <th nzWidth="50px">序号</th>
        <th>考场</th>
        <th>状态</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td>{{i + 1}}</td>
        <td>{{data.name}}</td>
        <td>
          @if (data.file.exist) {
            @if (data.file.integrity) {
              <!-- <span nz-icon nzType="check" nzTheme="outline" class="c-green"></span> -->
              <span nz-icon nzType="check-circle" nzTheme="outline" class="c-green icon-status"></span>
            } @else {
              <!-- <span nz-icon nzType="exclamation" nzTheme="outline" class="c-yellow"></span> -->
              <span nz-popover  nzPopoverContent="文件与预期不一致" nzPopoverPlacement="right" nz-icon nzType="exclamation-circle" nzTheme="outline" class="c-yellow icon-status"></span>
            }
          } @else {
            <!-- <span nz-icon nzType="close" nzTheme="outline" class="c-red"></span> -->
            <span nz-popover  nzPopoverContent="文件不存在" nzPopoverPlacement="right" nz-icon nzType="close-circle" nzTheme="outline" class="c-red icon-status"></span>
          }
        </td>
      </tr>
    </tbody>
  </nz-table>
</div>
<div class="footer modal-footer-btn_right">
  <button nz-button nzType="default" (click)="close()">取消</button>
  <button
    nz-button
    nzType="primary"
    (click)="exportResult()"
  >
    导出
  </button>
</div>