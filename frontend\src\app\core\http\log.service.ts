import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ElogType, Res } from '@share-types/center.types';

@Injectable({
  providedIn: 'root',
})
export class LogHttpService {
  constructor(private http: HttpClient) {}

  createLog(type: number, content: string) {
    return this.http.post<Res<any>>('/data/logs/create', {
      type,
      content,
    });
  }

  getLogs() {
    return this.http.get<Res<IHttpLog[]>>(`/data/logs`);
  }
}

export interface IHttpLog {
  id: string;
  name: string;
  type: ElogType;
  content: string;
  created_at: number;
}
