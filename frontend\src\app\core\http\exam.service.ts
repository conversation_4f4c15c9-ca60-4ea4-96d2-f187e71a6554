import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map, of } from 'rxjs';
import { Res } from '@share-types/center.types';

@Injectable({
  providedIn: 'root',
})
export class ExamHttpService {
  constructor(private http: HttpClient) {}

  public login(data: any): Observable<any> {
    return this.http.post('/login', data);
  }

  public register(data: any): Observable<any> {
    return this.http.post('/register', data);
  }

  public getProjects() {
    return this.http.get<Res<IHttpProject[]>>('/data/projects');
  }

  public getSchedules(id?: number) {
    return this.http.get<Res<IHttpSchedule[]>>('/data/schedules');
  }

  public getSchedule(id: string) {
    return this.http.get<Res<IHttpSchedule>>('/data/schedules/' + id);
  }

  public postFile(schedule_id: string, file: File, password?: string) {
    const formData = new FormData();
    formData.append('file', file, file.name);
    formData.append('password', password || '');
    return this.http.post<
      Res<
        | { status: 'success' }
        | { status: 'error'; error: { code: number; msg: string; data: any } }
      >
    >(`/data/schedules/${schedule_id}/package/import`, formData);
  }

  importFile(schedule_id: string, file_path: string, password?: string) {
    return this.http.post<Res<any>>(`/data/schedules/${schedule_id}/package/import`,{ file_path,password })
  }

  checkExamResult(schedule_id: string) {
    return this.http.get<Res<any>>(`/data/schedules/${schedule_id}/exam-result/check`)
  }

  public exportExamResult(
    schedule_id: string,
    dir_path: string,
    session_id?: string
  ) {
    return this.http.post<Res<any>>(
      `/data/schedules/${schedule_id}/exam-result/export`,
      {
        dir_path,
        session_id,
      }
    );
  }

  getRoomPackageList(schedule_id: string, session_id: string) {
    return this.http.get<Res<{id: string, name: string, created_at: number, size: number}[]>>(
      `/data/schedules/${schedule_id}/room-package/list`,
      {
        params: {
          session_id,
        }
      });
  }

  public exportRoomPackage(
    schedule_id: string,
    session_id: string,
    dir_path: string,
    file_id: string
  ) {
    return this.http.post<Res<any>>(
      `/data/schedules/${schedule_id}/room-package/export`,
      {
        session_id,
        dir_path,
        file_id
      }
    );
  }
}

export interface IHttpProject {
  id: string;
  name: string;
  start: string;
  end: string;
  test: 0 | 1;
}

export interface IHttpSchedule {
  schedule_id: string;
  start: string;
  end: string;
  test: 0 | 1;
  form_published: number;
  password_published: number;
  sessions: IHttpSession[];
  subjects: IHttpSubject[];
  events: IHttpEvent[];
}

export interface IHttpSession {
  id: string;
  name: string;
  schedule_id: string;
  start: string;
  end: string;
  room_sn: string;
  room_name: string;
  test: 0 | 1;
  status: SessionStatus;
  entry_status: IHttpEntryStat;
  form_status: 0 | 1;
  password_status: 0 | 1;
}

export enum SessionStatus {
  notEnter = 0, // 未入场
  enter = 1, // 已入场
  test = 2, // 考试中
  pause = 3, // 暂停
  end = 4, // 已结束

  centerUploaded = 100 // 已上传考点服务器
}

export interface IHttpEntryStat {
  total: number;
  not_enter: number;
  register: number;
  login: number;
  testing: number;
  finished: number;
  absent: number;
}

export interface IHttpSubject {
  id: string;
  session_id: string;
  name: string;
  code: string;
}

export interface IHttpEvent {
  id: string;
  session_id: string;
  type: string;
  content: string;
  created_at: number;
}
