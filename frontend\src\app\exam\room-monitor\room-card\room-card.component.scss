:host {
  display: inline-block;
}
.card {
  min-width: 400px;
  min-height: 300px;
  padding: 16px;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin: 5px;
  &:hover {
    box-shadow: 0px 6px 30px 0px rgba(0, 0, 0, 0.05),
      0px 16px 24px 0px rgba(0, 0, 0, 0.04),
      0px 8px 10px 0px rgba(0, 0, 0, 0.08);
    opacity: 1;
    border: 1px solid #419cf8;
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .more {
      cursor: pointer;
      &:hover {
        color: #419cf8;
      }
    }
  }
  .content {
    display: flex;
    justify-content: space-between;
    .info {
      width: 60%;
      flex-grow: 1;
    }
    .row {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin: 10px 0;
      .item {
        width: 100px;
      }
    }
    .chart {
      width: 150px;
    }
  }
  .footer {
    width: 100px;
  }
}
nz-divider {
  margin: 5px 0;
}
nz-steps {
  transform: scale(0.7) translate(0%, 100%);
}
