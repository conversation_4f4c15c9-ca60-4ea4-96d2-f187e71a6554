import { Injectable, signal } from '@angular/core';
import { LocalStorageService } from './local-storage.service';
import { Group } from '../http/cloud.http';

export interface Center {
  name: string;
  address: string;
  code: string;
  project_name: string;
  selectedGroup?: Group;

  formal: ProjectInfo
}

export interface ProjectInfo {
  name: string;
  project_id: string;
  start: string;
  end: string;
  // late_limit: number;
}
@Injectable({
  providedIn: 'root'
})
export class CenterService {

  center = signal<Center>({} as Center);

  constructor(private localStorage: LocalStorageService) {
    const center = this.localStorage.get('center') as Center;
    if (center) {
      this.center.set(center);
    }
  }

  register(center: Center) {
    this.localStorage.set('center', center);
    this.center.set(center);
  }

  logout() {
    this.localStorage.remove('center');
    this.center.set({} as Center);
  }

}
