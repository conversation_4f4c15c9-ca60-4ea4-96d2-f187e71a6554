import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Provider } from '@angular/core';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { URLService } from './url.service';

@Injectable()
export class ApiPrefixInterceptor implements HttpInterceptor {
  readonly serverUrl: string;
  readonly token: string = '';

  constructor(private urlService: URLService) {
    this.serverUrl = this.urlService.getServerUrl();
    // if (window.joyshell) {
    //   this.token = joyshell.Settings.ACCESS_TOKEN;
    // }
  }

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // 访问本地API(部分静态资源文件请求也需要本地访问,需统一加token)
    const opt: { url?: string; headers: HttpHeaders } = {
      headers: this.token
        ? req.headers.set('X-API-KEY', this.token)
        : req.headers,
    };

    if (!req.url.includes('http')) {
      opt.url = `${this.serverUrl}${req.url}`;
    }

    return next.handle(req.clone(opt));
  }
}

export const apiPrefixInterceptorProvider: Provider = {
  provide: HTTP_INTERCEPTORS,
  useClass: ApiPrefixInterceptor,
  multi: true,
};
