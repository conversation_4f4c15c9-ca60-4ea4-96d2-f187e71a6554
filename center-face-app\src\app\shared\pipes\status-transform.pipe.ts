import { Pipe, PipeTransform } from '@angular/core';

type transformArg =
  | 'sessionStatus'
  | 'sessionStatusTips'
  | 'entryStatus'
  | 'scheduleStatus';

@Pipe({ name: 'statusTrans', standalone: true })
export class StatusTransformPipe implements PipeTransform {
  sessionStatus = [
    '未开始',
    '入场中',
    '考试中',
    '已暂停',
    '结果未上传',
    '结果上传中',
    '结果已上传',
    '结果上传失败',
    '备份已上传',
  ];
  entryStatus = ['未进场', '已签到', '已登录', '考试中', '已完成', '缺考'];
  scheduleStatus = {
    'not-start': '未开始',
    'active': '进行中',
    'completed': '已结束',
  };
  constructor() {}

  transform(value: any, arg: transformArg) {
    switch (arg) {
      //  显示考生状态
      case 'entryStatus':
        return this.entryStatus[value];
      case 'scheduleStatus':
        return this.scheduleStatus[value as keyof typeof this.scheduleStatus] || '';
      default:
        return '';
    }
  }
}
