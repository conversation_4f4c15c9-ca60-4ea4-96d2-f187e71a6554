import { Injectable } from "@angular/core";
import { CameraSetting } from "./settings.service";
import { Center } from "./center.service";

export interface MediaConfig {
  deviceId: string | "default";
  label: string;
  kind: string;
}


interface KeyValue {
  "media-config": MediaConfig | null;
  "camera-settings": Partial<CameraSetting>;
  "center": Center;
}

type KeyName = keyof KeyValue;
@Injectable({
  providedIn: "root",
})
export class LocalStorageService {
  currentUser!: Record<string, unknown>;

  constructor() {
    this.getCurrentUser();
  }

  getCurrentUser() {
    const userStr = localStorage.getItem("currentUser");
    this.currentUser = userStr ? JSON.parse(userStr) : {};
    return this.currentUser;
  }

  get<T extends KeyName>(keyName: T, subKey?: string): KeyValue[T] | null {
    const itemString = window.localStorage.getItem(keyName);
    if (!itemString) {
      return null;
    }

    const itemObj = JSON.parse(itemString);
    if (!subKey || !itemObj) {
      return itemObj;
    }
    return itemObj[subKey] ? itemObj[subKey] : itemObj;
  }

  set<T extends KeyName>(keyName: T, item: KeyValue[T]): void {
    const parse = (itemObj: any) => {
      if (!itemObj) {
        return JSON.stringify({});
      }
      if (typeof itemObj === "object") {
        return JSON.stringify(itemObj);
      }
      return JSON.stringify({ data: itemObj });
    };
    window.localStorage.setItem(keyName, parse(item));
  }

  remove(keyName: KeyName): void {
    window.localStorage.removeItem(keyName);
  }

  clearAll(): void {
    window.localStorage.clear();
  }
}
