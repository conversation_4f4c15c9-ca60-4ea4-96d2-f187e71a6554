import { CommonModule, Location } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, Input, OnDestroy, signal, Signal, ViewChild, type OnInit, effect, computed } from '@angular/core';
import { MediaService } from '../../core/service/media.service';
import { Center, CenterService } from '@app/core/service/center.service';
import { ServerTimeService } from '@app/core/service/server-time.service';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { CustomDatePipe } from '@app/shared/pipes/custom-date.pipe';
import { OpenCvService } from '@app/core/service/face/open-cv.service';
import { FaceMatchByATAService, FaceDetectResultCode } from '@app/core/service/face/face-match-ata.service';
import { CloudHttpService } from '@app/core/http/cloud.http';
import { VideoDetectControl } from '@app/core/service/face/face-detect-base.service';
import { FaceDetectFactoryService } from '@app/core/service/face/face-detect-factory.service';
import { Subscription, filter, take, Subject, fromEvent } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ModalService } from '@app/core/service/modal.service';
import { FormsModule } from '@angular/forms';
import { SettingsService } from '@app/core/service/settings.service';
import { ActivatedRoute } from '@angular/router';
import { ScheduleStateService } from '@app/core/service/schedule-state.service';
import { PwaInstallService } from '@app/core/service/pwa-install.service';
import { HttpErrorResponse } from '@angular/common/http';

interface LastFaceData {
  image: HTMLImageElement | null;
  createTime: number;
  status: 'success'  | "not_found";
  entry_info: {name: string, permit: string} | null;
  retry: number;
  code?: string;
}

interface CameraSetting {
  width: number;
  height: number;
  mirror: boolean;
  frame: DetectFrameSize;
}

// 常见分辨率
const videoResolutions = {
  QVGA: {width: 320, height: 240},
  GVGA: {width: 480, height: 320},
  VGA: {width: 640, height: 480},
  Responsive: {width: 0, height: 0},
  // HD: {width: 1280, height: 720},
  // FHD: {width: 1920, height: 1080},
}

const detectFrameOptions = {
  small: 240,
  middle: 320,
  large: 480,
}

const faceDetectOptions = {
  faceInCenter: true,
  faceSize: true,
  minFaceNum: 1,
}

const enum TipType {
  NO_FACE = '请将面部置于框内',
  MULTI_FACE = '检测到多张人脸',
  FACE_NOT_IN_CENTER = '请将面部保持居中',
  FACE_TOO_SMALL = '人脸过小，请靠近一些',
  NOT_FOUND = '未找到考生',
  FACE_NOT_SHARP = '请保持不动',
}

const modalMessage = {
  "success": '核验完成，请入场准备考试',
  // "failed": '签到失败',
  "not_found": '您不在本考场，请查看准考证信息',
  "time_over": '签到结束，禁止入场',
}
type DetectFrameSize = keyof typeof detectFrameOptions;
@Component({
    selector: 'app-face-signin',
    imports: [
        CommonModule,
        NzIconModule,
        NzDropDownModule,
        NzBadgeModule,
        NzSelectModule,
        FormsModule,
        CustomDatePipe
    ],
    templateUrl: './face-signin.component.html',
    styleUrl: './face-signin.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class FaceSigninComponent implements OnInit, OnDestroy {

  tip = signal('请将面部置于框内');
  signinSuccess = signal(true);

  schedule_id!: string;
  center!: Signal<Center>;
  serverTime!: Signal<number>;
  
  // 超时状态监控
  isLate!: Signal<boolean>;
  hasShownTimeoutModal = signal(false);
  
  currentSchedule!: Signal<any>;
  
  isLoadingSchedule = signal(false);
  isScheduleLoaded = signal(false);

  settings: Partial<CameraSetting> = {};
  videoDevicesInfo: MediaDeviceInfo[] = [];
  currentDeviceId = signal('');
  isVedioPlaying = signal(false);
  isOpencvloaded = signal(false);
  isMobile = signal(false);
  isFullscreen = signal(false);
  isVideoMirror = signal(true);
  isPoseDetection = signal(true);
  isLandscape = signal(false);

  // 人脸识别引擎相关
  currentFaceEngine = signal<'mediapipe' | 'human'>('mediapipe');
  availableEngines = signal<{
    value: 'mediapipe' | 'human';
    label: string;
    supportsOffline: boolean;
    status: string;
  }[]>([]);
  isEngineReady = signal(false);
  engineStatus = signal<{
    engine: 'mediapipe' | 'human';
    ready: boolean;
    mode: string;
    supportsOffline: boolean;
    networkStatus: boolean;
  } | null>(null);

  autoAdjustDetectFrameSize = signal(true);

  videoResolutions = [
    {label: '320 * 240', value: 'QVGA'},
    {label: '480 * 320', value: 'GVGA'},
    {label: '640 * 480', value: 'VGA'},
    {label: '自适应', value: 'Responsive'},
    // {label: '1280 * 720', value: 'HD'},
    // {label: '1920 * 1080', value: 'FHD'},
  ];
  currentResolution = signal('Responsive');
  videoWidth = 640;
  videoHeight = 480;


  frameOptions: {label: string, value: DetectFrameSize}[] = [
    {label: '小', value: 'small'},
    {label: '中', value: 'middle'},
    {label: '大', value: 'large'},
  ]
  detectFrameSize = signal<DetectFrameSize>("middle");
  detectFrameWidth!: Signal<number>;
  detectFrame = { x: 200, y: 120, width: 240, height: 240 };
  faceDetectFrame!: CanvasRenderingContext2D;

  faceDetectControl!: VideoDetectControl;
  videoDetectSubscription!: Subscription;

  // 当前使用的人脸检测服务
  private currentFaceDetectService: any;

  lastFaceData: LastFaceData = { image: null, createTime: 0, status: 'success', retry: 0, entry_info: null };

  signinStatus = signal<{
    status: 'success' | 'failed';
    show: boolean;
    message?: string;
    info: {name: string, permit: string} | null;
  }>({
    status: 'success',
    show: false,
    message: '',
    info: null
  });
  showLogs = signal(true);
  logs: {timestamp: number, message: string, type: 'log' | 'error' | 'warn'}[] = [];
  isDebugExpanded = signal(true);

  @ViewChild("videoPlayer", { static: true }) videoPlayer!: ElementRef<HTMLVideoElement>;
  @ViewChild("face", { static: true }) faceCanvas!: ElementRef<HTMLCanvasElement>;
  @ViewChild("main", { static: true }) main!: ElementRef<HTMLElement>;
  @ViewChild("detectFrameImg", { static: true }) detectFrameImg!: ElementRef<HTMLImageElement>;
  @ViewChild('logContent') logContent!: ElementRef;

  @Input() set r_schedule_id(schedule_id: string) {
    this.schedule_id = schedule_id
  }

  private audioContext: AudioContext | null = null;
  private successAudioBuffer: AudioBuffer | null = null;
  private failedAudioBuffer: AudioBuffer | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private mediaService: MediaService,
    private location: Location,
    private centerService: CenterService,
    private serverTimeService: ServerTimeService,
    private opencvService: OpenCvService,
    private faceMatchByATAService: FaceMatchByATAService,
    private cloudHttp: CloudHttpService,
    private faceDetectFactory: FaceDetectFactoryService,
    private modalService: ModalService,
    private settingsService: SettingsService,
    private route: ActivatedRoute,
    private scheduleState: ScheduleStateService,
    private pwaInstallService: PwaInstallService
  ) {
    this.rewriteConsole();
    this.logs.push({timestamp: Date.now(), message: 'logger: start', type: 'log'})
    const {debug} = this.route.snapshot.queryParams;
    this.showLogs.set(!!debug);
        this.serverTime = toSignal(this.serverTimeService.getServerTimeStream(), { initialValue: 0 }) as Signal<number>;
    this.center = this.centerService.center;
    this.detectFrameWidth = computed(() => ({"small": 240, "middle": 320, "large": 480}[this.detectFrameSize()]));
    
    // 获取当前schedule
    this.currentSchedule = this.scheduleState.currentSchedule;

    // 初始化人脸识别引擎
    this.initializeFaceEngine();
    
    // 计算超时状态
    this.isLate = computed(() => {
      const serverTime = this.serverTime();
      const currentSchedule = this.currentSchedule();
      
      if (!serverTime || !currentSchedule) return false;
      
      const late_time = new Date(currentSchedule.start).getTime() + currentSchedule.late_limit * 60 * 1000;
      return serverTime > late_time;
    });
    toObservable(computed(() => this.isOpencvloaded() && this.isVedioPlaying() && this.isScheduleLoaded())).pipe(
      filter(inited => inited),
      take(1)
    ).subscribe(() => {
      console.log("加载完成");
      const {width, height} = (this.videoPlayer.nativeElement.srcObject as MediaStream).getVideoTracks()[0].getSettings();
      if (width && height) {
        this.setDetectFrameSizeByVideoSize(width, height);
      }

      this.calcVideoSize(width, height);
      this.setupFaceDetection();

      if (this.pwaInstallService.isInStandaloneMode && this.pwaInstallService.isIOS) {
        this.chooseResolution('Responsive');
        return;
      }

      if (!document.fullscreenElement) {
        this.modalService.confirm('是否切换到全屏模式以获得更好的签到体验？', () => {
          this.toggleFullscreen().then(
            () => {
              setTimeout(() => {
                this.chooseResolution('Responsive');
              }, 2000);
            }
          );
        });
      } else if (this.autoAdjustDetectFrameSize() && !this.isLate()) {
        this.modalService.confirm('是否需要自动调整画面大小？', () => {
          this.chooseResolution('Responsive');
        });
      }
    })

    effect(() => {
      if (this.detectFrameSize()) {
        this.resizeFaceDetectFrame();
      }
      if (!this.isOpencvloaded()) {
        return ;
      }
      if (this.currentFaceDetectService) {
        this.currentFaceDetectService.setVideoDetectConfig({
          video: this.videoPlayer.nativeElement,
          videoWidth: this.videoWidth,
          videoHeight: this.videoHeight,
          detectFrame: this.detectFrame,
          faceOptions: {
            faceInCenter: this.isPoseDetection(),
            faceSize: this.isPoseDetection(),
            minFaceNum: 1
          }
        });
      }
    })

    // 监控超时状态
    effect(() => {
      const isLate = this.isLate();
      if (isLate && !this.hasShownTimeoutModal()) {
        this.hasShownTimeoutModal.set(true);
        this.disableFaceRecognitionForTimeOver();
      }
    });

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (e) {
      console.warn('Web Audio API 不支持:', e);
    }
  }


  setDetectFrameSizeByVideoSize(width: number, height: number) {
    if (!this.autoAdjustDetectFrameSize()) {
      return;
    }
    if (!width || !height) return;
    
    const minDimension = Math.min(width, height);
    
    if (minDimension <= 320) {
      this.detectFrameSize.set('small');
    } else if (minDimension <= 480) {
      this.detectFrameSize.set('middle');
    } else {
      this.detectFrameSize.set('large');
    }
    
    this.settingsService.saveCameraSetting({ frame: this.detectFrameSize() });
    this.resizeFaceDetectFrame();
  }

  get isSmallScreen() {
    return window.innerWidth < 640;
  }

  // 初始化人脸识别引擎
  private async initializeFaceEngine() {
    const savedEngine = this.settingsService.getFaceEngine();
    this.currentFaceEngine.set(savedEngine);
    this.currentFaceDetectService = this.faceDetectFactory.getCurrentService();
    this.isEngineReady.set(this.currentFaceDetectService.isReady());

    // 加载可用引擎列表
    await this.loadAvailableEngines();

    // 加载当前引擎状态
    await this.updateEngineStatus();
  }

  // 加载可用引擎列表
  private async loadAvailableEngines() {
    try {
      const engines = await this.faceDetectFactory.getAvailableEngines();
      this.availableEngines.set(engines);
    } catch (error) {
      console.error('Failed to load available engines:', error);
      // 使用默认引擎列表
      this.availableEngines.set([
        { value: 'mediapipe', label: 'MediaPipe (OpenCV)', supportsOffline: true, status: 'Always available' },
        { value: 'human', label: 'Human.js', supportsOffline: false, status: 'Unknown' }
      ]);
    }
  }

  // 更新引擎状态
  private async updateEngineStatus() {
    try {
      const status = await this.faceDetectFactory.getCurrentEngineStatus();
      this.engineStatus.set(status);
    } catch (error) {
      console.error('Failed to get engine status:', error);
    }
  }

  // 切换人脸识别引擎
  async switchFaceEngine(engine: 'mediapipe' | 'human') {
    if (this.currentFaceEngine() === engine) {
      return;
    }

    try {
      this.tip.set('正在切换识别引擎...');

      // 停止当前检测
      if (this.faceDetectControl) {
        this.faceDetectControl.stop();
      }

      // 切换引擎（支持智能降级）
      this.currentFaceDetectService = await this.faceDetectFactory.switchEngine(engine);

      // 获取实际使用的引擎（可能因为降级而不同）
      const actualEngine = this.faceDetectFactory.getCurrentEngine();
      this.currentFaceEngine.set(actualEngine);
      this.isEngineReady.set(this.currentFaceDetectService.isReady());

      // 更新引擎状态
      await this.updateEngineStatus();

      // 重新设置检测配置
      if (this.isVedioPlaying() && this.videoPlayer?.nativeElement) {
        this.setupFaceDetection();
      }

      // 显示切换结果
      if (actualEngine !== engine) {
        this.tip.set(`已降级到 ${actualEngine === 'mediapipe' ? 'MediaPipe' : 'Human.js'} 引擎`);
        this.modalService.confirm(
          `${engine === 'human' ? 'Human.js' : 'MediaPipe'} 引擎不可用，已自动切换到 ${actualEngine === 'mediapipe' ? 'MediaPipe' : 'Human.js'} 引擎。`,
          () => {}
        );
      } else {
        this.tip.set('引擎切换完成');
      }

      console.log(`Switched to ${actualEngine} face detection engine`);
    } catch (error) {
      console.error('Failed to switch face engine:', error);
      this.tip.set('引擎切换失败');
      this.modalService.error('错误', `切换到 ${engine} 引擎失败: ${error}`);
    }
  }

  ngOnInit(): void {
    this.tip.set('正在初始化摄像头...');
    this.listenWindowResize();
    this.loadOpenCV();
    
    if (this.schedule_id) {
      this.scheduleState.setCurrentScheduleId(this.schedule_id);
      if (this.currentSchedule()) {
        this.isScheduleLoaded.set(true);
      } else {
        this.checkAndLoadScheduleData();
      }
    }

    // 加载保存的设置
    this.settings = this.settingsService.getCameraSetting();
    if (this.settings.frame) {
      this.detectFrameSize.set(this.settings.frame);
    }

    // 加载音频文件
    this.loadAudioFiles();
  }

  ngAfterViewInit(): void {
    this.isLandscape.set(window.innerWidth > window.innerHeight);
    const isResponsive = window.innerWidth < 640;
    const videoSize = this.getVideoSize(isResponsive);
    console.log('videoSize:', JSON.stringify(videoSize));
    this.faceDetectFrame = this.faceCanvas.nativeElement.getContext('2d')!;
    this.initMediaStream(videoSize);
    (window as any).initMediaStream = this.initMediaStream.bind(this);
  }

  ngOnDestroy(): void {
    this.mediaService.closeMediaStream();
    if (this.faceDetectControl) {
      this.faceDetectControl.stop();
    }
    if (this.videoDetectSubscription) {
      this.videoDetectSubscription.unsubscribe();
    }
    if (this.audioContext) {
      this.audioContext.close();
    }

    // 清理人脸检测工厂
    this.faceDetectFactory.destroy();

    this.destroy$.next();
    this.destroy$.complete();
  }

  
  private initMediaStream(videoSize: { width: number, height: number }) {
    this.mediaService.getMediaStream(videoSize.width, videoSize.height).then(stream => {
      if (stream) {
        (window as any)["media"] = stream;
        this.attachMediaStream(stream);
      } else {
        console.error('getMediaStream warning: stream is null');
      }
    }).catch(err => {
      console.error('getMediaStream error:', err);
      if (this.faceDetectControl) {
        this.faceDetectControl.stop();
      }
      this.modalService.error('错误', "获取摄像头失败: " + err.message);
    });
  }

  getVideoSize(responsive = true) {
    const mainWidth = this.main.nativeElement.offsetWidth;
    const mainHeight = this.main.nativeElement.offsetHeight;
    console.log('mainWidth:', mainWidth, 'mainHeight:', mainHeight);
    if (responsive) {
      this.isLandscape.set(window.innerWidth > window.innerHeight);
      const videoSize = !this.isLandscape() ? {
        width: mainHeight - 84,
        height: mainWidth
      } : {
        width: mainWidth,
        height: mainHeight - 84
      };
      console.log('videoSize:', JSON.stringify(videoSize));
      return videoSize;
    }
    let resolutionKey = 'QVGA';
    for (const [key, resolution] of Object.entries(videoResolutions)) {
      if (mainWidth >= resolution.width && mainHeight >= resolution.height) {
        resolutionKey = key;
      }
    }
    return videoResolutions[resolutionKey as keyof typeof videoResolutions];
  }

  listenWindowResize() {
    fromEvent(window, 'resize')
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        console.log('window resize');
        this.isLandscape.set(window.innerWidth > window.innerHeight);
        // setTimeout(() => {
        //   const {width, height} = (this.videoPlayer.nativeElement.srcObject as MediaStream).getVideoTracks()[0].getSettings();
        //   this.calcVideoSize(width, height);
        // }, 1000);
      });

    fromEvent(window, 'orientationchange')
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.isLandscape.set(window.innerWidth > window.innerHeight);
        console.log('orientationchange:', window.innerWidth, window.innerHeight);
        setTimeout(() => {
          const {width, height} = (this.videoPlayer.nativeElement.srcObject as MediaStream).getVideoTracks()[0].getSettings();

          if (width && height) {
            this.setDetectFrameSizeByVideoSize(width, height);
          }
          this.calcVideoSize(width, height);
          if (this.currentResolution() === 'Responsive') {
            this.modalService.confirm('是否重新调整分辨率？', () => {
                this.chooseResolution('Responsive');
            });
          }
        }, 1000);
      });

    fromEvent(window, 'fullscreenchange')
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        const isFullscreen = !!document.fullscreenElement;
        this.isFullscreen.set(isFullscreen);
        if (this.currentResolution() === "Responsive") {
          setTimeout(() => {
            this.chooseResolution('Responsive');
          }, 1000);
        }
      });
  }

  calcVideoSize(width: number | undefined, height: number | undefined) {
    width = width ?? 640;
    height = height ?? 480;
    const mainWidth = this.main.nativeElement.offsetWidth;
    const mainHeight = this.main.nativeElement.offsetHeight;
    this.videoWidth = width < mainWidth ? width : mainWidth;
    this.videoHeight = height < mainHeight ? height : mainHeight;
    // 根据当前视频尺寸设置分辨率
    let resolution = 'Responsive';
    for (const [key, value] of Object.entries(videoResolutions)) {
        if (this.videoWidth === value.width && this.videoHeight === value.height
          || this.videoWidth === value.height && this.videoHeight === value.width) {
            resolution = key;
            break;
        }
    }
    this.currentResolution.set(resolution);
    console.log('video width:', this.videoWidth, 'height:', this.videoHeight);
    this.resizeFaceDetectFrame();
  }

  loadOpenCV() {
    this.opencvService.loadOpenCV();
    this.opencvService.isReady$.subscribe((res) => {
      if (res.ready) {
        console.log('opencv loaded');
        this.isOpencvloaded.set(true);
      }
    });
  }

  setupFaceDetection() {
    // 检查是否超时，如果超时则不启动人脸识别
    if (this.isLate()) {
      this.disableFaceRecognitionForTimeOver();
      return;
    }
    
    this.faceDetectControl = this.currentFaceDetectService.setVideoDetectConfig({
        video: this.videoPlayer.nativeElement,
        videoWidth: this.videoWidth,
        videoHeight: this.videoHeight,
        detectFrame: this.detectFrame,
        faceOptions:  {
          faceInCenter: this.isPoseDetection(),
          faceSize: this.isPoseDetection(),
          minFaceNum: 1
        }
    });
    this.resizeFaceDetectFrame();
    this.videoDetectSubscription = this.faceDetectControl.faceDetect$.subscribe((faceDetectRes) => {
      if (!faceDetectRes) {
        console.log('face detect stop');
        return;
      }
      if (faceDetectRes.code !== FaceDetectResultCode.NO_FACE) {
        console.log('detectFace:', faceDetectRes.code);
      }
      const { code, image, faceImage } = faceDetectRes;
      if (code !== FaceDetectResultCode.SUCCESS) {
        const msg = ({
          [FaceDetectResultCode.NO_FACE]: TipType.NO_FACE, 
          [FaceDetectResultCode.MULTI_FACE]: TipType.MULTI_FACE, 
          [FaceDetectResultCode.FACE_NOT_IN_CENTER]: TipType.FACE_NOT_IN_CENTER,
          [FaceDetectResultCode.FACE_TOO_SMALL]: TipType.FACE_TOO_SMALL,
        } as Record<FaceDetectResultCode, TipType>)[code];
        if (msg) {
          if (this.tip() !== msg && this.showLogs()) {
            console.log(msg);
          }
          this.tip.set(msg);
        }
        return this.faceDetectControl.start();
      }
      if (image && faceImage) {
        this.handleFaceDetection(image, faceImage);
      } else {
        this.tip.set(TipType.NO_FACE);
        this.faceDetectControl.start();
      }
    })
    this.faceDetectControl.start();
  }

  attachMediaStream(mediaStream: MediaStream) {
    if (mediaStream) {
      const videoEle = this.videoPlayer.nativeElement;
      videoEle.srcObject = mediaStream;
      videoEle.onplaying = () => {
        console.log("video playing");
        this.isVedioPlaying.set(true);
      };
      videoEle.onerror = (e) => {
        console.log("video playing error", e);
        this.isVedioPlaying.set(false);
      };
      this.currentDeviceId.set(this.mediaService.mediaConfig?.deviceId || '');
      this.isVideoMirror.set(this.isFrontCamera(mediaStream));
    } else {
      console.error("photo capturer get media stream: no media stream!");
    }
  }

  async handleFaceDetection(imageBase64: string, faceImageBase64: string) {
    // 检查是否超时
    if (this.isLate()) {
      this.disableFaceRecognitionForTimeOver();
      return;
    }
    
    this.faceDetectControl.stop();
    const faceImageEle = await this.faceMatchByATAService.getHTMLImageElement(faceImageBase64);
    if (this.isPoseDetection()) {
      const { result } = await this.faceMatchByATAService.detect_sharp(faceImageEle, 100);
      console.log('face sharp:', result);
      if (!result) {
        this.tip.set(TipType.FACE_NOT_SHARP);
        this.faceDetectControl.start();
        return;
      }
    }
    const isNewFaceDetected = !this.lastFaceData.image || await this.isDifferentFace(faceImageEle, this.lastFaceData.image!);

    console.log('detected new face:', isNewFaceDetected);
    if (isNewFaceDetected) {
        this.lastFaceData = { image: faceImageEle, createTime: Date.now(), status: 'success', retry: 0, entry_info: null };
        this.searchFaceFromCloud(imageBase64);
    } else {
      if (this.lastFaceData.status === "success") {
        this.signinStatusModal('success', modalMessage.success, this.lastFaceData.entry_info);
      } else if (this.lastFaceData.status === "not_found") {
        console.warn('skip same face not found:', this.lastFaceData.retry);
        if (this.lastFaceData.retry >= 1) {
          this.lastFaceData.retry++;
          this.tip.set(TipType.NOT_FOUND);
          this.signinStatusModal('failed', modalMessage.not_found);
        } else {
          this.lastFaceData.retry++;
          this.searchFaceFromCloud(imageBase64, "secondly");
        }
      } else {
        this.faceDetectControl.start();
      }
    }
  }

  disableFaceRecognitionForTimeOver() {
    if (this.faceDetectControl) {
      this.faceDetectControl.stop();
    }
    
    this.tip.set('签到结束，禁止入场');
    
    console.log('人脸识别已禁用：签到超时');
  }

  chooseDetectFrame(option: DetectFrameSize) {
    this.detectFrameSize.set(option);
    this.settingsService.saveCameraSetting({ frame: option });
  }

  resizeFaceDetectFrame() {
    const frameWidth = detectFrameOptions[this.detectFrameSize() as DetectFrameSize];
    this.detectFrame = { x: this.videoWidth / 2 - frameWidth / 2, y: this.videoHeight / 2 - frameWidth/2, width: frameWidth, height: frameWidth };
    if (this.detectFrame.x < 0 || this.detectFrame.y < 0 || this.detectFrame.width > this.videoWidth || this.detectFrame.height > this.videoHeight) {
      this.detectFrame = { x: 0, y: 0, width: this.videoWidth, height: this.videoHeight };
      this.detectFrameImg.nativeElement.style.display = 'none';
    } else {
      this.detectFrameImg.nativeElement.style.display = 'block';
    }
    this.drawFaceDetectFrame();
  }

  drawFaceDetectFrame() {
    this.faceCanvas.nativeElement.width = this.videoWidth;
    this.faceCanvas.nativeElement.height = this.videoHeight;
    this.faceDetectFrame.clearRect(0, 0, this.videoWidth, this.videoHeight);
    
    // 绘制遮罩
    this.faceDetectFrame.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.faceDetectFrame.fillRect(0, 0, this.videoWidth, this.videoHeight);

    // 清除矩形区域以显示
    const { x, y, width, height } = this.detectFrame;
    this.faceDetectFrame.clearRect(x, y, width, height);
    // this.faceDetectFrame.lineWidth = 3;
    // this.faceDetectFrame.strokeStyle = 'red';
    // this.faceDetectFrame.strokeRect(x, y, width, height);
  }

  async isDifferentFace(image1: HTMLImageElement, image2: HTMLImageElement) {
    const res = await this.faceMatchByATAService.compare(image1, image2);
    if (res.code !== 0) {
      return true;
    }
    return res.confidence > 1.18;
  }

  searchFaceFromCloud(photo: string, mode?: "secondly") {
    this.tip.set('识别中...');
    if (this.showLogs()) {
      console.log('识别中...');
    }
    this.cloudHttp.searchFace(this.schedule_id, photo.split(',')[1], mode).subscribe({
      next: (res) => {
        const { errcode, errmsg, data } = res;
        if (errcode) {
            console.error("searchFaceFromCloud error:", errcode, errmsg);
            this.signinStatusModal('failed', `服务连接异常（${errcode}）`);
            return;
          }
    
          const { has_match, entry_info } = data;
          if (has_match) {
            console.log('签到成功', entry_info?.name, entry_info?.permit);
            this.lastFaceData.status = 'success';
            this.lastFaceData.entry_info = entry_info;
            this.signinStatusModal('success', modalMessage.success, entry_info);
          } else {
            const { error_code } = data;
            this.handleFaceSearchError(error_code);
          }
        console.log("search face from cloud:", JSON.stringify(res, null, 2));
      },
      error: (error) => {
        console.error('search face from cloud error:', error);
        if (error.name === 'TimeoutError') {
          console.error(error);
          this.handleFaceSearchError('408');
        } else if(error instanceof HttpErrorResponse) {
          this.handleFaceSearchError(error.status.toString());
        } else {
          this.handleFaceSearchError('unknown');
        }
      }
    });
  }

  handleFaceSearchError(error_code: string) {
    switch (error_code) {
      case "110002":
      case "110003":
        console.warn('未找到匹配的考生:', error_code);
        this.signinStatusModal('failed', modalMessage.not_found);
        this.lastFaceData.status = 'not_found';
        break;
      case "222207":
        console.warn('未找到匹配的考生:', error_code);
        this.signinStatusModal('failed', modalMessage.not_found);
        this.lastFaceData.status = 'not_found';
        break;
      case "1100018":
        const randomSeconds = Math.floor(Math.random() * 5) + 1;
        console.warn('服务连接异常: QPS 超限', randomSeconds);
        this.signinStatusModal('failed', `服务连接异常（${error_code}）`, null, randomSeconds * 1000);
        this.lastFaceData = { image: null, createTime: 0, status: 'not_found', retry: 0, entry_info: null };
        break;
      default:
        const errorMessage = errorCodeMap[error_code] || '服务连接异常';
        this.signinStatusModal('failed', `${errorMessage}(${error_code})`);
        this.lastFaceData = { image: null, createTime: 0, status: 'not_found', retry: 0, entry_info: null };
    }
  }

  signinStatusModal(status: 'success' | 'failed', message: string, info: {name: string, permit: string} | null = null, delay = 2000) {
    this.signinStatus.set({status, show: true, message, info});
    
    if (status === 'success') {
      this.playAudio(this.successAudioBuffer);
    } else {
      this.playAudio(this.failedAudioBuffer);
    }

    setTimeout(() => {
      this.signinStatus.set({status, show: false, message, info: null});
      this.faceDetectControl.start();
    }, delay);
  }

  async getVideoDeviceInfo() {
    this.videoDevicesInfo = (await this.mediaService.getVideoDevicesInfo()) || [];
  }

  async chooseDevice(device: MediaDeviceInfo) {
    this.mediaService.setDeviceConfig(device);
    console.log('chooseDevice:', this.videoWidth, this.videoHeight);

    const width = this.isLandscape() ? this.videoWidth : this.videoHeight;
    const height = this.isLandscape() ? this.videoHeight : this.videoWidth;
    const mediaStream = await this.mediaService.refreshCamera(width, height);
    if (mediaStream) {
      this.attachMediaStream(mediaStream);
      setTimeout(() => {
        const settings = mediaStream.getVideoTracks()[0].getSettings();
        const currentWidth = settings.width;
        const currentHeight = settings.height;

        if (currentWidth && currentHeight) {
          this.setDetectFrameSizeByVideoSize(currentWidth, currentHeight);
        }
        this.calcVideoSize(currentWidth, currentHeight);
      }, 1000);
    }
  }

  async chooseResolution(resolution: string) {
    let size: {width: number, height: number};
    if (resolution === 'Responsive') {
      size = this.getVideoSize(true);
    } else {
      size = videoResolutions[resolution as keyof typeof videoResolutions]
    }
    console.log('chooseResolution  size:', size);
    const mediaStream = await this.mediaService.refreshCamera(size.width, size.height)
    if (mediaStream) {
      this.attachMediaStream(mediaStream);
      setTimeout(() => {
        const {width, height} = mediaStream.getVideoTracks()[0].getSettings();
        console.log('chooseResolution:', width, height);
        const isResolutionMismatch = width !== undefined && height !== undefined && (width !== size.width || height !== size.height);
        const isWidthHeightSwapped = width !== undefined && height !== undefined && (width === size.height && height === size.width);
        if (isResolutionMismatch && !isWidthHeightSwapped) {
          this.modalService.error('提示', '摄像头不支持该分辨率！');
        } else {
          this.currentResolution.set(resolution);
        }
        if (width && height) {
          this.setDetectFrameSizeByVideoSize(width, height);
        }
        this.calcVideoSize(width, height);
      }, 1000); 
    }
    this.settingsService.saveCameraSetting({ resolution });
  }

  isFrontCamera(mediaStream: MediaStream) {
    const track = mediaStream.getVideoTracks()[0];
    if (!track) return false;
    return track.getSettings().facingMode === "user" || track.label.toLowerCase().includes("front");
  }

  saveCameraSetting(setting: Partial<CameraSetting>) {
    console.log('saveCameraSetting:', setting);
    localStorage.setItem('setting', JSON.stringify(setting));
  }

  goBack() {
    this.location.back();
  }

  toggleMirror() {
    const mirror = !this.isVideoMirror();
    this.isVideoMirror.set(mirror);
    this.settingsService.saveCameraSetting({ mirror });
  }

  togglePoseDetection() {
    const poseDetection = !this.isPoseDetection();
    this.isPoseDetection.set(poseDetection);
    if (this.currentFaceDetectService) {
      this.currentFaceDetectService.setFaceOptions({minFaceNum: 1});
    }
  }

  async toggleFullscreen() {
    const fullscreen = !this.isFullscreen();
    const ele = document.documentElement as HTMLElement;

    if (fullscreen) {
      const requestFullscreen = ele.requestFullscreen || (ele as any).webkitRequestFullscreen || (ele as any).mozRequestFullScreen;

      if (requestFullscreen) {
        return requestFullscreen.call(ele).catch((err) => {
          console.error('Fullscreen error:', err);
          this.modalService.error('提示', '您的浏览器不支持全屏模式。');
        });
      } else {
        this.modalService.error('提示', '您的浏览器不支持全屏模式。');
      }
    } else {
      if (document.fullscreenElement && document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }

  private async loadAudioFiles() {
    if (!this.audioContext) return;

    try {
      const successResponse = await fetch('assets/audio/success.mp3');
      const successArrayBuffer = await successResponse.arrayBuffer();
      this.successAudioBuffer = await this.audioContext.decodeAudioData(successArrayBuffer);
      const failedResponse = await fetch('assets/audio/failed.mp3');
      const failedArrayBuffer = await failedResponse.arrayBuffer();
      this.failedAudioBuffer = await this.audioContext.decodeAudioData(failedArrayBuffer);

    } catch (err) {
      console.error('加载音频文件失败:', err);
    }
  }

  private async playAudio(buffer: AudioBuffer | null) {
    if (!this.audioContext || !buffer) return;

    try {
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      const source = this.audioContext.createBufferSource();
      source.buffer = buffer;

      const gainNode = this.audioContext.createGain();
      gainNode.gain.value = 1.0;

      source.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      source.start(0);
    } catch (err) {
      console.error('播放音频失败:', err);
    }
  }

  rewriteConsole() {
    type ConsoleMethod = 'log' | 'warn' | 'error';
    const methods: ConsoleMethod[] = ['log', 'warn', 'error'];
    
    methods.forEach(method => {
      const originalMethod = console[method].bind(console);
      (console[method] as unknown as Function) = (...args: any[]) => {
        originalMethod(...args);
        if (!this.showLogs()) return;
        this.logs.push({timestamp: Date.now(), message: args.join(' '), type: method });
        setTimeout(() => {
          this.logContent.nativeElement.scrollTop = this.logContent.nativeElement.scrollHeight;
        }, 200);
      };
    });
  }

  /**
   * 检查并加载schedule数据
   */
  private checkAndLoadScheduleData(): void {
    // 使用setTimeout确保signals已经初始化
    setTimeout(() => {
      const currentSchedule = this.currentSchedule();
      if (!currentSchedule && this.schedule_id) {
        console.log('request schedule data from cloud...');
        this.isLoadingSchedule.set(true);
        
        this.scheduleState.getScheduleFromCloud(this.schedule_id)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (schedule) => {
              this.isLoadingSchedule.set(false);
              if (schedule) {
                this.isScheduleLoaded.set(true);
              } else {
                console.error('schedule not found:', this.schedule_id);
                this.tip.set('未找到考试信息');
                this.modalService.error('错误', '未找到指定的考试信息');
              }
            },
            error: (error) => {
              this.isLoadingSchedule.set(false);
              console.error('get schedule data failed:', error);
              this.tip.set('加载考试信息失败');
              this.modalService.error('错误', '加载考试信息失败，请稍后重试');
            }
          });
      }
    }, 100);
  }
}

const errorCodeMap: Record<string, string> = {
  "223113": '请勿遮挡面部',
  "223114": '人脸模糊，请勿晃动',
  "223115": '请到光线适宜的地方拍摄',
  "223116": '请勿遮挡面部',
  "223120": '活体检测未通过',
  "223121": '请勿遮挡左眼',
  "223122": '请勿遮挡右眼',
  "223123": '请勿遮挡左脸颊',
  "223124": '请勿遮挡右脸颊',
  "223125": '请勿遮挡下巴',
  "223126": '请勿遮挡鼻子',
  "223127": '请勿遮挡嘴巴',
}