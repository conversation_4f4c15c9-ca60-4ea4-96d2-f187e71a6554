import { inject, Injectable } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../service/auth.service';

export const AuthGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const authService = inject(AuthService);
  const server_code = route.params['server_code'];
  const checkServerCode = server_code ? authService.serverCode === server_code : true;
  console.log("login guard",authService.isLogin, checkServerCode);
  if (authService.isLogin && checkServerCode) {
    return true;
  }
  if (server_code) {
    return router.createUrlTree([`/login/${server_code}`], { queryParams: { relogin: true } });
  }
  return router.createUrlTree(['/login'], { queryParams: { relogin: true } });
};
