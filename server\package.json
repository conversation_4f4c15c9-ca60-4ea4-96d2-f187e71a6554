{"name": "center-server", "version": "1.0.0", "description": "center server", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsup ./app.ts --format cjs --dts-resolve --minify --clean --sourcemap --dts --target node18 --outDir ../dist/server/server.js", "dev": "ts-node ./app.ts"}, "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "express-jwt": "^8.4.1", "fs-extra": "^11.2.0", "got": "11.8.6", "jsonwebtoken": "^9.0.2", "multer": "1.4.5-lts.1", "qrcode": "^1.5.3", "mysql2": "^3.11.0", "sequelize": "^6.35.2", "rxjs": "^7.8.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "gulp": "^4.0.2", "jest": "^29.7.0", "nock": "^13.4.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsup": "^8.0.1", "sqlite3": "^5.1.6", "typescript": "^5.3.3"}}