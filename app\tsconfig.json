{
  "compilerOptions": {
    "target": "ESNext",
    "module": "commonjs",
    "esModuleInterop": true,
    "isolatedModules": true,
    "strict": true,
    "noEmit": false,
    "skipLibCheck": true,
    "allowJs": true,
    "noImplicitAny": false,
    "sourceMap": true,
    "baseUrl": ".",
    "outDir": "dist",
    "resolveJsonModule": true,
    "paths": {
      "@share-types/*": ["../share/types/*"],
    },

  },
  "types": ["node", "electron"],
  "exclude": [
    "node_modules"
  ],
  "include": ["src"],
  "ts-node": {
    "esm": true,
    "transpileOnly": true,
  }
}
