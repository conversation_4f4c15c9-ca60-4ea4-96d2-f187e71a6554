import { CommonModule, Location } from '@angular/common';

import { ChangeDetectorRef, Component, ViewContainerRef, type OnInit } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { filter, switchMap, take, tap, timer } from 'rxjs';
import { CenterHttpService } from '../core/http/center.service';
import { ServerTimeService } from '../core/services/server-time.service';
import { CustomDatePipe } from '../shared/pipes/custom-date.pipe';
import { resetStores } from '../core/data';
import { ModalService } from '@app/core/services/modal.service';
import { MessageService } from '@app/core/services/message.service';
import { LogHttpService } from '@app/core/http/log.service';
import { ElogType } from '@share-types/center.types';

@Component({
    selector: 'app-dashboard',
    imports: [
        CommonModule,
        RouterOutlet,
        NzIconModule,
        NzTabsModule,
        NzDropDownModule,
        CustomDatePipe,
    ],
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  current_tab_index = 0;
  time: number;

  routeHistory: { [key: string]: string } = {
    home: 'home',
    room: 'room',
    schedule: 'schedule',
  };
  activeRoute: string = '';
  showBackBtn = false;

  appVersion: string;

  constructor(
    private router: Router,
    private centerHttp: CenterHttpService,
    private timeService: ServerTimeService,
    private location: Location,
    private cdRef: ChangeDetectorRef,
    private modalService: ModalService,
    private viewContainerRef: ViewContainerRef,
    private msgService: MessageService,
    private logHttp: LogHttpService
  ) {}

  ngOnInit(): void {
    this.appVersion = window.joyshell?.AppVersion || '2.0';
    this.checkSystemTime();
    this.timeService.getServerTime$().subscribe(time => {
      this.time = time;
      this.cdRef.detectChanges();
    });
    this.location.onUrlChange((url) => this.onUrlChange(url));
    this.onUrlChange(this.location.path())
  }

  onUrlChange(url: string) {
    const u = url.replace(/^.*\/dashboard\//, '');

    this.showBackBtn = u.includes('/');

    for (const [prefix, route] of Object.entries(this.routeHistory)) {
      if (u.startsWith(prefix)) {
        this.activeRoute = route;
        this.routeHistory[prefix] = u;
        this.current_tab_index =
          { home: 0, room: 1, schedule: 2 }[prefix] ?? -1;
        break;
      }
    }
  }
  onTabClick(name: string) {
    const s = this.location
      .path()
      .replace(/^.*\/dashboard\//, '')
      .startsWith(name);

    const route = s
      ? this.router.parseUrl(`/dashboard/${name}`)
      : this.router.parseUrl(`/dashboard/${this.routeHistory[name]}`);
    this.router.navigateByUrl(route).catch((err) => console.error(err));
  }

  onBackClick() {
    const currentPath = this.router.url;
    const lastSlashIndex = currentPath.lastIndexOf('/');
    if (lastSlashIndex > 0) {
      const parentPath = currentPath.substring(0, lastSlashIndex);
      this.router.navigate([parentPath]).catch((err) => console.error(err));
    } else {
      this.location.back();
    }
  }

  openSetting() {
    const modal = this.modalService.settingModal(this.viewContainerRef);
    modal.afterClose.subscribe((result) => {
      if (result) {
        this.msgService.create("success", "设置成功");
      }
    })
  }

  goToOperationLog() {
    this.router.navigate(['/dashboard/log']);
    this.current_tab_index = 0;
  }

  logout() {
    this.modalService.confirm('确认注销吗？', () => {
      this.centerHttp.logout().subscribe(() => {
        resetStores();
        this.logHttp.createLog(ElogType.CenterLogout, "").subscribe();
        this.router.navigate(['/login']);
      });
    })
  }

  checkSystemTime() {
    timer(10000)
      .pipe(
        switchMap(() => this.timeService.is_time_synced$),
        filter((is_synced) => is_synced),
        take(1),
        tap(() => {
          const time = this.timeService.getServerTimeValue();
          const computer_time = new Date().getTime();
          console.log('Check system time', time, computer_time);
          if (Math.abs((time - computer_time) / 1000) > 5 * 60) {
            console.warn('System time ', time, computer_time);
            // this.checkTimeModalOn = true;
          }
        })
      )
      .subscribe();
  }
}
