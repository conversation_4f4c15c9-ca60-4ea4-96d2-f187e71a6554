import {
  SyncTime,
  AuthEventRes,
  EAuthEventTypes,
} from '@share-types/auth-event.types';
import { authEventService, centerService } from '@services/index';
import serverTime from '@services/serverTime';
import { cloudService } from '@services/cloud/cloud.service';

export async function syncTime(detail: SyncTime): Promise<AuthEventRes> {
  const { schedule_id } = detail;
  const auth_event = authEventService.createAuthEvent('timing', {
    schedule_id,
    type: EAuthEventTypes.SyncTime,
    content: '校时',
  });
  auth_event.addHandle(async () => {
    cloudService.is_time_synced = true;
    const now = serverTime.date().toISOString();
    console.log(`server time: ${now}`);
  });
  const qr_code = await authEventService.getQRCode(
    auth_event,
    centerService.center.center_id,
  );
  return {
    status: 'failed',
    info: { event_id: auth_event.id, type: auth_event.type, data: qr_code },
  };
}
