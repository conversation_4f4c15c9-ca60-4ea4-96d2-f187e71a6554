import { <PERSON><PERSON><PERSON>ead<PERSON> } from '@share-types/addon.types';
import { DataAccess } from '@data/types';
import path from 'node:path';
import * as IPackage from './package.type';
import {
  DataPackage,
  IPackageStrategy,
  PackageError,
  PackageErrorCode,
  readZipFile,
} from './package.manager';
import { fileMd5 } from '@server/utils/filemd5';
import fs from 'fs-extra';

export class FormPackageStrategy implements IPackageStrategy {
  dataAccess: DataAccess;
  formBaseDir: string;
  constructor(dataAccess: DataAccess, formBaseDir: string) {
    this.dataAccess = dataAccess;
    this.formBaseDir = formBaseDir;
  }

  async handlePackage(dataPackage: DataPackage) {
    const forms = await readZipFile<IPackage.Form[]>(
      dataPackage.zip,
      'form.json',
    );
    const schedule = await this.dataAccess.schedule.get(forms[0].schedule_id);
    const is_form_published = !!schedule?.form_published;
    const is_password_published = !!schedule?.password_published;
    let form_count = 0;
    let password_count = 0;
    for (const form of forms) {
      let is_form = false;
      const form_path = await this.importForm(dataPackage.zip, form);
      if (form_path) {
        const isFileOk = (await fileMd5(form_path)) === form.md5.toLowerCase();
        if (!isFileOk) {
          console.error('Package: file md5 is wrong:', form.id + '.form');
          throw new PackageError(
            'Package: file md5 is wrong: ' + form.id + '.form',
            PackageErrorCode.DATA_ERROR,
          );
        }
        is_form = true;
        form_count++;
      } else {
        console.warn('Package: import form failed:', form.id + '.form');
      }
      const session = await this.dataAccess.session.get({
        schedule_id: form.schedule_id,
      });
      if (!session) {
        throw new PackageError(
          'Package: session not found: ' + form.schedule_id,
          PackageErrorCode.DATA_ERROR,
        );
      }
      if (form.password) {
        password_count++;
      }
      await this.dataAccess.form.addOrUpdateForm({
        form_id: form.id,
        schedule_id: form.schedule_id,
        subject_id: form.subject_id,
        form_time: is_form ? new Date().toISOString() : undefined,
        password_time: form.password ? new Date().toISOString() : undefined,
        form_publish_time: is_form_published
          ? new Date().toISOString()
          : undefined,
        password_publish_time:
          is_password_published && form.password
            ? new Date().toISOString()
            : undefined,
        name: form.name,
        md5: form.md5,
        code: form.code || '',
        password: form.password,
        version: form.version,
      });
    }
    console.log(
      `Package: import ${form_count} forms, ${password_count} passwords`,
    );
    return { form: form_count, password: password_count };
  }

  async importForm(zip: ZipReader, form: IPackage.Form) {
    const form_name = form.id + '.form';
    if (!zip.exists(form_name)) {
      console.warn(`Package: form not exists: ${form_name}`);
      return null;
    }
    const formPath = path.join(
      this.formBaseDir,
      form.schedule_id,
      form.id + '.form',
    );
    fs.ensureDirSync(path.join(this.formBaseDir, form.schedule_id));
    await zip.extract(form_name, formPath);
    return formPath;
  }
}
