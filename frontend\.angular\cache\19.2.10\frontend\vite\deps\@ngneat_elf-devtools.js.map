{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@ngneat+elf-devtools@1.3.0/node_modules/@ngneat/elf-devtools/index.js"], "sourcesContent": ["import { getRegistry, registry$, capitalize, getStoresSnapshot, getStore } from '@ngneat/elf';\nimport { Subject } from 'rxjs';\nimport { skip } from 'rxjs/operators';\nconst externalEvents$ = new Subject();\nfunction send(action) {\n  externalEvents$.next(action);\n}\nfunction devTools(options = {}) {\n  if (!window.__REDUX_DEVTOOLS_EXTENSION__) return;\n  let lock = false;\n  const instance = window.__REDUX_DEVTOOLS_EXTENSION__.connect(options);\n  const subscriptions = new Map();\n  const send = action => {\n    instance.send(action, getStoresSnapshot());\n  };\n  subscriptions.set('externalSend', externalEvents$.subscribe(send));\n  const addStore = store => {\n    const name = store.name;\n    const displayName = capitalize(name);\n    send({\n      type: `[${displayName}] - @Init`\n    });\n    const update = store.pipe(skip(1)).subscribe(() => {\n      if (lock) {\n        lock = false;\n        return;\n      }\n      options.preAction?.();\n      send({\n        type: `[${displayName}] - Update`\n      });\n    });\n    subscriptions.set(name, update);\n  };\n  // There should be support for stores that were created before we initialized the `devTools`\n  getRegistry().forEach(addStore);\n  if (options.actionsDispatcher) {\n    subscriptions.set('actionsDispatcher', options.actionsDispatcher.subscribe(action => {\n      send(action);\n    }));\n  }\n  const subscription = registry$.subscribe(({\n    store,\n    type\n  }) => {\n    const name = store.name;\n    const displayName = capitalize(name);\n    if (options.logTrace) {\n      const msg = `[${displayName}] - ${type}`;\n      console.groupCollapsed(msg);\n      console.trace();\n      console.groupEnd();\n    }\n    if (type === 'add') {\n      addStore(store);\n    }\n    if (type === 'remove') {\n      subscriptions.get(name)?.unsubscribe();\n      subscriptions.delete(name);\n      send({\n        type: `Remove ${displayName}`\n      });\n    }\n  });\n  const devtoolsDispose = instance.subscribe(message => {\n    if (message.type === 'DISPATCH') {\n      const payloadType = message.payload.type;\n      if (payloadType === 'COMMIT') {\n        instance.init(getStoresSnapshot());\n        return;\n      }\n      if (payloadType === 'JUMP_TO_STATE' || payloadType === 'JUMP_TO_ACTION') {\n        const state = JSON.parse(message.state);\n        for (const [name, value] of Object.entries(state)) {\n          lock = true;\n          getStore(name)?.update(() => value);\n        }\n        options.postTimelineUpdate?.();\n      }\n    }\n  });\n  return {\n    unsubscribe() {\n      subscription.unsubscribe();\n      instance.unsubscribe();\n      subscriptions.forEach(sub => sub.unsubscribe());\n      devtoolsDispose();\n    }\n  };\n}\nexport { devTools, send };"], "mappings": ";;;;;;;;;;;;;;;;AAGA,IAAM,kBAAkB,IAAI,QAAQ;AACpC,SAAS,KAAK,QAAQ;AACpB,kBAAgB,KAAK,MAAM;AAC7B;AACA,SAAS,SAAS,UAAU,CAAC,GAAG;AAC9B,MAAI,CAAC,OAAO,6BAA8B;AAC1C,MAAI,OAAO;AACX,QAAM,WAAW,OAAO,6BAA6B,QAAQ,OAAO;AACpE,QAAM,gBAAgB,oBAAI,IAAI;AAC9B,QAAMA,QAAO,YAAU;AACrB,aAAS,KAAK,QAAQ,kBAAkB,CAAC;AAAA,EAC3C;AACA,gBAAc,IAAI,gBAAgB,gBAAgB,UAAUA,KAAI,CAAC;AACjE,QAAM,WAAW,WAAS;AACxB,UAAM,OAAO,MAAM;AACnB,UAAM,cAAc,WAAW,IAAI;AACnC,IAAAA,MAAK;AAAA,MACH,MAAM,IAAI,WAAW;AAAA,IACvB,CAAC;AACD,UAAM,SAAS,MAAM,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACjD,UAAI,MAAM;AACR,eAAO;AACP;AAAA,MACF;AACA,cAAQ,YAAY;AACpB,MAAAA,MAAK;AAAA,QACH,MAAM,IAAI,WAAW;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AACD,kBAAc,IAAI,MAAM,MAAM;AAAA,EAChC;AAEA,cAAY,EAAE,QAAQ,QAAQ;AAC9B,MAAI,QAAQ,mBAAmB;AAC7B,kBAAc,IAAI,qBAAqB,QAAQ,kBAAkB,UAAU,YAAU;AACnF,MAAAA,MAAK,MAAM;AAAA,IACb,CAAC,CAAC;AAAA,EACJ;AACA,QAAM,eAAe,UAAU,UAAU,CAAC;AAAA,IACxC;AAAA,IACA;AAAA,EACF,MAAM;AACJ,UAAM,OAAO,MAAM;AACnB,UAAM,cAAc,WAAW,IAAI;AACnC,QAAI,QAAQ,UAAU;AACpB,YAAM,MAAM,IAAI,WAAW,OAAO,IAAI;AACtC,cAAQ,eAAe,GAAG;AAC1B,cAAQ,MAAM;AACd,cAAQ,SAAS;AAAA,IACnB;AACA,QAAI,SAAS,OAAO;AAClB,eAAS,KAAK;AAAA,IAChB;AACA,QAAI,SAAS,UAAU;AACrB,oBAAc,IAAI,IAAI,GAAG,YAAY;AACrC,oBAAc,OAAO,IAAI;AACzB,MAAAA,MAAK;AAAA,QACH,MAAM,UAAU,WAAW;AAAA,MAC7B,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,QAAM,kBAAkB,SAAS,UAAU,aAAW;AACpD,QAAI,QAAQ,SAAS,YAAY;AAC/B,YAAM,cAAc,QAAQ,QAAQ;AACpC,UAAI,gBAAgB,UAAU;AAC5B,iBAAS,KAAK,kBAAkB,CAAC;AACjC;AAAA,MACF;AACA,UAAI,gBAAgB,mBAAmB,gBAAgB,kBAAkB;AACvE,cAAM,QAAQ,KAAK,MAAM,QAAQ,KAAK;AACtC,mBAAW,CAAC,MAAM,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AACjD,iBAAO;AACP,mBAAS,IAAI,GAAG,OAAO,MAAM,KAAK;AAAA,QACpC;AACA,gBAAQ,qBAAqB;AAAA,MAC/B;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL,cAAc;AACZ,mBAAa,YAAY;AACzB,eAAS,YAAY;AACrB,oBAAc,QAAQ,SAAO,IAAI,YAAY,CAAC;AAC9C,sBAAgB;AAAA,IAClB;AAAA,EACF;AACF;", "names": ["send"]}