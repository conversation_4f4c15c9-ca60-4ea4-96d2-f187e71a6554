import { DbConnection } from './types';
import globalWriteQueue from '../utils/writeQueue';

export interface ILogCreateFields {
  type: number;
  content: string;
}

export interface ILogData extends ILogCreateFields {
  id: string;
  created_at: number;
}

export interface ILogDataAccess {
  create(data: ILogCreateFields): Promise<ILogData>;
  getLogs(): Promise<ILogData[]>;
}

export class LogDataAccess implements ILogDataAccess {
  db: DbConnection;
  constructor(db: DbConnection) {
    this.db = db;
  }
  async create(data: ILogCreateFields): Promise<ILogData> {
    const log = await (globalWriteQueue.enqueue(() =>
      this.db.log.create(data as any),
    ) as Promise<any>);
    return log.get({ plain: true });
  }

  async getLogs(): Promise<ILogData[]> {
    return this.db.log.findAll({ raw: true, order: [['id', 'DESC']] });
  }
}
