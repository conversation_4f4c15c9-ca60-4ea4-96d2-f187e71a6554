@use "mixins" as *;
:host {
  display: block;
  background: #333;
  height: 100%;
}

.container {
  @include flex-box(column);
  background: url('/assets/images/signin_bg.png') no-repeat center center;
  background-size: cover;
  height: 100%;

  nav {
    @include flex-box(row, space-between, center);
    width: 100%;
    height: 40px;
    padding: 30px 20px;
    color: #fff;
    border-bottom: 1px dashed #fff;
    .title {
      font-size: 18px;
    }
    .back {
      height: 40px;
      width: 120px;
      cursor: pointer;
      display: flex;
      span {
        font-size: 20px;
        color: #fff;
      }
    }
    &.mobile {
      display: none;
      .back {
        display: none;
      }
    }
  }
}
main {
  @include flex-box(column, center, center);
  flex: 1;
  width: 100%;
  height: 100vh;
  position: relative;

  .vedio-card {
    position: relative;
    background: #fff;
    border-radius: 16px;
    padding: 15px 0;
    max-width: 100%;
    max-height: calc(100vh - 60px);
    overflow: hidden;
    .hint {
      font-size: 18px;
      color: #333;
      margin-bottom: 10px;
      text-align: center;
      position: relative;
      > span {
        font-size: 28px;
      }
      .engine-info {
        position: absolute;
        top: 0px;
        right: 60px;
        font-size: 12px;
        color: #666;
        background: rgba(0, 0, 0, 0.05);
        padding: 2px 8px;
        border-radius: 12px;
        z-index: 150;
      }
      .settings {
        cursor: pointer;
        z-index: 200;
        color: #333;
        position: absolute;
        top: 0px;
        left: 15px;
      }
    }
    .video-container {
      position: relative;
      overflow: hidden;
      max-width: 100%;
      max-height: calc(100vh - 60px);
      margin: 0 auto;
      video,
      canvas {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
        height: 100%;
        width: 100%;
      }
      video {
        object-fit: contain;
        &.mirror {
          transform: scaleX(-1);
        }
      }
      img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 250px;
        height: 250px;
        z-index: 100;
        &.mask-sm {
          width: 240px;
          height: 240px;
        }
        &.mask-md {
          width: 320px;
          height: 320px;
        }
        &.mask-lg {
          width: 480px;
          height: 480px;
        }
      }
    }
    .img-wrap {
      @include flex-box(row, space-between, center);
    }
  }
}
.signin-status {
  z-index: 200;
  background: #fff;
  height: 100px;
  width: 300px;
  @include flex-box(row, space-between, center);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 16px;
  &.error {
    .status-content {
      color: #FF4D4F;
    }
    .status-icon {
      background: #FF4D4F;
      .icon-wrap {
        span {
          color: #FF4D4F;
        }
      }
    }
  }
  .status-content {
    flex: 1;
    text-align: center;
    font-size: 18px;
    color: #30A46C;
    padding: 8px;
    @include flex-box(column, center, center);
    .info {
      color: #333;
      font-size: 20px;
    }
  }
  .status-icon {
    @include flex-box(column, center, center);
    height: 100%;
    width: 100px;
    border-radius: 0 16px 16px 0;
    background: #30A46C;
    font-size: 30px;
    .icon-wrap {
      @include flex-box(row, center, center);
      height: 50px;
      width: 50px;
      background: #fff;
      padding: 5px;
      border-radius: 50%;
      span {
        color: #30A46C;

      }
    }
  }
}

.log-container {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 300px;
  max-height: 400px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  color: white;
  z-index: 1000;
  
  .log-header {
    padding: 8px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .expand-btn {
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
  }
  
  .log-content {
    padding: 8px;
    max-height: 350px;
    overflow-y: auto;
    scroll-behavior: smooth;
    &.shrink {
      display: none;
    }
    .log-item {
      font-size: 12px;
      margin-bottom: 4px;
      font-family: monospace;
      
      &.log {
        color: #8cc;
      }
      
      &.error {
        color: #f66;
      }
      
      &.warn {
        color: #fc6;
      }
    }
  }
}

// 超时遮罩层样式
.timeout-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  // background-color: rgba(0, 0, 0, 0.7);
  // backdrop-filter: blur(8px);
  background-color: #B8D7FF;
  z-index: 300;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .timeout-message {
    // padding: 20px 40px;
    // border-radius: 12px;
    text-align: center;

    span {
      font-size: 60px;
      font-weight: bold;
      color: #ED1417;
      letter-spacing: 40px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      &.no-spacing {
        letter-spacing: 0;
      }
    }
  }
}