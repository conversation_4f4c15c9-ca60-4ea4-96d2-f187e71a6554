import { Sequelize } from 'sequelize';
import { ICenterInfo } from '@services/center.service';
import * as env from '@server/config/env';
import { DbConnection } from '../types';

const db: DbConnection = {} as DbConnection;
let mysqlConnection: Sequelize;
const DB_KEY = 'SGr5KzzGBb87ScHT';

async function testConnection(sequelize: Sequelize) {
  try {
    await sequelize.authenticate();
    console.log('DB: Connection has been established successfully.');
    return true;
  } catch (error) {
    console.error('DB: Unable to connect to the database:', error);
    throw error;
  }
}

function loadModels(sequelize: Sequelize) {
  const modelDefiners = [
    require('../model/project'),
    require('../model/schedule'),
    require('../model/session'),
    require('../model/room'),
    require('../model/subject'),
    require('../model/form'),
    require('../model/event'),
    require('../model/log'),
  ];

  for (const modelDefiner of modelDefiners) {
    const model = modelDefiner.default(sequelize);
    db[model.name] = model;
  }
}

function newDb(db_name: string) {
  const sequelize = new Sequelize({
    database: db_name,
    host: '127.0.0.1',
    username: 'root',
    password: DB_KEY,
    port: 19103,
    dialect: 'mysql',
    logging: false,
    dialectModule: require('mysql2'),
  });
  return sequelize;
}

async function ensureMysqlConnection() {
  if (mysqlConnection) {
    return mysqlConnection;
  }
  mysqlConnection = newDb('');
  await testConnection(mysqlConnection);
  return mysqlConnection;
}

db.open = async function () {
  const center = env.getUserConfig<ICenterInfo>('center');
  if (!center?.is_active) {
    return false;
  }
  const db_name = `${center.center_id}_${center.project_id}`;
  if (db.sequelize) {
    if (db.db_name !== db_name) {
      console.log('DB: close db', db.db_name);
      await db.sequelize.close();
    } else {
      console.log('DB: db already open', db.db_name);
      return true;
    }
  }
  db.db_name = db_name;
  console.log('DB: open db', db_name);
  const mysql = await ensureMysqlConnection();

  await mysql.query(`CREATE DATABASE IF NOT EXISTS ${db_name}`);

  const sequelize = newDb(db_name);
  try {
    await sequelize.authenticate();
  } catch (error) {
    console.error('DB: Unable to connect to the database:', db_name, error);
    throw error;
  }
  db.sequelize = sequelize;
  loadModels(sequelize);

  return !!(await sequelize.sync());
};

db.close = async function () {
  if (this.sequelize) {
    await this.sequelize.close();
    return true;
  }
  return false;
};

export default db;
