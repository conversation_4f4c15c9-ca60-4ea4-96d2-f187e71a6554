import { ModalService } from './../../../core/services/modal.service';
import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  OnDestroy,
  inject,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription, interval, switchMap } from 'rxjs';

import { CustomDatePipe } from '../../pipes/custom-date.pipe';
import { ServerTimeService } from '../../../core/services/server-time.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzQRCodeModule } from 'ng-zorro-antd/qr-code';
import { NzInputModule } from 'ng-zorro-antd/input';
import { AuthEventHttpService } from '../../../core/services/auth-event/auth-event.http';
import {
  AuthEventService,
  EventRes,
} from '../../../core/services/auth-event/auth-event.service';
import { MessageService } from '@app/core/services/message.service';

export interface ISyncTimeModalData {
  schedule_id: string;
}
type QrStatus = 'active' | 'loading' | 'expired';
@Component({
    selector: 'sync-time-modal',
    imports: [
        CommonModule,
        FormsModule,
        CustomDatePipe,
        NzButtonModule,
        NzQRCodeModule,
        NzInputModule,
    ],
    templateUrl: './sync-time-modal.component.html',
    styleUrls: ['./sync-time-modal.component.scss']
})
export class SyncTimeModalComponent implements OnInit, OnDestroy {
  time: number;

  isQR = false;
  qrCode = 'wwww.test.com';
  // qr_status: QrStatus = 'active';
  authCode = '';

  authEvent: EventRes;
  subscription: Subscription;

  readonly #modal = inject(NzModalRef);
  readonly nzModalData: ISyncTimeModalData = inject(NZ_MODAL_DATA);

  constructor(
    private serverTime: ServerTimeService,
    private authEventHttp: AuthEventHttpService,
    private authEventService: AuthEventService,
    private modalService: ModalService,
    private msgService: MessageService,
    private cdRef: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.serverTime.getServerTime$().subscribe((time) => {
      this.time = time;
      this.cdRef.detectChanges();
    });
    this.subscription = interval(2000)
      .pipe(switchMap(() => this.serverTime.getServerTime()))
      .subscribe();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  confirm() {
    if (!this.isQR) {
      this.isQR = true;
      this.getQrcode();
    } else {
      this.authEventService
        .authCode(this.authEvent.event_id, +this.authCode)
        .subscribe((res) => {
          if (res.status === 'success') {
            if (!res.data) {
              this.modalService.error('错误', '验证码不正确！');
              this.serverTime.getServerTime().subscribe();
            } else {
              this.msgService.create('success', '校时成功');
              this.serverTime.getServerTime().subscribe();
              this.closeModal();
            }
          }
        });
    }
  }

  getQrcode() {
    this.authEventHttp
      .createAuthEvent('timing', {
        schedule_id: this.nzModalData.schedule_id,
      })
      .subscribe((res) => {
        this.authEvent = res.info;
        this.qrCode = res.info.data;
        // this.closeModal();
      });
  }

  closeModal() {
    this.#modal.close();
  }
}
