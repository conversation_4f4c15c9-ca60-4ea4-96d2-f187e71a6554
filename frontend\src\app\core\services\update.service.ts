import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Res } from '@share-types/center.types';
import { map } from 'rxjs';
import { ModalService } from './modal.service';

@Injectable({
  providedIn: 'root'
})
export class UpdateService {

  constructor(
    private http: HttpClient,
    private modalservice: ModalService
  ) { }


  checkAppVersion() {
    this.getLatestVersion().subscribe((res) => {
      const appVersion = window.joyshell?.AppVersion || "1.0";
      console.log("app latest version: ", res?.version, "local version:", appVersion);
      if (!window.joyshell) {
        return;
      }
      if (!res.version) {
        console.warn("no latest version");
        return;
      }
      if (appVersion !== res.version) {
        this.modalservice.create("warning", {
          title: "提示",
          content: "发现新版本！请立即更新",
          onOk: () => {
            if (joyshell) {
              joyshell.OpenUrl("https://joytest.org/home/<USER>/center");
            }
          }
        })
      }
    })
  }

  getLatestVersion() {
    return this.http.get<Res<{ version: string; pub_date: string }>>("/data/latest-version").pipe(
      map((res) => {
        if (res.status === "success") {
          return res.data;
        } else {
          console.error("get latest version failed:", res.error);
          return {} as { version: string; pub_date: string };
        }
      })
    );
  }
}
