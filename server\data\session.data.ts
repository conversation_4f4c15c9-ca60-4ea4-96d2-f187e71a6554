import { DbConnection } from './types';
import globalWriteQueue from '../utils/writeQueue';

export interface ISessionData {
  id: string;
  name: string;
  schedule_id: string;
  start: string;
  end: string;
  room_sn: string;
  room_name: string;
  test: 0 | 1;
  form_status: 0 | 1;
  password_status: 0 | 1;
  status: ESessionStatus;
  entry_status: IEntryStat;
  version: number;
}

export interface IEntryStat {
  not_entered: number;
  checked: number;
  testing: number;
  finished: number;
  absent: number;
}

export enum ESessionStatus {
  notStart = 0, // 未入场
  enter = 1, // 已入场
  test = 2, // 考试中
  pause = 3, // 暂停
  end = 4, // 已结束
  uploading = 5, // 正在上传
  uploaded = 6, // 已上传
  upfailed = 7, // 上传失败
  backupUploaded = 8, // 备份上传成功

  centerUploaded = 100, // 考点已上传
}

export interface ISessionDataAccess {
  getSessions(whereFields: Partial<ISessionData>): Promise<ISessionData[]>;
  get(whereFields: Partial<ISessionData>): Promise<ISessionData | null>;
  delete(id: string): Promise<void>;
  create(sessions: ISessionData): Promise<void>;
  update(
    whereFields: Partial<ISessionData>,
    session: Partial<ISessionData>,
  ): Promise<void>;
  destroy(whereFields: { id: string[] }): Promise<void>;
}

export class SessionDataAccess implements ISessionDataAccess {
  db: DbConnection;
  constructor(db: DbConnection) {
    this.db = db;
  }
  async getSessions(
    whereFields: Partial<ISessionData>,
  ): Promise<ISessionData[]> {
    const sessions = await this.db.session.findAll({
      where: whereFields as any,
      raw: true,
    });
    return sessions.map((session) => ({
      id: session.id,
      name: session.name,
      schedule_id: session.schedule_id,
      start: session.start,
      end: session.end,
      test: session.test,
      room_sn: session.room_sn,
      room_name: session.room_name,
      form_status: session.form_status,
      password_status: session.password_status,
      status: session.status,
      entry_status: JSON.parse(session.entry_status) as IEntryStat,
      version: session.version,
    }));
  }

  async get(whereFields: Partial<ISessionData>) {
    const session = await this.db.session.findOne({
      where: whereFields as any,
      raw: true,
    });
    return session as ISessionData | null;
  }

  async delete(id: string): Promise<void> {
    await this.db.session.destroy({
      where: {
        id,
      },
    });
  }

  async create(session: ISessionData): Promise<void> {
    await globalWriteQueue.enqueue(() =>
      this.db.session.create({
        ...session,
        entry_status: JSON.stringify(session.entry_status),
      }),
    );
  }

  async update(
    whereFields: Partial<ISessionData>,
    session: Partial<ISessionData>,
  ) {
    await globalWriteQueue.enqueue(() =>
      this.db.session.update(
        {
          ...session,
          entry_status:
            session.entry_status && JSON.stringify(session.entry_status),
        },
        {
          where: whereFields as any,
        },
      ),
    );
  }

  async destroy(whereFields: { id: string[] }): Promise<void> {
    await globalWriteQueue.enqueue(() =>
      this.db.session.destroy({
        where: whereFields,
      }),
    );
  }
}
