import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  type OnInit,
  EventEmitter,
  ViewContainerRef,
} from '@angular/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { ModalService } from '../../core/services/modal.service';

@Component({
    selector: 'app-upload-button',
    imports: [CommonModule, NzButtonModule, NzIconModule, NzUploadModule],
    template: `
    <button nz-button nzType="primary" (click)="openChooseFileModal()">
      <span nz-icon nzType="download"></span>
      导入试卷包/密码包
    </button>
  `,
    styles: [
        `
      :host {
        display: inline-block;
        margin: 0 5px;
      }
      input.file-input {
        position: absolute;
        z-index: -1;
        opacity: 0;
        filter: alpha(opacity=0);
        display: block;
        height: 0;
        width: 0;
      }

      .btn {
        height: 32px;
        background-color: #1890ff;
        padding: 4px 15px;
        font-size: 14px;
        color: #fff;
        display: inline-block;
        border-radius: 2px;
        cursor: pointer;
      }
    `,
    ]
})
export class UploadButtonComponent implements OnInit {
  fileToUpload: File | null = null;

  @Input({ required: true }) scheduleId: string = '';
  @Output() uploadSuccess: EventEmitter<any> = new EventEmitter<any>();
  constructor(
    private viewContainerRef: ViewContainerRef,
    private modalService: ModalService
  ) {}
  ngOnInit(): void {}

  openChooseFileModal() {
    const modal = this.modalService.chooseFileModal(this.viewContainerRef, {
      schedule_id: this.scheduleId,
    });
    modal.afterClose.subscribe((result) => {
      if (result?.success) {
        this.uploadSuccess.emit(result);
      }
    });
  }
}
