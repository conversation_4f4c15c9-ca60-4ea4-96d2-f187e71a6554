import { CommonModule } from '@angular/common';
import { Component, inject, type OnInit } from '@angular/core';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';

export interface ConfirmModalData {
  content: string;
}
@Component({
  selector: 'app-confirm-modal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './confirm-modal.component.html',
  styleUrls: ['./confirm-modal.component.scss'],
})
export class ConfirmModalComponent implements OnInit {
  ngOnInit(): void {}
  readonly #modalRef = inject(NzModalRef);
  readonly nzModalData = inject<ConfirmModalData>(NZ_MODAL_DATA);
}
