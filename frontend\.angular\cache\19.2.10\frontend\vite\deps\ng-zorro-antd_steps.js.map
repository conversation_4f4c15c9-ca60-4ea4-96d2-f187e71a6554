{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-steps.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { TemplateRef, booleanAttribute, Input, ViewChild, ViewEncapsulation, ChangeDetectionStrategy, Component, EventEmitter, Output, ContentChildren, NgModule } from '@angular/core';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { filter, takeUntil, startWith } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/services';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { fromEventOutsideAngular, toBoolean } from 'ng-zorro-antd/core/util';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from 'ng-zorro-antd/progress';\nimport { NzProgressModule } from 'ng-zorro-antd/progress';\nimport * as i1$1 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"processDotTemplate\"];\nconst _c1 = [\"itemContainer\"];\nconst _c2 = (a0, a1, a2) => ({\n  $implicit: a0,\n  status: a1,\n  index: a2\n});\nfunction NzStepComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 3);\n  }\n}\nfunction NzStepComponent_Conditional_4_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"nz-progress\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzPercent\", ctx_r0.nzPercentage)(\"nzWidth\", ctx_r0.nzSize === \"small\" ? 32 : 40)(\"nzFormat\", ctx_r0.nullProcessFormat)(\"nzStrokeWidth\", 4);\n  }\n}\nfunction NzStepComponent_Conditional_4_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵelement(1, \"nz-icon\", 13);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NzStepComponent_Conditional_4_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵelement(1, \"nz-icon\", 14);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NzStepComponent_Conditional_4_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.index + 1, \" \");\n  }\n}\nfunction NzStepComponent_Conditional_4_Conditional_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nz-icon\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const icon_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzType\", icon_r2);\n  }\n}\nfunction NzStepComponent_Conditional_4_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtemplate(1, NzStepComponent_Conditional_4_Conditional_4_ng_container_1_Template, 2, 1, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzIcon);\n  }\n}\nfunction NzStepComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzStepComponent_Conditional_4_Conditional_0_Template, 2, 4, \"div\", 11)(1, NzStepComponent_Conditional_4_Conditional_1_Template, 2, 0, \"span\", 5)(2, NzStepComponent_Conditional_4_Conditional_2_Template, 2, 0, \"span\", 5)(3, NzStepComponent_Conditional_4_Conditional_3_Template, 2, 1, \"span\", 5)(4, NzStepComponent_Conditional_4_Conditional_4_Template, 2, 1, \"span\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r0.showProgress ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.nzStatus === \"finish\" && !ctx_r0.nzIcon ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.nzStatus === \"error\" ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional((ctx_r0.nzStatus === \"process\" || ctx_r0.nzStatus === \"wait\") && !ctx_r0.nzIcon ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.nzIcon ? 4 : -1);\n  }\n}\nfunction NzStepComponent_Conditional_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n  }\n}\nfunction NzStepComponent_Conditional_5_ng_template_3_Template(rf, ctx) {}\nfunction NzStepComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtemplate(1, NzStepComponent_Conditional_5_ng_template_1_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(3, NzStepComponent_Conditional_5_ng_template_3_Template, 0, 0, \"ng-template\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processDotTemplate_r3 = i0.ɵɵreference(2);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.customProcessTemplate || processDotTemplate_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(2, _c2, processDotTemplate_r3, ctx_r0.nzStatus, ctx_r0.index));\n  }\n}\nfunction NzStepComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzTitle);\n  }\n}\nfunction NzStepComponent_Conditional_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzSubtitle);\n  }\n}\nfunction NzStepComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, NzStepComponent_Conditional_9_ng_container_1_Template, 2, 1, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzSubtitle);\n  }\n}\nfunction NzStepComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzDescription);\n  }\n}\nconst _c3 = [\"*\"];\nclass NzStepComponent {\n  cdr;\n  destroy$;\n  processDotTemplate;\n  itemContainer;\n  nzTitle;\n  nzSubtitle;\n  nzDescription;\n  nzDisabled = false;\n  nzPercentage = null;\n  nzSize = 'default';\n  get nzStatus() {\n    return this._status;\n  }\n  set nzStatus(status) {\n    this._status = status;\n    this.isCustomStatus = true;\n  }\n  isCustomStatus = false;\n  _status = 'wait';\n  get nzIcon() {\n    return this._icon;\n  }\n  set nzIcon(value) {\n    if (!(value instanceof TemplateRef)) {\n      this.oldAPIIcon = typeof value === 'string' && value.indexOf('anticon') > -1;\n    }\n    this._icon = value;\n  }\n  oldAPIIcon = true;\n  _icon;\n  customProcessTemplate; // Set by parent.\n  direction = 'horizontal';\n  index = 0;\n  last = false;\n  outStatus = 'process';\n  showProcessDot = false;\n  clickable = false;\n  clickOutsideAngular$ = new Subject();\n  nullProcessFormat = () => null;\n  get showProgress() {\n    return this.nzPercentage !== null && !this.nzIcon && this.nzStatus === 'process' && this.nzPercentage >= 0 && this.nzPercentage <= 100;\n  }\n  get currentIndex() {\n    return this._currentIndex;\n  }\n  set currentIndex(current) {\n    this._currentIndex = current;\n    if (!this.isCustomStatus) {\n      this._status = current > this.index ? 'finish' : current === this.index ? this.outStatus || '' : 'wait';\n    }\n  }\n  _currentIndex = 0;\n  constructor(cdr, destroy$) {\n    this.cdr = cdr;\n    this.destroy$ = destroy$;\n  }\n  ngOnInit() {\n    fromEventOutsideAngular(this.itemContainer.nativeElement, 'click').pipe(filter(() => this.clickable && this.currentIndex !== this.index && !this.nzDisabled), takeUntil(this.destroy$)).subscribe(() => {\n      this.clickOutsideAngular$.next(this.index);\n    });\n  }\n  enable() {\n    this.nzDisabled = false;\n    this.cdr.markForCheck();\n  }\n  disable() {\n    this.nzDisabled = true;\n    this.cdr.markForCheck();\n  }\n  markForCheck() {\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function NzStepComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzStepComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NzDestroyService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzStepComponent,\n    selectors: [[\"nz-step\"]],\n    viewQuery: function NzStepComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.processDotTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemContainer = _t.first);\n      }\n    },\n    hostAttrs: [1, \"ant-steps-item\"],\n    hostVars: 16,\n    hostBindings: function NzStepComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-steps-item-wait\", ctx.nzStatus === \"wait\")(\"ant-steps-item-process\", ctx.nzStatus === \"process\")(\"ant-steps-item-finish\", ctx.nzStatus === \"finish\")(\"ant-steps-item-error\", ctx.nzStatus === \"error\")(\"ant-steps-item-active\", ctx.currentIndex === ctx.index)(\"ant-steps-item-disabled\", ctx.nzDisabled)(\"ant-steps-item-custom\", !!ctx.nzIcon)(\"ant-steps-next-error\", ctx.outStatus === \"error\" && ctx.currentIndex === ctx.index + 1);\n      }\n    },\n    inputs: {\n      nzTitle: \"nzTitle\",\n      nzSubtitle: \"nzSubtitle\",\n      nzDescription: \"nzDescription\",\n      nzDisabled: [2, \"nzDisabled\", \"nzDisabled\", booleanAttribute],\n      nzPercentage: \"nzPercentage\",\n      nzSize: \"nzSize\",\n      nzStatus: \"nzStatus\",\n      nzIcon: \"nzIcon\"\n    },\n    exportAs: [\"nzStep\"],\n    features: [i0.ɵɵProvidersFeature([NzDestroyService])],\n    decls: 12,\n    vars: 8,\n    consts: [[\"itemContainer\", \"\"], [\"processDotTemplate\", \"\"], [1, \"ant-steps-item-container\", 3, \"tabindex\"], [1, \"ant-steps-item-tail\"], [1, \"ant-steps-item-icon\"], [1, \"ant-steps-icon\"], [1, \"ant-steps-item-content\"], [1, \"ant-steps-item-title\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-steps-item-subtitle\"], [1, \"ant-steps-item-description\"], [1, \"ant-steps-progress-icon\"], [\"nzType\", \"circle\", 3, \"nzPercent\", \"nzWidth\", \"nzFormat\", \"nzStrokeWidth\"], [\"nzType\", \"check\"], [\"nzType\", \"close\"], [3, \"nzType\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"ant-steps-icon-dot\"]],\n    template: function NzStepComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 2, 0);\n        i0.ɵɵtemplate(2, NzStepComponent_Conditional_2_Template, 1, 0, \"div\", 3);\n        i0.ɵɵelementStart(3, \"div\", 4);\n        i0.ɵɵtemplate(4, NzStepComponent_Conditional_4_Template, 5, 5)(5, NzStepComponent_Conditional_5_Template, 4, 6, \"span\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7);\n        i0.ɵɵtemplate(8, NzStepComponent_ng_container_8_Template, 2, 1, \"ng-container\", 8)(9, NzStepComponent_Conditional_9_Template, 2, 1, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 10);\n        i0.ɵɵtemplate(11, NzStepComponent_ng_container_11_Template, 2, 1, \"ng-container\", 8);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"tabindex\", ctx.clickable && !ctx.nzDisabled ? 0 : null);\n        i0.ɵɵattribute(\"role\", ctx.clickable && !ctx.nzDisabled ? \"button\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(!ctx.last ? 2 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(!ctx.showProcessDot ? 4 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.showProcessDot ? 5 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzTitle);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.nzSubtitle ? 9 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzDescription);\n      }\n    },\n    dependencies: [NzProgressModule, i2.NzProgressComponent, NzIconModule, i3.NzIconDirective, NzOutletModule, i4.NzStringTemplateOutletDirective, NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzStepComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-step',\n      exportAs: 'nzStep',\n      preserveWhitespaces: false,\n      template: `\n    <div\n      #itemContainer\n      class=\"ant-steps-item-container\"\n      [attr.role]=\"clickable && !nzDisabled ? 'button' : null\"\n      [tabindex]=\"clickable && !nzDisabled ? 0 : null\"\n    >\n      @if (!last) {\n        <div class=\"ant-steps-item-tail\"></div>\n      }\n      <div class=\"ant-steps-item-icon\">\n        @if (!showProcessDot) {\n          @if (showProgress) {\n            <div class=\"ant-steps-progress-icon\">\n              <nz-progress\n                [nzPercent]=\"nzPercentage\"\n                nzType=\"circle\"\n                [nzWidth]=\"nzSize === 'small' ? 32 : 40\"\n                [nzFormat]=\"nullProcessFormat\"\n                [nzStrokeWidth]=\"4\"\n              ></nz-progress>\n            </div>\n          }\n          @if (nzStatus === 'finish' && !nzIcon) {\n            <span class=\"ant-steps-icon\"><nz-icon nzType=\"check\" /></span>\n          }\n          @if (nzStatus === 'error') {\n            <span class=\"ant-steps-icon\"><nz-icon nzType=\"close\" /></span>\n          }\n          @if ((nzStatus === 'process' || nzStatus === 'wait') && !nzIcon) {\n            <span class=\"ant-steps-icon\">\n              {{ index + 1 }}\n            </span>\n          }\n          @if (nzIcon) {\n            <span class=\"ant-steps-icon\">\n              <ng-container *nzStringTemplateOutlet=\"nzIcon; let icon\">\n                <nz-icon [nzType]=\"icon\" />\n              </ng-container>\n            </span>\n          }\n        }\n        @if (showProcessDot) {\n          <span class=\"ant-steps-icon\">\n            <ng-template #processDotTemplate>\n              <span class=\"ant-steps-icon-dot\"></span>\n            </ng-template>\n            <ng-template\n              [ngTemplateOutlet]=\"customProcessTemplate || processDotTemplate\"\n              [ngTemplateOutletContext]=\"{\n                $implicit: processDotTemplate,\n                status: nzStatus,\n                index: index\n              }\"\n            ></ng-template>\n          </span>\n        }\n      </div>\n      <div class=\"ant-steps-item-content\">\n        <div class=\"ant-steps-item-title\">\n          <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n          @if (nzSubtitle) {\n            <div class=\"ant-steps-item-subtitle\">\n              <ng-container *nzStringTemplateOutlet=\"nzSubtitle\">{{ nzSubtitle }}</ng-container>\n            </div>\n          }\n        </div>\n        <div class=\"ant-steps-item-description\">\n          <ng-container *nzStringTemplateOutlet=\"nzDescription\">{{ nzDescription }}</ng-container>\n        </div>\n      </div>\n    </div>\n  `,\n      host: {\n        class: 'ant-steps-item',\n        '[class.ant-steps-item-wait]': 'nzStatus === \"wait\"',\n        '[class.ant-steps-item-process]': 'nzStatus === \"process\"',\n        '[class.ant-steps-item-finish]': 'nzStatus === \"finish\"',\n        '[class.ant-steps-item-error]': 'nzStatus === \"error\"',\n        '[class.ant-steps-item-active]': 'currentIndex === index',\n        '[class.ant-steps-item-disabled]': 'nzDisabled',\n        '[class.ant-steps-item-custom]': '!!nzIcon',\n        '[class.ant-steps-next-error]': '(outStatus === \"error\") && (currentIndex === index + 1)'\n      },\n      providers: [NzDestroyService],\n      imports: [NzProgressModule, NzIconModule, NzOutletModule, NgTemplateOutlet]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.NzDestroyService\n  }], {\n    processDotTemplate: [{\n      type: ViewChild,\n      args: ['processDotTemplate', {\n        static: false\n      }]\n    }],\n    itemContainer: [{\n      type: ViewChild,\n      args: ['itemContainer', {\n        static: true\n      }]\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzSubtitle: [{\n      type: Input\n    }],\n    nzDescription: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzPercentage: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzIcon: [{\n      type: Input\n    }]\n  });\n})();\nclass NzStepsComponent {\n  ngZone;\n  cdr;\n  directionality;\n  destroy$;\n  static ngAcceptInputType_nzProgressDot;\n  steps;\n  nzCurrent = 0;\n  nzDirection = 'horizontal';\n  nzLabelPlacement = 'horizontal';\n  nzType = 'default';\n  nzSize = 'default';\n  nzStartIndex = 0;\n  nzStatus = 'process';\n  set nzProgressDot(value) {\n    if (value instanceof TemplateRef) {\n      this.showProcessDot = true;\n      this.customProcessDotTemplate = value;\n    } else {\n      this.showProcessDot = toBoolean(value);\n    }\n    this.updateChildrenSteps();\n  }\n  nzIndexChange = new EventEmitter();\n  indexChangeSubscription = Subscription.EMPTY;\n  showProcessDot = false;\n  showProgress = false;\n  customProcessDotTemplate;\n  dir = 'ltr';\n  constructor(ngZone, cdr, directionality, destroy$) {\n    this.ngZone = ngZone;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.destroy$ = destroy$;\n  }\n  ngOnChanges(changes) {\n    if (changes.nzStartIndex || changes.nzDirection || changes.nzStatus || changes.nzCurrent || changes.nzSize) {\n      this.updateChildrenSteps();\n    }\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.updateChildrenSteps();\n  }\n  ngAfterContentInit() {\n    if (this.steps) {\n      this.steps.changes.pipe(startWith(null), takeUntil(this.destroy$)).subscribe(() => {\n        this.updateHostProgressClass();\n        this.updateChildrenSteps();\n      });\n    }\n  }\n  updateHostProgressClass() {\n    if (this.steps && !this.showProcessDot) {\n      this.showProgress = !!this.steps.toArray().find(step => step.nzPercentage !== null);\n    }\n  }\n  updateChildrenSteps() {\n    if (this.steps) {\n      const length = this.steps.length;\n      this.steps.toArray().forEach((step, index) => {\n        Promise.resolve().then(() => {\n          step.nzSize = this.nzSize;\n          step.outStatus = this.nzStatus;\n          step.showProcessDot = this.showProcessDot;\n          if (this.customProcessDotTemplate) {\n            step.customProcessTemplate = this.customProcessDotTemplate;\n          }\n          step.clickable = this.nzIndexChange.observers.length > 0;\n          step.direction = this.nzDirection;\n          step.index = index + this.nzStartIndex;\n          step.currentIndex = this.nzCurrent;\n          step.last = length === index + 1;\n          step.markForCheck();\n        });\n      });\n      this.indexChangeSubscription.unsubscribe();\n      this.indexChangeSubscription = merge(...this.steps.map(step => step.clickOutsideAngular$)).pipe(takeUntil(this.destroy$)).subscribe(index => {\n        if (this.nzIndexChange.observers.length) {\n          this.ngZone.run(() => this.nzIndexChange.emit(index));\n        }\n      });\n    }\n  }\n  static ɵfac = function NzStepsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzStepsComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.Directionality), i0.ɵɵdirectiveInject(i1.NzDestroyService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzStepsComponent,\n    selectors: [[\"nz-steps\"]],\n    contentQueries: function NzStepsComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, NzStepComponent, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.steps = _t);\n      }\n    },\n    hostAttrs: [1, \"ant-steps\"],\n    hostVars: 18,\n    hostBindings: function NzStepsComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-steps-horizontal\", ctx.nzDirection === \"horizontal\")(\"ant-steps-vertical\", ctx.nzDirection === \"vertical\")(\"ant-steps-label-horizontal\", ctx.nzDirection === \"horizontal\")(\"ant-steps-label-vertical\", (ctx.showProcessDot || ctx.nzLabelPlacement === \"vertical\") && ctx.nzDirection === \"horizontal\")(\"ant-steps-dot\", ctx.showProcessDot)(\"ant-steps-small\", ctx.nzSize === \"small\")(\"ant-steps-navigation\", ctx.nzType === \"navigation\")(\"ant-steps-rtl\", ctx.dir === \"rtl\")(\"ant-steps-with-progress\", ctx.showProgress);\n      }\n    },\n    inputs: {\n      nzCurrent: \"nzCurrent\",\n      nzDirection: \"nzDirection\",\n      nzLabelPlacement: \"nzLabelPlacement\",\n      nzType: \"nzType\",\n      nzSize: \"nzSize\",\n      nzStartIndex: \"nzStartIndex\",\n      nzStatus: \"nzStatus\",\n      nzProgressDot: \"nzProgressDot\"\n    },\n    outputs: {\n      nzIndexChange: \"nzIndexChange\"\n    },\n    exportAs: [\"nzSteps\"],\n    features: [i0.ɵɵProvidersFeature([NzDestroyService]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c3,\n    decls: 1,\n    vars: 0,\n    template: function NzStepsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzStepsComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      preserveWhitespaces: false,\n      selector: 'nz-steps',\n      exportAs: 'nzSteps',\n      template: `<ng-content></ng-content>`,\n      host: {\n        class: 'ant-steps',\n        '[class.ant-steps-horizontal]': `nzDirection === 'horizontal'`,\n        '[class.ant-steps-vertical]': `nzDirection === 'vertical'`,\n        '[class.ant-steps-label-horizontal]': `nzDirection === 'horizontal'`,\n        '[class.ant-steps-label-vertical]': `(showProcessDot || nzLabelPlacement === 'vertical') && nzDirection === 'horizontal'`,\n        '[class.ant-steps-dot]': 'showProcessDot',\n        '[class.ant-steps-small]': `nzSize === 'small'`,\n        '[class.ant-steps-navigation]': `nzType === 'navigation'`,\n        '[class.ant-steps-rtl]': `dir === 'rtl'`,\n        '[class.ant-steps-with-progress]': 'showProgress'\n      },\n      providers: [NzDestroyService]\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$1.Directionality\n  }, {\n    type: i1.NzDestroyService\n  }], {\n    steps: [{\n      type: ContentChildren,\n      args: [NzStepComponent]\n    }],\n    nzCurrent: [{\n      type: Input\n    }],\n    nzDirection: [{\n      type: Input\n    }],\n    nzLabelPlacement: [{\n      type: Input\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzStartIndex: [{\n      type: Input\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzProgressDot: [{\n      type: Input\n    }],\n    nzIndexChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzStepsModule {\n  static ɵfac = function NzStepsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzStepsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzStepsModule,\n    imports: [NzStepsComponent, NzStepComponent],\n    exports: [NzStepsComponent, NzStepComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzStepComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzStepsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzStepsComponent, NzStepComponent],\n      exports: [NzStepsComponent, NzStepComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzStepComponent, NzStepsComponent, NzStepsModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAM,MAAM,CAAC,oBAAoB;AACjC,IAAM,MAAM,CAAC,eAAe;AAC5B,IAAM,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AACT;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,eAAe,EAAE;AACjC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,YAAY,EAAE,WAAW,OAAO,WAAW,UAAU,KAAK,EAAE,EAAE,YAAY,OAAO,iBAAiB,EAAE,iBAAiB,CAAC;AAAA,EAC1J;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,UAAU,GAAG,WAAW,EAAE;AAC7B,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,UAAU,GAAG,WAAW,EAAE;AAC7B,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,QAAQ,GAAG,GAAG;AAAA,EAClD;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,WAAW,EAAE;AAC7B,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,OAAO;AAAA,EACjC;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,qEAAqE,GAAG,GAAG,gBAAgB,CAAC;AAC7G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,MAAM;AAAA,EACvD;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,sDAAsD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,sDAAsD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,sDAAsD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,sDAAsD,GAAG,GAAG,QAAQ,CAAC;AAAA,EAChY;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,eAAe,IAAI,EAAE;AAC7C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,aAAa,YAAY,CAAC,OAAO,SAAS,IAAI,EAAE;AACxE,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,aAAa,UAAU,IAAI,EAAE;AACrD,IAAG,UAAU;AACb,IAAG,eAAe,OAAO,aAAa,aAAa,OAAO,aAAa,WAAW,CAAC,OAAO,SAAS,IAAI,EAAE;AACzG,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,SAAS,IAAI,EAAE;AAAA,EACzC;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AAAC;AACxE,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,sDAAsD,GAAG,GAAG,eAAe,EAAE;AAChN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,wBAA2B,YAAY,CAAC;AAC9C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,yBAAyB,qBAAqB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,uBAAuB,OAAO,UAAU,OAAO,KAAK,CAAC;AAAA,EACtM;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,UAAU;AAAA,EACxC;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,gBAAgB,CAAC;AAC/F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,UAAU;AAAA,EAC3D;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,aAAa;AAAA,EAC3C;AACF;AACA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,eAAe;AAAA,EACf,SAAS;AAAA,EACT,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,QAAQ;AACnB,SAAK,UAAU;AACf,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,QAAI,EAAE,iBAAiB,cAAc;AACnC,WAAK,aAAa,OAAO,UAAU,YAAY,MAAM,QAAQ,SAAS,IAAI;AAAA,IAC5E;AACA,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA;AAAA;AAAA,EACA,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,uBAAuB,IAAI,QAAQ;AAAA,EACnC,oBAAoB,MAAM;AAAA,EAC1B,IAAI,eAAe;AACjB,WAAO,KAAK,iBAAiB,QAAQ,CAAC,KAAK,UAAU,KAAK,aAAa,aAAa,KAAK,gBAAgB,KAAK,KAAK,gBAAgB;AAAA,EACrI;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa,SAAS;AACxB,SAAK,gBAAgB;AACrB,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,UAAU,UAAU,KAAK,QAAQ,WAAW,YAAY,KAAK,QAAQ,KAAK,aAAa,KAAK;AAAA,IACnG;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,EAChB,YAAY,KAAK,UAAU;AACzB,SAAK,MAAM;AACX,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AACT,4BAAwB,KAAK,cAAc,eAAe,OAAO,EAAE,KAAK,OAAO,MAAM,KAAK,aAAa,KAAK,iBAAiB,KAAK,SAAS,CAAC,KAAK,UAAU,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACtM,WAAK,qBAAqB,KAAK,KAAK,KAAK;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AACP,SAAK,aAAa;AAClB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,UAAU;AACR,SAAK,aAAa;AAClB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,eAAe;AACb,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,EACzI;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,CAAC;AAAA,IACvB,WAAW,SAAS,sBAAsB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,MACtE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,gBAAgB;AAAA,IAC/B,UAAU;AAAA,IACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,uBAAuB,IAAI,aAAa,MAAM,EAAE,0BAA0B,IAAI,aAAa,SAAS,EAAE,yBAAyB,IAAI,aAAa,QAAQ,EAAE,wBAAwB,IAAI,aAAa,OAAO,EAAE,yBAAyB,IAAI,iBAAiB,IAAI,KAAK,EAAE,2BAA2B,IAAI,UAAU,EAAE,yBAAyB,CAAC,CAAC,IAAI,MAAM,EAAE,wBAAwB,IAAI,cAAc,WAAW,IAAI,iBAAiB,IAAI,QAAQ,CAAC;AAAA,MAC/b;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,QAAQ;AAAA,IACnB,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AAAA,IACpD,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,GAAG,4BAA4B,GAAG,UAAU,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,4BAA4B,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,UAAU,UAAU,GAAG,aAAa,WAAW,YAAY,eAAe,GAAG,CAAC,UAAU,OAAO,GAAG,CAAC,UAAU,OAAO,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,oBAAoB,CAAC;AAAA,IAC7kB,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,OAAO,CAAC;AACvE,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,wCAAwC,GAAG,CAAC,EAAE,GAAG,wCAAwC,GAAG,GAAG,QAAQ,CAAC;AACzH,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,QAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,wCAAwC,GAAG,GAAG,OAAO,CAAC;AAC5I,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,OAAO,EAAE;AAC/B,QAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,gBAAgB,CAAC;AACnF,QAAG,aAAa,EAAE,EAAE;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,YAAY,IAAI,aAAa,CAAC,IAAI,aAAa,IAAI,IAAI;AACrE,QAAG,YAAY,QAAQ,IAAI,aAAa,CAAC,IAAI,aAAa,WAAW,IAAI;AACzE,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,OAAO,IAAI,EAAE;AACnC,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,iBAAiB,IAAI,EAAE;AAC7C,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,iBAAiB,IAAI,EAAE;AAC5C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,0BAA0B,IAAI,OAAO;AACnD,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,aAAa,IAAI,EAAE;AACxC,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,0BAA0B,IAAI,aAAa;AAAA,MAC3D;AAAA,IACF;AAAA,IACA,cAAc,CAAC,kBAAqB,qBAAqB,cAAiB,iBAAiB,gBAAmB,iCAAiC,gBAAgB;AAAA,IAC/J,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAyEV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,+BAA+B;AAAA,QAC/B,kCAAkC;AAAA,QAClC,iCAAiC;AAAA,QACjC,gCAAgC;AAAA,QAChC,iCAAiC;AAAA,QACjC,mCAAmC;AAAA,QACnC,iCAAiC;AAAA,QACjC,gCAAgC;AAAA,MAClC;AAAA,MACA,WAAW,CAAC,gBAAgB;AAAA,MAC5B,SAAS,CAAC,kBAAkB,cAAc,gBAAgB,gBAAgB;AAAA,IAC5E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,QAC3B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,eAAe;AAAA,EACf,WAAW;AAAA,EACX,IAAI,cAAc,OAAO;AACvB,QAAI,iBAAiB,aAAa;AAChC,WAAK,iBAAiB;AACtB,WAAK,2BAA2B;AAAA,IAClC,OAAO;AACL,WAAK,iBAAiB,UAAU,KAAK;AAAA,IACvC;AACA,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,gBAAgB,IAAI,aAAa;AAAA,EACjC,0BAA0B,aAAa;AAAA,EACvC,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf;AAAA,EACA,MAAM;AAAA,EACN,YAAY,QAAQ,KAAK,gBAAgB,UAAU;AACjD,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,gBAAgB,QAAQ,eAAe,QAAQ,YAAY,QAAQ,aAAa,QAAQ,QAAQ;AAC1G,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,OAAO;AACd,WAAK,MAAM,QAAQ,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACjF,aAAK,wBAAwB;AAC7B,aAAK,oBAAoB;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,QAAI,KAAK,SAAS,CAAC,KAAK,gBAAgB;AACtC,WAAK,eAAe,CAAC,CAAC,KAAK,MAAM,QAAQ,EAAE,KAAK,UAAQ,KAAK,iBAAiB,IAAI;AAAA,IACpF;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,OAAO;AACd,YAAM,SAAS,KAAK,MAAM;AAC1B,WAAK,MAAM,QAAQ,EAAE,QAAQ,CAAC,MAAM,UAAU;AAC5C,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,eAAK,SAAS,KAAK;AACnB,eAAK,YAAY,KAAK;AACtB,eAAK,iBAAiB,KAAK;AAC3B,cAAI,KAAK,0BAA0B;AACjC,iBAAK,wBAAwB,KAAK;AAAA,UACpC;AACA,eAAK,YAAY,KAAK,cAAc,UAAU,SAAS;AACvD,eAAK,YAAY,KAAK;AACtB,eAAK,QAAQ,QAAQ,KAAK;AAC1B,eAAK,eAAe,KAAK;AACzB,eAAK,OAAO,WAAW,QAAQ;AAC/B,eAAK,aAAa;AAAA,QACpB,CAAC;AAAA,MACH,CAAC;AACD,WAAK,wBAAwB,YAAY;AACzC,WAAK,0BAA0B,MAAM,GAAG,KAAK,MAAM,IAAI,UAAQ,KAAK,oBAAoB,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC3I,YAAI,KAAK,cAAc,UAAU,QAAQ;AACvC,eAAK,OAAO,IAAI,MAAM,KAAK,cAAc,KAAK,KAAK,CAAC;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,GAAM,kBAAuB,cAAc,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,EACtN;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAAA,MAChD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ;AAAA,MAC3D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,wBAAwB,IAAI,gBAAgB,YAAY,EAAE,sBAAsB,IAAI,gBAAgB,UAAU,EAAE,8BAA8B,IAAI,gBAAgB,YAAY,EAAE,6BAA6B,IAAI,kBAAkB,IAAI,qBAAqB,eAAe,IAAI,gBAAgB,YAAY,EAAE,iBAAiB,IAAI,cAAc,EAAE,mBAAmB,IAAI,WAAW,OAAO,EAAE,wBAAwB,IAAI,WAAW,YAAY,EAAE,iBAAiB,IAAI,QAAQ,KAAK,EAAE,2BAA2B,IAAI,YAAY;AAAA,MAClhB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,UAAU;AAAA,MACV,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAC,SAAS;AAAA,IACpB,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,oBAAoB;AAAA,IAC7E,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,gCAAgC;AAAA,QAChC,8BAA8B;AAAA,QAC9B,sCAAsC;AAAA,QACtC,oCAAoC;AAAA,QACpC,yBAAyB;AAAA,QACzB,2BAA2B;AAAA,QAC3B,gCAAgC;AAAA,QAChC,yBAAyB;AAAA,QACzB,mCAAmC;AAAA,MACrC;AAAA,MACA,WAAW,CAAC,gBAAgB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,kBAAkB,eAAe;AAAA,IAC3C,SAAS,CAAC,kBAAkB,eAAe;AAAA,EAC7C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,eAAe;AAAA,MAC3C,SAAS,CAAC,kBAAkB,eAAe;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}