import { DbConnection } from './types';
import globalWriteQueue from '../utils/writeQueue';

export interface ISubjectData {
  subject_id: string;
  session_id: string;
  name: string;
  code: string;
}

export interface ISubjectDataAccess {
  getScheduleSubjects(schedule_id: string): Promise<ISubjectData[]>;
  getSessionSubjects(session_id: string): Promise<ISubjectData[]>;
  getAllSubjects(): Promise<ISubjectData[]>;
  create(subject: ISubjectData): Promise<void>;
  update(
    whereFields: { session_id: string; subject_id: string },
    subject: ISubjectData,
  ): Promise<void>;
  bulkCreate(subjects: ISubjectData[]): Promise<void>;
  bulkUpdate(subjects: ISubjectData[]): Promise<void>;
  bulkCreateOrUpdate(subjects: ISubjectData[]): Promise<void>;
  delete(subject_ids: string[]): Promise<void>;
}

export class SubjectDataAccess implements ISubjectDataAccess {
  db: DbConnection;
  constructor(db: DbConnection) {
    this.db = db;
  }

  async getScheduleSubjects(schedule_id: string): Promise<ISubjectData[]> {
    const sessions = await this.db.session.findAll({
      where: { schedule_id },
      raw: true,
    });
    const subjects = await this.db.subject.findAll({ raw: true });
    const schedule_subjects = subjects.reduce((s, subject) => {
      if (sessions.find((s) => s.id === subject.session_id)) {
        s.push(subject as ISubjectData);
      }
      return s;
    }, [] as ISubjectData[]);
    return schedule_subjects;
  }

  async getSessionSubjects(session_id: string) {
    const subjects = await this.db.subject.findAll({
      where: { session_id },
      raw: true,
    });
    return subjects.map((subject) => {
      return {
        subject_id: subject.subject_id,
        session_id: subject.session_id,
        name: subject.name,
        code: subject.code,
      };
    });
  }

  async getAllSubjects() {
    const subjects = await this.db.subject.findAll({ raw: true });
    return subjects.map((subject) => {
      return {
        subject_id: subject.subject_id,
        session_id: subject.session_id,
        name: subject.name,
        code: subject.code,
      };
    });
  }

  async create(subject: ISubjectData): Promise<void> {
    await globalWriteQueue.enqueue(() => this.db.subject.create(subject));
  }

  async update(
    whereFields: { session_id: string; subject_id: string },
    subject: ISubjectData,
  ) {
    await globalWriteQueue.enqueue(() =>
      this.db.subject.update(subject, {
        where: whereFields,
      }),
    );
  }

  async bulkCreate(subjects: ISubjectData[]) {
    await globalWriteQueue.enqueue(() => this.db.subject.bulkCreate(subjects));
  }

  async bulkUpdate(subjects: ISubjectData[]) {
    await globalWriteQueue.enqueue(async () => {
      for (const subject of subjects) {
        await this.db.subject.update(subject, {
          where: {
            session_id: subject.session_id,
            subject_id: subject.subject_id,
          },
        });
      }
    });
  }

  async bulkCreateOrUpdate(subjects: ISubjectData[]) {
    await globalWriteQueue.enqueue(() =>
      this.db.subject.bulkCreate(subjects, {
        updateOnDuplicate: ['name', 'code'],
      }),
    );
  }

  async delete(subject_ids: string[]): Promise<void> {
    await globalWriteQueue.enqueue(() =>
      this.db.subject.destroy({ where: { subject_id: subject_ids } }),
    );
  }
}
