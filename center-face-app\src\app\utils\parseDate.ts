export function parseDateTime(date: Date) {
  const YYYY = date.getFullYear();
  const M = date.getMonth() + 1;
  const MM = ('0' + M).slice(-2);
  const D = date.getDate();
  const DD = ('0' + D).slice(-2);
  const H = date.getHours();
  const F = date.getMinutes();
  const S = date.getSeconds();
  const HH = ('0' + H).slice(-2);
  const FF = ('0' + F).slice(-2);
  const SS = ('0' + S).slice(-2);
  return { YYYY, MM, DD, HH, FF, SS };
}
