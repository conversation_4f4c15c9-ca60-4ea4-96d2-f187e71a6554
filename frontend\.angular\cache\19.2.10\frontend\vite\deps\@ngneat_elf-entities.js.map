{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@ngneat+elf-entities@5.0.1_@ngneat+elf@2.5.1_rxjs@7.8.1__rxjs@7.8.1/node_modules/@ngneat/elf-entities/index.esm.js"], "sourcesContent": ["import { isFunction, capitalize, coerceArray, isDev, select, isUndefined, distinctUntilArrayItemChanged, propsFactory, propsArrayFactory } from '@ngneat/elf';\nimport { pipe } from 'rxjs';\nimport { map, distinctUntilChanged, switchMap } from 'rxjs/operators';\nfunction buildEntities(entities, idKey) {\n  const asObject = {};\n  const ids = [];\n  for (const entity of entities) {\n    const id = entity[idKey];\n    ids.push(id);\n    asObject[id] = entity;\n  }\n  return {\n    ids,\n    asObject\n  };\n}\nfunction findIdsByPredicate(state, ref, predicate) {\n  const {\n    idsKey,\n    entitiesKey\n  } = ref;\n  const entities = state[entitiesKey];\n  return state[idsKey].filter(id => predicate(entities[id]));\n}\nfunction findEntityByPredicate(state, ref, predicate) {\n  const {\n    idsKey,\n    entitiesKey\n  } = ref;\n  const entities = state[entitiesKey];\n  const id = state[idsKey].find(id => {\n    return predicate(entities[id]);\n  });\n  return entities[id];\n}\nfunction checkPluck(entity, pluck) {\n  if (entity && pluck) {\n    return isFunction(pluck) ? pluck(entity) : entity[pluck];\n  } else {\n    return entity;\n  }\n}\nfunction getIdKey(context, ref) {\n  return context.config[ref.idKeyRef];\n}\n\n// This will return { entitiesKey: \"entities\", idsKey: \"ids\" }\n\n// This will return { entities: any, ids: any }\n\nclass EntitiesRef {\n  constructor(config) {\n    this.entitiesKey = void 0;\n    this.idsKey = void 0;\n    this.idKeyRef = 'idKey';\n    this.entitiesKey = config.entitiesKey;\n    this.idsKey = config.idsKey;\n    this.idKeyRef = config.idKeyRef;\n  }\n}\nfunction entitiesPropsFactory(feature) {\n  const idKeyRef = feature ? `idKey${capitalize(feature)}` : 'idKey';\n  const ref = new EntitiesRef({\n    entitiesKey: feature ? `${feature}Entities` : 'entities',\n    idsKey: feature ? `${feature}Ids` : 'ids',\n    idKeyRef: idKeyRef\n  });\n  function propsFactory(config) {\n    let entities = {};\n    let ids = [];\n    const idKey = config?.idKey || 'id';\n    if (config?.initialValue) {\n      ({\n        ids,\n        asObject: entities\n      } = buildEntities(config.initialValue, idKey));\n    }\n    return {\n      props: {\n        [ref.entitiesKey]: entities,\n        [ref.idsKey]: ids\n      },\n      config: {\n        [idKeyRef]: idKey\n      }\n    };\n  }\n  return {\n    [`${feature}EntitiesRef`]: ref,\n    [`with${capitalize(feature)}Entities`]: propsFactory\n  };\n}\nconst {\n  withEntities,\n  EntitiesRef: defaultEntitiesRef\n} = entitiesPropsFactory('');\nconst {\n  UIEntitiesRef,\n  withUIEntities\n} = entitiesPropsFactory('UI');\n\n/**\n *\n * Remove entities\n *\n * @example\n *\n * store.update(deleteEntities(1))\n *\n * store.update(deleteEntities([1, 2, 3])\n *\n */\nfunction deleteEntities(ids, options = {}) {\n  return function (state, ctx) {\n    const {\n      ref: {\n        idsKey,\n        entitiesKey\n      } = defaultEntitiesRef\n    } = options;\n    const idsToRemove = coerceArray(ids);\n    const newEntities = {\n      ...state[entitiesKey]\n    };\n    const newIds = state[idsKey].filter(id => !idsToRemove.includes(id));\n    for (const id of idsToRemove) {\n      Reflect.deleteProperty(newEntities, id);\n    }\n    ctx.setEvent({\n      type: 'delete',\n      ids: idsToRemove\n    });\n    return {\n      ...state,\n      [entitiesKey]: newEntities,\n      [idsKey]: newIds\n    };\n  };\n}\n\n/**\n *\n * Remove entities by predicate\n *\n * @example\n *\n * store.update(deleteEntitiesByPredicate(entity => entity.count === 0))\n *\n */\nfunction deleteEntitiesByPredicate(predicate, options = {}) {\n  return function reducer(state, ctx) {\n    const ids = findIdsByPredicate(state, options.ref || defaultEntitiesRef, predicate);\n    if (ids.length) {\n      ctx.setEvent({\n        type: 'delete',\n        ids\n      });\n      return deleteEntities(ids, options)(state, ctx);\n    }\n    return state;\n  };\n}\n\n/**\n *\n * Remove all entities\n *\n * @example\n *\n * store.update(deleteAllEntities())\n *\n */\nfunction deleteAllEntities(options = {}) {\n  return function reducer(state, ctx) {\n    const {\n      ref: {\n        idsKey,\n        entitiesKey\n      } = defaultEntitiesRef\n    } = options;\n    ctx.setEvent({\n      type: 'delete',\n      ids: []\n    });\n    return {\n      ...state,\n      [entitiesKey]: {},\n      [idsKey]: []\n    };\n  };\n}\n\n/**\n *\n * Add entities\n *\n * @example\n *\n * store.update(addEntities(entity))\n *\n * store.update(addEntities([entity, entity]))\n *\n * store.update(addEntities([entity, entity]), { prepend: true })\n *\n */\nfunction addEntities(entities, options = {}) {\n  return function (state, ctx) {\n    const {\n      prepend = false,\n      ref = defaultEntitiesRef\n    } = options;\n    const {\n      entitiesKey,\n      idsKey\n    } = ref;\n    const idKey = getIdKey(ctx, ref);\n    const asArray = coerceArray(entities);\n    if (!asArray.length) return state;\n    if (isDev()) {\n      throwIfEntityExists(asArray, idKey, state, entitiesKey);\n      throwIfDuplicateIdKey(asArray, idKey);\n    }\n    const {\n      ids,\n      asObject\n    } = buildEntities(asArray, idKey);\n    ctx.setEvent({\n      type: 'add',\n      ids\n    });\n    return {\n      ...state,\n      [entitiesKey]: {\n        ...state[entitiesKey],\n        ...asObject\n      },\n      [idsKey]: prepend ? [...ids, ...state[idsKey]] : [...state[idsKey], ...ids]\n    };\n  };\n}\n\n/**\n *\n * Add entities using fifo\n *\n * @example\n *\n *\n * store.update(addEntitiesFifo([entity, entity]), { limit: 3 })\n *\n */\nfunction addEntitiesFifo(entities, options) {\n  return function (state, ctx) {\n    const {\n      ref = defaultEntitiesRef,\n      limit\n    } = options;\n    const {\n      entitiesKey,\n      idsKey\n    } = ref;\n    const currentIds = state[idsKey];\n    let normalizedEntities = coerceArray(entities);\n    let newState = state;\n    if (normalizedEntities.length > limit) {\n      // Remove new entities that pass the limit\n      normalizedEntities = normalizedEntities.slice(normalizedEntities.length - limit);\n    }\n    const total = currentIds.length + normalizedEntities.length;\n\n    // Remove exiting entities that passes the limit\n    if (total > limit) {\n      const idsRemove = currentIds.slice(0, total - limit);\n      newState = deleteEntities(idsRemove)(state, ctx);\n    }\n    const {\n      ids,\n      asObject\n    } = buildEntities(normalizedEntities, getIdKey(ctx, ref));\n    ctx.setEvent({\n      type: 'add',\n      ids\n    });\n    return {\n      ...state,\n      [entitiesKey]: {\n        ...newState[entitiesKey],\n        ...asObject\n      },\n      [idsKey]: [...newState[idsKey], ...ids]\n    };\n  };\n}\nfunction throwIfEntityExists(entities, idKey, state, entitiesKey) {\n  entities.forEach(entity => {\n    const id = entity[idKey];\n    if (state[entitiesKey][id]) {\n      throw Error(`Entity already exists. ${idKey} ${id}`);\n    }\n  });\n}\nfunction throwIfDuplicateIdKey(entities, idKey) {\n  const check = new Set();\n  entities.forEach(entity => {\n    const id = entity[idKey];\n    if (check.has(id)) {\n      throw Error(`Duplicate entity id provided. ${idKey} ${id}`);\n    }\n    check.add(id);\n  });\n}\n\n/**\n *\n * Set entities\n *\n * @example\n *\n * store.update(setEntities([entity, entity]))\n *\n */\nfunction setEntities(entities, options = {}) {\n  return function (state, ctx) {\n    const {\n      ref = defaultEntitiesRef\n    } = options;\n    const {\n      entitiesKey,\n      idsKey\n    } = ref;\n    const {\n      ids,\n      asObject\n    } = buildEntities(entities, getIdKey(ctx, ref));\n    ctx.setEvent({\n      type: 'set',\n      ids\n    });\n    return {\n      ...state,\n      [entitiesKey]: asObject,\n      [idsKey]: ids\n    };\n  };\n}\nfunction setEntitiesMap(entities, options = {}) {\n  return setEntities(Object.values(entities), options);\n}\n\n/**\n *\n * Get the entities collection\n *\n * @example\n *\n * store.query(getAllEntities())\n *\n */\nfunction getAllEntities(options = {}) {\n  const {\n    ref: {\n      entitiesKey,\n      idsKey\n    } = defaultEntitiesRef\n  } = options;\n  return function (state) {\n    return state[idsKey].map(id => state[entitiesKey][id]);\n  };\n}\n\n/**\n *\n * Get the entities and apply filter/map\n *\n * @example\n *\n * store.query(getAllEntitiesApply())\n *\n */\nfunction getAllEntitiesApply(options) {\n  const {\n    ref: {\n      entitiesKey,\n      idsKey\n    } = defaultEntitiesRef,\n    filterEntity = () => true,\n    mapEntity = e => e\n  } = options;\n  return function (state) {\n    const result = [];\n    for (const id of state[idsKey]) {\n      const entity = state[entitiesKey][id];\n      if (filterEntity(entity)) {\n        result.push(mapEntity(entity));\n      }\n    }\n    return result;\n  };\n}\n\n/**\n *\n * Get an entity\n *\n * @example\n *\n * store.query(getEntity(1))\n *\n */\nfunction getEntity$1(id, options = {}) {\n  return function (state) {\n    const {\n      ref: {\n        entitiesKey\n      } = defaultEntitiesRef\n    } = options;\n    return state[entitiesKey][id];\n  };\n}\n\n/**\n *\n * Get first entity by predicate\n *\n * @example\n *\n * store.query(getEntityByPredicate(({ title }) => title === 'Elf'))\n *\n */\nfunction getEntityByPredicate(predicate, options = {}) {\n  return function (state) {\n    const {\n      ref: {\n        entitiesKey,\n        idsKey\n      } = defaultEntitiesRef\n    } = options;\n    const entities = state[entitiesKey];\n    const id = state[idsKey].find(id => {\n      return predicate(entities[id]);\n    });\n    return entities[id];\n  };\n}\n\n/**\n *\n * Check whether the entity exist\n *\n * @example\n *\n * store.query(hasEntity(1))\n *\n */\nfunction hasEntity(id, options = {}) {\n  return function (state) {\n    const {\n      ref: {\n        entitiesKey\n      } = defaultEntitiesRef\n    } = options;\n    return Reflect.has(state[entitiesKey], id);\n  };\n}\n\n/**\n *\n * Get the entities ids\n *\n * @example\n *\n * store.query(getEntitiesIds())\n *\n */\nfunction getEntitiesIds(options = {}) {\n  return function (state) {\n    const {\n      ref: {\n        idsKey\n      } = defaultEntitiesRef\n    } = options;\n    return state[idsKey];\n  };\n}\nfunction toModel(updater, entity) {\n  if (isFunction(updater)) {\n    return updater(entity);\n  }\n  return {\n    ...entity,\n    ...updater\n  };\n}\n\n/**\n *\n * Update entities\n *\n * @example\n *\n * store.update(updateEntities(id, { name }))\n * store.update(updateEntities(id, entity => ({ ...entity, name })))\n * store.update(updateEntities([id, id, id], { open: true }))\n *\n */\nfunction updateEntities(ids, updater, options = {}) {\n  return function (state, ctx) {\n    const coerceIds = coerceArray(ids);\n    if (!coerceIds.length) return state;\n    const {\n      ref: {\n        entitiesKey\n      } = defaultEntitiesRef\n    } = options;\n    const updatedEntities = {};\n    for (const id of coerceIds) {\n      if (hasEntity(id, options)(state)) {\n        updatedEntities[id] = toModel(updater, getEntity$1(id, options)(state));\n      }\n    }\n    ctx.setEvent({\n      type: 'update',\n      ids: coerceIds\n    });\n    return {\n      ...state,\n      [entitiesKey]: {\n        ...state[entitiesKey],\n        ...updatedEntities\n      }\n    };\n  };\n}\n\n/**\n *\n * Update entities by predicate\n *\n * @example\n *\n * store.update(updateEntitiesByPredicate(entity => entity.count === 0))\n *\n */\nfunction updateEntitiesByPredicate(predicate, updater, options = {}) {\n  return function (state, context) {\n    const ids = findIdsByPredicate(state, options.ref || defaultEntitiesRef, predicate);\n    if (ids.length) {\n      return updateEntities(ids, updater, options)(state, context);\n    }\n    return state;\n  };\n}\n\n/**\n *\n * Update all entities\n *\n * @example\n *\n * store.update(updateAllEntities({ name }))\n * store.update(updateAllEntities(entity => ({ ...entity, name })))\n *\n */\nfunction updateAllEntities(updater, options = {}) {\n  return function (state, context) {\n    const {\n      ref: {\n        idsKey\n      } = defaultEntitiesRef\n    } = options;\n    return updateEntities(state[idsKey], updater, options)(state, context);\n  };\n}\n/**\n *\n * Update entities that exists, add those who don't\n *\n * @example\n *\n */\nfunction upsertEntitiesById(ids, {\n  updater,\n  creator,\n  ...options\n}) {\n  return function (state, ctx) {\n    const updatedEntitiesIds = [];\n    const newEntities = [];\n    const asArray = coerceArray(ids);\n    if (!asArray.length) return state;\n    for (const id of asArray) {\n      if (hasEntity(id, options)(state)) {\n        updatedEntitiesIds.push(id);\n      } else {\n        let newEntity = creator(id);\n        if (options.mergeUpdaterWithCreator) {\n          newEntity = toModel(updater, newEntity);\n        }\n        newEntities.push(newEntity);\n      }\n    }\n    const newState = updateEntities(updatedEntitiesIds, updater, options)(state, ctx);\n    return addEntities(newEntities, options)(newState, ctx);\n  };\n}\n\n/**\n *\n * Merge entities that exists, add those who don't\n * Make sure all entities have an id\n *\n * @example\n *\n * // single entity\n * store.update(upsertEntities({ id: 1, completed: true }))\n *\n * // or multiple entities\n * store.update(upsertEntities([{ id: 1, completed: true }, { id: 2, completed: true }]))\n *\n * // or using a custom ref\n * store.update(upsertEntities([{ id: 1, open: true }], { ref: UIEntitiesRef }))\n *\n */\nfunction upsertEntities(entities, options = {}) {\n  return function (state, ctx) {\n    const {\n      prepend = false,\n      ref = defaultEntitiesRef\n    } = options;\n    const {\n      entitiesKey,\n      idsKey\n    } = ref;\n    const idKey = getIdKey(ctx, ref);\n    const asObject = {};\n    const ids = [];\n    const updatedEntitiesId = [];\n    const entitiesArray = coerceArray(entities);\n    if (!entitiesArray.length) {\n      return state;\n    }\n    for (const entity of entitiesArray) {\n      const id = entity[idKey];\n      // if entity exists, merge update, else add\n      if (hasEntity(id, options)(state)) {\n        asObject[id] = {\n          ...state[entitiesKey][id],\n          ...entity\n        };\n        updatedEntitiesId.push(id);\n      } else {\n        ids.push(id);\n        asObject[id] = entity;\n      }\n    }\n    const updatedIds = !ids.length ? {} : {\n      [idsKey]: prepend ? [...ids, ...state[idsKey]] : [...state[idsKey], ...ids]\n    };\n    if (ids.length) {\n      ctx.setEvent({\n        type: 'add',\n        ids\n      });\n    }\n    if (updatedEntitiesId.length) {\n      ctx.setEvent({\n        type: 'update',\n        ids: updatedEntitiesId\n      });\n    }\n    return {\n      ...state,\n      ...updatedIds,\n      [entitiesKey]: {\n        ...state[entitiesKey],\n        ...asObject\n      }\n    };\n  };\n}\n\n/**\n * Update entities ids\n *\n * @example\n *\n * // Update a single entity id\n * store.update(updateEntitiesIds(1, 2));\n *\n * // Update multiple entities ids\n * store.update(updateEntitiesIds([1, 2], [10, 20]));\n *\n * // Update entity id using a custom ref\n * store.update(updateEntitiesIds(1, 2, { ref: UIEntitiesRef }));\n *\n */\nfunction updateEntitiesIds(oldId, newId, options = {}) {\n  return function (state, ctx) {\n    const oldIds = coerceArray(oldId);\n    const newIds = coerceArray(newId);\n    if (oldIds.length !== newIds.length) {\n      throw new Error('The number of old and new ids must be equal');\n    }\n    if (!oldIds.length || !newIds.length) return state;\n    const {\n      ref = defaultEntitiesRef\n    } = options;\n    const idProp = getIdKey(ctx, ref);\n    const updatedEntities = {\n      ...state[ref.entitiesKey]\n    };\n    for (let i = 0; i < oldIds.length; i++) {\n      const oldVal = oldIds[i];\n      const newVal = newIds[i];\n      if (state[ref.entitiesKey][newVal]) {\n        throw new Error(`Updating id \"${oldVal}\". The new id \"${newVal}\" already exists`);\n      }\n      const oldEntity = state[ref.entitiesKey][oldVal];\n      const updated = {\n        ...oldEntity,\n        [idProp]: newVal\n      };\n      updatedEntities[newVal] = updated;\n      Reflect.deleteProperty(updatedEntities, oldVal);\n    }\n    const updatedStateIds = state[ref.idsKey].slice();\n    let processedIds = 0;\n    for (let i = 0; i < updatedStateIds.length; i++) {\n      const currentId = updatedStateIds[i];\n      for (let j = 0; j < oldIds.length; j++) {\n        const oldVal = oldIds[j];\n        const newVal = newIds[j];\n        if (currentId === oldVal) {\n          updatedStateIds[i] = newVal;\n          processedIds++;\n          break;\n        }\n      }\n      if (processedIds === oldIds.length) {\n        break;\n      }\n    }\n    ctx.setEvent({\n      type: 'update',\n      ids: newIds\n    });\n    return {\n      ...state,\n      [ref.entitiesKey]: updatedEntities,\n      [ref.idsKey]: updatedStateIds\n    };\n  };\n}\n\n/**\n *\n * Move entity\n *\n * @example\n *\n * store.update(moveEntity({ fromIndex: 2, toIndex: 3}))\n *\n */\nfunction moveEntity(options) {\n  return function (state) {\n    const {\n      fromIndex,\n      toIndex,\n      ref: {\n        idsKey,\n        entitiesKey\n      } = defaultEntitiesRef\n    } = options;\n    const ids = state[idsKey].slice();\n    ids.splice(toIndex < 0 ? ids.length + toIndex : toIndex, 0, ids.splice(fromIndex, 1)[0]);\n    return {\n      ...state,\n      [entitiesKey]: {\n        ...state[entitiesKey]\n      },\n      [idsKey]: ids\n    };\n  };\n}\nfunction untilEntitiesChanges(key) {\n  return distinctUntilChanged((prev, current) => {\n    return prev[key] === current[key];\n  });\n}\n\n/**\n *\n * Observe entities\n *\n * @example\n *\n * store.pipe(selectAllEntities())\n *\n * store.pipe(selectAllEntities({ ref: UIEntitiesRef }))\n *\n */\nfunction selectAllEntities(options = {}) {\n  const {\n    ref: {\n      entitiesKey,\n      idsKey\n    } = defaultEntitiesRef\n  } = options;\n  return pipe(untilEntitiesChanges(entitiesKey), map(state => state[idsKey].map(id => state[entitiesKey][id])));\n}\n\n/**\n *\n * Observe entities object\n *\n * @example\n *\n * store.pipe(selectEntities())\n *\n * store.pipe(selectEntities({ ref: UIEntitiesRef }))\n *\n */\nfunction selectEntities(options = {}) {\n  const {\n    ref: {\n      entitiesKey\n    } = defaultEntitiesRef\n  } = options;\n  return select(state => state[entitiesKey]);\n}\n\n/**\n *\n * Observe entities and apply filter/map\n *\n * @example\n *\n * store.pipe(selectAllEntitiesApply({\n *   map: (entity) => new Todo(entity),\n *   filter: entity => entity.completed\n * }))\n *\n *\n */\nfunction selectAllEntitiesApply(options) {\n  const {\n    ref: {\n      entitiesKey,\n      idsKey\n    } = defaultEntitiesRef,\n    filterEntity = () => true,\n    mapEntity = e => e\n  } = options;\n  return pipe(untilEntitiesChanges(entitiesKey), map(state => {\n    const result = [];\n    for (const id of state[idsKey]) {\n      const entity = state[entitiesKey][id];\n      if (filterEntity(entity)) {\n        result.push(mapEntity(entity));\n      }\n    }\n    return result;\n  }));\n}\n\n/**\n * Observe an entity\n *\n * @example\n *\n * store.pipe(selectEntity(id, { pluck: 'title' })\n *\n * store.pipe(selectEntity(id, { ref: UIEntitiesRef })\n *\n */\n\n/**\n * Observe an entity\n *\n * @example\n *\n * store.pipe(selectEntity(id, { pluck: e => e.title })\n *\n * store.pipe(selectEntity(id, { ref: UIEntitiesRef })\n *\n */\n\n/**\n *\n * Observe an entity\n *\n * @example\n *\n * store.pipe(selectEntity(id))\n *\n * store.pipe(selectEntity(id, { ref: UIEntitiesRef })\n *\n */\n\nfunction selectEntity(id, options = {}) {\n  const {\n    ref: {\n      entitiesKey\n    } = defaultEntitiesRef,\n    pluck\n  } = options;\n  return pipe(untilEntitiesChanges(entitiesKey), select(state => getEntity(state[entitiesKey], id, pluck)));\n}\nfunction getEntity(entities, id, pluck) {\n  const entity = entities[id];\n  if (isUndefined(entity)) {\n    return undefined;\n  }\n  if (!pluck) {\n    return entity;\n  }\n  return checkPluck(entity, pluck);\n}\n\n/**\n * Observe an entity\n *\n * @example\n *\n * store.pipe(selectEntityByPredicate(entity => entity.title, { pluck: entity => entity.title })\n *\n */\n\n/**\n *\n * Observe an entity\n *\n * @example\n *\n * store.pipe(selectEntityByPredicate(entity => entity.title, { pluck: 'title' })\n *\n */\n\n/**\n *\n * Observe an entity\n *\n * @example\n *\n * store.pipe(selectEntityByPredicate(entity => entity.title, { ref: UIEntitiesRef })\n *\n */\n\nfunction selectEntityByPredicate(predicate, options) {\n  const {\n    ref = defaultEntitiesRef,\n    pluck,\n    idKey = 'id'\n  } = options || {};\n  const {\n    entitiesKey\n  } = ref;\n  let id;\n  return pipe(select(state => {\n    if (isUndefined(id)) {\n      const entity = findEntityByPredicate(state, ref, predicate);\n      id = entity && entity[idKey];\n    }\n    return state[entitiesKey][id];\n  }), map(entity => entity ? checkPluck(entity, pluck) : undefined), distinctUntilChanged());\n}\n\n/**\n *\n * Observe the first entity\n *\n * @example\n *\n * store.pipe(selectFirst())\n *\n */\nfunction selectFirst(options = {}) {\n  const {\n    ref: {\n      entitiesKey,\n      idsKey\n    } = defaultEntitiesRef\n  } = options;\n  return select(state => state[entitiesKey][state[idsKey][0]]);\n}\n\n/**\n *\n * Observe the last entity\n *\n * @example\n *\n * store.pipe(selectLast())\n *\n */\nfunction selectLast(options = {}) {\n  const {\n    ref: {\n      entitiesKey,\n      idsKey\n    } = defaultEntitiesRef\n  } = options;\n  return select(state => state[entitiesKey][state[idsKey][state[idsKey].length - 1]]);\n}\n\n/**\n * Observe multiple entities\n *\n * @example\n *\n * store.pipe(selectMany([1,2,3], { pluck: 'title' })\n *\n */\n\n/**\n * Observe multiple entities\n *\n * @example\n *\n * store.pipe(selectMany([1,2,3], { pluck: e => e.title })\n *\n */\n\n/**\n * Observe multiple entities\n *\n * @example\n *\n * store.pipe(selectMany([1, 2, 3])\n *\n */\n\nfunction selectMany(ids, options = {}) {\n  const {\n    ref: {\n      entitiesKey\n    } = defaultEntitiesRef,\n    pluck\n  } = options;\n  return pipe(select(state => state[entitiesKey]), map(entities => {\n    if (!ids.length) return [];\n    const filtered = [];\n    for (const id of ids) {\n      const entity = getEntity(entities, id, pluck);\n      if (!isUndefined(entity)) filtered.push(entity);\n    }\n    return filtered;\n  }), distinctUntilArrayItemChanged());\n}\nfunction selectManyByPredicate(predicate, options) {\n  const {\n    ref: {\n      entitiesKey,\n      idsKey\n    } = defaultEntitiesRef,\n    pluck\n  } = options || {};\n  return pipe(untilEntitiesChanges(entitiesKey), select(state => {\n    const filteredEntities = [];\n    state[idsKey].forEach((id, index) => {\n      const entity = state[entitiesKey][id];\n      if (predicate(entity, index)) {\n        filteredEntities.push(checkPluck(entity, pluck));\n      }\n    });\n    return filteredEntities;\n  }), distinctUntilArrayItemChanged());\n}\n\n/**\n *\n * Observe the entities collection size\n *\n * @example\n *\n * store.pipe(selectEntitiesCount())\n *\n */\nfunction selectEntitiesCount(options = {}) {\n  const {\n    ref: {\n      idsKey\n    } = defaultEntitiesRef\n  } = options;\n  return select(state => state[idsKey].length);\n}\n\n/**\n *\n * Observe the entities collection size  that pass the predicate\n *\n * @example\n *\n * store.pipe(selectEntitiesCountByPredicate(entity => entity.completed))\n *\n */\nfunction selectEntitiesCountByPredicate(predicate, options = {}) {\n  const ref = options.ref || defaultEntitiesRef;\n  return pipe(untilEntitiesChanges(ref.entitiesKey), map(state => findIdsByPredicate(state, ref, predicate).length), distinctUntilChanged());\n}\n\n/**\n *\n * Return the entities collection size\n *\n * @example\n *\n * store.query(getEntitiesCount())\n *\n */\nfunction getEntitiesCount(options = {}) {\n  return function (state) {\n    const {\n      ref: {\n        idsKey\n      } = defaultEntitiesRef\n    } = options;\n    return state[idsKey].length;\n  };\n}\n\n/**\n *\n * Return the entities collection size that pass the predicate\n *\n * @example\n *\n * store.query(getEntitiesCountByPredicate(entity => entity.completed))\n *\n */\nfunction getEntitiesCountByPredicate(predicate, options = {}) {\n  return function (state) {\n    const ref = options.ref || defaultEntitiesRef;\n    return findIdsByPredicate(state, ref, predicate).length;\n  };\n}\nfunction unionEntities(idKey = 'id') {\n  return map(state => {\n    return state.entities.map(entity => {\n      return {\n        ...entity,\n        ...state.UIEntities[entity[idKey]]\n      };\n    });\n  });\n}\nfunction unionEntitiesAsMap(idKey = 'id') {\n  return map(state => {\n    return Object.fromEntries(state.entities.map(entity => {\n      return [entity[idKey], {\n        ...entity,\n        ...state.UIEntities[entity[idKey]]\n      }];\n    }));\n  });\n}\nconst {\n  selectActiveId,\n  setActiveId,\n  withActiveId,\n  resetActiveId,\n  getActiveId\n} = propsFactory('activeId', {\n  initialValue: undefined\n});\nfunction selectActiveEntity(options = {}) {\n  const {\n    ref = defaultEntitiesRef\n  } = options;\n  return function (source) {\n    return source.pipe(selectActiveId()).pipe(switchMap(id => source.pipe(selectEntity(id, {\n      ref\n    }))));\n  };\n}\nfunction getActiveEntity(options = {}) {\n  const {\n    ref: {\n      entitiesKey\n    } = defaultEntitiesRef\n  } = options;\n  return function (state) {\n    return state[entitiesKey][getActiveId(state)];\n  };\n}\nconst {\n  setActiveIds,\n  resetActiveIds,\n  withActiveIds,\n  selectActiveIds,\n  toggleActiveIds,\n  removeActiveIds,\n  addActiveIds,\n  getActiveIds\n} = propsArrayFactory('activeIds', {\n  initialValue: []\n});\nfunction selectActiveEntities(options = {}) {\n  const {\n    ref = defaultEntitiesRef\n  } = options;\n  return function (source) {\n    return source.pipe(selectActiveIds()).pipe(switchMap(ids => source.pipe(selectMany(ids, {\n      ref\n    }))));\n  };\n}\nfunction getActiveEntities(options = {}) {\n  const {\n    ref: {\n      entitiesKey\n    } = defaultEntitiesRef\n  } = options;\n  return function (state) {\n    const result = [];\n    for (const id of getActiveIds(state)) {\n      const entity = state[entitiesKey][id];\n      if (entity) {\n        result.push(entity);\n      }\n    }\n    return result;\n  };\n}\nexport { EntitiesRef, UIEntitiesRef, addActiveIds, addEntities, addEntitiesFifo, deleteAllEntities, deleteEntities, deleteEntitiesByPredicate, entitiesPropsFactory, getActiveEntities, getActiveEntity, getActiveId, getActiveIds, getAllEntities, getAllEntitiesApply, getEntitiesCount, getEntitiesCountByPredicate, getEntitiesIds, getEntity$1 as getEntity, getEntityByPredicate, hasEntity, moveEntity, removeActiveIds, resetActiveId, resetActiveIds, selectActiveEntities, selectActiveEntity, selectActiveId, selectActiveIds, selectAllEntities, selectAllEntitiesApply, selectEntities, selectEntitiesCount, selectEntitiesCountByPredicate, selectEntity, selectEntityByPredicate, selectFirst, selectLast, selectMany, selectManyByPredicate, setActiveId, setActiveIds, setEntities, setEntitiesMap, toggleActiveIds, unionEntities, unionEntitiesAsMap, updateAllEntities, updateEntities, updateEntitiesByPredicate, updateEntitiesIds, upsertEntities, upsertEntitiesById, withActiveId, withActiveIds, withEntities, withUIEntities };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,cAAc,UAAU,OAAO;AACtC,QAAM,WAAW,CAAC;AAClB,QAAM,MAAM,CAAC;AACb,aAAW,UAAU,UAAU;AAC7B,UAAM,KAAK,OAAO,KAAK;AACvB,QAAI,KAAK,EAAE;AACX,aAAS,EAAE,IAAI;AAAA,EACjB;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,OAAO,KAAK,WAAW;AACjD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,MAAM,WAAW;AAClC,SAAO,MAAM,MAAM,EAAE,OAAO,QAAM,UAAU,SAAS,EAAE,CAAC,CAAC;AAC3D;AACA,SAAS,sBAAsB,OAAO,KAAK,WAAW;AACpD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,MAAM,WAAW;AAClC,QAAM,KAAK,MAAM,MAAM,EAAE,KAAK,CAAAA,QAAM;AAClC,WAAO,UAAU,SAASA,GAAE,CAAC;AAAA,EAC/B,CAAC;AACD,SAAO,SAAS,EAAE;AACpB;AACA,SAAS,WAAW,QAAQ,OAAO;AACjC,MAAI,UAAU,OAAO;AACnB,WAAO,WAAW,KAAK,IAAI,MAAM,MAAM,IAAI,OAAO,KAAK;AAAA,EACzD,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,SAAS,SAAS,KAAK;AAC9B,SAAO,QAAQ,OAAO,IAAI,QAAQ;AACpC;AAMA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,QAAQ;AAClB,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,cAAc,OAAO;AAC1B,SAAK,SAAS,OAAO;AACrB,SAAK,WAAW,OAAO;AAAA,EACzB;AACF;AACA,SAAS,qBAAqB,SAAS;AACrC,QAAM,WAAW,UAAU,QAAQ,WAAW,OAAO,CAAC,KAAK;AAC3D,QAAM,MAAM,IAAI,YAAY;AAAA,IAC1B,aAAa,UAAU,GAAG,OAAO,aAAa;AAAA,IAC9C,QAAQ,UAAU,GAAG,OAAO,QAAQ;AAAA,IACpC;AAAA,EACF,CAAC;AACD,WAASC,cAAa,QAAQ;AAC5B,QAAI,WAAW,CAAC;AAChB,QAAI,MAAM,CAAC;AACX,UAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAI,QAAQ,cAAc;AACxB,OAAC;AAAA,QACC;AAAA,QACA,UAAU;AAAA,MACZ,IAAI,cAAc,OAAO,cAAc,KAAK;AAAA,IAC9C;AACA,WAAO;AAAA,MACL,OAAO;AAAA,QACL,CAAC,IAAI,WAAW,GAAG;AAAA,QACnB,CAAC,IAAI,MAAM,GAAG;AAAA,MAChB;AAAA,MACA,QAAQ;AAAA,QACN,CAAC,QAAQ,GAAG;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,CAAC,GAAG,OAAO,aAAa,GAAG;AAAA,IAC3B,CAAC,OAAO,WAAW,OAAO,CAAC,UAAU,GAAGA;AAAA,EAC1C;AACF;AACA,IAAM;AAAA,EACJ;AAAA,EACA,aAAa;AACf,IAAI,qBAAqB,EAAE;AAC3B,IAAM;AAAA,EACJ;AAAA,EACA;AACF,IAAI,qBAAqB,IAAI;AAa7B,SAAS,eAAe,KAAK,UAAU,CAAC,GAAG;AACzC,SAAO,SAAU,OAAO,KAAK;AAC3B,UAAM;AAAA,MACJ,KAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF,IAAI;AAAA,IACN,IAAI;AACJ,UAAM,cAAc,YAAY,GAAG;AACnC,UAAM,cAAc,mBACf,MAAM,WAAW;AAEtB,UAAM,SAAS,MAAM,MAAM,EAAE,OAAO,QAAM,CAAC,YAAY,SAAS,EAAE,CAAC;AACnE,eAAW,MAAM,aAAa;AAC5B,cAAQ,eAAe,aAAa,EAAE;AAAA,IACxC;AACA,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN,KAAK;AAAA,IACP,CAAC;AACD,WAAO,iCACF,QADE;AAAA,MAEL,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,MAAM,GAAG;AAAA,IACZ;AAAA,EACF;AACF;AAWA,SAAS,0BAA0B,WAAW,UAAU,CAAC,GAAG;AAC1D,SAAO,SAAS,QAAQ,OAAO,KAAK;AAClC,UAAM,MAAM,mBAAmB,OAAO,QAAQ,OAAO,oBAAoB,SAAS;AAClF,QAAI,IAAI,QAAQ;AACd,UAAI,SAAS;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AACD,aAAO,eAAe,KAAK,OAAO,EAAE,OAAO,GAAG;AAAA,IAChD;AACA,WAAO;AAAA,EACT;AACF;AAWA,SAAS,kBAAkB,UAAU,CAAC,GAAG;AACvC,SAAO,SAAS,QAAQ,OAAO,KAAK;AAClC,UAAM;AAAA,MACJ,KAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF,IAAI;AAAA,IACN,IAAI;AACJ,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN,KAAK,CAAC;AAAA,IACR,CAAC;AACD,WAAO,iCACF,QADE;AAAA,MAEL,CAAC,WAAW,GAAG,CAAC;AAAA,MAChB,CAAC,MAAM,GAAG,CAAC;AAAA,IACb;AAAA,EACF;AACF;AAeA,SAAS,YAAY,UAAU,UAAU,CAAC,GAAG;AAC3C,SAAO,SAAU,OAAO,KAAK;AAC3B,UAAM;AAAA,MACJ,UAAU;AAAA,MACV,MAAM;AAAA,IACR,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,QAAQ,SAAS,KAAK,GAAG;AAC/B,UAAM,UAAU,YAAY,QAAQ;AACpC,QAAI,CAAC,QAAQ,OAAQ,QAAO;AAC5B,QAAI,MAAM,GAAG;AACX,0BAAoB,SAAS,OAAO,OAAO,WAAW;AACtD,4BAAsB,SAAS,KAAK;AAAA,IACtC;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,SAAS,KAAK;AAChC,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AACD,WAAO,iCACF,QADE;AAAA,MAEL,CAAC,WAAW,GAAG,kCACV,MAAM,WAAW,IACjB;AAAA,MAEL,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,MAAM,GAAG,GAAG,GAAG;AAAA,IAC5E;AAAA,EACF;AACF;AAYA,SAAS,gBAAgB,UAAU,SAAS;AAC1C,SAAO,SAAU,OAAO,KAAK;AAC3B,UAAM;AAAA,MACJ,MAAM;AAAA,MACN;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,MAAM,MAAM;AAC/B,QAAI,qBAAqB,YAAY,QAAQ;AAC7C,QAAI,WAAW;AACf,QAAI,mBAAmB,SAAS,OAAO;AAErC,2BAAqB,mBAAmB,MAAM,mBAAmB,SAAS,KAAK;AAAA,IACjF;AACA,UAAM,QAAQ,WAAW,SAAS,mBAAmB;AAGrD,QAAI,QAAQ,OAAO;AACjB,YAAM,YAAY,WAAW,MAAM,GAAG,QAAQ,KAAK;AACnD,iBAAW,eAAe,SAAS,EAAE,OAAO,GAAG;AAAA,IACjD;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,oBAAoB,SAAS,KAAK,GAAG,CAAC;AACxD,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AACD,WAAO,iCACF,QADE;AAAA,MAEL,CAAC,WAAW,GAAG,kCACV,SAAS,WAAW,IACpB;AAAA,MAEL,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,GAAG,GAAG;AAAA,IACxC;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,UAAU,OAAO,OAAO,aAAa;AAChE,WAAS,QAAQ,YAAU;AACzB,UAAM,KAAK,OAAO,KAAK;AACvB,QAAI,MAAM,WAAW,EAAE,EAAE,GAAG;AAC1B,YAAM,MAAM,0BAA0B,KAAK,IAAI,EAAE,EAAE;AAAA,IACrD;AAAA,EACF,CAAC;AACH;AACA,SAAS,sBAAsB,UAAU,OAAO;AAC9C,QAAM,QAAQ,oBAAI,IAAI;AACtB,WAAS,QAAQ,YAAU;AACzB,UAAM,KAAK,OAAO,KAAK;AACvB,QAAI,MAAM,IAAI,EAAE,GAAG;AACjB,YAAM,MAAM,iCAAiC,KAAK,IAAI,EAAE,EAAE;AAAA,IAC5D;AACA,UAAM,IAAI,EAAE;AAAA,EACd,CAAC;AACH;AAWA,SAAS,YAAY,UAAU,UAAU,CAAC,GAAG;AAC3C,SAAO,SAAU,OAAO,KAAK;AAC3B,UAAM;AAAA,MACJ,MAAM;AAAA,IACR,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc,UAAU,SAAS,KAAK,GAAG,CAAC;AAC9C,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AACD,WAAO,iCACF,QADE;AAAA,MAEL,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,MAAM,GAAG;AAAA,IACZ;AAAA,EACF;AACF;AACA,SAAS,eAAe,UAAU,UAAU,CAAC,GAAG;AAC9C,SAAO,YAAY,OAAO,OAAO,QAAQ,GAAG,OAAO;AACrD;AAWA,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI;AAAA,EACN,IAAI;AACJ,SAAO,SAAU,OAAO;AACtB,WAAO,MAAM,MAAM,EAAE,IAAI,QAAM,MAAM,WAAW,EAAE,EAAE,CAAC;AAAA,EACvD;AACF;AAWA,SAAS,oBAAoB,SAAS;AACpC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI;AAAA,IACJ,eAAe,MAAM;AAAA,IACrB,YAAY,OAAK;AAAA,EACnB,IAAI;AACJ,SAAO,SAAU,OAAO;AACtB,UAAM,SAAS,CAAC;AAChB,eAAW,MAAM,MAAM,MAAM,GAAG;AAC9B,YAAM,SAAS,MAAM,WAAW,EAAE,EAAE;AACpC,UAAI,aAAa,MAAM,GAAG;AACxB,eAAO,KAAK,UAAU,MAAM,CAAC;AAAA,MAC/B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAWA,SAAS,YAAY,IAAI,UAAU,CAAC,GAAG;AACrC,SAAO,SAAU,OAAO;AACtB,UAAM;AAAA,MACJ,KAAK;AAAA,QACH;AAAA,MACF,IAAI;AAAA,IACN,IAAI;AACJ,WAAO,MAAM,WAAW,EAAE,EAAE;AAAA,EAC9B;AACF;AAWA,SAAS,qBAAqB,WAAW,UAAU,CAAC,GAAG;AACrD,SAAO,SAAU,OAAO;AACtB,UAAM;AAAA,MACJ,KAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF,IAAI;AAAA,IACN,IAAI;AACJ,UAAM,WAAW,MAAM,WAAW;AAClC,UAAM,KAAK,MAAM,MAAM,EAAE,KAAK,CAAAD,QAAM;AAClC,aAAO,UAAU,SAASA,GAAE,CAAC;AAAA,IAC/B,CAAC;AACD,WAAO,SAAS,EAAE;AAAA,EACpB;AACF;AAWA,SAAS,UAAU,IAAI,UAAU,CAAC,GAAG;AACnC,SAAO,SAAU,OAAO;AACtB,UAAM;AAAA,MACJ,KAAK;AAAA,QACH;AAAA,MACF,IAAI;AAAA,IACN,IAAI;AACJ,WAAO,QAAQ,IAAI,MAAM,WAAW,GAAG,EAAE;AAAA,EAC3C;AACF;AAWA,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,SAAO,SAAU,OAAO;AACtB,UAAM;AAAA,MACJ,KAAK;AAAA,QACH;AAAA,MACF,IAAI;AAAA,IACN,IAAI;AACJ,WAAO,MAAM,MAAM;AAAA,EACrB;AACF;AACA,SAAS,QAAQ,SAAS,QAAQ;AAChC,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO,QAAQ,MAAM;AAAA,EACvB;AACA,SAAO,kCACF,SACA;AAEP;AAaA,SAAS,eAAe,KAAK,SAAS,UAAU,CAAC,GAAG;AAClD,SAAO,SAAU,OAAO,KAAK;AAC3B,UAAM,YAAY,YAAY,GAAG;AACjC,QAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,UAAM;AAAA,MACJ,KAAK;AAAA,QACH;AAAA,MACF,IAAI;AAAA,IACN,IAAI;AACJ,UAAM,kBAAkB,CAAC;AACzB,eAAW,MAAM,WAAW;AAC1B,UAAI,UAAU,IAAI,OAAO,EAAE,KAAK,GAAG;AACjC,wBAAgB,EAAE,IAAI,QAAQ,SAAS,YAAY,IAAI,OAAO,EAAE,KAAK,CAAC;AAAA,MACxE;AAAA,IACF;AACA,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN,KAAK;AAAA,IACP,CAAC;AACD,WAAO,iCACF,QADE;AAAA,MAEL,CAAC,WAAW,GAAG,kCACV,MAAM,WAAW,IACjB;AAAA,IAEP;AAAA,EACF;AACF;AAWA,SAAS,0BAA0B,WAAW,SAAS,UAAU,CAAC,GAAG;AACnE,SAAO,SAAU,OAAO,SAAS;AAC/B,UAAM,MAAM,mBAAmB,OAAO,QAAQ,OAAO,oBAAoB,SAAS;AAClF,QAAI,IAAI,QAAQ;AACd,aAAO,eAAe,KAAK,SAAS,OAAO,EAAE,OAAO,OAAO;AAAA,IAC7D;AACA,WAAO;AAAA,EACT;AACF;AAYA,SAAS,kBAAkB,SAAS,UAAU,CAAC,GAAG;AAChD,SAAO,SAAU,OAAO,SAAS;AAC/B,UAAM;AAAA,MACJ,KAAK;AAAA,QACH;AAAA,MACF,IAAI;AAAA,IACN,IAAI;AACJ,WAAO,eAAe,MAAM,MAAM,GAAG,SAAS,OAAO,EAAE,OAAO,OAAO;AAAA,EACvE;AACF;AAQA,SAAS,mBAAmB,KAAK,IAI9B;AAJ8B,eAC/B;AAAA;AAAA,IACA;AAAA,EAtkBF,IAokBiC,IAG5B,oBAH4B,IAG5B;AAAA,IAFH;AAAA,IACA;AAAA;AAGA,SAAO,SAAU,OAAO,KAAK;AAC3B,UAAM,qBAAqB,CAAC;AAC5B,UAAM,cAAc,CAAC;AACrB,UAAM,UAAU,YAAY,GAAG;AAC/B,QAAI,CAAC,QAAQ,OAAQ,QAAO;AAC5B,eAAW,MAAM,SAAS;AACxB,UAAI,UAAU,IAAI,OAAO,EAAE,KAAK,GAAG;AACjC,2BAAmB,KAAK,EAAE;AAAA,MAC5B,OAAO;AACL,YAAI,YAAY,QAAQ,EAAE;AAC1B,YAAI,QAAQ,yBAAyB;AACnC,sBAAY,QAAQ,SAAS,SAAS;AAAA,QACxC;AACA,oBAAY,KAAK,SAAS;AAAA,MAC5B;AAAA,IACF;AACA,UAAM,WAAW,eAAe,oBAAoB,SAAS,OAAO,EAAE,OAAO,GAAG;AAChF,WAAO,YAAY,aAAa,OAAO,EAAE,UAAU,GAAG;AAAA,EACxD;AACF;AAmBA,SAAS,eAAe,UAAU,UAAU,CAAC,GAAG;AAC9C,SAAO,SAAU,OAAO,KAAK;AAC3B,UAAM;AAAA,MACJ,UAAU;AAAA,MACV,MAAM;AAAA,IACR,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,QAAQ,SAAS,KAAK,GAAG;AAC/B,UAAM,WAAW,CAAC;AAClB,UAAM,MAAM,CAAC;AACb,UAAM,oBAAoB,CAAC;AAC3B,UAAM,gBAAgB,YAAY,QAAQ;AAC1C,QAAI,CAAC,cAAc,QAAQ;AACzB,aAAO;AAAA,IACT;AACA,eAAW,UAAU,eAAe;AAClC,YAAM,KAAK,OAAO,KAAK;AAEvB,UAAI,UAAU,IAAI,OAAO,EAAE,KAAK,GAAG;AACjC,iBAAS,EAAE,IAAI,kCACV,MAAM,WAAW,EAAE,EAAE,IACrB;AAEL,0BAAkB,KAAK,EAAE;AAAA,MAC3B,OAAO;AACL,YAAI,KAAK,EAAE;AACX,iBAAS,EAAE,IAAI;AAAA,MACjB;AAAA,IACF;AACA,UAAM,aAAa,CAAC,IAAI,SAAS,CAAC,IAAI;AAAA,MACpC,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,MAAM,GAAG,GAAG,GAAG;AAAA,IAC5E;AACA,QAAI,IAAI,QAAQ;AACd,UAAI,SAAS;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,kBAAkB,QAAQ;AAC5B,UAAI,SAAS;AAAA,QACX,MAAM;AAAA,QACN,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AACA,WAAO,gDACF,QACA,aAFE;AAAA,MAGL,CAAC,WAAW,GAAG,kCACV,MAAM,WAAW,IACjB;AAAA,IAEP;AAAA,EACF;AACF;AAiBA,SAAS,kBAAkB,OAAO,OAAO,UAAU,CAAC,GAAG;AACrD,SAAO,SAAU,OAAO,KAAK;AAC3B,UAAM,SAAS,YAAY,KAAK;AAChC,UAAM,SAAS,YAAY,KAAK;AAChC,QAAI,OAAO,WAAW,OAAO,QAAQ;AACnC,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AACA,QAAI,CAAC,OAAO,UAAU,CAAC,OAAO,OAAQ,QAAO;AAC7C,UAAM;AAAA,MACJ,MAAM;AAAA,IACR,IAAI;AACJ,UAAM,SAAS,SAAS,KAAK,GAAG;AAChC,UAAM,kBAAkB,mBACnB,MAAM,IAAI,WAAW;AAE1B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,SAAS,OAAO,CAAC;AACvB,YAAM,SAAS,OAAO,CAAC;AACvB,UAAI,MAAM,IAAI,WAAW,EAAE,MAAM,GAAG;AAClC,cAAM,IAAI,MAAM,gBAAgB,MAAM,kBAAkB,MAAM,kBAAkB;AAAA,MAClF;AACA,YAAM,YAAY,MAAM,IAAI,WAAW,EAAE,MAAM;AAC/C,YAAM,UAAU,iCACX,YADW;AAAA,QAEd,CAAC,MAAM,GAAG;AAAA,MACZ;AACA,sBAAgB,MAAM,IAAI;AAC1B,cAAQ,eAAe,iBAAiB,MAAM;AAAA,IAChD;AACA,UAAM,kBAAkB,MAAM,IAAI,MAAM,EAAE,MAAM;AAChD,QAAI,eAAe;AACnB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,YAAM,YAAY,gBAAgB,CAAC;AACnC,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,SAAS,OAAO,CAAC;AACvB,cAAM,SAAS,OAAO,CAAC;AACvB,YAAI,cAAc,QAAQ;AACxB,0BAAgB,CAAC,IAAI;AACrB;AACA;AAAA,QACF;AAAA,MACF;AACA,UAAI,iBAAiB,OAAO,QAAQ;AAClC;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN,KAAK;AAAA,IACP,CAAC;AACD,WAAO,iCACF,QADE;AAAA,MAEL,CAAC,IAAI,WAAW,GAAG;AAAA,MACnB,CAAC,IAAI,MAAM,GAAG;AAAA,IAChB;AAAA,EACF;AACF;AAWA,SAAS,WAAW,SAAS;AAC3B,SAAO,SAAU,OAAO;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,KAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF,IAAI;AAAA,IACN,IAAI;AACJ,UAAM,MAAM,MAAM,MAAM,EAAE,MAAM;AAChC,QAAI,OAAO,UAAU,IAAI,IAAI,SAAS,UAAU,SAAS,GAAG,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,CAAC;AACvF,WAAO,iCACF,QADE;AAAA,MAEL,CAAC,WAAW,GAAG,mBACV,MAAM,WAAW;AAAA,MAEtB,CAAC,MAAM,GAAG;AAAA,IACZ;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,KAAK;AACjC,SAAO,qBAAqB,CAAC,MAAM,YAAY;AAC7C,WAAO,KAAK,GAAG,MAAM,QAAQ,GAAG;AAAA,EAClC,CAAC;AACH;AAaA,SAAS,kBAAkB,UAAU,CAAC,GAAG;AACvC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI;AAAA,EACN,IAAI;AACJ,SAAO,KAAK,qBAAqB,WAAW,GAAG,IAAI,WAAS,MAAM,MAAM,EAAE,IAAI,QAAM,MAAM,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;AAC9G;AAaA,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,IACF,IAAI;AAAA,EACN,IAAI;AACJ,SAAO,OAAO,WAAS,MAAM,WAAW,CAAC;AAC3C;AAeA,SAAS,uBAAuB,SAAS;AACvC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI;AAAA,IACJ,eAAe,MAAM;AAAA,IACrB,YAAY,OAAK;AAAA,EACnB,IAAI;AACJ,SAAO,KAAK,qBAAqB,WAAW,GAAG,IAAI,WAAS;AAC1D,UAAM,SAAS,CAAC;AAChB,eAAW,MAAM,MAAM,MAAM,GAAG;AAC9B,YAAM,SAAS,MAAM,WAAW,EAAE,EAAE;AACpC,UAAI,aAAa,MAAM,GAAG;AACxB,eAAO,KAAK,UAAU,MAAM,CAAC;AAAA,MAC/B;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC,CAAC;AACJ;AAoCA,SAAS,aAAa,IAAI,UAAU,CAAC,GAAG;AACtC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,IACF,IAAI;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,KAAK,qBAAqB,WAAW,GAAG,OAAO,WAAS,UAAU,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,CAAC;AAC1G;AACA,SAAS,UAAU,UAAU,IAAI,OAAO;AACtC,QAAM,SAAS,SAAS,EAAE;AAC1B,MAAI,YAAY,MAAM,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,WAAW,QAAQ,KAAK;AACjC;AA+BA,SAAS,wBAAwB,WAAW,SAAS;AACnD,QAAM;AAAA,IACJ,MAAM;AAAA,IACN;AAAA,IACA,QAAQ;AAAA,EACV,IAAI,WAAW,CAAC;AAChB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI;AACJ,SAAO,KAAK,OAAO,WAAS;AAC1B,QAAI,YAAY,EAAE,GAAG;AACnB,YAAM,SAAS,sBAAsB,OAAO,KAAK,SAAS;AAC1D,WAAK,UAAU,OAAO,KAAK;AAAA,IAC7B;AACA,WAAO,MAAM,WAAW,EAAE,EAAE;AAAA,EAC9B,CAAC,GAAG,IAAI,YAAU,SAAS,WAAW,QAAQ,KAAK,IAAI,MAAS,GAAG,qBAAqB,CAAC;AAC3F;AAWA,SAAS,YAAY,UAAU,CAAC,GAAG;AACjC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI;AAAA,EACN,IAAI;AACJ,SAAO,OAAO,WAAS,MAAM,WAAW,EAAE,MAAM,MAAM,EAAE,CAAC,CAAC,CAAC;AAC7D;AAWA,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI;AAAA,EACN,IAAI;AACJ,SAAO,OAAO,WAAS,MAAM,WAAW,EAAE,MAAM,MAAM,EAAE,MAAM,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;AACpF;AA6BA,SAAS,WAAW,KAAK,UAAU,CAAC,GAAG;AACrC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,IACF,IAAI;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,KAAK,OAAO,WAAS,MAAM,WAAW,CAAC,GAAG,IAAI,cAAY;AAC/D,QAAI,CAAC,IAAI,OAAQ,QAAO,CAAC;AACzB,UAAM,WAAW,CAAC;AAClB,eAAW,MAAM,KAAK;AACpB,YAAM,SAAS,UAAU,UAAU,IAAI,KAAK;AAC5C,UAAI,CAAC,YAAY,MAAM,EAAG,UAAS,KAAK,MAAM;AAAA,IAChD;AACA,WAAO;AAAA,EACT,CAAC,GAAG,8BAA8B,CAAC;AACrC;AACA,SAAS,sBAAsB,WAAW,SAAS;AACjD,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI;AAAA,IACJ;AAAA,EACF,IAAI,WAAW,CAAC;AAChB,SAAO,KAAK,qBAAqB,WAAW,GAAG,OAAO,WAAS;AAC7D,UAAM,mBAAmB,CAAC;AAC1B,UAAM,MAAM,EAAE,QAAQ,CAAC,IAAI,UAAU;AACnC,YAAM,SAAS,MAAM,WAAW,EAAE,EAAE;AACpC,UAAI,UAAU,QAAQ,KAAK,GAAG;AAC5B,yBAAiB,KAAK,WAAW,QAAQ,KAAK,CAAC;AAAA,MACjD;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,CAAC,GAAG,8BAA8B,CAAC;AACrC;AAWA,SAAS,oBAAoB,UAAU,CAAC,GAAG;AACzC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,IACF,IAAI;AAAA,EACN,IAAI;AACJ,SAAO,OAAO,WAAS,MAAM,MAAM,EAAE,MAAM;AAC7C;AAWA,SAAS,+BAA+B,WAAW,UAAU,CAAC,GAAG;AAC/D,QAAM,MAAM,QAAQ,OAAO;AAC3B,SAAO,KAAK,qBAAqB,IAAI,WAAW,GAAG,IAAI,WAAS,mBAAmB,OAAO,KAAK,SAAS,EAAE,MAAM,GAAG,qBAAqB,CAAC;AAC3I;AAWA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACtC,SAAO,SAAU,OAAO;AACtB,UAAM;AAAA,MACJ,KAAK;AAAA,QACH;AAAA,MACF,IAAI;AAAA,IACN,IAAI;AACJ,WAAO,MAAM,MAAM,EAAE;AAAA,EACvB;AACF;AAWA,SAAS,4BAA4B,WAAW,UAAU,CAAC,GAAG;AAC5D,SAAO,SAAU,OAAO;AACtB,UAAM,MAAM,QAAQ,OAAO;AAC3B,WAAO,mBAAmB,OAAO,KAAK,SAAS,EAAE;AAAA,EACnD;AACF;AACA,SAAS,cAAc,QAAQ,MAAM;AACnC,SAAO,IAAI,WAAS;AAClB,WAAO,MAAM,SAAS,IAAI,YAAU;AAClC,aAAO,kCACF,SACA,MAAM,WAAW,OAAO,KAAK,CAAC;AAAA,IAErC,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,mBAAmB,QAAQ,MAAM;AACxC,SAAO,IAAI,WAAS;AAClB,WAAO,OAAO,YAAY,MAAM,SAAS,IAAI,YAAU;AACrD,aAAO,CAAC,OAAO,KAAK,GAAG,kCAClB,SACA,MAAM,WAAW,OAAO,KAAK,CAAC,EAClC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AACA,IAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,aAAa,YAAY;AAAA,EAC3B,cAAc;AAChB,CAAC;AACD,SAAS,mBAAmB,UAAU,CAAC,GAAG;AACxC,QAAM;AAAA,IACJ,MAAM;AAAA,EACR,IAAI;AACJ,SAAO,SAAU,QAAQ;AACvB,WAAO,OAAO,KAAK,eAAe,CAAC,EAAE,KAAK,UAAU,QAAM,OAAO,KAAK,aAAa,IAAI;AAAA,MACrF;AAAA,IACF,CAAC,CAAC,CAAC,CAAC;AAAA,EACN;AACF;AACA,SAAS,gBAAgB,UAAU,CAAC,GAAG;AACrC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,IACF,IAAI;AAAA,EACN,IAAI;AACJ,SAAO,SAAU,OAAO;AACtB,WAAO,MAAM,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,EAC9C;AACF;AACA,IAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,kBAAkB,aAAa;AAAA,EACjC,cAAc,CAAC;AACjB,CAAC;AACD,SAAS,qBAAqB,UAAU,CAAC,GAAG;AAC1C,QAAM;AAAA,IACJ,MAAM;AAAA,EACR,IAAI;AACJ,SAAO,SAAU,QAAQ;AACvB,WAAO,OAAO,KAAK,gBAAgB,CAAC,EAAE,KAAK,UAAU,SAAO,OAAO,KAAK,WAAW,KAAK;AAAA,MACtF;AAAA,IACF,CAAC,CAAC,CAAC,CAAC;AAAA,EACN;AACF;AACA,SAAS,kBAAkB,UAAU,CAAC,GAAG;AACvC,QAAM;AAAA,IACJ,KAAK;AAAA,MACH;AAAA,IACF,IAAI;AAAA,EACN,IAAI;AACJ,SAAO,SAAU,OAAO;AACtB,UAAM,SAAS,CAAC;AAChB,eAAW,MAAM,aAAa,KAAK,GAAG;AACpC,YAAM,SAAS,MAAM,WAAW,EAAE,EAAE;AACpC,UAAI,QAAQ;AACV,eAAO,KAAK,MAAM;AAAA,MACpB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;", "names": ["id", "propsFactory"]}