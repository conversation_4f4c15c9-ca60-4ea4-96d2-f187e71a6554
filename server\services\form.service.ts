import { EFormStatus } from '../data/form.data';
import { DataAccess } from '../data/types';

export class FormService {
  dataAccess: DataAccess;
  constructor(dataAccess: DataAccess) {
    this.dataAccess = dataAccess;
  }

  async publishForm(schedule_id: string) {
    await this.dataAccess.form.updateForm(
      { schedule_id },
      {
        status: EFormStatus.PUBLISHED,
        form_publish_time: new Date().toISOString(),
      },
    );
  }

  async publishPassword(schedule_id: string) {
    await this.dataAccess.form.updateForm(
      { schedule_id },
      {
        status: EFormStatus.PASSWORD_PUBLISHED,
        password_publish_time: new Date().toISOString(),
      },
    );
  }

  async getSessionForms(session_id: string) {
    const session = await this.dataAccess.session.get({
      id: session_id,
    });
    if (!session) {
      return [];
    }
    const forms = await this.dataAccess.form.findForms({
      schedule_id: session.schedule_id,
    });
    return forms;
  }

  async getForms(schedule_id: string) {
    const forms = await this.dataAccess.form.findForms({ schedule_id });
    return forms;
  }

  async deleteForm(schedule_id: string, form_id: string) {
    await this.dataAccess.form.deleteForm(schedule_id, form_id);
  }
}
