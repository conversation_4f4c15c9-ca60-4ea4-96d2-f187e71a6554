/* eslint-disable @typescript-eslint/unbound-method */
/**
 * 服务器时间: 独立于系统的时钟
 */
export class ServerTime {
  /** Current time in seconds. */
  private _time: number;

  private _last: number; // in milliseconds
  private _startTick: number;

  // return a milliseconds resolution time
  private getTick = (function (): () => number {
    if (global.joyshell) {
      const ext = global.joyshell.GetAddon('osext');
      return () => ext.getTick();
    } else {
      return () => {
        const hr = process.hrtime();
        return hr[0] * 1000 + hr[1] / 1000000;
      };
    }
  })();

  constructor() {
    this._time = new Date().getTime() / 1000;
    this._last = 0;
    this._startTick = this.getTick();
  }

  /* Update current timestamp in seconds */
  public reset(now: number) {
    this._time = +now;
    this._last = 0;
    this._startTick = this.getTick();
  }

  /* Do NOT reset _last for reaching the new time smoothly */
  public sync(now: number) {
    this._time = +now;
    this._startTick = this.getTick();
  }

  /*
   * Return current time in seconds
   * */
  public now() {
    const elapsed_secs = (this.getTick() - this._startTick) / 1000;
    return Math.floor(this._time + elapsed_secs);
  }

  public timestamp() {
    return this.now();
  }

  // Return mononic "time" in milliseconds.
  // Caution, the time returned is inaccuracy.
  public jstimestamp() {
    const elapsed_milli = this.getTick() - this._startTick;
    const ms = Math.floor(this._time * 1000 + elapsed_milli);
    if (ms <= this._last) {
      return ++this._last;
    } else {
      this._last = ms;
    }
    return ms;
  }

  public date() {
    return new Date(this.now() * 1000);
  }
}

const defaultTime = new ServerTime();
export default defaultTime;

export const _Date = Date;
export const _now = Date.now;

const newDate = function () {
  if (arguments.length > 0) {
    // eslint-disable-next-line prefer-rest-params
    const args = Array.prototype.slice.call(arguments);
    return new (_Date as any)(...args);
  } else {
    return defaultTime.date();
  }
};

(newDate as any).now = function (): number {
  return defaultTime.now() * 1000;
};

(newDate as any).UTC = _Date.UTC;
(newDate as any).parse = _Date.parse;

if (typeof global !== 'undefined') {
  global['Date'] = newDate as DateConstructor;
}

if (typeof window !== 'undefined') {
  window['Date'] = newDate as DateConstructor;
}
