import { ipc<PERSON><PERSON><PERSON> } from "electron";

//
// Key Code Table:
// https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/keyCode
//
let lastkeypress_at = Date.now();

function onKeyDown(e) {
  const key = e.which;

  if (e.ctrl<PERSON>ey && key === 87) {
    // w
    lastkeypress_at = Date.now();
  }

  if (e.ctrl<PERSON>ey && key === 48) {
    // 0
    if (Date.now() - lastkeypress_at < 500) {
      if (ipcRenderer) {
        ipcRenderer.send("ipc-reload");
      }
    }
  }

  if (e.ctrlKey && key === 73) {
    // i
    if (Date.now() - lastkeypress_at < 500) {
      if (ipcRenderer) {
        ipcRenderer.send("ipc-open-devtools");
        e.preventDefault();
      }
    }
  }
}

export function listenKeyHook(){
  window.addEventListener("keydown", onKeyDown, true)
};
