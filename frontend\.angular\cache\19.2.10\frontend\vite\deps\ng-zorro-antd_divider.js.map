{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-divider.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { booleanAttribute, Input, ChangeDetectionStrategy, ViewEncapsulation, Component, NgModule } from '@angular/core';\nimport * as i1 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction NzDividerComponent_Conditional_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzText);\n  }\n}\nfunction NzDividerComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 0);\n    i0.ɵɵtemplate(1, NzDividerComponent_Conditional_0_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzText);\n  }\n}\nclass NzDividerComponent {\n  nzText;\n  nzType = 'horizontal';\n  nzOrientation = 'center';\n  nzVariant = 'solid';\n  nzDashed = false;\n  nzPlain = false;\n  static ɵfac = function NzDividerComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzDividerComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzDividerComponent,\n    selectors: [[\"nz-divider\"]],\n    hostAttrs: [1, \"ant-divider\"],\n    hostVars: 18,\n    hostBindings: function NzDividerComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-divider-horizontal\", ctx.nzType === \"horizontal\")(\"ant-divider-vertical\", ctx.nzType === \"vertical\")(\"ant-divider-with-text\", ctx.nzText)(\"ant-divider-plain\", ctx.nzPlain)(\"ant-divider-with-text-left\", ctx.nzText && ctx.nzOrientation === \"left\")(\"ant-divider-with-text-right\", ctx.nzText && ctx.nzOrientation === \"right\")(\"ant-divider-with-text-center\", ctx.nzText && ctx.nzOrientation === \"center\")(\"ant-divider-dashed\", ctx.nzDashed || ctx.nzVariant === \"dashed\")(\"ant-divider-dotted\", ctx.nzVariant === \"dotted\");\n      }\n    },\n    inputs: {\n      nzText: \"nzText\",\n      nzType: \"nzType\",\n      nzOrientation: \"nzOrientation\",\n      nzVariant: \"nzVariant\",\n      nzDashed: [2, \"nzDashed\", \"nzDashed\", booleanAttribute],\n      nzPlain: [2, \"nzPlain\", \"nzPlain\", booleanAttribute]\n    },\n    exportAs: [\"nzDivider\"],\n    decls: 1,\n    vars: 1,\n    consts: [[1, \"ant-divider-inner-text\"], [4, \"nzStringTemplateOutlet\"]],\n    template: function NzDividerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NzDividerComponent_Conditional_0_Template, 2, 1, \"span\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.nzText ? 0 : -1);\n      }\n    },\n    dependencies: [NzOutletModule, i1.NzStringTemplateOutletDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDividerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-divider',\n      exportAs: 'nzDivider',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (nzText) {\n      <span class=\"ant-divider-inner-text\">\n        <ng-container *nzStringTemplateOutlet=\"nzText\">{{ nzText }}</ng-container>\n      </span>\n    }\n  `,\n      host: {\n        class: 'ant-divider',\n        '[class.ant-divider-horizontal]': `nzType === 'horizontal'`,\n        '[class.ant-divider-vertical]': `nzType === 'vertical'`,\n        '[class.ant-divider-with-text]': `nzText`,\n        '[class.ant-divider-plain]': `nzPlain`,\n        '[class.ant-divider-with-text-left]': `nzText && nzOrientation === 'left'`,\n        '[class.ant-divider-with-text-right]': `nzText && nzOrientation === 'right'`,\n        '[class.ant-divider-with-text-center]': `nzText && nzOrientation === 'center'`,\n        '[class.ant-divider-dashed]': `nzDashed || nzVariant === 'dashed'`,\n        '[class.ant-divider-dotted]': `nzVariant === 'dotted'`\n      },\n      imports: [NzOutletModule]\n    }]\n  }], null, {\n    nzText: [{\n      type: Input\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzOrientation: [{\n      type: Input\n    }],\n    nzVariant: [{\n      type: Input\n    }],\n    nzDashed: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzPlain: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDividerModule {\n  static ɵfac = function NzDividerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzDividerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzDividerModule,\n    imports: [NzDividerComponent],\n    exports: [NzDividerComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzDividerComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzDividerComponent],\n      exports: [NzDividerComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzDividerComponent, NzDividerModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,gBAAgB,CAAC;AAClG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,MAAM;AAAA,EACvD;AACF;AACA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB;AAAA,EACA,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,WAAW,CAAC,GAAG,aAAa;AAAA,IAC5B,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0BAA0B,IAAI,WAAW,YAAY,EAAE,wBAAwB,IAAI,WAAW,UAAU,EAAE,yBAAyB,IAAI,MAAM,EAAE,qBAAqB,IAAI,OAAO,EAAE,8BAA8B,IAAI,UAAU,IAAI,kBAAkB,MAAM,EAAE,+BAA+B,IAAI,UAAU,IAAI,kBAAkB,OAAO,EAAE,gCAAgC,IAAI,UAAU,IAAI,kBAAkB,QAAQ,EAAE,sBAAsB,IAAI,YAAY,IAAI,cAAc,QAAQ,EAAE,sBAAsB,IAAI,cAAc,QAAQ;AAAA,MACxhB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,WAAW;AAAA,MACX,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,SAAS,CAAC,GAAG,WAAW,WAAW,gBAAgB;AAAA,IACrD;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,IACrE,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,QAAQ,CAAC;AAAA,MAC7E;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,SAAS,IAAI,EAAE;AAAA,MACtC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,IACjE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,kCAAkC;AAAA,QAClC,gCAAgC;AAAA,QAChC,iCAAiC;AAAA,QACjC,6BAA6B;AAAA,QAC7B,sCAAsC;AAAA,QACtC,uCAAuC;AAAA,QACvC,wCAAwC;AAAA,QACxC,8BAA8B;AAAA,QAC9B,8BAA8B;AAAA,MAChC;AAAA,MACA,SAAS,CAAC,cAAc;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,kBAAkB;AAAA,IAC5B,SAAS,CAAC,kBAAkB;AAAA,EAC9B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,kBAAkB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB;AAAA,MAC5B,SAAS,CAAC,kBAAkB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}