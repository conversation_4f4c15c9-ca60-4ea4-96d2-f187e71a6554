import { DbConnection } from './types';
import globalWriteQueue from '../utils/writeQueue';

interface IRoomData {
  id: string;
  name: string;
  sn_code: string;
  token: string;
  host: string;
  address: string;
  app_version: string;
}
export interface IRoomDataAccess {
  getAllRooms(): Promise<IRoomData[]>;
  create(room: Partial<IRoomData>): Promise<IRoomData>;
  update(room: Partial<IRoomData>): Promise<void>;
  delete(sn_codes: string[]): Promise<void>;
}
export class RoomDataAccess implements IRoomDataAccess {
  db: DbConnection;
  constructor(db: DbConnection) {
    this.db = db;
  }
  async getAllRooms(): Promise<IRoomData[]> {
    const rooms = await this.db.room.findAll({ raw: true });
    return rooms as IRoomData[];
  }

  async create(room: Partial<IRoomData>): Promise<IRoomData> {
    return globalWriteQueue.enqueue(async () => {
      const room_instance = await this.db.room.create(room as IRoomData);
      return {
        id: room_instance.id,
        name: room_instance.name,
        sn_code: room_instance.sn_code,
        token: room_instance.token,
        host: room_instance.host,
        address: room_instance.address,
        app_version: room_instance.app_version,
      };
    }) as Promise<IRoomData>;
  }

  async update(room: Partial<IRoomData>): Promise<void> {
    return globalWriteQueue.enqueue(() =>
      this.db.room.update(room, {
        where: {
          sn_code: room.sn_code,
        },
      }),
    ) as Promise<void>;
  }

  async delete(sn_codes: string[]): Promise<void> {
    return globalWriteQueue.enqueue(() =>
      this.db.room.destroy({
        where: {
          sn_code: sn_codes,
        },
      }),
    ) as Promise<void>;
  }
}
