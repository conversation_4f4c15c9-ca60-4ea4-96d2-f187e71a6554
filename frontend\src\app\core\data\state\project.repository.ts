import { Injectable } from '@angular/core';
import { createStore } from '@ngneat/elf';
import {
  selectAllEntities,
  selectEntity,
  selectEntityByPredicate,
  selectFirst,
  setEntities,
  upsertEntities,
  withEntities,
} from '@ngneat/elf-entities';
import { ExamHttpService } from '../../http/exam.service';
import {
  filter,
  map,
  Observable,
  OperatorFunction,
  share,
  shareReplay,
  tap,
} from 'rxjs';
import { CenterHttpService } from '../../http/center.service';

export interface IProjectData {
  id: string;
  name: string;
  start: string;
  end: string;
  test: 0 | 1;
}

const projectStore = createStore(
  { name: 'projects' },
  withEntities<IProjectData, 'id'>({ idKey: 'id' })
);

export interface ICenterData {
  center_id: string;
  center_name: string;
  center_address: string;
  project_id: string;
  project_name: string;
}
const centerStore = createStore(
  { name: 'center' },
  withEntities<ICenterData, 'center_id'>({ idKey: 'center_id' })
);
@Injectable({ providedIn: 'root' })
export class ProjectDataService {
  center$ = centerStore.pipe(selectFirst());

  testProject$ = projectStore.pipe(
    selectAllEntities(),
    filter((projects) => !!projects.length),
    map((projects) => {
      return projects.find((p) => p.test === 1);
    }),
    filter((p) => !!p) as OperatorFunction<
      IProjectData | undefined,
      IProjectData
    >
  ) as Observable<IProjectData>;

  formalProject$ = projectStore.pipe(
    selectAllEntities(),
    filter((projects) => !!projects.length),
    map((projects) => {
      return projects.find((p) => p.test === 0);
    }),
    filter((p) => !!p) as OperatorFunction<
      IProjectData | undefined,
      IProjectData
    >
  ) as Observable<IProjectData>;

  constructor(
    private examHttp: ExamHttpService,
    private centerHttp: CenterHttpService
  ) {}

  reqCenterLogin(code: string) {
    return this.centerHttp.login({ code }).pipe(
      tap((res) => {
        if (res.status === 'success') {
          const { center_name, center_address } = res.data;
          centerStore.update(
            setEntities([
              {
                center_name,
                center_address,
                center_id: '',
                project_id: '',
                project_name: '',
              },
            ])
          );
        }
      })
    );
  }

  reqCenterStatus() {
    return this.centerHttp.getCenterStatus().pipe(
      tap((res) => {
        if (res.status === 'success') {
          centerStore.update(setEntities([res.data]));
        }
      })
    );
  }

  reqProjects() {
    return this.examHttp.getProjects().pipe(
      tap((res) => {
        if (res.status === 'success') {
          projectStore.update(upsertEntities(res.data));
        }
      })
    );
  }
}
