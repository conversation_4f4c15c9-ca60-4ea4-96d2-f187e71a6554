import { Pipe, PipeTransform } from '@angular/core';
import { parseDateTime } from '@app/utils/parseDate';

@Pipe({ name: 'customDate', standalone: true })
export class CustomDatePipe implements PipeTransform {
  transform(value: any, status: string) {
    if (!value) {
      return '';
    }

    const date = parseDateTime(new Date(value));
    switch (status) {
      // 输入格式一般为UTC时间
      case 'yyyyMMdd':
        // 输出格式为"2016-06-18"
        return `${date.YYYY}-${date.MM}-${date.DD}`;
      case 'MMdd':
        // 输出格式为"06-18"
        return `${date.MM}-${date.DD}`;
      case 'dd':
        // 输出格式为"18"
        return `${date.DD}`;
      case 'HHmm':
        // 输出格式为"13:00"
        return `${date.HH}:${date.FF}`;
      // 输出格式为"2016-06-18 13:00:00"
      case 'date':
        return `${date.YYYY}-${date.MM}-${date.DD} ${date.HH}:${date.FF}:${date.SS}`;
      case 'date-minute':
        return `${date.YYYY}-${date.MM}-${date.DD} ${date.HH}:${date.FF}`;
      // 输入格式为时间戳
      case 'deltaTime': {
        const n = Math.abs(+value);
        const t = parseInt(n / 1000 + '', 10);
        const h = parseInt(t / 3600 + '', 10);
        const m = parseInt((t - h * 3600) / 60 + '', 10);
        const s = parseInt(t - h * 3600 - m * 60 + '', 10);
        const hh = (h + '').length > 1 ? h + '' : '0' + h;
        const mm = ('0' + m).slice(-2);
        const ss = ('0' + s).slice(-2);
        return `${hh}:${mm}:${ss}`;
      }
      default: {
        console.warn(
          'custom date pipe warning: invalid status arg',
          value,
          status
        );
        return null;
      }
    }
  }
}
