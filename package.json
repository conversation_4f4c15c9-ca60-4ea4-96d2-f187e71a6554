{"name": "test-center", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "postinstall": "pnpm -C frontend install &&  pnpm  -C app install && pnpm -C server install"}, "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "express-jwt": "^8.4.1", "fs-extra": "^11.2.0", "got": "11.8.6", "jsonwebtoken": "^9.0.2", "multer": "1.4.5-lts.1", "qrcode": "^1.5.3", "sequelize": "^6.35.2"}, "devDependencies": {"@types/gulp": "^4.0.17", "@types/jest": "^29.5.13", "@types/node": "^20.11.10", "@types/webpack": "^5.28.5", "copy-webpack-plugin": "^12.0.2", "electron": "^29.1.6", "esbuild": "^0.20.0", "gulp": "^4.0.2", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "5.3.3", "webpack": "^5.90.0", "webpack-merge": "^5.10.0", "sqlite3": "^5.1.6", "webpack-node-externals": "^3.0.0"}}