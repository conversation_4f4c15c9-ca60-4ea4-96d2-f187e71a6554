{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/platform.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/util.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/env.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/matrix.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/vector.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/tool/color.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/LRU.js", "../../../../../../node_modules/.pnpm/tslib@2.3.0/node_modules/tslib/tslib.es6.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/Transformable.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/animation/easing.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/curve.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/animation/cubicEasing.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/animation/Clip.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/svg/helper.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/animation/Animator.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/Point.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/BoundingRect.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/Eventful.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/contain/text.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/config.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/constants.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/Element.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/Displayable.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/bbox.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/PathProxy.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/helper/image.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/canvas/helper.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/contain/line.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/contain/cubic.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/contain/quadratic.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/contain/util.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/contain/arc.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/contain/windingLine.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/contain/path.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/Path.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/Image.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/TSpan.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/canvas/dashStyle.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/canvas/graphic.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/helper/parseText.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/helper/roundRect.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/helper/subPixelOptimize.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/shape/Rect.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/Text.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/graphic/CompoundPath.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/fourPointsTransform.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/core/dom.js", "../../../../../../node_modules/.pnpm/zrender@5.4.4/node_modules/zrender/lib/animation/requestAnimationFrame.js"], "sourcesContent": ["export var DEFAULT_FONT_SIZE = 12;\nexport var DEFAULT_FONT_FAMILY = 'sans-serif';\nexport var DEFAULT_FONT = DEFAULT_FONT_SIZE + \"px \" + DEFAULT_FONT_FAMILY;\nvar OFFSET = 20;\nvar SCALE = 100;\nvar defaultWidthMapStr = \"007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\\\\\WQb\\\\0FWLg\\\\bWb\\\\WQ\\\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\\\FFF5.5N\";\nfunction getTextWidthMap(mapStr) {\n  var map = {};\n  if (typeof JSON === 'undefined') {\n    return map;\n  }\n  for (var i = 0; i < mapStr.length; i++) {\n    var char = String.fromCharCode(i + 32);\n    var size = (mapStr.charCodeAt(i) - OFFSET) / SCALE;\n    map[char] = size;\n  }\n  return map;\n}\nexport var DEFAULT_TEXT_WIDTH_MAP = getTextWidthMap(defaultWidthMapStr);\nexport var platformApi = {\n  createCanvas: function () {\n    return typeof document !== 'undefined' && document.createElement('canvas');\n  },\n  measureText: function () {\n    var _ctx;\n    var _cachedFont;\n    return function (text, font) {\n      if (!_ctx) {\n        var canvas = platformApi.createCanvas();\n        _ctx = canvas && canvas.getContext('2d');\n      }\n      if (_ctx) {\n        if (_cachedFont !== font) {\n          _cachedFont = _ctx.font = font || DEFAULT_FONT;\n        }\n        return _ctx.measureText(text);\n      } else {\n        text = text || '';\n        font = font || DEFAULT_FONT;\n        var res = /(\\d+)px/.exec(font);\n        var fontSize = res && +res[1] || DEFAULT_FONT_SIZE;\n        var width = 0;\n        if (font.indexOf('mono') >= 0) {\n          width = fontSize * text.length;\n        } else {\n          for (var i = 0; i < text.length; i++) {\n            var preCalcWidth = DEFAULT_TEXT_WIDTH_MAP[text[i]];\n            width += preCalcWidth == null ? fontSize : preCalcWidth * fontSize;\n          }\n        }\n        return {\n          width: width\n        };\n      }\n    };\n  }(),\n  loadImage: function (src, onload, onerror) {\n    var image = new Image();\n    image.onload = onload;\n    image.onerror = onerror;\n    image.src = src;\n    return image;\n  }\n};\nexport function setPlatformAPI(newPlatformApis) {\n  for (var key in platformApi) {\n    if (newPlatformApis[key]) {\n      platformApi[key] = newPlatformApis[key];\n    }\n  }\n}", "import { platformApi } from './platform.js';\nvar BUILTIN_OBJECT = reduce(['Function', 'RegExp', 'Date', 'Error', 'CanvasGradient', 'CanvasPattern', 'Image', 'Canvas'], function (obj, val) {\n  obj['[object ' + val + ']'] = true;\n  return obj;\n}, {});\nvar TYPED_ARRAY = reduce(['Int8', 'Uint8', 'Uint8Clamped', 'Int16', 'Uint16', 'Int32', 'Uint32', 'Float32', 'Float64'], function (obj, val) {\n  obj['[object ' + val + 'Array]'] = true;\n  return obj;\n}, {});\nvar objToString = Object.prototype.toString;\nvar arrayProto = Array.prototype;\nvar nativeForEach = arrayProto.forEach;\nvar nativeFilter = arrayProto.filter;\nvar nativeSlice = arrayProto.slice;\nvar nativeMap = arrayProto.map;\nvar ctorFunction = function () {}.constructor;\nvar protoFunction = ctorFunction ? ctorFunction.prototype : null;\nvar protoKey = '__proto__';\nvar idStart = 0x0907;\nexport function guid() {\n  return idStart++;\n}\nexport function logError() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  if (typeof console !== 'undefined') {\n    console.error.apply(console, args);\n  }\n}\nexport function clone(source) {\n  if (source == null || typeof source !== 'object') {\n    return source;\n  }\n  var result = source;\n  var typeStr = objToString.call(source);\n  if (typeStr === '[object Array]') {\n    if (!isPrimitive(source)) {\n      result = [];\n      for (var i = 0, len = source.length; i < len; i++) {\n        result[i] = clone(source[i]);\n      }\n    }\n  } else if (TYPED_ARRAY[typeStr]) {\n    if (!isPrimitive(source)) {\n      var Ctor = source.constructor;\n      if (Ctor.from) {\n        result = Ctor.from(source);\n      } else {\n        result = new Ctor(source.length);\n        for (var i = 0, len = source.length; i < len; i++) {\n          result[i] = source[i];\n        }\n      }\n    }\n  } else if (!BUILTIN_OBJECT[typeStr] && !isPrimitive(source) && !isDom(source)) {\n    result = {};\n    for (var key in source) {\n      if (source.hasOwnProperty(key) && key !== protoKey) {\n        result[key] = clone(source[key]);\n      }\n    }\n  }\n  return result;\n}\nexport function merge(target, source, overwrite) {\n  if (!isObject(source) || !isObject(target)) {\n    return overwrite ? clone(source) : target;\n  }\n  for (var key in source) {\n    if (source.hasOwnProperty(key) && key !== protoKey) {\n      var targetProp = target[key];\n      var sourceProp = source[key];\n      if (isObject(sourceProp) && isObject(targetProp) && !isArray(sourceProp) && !isArray(targetProp) && !isDom(sourceProp) && !isDom(targetProp) && !isBuiltInObject(sourceProp) && !isBuiltInObject(targetProp) && !isPrimitive(sourceProp) && !isPrimitive(targetProp)) {\n        merge(targetProp, sourceProp, overwrite);\n      } else if (overwrite || !(key in target)) {\n        target[key] = clone(source[key]);\n      }\n    }\n  }\n  return target;\n}\nexport function mergeAll(targetAndSources, overwrite) {\n  var result = targetAndSources[0];\n  for (var i = 1, len = targetAndSources.length; i < len; i++) {\n    result = merge(result, targetAndSources[i], overwrite);\n  }\n  return result;\n}\nexport function extend(target, source) {\n  if (Object.assign) {\n    Object.assign(target, source);\n  } else {\n    for (var key in source) {\n      if (source.hasOwnProperty(key) && key !== protoKey) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n}\nexport function defaults(target, source, overlay) {\n  var keysArr = keys(source);\n  for (var i = 0; i < keysArr.length; i++) {\n    var key = keysArr[i];\n    if (overlay ? source[key] != null : target[key] == null) {\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nexport var createCanvas = platformApi.createCanvas;\nexport function indexOf(array, value) {\n  if (array) {\n    if (array.indexOf) {\n      return array.indexOf(value);\n    }\n    for (var i = 0, len = array.length; i < len; i++) {\n      if (array[i] === value) {\n        return i;\n      }\n    }\n  }\n  return -1;\n}\nexport function inherits(clazz, baseClazz) {\n  var clazzPrototype = clazz.prototype;\n  function F() {}\n  F.prototype = baseClazz.prototype;\n  clazz.prototype = new F();\n  for (var prop in clazzPrototype) {\n    if (clazzPrototype.hasOwnProperty(prop)) {\n      clazz.prototype[prop] = clazzPrototype[prop];\n    }\n  }\n  clazz.prototype.constructor = clazz;\n  clazz.superClass = baseClazz;\n}\nexport function mixin(target, source, override) {\n  target = 'prototype' in target ? target.prototype : target;\n  source = 'prototype' in source ? source.prototype : source;\n  if (Object.getOwnPropertyNames) {\n    var keyList = Object.getOwnPropertyNames(source);\n    for (var i = 0; i < keyList.length; i++) {\n      var key = keyList[i];\n      if (key !== 'constructor') {\n        if (override ? source[key] != null : target[key] == null) {\n          target[key] = source[key];\n        }\n      }\n    }\n  } else {\n    defaults(target, source, override);\n  }\n}\nexport function isArrayLike(data) {\n  if (!data) {\n    return false;\n  }\n  if (typeof data === 'string') {\n    return false;\n  }\n  return typeof data.length === 'number';\n}\nexport function each(arr, cb, context) {\n  if (!(arr && cb)) {\n    return;\n  }\n  if (arr.forEach && arr.forEach === nativeForEach) {\n    arr.forEach(cb, context);\n  } else if (arr.length === +arr.length) {\n    for (var i = 0, len = arr.length; i < len; i++) {\n      cb.call(context, arr[i], i, arr);\n    }\n  } else {\n    for (var key in arr) {\n      if (arr.hasOwnProperty(key)) {\n        cb.call(context, arr[key], key, arr);\n      }\n    }\n  }\n}\nexport function map(arr, cb, context) {\n  if (!arr) {\n    return [];\n  }\n  if (!cb) {\n    return slice(arr);\n  }\n  if (arr.map && arr.map === nativeMap) {\n    return arr.map(cb, context);\n  } else {\n    var result = [];\n    for (var i = 0, len = arr.length; i < len; i++) {\n      result.push(cb.call(context, arr[i], i, arr));\n    }\n    return result;\n  }\n}\nexport function reduce(arr, cb, memo, context) {\n  if (!(arr && cb)) {\n    return;\n  }\n  for (var i = 0, len = arr.length; i < len; i++) {\n    memo = cb.call(context, memo, arr[i], i, arr);\n  }\n  return memo;\n}\nexport function filter(arr, cb, context) {\n  if (!arr) {\n    return [];\n  }\n  if (!cb) {\n    return slice(arr);\n  }\n  if (arr.filter && arr.filter === nativeFilter) {\n    return arr.filter(cb, context);\n  } else {\n    var result = [];\n    for (var i = 0, len = arr.length; i < len; i++) {\n      if (cb.call(context, arr[i], i, arr)) {\n        result.push(arr[i]);\n      }\n    }\n    return result;\n  }\n}\nexport function find(arr, cb, context) {\n  if (!(arr && cb)) {\n    return;\n  }\n  for (var i = 0, len = arr.length; i < len; i++) {\n    if (cb.call(context, arr[i], i, arr)) {\n      return arr[i];\n    }\n  }\n}\nexport function keys(obj) {\n  if (!obj) {\n    return [];\n  }\n  if (Object.keys) {\n    return Object.keys(obj);\n  }\n  var keyList = [];\n  for (var key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      keyList.push(key);\n    }\n  }\n  return keyList;\n}\nfunction bindPolyfill(func, context) {\n  var args = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    args[_i - 2] = arguments[_i];\n  }\n  return function () {\n    return func.apply(context, args.concat(nativeSlice.call(arguments)));\n  };\n}\nexport var bind = protoFunction && isFunction(protoFunction.bind) ? protoFunction.call.bind(protoFunction.bind) : bindPolyfill;\nfunction curry(func) {\n  var args = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    args[_i - 1] = arguments[_i];\n  }\n  return function () {\n    return func.apply(this, args.concat(nativeSlice.call(arguments)));\n  };\n}\nexport { curry };\nexport function isArray(value) {\n  if (Array.isArray) {\n    return Array.isArray(value);\n  }\n  return objToString.call(value) === '[object Array]';\n}\nexport function isFunction(value) {\n  return typeof value === 'function';\n}\nexport function isString(value) {\n  return typeof value === 'string';\n}\nexport function isStringSafe(value) {\n  return objToString.call(value) === '[object String]';\n}\nexport function isNumber(value) {\n  return typeof value === 'number';\n}\nexport function isObject(value) {\n  var type = typeof value;\n  return type === 'function' || !!value && type === 'object';\n}\nexport function isBuiltInObject(value) {\n  return !!BUILTIN_OBJECT[objToString.call(value)];\n}\nexport function isTypedArray(value) {\n  return !!TYPED_ARRAY[objToString.call(value)];\n}\nexport function isDom(value) {\n  return typeof value === 'object' && typeof value.nodeType === 'number' && typeof value.ownerDocument === 'object';\n}\nexport function isGradientObject(value) {\n  return value.colorStops != null;\n}\nexport function isImagePatternObject(value) {\n  return value.image != null;\n}\nexport function isRegExp(value) {\n  return objToString.call(value) === '[object RegExp]';\n}\nexport function eqNaN(value) {\n  return value !== value;\n}\nexport function retrieve() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  for (var i = 0, len = args.length; i < len; i++) {\n    if (args[i] != null) {\n      return args[i];\n    }\n  }\n}\nexport function retrieve2(value0, value1) {\n  return value0 != null ? value0 : value1;\n}\nexport function retrieve3(value0, value1, value2) {\n  return value0 != null ? value0 : value1 != null ? value1 : value2;\n}\nexport function slice(arr) {\n  var args = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    args[_i - 1] = arguments[_i];\n  }\n  return nativeSlice.apply(arr, args);\n}\nexport function normalizeCssArray(val) {\n  if (typeof val === 'number') {\n    return [val, val, val, val];\n  }\n  var len = val.length;\n  if (len === 2) {\n    return [val[0], val[1], val[0], val[1]];\n  } else if (len === 3) {\n    return [val[0], val[1], val[2], val[1]];\n  }\n  return val;\n}\nexport function assert(condition, message) {\n  if (!condition) {\n    throw new Error(message);\n  }\n}\nexport function trim(str) {\n  if (str == null) {\n    return null;\n  } else if (typeof str.trim === 'function') {\n    return str.trim();\n  } else {\n    return str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n  }\n}\nvar primitiveKey = '__ec_primitive__';\nexport function setAsPrimitive(obj) {\n  obj[primitiveKey] = true;\n}\nexport function isPrimitive(obj) {\n  return obj[primitiveKey];\n}\nvar MapPolyfill = function () {\n  function MapPolyfill() {\n    this.data = {};\n  }\n  MapPolyfill.prototype[\"delete\"] = function (key) {\n    var existed = this.has(key);\n    if (existed) {\n      delete this.data[key];\n    }\n    return existed;\n  };\n  MapPolyfill.prototype.has = function (key) {\n    return this.data.hasOwnProperty(key);\n  };\n  MapPolyfill.prototype.get = function (key) {\n    return this.data[key];\n  };\n  MapPolyfill.prototype.set = function (key, value) {\n    this.data[key] = value;\n    return this;\n  };\n  MapPolyfill.prototype.keys = function () {\n    return keys(this.data);\n  };\n  MapPolyfill.prototype.forEach = function (callback) {\n    var data = this.data;\n    for (var key in data) {\n      if (data.hasOwnProperty(key)) {\n        callback(data[key], key);\n      }\n    }\n  };\n  return MapPolyfill;\n}();\nvar isNativeMapSupported = typeof Map === 'function';\nfunction maybeNativeMap() {\n  return isNativeMapSupported ? new Map() : new MapPolyfill();\n}\nvar HashMap = function () {\n  function HashMap(obj) {\n    var isArr = isArray(obj);\n    this.data = maybeNativeMap();\n    var thisMap = this;\n    obj instanceof HashMap ? obj.each(visit) : obj && each(obj, visit);\n    function visit(value, key) {\n      isArr ? thisMap.set(value, key) : thisMap.set(key, value);\n    }\n  }\n  HashMap.prototype.hasKey = function (key) {\n    return this.data.has(key);\n  };\n  HashMap.prototype.get = function (key) {\n    return this.data.get(key);\n  };\n  HashMap.prototype.set = function (key, value) {\n    this.data.set(key, value);\n    return value;\n  };\n  HashMap.prototype.each = function (cb, context) {\n    this.data.forEach(function (value, key) {\n      cb.call(context, value, key);\n    });\n  };\n  HashMap.prototype.keys = function () {\n    var keys = this.data.keys();\n    return isNativeMapSupported ? Array.from(keys) : keys;\n  };\n  HashMap.prototype.removeKey = function (key) {\n    this.data[\"delete\"](key);\n  };\n  return HashMap;\n}();\nexport { HashMap };\nexport function createHashMap(obj) {\n  return new HashMap(obj);\n}\nexport function concatArray(a, b) {\n  var newArray = new a.constructor(a.length + b.length);\n  for (var i = 0; i < a.length; i++) {\n    newArray[i] = a[i];\n  }\n  var offset = a.length;\n  for (var i = 0; i < b.length; i++) {\n    newArray[i + offset] = b[i];\n  }\n  return newArray;\n}\nexport function createObject(proto, properties) {\n  var obj;\n  if (Object.create) {\n    obj = Object.create(proto);\n  } else {\n    var StyleCtor = function () {};\n    StyleCtor.prototype = proto;\n    obj = new StyleCtor();\n  }\n  if (properties) {\n    extend(obj, properties);\n  }\n  return obj;\n}\nexport function disableUserSelect(dom) {\n  var domStyle = dom.style;\n  domStyle.webkitUserSelect = 'none';\n  domStyle.userSelect = 'none';\n  domStyle.webkitTapHighlightColor = 'rgba(0,0,0,0)';\n  domStyle['-webkit-touch-callout'] = 'none';\n}\nexport function hasOwn(own, prop) {\n  return own.hasOwnProperty(prop);\n}\nexport function noop() {}\nexport var RADIAN_TO_DEGREE = 180 / Math.PI;", "var Browser = function () {\n  function Browser() {\n    this.firefox = false;\n    this.ie = false;\n    this.edge = false;\n    this.newEdge = false;\n    this.weChat = false;\n  }\n  return Browser;\n}();\nvar Env = function () {\n  function Env() {\n    this.browser = new Browser();\n    this.node = false;\n    this.wxa = false;\n    this.worker = false;\n    this.svgSupported = false;\n    this.touchEventsSupported = false;\n    this.pointerEventsSupported = false;\n    this.domSupported = false;\n    this.transformSupported = false;\n    this.transform3dSupported = false;\n    this.hasGlobalWindow = typeof window !== 'undefined';\n  }\n  return Env;\n}();\nvar env = new Env();\nif (typeof wx === 'object' && typeof wx.getSystemInfoSync === 'function') {\n  env.wxa = true;\n  env.touchEventsSupported = true;\n} else if (typeof document === 'undefined' && typeof self !== 'undefined') {\n  env.worker = true;\n} else if (typeof navigator === 'undefined') {\n  env.node = true;\n  env.svgSupported = true;\n} else {\n  detect(navigator.userAgent, env);\n}\nfunction detect(ua, env) {\n  var browser = env.browser;\n  var firefox = ua.match(/Firefox\\/([\\d.]+)/);\n  var ie = ua.match(/MSIE\\s([\\d.]+)/) || ua.match(/Trident\\/.+?rv:(([\\d.]+))/);\n  var edge = ua.match(/Edge?\\/([\\d.]+)/);\n  var weChat = /micromessenger/i.test(ua);\n  if (firefox) {\n    browser.firefox = true;\n    browser.version = firefox[1];\n  }\n  if (ie) {\n    browser.ie = true;\n    browser.version = ie[1];\n  }\n  if (edge) {\n    browser.edge = true;\n    browser.version = edge[1];\n    browser.newEdge = +edge[1].split('.')[0] > 18;\n  }\n  if (weChat) {\n    browser.weChat = true;\n  }\n  env.svgSupported = typeof SVGRect !== 'undefined';\n  env.touchEventsSupported = 'ontouchstart' in window && !browser.ie && !browser.edge;\n  env.pointerEventsSupported = 'onpointerdown' in window && (browser.edge || browser.ie && +browser.version >= 11);\n  env.domSupported = typeof document !== 'undefined';\n  var style = document.documentElement.style;\n  env.transform3dSupported = (browser.ie && 'transition' in style || browser.edge || 'WebKitCSSMatrix' in window && 'm11' in new WebKitCSSMatrix() || 'MozPerspective' in style) && !('OTransition' in style);\n  env.transformSupported = env.transform3dSupported || browser.ie && +browser.version >= 9;\n}\nexport default env;", "export function create() {\n  return [1, 0, 0, 1, 0, 0];\n}\nexport function identity(out) {\n  out[0] = 1;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 1;\n  out[4] = 0;\n  out[5] = 0;\n  return out;\n}\nexport function copy(out, m) {\n  out[0] = m[0];\n  out[1] = m[1];\n  out[2] = m[2];\n  out[3] = m[3];\n  out[4] = m[4];\n  out[5] = m[5];\n  return out;\n}\nexport function mul(out, m1, m2) {\n  var out0 = m1[0] * m2[0] + m1[2] * m2[1];\n  var out1 = m1[1] * m2[0] + m1[3] * m2[1];\n  var out2 = m1[0] * m2[2] + m1[2] * m2[3];\n  var out3 = m1[1] * m2[2] + m1[3] * m2[3];\n  var out4 = m1[0] * m2[4] + m1[2] * m2[5] + m1[4];\n  var out5 = m1[1] * m2[4] + m1[3] * m2[5] + m1[5];\n  out[0] = out0;\n  out[1] = out1;\n  out[2] = out2;\n  out[3] = out3;\n  out[4] = out4;\n  out[5] = out5;\n  return out;\n}\nexport function translate(out, a, v) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  out[4] = a[4] + v[0];\n  out[5] = a[5] + v[1];\n  return out;\n}\nexport function rotate(out, a, rad) {\n  var aa = a[0];\n  var ac = a[2];\n  var atx = a[4];\n  var ab = a[1];\n  var ad = a[3];\n  var aty = a[5];\n  var st = Math.sin(rad);\n  var ct = Math.cos(rad);\n  out[0] = aa * ct + ab * st;\n  out[1] = -aa * st + ab * ct;\n  out[2] = ac * ct + ad * st;\n  out[3] = -ac * st + ct * ad;\n  out[4] = ct * atx + st * aty;\n  out[5] = ct * aty - st * atx;\n  return out;\n}\nexport function scale(out, a, v) {\n  var vx = v[0];\n  var vy = v[1];\n  out[0] = a[0] * vx;\n  out[1] = a[1] * vy;\n  out[2] = a[2] * vx;\n  out[3] = a[3] * vy;\n  out[4] = a[4] * vx;\n  out[5] = a[5] * vy;\n  return out;\n}\nexport function invert(out, a) {\n  var aa = a[0];\n  var ac = a[2];\n  var atx = a[4];\n  var ab = a[1];\n  var ad = a[3];\n  var aty = a[5];\n  var det = aa * ad - ab * ac;\n  if (!det) {\n    return null;\n  }\n  det = 1.0 / det;\n  out[0] = ad * det;\n  out[1] = -ab * det;\n  out[2] = -ac * det;\n  out[3] = aa * det;\n  out[4] = (ac * aty - ad * atx) * det;\n  out[5] = (ab * atx - aa * aty) * det;\n  return out;\n}\nexport function clone(a) {\n  var b = create();\n  copy(b, a);\n  return b;\n}", "export function create(x, y) {\n  if (x == null) {\n    x = 0;\n  }\n  if (y == null) {\n    y = 0;\n  }\n  return [x, y];\n}\nexport function copy(out, v) {\n  out[0] = v[0];\n  out[1] = v[1];\n  return out;\n}\nexport function clone(v) {\n  return [v[0], v[1]];\n}\nexport function set(out, a, b) {\n  out[0] = a;\n  out[1] = b;\n  return out;\n}\nexport function add(out, v1, v2) {\n  out[0] = v1[0] + v2[0];\n  out[1] = v1[1] + v2[1];\n  return out;\n}\nexport function scaleAndAdd(out, v1, v2, a) {\n  out[0] = v1[0] + v2[0] * a;\n  out[1] = v1[1] + v2[1] * a;\n  return out;\n}\nexport function sub(out, v1, v2) {\n  out[0] = v1[0] - v2[0];\n  out[1] = v1[1] - v2[1];\n  return out;\n}\nexport function len(v) {\n  return Math.sqrt(lenSquare(v));\n}\nexport var length = len;\nexport function lenSquare(v) {\n  return v[0] * v[0] + v[1] * v[1];\n}\nexport var lengthSquare = lenSquare;\nexport function mul(out, v1, v2) {\n  out[0] = v1[0] * v2[0];\n  out[1] = v1[1] * v2[1];\n  return out;\n}\nexport function div(out, v1, v2) {\n  out[0] = v1[0] / v2[0];\n  out[1] = v1[1] / v2[1];\n  return out;\n}\nexport function dot(v1, v2) {\n  return v1[0] * v2[0] + v1[1] * v2[1];\n}\nexport function scale(out, v, s) {\n  out[0] = v[0] * s;\n  out[1] = v[1] * s;\n  return out;\n}\nexport function normalize(out, v) {\n  var d = len(v);\n  if (d === 0) {\n    out[0] = 0;\n    out[1] = 0;\n  } else {\n    out[0] = v[0] / d;\n    out[1] = v[1] / d;\n  }\n  return out;\n}\nexport function distance(v1, v2) {\n  return Math.sqrt((v1[0] - v2[0]) * (v1[0] - v2[0]) + (v1[1] - v2[1]) * (v1[1] - v2[1]));\n}\nexport var dist = distance;\nexport function distanceSquare(v1, v2) {\n  return (v1[0] - v2[0]) * (v1[0] - v2[0]) + (v1[1] - v2[1]) * (v1[1] - v2[1]);\n}\nexport var distSquare = distanceSquare;\nexport function negate(out, v) {\n  out[0] = -v[0];\n  out[1] = -v[1];\n  return out;\n}\nexport function lerp(out, v1, v2, t) {\n  out[0] = v1[0] + t * (v2[0] - v1[0]);\n  out[1] = v1[1] + t * (v2[1] - v1[1]);\n  return out;\n}\nexport function applyTransform(out, v, m) {\n  var x = v[0];\n  var y = v[1];\n  out[0] = m[0] * x + m[2] * y + m[4];\n  out[1] = m[1] * x + m[3] * y + m[5];\n  return out;\n}\nexport function min(out, v1, v2) {\n  out[0] = Math.min(v1[0], v2[0]);\n  out[1] = Math.min(v1[1], v2[1]);\n  return out;\n}\nexport function max(out, v1, v2) {\n  out[0] = Math.max(v1[0], v2[0]);\n  out[1] = Math.max(v1[1], v2[1]);\n  return out;\n}", "import LRU from '../core/LRU.js';\nvar kCSSColorTable = {\n  'transparent': [0, 0, 0, 0],\n  'aliceblue': [240, 248, 255, 1],\n  'antiquewhite': [250, 235, 215, 1],\n  'aqua': [0, 255, 255, 1],\n  'aquamarine': [127, 255, 212, 1],\n  'azure': [240, 255, 255, 1],\n  'beige': [245, 245, 220, 1],\n  'bisque': [255, 228, 196, 1],\n  'black': [0, 0, 0, 1],\n  'blanchedalmond': [255, 235, 205, 1],\n  'blue': [0, 0, 255, 1],\n  'blueviolet': [138, 43, 226, 1],\n  'brown': [165, 42, 42, 1],\n  'burlywood': [222, 184, 135, 1],\n  'cadetblue': [95, 158, 160, 1],\n  'chartreuse': [127, 255, 0, 1],\n  'chocolate': [210, 105, 30, 1],\n  'coral': [255, 127, 80, 1],\n  'cornflowerblue': [100, 149, 237, 1],\n  'cornsilk': [255, 248, 220, 1],\n  'crimson': [220, 20, 60, 1],\n  'cyan': [0, 255, 255, 1],\n  'darkblue': [0, 0, 139, 1],\n  'darkcyan': [0, 139, 139, 1],\n  'darkgoldenrod': [184, 134, 11, 1],\n  'darkgray': [169, 169, 169, 1],\n  'darkgreen': [0, 100, 0, 1],\n  'darkgrey': [169, 169, 169, 1],\n  'darkkhaki': [189, 183, 107, 1],\n  'darkmagenta': [139, 0, 139, 1],\n  'darkolivegreen': [85, 107, 47, 1],\n  'darkorange': [255, 140, 0, 1],\n  'darkorchid': [153, 50, 204, 1],\n  'darkred': [139, 0, 0, 1],\n  'darksalmon': [233, 150, 122, 1],\n  'darkseagreen': [143, 188, 143, 1],\n  'darkslateblue': [72, 61, 139, 1],\n  'darkslategray': [47, 79, 79, 1],\n  'darkslategrey': [47, 79, 79, 1],\n  'darkturquoise': [0, 206, 209, 1],\n  'darkviolet': [148, 0, 211, 1],\n  'deeppink': [255, 20, 147, 1],\n  'deepskyblue': [0, 191, 255, 1],\n  'dimgray': [105, 105, 105, 1],\n  'dimgrey': [105, 105, 105, 1],\n  'dodgerblue': [30, 144, 255, 1],\n  'firebrick': [178, 34, 34, 1],\n  'floralwhite': [255, 250, 240, 1],\n  'forestgreen': [34, 139, 34, 1],\n  'fuchsia': [255, 0, 255, 1],\n  'gainsboro': [220, 220, 220, 1],\n  'ghostwhite': [248, 248, 255, 1],\n  'gold': [255, 215, 0, 1],\n  'goldenrod': [218, 165, 32, 1],\n  'gray': [128, 128, 128, 1],\n  'green': [0, 128, 0, 1],\n  'greenyellow': [173, 255, 47, 1],\n  'grey': [128, 128, 128, 1],\n  'honeydew': [240, 255, 240, 1],\n  'hotpink': [255, 105, 180, 1],\n  'indianred': [205, 92, 92, 1],\n  'indigo': [75, 0, 130, 1],\n  'ivory': [255, 255, 240, 1],\n  'khaki': [240, 230, 140, 1],\n  'lavender': [230, 230, 250, 1],\n  'lavenderblush': [255, 240, 245, 1],\n  'lawngreen': [124, 252, 0, 1],\n  'lemonchiffon': [255, 250, 205, 1],\n  'lightblue': [173, 216, 230, 1],\n  'lightcoral': [240, 128, 128, 1],\n  'lightcyan': [224, 255, 255, 1],\n  'lightgoldenrodyellow': [250, 250, 210, 1],\n  'lightgray': [211, 211, 211, 1],\n  'lightgreen': [144, 238, 144, 1],\n  'lightgrey': [211, 211, 211, 1],\n  'lightpink': [255, 182, 193, 1],\n  'lightsalmon': [255, 160, 122, 1],\n  'lightseagreen': [32, 178, 170, 1],\n  'lightskyblue': [135, 206, 250, 1],\n  'lightslategray': [119, 136, 153, 1],\n  'lightslategrey': [119, 136, 153, 1],\n  'lightsteelblue': [176, 196, 222, 1],\n  'lightyellow': [255, 255, 224, 1],\n  'lime': [0, 255, 0, 1],\n  'limegreen': [50, 205, 50, 1],\n  'linen': [250, 240, 230, 1],\n  'magenta': [255, 0, 255, 1],\n  'maroon': [128, 0, 0, 1],\n  'mediumaquamarine': [102, 205, 170, 1],\n  'mediumblue': [0, 0, 205, 1],\n  'mediumorchid': [186, 85, 211, 1],\n  'mediumpurple': [147, 112, 219, 1],\n  'mediumseagreen': [60, 179, 113, 1],\n  'mediumslateblue': [123, 104, 238, 1],\n  'mediumspringgreen': [0, 250, 154, 1],\n  'mediumturquoise': [72, 209, 204, 1],\n  'mediumvioletred': [199, 21, 133, 1],\n  'midnightblue': [25, 25, 112, 1],\n  'mintcream': [245, 255, 250, 1],\n  'mistyrose': [255, 228, 225, 1],\n  'moccasin': [255, 228, 181, 1],\n  'navajowhite': [255, 222, 173, 1],\n  'navy': [0, 0, 128, 1],\n  'oldlace': [253, 245, 230, 1],\n  'olive': [128, 128, 0, 1],\n  'olivedrab': [107, 142, 35, 1],\n  'orange': [255, 165, 0, 1],\n  'orangered': [255, 69, 0, 1],\n  'orchid': [218, 112, 214, 1],\n  'palegoldenrod': [238, 232, 170, 1],\n  'palegreen': [152, 251, 152, 1],\n  'paleturquoise': [175, 238, 238, 1],\n  'palevioletred': [219, 112, 147, 1],\n  'papayawhip': [255, 239, 213, 1],\n  'peachpuff': [255, 218, 185, 1],\n  'peru': [205, 133, 63, 1],\n  'pink': [255, 192, 203, 1],\n  'plum': [221, 160, 221, 1],\n  'powderblue': [176, 224, 230, 1],\n  'purple': [128, 0, 128, 1],\n  'red': [255, 0, 0, 1],\n  'rosybrown': [188, 143, 143, 1],\n  'royalblue': [65, 105, 225, 1],\n  'saddlebrown': [139, 69, 19, 1],\n  'salmon': [250, 128, 114, 1],\n  'sandybrown': [244, 164, 96, 1],\n  'seagreen': [46, 139, 87, 1],\n  'seashell': [255, 245, 238, 1],\n  'sienna': [160, 82, 45, 1],\n  'silver': [192, 192, 192, 1],\n  'skyblue': [135, 206, 235, 1],\n  'slateblue': [106, 90, 205, 1],\n  'slategray': [112, 128, 144, 1],\n  'slategrey': [112, 128, 144, 1],\n  'snow': [255, 250, 250, 1],\n  'springgreen': [0, 255, 127, 1],\n  'steelblue': [70, 130, 180, 1],\n  'tan': [210, 180, 140, 1],\n  'teal': [0, 128, 128, 1],\n  'thistle': [216, 191, 216, 1],\n  'tomato': [255, 99, 71, 1],\n  'turquoise': [64, 224, 208, 1],\n  'violet': [238, 130, 238, 1],\n  'wheat': [245, 222, 179, 1],\n  'white': [255, 255, 255, 1],\n  'whitesmoke': [245, 245, 245, 1],\n  'yellow': [255, 255, 0, 1],\n  'yellowgreen': [154, 205, 50, 1]\n};\nfunction clampCssByte(i) {\n  i = Math.round(i);\n  return i < 0 ? 0 : i > 255 ? 255 : i;\n}\nfunction clampCssAngle(i) {\n  i = Math.round(i);\n  return i < 0 ? 0 : i > 360 ? 360 : i;\n}\nfunction clampCssFloat(f) {\n  return f < 0 ? 0 : f > 1 ? 1 : f;\n}\nfunction parseCssInt(val) {\n  var str = val;\n  if (str.length && str.charAt(str.length - 1) === '%') {\n    return clampCssByte(parseFloat(str) / 100 * 255);\n  }\n  return clampCssByte(parseInt(str, 10));\n}\nfunction parseCssFloat(val) {\n  var str = val;\n  if (str.length && str.charAt(str.length - 1) === '%') {\n    return clampCssFloat(parseFloat(str) / 100);\n  }\n  return clampCssFloat(parseFloat(str));\n}\nfunction cssHueToRgb(m1, m2, h) {\n  if (h < 0) {\n    h += 1;\n  } else if (h > 1) {\n    h -= 1;\n  }\n  if (h * 6 < 1) {\n    return m1 + (m2 - m1) * h * 6;\n  }\n  if (h * 2 < 1) {\n    return m2;\n  }\n  if (h * 3 < 2) {\n    return m1 + (m2 - m1) * (2 / 3 - h) * 6;\n  }\n  return m1;\n}\nfunction lerpNumber(a, b, p) {\n  return a + (b - a) * p;\n}\nfunction setRgba(out, r, g, b, a) {\n  out[0] = r;\n  out[1] = g;\n  out[2] = b;\n  out[3] = a;\n  return out;\n}\nfunction copyRgba(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  return out;\n}\nvar colorCache = new LRU(20);\nvar lastRemovedArr = null;\nfunction putToCache(colorStr, rgbaArr) {\n  if (lastRemovedArr) {\n    copyRgba(lastRemovedArr, rgbaArr);\n  }\n  lastRemovedArr = colorCache.put(colorStr, lastRemovedArr || rgbaArr.slice());\n}\nexport function parse(colorStr, rgbaArr) {\n  if (!colorStr) {\n    return;\n  }\n  rgbaArr = rgbaArr || [];\n  var cached = colorCache.get(colorStr);\n  if (cached) {\n    return copyRgba(rgbaArr, cached);\n  }\n  colorStr = colorStr + '';\n  var str = colorStr.replace(/ /g, '').toLowerCase();\n  if (str in kCSSColorTable) {\n    copyRgba(rgbaArr, kCSSColorTable[str]);\n    putToCache(colorStr, rgbaArr);\n    return rgbaArr;\n  }\n  var strLen = str.length;\n  if (str.charAt(0) === '#') {\n    if (strLen === 4 || strLen === 5) {\n      var iv = parseInt(str.slice(1, 4), 16);\n      if (!(iv >= 0 && iv <= 0xfff)) {\n        setRgba(rgbaArr, 0, 0, 0, 1);\n        return;\n      }\n      setRgba(rgbaArr, (iv & 0xf00) >> 4 | (iv & 0xf00) >> 8, iv & 0xf0 | (iv & 0xf0) >> 4, iv & 0xf | (iv & 0xf) << 4, strLen === 5 ? parseInt(str.slice(4), 16) / 0xf : 1);\n      putToCache(colorStr, rgbaArr);\n      return rgbaArr;\n    } else if (strLen === 7 || strLen === 9) {\n      var iv = parseInt(str.slice(1, 7), 16);\n      if (!(iv >= 0 && iv <= 0xffffff)) {\n        setRgba(rgbaArr, 0, 0, 0, 1);\n        return;\n      }\n      setRgba(rgbaArr, (iv & 0xff0000) >> 16, (iv & 0xff00) >> 8, iv & 0xff, strLen === 9 ? parseInt(str.slice(7), 16) / 0xff : 1);\n      putToCache(colorStr, rgbaArr);\n      return rgbaArr;\n    }\n    return;\n  }\n  var op = str.indexOf('(');\n  var ep = str.indexOf(')');\n  if (op !== -1 && ep + 1 === strLen) {\n    var fname = str.substr(0, op);\n    var params = str.substr(op + 1, ep - (op + 1)).split(',');\n    var alpha = 1;\n    switch (fname) {\n      case 'rgba':\n        if (params.length !== 4) {\n          return params.length === 3 ? setRgba(rgbaArr, +params[0], +params[1], +params[2], 1) : setRgba(rgbaArr, 0, 0, 0, 1);\n        }\n        alpha = parseCssFloat(params.pop());\n      case 'rgb':\n        if (params.length >= 3) {\n          setRgba(rgbaArr, parseCssInt(params[0]), parseCssInt(params[1]), parseCssInt(params[2]), params.length === 3 ? alpha : parseCssFloat(params[3]));\n          putToCache(colorStr, rgbaArr);\n          return rgbaArr;\n        } else {\n          setRgba(rgbaArr, 0, 0, 0, 1);\n          return;\n        }\n      case 'hsla':\n        if (params.length !== 4) {\n          setRgba(rgbaArr, 0, 0, 0, 1);\n          return;\n        }\n        params[3] = parseCssFloat(params[3]);\n        hsla2rgba(params, rgbaArr);\n        putToCache(colorStr, rgbaArr);\n        return rgbaArr;\n      case 'hsl':\n        if (params.length !== 3) {\n          setRgba(rgbaArr, 0, 0, 0, 1);\n          return;\n        }\n        hsla2rgba(params, rgbaArr);\n        putToCache(colorStr, rgbaArr);\n        return rgbaArr;\n      default:\n        return;\n    }\n  }\n  setRgba(rgbaArr, 0, 0, 0, 1);\n  return;\n}\nfunction hsla2rgba(hsla, rgba) {\n  var h = (parseFloat(hsla[0]) % 360 + 360) % 360 / 360;\n  var s = parseCssFloat(hsla[1]);\n  var l = parseCssFloat(hsla[2]);\n  var m2 = l <= 0.5 ? l * (s + 1) : l + s - l * s;\n  var m1 = l * 2 - m2;\n  rgba = rgba || [];\n  setRgba(rgba, clampCssByte(cssHueToRgb(m1, m2, h + 1 / 3) * 255), clampCssByte(cssHueToRgb(m1, m2, h) * 255), clampCssByte(cssHueToRgb(m1, m2, h - 1 / 3) * 255), 1);\n  if (hsla.length === 4) {\n    rgba[3] = hsla[3];\n  }\n  return rgba;\n}\nfunction rgba2hsla(rgba) {\n  if (!rgba) {\n    return;\n  }\n  var R = rgba[0] / 255;\n  var G = rgba[1] / 255;\n  var B = rgba[2] / 255;\n  var vMin = Math.min(R, G, B);\n  var vMax = Math.max(R, G, B);\n  var delta = vMax - vMin;\n  var L = (vMax + vMin) / 2;\n  var H;\n  var S;\n  if (delta === 0) {\n    H = 0;\n    S = 0;\n  } else {\n    if (L < 0.5) {\n      S = delta / (vMax + vMin);\n    } else {\n      S = delta / (2 - vMax - vMin);\n    }\n    var deltaR = ((vMax - R) / 6 + delta / 2) / delta;\n    var deltaG = ((vMax - G) / 6 + delta / 2) / delta;\n    var deltaB = ((vMax - B) / 6 + delta / 2) / delta;\n    if (R === vMax) {\n      H = deltaB - deltaG;\n    } else if (G === vMax) {\n      H = 1 / 3 + deltaR - deltaB;\n    } else if (B === vMax) {\n      H = 2 / 3 + deltaG - deltaR;\n    }\n    if (H < 0) {\n      H += 1;\n    }\n    if (H > 1) {\n      H -= 1;\n    }\n  }\n  var hsla = [H * 360, S, L];\n  if (rgba[3] != null) {\n    hsla.push(rgba[3]);\n  }\n  return hsla;\n}\nexport function lift(color, level) {\n  var colorArr = parse(color);\n  if (colorArr) {\n    for (var i = 0; i < 3; i++) {\n      if (level < 0) {\n        colorArr[i] = colorArr[i] * (1 - level) | 0;\n      } else {\n        colorArr[i] = (255 - colorArr[i]) * level + colorArr[i] | 0;\n      }\n      if (colorArr[i] > 255) {\n        colorArr[i] = 255;\n      } else if (colorArr[i] < 0) {\n        colorArr[i] = 0;\n      }\n    }\n    return stringify(colorArr, colorArr.length === 4 ? 'rgba' : 'rgb');\n  }\n}\nexport function toHex(color) {\n  var colorArr = parse(color);\n  if (colorArr) {\n    return ((1 << 24) + (colorArr[0] << 16) + (colorArr[1] << 8) + +colorArr[2]).toString(16).slice(1);\n  }\n}\nexport function fastLerp(normalizedValue, colors, out) {\n  if (!(colors && colors.length) || !(normalizedValue >= 0 && normalizedValue <= 1)) {\n    return;\n  }\n  out = out || [];\n  var value = normalizedValue * (colors.length - 1);\n  var leftIndex = Math.floor(value);\n  var rightIndex = Math.ceil(value);\n  var leftColor = colors[leftIndex];\n  var rightColor = colors[rightIndex];\n  var dv = value - leftIndex;\n  out[0] = clampCssByte(lerpNumber(leftColor[0], rightColor[0], dv));\n  out[1] = clampCssByte(lerpNumber(leftColor[1], rightColor[1], dv));\n  out[2] = clampCssByte(lerpNumber(leftColor[2], rightColor[2], dv));\n  out[3] = clampCssFloat(lerpNumber(leftColor[3], rightColor[3], dv));\n  return out;\n}\nexport var fastMapToColor = fastLerp;\nexport function lerp(normalizedValue, colors, fullOutput) {\n  if (!(colors && colors.length) || !(normalizedValue >= 0 && normalizedValue <= 1)) {\n    return;\n  }\n  var value = normalizedValue * (colors.length - 1);\n  var leftIndex = Math.floor(value);\n  var rightIndex = Math.ceil(value);\n  var leftColor = parse(colors[leftIndex]);\n  var rightColor = parse(colors[rightIndex]);\n  var dv = value - leftIndex;\n  var color = stringify([clampCssByte(lerpNumber(leftColor[0], rightColor[0], dv)), clampCssByte(lerpNumber(leftColor[1], rightColor[1], dv)), clampCssByte(lerpNumber(leftColor[2], rightColor[2], dv)), clampCssFloat(lerpNumber(leftColor[3], rightColor[3], dv))], 'rgba');\n  return fullOutput ? {\n    color: color,\n    leftIndex: leftIndex,\n    rightIndex: rightIndex,\n    value: value\n  } : color;\n}\nexport var mapToColor = lerp;\nexport function modifyHSL(color, h, s, l) {\n  var colorArr = parse(color);\n  if (color) {\n    colorArr = rgba2hsla(colorArr);\n    h != null && (colorArr[0] = clampCssAngle(h));\n    s != null && (colorArr[1] = parseCssFloat(s));\n    l != null && (colorArr[2] = parseCssFloat(l));\n    return stringify(hsla2rgba(colorArr), 'rgba');\n  }\n}\nexport function modifyAlpha(color, alpha) {\n  var colorArr = parse(color);\n  if (colorArr && alpha != null) {\n    colorArr[3] = clampCssFloat(alpha);\n    return stringify(colorArr, 'rgba');\n  }\n}\nexport function stringify(arrColor, type) {\n  if (!arrColor || !arrColor.length) {\n    return;\n  }\n  var colorStr = arrColor[0] + ',' + arrColor[1] + ',' + arrColor[2];\n  if (type === 'rgba' || type === 'hsva' || type === 'hsla') {\n    colorStr += ',' + arrColor[3];\n  }\n  return type + '(' + colorStr + ')';\n}\nexport function lum(color, backgroundLum) {\n  var arr = parse(color);\n  return arr ? (0.299 * arr[0] + 0.587 * arr[1] + 0.114 * arr[2]) * arr[3] / 255 + (1 - arr[3]) * backgroundLum : 0;\n}\nexport function random() {\n  return stringify([Math.round(Math.random() * 255), Math.round(Math.random() * 255), Math.round(Math.random() * 255)], 'rgb');\n}", "var Entry = function () {\n  function Entry(val) {\n    this.value = val;\n  }\n  return Entry;\n}();\nexport { Entry };\nvar LinkedList = function () {\n  function LinkedList() {\n    this._len = 0;\n  }\n  LinkedList.prototype.insert = function (val) {\n    var entry = new Entry(val);\n    this.insertEntry(entry);\n    return entry;\n  };\n  LinkedList.prototype.insertEntry = function (entry) {\n    if (!this.head) {\n      this.head = this.tail = entry;\n    } else {\n      this.tail.next = entry;\n      entry.prev = this.tail;\n      entry.next = null;\n      this.tail = entry;\n    }\n    this._len++;\n  };\n  LinkedList.prototype.remove = function (entry) {\n    var prev = entry.prev;\n    var next = entry.next;\n    if (prev) {\n      prev.next = next;\n    } else {\n      this.head = next;\n    }\n    if (next) {\n      next.prev = prev;\n    } else {\n      this.tail = prev;\n    }\n    entry.next = entry.prev = null;\n    this._len--;\n  };\n  LinkedList.prototype.len = function () {\n    return this._len;\n  };\n  LinkedList.prototype.clear = function () {\n    this.head = this.tail = null;\n    this._len = 0;\n  };\n  return LinkedList;\n}();\nexport { LinkedList };\nvar LRU = function () {\n  function LRU(maxSize) {\n    this._list = new LinkedList();\n    this._maxSize = 10;\n    this._map = {};\n    this._maxSize = maxSize;\n  }\n  LRU.prototype.put = function (key, value) {\n    var list = this._list;\n    var map = this._map;\n    var removed = null;\n    if (map[key] == null) {\n      var len = list.len();\n      var entry = this._lastRemovedEntry;\n      if (len >= this._maxSize && len > 0) {\n        var leastUsedEntry = list.head;\n        list.remove(leastUsedEntry);\n        delete map[leastUsedEntry.key];\n        removed = leastUsedEntry.value;\n        this._lastRemovedEntry = leastUsedEntry;\n      }\n      if (entry) {\n        entry.value = value;\n      } else {\n        entry = new Entry(value);\n      }\n      entry.key = key;\n      list.insertEntry(entry);\n      map[key] = entry;\n    }\n    return removed;\n  };\n  LRU.prototype.get = function (key) {\n    var entry = this._map[key];\n    var list = this._list;\n    if (entry != null) {\n      if (entry !== list.tail) {\n        list.remove(entry);\n        list.insertEntry(entry);\n      }\n      return entry.value;\n    }\n  };\n  LRU.prototype.clear = function () {\n    this._list.clear();\n    this._map = {};\n  };\n  LRU.prototype.len = function () {\n    return this._list.len();\n  };\n  return LRU;\n}();\nexport default LRU;", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise */\n\nvar extendStatics = function (d, b) {\n  extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n  };\n  return extendStatics(d, b);\n};\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() {\n    this.constructor = d;\n  }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\nexport var __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\nexport function __param(paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n}\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\nexport function __generator(thisArg, body) {\n  var _ = {\n      label: 0,\n      sent: function () {\n        if (t[0] & 1) throw t[1];\n        return t[1];\n      },\n      trys: [],\n      ops: []\n    },\n    f,\n    y,\n    t,\n    g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n    while (_) try {\n      if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n      if (y = 0, t) op = [op[0] & 2, t.value];\n      switch (op[0]) {\n        case 0:\n        case 1:\n          t = op;\n          break;\n        case 4:\n          _.label++;\n          return {\n            value: op[1],\n            done: false\n          };\n        case 5:\n          _.label++;\n          y = op[1];\n          op = [0];\n          continue;\n        case 7:\n          op = _.ops.pop();\n          _.trys.pop();\n          continue;\n        default:\n          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n            _ = 0;\n            continue;\n          }\n          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n            _.label = op[1];\n            break;\n          }\n          if (op[0] === 6 && _.label < t[1]) {\n            _.label = t[1];\n            t = op;\n            break;\n          }\n          if (t && _.label < t[2]) {\n            _.label = t[2];\n            _.ops.push(op);\n            break;\n          }\n          if (t[2]) _.ops.pop();\n          _.trys.pop();\n          continue;\n      }\n      op = body.call(thisArg, _);\n    } catch (e) {\n      op = [6, e];\n      y = 0;\n    } finally {\n      f = t = 0;\n    }\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n}\nexport var __createBinding = Object.create ? function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  Object.defineProperty(o, k2, {\n    enumerable: true,\n    get: function () {\n      return m[k];\n    }\n  });\n} : function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n};\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];\n  return r;\n}\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || from);\n}\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []),\n    i,\n    q = [];\n  return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () {\n    return this;\n  }, i;\n  function verb(n) {\n    if (g[n]) i[n] = function (v) {\n      return new Promise(function (a, b) {\n        q.push([n, v, a, b]) > 1 || resume(n, v);\n      });\n    };\n  }\n  function resume(n, v) {\n    try {\n      step(g[n](v));\n    } catch (e) {\n      settle(q[0][3], e);\n    }\n  }\n  function step(r) {\n    r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);\n  }\n  function fulfill(value) {\n    resume(\"next\", value);\n  }\n  function reject(value) {\n    resume(\"throw\", value);\n  }\n  function settle(f, v) {\n    if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);\n  }\n}\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) {\n    throw e;\n  }), verb(\"return\"), i[Symbol.iterator] = function () {\n    return this;\n  }, i;\n  function verb(n, f) {\n    i[n] = o[n] ? function (v) {\n      return (p = !p) ? {\n        value: __await(o[n](v)),\n        done: n === \"return\"\n      } : f ? f(v) : v;\n    } : f;\n  }\n}\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator],\n    i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () {\n    return this;\n  }, i);\n  function verb(n) {\n    i[n] = o[n] && function (v) {\n      return new Promise(function (resolve, reject) {\n        v = o[n](v), settle(resolve, reject, v.done, v.value);\n      });\n    };\n  }\n  function settle(resolve, reject, d, v) {\n    Promise.resolve(v).then(function (v) {\n      resolve({\n        value: v,\n        done: d\n      });\n    }, reject);\n  }\n}\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) {\n    Object.defineProperty(cooked, \"raw\", {\n      value: raw\n    });\n  } else {\n    cooked.raw = raw;\n  }\n  return cooked;\n}\n;\nvar __setModuleDefault = Object.create ? function (o, v) {\n  Object.defineProperty(o, \"default\", {\n    enumerable: true,\n    value: v\n  });\n} : function (o, v) {\n  o[\"default\"] = v;\n};\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n}\nexport function __importDefault(mod) {\n  return mod && mod.__esModule ? mod : {\n    default: mod\n  };\n}\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n}", "import * as matrix from './matrix.js';\nimport * as vector from './vector.js';\nvar mIdentity = matrix.identity;\nvar EPSILON = 5e-5;\nfunction isNotAroundZero(val) {\n  return val > EPSILON || val < -EPSILON;\n}\nvar scaleTmp = [];\nvar tmpTransform = [];\nvar originTransform = matrix.create();\nvar abs = Math.abs;\nvar Transformable = function () {\n  function Transformable() {}\n  Transformable.prototype.getLocalTransform = function (m) {\n    return Transformable.getLocalTransform(this, m);\n  };\n  Transformable.prototype.setPosition = function (arr) {\n    this.x = arr[0];\n    this.y = arr[1];\n  };\n  Transformable.prototype.setScale = function (arr) {\n    this.scaleX = arr[0];\n    this.scaleY = arr[1];\n  };\n  Transformable.prototype.setSkew = function (arr) {\n    this.skewX = arr[0];\n    this.skewY = arr[1];\n  };\n  Transformable.prototype.setOrigin = function (arr) {\n    this.originX = arr[0];\n    this.originY = arr[1];\n  };\n  Transformable.prototype.needLocalTransform = function () {\n    return isNotAroundZero(this.rotation) || isNotAroundZero(this.x) || isNotAroundZero(this.y) || isNotAroundZero(this.scaleX - 1) || isNotAroundZero(this.scaleY - 1) || isNotAroundZero(this.skewX) || isNotAroundZero(this.skewY);\n  };\n  Transformable.prototype.updateTransform = function () {\n    var parentTransform = this.parent && this.parent.transform;\n    var needLocalTransform = this.needLocalTransform();\n    var m = this.transform;\n    if (!(needLocalTransform || parentTransform)) {\n      if (m) {\n        mIdentity(m);\n        this.invTransform = null;\n      }\n      return;\n    }\n    m = m || matrix.create();\n    if (needLocalTransform) {\n      this.getLocalTransform(m);\n    } else {\n      mIdentity(m);\n    }\n    if (parentTransform) {\n      if (needLocalTransform) {\n        matrix.mul(m, parentTransform, m);\n      } else {\n        matrix.copy(m, parentTransform);\n      }\n    }\n    this.transform = m;\n    this._resolveGlobalScaleRatio(m);\n  };\n  Transformable.prototype._resolveGlobalScaleRatio = function (m) {\n    var globalScaleRatio = this.globalScaleRatio;\n    if (globalScaleRatio != null && globalScaleRatio !== 1) {\n      this.getGlobalScale(scaleTmp);\n      var relX = scaleTmp[0] < 0 ? -1 : 1;\n      var relY = scaleTmp[1] < 0 ? -1 : 1;\n      var sx = ((scaleTmp[0] - relX) * globalScaleRatio + relX) / scaleTmp[0] || 0;\n      var sy = ((scaleTmp[1] - relY) * globalScaleRatio + relY) / scaleTmp[1] || 0;\n      m[0] *= sx;\n      m[1] *= sx;\n      m[2] *= sy;\n      m[3] *= sy;\n    }\n    this.invTransform = this.invTransform || matrix.create();\n    matrix.invert(this.invTransform, m);\n  };\n  Transformable.prototype.getComputedTransform = function () {\n    var transformNode = this;\n    var ancestors = [];\n    while (transformNode) {\n      ancestors.push(transformNode);\n      transformNode = transformNode.parent;\n    }\n    while (transformNode = ancestors.pop()) {\n      transformNode.updateTransform();\n    }\n    return this.transform;\n  };\n  Transformable.prototype.setLocalTransform = function (m) {\n    if (!m) {\n      return;\n    }\n    var sx = m[0] * m[0] + m[1] * m[1];\n    var sy = m[2] * m[2] + m[3] * m[3];\n    var rotation = Math.atan2(m[1], m[0]);\n    var shearX = Math.PI / 2 + rotation - Math.atan2(m[3], m[2]);\n    sy = Math.sqrt(sy) * Math.cos(shearX);\n    sx = Math.sqrt(sx);\n    this.skewX = shearX;\n    this.skewY = 0;\n    this.rotation = -rotation;\n    this.x = +m[4];\n    this.y = +m[5];\n    this.scaleX = sx;\n    this.scaleY = sy;\n    this.originX = 0;\n    this.originY = 0;\n  };\n  Transformable.prototype.decomposeTransform = function () {\n    if (!this.transform) {\n      return;\n    }\n    var parent = this.parent;\n    var m = this.transform;\n    if (parent && parent.transform) {\n      matrix.mul(tmpTransform, parent.invTransform, m);\n      m = tmpTransform;\n    }\n    var ox = this.originX;\n    var oy = this.originY;\n    if (ox || oy) {\n      originTransform[4] = ox;\n      originTransform[5] = oy;\n      matrix.mul(tmpTransform, m, originTransform);\n      tmpTransform[4] -= ox;\n      tmpTransform[5] -= oy;\n      m = tmpTransform;\n    }\n    this.setLocalTransform(m);\n  };\n  Transformable.prototype.getGlobalScale = function (out) {\n    var m = this.transform;\n    out = out || [];\n    if (!m) {\n      out[0] = 1;\n      out[1] = 1;\n      return out;\n    }\n    out[0] = Math.sqrt(m[0] * m[0] + m[1] * m[1]);\n    out[1] = Math.sqrt(m[2] * m[2] + m[3] * m[3]);\n    if (m[0] < 0) {\n      out[0] = -out[0];\n    }\n    if (m[3] < 0) {\n      out[1] = -out[1];\n    }\n    return out;\n  };\n  Transformable.prototype.transformCoordToLocal = function (x, y) {\n    var v2 = [x, y];\n    var invTransform = this.invTransform;\n    if (invTransform) {\n      vector.applyTransform(v2, v2, invTransform);\n    }\n    return v2;\n  };\n  Transformable.prototype.transformCoordToGlobal = function (x, y) {\n    var v2 = [x, y];\n    var transform = this.transform;\n    if (transform) {\n      vector.applyTransform(v2, v2, transform);\n    }\n    return v2;\n  };\n  Transformable.prototype.getLineScale = function () {\n    var m = this.transform;\n    return m && abs(m[0] - 1) > 1e-10 && abs(m[3] - 1) > 1e-10 ? Math.sqrt(abs(m[0] * m[3] - m[2] * m[1])) : 1;\n  };\n  Transformable.prototype.copyTransform = function (source) {\n    copyTransform(this, source);\n  };\n  Transformable.getLocalTransform = function (target, m) {\n    m = m || [];\n    var ox = target.originX || 0;\n    var oy = target.originY || 0;\n    var sx = target.scaleX;\n    var sy = target.scaleY;\n    var ax = target.anchorX;\n    var ay = target.anchorY;\n    var rotation = target.rotation || 0;\n    var x = target.x;\n    var y = target.y;\n    var skewX = target.skewX ? Math.tan(target.skewX) : 0;\n    var skewY = target.skewY ? Math.tan(-target.skewY) : 0;\n    if (ox || oy || ax || ay) {\n      var dx = ox + ax;\n      var dy = oy + ay;\n      m[4] = -dx * sx - skewX * dy * sy;\n      m[5] = -dy * sy - skewY * dx * sx;\n    } else {\n      m[4] = m[5] = 0;\n    }\n    m[0] = sx;\n    m[3] = sy;\n    m[1] = skewY * sx;\n    m[2] = skewX * sy;\n    rotation && matrix.rotate(m, m, rotation);\n    m[4] += ox + x;\n    m[5] += oy + y;\n    return m;\n  };\n  Transformable.initDefaultProps = function () {\n    var proto = Transformable.prototype;\n    proto.scaleX = proto.scaleY = proto.globalScaleRatio = 1;\n    proto.x = proto.y = proto.originX = proto.originY = proto.skewX = proto.skewY = proto.rotation = proto.anchorX = proto.anchorY = 0;\n  }();\n  return Transformable;\n}();\n;\nexport var TRANSFORMABLE_PROPS = ['x', 'y', 'originX', 'originY', 'anchorX', 'anchorY', 'rotation', 'scaleX', 'scaleY', 'skewX', 'skewY'];\nexport function copyTransform(target, source) {\n  for (var i = 0; i < TRANSFORMABLE_PROPS.length; i++) {\n    var propName = TRANSFORMABLE_PROPS[i];\n    target[propName] = source[propName];\n  }\n}\nexport default Transformable;", "var easingFuncs = {\n  linear: function (k) {\n    return k;\n  },\n  quadraticIn: function (k) {\n    return k * k;\n  },\n  quadraticOut: function (k) {\n    return k * (2 - k);\n  },\n  quadraticInOut: function (k) {\n    if ((k *= 2) < 1) {\n      return 0.5 * k * k;\n    }\n    return -0.5 * (--k * (k - 2) - 1);\n  },\n  cubicIn: function (k) {\n    return k * k * k;\n  },\n  cubicOut: function (k) {\n    return --k * k * k + 1;\n  },\n  cubicInOut: function (k) {\n    if ((k *= 2) < 1) {\n      return 0.5 * k * k * k;\n    }\n    return 0.5 * ((k -= 2) * k * k + 2);\n  },\n  quarticIn: function (k) {\n    return k * k * k * k;\n  },\n  quarticOut: function (k) {\n    return 1 - --k * k * k * k;\n  },\n  quarticInOut: function (k) {\n    if ((k *= 2) < 1) {\n      return 0.5 * k * k * k * k;\n    }\n    return -0.5 * ((k -= 2) * k * k * k - 2);\n  },\n  quinticIn: function (k) {\n    return k * k * k * k * k;\n  },\n  quinticOut: function (k) {\n    return --k * k * k * k * k + 1;\n  },\n  quinticInOut: function (k) {\n    if ((k *= 2) < 1) {\n      return 0.5 * k * k * k * k * k;\n    }\n    return 0.5 * ((k -= 2) * k * k * k * k + 2);\n  },\n  sinusoidalIn: function (k) {\n    return 1 - Math.cos(k * Math.PI / 2);\n  },\n  sinusoidalOut: function (k) {\n    return Math.sin(k * Math.PI / 2);\n  },\n  sinusoidalInOut: function (k) {\n    return 0.5 * (1 - Math.cos(Math.PI * k));\n  },\n  exponentialIn: function (k) {\n    return k === 0 ? 0 : Math.pow(1024, k - 1);\n  },\n  exponentialOut: function (k) {\n    return k === 1 ? 1 : 1 - Math.pow(2, -10 * k);\n  },\n  exponentialInOut: function (k) {\n    if (k === 0) {\n      return 0;\n    }\n    if (k === 1) {\n      return 1;\n    }\n    if ((k *= 2) < 1) {\n      return 0.5 * Math.pow(1024, k - 1);\n    }\n    return 0.5 * (-Math.pow(2, -10 * (k - 1)) + 2);\n  },\n  circularIn: function (k) {\n    return 1 - Math.sqrt(1 - k * k);\n  },\n  circularOut: function (k) {\n    return Math.sqrt(1 - --k * k);\n  },\n  circularInOut: function (k) {\n    if ((k *= 2) < 1) {\n      return -0.5 * (Math.sqrt(1 - k * k) - 1);\n    }\n    return 0.5 * (Math.sqrt(1 - (k -= 2) * k) + 1);\n  },\n  elasticIn: function (k) {\n    var s;\n    var a = 0.1;\n    var p = 0.4;\n    if (k === 0) {\n      return 0;\n    }\n    if (k === 1) {\n      return 1;\n    }\n    if (!a || a < 1) {\n      a = 1;\n      s = p / 4;\n    } else {\n      s = p * Math.asin(1 / a) / (2 * Math.PI);\n    }\n    return -(a * Math.pow(2, 10 * (k -= 1)) * Math.sin((k - s) * (2 * Math.PI) / p));\n  },\n  elasticOut: function (k) {\n    var s;\n    var a = 0.1;\n    var p = 0.4;\n    if (k === 0) {\n      return 0;\n    }\n    if (k === 1) {\n      return 1;\n    }\n    if (!a || a < 1) {\n      a = 1;\n      s = p / 4;\n    } else {\n      s = p * Math.asin(1 / a) / (2 * Math.PI);\n    }\n    return a * Math.pow(2, -10 * k) * Math.sin((k - s) * (2 * Math.PI) / p) + 1;\n  },\n  elasticInOut: function (k) {\n    var s;\n    var a = 0.1;\n    var p = 0.4;\n    if (k === 0) {\n      return 0;\n    }\n    if (k === 1) {\n      return 1;\n    }\n    if (!a || a < 1) {\n      a = 1;\n      s = p / 4;\n    } else {\n      s = p * Math.asin(1 / a) / (2 * Math.PI);\n    }\n    if ((k *= 2) < 1) {\n      return -0.5 * (a * Math.pow(2, 10 * (k -= 1)) * Math.sin((k - s) * (2 * Math.PI) / p));\n    }\n    return a * Math.pow(2, -10 * (k -= 1)) * Math.sin((k - s) * (2 * Math.PI) / p) * 0.5 + 1;\n  },\n  backIn: function (k) {\n    var s = 1.70158;\n    return k * k * ((s + 1) * k - s);\n  },\n  backOut: function (k) {\n    var s = 1.70158;\n    return --k * k * ((s + 1) * k + s) + 1;\n  },\n  backInOut: function (k) {\n    var s = 1.70158 * 1.525;\n    if ((k *= 2) < 1) {\n      return 0.5 * (k * k * ((s + 1) * k - s));\n    }\n    return 0.5 * ((k -= 2) * k * ((s + 1) * k + s) + 2);\n  },\n  bounceIn: function (k) {\n    return 1 - easingFuncs.bounceOut(1 - k);\n  },\n  bounceOut: function (k) {\n    if (k < 1 / 2.75) {\n      return 7.5625 * k * k;\n    } else if (k < 2 / 2.75) {\n      return 7.5625 * (k -= 1.5 / 2.75) * k + 0.75;\n    } else if (k < 2.5 / 2.75) {\n      return 7.5625 * (k -= 2.25 / 2.75) * k + 0.9375;\n    } else {\n      return 7.5625 * (k -= 2.625 / 2.75) * k + 0.984375;\n    }\n  },\n  bounceInOut: function (k) {\n    if (k < 0.5) {\n      return easingFuncs.bounceIn(k * 2) * 0.5;\n    }\n    return easingFuncs.bounceOut(k * 2 - 1) * 0.5 + 0.5;\n  }\n};\nexport default easingFuncs;", "import { create as v2Create, distSquare as v2DistSquare } from './vector.js';\nvar mathPow = Math.pow;\nvar mathSqrt = Math.sqrt;\nvar EPSILON = 1e-8;\nvar EPSILON_NUMERIC = 1e-4;\nvar THREE_SQRT = mathSqrt(3);\nvar ONE_THIRD = 1 / 3;\nvar _v0 = v2Create();\nvar _v1 = v2Create();\nvar _v2 = v2Create();\nfunction isAroundZero(val) {\n  return val > -EPSILON && val < EPSILON;\n}\nfunction isNotAroundZero(val) {\n  return val > EPSILON || val < -EPSILON;\n}\nexport function cubicAt(p0, p1, p2, p3, t) {\n  var onet = 1 - t;\n  return onet * onet * (onet * p0 + 3 * t * p1) + t * t * (t * p3 + 3 * onet * p2);\n}\nexport function cubicDerivativeAt(p0, p1, p2, p3, t) {\n  var onet = 1 - t;\n  return 3 * (((p1 - p0) * onet + 2 * (p2 - p1) * t) * onet + (p3 - p2) * t * t);\n}\nexport function cubicRootAt(p0, p1, p2, p3, val, roots) {\n  var a = p3 + 3 * (p1 - p2) - p0;\n  var b = 3 * (p2 - p1 * 2 + p0);\n  var c = 3 * (p1 - p0);\n  var d = p0 - val;\n  var A = b * b - 3 * a * c;\n  var B = b * c - 9 * a * d;\n  var C = c * c - 3 * b * d;\n  var n = 0;\n  if (isAroundZero(A) && isAroundZero(B)) {\n    if (isAroundZero(b)) {\n      roots[0] = 0;\n    } else {\n      var t1 = -c / b;\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n    }\n  } else {\n    var disc = B * B - 4 * A * C;\n    if (isAroundZero(disc)) {\n      var K = B / A;\n      var t1 = -b / a + K;\n      var t2 = -K / 2;\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n      if (t2 >= 0 && t2 <= 1) {\n        roots[n++] = t2;\n      }\n    } else if (disc > 0) {\n      var discSqrt = mathSqrt(disc);\n      var Y1 = A * b + 1.5 * a * (-B + discSqrt);\n      var Y2 = A * b + 1.5 * a * (-B - discSqrt);\n      if (Y1 < 0) {\n        Y1 = -mathPow(-Y1, ONE_THIRD);\n      } else {\n        Y1 = mathPow(Y1, ONE_THIRD);\n      }\n      if (Y2 < 0) {\n        Y2 = -mathPow(-Y2, ONE_THIRD);\n      } else {\n        Y2 = mathPow(Y2, ONE_THIRD);\n      }\n      var t1 = (-b - (Y1 + Y2)) / (3 * a);\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n    } else {\n      var T = (2 * A * b - 3 * a * B) / (2 * mathSqrt(A * A * A));\n      var theta = Math.acos(T) / 3;\n      var ASqrt = mathSqrt(A);\n      var tmp = Math.cos(theta);\n      var t1 = (-b - 2 * ASqrt * tmp) / (3 * a);\n      var t2 = (-b + ASqrt * (tmp + THREE_SQRT * Math.sin(theta))) / (3 * a);\n      var t3 = (-b + ASqrt * (tmp - THREE_SQRT * Math.sin(theta))) / (3 * a);\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n      if (t2 >= 0 && t2 <= 1) {\n        roots[n++] = t2;\n      }\n      if (t3 >= 0 && t3 <= 1) {\n        roots[n++] = t3;\n      }\n    }\n  }\n  return n;\n}\nexport function cubicExtrema(p0, p1, p2, p3, extrema) {\n  var b = 6 * p2 - 12 * p1 + 6 * p0;\n  var a = 9 * p1 + 3 * p3 - 3 * p0 - 9 * p2;\n  var c = 3 * p1 - 3 * p0;\n  var n = 0;\n  if (isAroundZero(a)) {\n    if (isNotAroundZero(b)) {\n      var t1 = -c / b;\n      if (t1 >= 0 && t1 <= 1) {\n        extrema[n++] = t1;\n      }\n    }\n  } else {\n    var disc = b * b - 4 * a * c;\n    if (isAroundZero(disc)) {\n      extrema[0] = -b / (2 * a);\n    } else if (disc > 0) {\n      var discSqrt = mathSqrt(disc);\n      var t1 = (-b + discSqrt) / (2 * a);\n      var t2 = (-b - discSqrt) / (2 * a);\n      if (t1 >= 0 && t1 <= 1) {\n        extrema[n++] = t1;\n      }\n      if (t2 >= 0 && t2 <= 1) {\n        extrema[n++] = t2;\n      }\n    }\n  }\n  return n;\n}\nexport function cubicSubdivide(p0, p1, p2, p3, t, out) {\n  var p01 = (p1 - p0) * t + p0;\n  var p12 = (p2 - p1) * t + p1;\n  var p23 = (p3 - p2) * t + p2;\n  var p012 = (p12 - p01) * t + p01;\n  var p123 = (p23 - p12) * t + p12;\n  var p0123 = (p123 - p012) * t + p012;\n  out[0] = p0;\n  out[1] = p01;\n  out[2] = p012;\n  out[3] = p0123;\n  out[4] = p0123;\n  out[5] = p123;\n  out[6] = p23;\n  out[7] = p3;\n}\nexport function cubicProjectPoint(x0, y0, x1, y1, x2, y2, x3, y3, x, y, out) {\n  var t;\n  var interval = 0.005;\n  var d = Infinity;\n  var prev;\n  var next;\n  var d1;\n  var d2;\n  _v0[0] = x;\n  _v0[1] = y;\n  for (var _t = 0; _t < 1; _t += 0.05) {\n    _v1[0] = cubicAt(x0, x1, x2, x3, _t);\n    _v1[1] = cubicAt(y0, y1, y2, y3, _t);\n    d1 = v2DistSquare(_v0, _v1);\n    if (d1 < d) {\n      t = _t;\n      d = d1;\n    }\n  }\n  d = Infinity;\n  for (var i = 0; i < 32; i++) {\n    if (interval < EPSILON_NUMERIC) {\n      break;\n    }\n    prev = t - interval;\n    next = t + interval;\n    _v1[0] = cubicAt(x0, x1, x2, x3, prev);\n    _v1[1] = cubicAt(y0, y1, y2, y3, prev);\n    d1 = v2DistSquare(_v1, _v0);\n    if (prev >= 0 && d1 < d) {\n      t = prev;\n      d = d1;\n    } else {\n      _v2[0] = cubicAt(x0, x1, x2, x3, next);\n      _v2[1] = cubicAt(y0, y1, y2, y3, next);\n      d2 = v2DistSquare(_v2, _v0);\n      if (next <= 1 && d2 < d) {\n        t = next;\n        d = d2;\n      } else {\n        interval *= 0.5;\n      }\n    }\n  }\n  if (out) {\n    out[0] = cubicAt(x0, x1, x2, x3, t);\n    out[1] = cubicAt(y0, y1, y2, y3, t);\n  }\n  return mathSqrt(d);\n}\nexport function cubicLength(x0, y0, x1, y1, x2, y2, x3, y3, iteration) {\n  var px = x0;\n  var py = y0;\n  var d = 0;\n  var step = 1 / iteration;\n  for (var i = 1; i <= iteration; i++) {\n    var t = i * step;\n    var x = cubicAt(x0, x1, x2, x3, t);\n    var y = cubicAt(y0, y1, y2, y3, t);\n    var dx = x - px;\n    var dy = y - py;\n    d += Math.sqrt(dx * dx + dy * dy);\n    px = x;\n    py = y;\n  }\n  return d;\n}\nexport function quadraticAt(p0, p1, p2, t) {\n  var onet = 1 - t;\n  return onet * (onet * p0 + 2 * t * p1) + t * t * p2;\n}\nexport function quadraticDerivativeAt(p0, p1, p2, t) {\n  return 2 * ((1 - t) * (p1 - p0) + t * (p2 - p1));\n}\nexport function quadraticRootAt(p0, p1, p2, val, roots) {\n  var a = p0 - 2 * p1 + p2;\n  var b = 2 * (p1 - p0);\n  var c = p0 - val;\n  var n = 0;\n  if (isAroundZero(a)) {\n    if (isNotAroundZero(b)) {\n      var t1 = -c / b;\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n    }\n  } else {\n    var disc = b * b - 4 * a * c;\n    if (isAroundZero(disc)) {\n      var t1 = -b / (2 * a);\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n    } else if (disc > 0) {\n      var discSqrt = mathSqrt(disc);\n      var t1 = (-b + discSqrt) / (2 * a);\n      var t2 = (-b - discSqrt) / (2 * a);\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n      if (t2 >= 0 && t2 <= 1) {\n        roots[n++] = t2;\n      }\n    }\n  }\n  return n;\n}\nexport function quadraticExtremum(p0, p1, p2) {\n  var divider = p0 + p2 - 2 * p1;\n  if (divider === 0) {\n    return 0.5;\n  } else {\n    return (p0 - p1) / divider;\n  }\n}\nexport function quadraticSubdivide(p0, p1, p2, t, out) {\n  var p01 = (p1 - p0) * t + p0;\n  var p12 = (p2 - p1) * t + p1;\n  var p012 = (p12 - p01) * t + p01;\n  out[0] = p0;\n  out[1] = p01;\n  out[2] = p012;\n  out[3] = p012;\n  out[4] = p12;\n  out[5] = p2;\n}\nexport function quadraticProjectPoint(x0, y0, x1, y1, x2, y2, x, y, out) {\n  var t;\n  var interval = 0.005;\n  var d = Infinity;\n  _v0[0] = x;\n  _v0[1] = y;\n  for (var _t = 0; _t < 1; _t += 0.05) {\n    _v1[0] = quadraticAt(x0, x1, x2, _t);\n    _v1[1] = quadraticAt(y0, y1, y2, _t);\n    var d1 = v2DistSquare(_v0, _v1);\n    if (d1 < d) {\n      t = _t;\n      d = d1;\n    }\n  }\n  d = Infinity;\n  for (var i = 0; i < 32; i++) {\n    if (interval < EPSILON_NUMERIC) {\n      break;\n    }\n    var prev = t - interval;\n    var next = t + interval;\n    _v1[0] = quadraticAt(x0, x1, x2, prev);\n    _v1[1] = quadraticAt(y0, y1, y2, prev);\n    var d1 = v2DistSquare(_v1, _v0);\n    if (prev >= 0 && d1 < d) {\n      t = prev;\n      d = d1;\n    } else {\n      _v2[0] = quadraticAt(x0, x1, x2, next);\n      _v2[1] = quadraticAt(y0, y1, y2, next);\n      var d2 = v2DistSquare(_v2, _v0);\n      if (next <= 1 && d2 < d) {\n        t = next;\n        d = d2;\n      } else {\n        interval *= 0.5;\n      }\n    }\n  }\n  if (out) {\n    out[0] = quadraticAt(x0, x1, x2, t);\n    out[1] = quadraticAt(y0, y1, y2, t);\n  }\n  return mathSqrt(d);\n}\nexport function quadraticLength(x0, y0, x1, y1, x2, y2, iteration) {\n  var px = x0;\n  var py = y0;\n  var d = 0;\n  var step = 1 / iteration;\n  for (var i = 1; i <= iteration; i++) {\n    var t = i * step;\n    var x = quadraticAt(x0, x1, x2, t);\n    var y = quadraticAt(y0, y1, y2, t);\n    var dx = x - px;\n    var dy = y - py;\n    d += Math.sqrt(dx * dx + dy * dy);\n    px = x;\n    py = y;\n  }\n  return d;\n}", "import { cubicAt, cubicRootAt } from '../core/curve.js';\nimport { trim } from '../core/util.js';\nvar regexp = /cubic-bezier\\(([0-9,\\.e ]+)\\)/;\nexport function createCubicEasingFunc(cubicEasingStr) {\n  var cubic = cubicEasingStr && regexp.exec(cubicEasingStr);\n  if (cubic) {\n    var points = cubic[1].split(',');\n    var a_1 = +trim(points[0]);\n    var b_1 = +trim(points[1]);\n    var c_1 = +trim(points[2]);\n    var d_1 = +trim(points[3]);\n    if (isNaN(a_1 + b_1 + c_1 + d_1)) {\n      return;\n    }\n    var roots_1 = [];\n    return function (p) {\n      return p <= 0 ? 0 : p >= 1 ? 1 : cubicRootAt(0, a_1, c_1, 1, p, roots_1) && cubicAt(0, b_1, d_1, 1, roots_1[0]);\n    };\n  }\n}", "import easingFuncs from './easing.js';\nimport { isFunction, noop } from '../core/util.js';\nimport { createCubicEasingFunc } from './cubicEasing.js';\nvar Clip = function () {\n  function Clip(opts) {\n    this._inited = false;\n    this._startTime = 0;\n    this._pausedTime = 0;\n    this._paused = false;\n    this._life = opts.life || 1000;\n    this._delay = opts.delay || 0;\n    this.loop = opts.loop || false;\n    this.onframe = opts.onframe || noop;\n    this.ondestroy = opts.ondestroy || noop;\n    this.onrestart = opts.onrestart || noop;\n    opts.easing && this.setEasing(opts.easing);\n  }\n  Clip.prototype.step = function (globalTime, deltaTime) {\n    if (!this._inited) {\n      this._startTime = globalTime + this._delay;\n      this._inited = true;\n    }\n    if (this._paused) {\n      this._pausedTime += deltaTime;\n      return;\n    }\n    var life = this._life;\n    var elapsedTime = globalTime - this._startTime - this._pausedTime;\n    var percent = elapsedTime / life;\n    if (percent < 0) {\n      percent = 0;\n    }\n    percent = Math.min(percent, 1);\n    var easingFunc = this.easingFunc;\n    var schedule = easingFunc ? easingFunc(percent) : percent;\n    this.onframe(schedule);\n    if (percent === 1) {\n      if (this.loop) {\n        var remainder = elapsedTime % life;\n        this._startTime = globalTime - remainder;\n        this._pausedTime = 0;\n        this.onrestart();\n      } else {\n        return true;\n      }\n    }\n    return false;\n  };\n  Clip.prototype.pause = function () {\n    this._paused = true;\n  };\n  Clip.prototype.resume = function () {\n    this._paused = false;\n  };\n  Clip.prototype.setEasing = function (easing) {\n    this.easing = easing;\n    this.easingFunc = isFunction(easing) ? easing : easingFuncs[easing] || createCubicEasingFunc(easing);\n  };\n  return Clip;\n}();\nexport default Clip;", "import { RADIAN_TO_DEGREE, retrieve2, logError, isFunction } from '../core/util.js';\nimport { parse } from '../tool/color.js';\nimport env from '../core/env.js';\nvar mathRound = Math.round;\nexport function normalizeColor(color) {\n  var opacity;\n  if (!color || color === 'transparent') {\n    color = 'none';\n  } else if (typeof color === 'string' && color.indexOf('rgba') > -1) {\n    var arr = parse(color);\n    if (arr) {\n      color = 'rgb(' + arr[0] + ',' + arr[1] + ',' + arr[2] + ')';\n      opacity = arr[3];\n    }\n  }\n  return {\n    color: color,\n    opacity: opacity == null ? 1 : opacity\n  };\n}\nvar EPSILON = 1e-4;\nexport function isAroundZero(transform) {\n  return transform < EPSILON && transform > -EPSILON;\n}\nexport function round3(transform) {\n  return mathRound(transform * 1e3) / 1e3;\n}\nexport function round4(transform) {\n  return mathRound(transform * 1e4) / 1e4;\n}\nexport function round1(transform) {\n  return mathRound(transform * 10) / 10;\n}\nexport function getMatrixStr(m) {\n  return 'matrix(' + round3(m[0]) + ',' + round3(m[1]) + ',' + round3(m[2]) + ',' + round3(m[3]) + ',' + round4(m[4]) + ',' + round4(m[5]) + ')';\n}\nexport var TEXT_ALIGN_TO_ANCHOR = {\n  left: 'start',\n  right: 'end',\n  center: 'middle',\n  middle: 'middle'\n};\nexport function adjustTextY(y, lineHeight, textBaseline) {\n  if (textBaseline === 'top') {\n    y += lineHeight / 2;\n  } else if (textBaseline === 'bottom') {\n    y -= lineHeight / 2;\n  }\n  return y;\n}\nexport function hasShadow(style) {\n  return style && (style.shadowBlur || style.shadowOffsetX || style.shadowOffsetY);\n}\nexport function getShadowKey(displayable) {\n  var style = displayable.style;\n  var globalScale = displayable.getGlobalScale();\n  return [style.shadowColor, (style.shadowBlur || 0).toFixed(2), (style.shadowOffsetX || 0).toFixed(2), (style.shadowOffsetY || 0).toFixed(2), globalScale[0], globalScale[1]].join(',');\n}\nexport function getClipPathsKey(clipPaths) {\n  var key = [];\n  if (clipPaths) {\n    for (var i = 0; i < clipPaths.length; i++) {\n      var clipPath = clipPaths[i];\n      key.push(clipPath.id);\n    }\n  }\n  return key.join(',');\n}\nexport function isImagePattern(val) {\n  return val && !!val.image;\n}\nexport function isSVGPattern(val) {\n  return val && !!val.svgElement;\n}\nexport function isPattern(val) {\n  return isImagePattern(val) || isSVGPattern(val);\n}\nexport function isLinearGradient(val) {\n  return val.type === 'linear';\n}\nexport function isRadialGradient(val) {\n  return val.type === 'radial';\n}\nexport function isGradient(val) {\n  return val && (val.type === 'linear' || val.type === 'radial');\n}\nexport function getIdURL(id) {\n  return \"url(#\" + id + \")\";\n}\nexport function getPathPrecision(el) {\n  var scale = el.getGlobalScale();\n  var size = Math.max(scale[0], scale[1]);\n  return Math.max(Math.ceil(Math.log(size) / Math.log(10)), 1);\n}\nexport function getSRTTransformString(transform) {\n  var x = transform.x || 0;\n  var y = transform.y || 0;\n  var rotation = (transform.rotation || 0) * RADIAN_TO_DEGREE;\n  var scaleX = retrieve2(transform.scaleX, 1);\n  var scaleY = retrieve2(transform.scaleY, 1);\n  var skewX = transform.skewX || 0;\n  var skewY = transform.skewY || 0;\n  var res = [];\n  if (x || y) {\n    res.push(\"translate(\" + x + \"px,\" + y + \"px)\");\n  }\n  if (rotation) {\n    res.push(\"rotate(\" + rotation + \")\");\n  }\n  if (scaleX !== 1 || scaleY !== 1) {\n    res.push(\"scale(\" + scaleX + \",\" + scaleY + \")\");\n  }\n  if (skewX || skewY) {\n    res.push(\"skew(\" + mathRound(skewX * RADIAN_TO_DEGREE) + \"deg, \" + mathRound(skewY * RADIAN_TO_DEGREE) + \"deg)\");\n  }\n  return res.join(' ');\n}\nexport var encodeBase64 = function () {\n  if (env.hasGlobalWindow && isFunction(window.btoa)) {\n    return function (str) {\n      return window.btoa(unescape(encodeURIComponent(str)));\n    };\n  }\n  if (typeof Buffer !== 'undefined') {\n    return function (str) {\n      return Buffer.from(str).toString('base64');\n    };\n  }\n  return function (str) {\n    if (process.env.NODE_ENV !== 'production') {\n      logError('Base64 isn\\'t natively supported in the current environment.');\n    }\n    return null;\n  };\n}();", "import Clip from './Clip.js';\nimport * as color from '../tool/color.js';\nimport { eqNaN, extend, isArrayLike, isFunction, isGradientObject, isNumber, isString, keys, logError, map } from '../core/util.js';\nimport easingFuncs from './easing.js';\nimport { createCubicEasingFunc } from './cubicEasing.js';\nimport { isLinearGradient, isRadialGradient } from '../svg/helper.js';\n;\nvar arraySlice = Array.prototype.slice;\nfunction interpolateNumber(p0, p1, percent) {\n  return (p1 - p0) * percent + p0;\n}\nfunction interpolate1DArray(out, p0, p1, percent) {\n  var len = p0.length;\n  for (var i = 0; i < len; i++) {\n    out[i] = interpolateNumber(p0[i], p1[i], percent);\n  }\n  return out;\n}\nfunction interpolate2DArray(out, p0, p1, percent) {\n  var len = p0.length;\n  var len2 = len && p0[0].length;\n  for (var i = 0; i < len; i++) {\n    if (!out[i]) {\n      out[i] = [];\n    }\n    for (var j = 0; j < len2; j++) {\n      out[i][j] = interpolateNumber(p0[i][j], p1[i][j], percent);\n    }\n  }\n  return out;\n}\nfunction add1DArray(out, p0, p1, sign) {\n  var len = p0.length;\n  for (var i = 0; i < len; i++) {\n    out[i] = p0[i] + p1[i] * sign;\n  }\n  return out;\n}\nfunction add2DArray(out, p0, p1, sign) {\n  var len = p0.length;\n  var len2 = len && p0[0].length;\n  for (var i = 0; i < len; i++) {\n    if (!out[i]) {\n      out[i] = [];\n    }\n    for (var j = 0; j < len2; j++) {\n      out[i][j] = p0[i][j] + p1[i][j] * sign;\n    }\n  }\n  return out;\n}\nfunction fillColorStops(val0, val1) {\n  var len0 = val0.length;\n  var len1 = val1.length;\n  var shorterArr = len0 > len1 ? val1 : val0;\n  var shorterLen = Math.min(len0, len1);\n  var last = shorterArr[shorterLen - 1] || {\n    color: [0, 0, 0, 0],\n    offset: 0\n  };\n  for (var i = shorterLen; i < Math.max(len0, len1); i++) {\n    shorterArr.push({\n      offset: last.offset,\n      color: last.color.slice()\n    });\n  }\n}\nfunction fillArray(val0, val1, arrDim) {\n  var arr0 = val0;\n  var arr1 = val1;\n  if (!arr0.push || !arr1.push) {\n    return;\n  }\n  var arr0Len = arr0.length;\n  var arr1Len = arr1.length;\n  if (arr0Len !== arr1Len) {\n    var isPreviousLarger = arr0Len > arr1Len;\n    if (isPreviousLarger) {\n      arr0.length = arr1Len;\n    } else {\n      for (var i = arr0Len; i < arr1Len; i++) {\n        arr0.push(arrDim === 1 ? arr1[i] : arraySlice.call(arr1[i]));\n      }\n    }\n  }\n  var len2 = arr0[0] && arr0[0].length;\n  for (var i = 0; i < arr0.length; i++) {\n    if (arrDim === 1) {\n      if (isNaN(arr0[i])) {\n        arr0[i] = arr1[i];\n      }\n    } else {\n      for (var j = 0; j < len2; j++) {\n        if (isNaN(arr0[i][j])) {\n          arr0[i][j] = arr1[i][j];\n        }\n      }\n    }\n  }\n}\nexport function cloneValue(value) {\n  if (isArrayLike(value)) {\n    var len = value.length;\n    if (isArrayLike(value[0])) {\n      var ret = [];\n      for (var i = 0; i < len; i++) {\n        ret.push(arraySlice.call(value[i]));\n      }\n      return ret;\n    }\n    return arraySlice.call(value);\n  }\n  return value;\n}\nfunction rgba2String(rgba) {\n  rgba[0] = Math.floor(rgba[0]) || 0;\n  rgba[1] = Math.floor(rgba[1]) || 0;\n  rgba[2] = Math.floor(rgba[2]) || 0;\n  rgba[3] = rgba[3] == null ? 1 : rgba[3];\n  return 'rgba(' + rgba.join(',') + ')';\n}\nfunction guessArrayDim(value) {\n  return isArrayLike(value && value[0]) ? 2 : 1;\n}\nvar VALUE_TYPE_NUMBER = 0;\nvar VALUE_TYPE_1D_ARRAY = 1;\nvar VALUE_TYPE_2D_ARRAY = 2;\nvar VALUE_TYPE_COLOR = 3;\nvar VALUE_TYPE_LINEAR_GRADIENT = 4;\nvar VALUE_TYPE_RADIAL_GRADIENT = 5;\nvar VALUE_TYPE_UNKOWN = 6;\nfunction isGradientValueType(valType) {\n  return valType === VALUE_TYPE_LINEAR_GRADIENT || valType === VALUE_TYPE_RADIAL_GRADIENT;\n}\nfunction isArrayValueType(valType) {\n  return valType === VALUE_TYPE_1D_ARRAY || valType === VALUE_TYPE_2D_ARRAY;\n}\nvar tmpRgba = [0, 0, 0, 0];\nvar Track = function () {\n  function Track(propName) {\n    this.keyframes = [];\n    this.discrete = false;\n    this._invalid = false;\n    this._needsSort = false;\n    this._lastFr = 0;\n    this._lastFrP = 0;\n    this.propName = propName;\n  }\n  Track.prototype.isFinished = function () {\n    return this._finished;\n  };\n  Track.prototype.setFinished = function () {\n    this._finished = true;\n    if (this._additiveTrack) {\n      this._additiveTrack.setFinished();\n    }\n  };\n  Track.prototype.needsAnimate = function () {\n    return this.keyframes.length >= 1;\n  };\n  Track.prototype.getAdditiveTrack = function () {\n    return this._additiveTrack;\n  };\n  Track.prototype.addKeyframe = function (time, rawValue, easing) {\n    this._needsSort = true;\n    var keyframes = this.keyframes;\n    var len = keyframes.length;\n    var discrete = false;\n    var valType = VALUE_TYPE_UNKOWN;\n    var value = rawValue;\n    if (isArrayLike(rawValue)) {\n      var arrayDim = guessArrayDim(rawValue);\n      valType = arrayDim;\n      if (arrayDim === 1 && !isNumber(rawValue[0]) || arrayDim === 2 && !isNumber(rawValue[0][0])) {\n        discrete = true;\n      }\n    } else {\n      if (isNumber(rawValue) && !eqNaN(rawValue)) {\n        valType = VALUE_TYPE_NUMBER;\n      } else if (isString(rawValue)) {\n        if (!isNaN(+rawValue)) {\n          valType = VALUE_TYPE_NUMBER;\n        } else {\n          var colorArray = color.parse(rawValue);\n          if (colorArray) {\n            value = colorArray;\n            valType = VALUE_TYPE_COLOR;\n          }\n        }\n      } else if (isGradientObject(rawValue)) {\n        var parsedGradient = extend({}, value);\n        parsedGradient.colorStops = map(rawValue.colorStops, function (colorStop) {\n          return {\n            offset: colorStop.offset,\n            color: color.parse(colorStop.color)\n          };\n        });\n        if (isLinearGradient(rawValue)) {\n          valType = VALUE_TYPE_LINEAR_GRADIENT;\n        } else if (isRadialGradient(rawValue)) {\n          valType = VALUE_TYPE_RADIAL_GRADIENT;\n        }\n        value = parsedGradient;\n      }\n    }\n    if (len === 0) {\n      this.valType = valType;\n    } else if (valType !== this.valType || valType === VALUE_TYPE_UNKOWN) {\n      discrete = true;\n    }\n    this.discrete = this.discrete || discrete;\n    var kf = {\n      time: time,\n      value: value,\n      rawValue: rawValue,\n      percent: 0\n    };\n    if (easing) {\n      kf.easing = easing;\n      kf.easingFunc = isFunction(easing) ? easing : easingFuncs[easing] || createCubicEasingFunc(easing);\n    }\n    keyframes.push(kf);\n    return kf;\n  };\n  Track.prototype.prepare = function (maxTime, additiveTrack) {\n    var kfs = this.keyframes;\n    if (this._needsSort) {\n      kfs.sort(function (a, b) {\n        return a.time - b.time;\n      });\n    }\n    var valType = this.valType;\n    var kfsLen = kfs.length;\n    var lastKf = kfs[kfsLen - 1];\n    var isDiscrete = this.discrete;\n    var isArr = isArrayValueType(valType);\n    var isGradient = isGradientValueType(valType);\n    for (var i = 0; i < kfsLen; i++) {\n      var kf = kfs[i];\n      var value = kf.value;\n      var lastValue = lastKf.value;\n      kf.percent = kf.time / maxTime;\n      if (!isDiscrete) {\n        if (isArr && i !== kfsLen - 1) {\n          fillArray(value, lastValue, valType);\n        } else if (isGradient) {\n          fillColorStops(value.colorStops, lastValue.colorStops);\n        }\n      }\n    }\n    if (!isDiscrete && valType !== VALUE_TYPE_RADIAL_GRADIENT && additiveTrack && this.needsAnimate() && additiveTrack.needsAnimate() && valType === additiveTrack.valType && !additiveTrack._finished) {\n      this._additiveTrack = additiveTrack;\n      var startValue = kfs[0].value;\n      for (var i = 0; i < kfsLen; i++) {\n        if (valType === VALUE_TYPE_NUMBER) {\n          kfs[i].additiveValue = kfs[i].value - startValue;\n        } else if (valType === VALUE_TYPE_COLOR) {\n          kfs[i].additiveValue = add1DArray([], kfs[i].value, startValue, -1);\n        } else if (isArrayValueType(valType)) {\n          kfs[i].additiveValue = valType === VALUE_TYPE_1D_ARRAY ? add1DArray([], kfs[i].value, startValue, -1) : add2DArray([], kfs[i].value, startValue, -1);\n        }\n      }\n    }\n  };\n  Track.prototype.step = function (target, percent) {\n    if (this._finished) {\n      return;\n    }\n    if (this._additiveTrack && this._additiveTrack._finished) {\n      this._additiveTrack = null;\n    }\n    var isAdditive = this._additiveTrack != null;\n    var valueKey = isAdditive ? 'additiveValue' : 'value';\n    var valType = this.valType;\n    var keyframes = this.keyframes;\n    var kfsNum = keyframes.length;\n    var propName = this.propName;\n    var isValueColor = valType === VALUE_TYPE_COLOR;\n    var frameIdx;\n    var lastFrame = this._lastFr;\n    var mathMin = Math.min;\n    var frame;\n    var nextFrame;\n    if (kfsNum === 1) {\n      frame = nextFrame = keyframes[0];\n    } else {\n      if (percent < 0) {\n        frameIdx = 0;\n      } else if (percent < this._lastFrP) {\n        var start = mathMin(lastFrame + 1, kfsNum - 1);\n        for (frameIdx = start; frameIdx >= 0; frameIdx--) {\n          if (keyframes[frameIdx].percent <= percent) {\n            break;\n          }\n        }\n        frameIdx = mathMin(frameIdx, kfsNum - 2);\n      } else {\n        for (frameIdx = lastFrame; frameIdx < kfsNum; frameIdx++) {\n          if (keyframes[frameIdx].percent > percent) {\n            break;\n          }\n        }\n        frameIdx = mathMin(frameIdx - 1, kfsNum - 2);\n      }\n      nextFrame = keyframes[frameIdx + 1];\n      frame = keyframes[frameIdx];\n    }\n    if (!(frame && nextFrame)) {\n      return;\n    }\n    this._lastFr = frameIdx;\n    this._lastFrP = percent;\n    var interval = nextFrame.percent - frame.percent;\n    var w = interval === 0 ? 1 : mathMin((percent - frame.percent) / interval, 1);\n    if (nextFrame.easingFunc) {\n      w = nextFrame.easingFunc(w);\n    }\n    var targetArr = isAdditive ? this._additiveValue : isValueColor ? tmpRgba : target[propName];\n    if ((isArrayValueType(valType) || isValueColor) && !targetArr) {\n      targetArr = this._additiveValue = [];\n    }\n    if (this.discrete) {\n      target[propName] = w < 1 ? frame.rawValue : nextFrame.rawValue;\n    } else if (isArrayValueType(valType)) {\n      valType === VALUE_TYPE_1D_ARRAY ? interpolate1DArray(targetArr, frame[valueKey], nextFrame[valueKey], w) : interpolate2DArray(targetArr, frame[valueKey], nextFrame[valueKey], w);\n    } else if (isGradientValueType(valType)) {\n      var val = frame[valueKey];\n      var nextVal_1 = nextFrame[valueKey];\n      var isLinearGradient_1 = valType === VALUE_TYPE_LINEAR_GRADIENT;\n      target[propName] = {\n        type: isLinearGradient_1 ? 'linear' : 'radial',\n        x: interpolateNumber(val.x, nextVal_1.x, w),\n        y: interpolateNumber(val.y, nextVal_1.y, w),\n        colorStops: map(val.colorStops, function (colorStop, idx) {\n          var nextColorStop = nextVal_1.colorStops[idx];\n          return {\n            offset: interpolateNumber(colorStop.offset, nextColorStop.offset, w),\n            color: rgba2String(interpolate1DArray([], colorStop.color, nextColorStop.color, w))\n          };\n        }),\n        global: nextVal_1.global\n      };\n      if (isLinearGradient_1) {\n        target[propName].x2 = interpolateNumber(val.x2, nextVal_1.x2, w);\n        target[propName].y2 = interpolateNumber(val.y2, nextVal_1.y2, w);\n      } else {\n        target[propName].r = interpolateNumber(val.r, nextVal_1.r, w);\n      }\n    } else if (isValueColor) {\n      interpolate1DArray(targetArr, frame[valueKey], nextFrame[valueKey], w);\n      if (!isAdditive) {\n        target[propName] = rgba2String(targetArr);\n      }\n    } else {\n      var value = interpolateNumber(frame[valueKey], nextFrame[valueKey], w);\n      if (isAdditive) {\n        this._additiveValue = value;\n      } else {\n        target[propName] = value;\n      }\n    }\n    if (isAdditive) {\n      this._addToTarget(target);\n    }\n  };\n  Track.prototype._addToTarget = function (target) {\n    var valType = this.valType;\n    var propName = this.propName;\n    var additiveValue = this._additiveValue;\n    if (valType === VALUE_TYPE_NUMBER) {\n      target[propName] = target[propName] + additiveValue;\n    } else if (valType === VALUE_TYPE_COLOR) {\n      color.parse(target[propName], tmpRgba);\n      add1DArray(tmpRgba, tmpRgba, additiveValue, 1);\n      target[propName] = rgba2String(tmpRgba);\n    } else if (valType === VALUE_TYPE_1D_ARRAY) {\n      add1DArray(target[propName], target[propName], additiveValue, 1);\n    } else if (valType === VALUE_TYPE_2D_ARRAY) {\n      add2DArray(target[propName], target[propName], additiveValue, 1);\n    }\n  };\n  return Track;\n}();\nvar Animator = function () {\n  function Animator(target, loop, allowDiscreteAnimation, additiveTo) {\n    this._tracks = {};\n    this._trackKeys = [];\n    this._maxTime = 0;\n    this._started = 0;\n    this._clip = null;\n    this._target = target;\n    this._loop = loop;\n    if (loop && additiveTo) {\n      logError('Can\\' use additive animation on looped animation.');\n      return;\n    }\n    this._additiveAnimators = additiveTo;\n    this._allowDiscrete = allowDiscreteAnimation;\n  }\n  Animator.prototype.getMaxTime = function () {\n    return this._maxTime;\n  };\n  Animator.prototype.getDelay = function () {\n    return this._delay;\n  };\n  Animator.prototype.getLoop = function () {\n    return this._loop;\n  };\n  Animator.prototype.getTarget = function () {\n    return this._target;\n  };\n  Animator.prototype.changeTarget = function (target) {\n    this._target = target;\n  };\n  Animator.prototype.when = function (time, props, easing) {\n    return this.whenWithKeys(time, props, keys(props), easing);\n  };\n  Animator.prototype.whenWithKeys = function (time, props, propNames, easing) {\n    var tracks = this._tracks;\n    for (var i = 0; i < propNames.length; i++) {\n      var propName = propNames[i];\n      var track = tracks[propName];\n      if (!track) {\n        track = tracks[propName] = new Track(propName);\n        var initialValue = void 0;\n        var additiveTrack = this._getAdditiveTrack(propName);\n        if (additiveTrack) {\n          var addtiveTrackKfs = additiveTrack.keyframes;\n          var lastFinalKf = addtiveTrackKfs[addtiveTrackKfs.length - 1];\n          initialValue = lastFinalKf && lastFinalKf.value;\n          if (additiveTrack.valType === VALUE_TYPE_COLOR && initialValue) {\n            initialValue = rgba2String(initialValue);\n          }\n        } else {\n          initialValue = this._target[propName];\n        }\n        if (initialValue == null) {\n          continue;\n        }\n        if (time > 0) {\n          track.addKeyframe(0, cloneValue(initialValue), easing);\n        }\n        this._trackKeys.push(propName);\n      }\n      track.addKeyframe(time, cloneValue(props[propName]), easing);\n    }\n    this._maxTime = Math.max(this._maxTime, time);\n    return this;\n  };\n  Animator.prototype.pause = function () {\n    this._clip.pause();\n    this._paused = true;\n  };\n  Animator.prototype.resume = function () {\n    this._clip.resume();\n    this._paused = false;\n  };\n  Animator.prototype.isPaused = function () {\n    return !!this._paused;\n  };\n  Animator.prototype.duration = function (duration) {\n    this._maxTime = duration;\n    this._force = true;\n    return this;\n  };\n  Animator.prototype._doneCallback = function () {\n    this._setTracksFinished();\n    this._clip = null;\n    var doneList = this._doneCbs;\n    if (doneList) {\n      var len = doneList.length;\n      for (var i = 0; i < len; i++) {\n        doneList[i].call(this);\n      }\n    }\n  };\n  Animator.prototype._abortedCallback = function () {\n    this._setTracksFinished();\n    var animation = this.animation;\n    var abortedList = this._abortedCbs;\n    if (animation) {\n      animation.removeClip(this._clip);\n    }\n    this._clip = null;\n    if (abortedList) {\n      for (var i = 0; i < abortedList.length; i++) {\n        abortedList[i].call(this);\n      }\n    }\n  };\n  Animator.prototype._setTracksFinished = function () {\n    var tracks = this._tracks;\n    var tracksKeys = this._trackKeys;\n    for (var i = 0; i < tracksKeys.length; i++) {\n      tracks[tracksKeys[i]].setFinished();\n    }\n  };\n  Animator.prototype._getAdditiveTrack = function (trackName) {\n    var additiveTrack;\n    var additiveAnimators = this._additiveAnimators;\n    if (additiveAnimators) {\n      for (var i = 0; i < additiveAnimators.length; i++) {\n        var track = additiveAnimators[i].getTrack(trackName);\n        if (track) {\n          additiveTrack = track;\n        }\n      }\n    }\n    return additiveTrack;\n  };\n  Animator.prototype.start = function (easing) {\n    if (this._started > 0) {\n      return;\n    }\n    this._started = 1;\n    var self = this;\n    var tracks = [];\n    var maxTime = this._maxTime || 0;\n    for (var i = 0; i < this._trackKeys.length; i++) {\n      var propName = this._trackKeys[i];\n      var track = this._tracks[propName];\n      var additiveTrack = this._getAdditiveTrack(propName);\n      var kfs = track.keyframes;\n      var kfsNum = kfs.length;\n      track.prepare(maxTime, additiveTrack);\n      if (track.needsAnimate()) {\n        if (!this._allowDiscrete && track.discrete) {\n          var lastKf = kfs[kfsNum - 1];\n          if (lastKf) {\n            self._target[track.propName] = lastKf.rawValue;\n          }\n          track.setFinished();\n        } else {\n          tracks.push(track);\n        }\n      }\n    }\n    if (tracks.length || this._force) {\n      var clip = new Clip({\n        life: maxTime,\n        loop: this._loop,\n        delay: this._delay || 0,\n        onframe: function (percent) {\n          self._started = 2;\n          var additiveAnimators = self._additiveAnimators;\n          if (additiveAnimators) {\n            var stillHasAdditiveAnimator = false;\n            for (var i = 0; i < additiveAnimators.length; i++) {\n              if (additiveAnimators[i]._clip) {\n                stillHasAdditiveAnimator = true;\n                break;\n              }\n            }\n            if (!stillHasAdditiveAnimator) {\n              self._additiveAnimators = null;\n            }\n          }\n          for (var i = 0; i < tracks.length; i++) {\n            tracks[i].step(self._target, percent);\n          }\n          var onframeList = self._onframeCbs;\n          if (onframeList) {\n            for (var i = 0; i < onframeList.length; i++) {\n              onframeList[i](self._target, percent);\n            }\n          }\n        },\n        ondestroy: function () {\n          self._doneCallback();\n        }\n      });\n      this._clip = clip;\n      if (this.animation) {\n        this.animation.addClip(clip);\n      }\n      if (easing) {\n        clip.setEasing(easing);\n      }\n    } else {\n      this._doneCallback();\n    }\n    return this;\n  };\n  Animator.prototype.stop = function (forwardToLast) {\n    if (!this._clip) {\n      return;\n    }\n    var clip = this._clip;\n    if (forwardToLast) {\n      clip.onframe(1);\n    }\n    this._abortedCallback();\n  };\n  Animator.prototype.delay = function (time) {\n    this._delay = time;\n    return this;\n  };\n  Animator.prototype.during = function (cb) {\n    if (cb) {\n      if (!this._onframeCbs) {\n        this._onframeCbs = [];\n      }\n      this._onframeCbs.push(cb);\n    }\n    return this;\n  };\n  Animator.prototype.done = function (cb) {\n    if (cb) {\n      if (!this._doneCbs) {\n        this._doneCbs = [];\n      }\n      this._doneCbs.push(cb);\n    }\n    return this;\n  };\n  Animator.prototype.aborted = function (cb) {\n    if (cb) {\n      if (!this._abortedCbs) {\n        this._abortedCbs = [];\n      }\n      this._abortedCbs.push(cb);\n    }\n    return this;\n  };\n  Animator.prototype.getClip = function () {\n    return this._clip;\n  };\n  Animator.prototype.getTrack = function (propName) {\n    return this._tracks[propName];\n  };\n  Animator.prototype.getTracks = function () {\n    var _this = this;\n    return map(this._trackKeys, function (key) {\n      return _this._tracks[key];\n    });\n  };\n  Animator.prototype.stopTracks = function (propNames, forwardToLast) {\n    if (!propNames.length || !this._clip) {\n      return true;\n    }\n    var tracks = this._tracks;\n    var tracksKeys = this._trackKeys;\n    for (var i = 0; i < propNames.length; i++) {\n      var track = tracks[propNames[i]];\n      if (track && !track.isFinished()) {\n        if (forwardToLast) {\n          track.step(this._target, 1);\n        } else if (this._started === 1) {\n          track.step(this._target, 0);\n        }\n        track.setFinished();\n      }\n    }\n    var allAborted = true;\n    for (var i = 0; i < tracksKeys.length; i++) {\n      if (!tracks[tracksKeys[i]].isFinished()) {\n        allAborted = false;\n        break;\n      }\n    }\n    if (allAborted) {\n      this._abortedCallback();\n    }\n    return allAborted;\n  };\n  Animator.prototype.saveTo = function (target, trackKeys, firstOrLast) {\n    if (!target) {\n      return;\n    }\n    trackKeys = trackKeys || this._trackKeys;\n    for (var i = 0; i < trackKeys.length; i++) {\n      var propName = trackKeys[i];\n      var track = this._tracks[propName];\n      if (!track || track.isFinished()) {\n        continue;\n      }\n      var kfs = track.keyframes;\n      var kf = kfs[firstOrLast ? 0 : kfs.length - 1];\n      if (kf) {\n        target[propName] = cloneValue(kf.rawValue);\n      }\n    }\n  };\n  Animator.prototype.__changeFinalValue = function (finalProps, trackKeys) {\n    trackKeys = trackKeys || keys(finalProps);\n    for (var i = 0; i < trackKeys.length; i++) {\n      var propName = trackKeys[i];\n      var track = this._tracks[propName];\n      if (!track) {\n        continue;\n      }\n      var kfs = track.keyframes;\n      if (kfs.length > 1) {\n        var lastKf = kfs.pop();\n        track.addKeyframe(lastKf.time, finalProps[propName]);\n        track.prepare(this._maxTime, track.getAdditiveTrack());\n      }\n    }\n  };\n  return Animator;\n}();\nexport default Animator;", "var Point = function () {\n  function Point(x, y) {\n    this.x = x || 0;\n    this.y = y || 0;\n  }\n  Point.prototype.copy = function (other) {\n    this.x = other.x;\n    this.y = other.y;\n    return this;\n  };\n  Point.prototype.clone = function () {\n    return new Point(this.x, this.y);\n  };\n  Point.prototype.set = function (x, y) {\n    this.x = x;\n    this.y = y;\n    return this;\n  };\n  Point.prototype.equal = function (other) {\n    return other.x === this.x && other.y === this.y;\n  };\n  Point.prototype.add = function (other) {\n    this.x += other.x;\n    this.y += other.y;\n    return this;\n  };\n  Point.prototype.scale = function (scalar) {\n    this.x *= scalar;\n    this.y *= scalar;\n  };\n  Point.prototype.scaleAndAdd = function (other, scalar) {\n    this.x += other.x * scalar;\n    this.y += other.y * scalar;\n  };\n  Point.prototype.sub = function (other) {\n    this.x -= other.x;\n    this.y -= other.y;\n    return this;\n  };\n  Point.prototype.dot = function (other) {\n    return this.x * other.x + this.y * other.y;\n  };\n  Point.prototype.len = function () {\n    return Math.sqrt(this.x * this.x + this.y * this.y);\n  };\n  Point.prototype.lenSquare = function () {\n    return this.x * this.x + this.y * this.y;\n  };\n  Point.prototype.normalize = function () {\n    var len = this.len();\n    this.x /= len;\n    this.y /= len;\n    return this;\n  };\n  Point.prototype.distance = function (other) {\n    var dx = this.x - other.x;\n    var dy = this.y - other.y;\n    return Math.sqrt(dx * dx + dy * dy);\n  };\n  Point.prototype.distanceSquare = function (other) {\n    var dx = this.x - other.x;\n    var dy = this.y - other.y;\n    return dx * dx + dy * dy;\n  };\n  Point.prototype.negate = function () {\n    this.x = -this.x;\n    this.y = -this.y;\n    return this;\n  };\n  Point.prototype.transform = function (m) {\n    if (!m) {\n      return;\n    }\n    var x = this.x;\n    var y = this.y;\n    this.x = m[0] * x + m[2] * y + m[4];\n    this.y = m[1] * x + m[3] * y + m[5];\n    return this;\n  };\n  Point.prototype.toArray = function (out) {\n    out[0] = this.x;\n    out[1] = this.y;\n    return out;\n  };\n  Point.prototype.fromArray = function (input) {\n    this.x = input[0];\n    this.y = input[1];\n  };\n  Point.set = function (p, x, y) {\n    p.x = x;\n    p.y = y;\n  };\n  Point.copy = function (p, p2) {\n    p.x = p2.x;\n    p.y = p2.y;\n  };\n  Point.len = function (p) {\n    return Math.sqrt(p.x * p.x + p.y * p.y);\n  };\n  Point.lenSquare = function (p) {\n    return p.x * p.x + p.y * p.y;\n  };\n  Point.dot = function (p0, p1) {\n    return p0.x * p1.x + p0.y * p1.y;\n  };\n  Point.add = function (out, p0, p1) {\n    out.x = p0.x + p1.x;\n    out.y = p0.y + p1.y;\n  };\n  Point.sub = function (out, p0, p1) {\n    out.x = p0.x - p1.x;\n    out.y = p0.y - p1.y;\n  };\n  Point.scale = function (out, p0, scalar) {\n    out.x = p0.x * scalar;\n    out.y = p0.y * scalar;\n  };\n  Point.scaleAndAdd = function (out, p0, p1, scalar) {\n    out.x = p0.x + p1.x * scalar;\n    out.y = p0.y + p1.y * scalar;\n  };\n  Point.lerp = function (out, p0, p1, t) {\n    var onet = 1 - t;\n    out.x = onet * p0.x + t * p1.x;\n    out.y = onet * p0.y + t * p1.y;\n  };\n  return Point;\n}();\nexport default Point;", "import * as matrix from './matrix.js';\nimport Point from './Point.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar lt = new Point();\nvar rb = new Point();\nvar lb = new Point();\nvar rt = new Point();\nvar minTv = new Point();\nvar maxTv = new Point();\nvar BoundingRect = function () {\n  function BoundingRect(x, y, width, height) {\n    if (width < 0) {\n      x = x + width;\n      width = -width;\n    }\n    if (height < 0) {\n      y = y + height;\n      height = -height;\n    }\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n  BoundingRect.prototype.union = function (other) {\n    var x = mathMin(other.x, this.x);\n    var y = mathMin(other.y, this.y);\n    if (isFinite(this.x) && isFinite(this.width)) {\n      this.width = mathMax(other.x + other.width, this.x + this.width) - x;\n    } else {\n      this.width = other.width;\n    }\n    if (isFinite(this.y) && isFinite(this.height)) {\n      this.height = mathMax(other.y + other.height, this.y + this.height) - y;\n    } else {\n      this.height = other.height;\n    }\n    this.x = x;\n    this.y = y;\n  };\n  BoundingRect.prototype.applyTransform = function (m) {\n    BoundingRect.applyTransform(this, this, m);\n  };\n  BoundingRect.prototype.calculateTransform = function (b) {\n    var a = this;\n    var sx = b.width / a.width;\n    var sy = b.height / a.height;\n    var m = matrix.create();\n    matrix.translate(m, m, [-a.x, -a.y]);\n    matrix.scale(m, m, [sx, sy]);\n    matrix.translate(m, m, [b.x, b.y]);\n    return m;\n  };\n  BoundingRect.prototype.intersect = function (b, mtv) {\n    if (!b) {\n      return false;\n    }\n    if (!(b instanceof BoundingRect)) {\n      b = BoundingRect.create(b);\n    }\n    var a = this;\n    var ax0 = a.x;\n    var ax1 = a.x + a.width;\n    var ay0 = a.y;\n    var ay1 = a.y + a.height;\n    var bx0 = b.x;\n    var bx1 = b.x + b.width;\n    var by0 = b.y;\n    var by1 = b.y + b.height;\n    var overlap = !(ax1 < bx0 || bx1 < ax0 || ay1 < by0 || by1 < ay0);\n    if (mtv) {\n      var dMin = Infinity;\n      var dMax = 0;\n      var d0 = Math.abs(ax1 - bx0);\n      var d1 = Math.abs(bx1 - ax0);\n      var d2 = Math.abs(ay1 - by0);\n      var d3 = Math.abs(by1 - ay0);\n      var dx = Math.min(d0, d1);\n      var dy = Math.min(d2, d3);\n      if (ax1 < bx0 || bx1 < ax0) {\n        if (dx > dMax) {\n          dMax = dx;\n          if (d0 < d1) {\n            Point.set(maxTv, -d0, 0);\n          } else {\n            Point.set(maxTv, d1, 0);\n          }\n        }\n      } else {\n        if (dx < dMin) {\n          dMin = dx;\n          if (d0 < d1) {\n            Point.set(minTv, d0, 0);\n          } else {\n            Point.set(minTv, -d1, 0);\n          }\n        }\n      }\n      if (ay1 < by0 || by1 < ay0) {\n        if (dy > dMax) {\n          dMax = dy;\n          if (d2 < d3) {\n            Point.set(maxTv, 0, -d2);\n          } else {\n            Point.set(maxTv, 0, d3);\n          }\n        }\n      } else {\n        if (dx < dMin) {\n          dMin = dx;\n          if (d2 < d3) {\n            Point.set(minTv, 0, d2);\n          } else {\n            Point.set(minTv, 0, -d3);\n          }\n        }\n      }\n    }\n    if (mtv) {\n      Point.copy(mtv, overlap ? minTv : maxTv);\n    }\n    return overlap;\n  };\n  BoundingRect.prototype.contain = function (x, y) {\n    var rect = this;\n    return x >= rect.x && x <= rect.x + rect.width && y >= rect.y && y <= rect.y + rect.height;\n  };\n  BoundingRect.prototype.clone = function () {\n    return new BoundingRect(this.x, this.y, this.width, this.height);\n  };\n  BoundingRect.prototype.copy = function (other) {\n    BoundingRect.copy(this, other);\n  };\n  BoundingRect.prototype.plain = function () {\n    return {\n      x: this.x,\n      y: this.y,\n      width: this.width,\n      height: this.height\n    };\n  };\n  BoundingRect.prototype.isFinite = function () {\n    return isFinite(this.x) && isFinite(this.y) && isFinite(this.width) && isFinite(this.height);\n  };\n  BoundingRect.prototype.isZero = function () {\n    return this.width === 0 || this.height === 0;\n  };\n  BoundingRect.create = function (rect) {\n    return new BoundingRect(rect.x, rect.y, rect.width, rect.height);\n  };\n  BoundingRect.copy = function (target, source) {\n    target.x = source.x;\n    target.y = source.y;\n    target.width = source.width;\n    target.height = source.height;\n  };\n  BoundingRect.applyTransform = function (target, source, m) {\n    if (!m) {\n      if (target !== source) {\n        BoundingRect.copy(target, source);\n      }\n      return;\n    }\n    if (m[1] < 1e-5 && m[1] > -1e-5 && m[2] < 1e-5 && m[2] > -1e-5) {\n      var sx = m[0];\n      var sy = m[3];\n      var tx = m[4];\n      var ty = m[5];\n      target.x = source.x * sx + tx;\n      target.y = source.y * sy + ty;\n      target.width = source.width * sx;\n      target.height = source.height * sy;\n      if (target.width < 0) {\n        target.x += target.width;\n        target.width = -target.width;\n      }\n      if (target.height < 0) {\n        target.y += target.height;\n        target.height = -target.height;\n      }\n      return;\n    }\n    lt.x = lb.x = source.x;\n    lt.y = rt.y = source.y;\n    rb.x = rt.x = source.x + source.width;\n    rb.y = lb.y = source.y + source.height;\n    lt.transform(m);\n    rt.transform(m);\n    rb.transform(m);\n    lb.transform(m);\n    target.x = mathMin(lt.x, rb.x, lb.x, rt.x);\n    target.y = mathMin(lt.y, rb.y, lb.y, rt.y);\n    var maxX = mathMax(lt.x, rb.x, lb.x, rt.x);\n    var maxY = mathMax(lt.y, rb.y, lb.y, rt.y);\n    target.width = maxX - target.x;\n    target.height = maxY - target.y;\n  };\n  return BoundingRect;\n}();\nexport default BoundingRect;", "var Eventful = function () {\n  function Eventful(eventProcessors) {\n    if (eventProcessors) {\n      this._$eventProcessor = eventProcessors;\n    }\n  }\n  Eventful.prototype.on = function (event, query, handler, context) {\n    if (!this._$handlers) {\n      this._$handlers = {};\n    }\n    var _h = this._$handlers;\n    if (typeof query === 'function') {\n      context = handler;\n      handler = query;\n      query = null;\n    }\n    if (!handler || !event) {\n      return this;\n    }\n    var eventProcessor = this._$eventProcessor;\n    if (query != null && eventProcessor && eventProcessor.normalizeQuery) {\n      query = eventProcessor.normalizeQuery(query);\n    }\n    if (!_h[event]) {\n      _h[event] = [];\n    }\n    for (var i = 0; i < _h[event].length; i++) {\n      if (_h[event][i].h === handler) {\n        return this;\n      }\n    }\n    var wrap = {\n      h: handler,\n      query: query,\n      ctx: context || this,\n      callAtLast: handler.zrEventfulCallAtLast\n    };\n    var lastIndex = _h[event].length - 1;\n    var lastWrap = _h[event][lastIndex];\n    lastWrap && lastWrap.callAtLast ? _h[event].splice(lastIndex, 0, wrap) : _h[event].push(wrap);\n    return this;\n  };\n  Eventful.prototype.isSilent = function (eventName) {\n    var _h = this._$handlers;\n    return !_h || !_h[eventName] || !_h[eventName].length;\n  };\n  Eventful.prototype.off = function (eventType, handler) {\n    var _h = this._$handlers;\n    if (!_h) {\n      return this;\n    }\n    if (!eventType) {\n      this._$handlers = {};\n      return this;\n    }\n    if (handler) {\n      if (_h[eventType]) {\n        var newList = [];\n        for (var i = 0, l = _h[eventType].length; i < l; i++) {\n          if (_h[eventType][i].h !== handler) {\n            newList.push(_h[eventType][i]);\n          }\n        }\n        _h[eventType] = newList;\n      }\n      if (_h[eventType] && _h[eventType].length === 0) {\n        delete _h[eventType];\n      }\n    } else {\n      delete _h[eventType];\n    }\n    return this;\n  };\n  Eventful.prototype.trigger = function (eventType) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    if (!this._$handlers) {\n      return this;\n    }\n    var _h = this._$handlers[eventType];\n    var eventProcessor = this._$eventProcessor;\n    if (_h) {\n      var argLen = args.length;\n      var len = _h.length;\n      for (var i = 0; i < len; i++) {\n        var hItem = _h[i];\n        if (eventProcessor && eventProcessor.filter && hItem.query != null && !eventProcessor.filter(eventType, hItem.query)) {\n          continue;\n        }\n        switch (argLen) {\n          case 0:\n            hItem.h.call(hItem.ctx);\n            break;\n          case 1:\n            hItem.h.call(hItem.ctx, args[0]);\n            break;\n          case 2:\n            hItem.h.call(hItem.ctx, args[0], args[1]);\n            break;\n          default:\n            hItem.h.apply(hItem.ctx, args);\n            break;\n        }\n      }\n    }\n    eventProcessor && eventProcessor.afterTrigger && eventProcessor.afterTrigger(eventType);\n    return this;\n  };\n  Eventful.prototype.triggerWithContext = function (type) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    if (!this._$handlers) {\n      return this;\n    }\n    var _h = this._$handlers[type];\n    var eventProcessor = this._$eventProcessor;\n    if (_h) {\n      var argLen = args.length;\n      var ctx = args[argLen - 1];\n      var len = _h.length;\n      for (var i = 0; i < len; i++) {\n        var hItem = _h[i];\n        if (eventProcessor && eventProcessor.filter && hItem.query != null && !eventProcessor.filter(type, hItem.query)) {\n          continue;\n        }\n        switch (argLen) {\n          case 0:\n            hItem.h.call(ctx);\n            break;\n          case 1:\n            hItem.h.call(ctx, args[0]);\n            break;\n          case 2:\n            hItem.h.call(ctx, args[0], args[1]);\n            break;\n          default:\n            hItem.h.apply(ctx, args.slice(1, argLen - 1));\n            break;\n        }\n      }\n    }\n    eventProcessor && eventProcessor.afterTrigger && eventProcessor.afterTrigger(type);\n    return this;\n  };\n  return Eventful;\n}();\nexport default Eventful;", "import BoundingRect from '../core/BoundingRect.js';\nimport LRU from '../core/LRU.js';\nimport { DEFAULT_FONT, platformApi } from '../core/platform.js';\nvar textWidthCache = {};\nexport function getWidth(text, font) {\n  font = font || DEFAULT_FONT;\n  var cacheOfFont = textWidthCache[font];\n  if (!cacheOfFont) {\n    cacheOfFont = textWidthCache[font] = new LRU(500);\n  }\n  var width = cacheOfFont.get(text);\n  if (width == null) {\n    width = platformApi.measureText(text, font).width;\n    cacheOfFont.put(text, width);\n  }\n  return width;\n}\nexport function innerGetBoundingRect(text, font, textAlign, textBaseline) {\n  var width = getWidth(text, font);\n  var height = getLineHeight(font);\n  var x = adjustTextX(0, width, textAlign);\n  var y = adjustTextY(0, height, textBaseline);\n  var rect = new BoundingRect(x, y, width, height);\n  return rect;\n}\nexport function getBoundingRect(text, font, textAlign, textBaseline) {\n  var textLines = ((text || '') + '').split('\\n');\n  var len = textLines.length;\n  if (len === 1) {\n    return innerGetBoundingRect(textLines[0], font, textAlign, textBaseline);\n  } else {\n    var uniondRect = new BoundingRect(0, 0, 0, 0);\n    for (var i = 0; i < textLines.length; i++) {\n      var rect = innerGetBoundingRect(textLines[i], font, textAlign, textBaseline);\n      i === 0 ? uniondRect.copy(rect) : uniondRect.union(rect);\n    }\n    return uniondRect;\n  }\n}\nexport function adjustTextX(x, width, textAlign) {\n  if (textAlign === 'right') {\n    x -= width;\n  } else if (textAlign === 'center') {\n    x -= width / 2;\n  }\n  return x;\n}\nexport function adjustTextY(y, height, verticalAlign) {\n  if (verticalAlign === 'middle') {\n    y -= height / 2;\n  } else if (verticalAlign === 'bottom') {\n    y -= height;\n  }\n  return y;\n}\nexport function getLineHeight(font) {\n  return getWidth('国', font);\n}\nexport function measureText(text, font) {\n  return platformApi.measureText(text, font);\n}\nexport function parsePercent(value, maxValue) {\n  if (typeof value === 'string') {\n    if (value.lastIndexOf('%') >= 0) {\n      return parseFloat(value) / 100 * maxValue;\n    }\n    return parseFloat(value);\n  }\n  return value;\n}\nexport function calculateTextPosition(out, opts, rect) {\n  var textPosition = opts.position || 'inside';\n  var distance = opts.distance != null ? opts.distance : 5;\n  var height = rect.height;\n  var width = rect.width;\n  var halfHeight = height / 2;\n  var x = rect.x;\n  var y = rect.y;\n  var textAlign = 'left';\n  var textVerticalAlign = 'top';\n  if (textPosition instanceof Array) {\n    x += parsePercent(textPosition[0], rect.width);\n    y += parsePercent(textPosition[1], rect.height);\n    textAlign = null;\n    textVerticalAlign = null;\n  } else {\n    switch (textPosition) {\n      case 'left':\n        x -= distance;\n        y += halfHeight;\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      case 'right':\n        x += distance + width;\n        y += halfHeight;\n        textVerticalAlign = 'middle';\n        break;\n      case 'top':\n        x += width / 2;\n        y -= distance;\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'bottom':\n        x += width / 2;\n        y += height + distance;\n        textAlign = 'center';\n        break;\n      case 'inside':\n        x += width / 2;\n        y += halfHeight;\n        textAlign = 'center';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideLeft':\n        x += distance;\n        y += halfHeight;\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideRight':\n        x += width - distance;\n        y += halfHeight;\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideTop':\n        x += width / 2;\n        y += distance;\n        textAlign = 'center';\n        break;\n      case 'insideBottom':\n        x += width / 2;\n        y += height - distance;\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'insideTopLeft':\n        x += distance;\n        y += distance;\n        break;\n      case 'insideTopRight':\n        x += width - distance;\n        y += distance;\n        textAlign = 'right';\n        break;\n      case 'insideBottomLeft':\n        x += distance;\n        y += height - distance;\n        textVerticalAlign = 'bottom';\n        break;\n      case 'insideBottomRight':\n        x += width - distance;\n        y += height - distance;\n        textAlign = 'right';\n        textVerticalAlign = 'bottom';\n        break;\n    }\n  }\n  out = out || {};\n  out.x = x;\n  out.y = y;\n  out.align = textAlign;\n  out.verticalAlign = textVerticalAlign;\n  return out;\n}", "import env from './core/env.js';\nvar dpr = 1;\nif (env.hasGlobalWindow) {\n  dpr = Math.max(window.devicePixelRatio || window.screen && window.screen.deviceXDPI / window.screen.logicalXDPI || 1, 1);\n}\nexport var debugMode = 0;\nexport var devicePixelRatio = dpr;\nexport var DARK_MODE_THRESHOLD = 0.4;\nexport var DARK_LABEL_COLOR = '#333';\nexport var LIGHT_LABEL_COLOR = '#ccc';\nexport var LIGHTER_LABEL_COLOR = '#eee';", "export var REDRAW_BIT = 1;\nexport var STYLE_CHANGED_BIT = 2;\nexport var SHAPE_CHANGED_BIT = 4;", "import Transformable, { TRANSFORMABLE_PROPS } from './core/Transformable.js';\nimport Animator, { cloneValue } from './animation/Animator.js';\nimport BoundingRect from './core/BoundingRect.js';\nimport Eventful from './core/Eventful.js';\nimport { calculateTextPosition, parsePercent } from './contain/text.js';\nimport { guid, isObject, keys, extend, indexOf, logError, mixin, isArrayLike, isTypedArray, isGradientObject, filter, reduce } from './core/util.js';\nimport { LIGHT_LABEL_COLOR, DARK_LABEL_COLOR } from './config.js';\nimport { parse, stringify } from './tool/color.js';\nimport { REDRAW_BIT } from './graphic/constants.js';\nexport var PRESERVED_NORMAL_STATE = '__zr_normal__';\nvar PRIMARY_STATES_KEYS = TRANSFORMABLE_PROPS.concat(['ignore']);\nvar DEFAULT_ANIMATABLE_MAP = reduce(TRANSFORMABLE_PROPS, function (obj, key) {\n  obj[key] = true;\n  return obj;\n}, {\n  ignore: false\n});\nvar tmpTextPosCalcRes = {};\nvar tmpBoundingRect = new BoundingRect(0, 0, 0, 0);\nvar Element = function () {\n  function Element(props) {\n    this.id = guid();\n    this.animators = [];\n    this.currentStates = [];\n    this.states = {};\n    this._init(props);\n  }\n  Element.prototype._init = function (props) {\n    this.attr(props);\n  };\n  Element.prototype.drift = function (dx, dy, e) {\n    switch (this.draggable) {\n      case 'horizontal':\n        dy = 0;\n        break;\n      case 'vertical':\n        dx = 0;\n        break;\n    }\n    var m = this.transform;\n    if (!m) {\n      m = this.transform = [1, 0, 0, 1, 0, 0];\n    }\n    m[4] += dx;\n    m[5] += dy;\n    this.decomposeTransform();\n    this.markRedraw();\n  };\n  Element.prototype.beforeUpdate = function () {};\n  Element.prototype.afterUpdate = function () {};\n  Element.prototype.update = function () {\n    this.updateTransform();\n    if (this.__dirty) {\n      this.updateInnerText();\n    }\n  };\n  Element.prototype.updateInnerText = function (forceUpdate) {\n    var textEl = this._textContent;\n    if (textEl && (!textEl.ignore || forceUpdate)) {\n      if (!this.textConfig) {\n        this.textConfig = {};\n      }\n      var textConfig = this.textConfig;\n      var isLocal = textConfig.local;\n      var innerTransformable = textEl.innerTransformable;\n      var textAlign = void 0;\n      var textVerticalAlign = void 0;\n      var textStyleChanged = false;\n      innerTransformable.parent = isLocal ? this : null;\n      var innerOrigin = false;\n      innerTransformable.copyTransform(textEl);\n      if (textConfig.position != null) {\n        var layoutRect = tmpBoundingRect;\n        if (textConfig.layoutRect) {\n          layoutRect.copy(textConfig.layoutRect);\n        } else {\n          layoutRect.copy(this.getBoundingRect());\n        }\n        if (!isLocal) {\n          layoutRect.applyTransform(this.transform);\n        }\n        if (this.calculateTextPosition) {\n          this.calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n        } else {\n          calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n        }\n        innerTransformable.x = tmpTextPosCalcRes.x;\n        innerTransformable.y = tmpTextPosCalcRes.y;\n        textAlign = tmpTextPosCalcRes.align;\n        textVerticalAlign = tmpTextPosCalcRes.verticalAlign;\n        var textOrigin = textConfig.origin;\n        if (textOrigin && textConfig.rotation != null) {\n          var relOriginX = void 0;\n          var relOriginY = void 0;\n          if (textOrigin === 'center') {\n            relOriginX = layoutRect.width * 0.5;\n            relOriginY = layoutRect.height * 0.5;\n          } else {\n            relOriginX = parsePercent(textOrigin[0], layoutRect.width);\n            relOriginY = parsePercent(textOrigin[1], layoutRect.height);\n          }\n          innerOrigin = true;\n          innerTransformable.originX = -innerTransformable.x + relOriginX + (isLocal ? 0 : layoutRect.x);\n          innerTransformable.originY = -innerTransformable.y + relOriginY + (isLocal ? 0 : layoutRect.y);\n        }\n      }\n      if (textConfig.rotation != null) {\n        innerTransformable.rotation = textConfig.rotation;\n      }\n      var textOffset = textConfig.offset;\n      if (textOffset) {\n        innerTransformable.x += textOffset[0];\n        innerTransformable.y += textOffset[1];\n        if (!innerOrigin) {\n          innerTransformable.originX = -textOffset[0];\n          innerTransformable.originY = -textOffset[1];\n        }\n      }\n      var isInside = textConfig.inside == null ? typeof textConfig.position === 'string' && textConfig.position.indexOf('inside') >= 0 : textConfig.inside;\n      var innerTextDefaultStyle = this._innerTextDefaultStyle || (this._innerTextDefaultStyle = {});\n      var textFill = void 0;\n      var textStroke = void 0;\n      var autoStroke = void 0;\n      if (isInside && this.canBeInsideText()) {\n        textFill = textConfig.insideFill;\n        textStroke = textConfig.insideStroke;\n        if (textFill == null || textFill === 'auto') {\n          textFill = this.getInsideTextFill();\n        }\n        if (textStroke == null || textStroke === 'auto') {\n          textStroke = this.getInsideTextStroke(textFill);\n          autoStroke = true;\n        }\n      } else {\n        textFill = textConfig.outsideFill;\n        textStroke = textConfig.outsideStroke;\n        if (textFill == null || textFill === 'auto') {\n          textFill = this.getOutsideFill();\n        }\n        if (textStroke == null || textStroke === 'auto') {\n          textStroke = this.getOutsideStroke(textFill);\n          autoStroke = true;\n        }\n      }\n      textFill = textFill || '#000';\n      if (textFill !== innerTextDefaultStyle.fill || textStroke !== innerTextDefaultStyle.stroke || autoStroke !== innerTextDefaultStyle.autoStroke || textAlign !== innerTextDefaultStyle.align || textVerticalAlign !== innerTextDefaultStyle.verticalAlign) {\n        textStyleChanged = true;\n        innerTextDefaultStyle.fill = textFill;\n        innerTextDefaultStyle.stroke = textStroke;\n        innerTextDefaultStyle.autoStroke = autoStroke;\n        innerTextDefaultStyle.align = textAlign;\n        innerTextDefaultStyle.verticalAlign = textVerticalAlign;\n        textEl.setDefaultTextStyle(innerTextDefaultStyle);\n      }\n      textEl.__dirty |= REDRAW_BIT;\n      if (textStyleChanged) {\n        textEl.dirtyStyle(true);\n      }\n    }\n  };\n  Element.prototype.canBeInsideText = function () {\n    return true;\n  };\n  Element.prototype.getInsideTextFill = function () {\n    return '#fff';\n  };\n  Element.prototype.getInsideTextStroke = function (textFill) {\n    return '#000';\n  };\n  Element.prototype.getOutsideFill = function () {\n    return this.__zr && this.__zr.isDarkMode() ? LIGHT_LABEL_COLOR : DARK_LABEL_COLOR;\n  };\n  Element.prototype.getOutsideStroke = function (textFill) {\n    var backgroundColor = this.__zr && this.__zr.getBackgroundColor();\n    var colorArr = typeof backgroundColor === 'string' && parse(backgroundColor);\n    if (!colorArr) {\n      colorArr = [255, 255, 255, 1];\n    }\n    var alpha = colorArr[3];\n    var isDark = this.__zr.isDarkMode();\n    for (var i = 0; i < 3; i++) {\n      colorArr[i] = colorArr[i] * alpha + (isDark ? 0 : 255) * (1 - alpha);\n    }\n    colorArr[3] = 1;\n    return stringify(colorArr, 'rgba');\n  };\n  Element.prototype.traverse = function (cb, context) {};\n  Element.prototype.attrKV = function (key, value) {\n    if (key === 'textConfig') {\n      this.setTextConfig(value);\n    } else if (key === 'textContent') {\n      this.setTextContent(value);\n    } else if (key === 'clipPath') {\n      this.setClipPath(value);\n    } else if (key === 'extra') {\n      this.extra = this.extra || {};\n      extend(this.extra, value);\n    } else {\n      this[key] = value;\n    }\n  };\n  Element.prototype.hide = function () {\n    this.ignore = true;\n    this.markRedraw();\n  };\n  Element.prototype.show = function () {\n    this.ignore = false;\n    this.markRedraw();\n  };\n  Element.prototype.attr = function (keyOrObj, value) {\n    if (typeof keyOrObj === 'string') {\n      this.attrKV(keyOrObj, value);\n    } else if (isObject(keyOrObj)) {\n      var obj = keyOrObj;\n      var keysArr = keys(obj);\n      for (var i = 0; i < keysArr.length; i++) {\n        var key = keysArr[i];\n        this.attrKV(key, keyOrObj[key]);\n      }\n    }\n    this.markRedraw();\n    return this;\n  };\n  Element.prototype.saveCurrentToNormalState = function (toState) {\n    this._innerSaveToNormal(toState);\n    var normalState = this._normalState;\n    for (var i = 0; i < this.animators.length; i++) {\n      var animator = this.animators[i];\n      var fromStateTransition = animator.__fromStateTransition;\n      if (animator.getLoop() || fromStateTransition && fromStateTransition !== PRESERVED_NORMAL_STATE) {\n        continue;\n      }\n      var targetName = animator.targetName;\n      var target = targetName ? normalState[targetName] : normalState;\n      animator.saveTo(target);\n    }\n  };\n  Element.prototype._innerSaveToNormal = function (toState) {\n    var normalState = this._normalState;\n    if (!normalState) {\n      normalState = this._normalState = {};\n    }\n    if (toState.textConfig && !normalState.textConfig) {\n      normalState.textConfig = this.textConfig;\n    }\n    this._savePrimaryToNormal(toState, normalState, PRIMARY_STATES_KEYS);\n  };\n  Element.prototype._savePrimaryToNormal = function (toState, normalState, primaryKeys) {\n    for (var i = 0; i < primaryKeys.length; i++) {\n      var key = primaryKeys[i];\n      if (toState[key] != null && !(key in normalState)) {\n        normalState[key] = this[key];\n      }\n    }\n  };\n  Element.prototype.hasState = function () {\n    return this.currentStates.length > 0;\n  };\n  Element.prototype.getState = function (name) {\n    return this.states[name];\n  };\n  Element.prototype.ensureState = function (name) {\n    var states = this.states;\n    if (!states[name]) {\n      states[name] = {};\n    }\n    return states[name];\n  };\n  Element.prototype.clearStates = function (noAnimation) {\n    this.useState(PRESERVED_NORMAL_STATE, false, noAnimation);\n  };\n  Element.prototype.useState = function (stateName, keepCurrentStates, noAnimation, forceUseHoverLayer) {\n    var toNormalState = stateName === PRESERVED_NORMAL_STATE;\n    var hasStates = this.hasState();\n    if (!hasStates && toNormalState) {\n      return;\n    }\n    var currentStates = this.currentStates;\n    var animationCfg = this.stateTransition;\n    if (indexOf(currentStates, stateName) >= 0 && (keepCurrentStates || currentStates.length === 1)) {\n      return;\n    }\n    var state;\n    if (this.stateProxy && !toNormalState) {\n      state = this.stateProxy(stateName);\n    }\n    if (!state) {\n      state = this.states && this.states[stateName];\n    }\n    if (!state && !toNormalState) {\n      logError(\"State \" + stateName + \" not exists.\");\n      return;\n    }\n    if (!toNormalState) {\n      this.saveCurrentToNormalState(state);\n    }\n    var useHoverLayer = !!(state && state.hoverLayer || forceUseHoverLayer);\n    if (useHoverLayer) {\n      this._toggleHoverLayerFlag(true);\n    }\n    this._applyStateObj(stateName, state, this._normalState, keepCurrentStates, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n    var textContent = this._textContent;\n    var textGuide = this._textGuide;\n    if (textContent) {\n      textContent.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n    }\n    if (textGuide) {\n      textGuide.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n    }\n    if (toNormalState) {\n      this.currentStates = [];\n      this._normalState = {};\n    } else {\n      if (!keepCurrentStates) {\n        this.currentStates = [stateName];\n      } else {\n        this.currentStates.push(stateName);\n      }\n    }\n    this._updateAnimationTargets();\n    this.markRedraw();\n    if (!useHoverLayer && this.__inHover) {\n      this._toggleHoverLayerFlag(false);\n      this.__dirty &= ~REDRAW_BIT;\n    }\n    return state;\n  };\n  Element.prototype.useStates = function (states, noAnimation, forceUseHoverLayer) {\n    if (!states.length) {\n      this.clearStates();\n    } else {\n      var stateObjects = [];\n      var currentStates = this.currentStates;\n      var len = states.length;\n      var notChange = len === currentStates.length;\n      if (notChange) {\n        for (var i = 0; i < len; i++) {\n          if (states[i] !== currentStates[i]) {\n            notChange = false;\n            break;\n          }\n        }\n      }\n      if (notChange) {\n        return;\n      }\n      for (var i = 0; i < len; i++) {\n        var stateName = states[i];\n        var stateObj = void 0;\n        if (this.stateProxy) {\n          stateObj = this.stateProxy(stateName, states);\n        }\n        if (!stateObj) {\n          stateObj = this.states[stateName];\n        }\n        if (stateObj) {\n          stateObjects.push(stateObj);\n        }\n      }\n      var lastStateObj = stateObjects[len - 1];\n      var useHoverLayer = !!(lastStateObj && lastStateObj.hoverLayer || forceUseHoverLayer);\n      if (useHoverLayer) {\n        this._toggleHoverLayerFlag(true);\n      }\n      var mergedState = this._mergeStates(stateObjects);\n      var animationCfg = this.stateTransition;\n      this.saveCurrentToNormalState(mergedState);\n      this._applyStateObj(states.join(','), mergedState, this._normalState, false, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n      var textContent = this._textContent;\n      var textGuide = this._textGuide;\n      if (textContent) {\n        textContent.useStates(states, noAnimation, useHoverLayer);\n      }\n      if (textGuide) {\n        textGuide.useStates(states, noAnimation, useHoverLayer);\n      }\n      this._updateAnimationTargets();\n      this.currentStates = states.slice();\n      this.markRedraw();\n      if (!useHoverLayer && this.__inHover) {\n        this._toggleHoverLayerFlag(false);\n        this.__dirty &= ~REDRAW_BIT;\n      }\n    }\n  };\n  Element.prototype._updateAnimationTargets = function () {\n    for (var i = 0; i < this.animators.length; i++) {\n      var animator = this.animators[i];\n      if (animator.targetName) {\n        animator.changeTarget(this[animator.targetName]);\n      }\n    }\n  };\n  Element.prototype.removeState = function (state) {\n    var idx = indexOf(this.currentStates, state);\n    if (idx >= 0) {\n      var currentStates = this.currentStates.slice();\n      currentStates.splice(idx, 1);\n      this.useStates(currentStates);\n    }\n  };\n  Element.prototype.replaceState = function (oldState, newState, forceAdd) {\n    var currentStates = this.currentStates.slice();\n    var idx = indexOf(currentStates, oldState);\n    var newStateExists = indexOf(currentStates, newState) >= 0;\n    if (idx >= 0) {\n      if (!newStateExists) {\n        currentStates[idx] = newState;\n      } else {\n        currentStates.splice(idx, 1);\n      }\n    } else if (forceAdd && !newStateExists) {\n      currentStates.push(newState);\n    }\n    this.useStates(currentStates);\n  };\n  Element.prototype.toggleState = function (state, enable) {\n    if (enable) {\n      this.useState(state, true);\n    } else {\n      this.removeState(state);\n    }\n  };\n  Element.prototype._mergeStates = function (states) {\n    var mergedState = {};\n    var mergedTextConfig;\n    for (var i = 0; i < states.length; i++) {\n      var state = states[i];\n      extend(mergedState, state);\n      if (state.textConfig) {\n        mergedTextConfig = mergedTextConfig || {};\n        extend(mergedTextConfig, state.textConfig);\n      }\n    }\n    if (mergedTextConfig) {\n      mergedState.textConfig = mergedTextConfig;\n    }\n    return mergedState;\n  };\n  Element.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n    var needsRestoreToNormal = !(state && keepCurrentStates);\n    if (state && state.textConfig) {\n      this.textConfig = extend({}, keepCurrentStates ? this.textConfig : normalState.textConfig);\n      extend(this.textConfig, state.textConfig);\n    } else if (needsRestoreToNormal) {\n      if (normalState.textConfig) {\n        this.textConfig = normalState.textConfig;\n      }\n    }\n    var transitionTarget = {};\n    var hasTransition = false;\n    for (var i = 0; i < PRIMARY_STATES_KEYS.length; i++) {\n      var key = PRIMARY_STATES_KEYS[i];\n      var propNeedsTransition = transition && DEFAULT_ANIMATABLE_MAP[key];\n      if (state && state[key] != null) {\n        if (propNeedsTransition) {\n          hasTransition = true;\n          transitionTarget[key] = state[key];\n        } else {\n          this[key] = state[key];\n        }\n      } else if (needsRestoreToNormal) {\n        if (normalState[key] != null) {\n          if (propNeedsTransition) {\n            hasTransition = true;\n            transitionTarget[key] = normalState[key];\n          } else {\n            this[key] = normalState[key];\n          }\n        }\n      }\n    }\n    if (!transition) {\n      for (var i = 0; i < this.animators.length; i++) {\n        var animator = this.animators[i];\n        var targetName = animator.targetName;\n        if (!animator.getLoop()) {\n          animator.__changeFinalValue(targetName ? (state || normalState)[targetName] : state || normalState);\n        }\n      }\n    }\n    if (hasTransition) {\n      this._transitionState(stateName, transitionTarget, animationCfg);\n    }\n  };\n  Element.prototype._attachComponent = function (componentEl) {\n    if (componentEl.__zr && !componentEl.__hostTarget) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error('Text element has been added to zrender.');\n      }\n      return;\n    }\n    if (componentEl === this) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error('Recursive component attachment.');\n      }\n      return;\n    }\n    var zr = this.__zr;\n    if (zr) {\n      componentEl.addSelfToZr(zr);\n    }\n    componentEl.__zr = zr;\n    componentEl.__hostTarget = this;\n  };\n  Element.prototype._detachComponent = function (componentEl) {\n    if (componentEl.__zr) {\n      componentEl.removeSelfFromZr(componentEl.__zr);\n    }\n    componentEl.__zr = null;\n    componentEl.__hostTarget = null;\n  };\n  Element.prototype.getClipPath = function () {\n    return this._clipPath;\n  };\n  Element.prototype.setClipPath = function (clipPath) {\n    if (this._clipPath && this._clipPath !== clipPath) {\n      this.removeClipPath();\n    }\n    this._attachComponent(clipPath);\n    this._clipPath = clipPath;\n    this.markRedraw();\n  };\n  Element.prototype.removeClipPath = function () {\n    var clipPath = this._clipPath;\n    if (clipPath) {\n      this._detachComponent(clipPath);\n      this._clipPath = null;\n      this.markRedraw();\n    }\n  };\n  Element.prototype.getTextContent = function () {\n    return this._textContent;\n  };\n  Element.prototype.setTextContent = function (textEl) {\n    var previousTextContent = this._textContent;\n    if (previousTextContent === textEl) {\n      return;\n    }\n    if (previousTextContent && previousTextContent !== textEl) {\n      this.removeTextContent();\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (textEl.__zr && !textEl.__hostTarget) {\n        throw new Error('Text element has been added to zrender.');\n      }\n    }\n    textEl.innerTransformable = new Transformable();\n    this._attachComponent(textEl);\n    this._textContent = textEl;\n    this.markRedraw();\n  };\n  Element.prototype.setTextConfig = function (cfg) {\n    if (!this.textConfig) {\n      this.textConfig = {};\n    }\n    extend(this.textConfig, cfg);\n    this.markRedraw();\n  };\n  Element.prototype.removeTextConfig = function () {\n    this.textConfig = null;\n    this.markRedraw();\n  };\n  Element.prototype.removeTextContent = function () {\n    var textEl = this._textContent;\n    if (textEl) {\n      textEl.innerTransformable = null;\n      this._detachComponent(textEl);\n      this._textContent = null;\n      this._innerTextDefaultStyle = null;\n      this.markRedraw();\n    }\n  };\n  Element.prototype.getTextGuideLine = function () {\n    return this._textGuide;\n  };\n  Element.prototype.setTextGuideLine = function (guideLine) {\n    if (this._textGuide && this._textGuide !== guideLine) {\n      this.removeTextGuideLine();\n    }\n    this._attachComponent(guideLine);\n    this._textGuide = guideLine;\n    this.markRedraw();\n  };\n  Element.prototype.removeTextGuideLine = function () {\n    var textGuide = this._textGuide;\n    if (textGuide) {\n      this._detachComponent(textGuide);\n      this._textGuide = null;\n      this.markRedraw();\n    }\n  };\n  Element.prototype.markRedraw = function () {\n    this.__dirty |= REDRAW_BIT;\n    var zr = this.__zr;\n    if (zr) {\n      if (this.__inHover) {\n        zr.refreshHover();\n      } else {\n        zr.refresh();\n      }\n    }\n    if (this.__hostTarget) {\n      this.__hostTarget.markRedraw();\n    }\n  };\n  Element.prototype.dirty = function () {\n    this.markRedraw();\n  };\n  Element.prototype._toggleHoverLayerFlag = function (inHover) {\n    this.__inHover = inHover;\n    var textContent = this._textContent;\n    var textGuide = this._textGuide;\n    if (textContent) {\n      textContent.__inHover = inHover;\n    }\n    if (textGuide) {\n      textGuide.__inHover = inHover;\n    }\n  };\n  Element.prototype.addSelfToZr = function (zr) {\n    if (this.__zr === zr) {\n      return;\n    }\n    this.__zr = zr;\n    var animators = this.animators;\n    if (animators) {\n      for (var i = 0; i < animators.length; i++) {\n        zr.animation.addAnimator(animators[i]);\n      }\n    }\n    if (this._clipPath) {\n      this._clipPath.addSelfToZr(zr);\n    }\n    if (this._textContent) {\n      this._textContent.addSelfToZr(zr);\n    }\n    if (this._textGuide) {\n      this._textGuide.addSelfToZr(zr);\n    }\n  };\n  Element.prototype.removeSelfFromZr = function (zr) {\n    if (!this.__zr) {\n      return;\n    }\n    this.__zr = null;\n    var animators = this.animators;\n    if (animators) {\n      for (var i = 0; i < animators.length; i++) {\n        zr.animation.removeAnimator(animators[i]);\n      }\n    }\n    if (this._clipPath) {\n      this._clipPath.removeSelfFromZr(zr);\n    }\n    if (this._textContent) {\n      this._textContent.removeSelfFromZr(zr);\n    }\n    if (this._textGuide) {\n      this._textGuide.removeSelfFromZr(zr);\n    }\n  };\n  Element.prototype.animate = function (key, loop, allowDiscreteAnimation) {\n    var target = key ? this[key] : this;\n    if (process.env.NODE_ENV !== 'production') {\n      if (!target) {\n        logError('Property \"' + key + '\" is not existed in element ' + this.id);\n        return;\n      }\n    }\n    var animator = new Animator(target, loop, allowDiscreteAnimation);\n    key && (animator.targetName = key);\n    this.addAnimator(animator, key);\n    return animator;\n  };\n  Element.prototype.addAnimator = function (animator, key) {\n    var zr = this.__zr;\n    var el = this;\n    animator.during(function () {\n      el.updateDuringAnimation(key);\n    }).done(function () {\n      var animators = el.animators;\n      var idx = indexOf(animators, animator);\n      if (idx >= 0) {\n        animators.splice(idx, 1);\n      }\n    });\n    this.animators.push(animator);\n    if (zr) {\n      zr.animation.addAnimator(animator);\n    }\n    zr && zr.wakeUp();\n  };\n  Element.prototype.updateDuringAnimation = function (key) {\n    this.markRedraw();\n  };\n  Element.prototype.stopAnimation = function (scope, forwardToLast) {\n    var animators = this.animators;\n    var len = animators.length;\n    var leftAnimators = [];\n    for (var i = 0; i < len; i++) {\n      var animator = animators[i];\n      if (!scope || scope === animator.scope) {\n        animator.stop(forwardToLast);\n      } else {\n        leftAnimators.push(animator);\n      }\n    }\n    this.animators = leftAnimators;\n    return this;\n  };\n  Element.prototype.animateTo = function (target, cfg, animationProps) {\n    animateTo(this, target, cfg, animationProps);\n  };\n  Element.prototype.animateFrom = function (target, cfg, animationProps) {\n    animateTo(this, target, cfg, animationProps, true);\n  };\n  Element.prototype._transitionState = function (stateName, target, cfg, animationProps) {\n    var animators = animateTo(this, target, cfg, animationProps);\n    for (var i = 0; i < animators.length; i++) {\n      animators[i].__fromStateTransition = stateName;\n    }\n  };\n  Element.prototype.getBoundingRect = function () {\n    return null;\n  };\n  Element.prototype.getPaintRect = function () {\n    return null;\n  };\n  Element.initDefaultProps = function () {\n    var elProto = Element.prototype;\n    elProto.type = 'element';\n    elProto.name = '';\n    elProto.ignore = elProto.silent = elProto.isGroup = elProto.draggable = elProto.dragging = elProto.ignoreClip = elProto.__inHover = false;\n    elProto.__dirty = REDRAW_BIT;\n    var logs = {};\n    function logDeprecatedError(key, xKey, yKey) {\n      if (!logs[key + xKey + yKey]) {\n        console.warn(\"DEPRECATED: '\" + key + \"' has been deprecated. use '\" + xKey + \"', '\" + yKey + \"' instead\");\n        logs[key + xKey + yKey] = true;\n      }\n    }\n    function createLegacyProperty(key, privateKey, xKey, yKey) {\n      Object.defineProperty(elProto, key, {\n        get: function () {\n          if (process.env.NODE_ENV !== 'production') {\n            logDeprecatedError(key, xKey, yKey);\n          }\n          if (!this[privateKey]) {\n            var pos = this[privateKey] = [];\n            enhanceArray(this, pos);\n          }\n          return this[privateKey];\n        },\n        set: function (pos) {\n          if (process.env.NODE_ENV !== 'production') {\n            logDeprecatedError(key, xKey, yKey);\n          }\n          this[xKey] = pos[0];\n          this[yKey] = pos[1];\n          this[privateKey] = pos;\n          enhanceArray(this, pos);\n        }\n      });\n      function enhanceArray(self, pos) {\n        Object.defineProperty(pos, 0, {\n          get: function () {\n            return self[xKey];\n          },\n          set: function (val) {\n            self[xKey] = val;\n          }\n        });\n        Object.defineProperty(pos, 1, {\n          get: function () {\n            return self[yKey];\n          },\n          set: function (val) {\n            self[yKey] = val;\n          }\n        });\n      }\n    }\n    if (Object.defineProperty) {\n      createLegacyProperty('position', '_legacyPos', 'x', 'y');\n      createLegacyProperty('scale', '_legacyScale', 'scaleX', 'scaleY');\n      createLegacyProperty('origin', '_legacyOrigin', 'originX', 'originY');\n    }\n  }();\n  return Element;\n}();\nmixin(Element, Eventful);\nmixin(Element, Transformable);\nfunction animateTo(animatable, target, cfg, animationProps, reverse) {\n  cfg = cfg || {};\n  var animators = [];\n  animateToShallow(animatable, '', animatable, target, cfg, animationProps, animators, reverse);\n  var finishCount = animators.length;\n  var doneHappened = false;\n  var cfgDone = cfg.done;\n  var cfgAborted = cfg.aborted;\n  var doneCb = function () {\n    doneHappened = true;\n    finishCount--;\n    if (finishCount <= 0) {\n      doneHappened ? cfgDone && cfgDone() : cfgAborted && cfgAborted();\n    }\n  };\n  var abortedCb = function () {\n    finishCount--;\n    if (finishCount <= 0) {\n      doneHappened ? cfgDone && cfgDone() : cfgAborted && cfgAborted();\n    }\n  };\n  if (!finishCount) {\n    cfgDone && cfgDone();\n  }\n  if (animators.length > 0 && cfg.during) {\n    animators[0].during(function (target, percent) {\n      cfg.during(percent);\n    });\n  }\n  for (var i = 0; i < animators.length; i++) {\n    var animator = animators[i];\n    if (doneCb) {\n      animator.done(doneCb);\n    }\n    if (abortedCb) {\n      animator.aborted(abortedCb);\n    }\n    if (cfg.force) {\n      animator.duration(cfg.duration);\n    }\n    animator.start(cfg.easing);\n  }\n  return animators;\n}\nfunction copyArrShallow(source, target, len) {\n  for (var i = 0; i < len; i++) {\n    source[i] = target[i];\n  }\n}\nfunction is2DArray(value) {\n  return isArrayLike(value[0]);\n}\nfunction copyValue(target, source, key) {\n  if (isArrayLike(source[key])) {\n    if (!isArrayLike(target[key])) {\n      target[key] = [];\n    }\n    if (isTypedArray(source[key])) {\n      var len = source[key].length;\n      if (target[key].length !== len) {\n        target[key] = new source[key].constructor(len);\n        copyArrShallow(target[key], source[key], len);\n      }\n    } else {\n      var sourceArr = source[key];\n      var targetArr = target[key];\n      var len0 = sourceArr.length;\n      if (is2DArray(sourceArr)) {\n        var len1 = sourceArr[0].length;\n        for (var i = 0; i < len0; i++) {\n          if (!targetArr[i]) {\n            targetArr[i] = Array.prototype.slice.call(sourceArr[i]);\n          } else {\n            copyArrShallow(targetArr[i], sourceArr[i], len1);\n          }\n        }\n      } else {\n        copyArrShallow(targetArr, sourceArr, len0);\n      }\n      targetArr.length = sourceArr.length;\n    }\n  } else {\n    target[key] = source[key];\n  }\n}\nfunction isValueSame(val1, val2) {\n  return val1 === val2 || isArrayLike(val1) && isArrayLike(val2) && is1DArraySame(val1, val2);\n}\nfunction is1DArraySame(arr0, arr1) {\n  var len = arr0.length;\n  if (len !== arr1.length) {\n    return false;\n  }\n  for (var i = 0; i < len; i++) {\n    if (arr0[i] !== arr1[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction animateToShallow(animatable, topKey, animateObj, target, cfg, animationProps, animators, reverse) {\n  var targetKeys = keys(target);\n  var duration = cfg.duration;\n  var delay = cfg.delay;\n  var additive = cfg.additive;\n  var setToFinal = cfg.setToFinal;\n  var animateAll = !isObject(animationProps);\n  var existsAnimators = animatable.animators;\n  var animationKeys = [];\n  for (var k = 0; k < targetKeys.length; k++) {\n    var innerKey = targetKeys[k];\n    var targetVal = target[innerKey];\n    if (targetVal != null && animateObj[innerKey] != null && (animateAll || animationProps[innerKey])) {\n      if (isObject(targetVal) && !isArrayLike(targetVal) && !isGradientObject(targetVal)) {\n        if (topKey) {\n          if (!reverse) {\n            animateObj[innerKey] = targetVal;\n            animatable.updateDuringAnimation(topKey);\n          }\n          continue;\n        }\n        animateToShallow(animatable, innerKey, animateObj[innerKey], targetVal, cfg, animationProps && animationProps[innerKey], animators, reverse);\n      } else {\n        animationKeys.push(innerKey);\n      }\n    } else if (!reverse) {\n      animateObj[innerKey] = targetVal;\n      animatable.updateDuringAnimation(topKey);\n      animationKeys.push(innerKey);\n    }\n  }\n  var keyLen = animationKeys.length;\n  if (!additive && keyLen) {\n    for (var i = 0; i < existsAnimators.length; i++) {\n      var animator = existsAnimators[i];\n      if (animator.targetName === topKey) {\n        var allAborted = animator.stopTracks(animationKeys);\n        if (allAborted) {\n          var idx = indexOf(existsAnimators, animator);\n          existsAnimators.splice(idx, 1);\n        }\n      }\n    }\n  }\n  if (!cfg.force) {\n    animationKeys = filter(animationKeys, function (key) {\n      return !isValueSame(target[key], animateObj[key]);\n    });\n    keyLen = animationKeys.length;\n  }\n  if (keyLen > 0 || cfg.force && !animators.length) {\n    var revertedSource = void 0;\n    var reversedTarget = void 0;\n    var sourceClone = void 0;\n    if (reverse) {\n      reversedTarget = {};\n      if (setToFinal) {\n        revertedSource = {};\n      }\n      for (var i = 0; i < keyLen; i++) {\n        var innerKey = animationKeys[i];\n        reversedTarget[innerKey] = animateObj[innerKey];\n        if (setToFinal) {\n          revertedSource[innerKey] = target[innerKey];\n        } else {\n          animateObj[innerKey] = target[innerKey];\n        }\n      }\n    } else if (setToFinal) {\n      sourceClone = {};\n      for (var i = 0; i < keyLen; i++) {\n        var innerKey = animationKeys[i];\n        sourceClone[innerKey] = cloneValue(animateObj[innerKey]);\n        copyValue(animateObj, target, innerKey);\n      }\n    }\n    var animator = new Animator(animateObj, false, false, additive ? filter(existsAnimators, function (animator) {\n      return animator.targetName === topKey;\n    }) : null);\n    animator.targetName = topKey;\n    if (cfg.scope) {\n      animator.scope = cfg.scope;\n    }\n    if (setToFinal && revertedSource) {\n      animator.whenWithKeys(0, revertedSource, animationKeys);\n    }\n    if (sourceClone) {\n      animator.whenWithKeys(0, sourceClone, animationKeys);\n    }\n    animator.whenWithKeys(duration == null ? 500 : duration, reverse ? reversedTarget : target, animationKeys).delay(delay || 0);\n    animatable.addAnimator(animator, topKey);\n    animators.push(animator);\n  }\n}\nexport default Element;", "import { __extends } from \"tslib\";\nimport Element from '../Element.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { keys, extend, createObject } from '../core/util.js';\nimport { REDRAW_BIT, STYLE_CHANGED_BIT } from './constants.js';\nvar STYLE_MAGIC_KEY = '__zr_style_' + Math.round(Math.random() * 10);\nexport var DEFAULT_COMMON_STYLE = {\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  shadowColor: '#000',\n  opacity: 1,\n  blend: 'source-over'\n};\nexport var DEFAULT_COMMON_ANIMATION_PROPS = {\n  style: {\n    shadowBlur: true,\n    shadowOffsetX: true,\n    shadowOffsetY: true,\n    shadowColor: true,\n    opacity: true\n  }\n};\nDEFAULT_COMMON_STYLE[STYLE_MAGIC_KEY] = true;\nvar PRIMARY_STATES_KEYS = ['z', 'z2', 'invisible'];\nvar PRIMARY_STATES_KEYS_IN_HOVER_LAYER = ['invisible'];\nvar Displayable = function (_super) {\n  __extends(Displayable, _super);\n  function Displayable(props) {\n    return _super.call(this, props) || this;\n  }\n  Displayable.prototype._init = function (props) {\n    var keysArr = keys(props);\n    for (var i = 0; i < keysArr.length; i++) {\n      var key = keysArr[i];\n      if (key === 'style') {\n        this.useStyle(props[key]);\n      } else {\n        _super.prototype.attrKV.call(this, key, props[key]);\n      }\n    }\n    if (!this.style) {\n      this.useStyle({});\n    }\n  };\n  Displayable.prototype.beforeBrush = function () {};\n  Displayable.prototype.afterBrush = function () {};\n  Displayable.prototype.innerBeforeBrush = function () {};\n  Displayable.prototype.innerAfterBrush = function () {};\n  Displayable.prototype.shouldBePainted = function (viewWidth, viewHeight, considerClipPath, considerAncestors) {\n    var m = this.transform;\n    if (this.ignore || this.invisible || this.style.opacity === 0 || this.culling && isDisplayableCulled(this, viewWidth, viewHeight) || m && !m[0] && !m[3]) {\n      return false;\n    }\n    if (considerClipPath && this.__clipPaths) {\n      for (var i = 0; i < this.__clipPaths.length; ++i) {\n        if (this.__clipPaths[i].isZeroArea()) {\n          return false;\n        }\n      }\n    }\n    if (considerAncestors && this.parent) {\n      var parent_1 = this.parent;\n      while (parent_1) {\n        if (parent_1.ignore) {\n          return false;\n        }\n        parent_1 = parent_1.parent;\n      }\n    }\n    return true;\n  };\n  Displayable.prototype.contain = function (x, y) {\n    return this.rectContain(x, y);\n  };\n  Displayable.prototype.traverse = function (cb, context) {\n    cb.call(context, this);\n  };\n  Displayable.prototype.rectContain = function (x, y) {\n    var coord = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    return rect.contain(coord[0], coord[1]);\n  };\n  Displayable.prototype.getPaintRect = function () {\n    var rect = this._paintRect;\n    if (!this._paintRect || this.__dirty) {\n      var transform = this.transform;\n      var elRect = this.getBoundingRect();\n      var style = this.style;\n      var shadowSize = style.shadowBlur || 0;\n      var shadowOffsetX = style.shadowOffsetX || 0;\n      var shadowOffsetY = style.shadowOffsetY || 0;\n      rect = this._paintRect || (this._paintRect = new BoundingRect(0, 0, 0, 0));\n      if (transform) {\n        BoundingRect.applyTransform(rect, elRect, transform);\n      } else {\n        rect.copy(elRect);\n      }\n      if (shadowSize || shadowOffsetX || shadowOffsetY) {\n        rect.width += shadowSize * 2 + Math.abs(shadowOffsetX);\n        rect.height += shadowSize * 2 + Math.abs(shadowOffsetY);\n        rect.x = Math.min(rect.x, rect.x + shadowOffsetX - shadowSize);\n        rect.y = Math.min(rect.y, rect.y + shadowOffsetY - shadowSize);\n      }\n      var tolerance = this.dirtyRectTolerance;\n      if (!rect.isZero()) {\n        rect.x = Math.floor(rect.x - tolerance);\n        rect.y = Math.floor(rect.y - tolerance);\n        rect.width = Math.ceil(rect.width + 1 + tolerance * 2);\n        rect.height = Math.ceil(rect.height + 1 + tolerance * 2);\n      }\n    }\n    return rect;\n  };\n  Displayable.prototype.setPrevPaintRect = function (paintRect) {\n    if (paintRect) {\n      this._prevPaintRect = this._prevPaintRect || new BoundingRect(0, 0, 0, 0);\n      this._prevPaintRect.copy(paintRect);\n    } else {\n      this._prevPaintRect = null;\n    }\n  };\n  Displayable.prototype.getPrevPaintRect = function () {\n    return this._prevPaintRect;\n  };\n  Displayable.prototype.animateStyle = function (loop) {\n    return this.animate('style', loop);\n  };\n  Displayable.prototype.updateDuringAnimation = function (targetKey) {\n    if (targetKey === 'style') {\n      this.dirtyStyle();\n    } else {\n      this.markRedraw();\n    }\n  };\n  Displayable.prototype.attrKV = function (key, value) {\n    if (key !== 'style') {\n      _super.prototype.attrKV.call(this, key, value);\n    } else {\n      if (!this.style) {\n        this.useStyle(value);\n      } else {\n        this.setStyle(value);\n      }\n    }\n  };\n  Displayable.prototype.setStyle = function (keyOrObj, value) {\n    if (typeof keyOrObj === 'string') {\n      this.style[keyOrObj] = value;\n    } else {\n      extend(this.style, keyOrObj);\n    }\n    this.dirtyStyle();\n    return this;\n  };\n  Displayable.prototype.dirtyStyle = function (notRedraw) {\n    if (!notRedraw) {\n      this.markRedraw();\n    }\n    this.__dirty |= STYLE_CHANGED_BIT;\n    if (this._rect) {\n      this._rect = null;\n    }\n  };\n  Displayable.prototype.dirty = function () {\n    this.dirtyStyle();\n  };\n  Displayable.prototype.styleChanged = function () {\n    return !!(this.__dirty & STYLE_CHANGED_BIT);\n  };\n  Displayable.prototype.styleUpdated = function () {\n    this.__dirty &= ~STYLE_CHANGED_BIT;\n  };\n  Displayable.prototype.createStyle = function (obj) {\n    return createObject(DEFAULT_COMMON_STYLE, obj);\n  };\n  Displayable.prototype.useStyle = function (obj) {\n    if (!obj[STYLE_MAGIC_KEY]) {\n      obj = this.createStyle(obj);\n    }\n    if (this.__inHover) {\n      this.__hoverStyle = obj;\n    } else {\n      this.style = obj;\n    }\n    this.dirtyStyle();\n  };\n  Displayable.prototype.isStyleObject = function (obj) {\n    return obj[STYLE_MAGIC_KEY];\n  };\n  Displayable.prototype._innerSaveToNormal = function (toState) {\n    _super.prototype._innerSaveToNormal.call(this, toState);\n    var normalState = this._normalState;\n    if (toState.style && !normalState.style) {\n      normalState.style = this._mergeStyle(this.createStyle(), this.style);\n    }\n    this._savePrimaryToNormal(toState, normalState, PRIMARY_STATES_KEYS);\n  };\n  Displayable.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n    _super.prototype._applyStateObj.call(this, stateName, state, normalState, keepCurrentStates, transition, animationCfg);\n    var needsRestoreToNormal = !(state && keepCurrentStates);\n    var targetStyle;\n    if (state && state.style) {\n      if (transition) {\n        if (keepCurrentStates) {\n          targetStyle = state.style;\n        } else {\n          targetStyle = this._mergeStyle(this.createStyle(), normalState.style);\n          this._mergeStyle(targetStyle, state.style);\n        }\n      } else {\n        targetStyle = this._mergeStyle(this.createStyle(), keepCurrentStates ? this.style : normalState.style);\n        this._mergeStyle(targetStyle, state.style);\n      }\n    } else if (needsRestoreToNormal) {\n      targetStyle = normalState.style;\n    }\n    if (targetStyle) {\n      if (transition) {\n        var sourceStyle = this.style;\n        this.style = this.createStyle(needsRestoreToNormal ? {} : sourceStyle);\n        if (needsRestoreToNormal) {\n          var changedKeys = keys(sourceStyle);\n          for (var i = 0; i < changedKeys.length; i++) {\n            var key = changedKeys[i];\n            if (key in targetStyle) {\n              targetStyle[key] = targetStyle[key];\n              this.style[key] = sourceStyle[key];\n            }\n          }\n        }\n        var targetKeys = keys(targetStyle);\n        for (var i = 0; i < targetKeys.length; i++) {\n          var key = targetKeys[i];\n          this.style[key] = this.style[key];\n        }\n        this._transitionState(stateName, {\n          style: targetStyle\n        }, animationCfg, this.getAnimationStyleProps());\n      } else {\n        this.useStyle(targetStyle);\n      }\n    }\n    var statesKeys = this.__inHover ? PRIMARY_STATES_KEYS_IN_HOVER_LAYER : PRIMARY_STATES_KEYS;\n    for (var i = 0; i < statesKeys.length; i++) {\n      var key = statesKeys[i];\n      if (state && state[key] != null) {\n        this[key] = state[key];\n      } else if (needsRestoreToNormal) {\n        if (normalState[key] != null) {\n          this[key] = normalState[key];\n        }\n      }\n    }\n  };\n  Displayable.prototype._mergeStates = function (states) {\n    var mergedState = _super.prototype._mergeStates.call(this, states);\n    var mergedStyle;\n    for (var i = 0; i < states.length; i++) {\n      var state = states[i];\n      if (state.style) {\n        mergedStyle = mergedStyle || {};\n        this._mergeStyle(mergedStyle, state.style);\n      }\n    }\n    if (mergedStyle) {\n      mergedState.style = mergedStyle;\n    }\n    return mergedState;\n  };\n  Displayable.prototype._mergeStyle = function (targetStyle, sourceStyle) {\n    extend(targetStyle, sourceStyle);\n    return targetStyle;\n  };\n  Displayable.prototype.getAnimationStyleProps = function () {\n    return DEFAULT_COMMON_ANIMATION_PROPS;\n  };\n  Displayable.initDefaultProps = function () {\n    var dispProto = Displayable.prototype;\n    dispProto.type = 'displayable';\n    dispProto.invisible = false;\n    dispProto.z = 0;\n    dispProto.z2 = 0;\n    dispProto.zlevel = 0;\n    dispProto.culling = false;\n    dispProto.cursor = 'pointer';\n    dispProto.rectHover = false;\n    dispProto.incremental = false;\n    dispProto._rect = null;\n    dispProto.dirtyRectTolerance = 0;\n    dispProto.__dirty = REDRAW_BIT | STYLE_CHANGED_BIT;\n  }();\n  return Displayable;\n}(Element);\nvar tmpRect = new BoundingRect(0, 0, 0, 0);\nvar viewRect = new BoundingRect(0, 0, 0, 0);\nfunction isDisplayableCulled(el, width, height) {\n  tmpRect.copy(el.getBoundingRect());\n  if (el.transform) {\n    tmpRect.applyTransform(el.transform);\n  }\n  viewRect.width = width;\n  viewRect.height = height;\n  return !tmpRect.intersect(viewRect);\n}\nexport default Displayable;", "import * as vec2 from './vector.js';\nimport * as curve from './curve.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathSin = Math.sin;\nvar mathCos = Math.cos;\nvar PI2 = Math.PI * 2;\nvar start = vec2.create();\nvar end = vec2.create();\nvar extremity = vec2.create();\nexport function fromPoints(points, min, max) {\n  if (points.length === 0) {\n    return;\n  }\n  var p = points[0];\n  var left = p[0];\n  var right = p[0];\n  var top = p[1];\n  var bottom = p[1];\n  for (var i = 1; i < points.length; i++) {\n    p = points[i];\n    left = mathMin(left, p[0]);\n    right = mathMax(right, p[0]);\n    top = mathMin(top, p[1]);\n    bottom = mathMax(bottom, p[1]);\n  }\n  min[0] = left;\n  min[1] = top;\n  max[0] = right;\n  max[1] = bottom;\n}\nexport function fromLine(x0, y0, x1, y1, min, max) {\n  min[0] = mathMin(x0, x1);\n  min[1] = mathMin(y0, y1);\n  max[0] = mathMax(x0, x1);\n  max[1] = mathMax(y0, y1);\n}\nvar xDim = [];\nvar yDim = [];\nexport function fromCubic(x0, y0, x1, y1, x2, y2, x3, y3, min, max) {\n  var cubicExtrema = curve.cubicExtrema;\n  var cubicAt = curve.cubicAt;\n  var n = cubicExtrema(x0, x1, x2, x3, xDim);\n  min[0] = Infinity;\n  min[1] = Infinity;\n  max[0] = -Infinity;\n  max[1] = -Infinity;\n  for (var i = 0; i < n; i++) {\n    var x = cubicAt(x0, x1, x2, x3, xDim[i]);\n    min[0] = mathMin(x, min[0]);\n    max[0] = mathMax(x, max[0]);\n  }\n  n = cubicExtrema(y0, y1, y2, y3, yDim);\n  for (var i = 0; i < n; i++) {\n    var y = cubicAt(y0, y1, y2, y3, yDim[i]);\n    min[1] = mathMin(y, min[1]);\n    max[1] = mathMax(y, max[1]);\n  }\n  min[0] = mathMin(x0, min[0]);\n  max[0] = mathMax(x0, max[0]);\n  min[0] = mathMin(x3, min[0]);\n  max[0] = mathMax(x3, max[0]);\n  min[1] = mathMin(y0, min[1]);\n  max[1] = mathMax(y0, max[1]);\n  min[1] = mathMin(y3, min[1]);\n  max[1] = mathMax(y3, max[1]);\n}\nexport function fromQuadratic(x0, y0, x1, y1, x2, y2, min, max) {\n  var quadraticExtremum = curve.quadraticExtremum;\n  var quadraticAt = curve.quadraticAt;\n  var tx = mathMax(mathMin(quadraticExtremum(x0, x1, x2), 1), 0);\n  var ty = mathMax(mathMin(quadraticExtremum(y0, y1, y2), 1), 0);\n  var x = quadraticAt(x0, x1, x2, tx);\n  var y = quadraticAt(y0, y1, y2, ty);\n  min[0] = mathMin(x0, x2, x);\n  min[1] = mathMin(y0, y2, y);\n  max[0] = mathMax(x0, x2, x);\n  max[1] = mathMax(y0, y2, y);\n}\nexport function fromArc(x, y, rx, ry, startAngle, endAngle, anticlockwise, min, max) {\n  var vec2Min = vec2.min;\n  var vec2Max = vec2.max;\n  var diff = Math.abs(startAngle - endAngle);\n  if (diff % PI2 < 1e-4 && diff > 1e-4) {\n    min[0] = x - rx;\n    min[1] = y - ry;\n    max[0] = x + rx;\n    max[1] = y + ry;\n    return;\n  }\n  start[0] = mathCos(startAngle) * rx + x;\n  start[1] = mathSin(startAngle) * ry + y;\n  end[0] = mathCos(endAngle) * rx + x;\n  end[1] = mathSin(endAngle) * ry + y;\n  vec2Min(min, start, end);\n  vec2Max(max, start, end);\n  startAngle = startAngle % PI2;\n  if (startAngle < 0) {\n    startAngle = startAngle + PI2;\n  }\n  endAngle = endAngle % PI2;\n  if (endAngle < 0) {\n    endAngle = endAngle + PI2;\n  }\n  if (startAngle > endAngle && !anticlockwise) {\n    endAngle += PI2;\n  } else if (startAngle < endAngle && anticlockwise) {\n    startAngle += PI2;\n  }\n  if (anticlockwise) {\n    var tmp = endAngle;\n    endAngle = startAngle;\n    startAngle = tmp;\n  }\n  for (var angle = 0; angle < endAngle; angle += Math.PI / 2) {\n    if (angle > startAngle) {\n      extremity[0] = mathCos(angle) * rx + x;\n      extremity[1] = mathSin(angle) * ry + y;\n      vec2Min(min, extremity, min);\n      vec2Max(max, extremity, max);\n    }\n  }\n}", "import * as vec2 from './vector.js';\nimport BoundingRect from './BoundingRect.js';\nimport { devicePixelRatio as dpr } from '../config.js';\nimport { fromLine, fromCubic, fromQuadratic, fromArc } from './bbox.js';\nimport { cubicLength, cubicSubdivide, quadraticLength, quadraticSubdivide } from './curve.js';\nvar CMD = {\n  M: 1,\n  L: 2,\n  C: 3,\n  Q: 4,\n  A: 5,\n  Z: 6,\n  R: 7\n};\nvar tmpOutX = [];\nvar tmpOutY = [];\nvar min = [];\nvar max = [];\nvar min2 = [];\nvar max2 = [];\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathCos = Math.cos;\nvar mathSin = Math.sin;\nvar mathAbs = Math.abs;\nvar PI = Math.PI;\nvar PI2 = PI * 2;\nvar hasTypedArray = typeof Float32Array !== 'undefined';\nvar tmpAngles = [];\nfunction modPI2(radian) {\n  var n = Math.round(radian / PI * 1e8) / 1e8;\n  return n % 2 * PI;\n}\nexport function normalizeArcAngles(angles, anticlockwise) {\n  var newStartAngle = modPI2(angles[0]);\n  if (newStartAngle < 0) {\n    newStartAngle += PI2;\n  }\n  var delta = newStartAngle - angles[0];\n  var newEndAngle = angles[1];\n  newEndAngle += delta;\n  if (!anticlockwise && newEndAngle - newStartAngle >= PI2) {\n    newEndAngle = newStartAngle + PI2;\n  } else if (anticlockwise && newStartAngle - newEndAngle >= PI2) {\n    newEndAngle = newStartAngle - PI2;\n  } else if (!anticlockwise && newStartAngle > newEndAngle) {\n    newEndAngle = newStartAngle + (PI2 - modPI2(newStartAngle - newEndAngle));\n  } else if (anticlockwise && newStartAngle < newEndAngle) {\n    newEndAngle = newStartAngle - (PI2 - modPI2(newEndAngle - newStartAngle));\n  }\n  angles[0] = newStartAngle;\n  angles[1] = newEndAngle;\n}\nvar PathProxy = function () {\n  function PathProxy(notSaveData) {\n    this.dpr = 1;\n    this._xi = 0;\n    this._yi = 0;\n    this._x0 = 0;\n    this._y0 = 0;\n    this._len = 0;\n    if (notSaveData) {\n      this._saveData = false;\n    }\n    if (this._saveData) {\n      this.data = [];\n    }\n  }\n  PathProxy.prototype.increaseVersion = function () {\n    this._version++;\n  };\n  PathProxy.prototype.getVersion = function () {\n    return this._version;\n  };\n  PathProxy.prototype.setScale = function (sx, sy, segmentIgnoreThreshold) {\n    segmentIgnoreThreshold = segmentIgnoreThreshold || 0;\n    if (segmentIgnoreThreshold > 0) {\n      this._ux = mathAbs(segmentIgnoreThreshold / dpr / sx) || 0;\n      this._uy = mathAbs(segmentIgnoreThreshold / dpr / sy) || 0;\n    }\n  };\n  PathProxy.prototype.setDPR = function (dpr) {\n    this.dpr = dpr;\n  };\n  PathProxy.prototype.setContext = function (ctx) {\n    this._ctx = ctx;\n  };\n  PathProxy.prototype.getContext = function () {\n    return this._ctx;\n  };\n  PathProxy.prototype.beginPath = function () {\n    this._ctx && this._ctx.beginPath();\n    this.reset();\n    return this;\n  };\n  PathProxy.prototype.reset = function () {\n    if (this._saveData) {\n      this._len = 0;\n    }\n    if (this._pathSegLen) {\n      this._pathSegLen = null;\n      this._pathLen = 0;\n    }\n    this._version++;\n  };\n  PathProxy.prototype.moveTo = function (x, y) {\n    this._drawPendingPt();\n    this.addData(CMD.M, x, y);\n    this._ctx && this._ctx.moveTo(x, y);\n    this._x0 = x;\n    this._y0 = y;\n    this._xi = x;\n    this._yi = y;\n    return this;\n  };\n  PathProxy.prototype.lineTo = function (x, y) {\n    var dx = mathAbs(x - this._xi);\n    var dy = mathAbs(y - this._yi);\n    var exceedUnit = dx > this._ux || dy > this._uy;\n    this.addData(CMD.L, x, y);\n    if (this._ctx && exceedUnit) {\n      this._ctx.lineTo(x, y);\n    }\n    if (exceedUnit) {\n      this._xi = x;\n      this._yi = y;\n      this._pendingPtDist = 0;\n    } else {\n      var d2 = dx * dx + dy * dy;\n      if (d2 > this._pendingPtDist) {\n        this._pendingPtX = x;\n        this._pendingPtY = y;\n        this._pendingPtDist = d2;\n      }\n    }\n    return this;\n  };\n  PathProxy.prototype.bezierCurveTo = function (x1, y1, x2, y2, x3, y3) {\n    this._drawPendingPt();\n    this.addData(CMD.C, x1, y1, x2, y2, x3, y3);\n    if (this._ctx) {\n      this._ctx.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n    }\n    this._xi = x3;\n    this._yi = y3;\n    return this;\n  };\n  PathProxy.prototype.quadraticCurveTo = function (x1, y1, x2, y2) {\n    this._drawPendingPt();\n    this.addData(CMD.Q, x1, y1, x2, y2);\n    if (this._ctx) {\n      this._ctx.quadraticCurveTo(x1, y1, x2, y2);\n    }\n    this._xi = x2;\n    this._yi = y2;\n    return this;\n  };\n  PathProxy.prototype.arc = function (cx, cy, r, startAngle, endAngle, anticlockwise) {\n    this._drawPendingPt();\n    tmpAngles[0] = startAngle;\n    tmpAngles[1] = endAngle;\n    normalizeArcAngles(tmpAngles, anticlockwise);\n    startAngle = tmpAngles[0];\n    endAngle = tmpAngles[1];\n    var delta = endAngle - startAngle;\n    this.addData(CMD.A, cx, cy, r, r, startAngle, delta, 0, anticlockwise ? 0 : 1);\n    this._ctx && this._ctx.arc(cx, cy, r, startAngle, endAngle, anticlockwise);\n    this._xi = mathCos(endAngle) * r + cx;\n    this._yi = mathSin(endAngle) * r + cy;\n    return this;\n  };\n  PathProxy.prototype.arcTo = function (x1, y1, x2, y2, radius) {\n    this._drawPendingPt();\n    if (this._ctx) {\n      this._ctx.arcTo(x1, y1, x2, y2, radius);\n    }\n    return this;\n  };\n  PathProxy.prototype.rect = function (x, y, w, h) {\n    this._drawPendingPt();\n    this._ctx && this._ctx.rect(x, y, w, h);\n    this.addData(CMD.R, x, y, w, h);\n    return this;\n  };\n  PathProxy.prototype.closePath = function () {\n    this._drawPendingPt();\n    this.addData(CMD.Z);\n    var ctx = this._ctx;\n    var x0 = this._x0;\n    var y0 = this._y0;\n    if (ctx) {\n      ctx.closePath();\n    }\n    this._xi = x0;\n    this._yi = y0;\n    return this;\n  };\n  PathProxy.prototype.fill = function (ctx) {\n    ctx && ctx.fill();\n    this.toStatic();\n  };\n  PathProxy.prototype.stroke = function (ctx) {\n    ctx && ctx.stroke();\n    this.toStatic();\n  };\n  PathProxy.prototype.len = function () {\n    return this._len;\n  };\n  PathProxy.prototype.setData = function (data) {\n    var len = data.length;\n    if (!(this.data && this.data.length === len) && hasTypedArray) {\n      this.data = new Float32Array(len);\n    }\n    for (var i = 0; i < len; i++) {\n      this.data[i] = data[i];\n    }\n    this._len = len;\n  };\n  PathProxy.prototype.appendPath = function (path) {\n    if (!(path instanceof Array)) {\n      path = [path];\n    }\n    var len = path.length;\n    var appendSize = 0;\n    var offset = this._len;\n    for (var i = 0; i < len; i++) {\n      appendSize += path[i].len();\n    }\n    if (hasTypedArray && this.data instanceof Float32Array) {\n      this.data = new Float32Array(offset + appendSize);\n    }\n    for (var i = 0; i < len; i++) {\n      var appendPathData = path[i].data;\n      for (var k = 0; k < appendPathData.length; k++) {\n        this.data[offset++] = appendPathData[k];\n      }\n    }\n    this._len = offset;\n  };\n  PathProxy.prototype.addData = function (cmd, a, b, c, d, e, f, g, h) {\n    if (!this._saveData) {\n      return;\n    }\n    var data = this.data;\n    if (this._len + arguments.length > data.length) {\n      this._expandData();\n      data = this.data;\n    }\n    for (var i = 0; i < arguments.length; i++) {\n      data[this._len++] = arguments[i];\n    }\n  };\n  PathProxy.prototype._drawPendingPt = function () {\n    if (this._pendingPtDist > 0) {\n      this._ctx && this._ctx.lineTo(this._pendingPtX, this._pendingPtY);\n      this._pendingPtDist = 0;\n    }\n  };\n  PathProxy.prototype._expandData = function () {\n    if (!(this.data instanceof Array)) {\n      var newData = [];\n      for (var i = 0; i < this._len; i++) {\n        newData[i] = this.data[i];\n      }\n      this.data = newData;\n    }\n  };\n  PathProxy.prototype.toStatic = function () {\n    if (!this._saveData) {\n      return;\n    }\n    this._drawPendingPt();\n    var data = this.data;\n    if (data instanceof Array) {\n      data.length = this._len;\n      if (hasTypedArray && this._len > 11) {\n        this.data = new Float32Array(data);\n      }\n    }\n  };\n  PathProxy.prototype.getBoundingRect = function () {\n    min[0] = min[1] = min2[0] = min2[1] = Number.MAX_VALUE;\n    max[0] = max[1] = max2[0] = max2[1] = -Number.MAX_VALUE;\n    var data = this.data;\n    var xi = 0;\n    var yi = 0;\n    var x0 = 0;\n    var y0 = 0;\n    var i;\n    for (i = 0; i < this._len;) {\n      var cmd = data[i++];\n      var isFirst = i === 1;\n      if (isFirst) {\n        xi = data[i];\n        yi = data[i + 1];\n        x0 = xi;\n        y0 = yi;\n      }\n      switch (cmd) {\n        case CMD.M:\n          xi = x0 = data[i++];\n          yi = y0 = data[i++];\n          min2[0] = x0;\n          min2[1] = y0;\n          max2[0] = x0;\n          max2[1] = y0;\n          break;\n        case CMD.L:\n          fromLine(xi, yi, data[i], data[i + 1], min2, max2);\n          xi = data[i++];\n          yi = data[i++];\n          break;\n        case CMD.C:\n          fromCubic(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], min2, max2);\n          xi = data[i++];\n          yi = data[i++];\n          break;\n        case CMD.Q:\n          fromQuadratic(xi, yi, data[i++], data[i++], data[i], data[i + 1], min2, max2);\n          xi = data[i++];\n          yi = data[i++];\n          break;\n        case CMD.A:\n          var cx = data[i++];\n          var cy = data[i++];\n          var rx = data[i++];\n          var ry = data[i++];\n          var startAngle = data[i++];\n          var endAngle = data[i++] + startAngle;\n          i += 1;\n          var anticlockwise = !data[i++];\n          if (isFirst) {\n            x0 = mathCos(startAngle) * rx + cx;\n            y0 = mathSin(startAngle) * ry + cy;\n          }\n          fromArc(cx, cy, rx, ry, startAngle, endAngle, anticlockwise, min2, max2);\n          xi = mathCos(endAngle) * rx + cx;\n          yi = mathSin(endAngle) * ry + cy;\n          break;\n        case CMD.R:\n          x0 = xi = data[i++];\n          y0 = yi = data[i++];\n          var width = data[i++];\n          var height = data[i++];\n          fromLine(x0, y0, x0 + width, y0 + height, min2, max2);\n          break;\n        case CMD.Z:\n          xi = x0;\n          yi = y0;\n          break;\n      }\n      vec2.min(min, min, min2);\n      vec2.max(max, max, max2);\n    }\n    if (i === 0) {\n      min[0] = min[1] = max[0] = max[1] = 0;\n    }\n    return new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n  };\n  PathProxy.prototype._calculateLength = function () {\n    var data = this.data;\n    var len = this._len;\n    var ux = this._ux;\n    var uy = this._uy;\n    var xi = 0;\n    var yi = 0;\n    var x0 = 0;\n    var y0 = 0;\n    if (!this._pathSegLen) {\n      this._pathSegLen = [];\n    }\n    var pathSegLen = this._pathSegLen;\n    var pathTotalLen = 0;\n    var segCount = 0;\n    for (var i = 0; i < len;) {\n      var cmd = data[i++];\n      var isFirst = i === 1;\n      if (isFirst) {\n        xi = data[i];\n        yi = data[i + 1];\n        x0 = xi;\n        y0 = yi;\n      }\n      var l = -1;\n      switch (cmd) {\n        case CMD.M:\n          xi = x0 = data[i++];\n          yi = y0 = data[i++];\n          break;\n        case CMD.L:\n          {\n            var x2 = data[i++];\n            var y2 = data[i++];\n            var dx = x2 - xi;\n            var dy = y2 - yi;\n            if (mathAbs(dx) > ux || mathAbs(dy) > uy || i === len - 1) {\n              l = Math.sqrt(dx * dx + dy * dy);\n              xi = x2;\n              yi = y2;\n            }\n            break;\n          }\n        case CMD.C:\n          {\n            var x1 = data[i++];\n            var y1 = data[i++];\n            var x2 = data[i++];\n            var y2 = data[i++];\n            var x3 = data[i++];\n            var y3 = data[i++];\n            l = cubicLength(xi, yi, x1, y1, x2, y2, x3, y3, 10);\n            xi = x3;\n            yi = y3;\n            break;\n          }\n        case CMD.Q:\n          {\n            var x1 = data[i++];\n            var y1 = data[i++];\n            var x2 = data[i++];\n            var y2 = data[i++];\n            l = quadraticLength(xi, yi, x1, y1, x2, y2, 10);\n            xi = x2;\n            yi = y2;\n            break;\n          }\n        case CMD.A:\n          var cx = data[i++];\n          var cy = data[i++];\n          var rx = data[i++];\n          var ry = data[i++];\n          var startAngle = data[i++];\n          var delta = data[i++];\n          var endAngle = delta + startAngle;\n          i += 1;\n          var anticlockwise = !data[i++];\n          if (isFirst) {\n            x0 = mathCos(startAngle) * rx + cx;\n            y0 = mathSin(startAngle) * ry + cy;\n          }\n          l = mathMax(rx, ry) * mathMin(PI2, Math.abs(delta));\n          xi = mathCos(endAngle) * rx + cx;\n          yi = mathSin(endAngle) * ry + cy;\n          break;\n        case CMD.R:\n          {\n            x0 = xi = data[i++];\n            y0 = yi = data[i++];\n            var width = data[i++];\n            var height = data[i++];\n            l = width * 2 + height * 2;\n            break;\n          }\n        case CMD.Z:\n          {\n            var dx = x0 - xi;\n            var dy = y0 - yi;\n            l = Math.sqrt(dx * dx + dy * dy);\n            xi = x0;\n            yi = y0;\n            break;\n          }\n      }\n      if (l >= 0) {\n        pathSegLen[segCount++] = l;\n        pathTotalLen += l;\n      }\n    }\n    this._pathLen = pathTotalLen;\n    return pathTotalLen;\n  };\n  PathProxy.prototype.rebuildPath = function (ctx, percent) {\n    var d = this.data;\n    var ux = this._ux;\n    var uy = this._uy;\n    var len = this._len;\n    var x0;\n    var y0;\n    var xi;\n    var yi;\n    var x;\n    var y;\n    var drawPart = percent < 1;\n    var pathSegLen;\n    var pathTotalLen;\n    var accumLength = 0;\n    var segCount = 0;\n    var displayedLength;\n    var pendingPtDist = 0;\n    var pendingPtX;\n    var pendingPtY;\n    if (drawPart) {\n      if (!this._pathSegLen) {\n        this._calculateLength();\n      }\n      pathSegLen = this._pathSegLen;\n      pathTotalLen = this._pathLen;\n      displayedLength = percent * pathTotalLen;\n      if (!displayedLength) {\n        return;\n      }\n    }\n    lo: for (var i = 0; i < len;) {\n      var cmd = d[i++];\n      var isFirst = i === 1;\n      if (isFirst) {\n        xi = d[i];\n        yi = d[i + 1];\n        x0 = xi;\n        y0 = yi;\n      }\n      if (cmd !== CMD.L && pendingPtDist > 0) {\n        ctx.lineTo(pendingPtX, pendingPtY);\n        pendingPtDist = 0;\n      }\n      switch (cmd) {\n        case CMD.M:\n          x0 = xi = d[i++];\n          y0 = yi = d[i++];\n          ctx.moveTo(xi, yi);\n          break;\n        case CMD.L:\n          {\n            x = d[i++];\n            y = d[i++];\n            var dx = mathAbs(x - xi);\n            var dy = mathAbs(y - yi);\n            if (dx > ux || dy > uy) {\n              if (drawPart) {\n                var l = pathSegLen[segCount++];\n                if (accumLength + l > displayedLength) {\n                  var t = (displayedLength - accumLength) / l;\n                  ctx.lineTo(xi * (1 - t) + x * t, yi * (1 - t) + y * t);\n                  break lo;\n                }\n                accumLength += l;\n              }\n              ctx.lineTo(x, y);\n              xi = x;\n              yi = y;\n              pendingPtDist = 0;\n            } else {\n              var d2 = dx * dx + dy * dy;\n              if (d2 > pendingPtDist) {\n                pendingPtX = x;\n                pendingPtY = y;\n                pendingPtDist = d2;\n              }\n            }\n            break;\n          }\n        case CMD.C:\n          {\n            var x1 = d[i++];\n            var y1 = d[i++];\n            var x2 = d[i++];\n            var y2 = d[i++];\n            var x3 = d[i++];\n            var y3 = d[i++];\n            if (drawPart) {\n              var l = pathSegLen[segCount++];\n              if (accumLength + l > displayedLength) {\n                var t = (displayedLength - accumLength) / l;\n                cubicSubdivide(xi, x1, x2, x3, t, tmpOutX);\n                cubicSubdivide(yi, y1, y2, y3, t, tmpOutY);\n                ctx.bezierCurveTo(tmpOutX[1], tmpOutY[1], tmpOutX[2], tmpOutY[2], tmpOutX[3], tmpOutY[3]);\n                break lo;\n              }\n              accumLength += l;\n            }\n            ctx.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n            xi = x3;\n            yi = y3;\n            break;\n          }\n        case CMD.Q:\n          {\n            var x1 = d[i++];\n            var y1 = d[i++];\n            var x2 = d[i++];\n            var y2 = d[i++];\n            if (drawPart) {\n              var l = pathSegLen[segCount++];\n              if (accumLength + l > displayedLength) {\n                var t = (displayedLength - accumLength) / l;\n                quadraticSubdivide(xi, x1, x2, t, tmpOutX);\n                quadraticSubdivide(yi, y1, y2, t, tmpOutY);\n                ctx.quadraticCurveTo(tmpOutX[1], tmpOutY[1], tmpOutX[2], tmpOutY[2]);\n                break lo;\n              }\n              accumLength += l;\n            }\n            ctx.quadraticCurveTo(x1, y1, x2, y2);\n            xi = x2;\n            yi = y2;\n            break;\n          }\n        case CMD.A:\n          var cx = d[i++];\n          var cy = d[i++];\n          var rx = d[i++];\n          var ry = d[i++];\n          var startAngle = d[i++];\n          var delta = d[i++];\n          var psi = d[i++];\n          var anticlockwise = !d[i++];\n          var r = rx > ry ? rx : ry;\n          var isEllipse = mathAbs(rx - ry) > 1e-3;\n          var endAngle = startAngle + delta;\n          var breakBuild = false;\n          if (drawPart) {\n            var l = pathSegLen[segCount++];\n            if (accumLength + l > displayedLength) {\n              endAngle = startAngle + delta * (displayedLength - accumLength) / l;\n              breakBuild = true;\n            }\n            accumLength += l;\n          }\n          if (isEllipse && ctx.ellipse) {\n            ctx.ellipse(cx, cy, rx, ry, psi, startAngle, endAngle, anticlockwise);\n          } else {\n            ctx.arc(cx, cy, r, startAngle, endAngle, anticlockwise);\n          }\n          if (breakBuild) {\n            break lo;\n          }\n          if (isFirst) {\n            x0 = mathCos(startAngle) * rx + cx;\n            y0 = mathSin(startAngle) * ry + cy;\n          }\n          xi = mathCos(endAngle) * rx + cx;\n          yi = mathSin(endAngle) * ry + cy;\n          break;\n        case CMD.R:\n          x0 = xi = d[i];\n          y0 = yi = d[i + 1];\n          x = d[i++];\n          y = d[i++];\n          var width = d[i++];\n          var height = d[i++];\n          if (drawPart) {\n            var l = pathSegLen[segCount++];\n            if (accumLength + l > displayedLength) {\n              var d_1 = displayedLength - accumLength;\n              ctx.moveTo(x, y);\n              ctx.lineTo(x + mathMin(d_1, width), y);\n              d_1 -= width;\n              if (d_1 > 0) {\n                ctx.lineTo(x + width, y + mathMin(d_1, height));\n              }\n              d_1 -= height;\n              if (d_1 > 0) {\n                ctx.lineTo(x + mathMax(width - d_1, 0), y + height);\n              }\n              d_1 -= width;\n              if (d_1 > 0) {\n                ctx.lineTo(x, y + mathMax(height - d_1, 0));\n              }\n              break lo;\n            }\n            accumLength += l;\n          }\n          ctx.rect(x, y, width, height);\n          break;\n        case CMD.Z:\n          if (drawPart) {\n            var l = pathSegLen[segCount++];\n            if (accumLength + l > displayedLength) {\n              var t = (displayedLength - accumLength) / l;\n              ctx.lineTo(xi * (1 - t) + x0 * t, yi * (1 - t) + y0 * t);\n              break lo;\n            }\n            accumLength += l;\n          }\n          ctx.closePath();\n          xi = x0;\n          yi = y0;\n      }\n    }\n  };\n  PathProxy.prototype.clone = function () {\n    var newProxy = new PathProxy();\n    var data = this.data;\n    newProxy.data = data.slice ? data.slice() : Array.prototype.slice.call(data);\n    newProxy._len = this._len;\n    return newProxy;\n  };\n  PathProxy.CMD = CMD;\n  PathProxy.initDefaultProps = function () {\n    var proto = PathProxy.prototype;\n    proto._saveData = true;\n    proto._ux = 0;\n    proto._uy = 0;\n    proto._pendingPtDist = 0;\n    proto._version = 0;\n  }();\n  return PathProxy;\n}();\nexport default PathProxy;", "import LRU from '../../core/LRU.js';\nimport { platformApi } from '../../core/platform.js';\nvar globalImageCache = new LRU(50);\nexport function findExistImage(newImageOrSrc) {\n  if (typeof newImageOrSrc === 'string') {\n    var cachedImgObj = globalImageCache.get(newImageOrSrc);\n    return cachedImgObj && cachedImgObj.image;\n  } else {\n    return newImageOrSrc;\n  }\n}\nexport function createOrUpdateImage(newImageOrSrc, image, hostEl, onload, cbPayload) {\n  if (!newImageOrSrc) {\n    return image;\n  } else if (typeof newImageOrSrc === 'string') {\n    if (image && image.__zrImageSrc === newImageOrSrc || !hostEl) {\n      return image;\n    }\n    var cachedImgObj = globalImageCache.get(newImageOrSrc);\n    var pendingWrap = {\n      hostEl: hostEl,\n      cb: onload,\n      cbPayload: cbPayload\n    };\n    if (cachedImgObj) {\n      image = cachedImgObj.image;\n      !isImageReady(image) && cachedImgObj.pending.push(pendingWrap);\n    } else {\n      image = platformApi.loadImage(newImageOrSrc, imageOnLoad, imageOnLoad);\n      image.__zrImageSrc = newImageOrSrc;\n      globalImageCache.put(newImageOrSrc, image.__cachedImgObj = {\n        image: image,\n        pending: [pendingWrap]\n      });\n    }\n    return image;\n  } else {\n    return newImageOrSrc;\n  }\n}\nfunction imageOnLoad() {\n  var cachedImgObj = this.__cachedImgObj;\n  this.onload = this.onerror = this.__cachedImgObj = null;\n  for (var i = 0; i < cachedImgObj.pending.length; i++) {\n    var pendingWrap = cachedImgObj.pending[i];\n    var cb = pendingWrap.cb;\n    cb && cb(this, pendingWrap.cbPayload);\n    pendingWrap.hostEl.dirty();\n  }\n  cachedImgObj.pending.length = 0;\n}\nexport function isImageReady(image) {\n  return image && image.width && image.height;\n}", "function isSafeNum(num) {\n  return isFinite(num);\n}\nexport function createLinearGradient(ctx, obj, rect) {\n  var x = obj.x == null ? 0 : obj.x;\n  var x2 = obj.x2 == null ? 1 : obj.x2;\n  var y = obj.y == null ? 0 : obj.y;\n  var y2 = obj.y2 == null ? 0 : obj.y2;\n  if (!obj.global) {\n    x = x * rect.width + rect.x;\n    x2 = x2 * rect.width + rect.x;\n    y = y * rect.height + rect.y;\n    y2 = y2 * rect.height + rect.y;\n  }\n  x = isSafeNum(x) ? x : 0;\n  x2 = isSafeNum(x2) ? x2 : 1;\n  y = isSafeNum(y) ? y : 0;\n  y2 = isSafeNum(y2) ? y2 : 0;\n  var canvasGradient = ctx.createLinearGradient(x, y, x2, y2);\n  return canvasGradient;\n}\nexport function createRadialGradient(ctx, obj, rect) {\n  var width = rect.width;\n  var height = rect.height;\n  var min = Math.min(width, height);\n  var x = obj.x == null ? 0.5 : obj.x;\n  var y = obj.y == null ? 0.5 : obj.y;\n  var r = obj.r == null ? 0.5 : obj.r;\n  if (!obj.global) {\n    x = x * width + rect.x;\n    y = y * height + rect.y;\n    r = r * min;\n  }\n  x = isSafeNum(x) ? x : 0.5;\n  y = isSafeNum(y) ? y : 0.5;\n  r = r >= 0 && isSafeNum(r) ? r : 0.5;\n  var canvasGradient = ctx.createRadialGradient(x, y, 0, x, y, r);\n  return canvasGradient;\n}\nexport function getCanvasGradient(ctx, obj, rect) {\n  var canvasGradient = obj.type === 'radial' ? createRadialGradient(ctx, obj, rect) : createLinearGradient(ctx, obj, rect);\n  var colorStops = obj.colorStops;\n  for (var i = 0; i < colorStops.length; i++) {\n    canvasGradient.addColorStop(colorStops[i].offset, colorStops[i].color);\n  }\n  return canvasGradient;\n}\nexport function isClipPathChanged(clipPaths, prevClipPaths) {\n  if (clipPaths === prevClipPaths || !clipPaths && !prevClipPaths) {\n    return false;\n  }\n  if (!clipPaths || !prevClipPaths || clipPaths.length !== prevClipPaths.length) {\n    return true;\n  }\n  for (var i = 0; i < clipPaths.length; i++) {\n    if (clipPaths[i] !== prevClipPaths[i]) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction parseInt10(val) {\n  return parseInt(val, 10);\n}\nexport function getSize(root, whIdx, opts) {\n  var wh = ['width', 'height'][whIdx];\n  var cwh = ['clientWidth', 'clientHeight'][whIdx];\n  var plt = ['paddingLeft', 'paddingTop'][whIdx];\n  var prb = ['paddingRight', 'paddingBottom'][whIdx];\n  if (opts[wh] != null && opts[wh] !== 'auto') {\n    return parseFloat(opts[wh]);\n  }\n  var stl = document.defaultView.getComputedStyle(root);\n  return (root[cwh] || parseInt10(stl[wh]) || parseInt10(root.style[wh])) - (parseInt10(stl[plt]) || 0) - (parseInt10(stl[prb]) || 0) | 0;\n}", "export function containStroke(x0, y0, x1, y1, lineWidth, x, y) {\n  if (lineWidth === 0) {\n    return false;\n  }\n  var _l = lineWidth;\n  var _a = 0;\n  var _b = x0;\n  if (y > y0 + _l && y > y1 + _l || y < y0 - _l && y < y1 - _l || x > x0 + _l && x > x1 + _l || x < x0 - _l && x < x1 - _l) {\n    return false;\n  }\n  if (x0 !== x1) {\n    _a = (y0 - y1) / (x0 - x1);\n    _b = (x0 * y1 - x1 * y0) / (x0 - x1);\n  } else {\n    return Math.abs(x - x0) <= _l / 2;\n  }\n  var tmp = _a * x - y + _b;\n  var _s = tmp * tmp / (_a * _a + 1);\n  return _s <= _l / 2 * _l / 2;\n}", "import * as curve from '../core/curve.js';\nexport function containStroke(x0, y0, x1, y1, x2, y2, x3, y3, lineWidth, x, y) {\n  if (lineWidth === 0) {\n    return false;\n  }\n  var _l = lineWidth;\n  if (y > y0 + _l && y > y1 + _l && y > y2 + _l && y > y3 + _l || y < y0 - _l && y < y1 - _l && y < y2 - _l && y < y3 - _l || x > x0 + _l && x > x1 + _l && x > x2 + _l && x > x3 + _l || x < x0 - _l && x < x1 - _l && x < x2 - _l && x < x3 - _l) {\n    return false;\n  }\n  var d = curve.cubicProjectPoint(x0, y0, x1, y1, x2, y2, x3, y3, x, y, null);\n  return d <= _l / 2;\n}", "import { quadraticProjectPoint } from '../core/curve.js';\nexport function containStroke(x0, y0, x1, y1, x2, y2, lineWidth, x, y) {\n  if (lineWidth === 0) {\n    return false;\n  }\n  var _l = lineWidth;\n  if (y > y0 + _l && y > y1 + _l && y > y2 + _l || y < y0 - _l && y < y1 - _l && y < y2 - _l || x > x0 + _l && x > x1 + _l && x > x2 + _l || x < x0 - _l && x < x1 - _l && x < x2 - _l) {\n    return false;\n  }\n  var d = quadraticProjectPoint(x0, y0, x1, y1, x2, y2, x, y, null);\n  return d <= _l / 2;\n}", "var PI2 = Math.PI * 2;\nexport function normalizeRadian(angle) {\n  angle %= PI2;\n  if (angle < 0) {\n    angle += PI2;\n  }\n  return angle;\n}", "import { normalizeRadian } from './util.js';\nvar PI2 = Math.PI * 2;\nexport function containStroke(cx, cy, r, startAngle, endAngle, anticlockwise, lineWidth, x, y) {\n  if (lineWidth === 0) {\n    return false;\n  }\n  var _l = lineWidth;\n  x -= cx;\n  y -= cy;\n  var d = Math.sqrt(x * x + y * y);\n  if (d - _l > r || d + _l < r) {\n    return false;\n  }\n  if (Math.abs(startAngle - endAngle) % PI2 < 1e-4) {\n    return true;\n  }\n  if (anticlockwise) {\n    var tmp = startAngle;\n    startAngle = normalizeRadian(endAngle);\n    endAngle = normalizeRadian(tmp);\n  } else {\n    startAngle = normalizeRadian(startAngle);\n    endAngle = normalizeRadian(endAngle);\n  }\n  if (startAngle > endAngle) {\n    endAngle += PI2;\n  }\n  var angle = Math.atan2(y, x);\n  if (angle < 0) {\n    angle += PI2;\n  }\n  return angle >= startAngle && angle <= endAngle || angle + PI2 >= startAngle && angle + PI2 <= endAngle;\n}", "export default function windingLine(x0, y0, x1, y1, x, y) {\n  if (y > y0 && y > y1 || y < y0 && y < y1) {\n    return 0;\n  }\n  if (y1 === y0) {\n    return 0;\n  }\n  var t = (y - y0) / (y1 - y0);\n  var dir = y1 < y0 ? 1 : -1;\n  if (t === 1 || t === 0) {\n    dir = y1 < y0 ? 0.5 : -0.5;\n  }\n  var x_ = t * (x1 - x0) + x0;\n  return x_ === x ? Infinity : x_ > x ? dir : 0;\n}", "import PathProxy from '../core/PathProxy.js';\nimport * as line from './line.js';\nimport * as cubic from './cubic.js';\nimport * as quadratic from './quadratic.js';\nimport * as arc from './arc.js';\nimport * as curve from '../core/curve.js';\nimport windingLine from './windingLine.js';\nvar CMD = PathProxy.CMD;\nvar PI2 = Math.PI * 2;\nvar EPSILON = 1e-4;\nfunction isAroundEqual(a, b) {\n  return Math.abs(a - b) < EPSILON;\n}\nvar roots = [-1, -1, -1];\nvar extrema = [-1, -1];\nfunction swapExtrema() {\n  var tmp = extrema[0];\n  extrema[0] = extrema[1];\n  extrema[1] = tmp;\n}\nfunction windingCubic(x0, y0, x1, y1, x2, y2, x3, y3, x, y) {\n  if (y > y0 && y > y1 && y > y2 && y > y3 || y < y0 && y < y1 && y < y2 && y < y3) {\n    return 0;\n  }\n  var nRoots = curve.cubicRootAt(y0, y1, y2, y3, y, roots);\n  if (nRoots === 0) {\n    return 0;\n  } else {\n    var w = 0;\n    var nExtrema = -1;\n    var y0_ = void 0;\n    var y1_ = void 0;\n    for (var i = 0; i < nRoots; i++) {\n      var t = roots[i];\n      var unit = t === 0 || t === 1 ? 0.5 : 1;\n      var x_ = curve.cubicAt(x0, x1, x2, x3, t);\n      if (x_ < x) {\n        continue;\n      }\n      if (nExtrema < 0) {\n        nExtrema = curve.cubicExtrema(y0, y1, y2, y3, extrema);\n        if (extrema[1] < extrema[0] && nExtrema > 1) {\n          swapExtrema();\n        }\n        y0_ = curve.cubicAt(y0, y1, y2, y3, extrema[0]);\n        if (nExtrema > 1) {\n          y1_ = curve.cubicAt(y0, y1, y2, y3, extrema[1]);\n        }\n      }\n      if (nExtrema === 2) {\n        if (t < extrema[0]) {\n          w += y0_ < y0 ? unit : -unit;\n        } else if (t < extrema[1]) {\n          w += y1_ < y0_ ? unit : -unit;\n        } else {\n          w += y3 < y1_ ? unit : -unit;\n        }\n      } else {\n        if (t < extrema[0]) {\n          w += y0_ < y0 ? unit : -unit;\n        } else {\n          w += y3 < y0_ ? unit : -unit;\n        }\n      }\n    }\n    return w;\n  }\n}\nfunction windingQuadratic(x0, y0, x1, y1, x2, y2, x, y) {\n  if (y > y0 && y > y1 && y > y2 || y < y0 && y < y1 && y < y2) {\n    return 0;\n  }\n  var nRoots = curve.quadraticRootAt(y0, y1, y2, y, roots);\n  if (nRoots === 0) {\n    return 0;\n  } else {\n    var t = curve.quadraticExtremum(y0, y1, y2);\n    if (t >= 0 && t <= 1) {\n      var w = 0;\n      var y_ = curve.quadraticAt(y0, y1, y2, t);\n      for (var i = 0; i < nRoots; i++) {\n        var unit = roots[i] === 0 || roots[i] === 1 ? 0.5 : 1;\n        var x_ = curve.quadraticAt(x0, x1, x2, roots[i]);\n        if (x_ < x) {\n          continue;\n        }\n        if (roots[i] < t) {\n          w += y_ < y0 ? unit : -unit;\n        } else {\n          w += y2 < y_ ? unit : -unit;\n        }\n      }\n      return w;\n    } else {\n      var unit = roots[0] === 0 || roots[0] === 1 ? 0.5 : 1;\n      var x_ = curve.quadraticAt(x0, x1, x2, roots[0]);\n      if (x_ < x) {\n        return 0;\n      }\n      return y2 < y0 ? unit : -unit;\n    }\n  }\n}\nfunction windingArc(cx, cy, r, startAngle, endAngle, anticlockwise, x, y) {\n  y -= cy;\n  if (y > r || y < -r) {\n    return 0;\n  }\n  var tmp = Math.sqrt(r * r - y * y);\n  roots[0] = -tmp;\n  roots[1] = tmp;\n  var dTheta = Math.abs(startAngle - endAngle);\n  if (dTheta < 1e-4) {\n    return 0;\n  }\n  if (dTheta >= PI2 - 1e-4) {\n    startAngle = 0;\n    endAngle = PI2;\n    var dir = anticlockwise ? 1 : -1;\n    if (x >= roots[0] + cx && x <= roots[1] + cx) {\n      return dir;\n    } else {\n      return 0;\n    }\n  }\n  if (startAngle > endAngle) {\n    var tmp_1 = startAngle;\n    startAngle = endAngle;\n    endAngle = tmp_1;\n  }\n  if (startAngle < 0) {\n    startAngle += PI2;\n    endAngle += PI2;\n  }\n  var w = 0;\n  for (var i = 0; i < 2; i++) {\n    var x_ = roots[i];\n    if (x_ + cx > x) {\n      var angle = Math.atan2(y, x_);\n      var dir = anticlockwise ? 1 : -1;\n      if (angle < 0) {\n        angle = PI2 + angle;\n      }\n      if (angle >= startAngle && angle <= endAngle || angle + PI2 >= startAngle && angle + PI2 <= endAngle) {\n        if (angle > Math.PI / 2 && angle < Math.PI * 1.5) {\n          dir = -dir;\n        }\n        w += dir;\n      }\n    }\n  }\n  return w;\n}\nfunction containPath(path, lineWidth, isStroke, x, y) {\n  var data = path.data;\n  var len = path.len();\n  var w = 0;\n  var xi = 0;\n  var yi = 0;\n  var x0 = 0;\n  var y0 = 0;\n  var x1;\n  var y1;\n  for (var i = 0; i < len;) {\n    var cmd = data[i++];\n    var isFirst = i === 1;\n    if (cmd === CMD.M && i > 1) {\n      if (!isStroke) {\n        w += windingLine(xi, yi, x0, y0, x, y);\n      }\n    }\n    if (isFirst) {\n      xi = data[i];\n      yi = data[i + 1];\n      x0 = xi;\n      y0 = yi;\n    }\n    switch (cmd) {\n      case CMD.M:\n        x0 = data[i++];\n        y0 = data[i++];\n        xi = x0;\n        yi = y0;\n        break;\n      case CMD.L:\n        if (isStroke) {\n          if (line.containStroke(xi, yi, data[i], data[i + 1], lineWidth, x, y)) {\n            return true;\n          }\n        } else {\n          w += windingLine(xi, yi, data[i], data[i + 1], x, y) || 0;\n        }\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.C:\n        if (isStroke) {\n          if (cubic.containStroke(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], lineWidth, x, y)) {\n            return true;\n          }\n        } else {\n          w += windingCubic(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], x, y) || 0;\n        }\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.Q:\n        if (isStroke) {\n          if (quadratic.containStroke(xi, yi, data[i++], data[i++], data[i], data[i + 1], lineWidth, x, y)) {\n            return true;\n          }\n        } else {\n          w += windingQuadratic(xi, yi, data[i++], data[i++], data[i], data[i + 1], x, y) || 0;\n        }\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.A:\n        var cx = data[i++];\n        var cy = data[i++];\n        var rx = data[i++];\n        var ry = data[i++];\n        var theta = data[i++];\n        var dTheta = data[i++];\n        i += 1;\n        var anticlockwise = !!(1 - data[i++]);\n        x1 = Math.cos(theta) * rx + cx;\n        y1 = Math.sin(theta) * ry + cy;\n        if (!isFirst) {\n          w += windingLine(xi, yi, x1, y1, x, y);\n        } else {\n          x0 = x1;\n          y0 = y1;\n        }\n        var _x = (x - cx) * ry / rx + cx;\n        if (isStroke) {\n          if (arc.containStroke(cx, cy, ry, theta, theta + dTheta, anticlockwise, lineWidth, _x, y)) {\n            return true;\n          }\n        } else {\n          w += windingArc(cx, cy, ry, theta, theta + dTheta, anticlockwise, _x, y);\n        }\n        xi = Math.cos(theta + dTheta) * rx + cx;\n        yi = Math.sin(theta + dTheta) * ry + cy;\n        break;\n      case CMD.R:\n        x0 = xi = data[i++];\n        y0 = yi = data[i++];\n        var width = data[i++];\n        var height = data[i++];\n        x1 = x0 + width;\n        y1 = y0 + height;\n        if (isStroke) {\n          if (line.containStroke(x0, y0, x1, y0, lineWidth, x, y) || line.containStroke(x1, y0, x1, y1, lineWidth, x, y) || line.containStroke(x1, y1, x0, y1, lineWidth, x, y) || line.containStroke(x0, y1, x0, y0, lineWidth, x, y)) {\n            return true;\n          }\n        } else {\n          w += windingLine(x1, y0, x1, y1, x, y);\n          w += windingLine(x0, y1, x0, y0, x, y);\n        }\n        break;\n      case CMD.Z:\n        if (isStroke) {\n          if (line.containStroke(xi, yi, x0, y0, lineWidth, x, y)) {\n            return true;\n          }\n        } else {\n          w += windingLine(xi, yi, x0, y0, x, y);\n        }\n        xi = x0;\n        yi = y0;\n        break;\n    }\n  }\n  if (!isStroke && !isAroundEqual(yi, y0)) {\n    w += windingLine(xi, yi, x0, y0, x, y) || 0;\n  }\n  return w !== 0;\n}\nexport function contain(pathProxy, x, y) {\n  return containPath(pathProxy, 0, false, x, y);\n}\nexport function containStroke(pathProxy, lineWidth, x, y) {\n  return containPath(pathProxy, lineWidth, true, x, y);\n}", "import { __extends } from \"tslib\";\nimport Displayable, { DEFAULT_COMMON_STYLE, DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport PathProxy from '../core/PathProxy.js';\nimport * as pathContain from '../contain/path.js';\nimport { defaults, keys, extend, clone, isString, createObject } from '../core/util.js';\nimport { lum } from '../tool/color.js';\nimport { DARK_LABEL_COLOR, LIGHT_LABEL_COLOR, DARK_MODE_THRESHOLD, LIGHTER_LABEL_COLOR } from '../config.js';\nimport { REDRAW_BIT, SHAPE_CHANGED_BIT, STYLE_CHANGED_BIT } from './constants.js';\nimport { TRANSFORMABLE_PROPS } from '../core/Transformable.js';\nexport var DEFAULT_PATH_STYLE = defaults({\n  fill: '#000',\n  stroke: null,\n  strokePercent: 1,\n  fillOpacity: 1,\n  strokeOpacity: 1,\n  lineDashOffset: 0,\n  lineWidth: 1,\n  lineCap: 'butt',\n  miterLimit: 10,\n  strokeNoScale: false,\n  strokeFirst: false\n}, DEFAULT_COMMON_STYLE);\nexport var DEFAULT_PATH_ANIMATION_PROPS = {\n  style: defaults({\n    fill: true,\n    stroke: true,\n    strokePercent: true,\n    fillOpacity: true,\n    strokeOpacity: true,\n    lineDashOffset: true,\n    lineWidth: true,\n    miterLimit: true\n  }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nvar pathCopyParams = TRANSFORMABLE_PROPS.concat(['invisible', 'culling', 'z', 'z2', 'zlevel', 'parent']);\nvar Path = function (_super) {\n  __extends(Path, _super);\n  function Path(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Path.prototype.update = function () {\n    var _this = this;\n    _super.prototype.update.call(this);\n    var style = this.style;\n    if (style.decal) {\n      var decalEl = this._decalEl = this._decalEl || new Path();\n      if (decalEl.buildPath === Path.prototype.buildPath) {\n        decalEl.buildPath = function (ctx) {\n          _this.buildPath(ctx, _this.shape);\n        };\n      }\n      decalEl.silent = true;\n      var decalElStyle = decalEl.style;\n      for (var key in style) {\n        if (decalElStyle[key] !== style[key]) {\n          decalElStyle[key] = style[key];\n        }\n      }\n      decalElStyle.fill = style.fill ? style.decal : null;\n      decalElStyle.decal = null;\n      decalElStyle.shadowColor = null;\n      style.strokeFirst && (decalElStyle.stroke = null);\n      for (var i = 0; i < pathCopyParams.length; ++i) {\n        decalEl[pathCopyParams[i]] = this[pathCopyParams[i]];\n      }\n      decalEl.__dirty |= REDRAW_BIT;\n    } else if (this._decalEl) {\n      this._decalEl = null;\n    }\n  };\n  Path.prototype.getDecalElement = function () {\n    return this._decalEl;\n  };\n  Path.prototype._init = function (props) {\n    var keysArr = keys(props);\n    this.shape = this.getDefaultShape();\n    var defaultStyle = this.getDefaultStyle();\n    if (defaultStyle) {\n      this.useStyle(defaultStyle);\n    }\n    for (var i = 0; i < keysArr.length; i++) {\n      var key = keysArr[i];\n      var value = props[key];\n      if (key === 'style') {\n        if (!this.style) {\n          this.useStyle(value);\n        } else {\n          extend(this.style, value);\n        }\n      } else if (key === 'shape') {\n        extend(this.shape, value);\n      } else {\n        _super.prototype.attrKV.call(this, key, value);\n      }\n    }\n    if (!this.style) {\n      this.useStyle({});\n    }\n  };\n  Path.prototype.getDefaultStyle = function () {\n    return null;\n  };\n  Path.prototype.getDefaultShape = function () {\n    return {};\n  };\n  Path.prototype.canBeInsideText = function () {\n    return this.hasFill();\n  };\n  Path.prototype.getInsideTextFill = function () {\n    var pathFill = this.style.fill;\n    if (pathFill !== 'none') {\n      if (isString(pathFill)) {\n        var fillLum = lum(pathFill, 0);\n        if (fillLum > 0.5) {\n          return DARK_LABEL_COLOR;\n        } else if (fillLum > 0.2) {\n          return LIGHTER_LABEL_COLOR;\n        }\n        return LIGHT_LABEL_COLOR;\n      } else if (pathFill) {\n        return LIGHT_LABEL_COLOR;\n      }\n    }\n    return DARK_LABEL_COLOR;\n  };\n  Path.prototype.getInsideTextStroke = function (textFill) {\n    var pathFill = this.style.fill;\n    if (isString(pathFill)) {\n      var zr = this.__zr;\n      var isDarkMode = !!(zr && zr.isDarkMode());\n      var isDarkLabel = lum(textFill, 0) < DARK_MODE_THRESHOLD;\n      if (isDarkMode === isDarkLabel) {\n        return pathFill;\n      }\n    }\n  };\n  Path.prototype.buildPath = function (ctx, shapeCfg, inBatch) {};\n  Path.prototype.pathUpdated = function () {\n    this.__dirty &= ~SHAPE_CHANGED_BIT;\n  };\n  Path.prototype.getUpdatedPathProxy = function (inBatch) {\n    !this.path && this.createPathProxy();\n    this.path.beginPath();\n    this.buildPath(this.path, this.shape, inBatch);\n    return this.path;\n  };\n  Path.prototype.createPathProxy = function () {\n    this.path = new PathProxy(false);\n  };\n  Path.prototype.hasStroke = function () {\n    var style = this.style;\n    var stroke = style.stroke;\n    return !(stroke == null || stroke === 'none' || !(style.lineWidth > 0));\n  };\n  Path.prototype.hasFill = function () {\n    var style = this.style;\n    var fill = style.fill;\n    return fill != null && fill !== 'none';\n  };\n  Path.prototype.getBoundingRect = function () {\n    var rect = this._rect;\n    var style = this.style;\n    var needsUpdateRect = !rect;\n    if (needsUpdateRect) {\n      var firstInvoke = false;\n      if (!this.path) {\n        firstInvoke = true;\n        this.createPathProxy();\n      }\n      var path = this.path;\n      if (firstInvoke || this.__dirty & SHAPE_CHANGED_BIT) {\n        path.beginPath();\n        this.buildPath(path, this.shape, false);\n        this.pathUpdated();\n      }\n      rect = path.getBoundingRect();\n    }\n    this._rect = rect;\n    if (this.hasStroke() && this.path && this.path.len() > 0) {\n      var rectStroke = this._rectStroke || (this._rectStroke = rect.clone());\n      if (this.__dirty || needsUpdateRect) {\n        rectStroke.copy(rect);\n        var lineScale = style.strokeNoScale ? this.getLineScale() : 1;\n        var w = style.lineWidth;\n        if (!this.hasFill()) {\n          var strokeContainThreshold = this.strokeContainThreshold;\n          w = Math.max(w, strokeContainThreshold == null ? 4 : strokeContainThreshold);\n        }\n        if (lineScale > 1e-10) {\n          rectStroke.width += w / lineScale;\n          rectStroke.height += w / lineScale;\n          rectStroke.x -= w / lineScale / 2;\n          rectStroke.y -= w / lineScale / 2;\n        }\n      }\n      return rectStroke;\n    }\n    return rect;\n  };\n  Path.prototype.contain = function (x, y) {\n    var localPos = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    var style = this.style;\n    x = localPos[0];\n    y = localPos[1];\n    if (rect.contain(x, y)) {\n      var pathProxy = this.path;\n      if (this.hasStroke()) {\n        var lineWidth = style.lineWidth;\n        var lineScale = style.strokeNoScale ? this.getLineScale() : 1;\n        if (lineScale > 1e-10) {\n          if (!this.hasFill()) {\n            lineWidth = Math.max(lineWidth, this.strokeContainThreshold);\n          }\n          if (pathContain.containStroke(pathProxy, lineWidth / lineScale, x, y)) {\n            return true;\n          }\n        }\n      }\n      if (this.hasFill()) {\n        return pathContain.contain(pathProxy, x, y);\n      }\n    }\n    return false;\n  };\n  Path.prototype.dirtyShape = function () {\n    this.__dirty |= SHAPE_CHANGED_BIT;\n    if (this._rect) {\n      this._rect = null;\n    }\n    if (this._decalEl) {\n      this._decalEl.dirtyShape();\n    }\n    this.markRedraw();\n  };\n  Path.prototype.dirty = function () {\n    this.dirtyStyle();\n    this.dirtyShape();\n  };\n  Path.prototype.animateShape = function (loop) {\n    return this.animate('shape', loop);\n  };\n  Path.prototype.updateDuringAnimation = function (targetKey) {\n    if (targetKey === 'style') {\n      this.dirtyStyle();\n    } else if (targetKey === 'shape') {\n      this.dirtyShape();\n    } else {\n      this.markRedraw();\n    }\n  };\n  Path.prototype.attrKV = function (key, value) {\n    if (key === 'shape') {\n      this.setShape(value);\n    } else {\n      _super.prototype.attrKV.call(this, key, value);\n    }\n  };\n  Path.prototype.setShape = function (keyOrObj, value) {\n    var shape = this.shape;\n    if (!shape) {\n      shape = this.shape = {};\n    }\n    if (typeof keyOrObj === 'string') {\n      shape[keyOrObj] = value;\n    } else {\n      extend(shape, keyOrObj);\n    }\n    this.dirtyShape();\n    return this;\n  };\n  Path.prototype.shapeChanged = function () {\n    return !!(this.__dirty & SHAPE_CHANGED_BIT);\n  };\n  Path.prototype.createStyle = function (obj) {\n    return createObject(DEFAULT_PATH_STYLE, obj);\n  };\n  Path.prototype._innerSaveToNormal = function (toState) {\n    _super.prototype._innerSaveToNormal.call(this, toState);\n    var normalState = this._normalState;\n    if (toState.shape && !normalState.shape) {\n      normalState.shape = extend({}, this.shape);\n    }\n  };\n  Path.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n    _super.prototype._applyStateObj.call(this, stateName, state, normalState, keepCurrentStates, transition, animationCfg);\n    var needsRestoreToNormal = !(state && keepCurrentStates);\n    var targetShape;\n    if (state && state.shape) {\n      if (transition) {\n        if (keepCurrentStates) {\n          targetShape = state.shape;\n        } else {\n          targetShape = extend({}, normalState.shape);\n          extend(targetShape, state.shape);\n        }\n      } else {\n        targetShape = extend({}, keepCurrentStates ? this.shape : normalState.shape);\n        extend(targetShape, state.shape);\n      }\n    } else if (needsRestoreToNormal) {\n      targetShape = normalState.shape;\n    }\n    if (targetShape) {\n      if (transition) {\n        this.shape = extend({}, this.shape);\n        var targetShapePrimaryProps = {};\n        var shapeKeys = keys(targetShape);\n        for (var i = 0; i < shapeKeys.length; i++) {\n          var key = shapeKeys[i];\n          if (typeof targetShape[key] === 'object') {\n            this.shape[key] = targetShape[key];\n          } else {\n            targetShapePrimaryProps[key] = targetShape[key];\n          }\n        }\n        this._transitionState(stateName, {\n          shape: targetShapePrimaryProps\n        }, animationCfg);\n      } else {\n        this.shape = targetShape;\n        this.dirtyShape();\n      }\n    }\n  };\n  Path.prototype._mergeStates = function (states) {\n    var mergedState = _super.prototype._mergeStates.call(this, states);\n    var mergedShape;\n    for (var i = 0; i < states.length; i++) {\n      var state = states[i];\n      if (state.shape) {\n        mergedShape = mergedShape || {};\n        this._mergeStyle(mergedShape, state.shape);\n      }\n    }\n    if (mergedShape) {\n      mergedState.shape = mergedShape;\n    }\n    return mergedState;\n  };\n  Path.prototype.getAnimationStyleProps = function () {\n    return DEFAULT_PATH_ANIMATION_PROPS;\n  };\n  Path.prototype.isZeroArea = function () {\n    return false;\n  };\n  Path.extend = function (defaultProps) {\n    var Sub = function (_super) {\n      __extends(Sub, _super);\n      function Sub(opts) {\n        var _this = _super.call(this, opts) || this;\n        defaultProps.init && defaultProps.init.call(_this, opts);\n        return _this;\n      }\n      Sub.prototype.getDefaultStyle = function () {\n        return clone(defaultProps.style);\n      };\n      Sub.prototype.getDefaultShape = function () {\n        return clone(defaultProps.shape);\n      };\n      return Sub;\n    }(Path);\n    for (var key in defaultProps) {\n      if (typeof defaultProps[key] === 'function') {\n        Sub.prototype[key] = defaultProps[key];\n      }\n    }\n    return Sub;\n  };\n  Path.initDefaultProps = function () {\n    var pathProto = Path.prototype;\n    pathProto.type = 'path';\n    pathProto.strokeContainThreshold = 5;\n    pathProto.segmentIgnoreThreshold = 0;\n    pathProto.subPixelOptimize = false;\n    pathProto.autoBatch = false;\n    pathProto.__dirty = REDRAW_BIT | STYLE_CHANGED_BIT | SHAPE_CHANGED_BIT;\n  }();\n  return Path;\n}(Displayable);\nexport default Path;", "import { __extends } from \"tslib\";\nimport Displayable, { DEFAULT_COMMON_STYLE, DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { defaults, createObject } from '../core/util.js';\nexport var DEFAULT_IMAGE_STYLE = defaults({\n  x: 0,\n  y: 0\n}, DEFAULT_COMMON_STYLE);\nexport var DEFAULT_IMAGE_ANIMATION_PROPS = {\n  style: defaults({\n    x: true,\n    y: true,\n    width: true,\n    height: true,\n    sx: true,\n    sy: true,\n    sWidth: true,\n    sHeight: true\n  }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nfunction isImageLike(source) {\n  return !!(source && typeof source !== 'string' && source.width && source.height);\n}\nvar ZRImage = function (_super) {\n  __extends(ZRImage, _super);\n  function ZRImage() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  ZRImage.prototype.createStyle = function (obj) {\n    return createObject(DEFAULT_IMAGE_STYLE, obj);\n  };\n  ZRImage.prototype._getSize = function (dim) {\n    var style = this.style;\n    var size = style[dim];\n    if (size != null) {\n      return size;\n    }\n    var imageSource = isImageLike(style.image) ? style.image : this.__image;\n    if (!imageSource) {\n      return 0;\n    }\n    var otherDim = dim === 'width' ? 'height' : 'width';\n    var otherDimSize = style[otherDim];\n    if (otherDimSize == null) {\n      return imageSource[dim];\n    } else {\n      return imageSource[dim] / imageSource[otherDim] * otherDimSize;\n    }\n  };\n  ZRImage.prototype.getWidth = function () {\n    return this._getSize('width');\n  };\n  ZRImage.prototype.getHeight = function () {\n    return this._getSize('height');\n  };\n  ZRImage.prototype.getAnimationStyleProps = function () {\n    return DEFAULT_IMAGE_ANIMATION_PROPS;\n  };\n  ZRImage.prototype.getBoundingRect = function () {\n    var style = this.style;\n    if (!this._rect) {\n      this._rect = new BoundingRect(style.x || 0, style.y || 0, this.getWidth(), this.getHeight());\n    }\n    return this._rect;\n  };\n  return ZRImage;\n}(Displayable);\nZRImage.prototype.type = 'image';\nexport default ZRImage;", "import { __extends } from \"tslib\";\nimport Displayable from './Displayable.js';\nimport { getBoundingRect } from '../contain/text.js';\nimport { DEFAULT_PATH_STYLE } from './Path.js';\nimport { createObject, defaults } from '../core/util.js';\nimport { DEFAULT_FONT } from '../core/platform.js';\nexport var DEFAULT_TSPAN_STYLE = defaults({\n  strokeFirst: true,\n  font: DEFAULT_FONT,\n  x: 0,\n  y: 0,\n  textAlign: 'left',\n  textBaseline: 'top',\n  miterLimit: 2\n}, DEFAULT_PATH_STYLE);\nvar TSpan = function (_super) {\n  __extends(TSpan, _super);\n  function TSpan() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  TSpan.prototype.hasStroke = function () {\n    var style = this.style;\n    var stroke = style.stroke;\n    return stroke != null && stroke !== 'none' && style.lineWidth > 0;\n  };\n  TSpan.prototype.hasFill = function () {\n    var style = this.style;\n    var fill = style.fill;\n    return fill != null && fill !== 'none';\n  };\n  TSpan.prototype.createStyle = function (obj) {\n    return createObject(DEFAULT_TSPAN_STYLE, obj);\n  };\n  TSpan.prototype.setBoundingRect = function (rect) {\n    this._rect = rect;\n  };\n  TSpan.prototype.getBoundingRect = function () {\n    var style = this.style;\n    if (!this._rect) {\n      var text = style.text;\n      text != null ? text += '' : text = '';\n      var rect = getBoundingRect(text, style.font, style.textAlign, style.textBaseline);\n      rect.x += style.x || 0;\n      rect.y += style.y || 0;\n      if (this.hasStroke()) {\n        var w = style.lineWidth;\n        rect.x -= w / 2;\n        rect.y -= w / 2;\n        rect.width += w;\n        rect.height += w;\n      }\n      this._rect = rect;\n    }\n    return this._rect;\n  };\n  TSpan.initDefaultProps = function () {\n    var tspanProto = TSpan.prototype;\n    tspanProto.dirtyRectTolerance = 10;\n  }();\n  return TSpan;\n}(Displayable);\nTSpan.prototype.type = 'tspan';\nexport default TSpan;", "import { isArray, isNumber, map } from '../core/util.js';\nexport function normalizeLineDash(lineType, lineWidth) {\n  if (!lineType || lineType === 'solid' || !(lineWidth > 0)) {\n    return null;\n  }\n  return lineType === 'dashed' ? [4 * lineWidth, 2 * lineWidth] : lineType === 'dotted' ? [lineWidth] : isNumber(lineType) ? [lineType] : isArray(lineType) ? lineType : null;\n}\nexport function getLineDash(el) {\n  var style = el.style;\n  var lineDash = style.lineDash && style.lineWidth > 0 && normalizeLineDash(style.lineDash, style.lineWidth);\n  var lineDashOffset = style.lineDashOffset;\n  if (lineDash) {\n    var lineScale_1 = style.strokeNoScale && el.getLineScale ? el.getLineScale() : 1;\n    if (lineScale_1 && lineScale_1 !== 1) {\n      lineDash = map(lineDash, function (rawVal) {\n        return rawVal / lineScale_1;\n      });\n      lineDashOffset /= lineScale_1;\n    }\n  }\n  return [lineDash, lineDashOffset];\n}", "import { DEFAULT_COMMON_STYLE } from '../graphic/Displayable.js';\nimport PathProxy from '../core/PathProxy.js';\nimport { createOrUpdateImage, isImageReady } from '../graphic/helper/image.js';\nimport { getCanvasGradient, isClipPathChanged } from './helper.js';\nimport Path from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport TSpan from '../graphic/TSpan.js';\nimport { RADIAN_TO_DEGREE } from '../core/util.js';\nimport { getLineDash } from './dashStyle.js';\nimport { REDRAW_BIT, SHAPE_CHANGED_BIT } from '../graphic/constants.js';\nimport { DEFAULT_FONT } from '../core/platform.js';\nvar pathProxyForDraw = new PathProxy(true);\nfunction styleHasStroke(style) {\n  var stroke = style.stroke;\n  return !(stroke == null || stroke === 'none' || !(style.lineWidth > 0));\n}\nfunction isValidStrokeFillStyle(strokeOrFill) {\n  return typeof strokeOrFill === 'string' && strokeOrFill !== 'none';\n}\nfunction styleHasFill(style) {\n  var fill = style.fill;\n  return fill != null && fill !== 'none';\n}\nfunction doFillPath(ctx, style) {\n  if (style.fillOpacity != null && style.fillOpacity !== 1) {\n    var originalGlobalAlpha = ctx.globalAlpha;\n    ctx.globalAlpha = style.fillOpacity * style.opacity;\n    ctx.fill();\n    ctx.globalAlpha = originalGlobalAlpha;\n  } else {\n    ctx.fill();\n  }\n}\nfunction doStrokePath(ctx, style) {\n  if (style.strokeOpacity != null && style.strokeOpacity !== 1) {\n    var originalGlobalAlpha = ctx.globalAlpha;\n    ctx.globalAlpha = style.strokeOpacity * style.opacity;\n    ctx.stroke();\n    ctx.globalAlpha = originalGlobalAlpha;\n  } else {\n    ctx.stroke();\n  }\n}\nexport function createCanvasPattern(ctx, pattern, el) {\n  var image = createOrUpdateImage(pattern.image, pattern.__image, el);\n  if (isImageReady(image)) {\n    var canvasPattern = ctx.createPattern(image, pattern.repeat || 'repeat');\n    if (typeof DOMMatrix === 'function' && canvasPattern && canvasPattern.setTransform) {\n      var matrix = new DOMMatrix();\n      matrix.translateSelf(pattern.x || 0, pattern.y || 0);\n      matrix.rotateSelf(0, 0, (pattern.rotation || 0) * RADIAN_TO_DEGREE);\n      matrix.scaleSelf(pattern.scaleX || 1, pattern.scaleY || 1);\n      canvasPattern.setTransform(matrix);\n    }\n    return canvasPattern;\n  }\n}\nfunction brushPath(ctx, el, style, inBatch) {\n  var _a;\n  var hasStroke = styleHasStroke(style);\n  var hasFill = styleHasFill(style);\n  var strokePercent = style.strokePercent;\n  var strokePart = strokePercent < 1;\n  var firstDraw = !el.path;\n  if ((!el.silent || strokePart) && firstDraw) {\n    el.createPathProxy();\n  }\n  var path = el.path || pathProxyForDraw;\n  var dirtyFlag = el.__dirty;\n  if (!inBatch) {\n    var fill = style.fill;\n    var stroke = style.stroke;\n    var hasFillGradient = hasFill && !!fill.colorStops;\n    var hasStrokeGradient = hasStroke && !!stroke.colorStops;\n    var hasFillPattern = hasFill && !!fill.image;\n    var hasStrokePattern = hasStroke && !!stroke.image;\n    var fillGradient = void 0;\n    var strokeGradient = void 0;\n    var fillPattern = void 0;\n    var strokePattern = void 0;\n    var rect = void 0;\n    if (hasFillGradient || hasStrokeGradient) {\n      rect = el.getBoundingRect();\n    }\n    if (hasFillGradient) {\n      fillGradient = dirtyFlag ? getCanvasGradient(ctx, fill, rect) : el.__canvasFillGradient;\n      el.__canvasFillGradient = fillGradient;\n    }\n    if (hasStrokeGradient) {\n      strokeGradient = dirtyFlag ? getCanvasGradient(ctx, stroke, rect) : el.__canvasStrokeGradient;\n      el.__canvasStrokeGradient = strokeGradient;\n    }\n    if (hasFillPattern) {\n      fillPattern = dirtyFlag || !el.__canvasFillPattern ? createCanvasPattern(ctx, fill, el) : el.__canvasFillPattern;\n      el.__canvasFillPattern = fillPattern;\n    }\n    if (hasStrokePattern) {\n      strokePattern = dirtyFlag || !el.__canvasStrokePattern ? createCanvasPattern(ctx, stroke, el) : el.__canvasStrokePattern;\n      el.__canvasStrokePattern = fillPattern;\n    }\n    if (hasFillGradient) {\n      ctx.fillStyle = fillGradient;\n    } else if (hasFillPattern) {\n      if (fillPattern) {\n        ctx.fillStyle = fillPattern;\n      } else {\n        hasFill = false;\n      }\n    }\n    if (hasStrokeGradient) {\n      ctx.strokeStyle = strokeGradient;\n    } else if (hasStrokePattern) {\n      if (strokePattern) {\n        ctx.strokeStyle = strokePattern;\n      } else {\n        hasStroke = false;\n      }\n    }\n  }\n  var scale = el.getGlobalScale();\n  path.setScale(scale[0], scale[1], el.segmentIgnoreThreshold);\n  var lineDash;\n  var lineDashOffset;\n  if (ctx.setLineDash && style.lineDash) {\n    _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n  }\n  var needsRebuild = true;\n  if (firstDraw || dirtyFlag & SHAPE_CHANGED_BIT) {\n    path.setDPR(ctx.dpr);\n    if (strokePart) {\n      path.setContext(null);\n    } else {\n      path.setContext(ctx);\n      needsRebuild = false;\n    }\n    path.reset();\n    el.buildPath(path, el.shape, inBatch);\n    path.toStatic();\n    el.pathUpdated();\n  }\n  if (needsRebuild) {\n    path.rebuildPath(ctx, strokePart ? strokePercent : 1);\n  }\n  if (lineDash) {\n    ctx.setLineDash(lineDash);\n    ctx.lineDashOffset = lineDashOffset;\n  }\n  if (!inBatch) {\n    if (style.strokeFirst) {\n      if (hasStroke) {\n        doStrokePath(ctx, style);\n      }\n      if (hasFill) {\n        doFillPath(ctx, style);\n      }\n    } else {\n      if (hasFill) {\n        doFillPath(ctx, style);\n      }\n      if (hasStroke) {\n        doStrokePath(ctx, style);\n      }\n    }\n  }\n  if (lineDash) {\n    ctx.setLineDash([]);\n  }\n}\nfunction brushImage(ctx, el, style) {\n  var image = el.__image = createOrUpdateImage(style.image, el.__image, el, el.onload);\n  if (!image || !isImageReady(image)) {\n    return;\n  }\n  var x = style.x || 0;\n  var y = style.y || 0;\n  var width = el.getWidth();\n  var height = el.getHeight();\n  var aspect = image.width / image.height;\n  if (width == null && height != null) {\n    width = height * aspect;\n  } else if (height == null && width != null) {\n    height = width / aspect;\n  } else if (width == null && height == null) {\n    width = image.width;\n    height = image.height;\n  }\n  if (style.sWidth && style.sHeight) {\n    var sx = style.sx || 0;\n    var sy = style.sy || 0;\n    ctx.drawImage(image, sx, sy, style.sWidth, style.sHeight, x, y, width, height);\n  } else if (style.sx && style.sy) {\n    var sx = style.sx;\n    var sy = style.sy;\n    var sWidth = width - sx;\n    var sHeight = height - sy;\n    ctx.drawImage(image, sx, sy, sWidth, sHeight, x, y, width, height);\n  } else {\n    ctx.drawImage(image, x, y, width, height);\n  }\n}\nfunction brushText(ctx, el, style) {\n  var _a;\n  var text = style.text;\n  text != null && (text += '');\n  if (text) {\n    ctx.font = style.font || DEFAULT_FONT;\n    ctx.textAlign = style.textAlign;\n    ctx.textBaseline = style.textBaseline;\n    var lineDash = void 0;\n    var lineDashOffset = void 0;\n    if (ctx.setLineDash && style.lineDash) {\n      _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n    }\n    if (lineDash) {\n      ctx.setLineDash(lineDash);\n      ctx.lineDashOffset = lineDashOffset;\n    }\n    if (style.strokeFirst) {\n      if (styleHasStroke(style)) {\n        ctx.strokeText(text, style.x, style.y);\n      }\n      if (styleHasFill(style)) {\n        ctx.fillText(text, style.x, style.y);\n      }\n    } else {\n      if (styleHasFill(style)) {\n        ctx.fillText(text, style.x, style.y);\n      }\n      if (styleHasStroke(style)) {\n        ctx.strokeText(text, style.x, style.y);\n      }\n    }\n    if (lineDash) {\n      ctx.setLineDash([]);\n    }\n  }\n}\nvar SHADOW_NUMBER_PROPS = ['shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\nvar STROKE_PROPS = [['lineCap', 'butt'], ['lineJoin', 'miter'], ['miterLimit', 10]];\nfunction bindCommonProps(ctx, style, prevStyle, forceSetAll, scope) {\n  var styleChanged = false;\n  if (!forceSetAll) {\n    prevStyle = prevStyle || {};\n    if (style === prevStyle) {\n      return false;\n    }\n  }\n  if (forceSetAll || style.opacity !== prevStyle.opacity) {\n    flushPathDrawn(ctx, scope);\n    styleChanged = true;\n    var opacity = Math.max(Math.min(style.opacity, 1), 0);\n    ctx.globalAlpha = isNaN(opacity) ? DEFAULT_COMMON_STYLE.opacity : opacity;\n  }\n  if (forceSetAll || style.blend !== prevStyle.blend) {\n    if (!styleChanged) {\n      flushPathDrawn(ctx, scope);\n      styleChanged = true;\n    }\n    ctx.globalCompositeOperation = style.blend || DEFAULT_COMMON_STYLE.blend;\n  }\n  for (var i = 0; i < SHADOW_NUMBER_PROPS.length; i++) {\n    var propName = SHADOW_NUMBER_PROPS[i];\n    if (forceSetAll || style[propName] !== prevStyle[propName]) {\n      if (!styleChanged) {\n        flushPathDrawn(ctx, scope);\n        styleChanged = true;\n      }\n      ctx[propName] = ctx.dpr * (style[propName] || 0);\n    }\n  }\n  if (forceSetAll || style.shadowColor !== prevStyle.shadowColor) {\n    if (!styleChanged) {\n      flushPathDrawn(ctx, scope);\n      styleChanged = true;\n    }\n    ctx.shadowColor = style.shadowColor || DEFAULT_COMMON_STYLE.shadowColor;\n  }\n  return styleChanged;\n}\nfunction bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetAll, scope) {\n  var style = getStyle(el, scope.inHover);\n  var prevStyle = forceSetAll ? null : prevEl && getStyle(prevEl, scope.inHover) || {};\n  if (style === prevStyle) {\n    return false;\n  }\n  var styleChanged = bindCommonProps(ctx, style, prevStyle, forceSetAll, scope);\n  if (forceSetAll || style.fill !== prevStyle.fill) {\n    if (!styleChanged) {\n      flushPathDrawn(ctx, scope);\n      styleChanged = true;\n    }\n    isValidStrokeFillStyle(style.fill) && (ctx.fillStyle = style.fill);\n  }\n  if (forceSetAll || style.stroke !== prevStyle.stroke) {\n    if (!styleChanged) {\n      flushPathDrawn(ctx, scope);\n      styleChanged = true;\n    }\n    isValidStrokeFillStyle(style.stroke) && (ctx.strokeStyle = style.stroke);\n  }\n  if (forceSetAll || style.opacity !== prevStyle.opacity) {\n    if (!styleChanged) {\n      flushPathDrawn(ctx, scope);\n      styleChanged = true;\n    }\n    ctx.globalAlpha = style.opacity == null ? 1 : style.opacity;\n  }\n  if (el.hasStroke()) {\n    var lineWidth = style.lineWidth;\n    var newLineWidth = lineWidth / (style.strokeNoScale && el.getLineScale ? el.getLineScale() : 1);\n    if (ctx.lineWidth !== newLineWidth) {\n      if (!styleChanged) {\n        flushPathDrawn(ctx, scope);\n        styleChanged = true;\n      }\n      ctx.lineWidth = newLineWidth;\n    }\n  }\n  for (var i = 0; i < STROKE_PROPS.length; i++) {\n    var prop = STROKE_PROPS[i];\n    var propName = prop[0];\n    if (forceSetAll || style[propName] !== prevStyle[propName]) {\n      if (!styleChanged) {\n        flushPathDrawn(ctx, scope);\n        styleChanged = true;\n      }\n      ctx[propName] = style[propName] || prop[1];\n    }\n  }\n  return styleChanged;\n}\nfunction bindImageStyle(ctx, el, prevEl, forceSetAll, scope) {\n  return bindCommonProps(ctx, getStyle(el, scope.inHover), prevEl && getStyle(prevEl, scope.inHover), forceSetAll, scope);\n}\nfunction setContextTransform(ctx, el) {\n  var m = el.transform;\n  var dpr = ctx.dpr || 1;\n  if (m) {\n    ctx.setTransform(dpr * m[0], dpr * m[1], dpr * m[2], dpr * m[3], dpr * m[4], dpr * m[5]);\n  } else {\n    ctx.setTransform(dpr, 0, 0, dpr, 0, 0);\n  }\n}\nfunction updateClipStatus(clipPaths, ctx, scope) {\n  var allClipped = false;\n  for (var i = 0; i < clipPaths.length; i++) {\n    var clipPath = clipPaths[i];\n    allClipped = allClipped || clipPath.isZeroArea();\n    setContextTransform(ctx, clipPath);\n    ctx.beginPath();\n    clipPath.buildPath(ctx, clipPath.shape);\n    ctx.clip();\n  }\n  scope.allClipped = allClipped;\n}\nfunction isTransformChanged(m0, m1) {\n  if (m0 && m1) {\n    return m0[0] !== m1[0] || m0[1] !== m1[1] || m0[2] !== m1[2] || m0[3] !== m1[3] || m0[4] !== m1[4] || m0[5] !== m1[5];\n  } else if (!m0 && !m1) {\n    return false;\n  }\n  return true;\n}\nvar DRAW_TYPE_PATH = 1;\nvar DRAW_TYPE_IMAGE = 2;\nvar DRAW_TYPE_TEXT = 3;\nvar DRAW_TYPE_INCREMENTAL = 4;\nfunction canPathBatch(style) {\n  var hasFill = styleHasFill(style);\n  var hasStroke = styleHasStroke(style);\n  return !(style.lineDash || !(+hasFill ^ +hasStroke) || hasFill && typeof style.fill !== 'string' || hasStroke && typeof style.stroke !== 'string' || style.strokePercent < 1 || style.strokeOpacity < 1 || style.fillOpacity < 1);\n}\nfunction flushPathDrawn(ctx, scope) {\n  scope.batchFill && ctx.fill();\n  scope.batchStroke && ctx.stroke();\n  scope.batchFill = '';\n  scope.batchStroke = '';\n}\nfunction getStyle(el, inHover) {\n  return inHover ? el.__hoverStyle || el.style : el.style;\n}\nexport function brushSingle(ctx, el) {\n  brush(ctx, el, {\n    inHover: false,\n    viewWidth: 0,\n    viewHeight: 0\n  }, true);\n}\nexport function brush(ctx, el, scope, isLast) {\n  var m = el.transform;\n  if (!el.shouldBePainted(scope.viewWidth, scope.viewHeight, false, false)) {\n    el.__dirty &= ~REDRAW_BIT;\n    el.__isRendered = false;\n    return;\n  }\n  var clipPaths = el.__clipPaths;\n  var prevElClipPaths = scope.prevElClipPaths;\n  var forceSetTransform = false;\n  var forceSetStyle = false;\n  if (!prevElClipPaths || isClipPathChanged(clipPaths, prevElClipPaths)) {\n    if (prevElClipPaths && prevElClipPaths.length) {\n      flushPathDrawn(ctx, scope);\n      ctx.restore();\n      forceSetStyle = forceSetTransform = true;\n      scope.prevElClipPaths = null;\n      scope.allClipped = false;\n      scope.prevEl = null;\n    }\n    if (clipPaths && clipPaths.length) {\n      flushPathDrawn(ctx, scope);\n      ctx.save();\n      updateClipStatus(clipPaths, ctx, scope);\n      forceSetTransform = true;\n    }\n    scope.prevElClipPaths = clipPaths;\n  }\n  if (scope.allClipped) {\n    el.__isRendered = false;\n    return;\n  }\n  el.beforeBrush && el.beforeBrush();\n  el.innerBeforeBrush();\n  var prevEl = scope.prevEl;\n  if (!prevEl) {\n    forceSetStyle = forceSetTransform = true;\n  }\n  var canBatchPath = el instanceof Path && el.autoBatch && canPathBatch(el.style);\n  if (forceSetTransform || isTransformChanged(m, prevEl.transform)) {\n    flushPathDrawn(ctx, scope);\n    setContextTransform(ctx, el);\n  } else if (!canBatchPath) {\n    flushPathDrawn(ctx, scope);\n  }\n  var style = getStyle(el, scope.inHover);\n  if (el instanceof Path) {\n    if (scope.lastDrawType !== DRAW_TYPE_PATH) {\n      forceSetStyle = true;\n      scope.lastDrawType = DRAW_TYPE_PATH;\n    }\n    bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetStyle, scope);\n    if (!canBatchPath || !scope.batchFill && !scope.batchStroke) {\n      ctx.beginPath();\n    }\n    brushPath(ctx, el, style, canBatchPath);\n    if (canBatchPath) {\n      scope.batchFill = style.fill || '';\n      scope.batchStroke = style.stroke || '';\n    }\n  } else {\n    if (el instanceof TSpan) {\n      if (scope.lastDrawType !== DRAW_TYPE_TEXT) {\n        forceSetStyle = true;\n        scope.lastDrawType = DRAW_TYPE_TEXT;\n      }\n      bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetStyle, scope);\n      brushText(ctx, el, style);\n    } else if (el instanceof ZRImage) {\n      if (scope.lastDrawType !== DRAW_TYPE_IMAGE) {\n        forceSetStyle = true;\n        scope.lastDrawType = DRAW_TYPE_IMAGE;\n      }\n      bindImageStyle(ctx, el, prevEl, forceSetStyle, scope);\n      brushImage(ctx, el, style);\n    } else if (el.getTemporalDisplayables) {\n      if (scope.lastDrawType !== DRAW_TYPE_INCREMENTAL) {\n        forceSetStyle = true;\n        scope.lastDrawType = DRAW_TYPE_INCREMENTAL;\n      }\n      brushIncremental(ctx, el, scope);\n    }\n  }\n  if (canBatchPath && isLast) {\n    flushPathDrawn(ctx, scope);\n  }\n  el.innerAfterBrush();\n  el.afterBrush && el.afterBrush();\n  scope.prevEl = el;\n  el.__dirty = 0;\n  el.__isRendered = true;\n}\nfunction brushIncremental(ctx, el, scope) {\n  var displayables = el.getDisplayables();\n  var temporalDisplayables = el.getTemporalDisplayables();\n  ctx.save();\n  var innerScope = {\n    prevElClipPaths: null,\n    prevEl: null,\n    allClipped: false,\n    viewWidth: scope.viewWidth,\n    viewHeight: scope.viewHeight,\n    inHover: scope.inHover\n  };\n  var i;\n  var len;\n  for (i = el.getCursor(), len = displayables.length; i < len; i++) {\n    var displayable = displayables[i];\n    displayable.beforeBrush && displayable.beforeBrush();\n    displayable.innerBeforeBrush();\n    brush(ctx, displayable, innerScope, i === len - 1);\n    displayable.innerAfterBrush();\n    displayable.afterBrush && displayable.afterBrush();\n    innerScope.prevEl = displayable;\n  }\n  for (var i_1 = 0, len_1 = temporalDisplayables.length; i_1 < len_1; i_1++) {\n    var displayable = temporalDisplayables[i_1];\n    displayable.beforeBrush && displayable.beforeBrush();\n    displayable.innerBeforeBrush();\n    brush(ctx, displayable, innerScope, i_1 === len_1 - 1);\n    displayable.innerAfterBrush();\n    displayable.afterBrush && displayable.afterBrush();\n    innerScope.prevEl = displayable;\n  }\n  el.clearTemporalDisplayables();\n  el.notClear = true;\n  ctx.restore();\n}", "import * as imageHelper from '../helper/image.js';\nimport { extend, retrieve2, retrieve3, reduce } from '../../core/util.js';\nimport { getLineHeight, getWidth, parsePercent } from '../../contain/text.js';\nvar STYLE_REG = /\\{([a-zA-Z0-9_]+)\\|([^}]*)\\}/g;\nexport function truncateText(text, containerWidth, font, ellipsis, options) {\n  if (!containerWidth) {\n    return '';\n  }\n  var textLines = (text + '').split('\\n');\n  options = prepareTruncateOptions(containerWidth, font, ellipsis, options);\n  for (var i = 0, len = textLines.length; i < len; i++) {\n    textLines[i] = truncateSingleLine(textLines[i], options);\n  }\n  return textLines.join('\\n');\n}\nfunction prepareTruncateOptions(containerWidth, font, ellipsis, options) {\n  options = options || {};\n  var preparedOpts = extend({}, options);\n  preparedOpts.font = font;\n  ellipsis = retrieve2(ellipsis, '...');\n  preparedOpts.maxIterations = retrieve2(options.maxIterations, 2);\n  var minChar = preparedOpts.minChar = retrieve2(options.minChar, 0);\n  preparedOpts.cnCharWidth = getWidth('国', font);\n  var ascCharWidth = preparedOpts.ascCharWidth = getWidth('a', font);\n  preparedOpts.placeholder = retrieve2(options.placeholder, '');\n  var contentWidth = containerWidth = Math.max(0, containerWidth - 1);\n  for (var i = 0; i < minChar && contentWidth >= ascCharWidth; i++) {\n    contentWidth -= ascCharWidth;\n  }\n  var ellipsisWidth = getWidth(ellipsis, font);\n  if (ellipsisWidth > contentWidth) {\n    ellipsis = '';\n    ellipsisWidth = 0;\n  }\n  contentWidth = containerWidth - ellipsisWidth;\n  preparedOpts.ellipsis = ellipsis;\n  preparedOpts.ellipsisWidth = ellipsisWidth;\n  preparedOpts.contentWidth = contentWidth;\n  preparedOpts.containerWidth = containerWidth;\n  return preparedOpts;\n}\nfunction truncateSingleLine(textLine, options) {\n  var containerWidth = options.containerWidth;\n  var font = options.font;\n  var contentWidth = options.contentWidth;\n  if (!containerWidth) {\n    return '';\n  }\n  var lineWidth = getWidth(textLine, font);\n  if (lineWidth <= containerWidth) {\n    return textLine;\n  }\n  for (var j = 0;; j++) {\n    if (lineWidth <= contentWidth || j >= options.maxIterations) {\n      textLine += options.ellipsis;\n      break;\n    }\n    var subLength = j === 0 ? estimateLength(textLine, contentWidth, options.ascCharWidth, options.cnCharWidth) : lineWidth > 0 ? Math.floor(textLine.length * contentWidth / lineWidth) : 0;\n    textLine = textLine.substr(0, subLength);\n    lineWidth = getWidth(textLine, font);\n  }\n  if (textLine === '') {\n    textLine = options.placeholder;\n  }\n  return textLine;\n}\nfunction estimateLength(text, contentWidth, ascCharWidth, cnCharWidth) {\n  var width = 0;\n  var i = 0;\n  for (var len = text.length; i < len && width < contentWidth; i++) {\n    var charCode = text.charCodeAt(i);\n    width += 0 <= charCode && charCode <= 127 ? ascCharWidth : cnCharWidth;\n  }\n  return i;\n}\nexport function parsePlainText(text, style) {\n  text != null && (text += '');\n  var overflow = style.overflow;\n  var padding = style.padding;\n  var font = style.font;\n  var truncate = overflow === 'truncate';\n  var calculatedLineHeight = getLineHeight(font);\n  var lineHeight = retrieve2(style.lineHeight, calculatedLineHeight);\n  var bgColorDrawn = !!style.backgroundColor;\n  var truncateLineOverflow = style.lineOverflow === 'truncate';\n  var width = style.width;\n  var lines;\n  if (width != null && (overflow === 'break' || overflow === 'breakAll')) {\n    lines = text ? wrapText(text, style.font, width, overflow === 'breakAll', 0).lines : [];\n  } else {\n    lines = text ? text.split('\\n') : [];\n  }\n  var contentHeight = lines.length * lineHeight;\n  var height = retrieve2(style.height, contentHeight);\n  if (contentHeight > height && truncateLineOverflow) {\n    var lineCount = Math.floor(height / lineHeight);\n    lines = lines.slice(0, lineCount);\n  }\n  if (text && truncate && width != null) {\n    var options = prepareTruncateOptions(width, font, style.ellipsis, {\n      minChar: style.truncateMinChar,\n      placeholder: style.placeholder\n    });\n    for (var i = 0; i < lines.length; i++) {\n      lines[i] = truncateSingleLine(lines[i], options);\n    }\n  }\n  var outerHeight = height;\n  var contentWidth = 0;\n  for (var i = 0; i < lines.length; i++) {\n    contentWidth = Math.max(getWidth(lines[i], font), contentWidth);\n  }\n  if (width == null) {\n    width = contentWidth;\n  }\n  var outerWidth = contentWidth;\n  if (padding) {\n    outerHeight += padding[0] + padding[2];\n    outerWidth += padding[1] + padding[3];\n    width += padding[1] + padding[3];\n  }\n  if (bgColorDrawn) {\n    outerWidth = width;\n  }\n  return {\n    lines: lines,\n    height: height,\n    outerWidth: outerWidth,\n    outerHeight: outerHeight,\n    lineHeight: lineHeight,\n    calculatedLineHeight: calculatedLineHeight,\n    contentWidth: contentWidth,\n    contentHeight: contentHeight,\n    width: width\n  };\n}\nvar RichTextToken = function () {\n  function RichTextToken() {}\n  return RichTextToken;\n}();\nvar RichTextLine = function () {\n  function RichTextLine(tokens) {\n    this.tokens = [];\n    if (tokens) {\n      this.tokens = tokens;\n    }\n  }\n  return RichTextLine;\n}();\nvar RichTextContentBlock = function () {\n  function RichTextContentBlock() {\n    this.width = 0;\n    this.height = 0;\n    this.contentWidth = 0;\n    this.contentHeight = 0;\n    this.outerWidth = 0;\n    this.outerHeight = 0;\n    this.lines = [];\n  }\n  return RichTextContentBlock;\n}();\nexport { RichTextContentBlock };\nexport function parseRichText(text, style) {\n  var contentBlock = new RichTextContentBlock();\n  text != null && (text += '');\n  if (!text) {\n    return contentBlock;\n  }\n  var topWidth = style.width;\n  var topHeight = style.height;\n  var overflow = style.overflow;\n  var wrapInfo = (overflow === 'break' || overflow === 'breakAll') && topWidth != null ? {\n    width: topWidth,\n    accumWidth: 0,\n    breakAll: overflow === 'breakAll'\n  } : null;\n  var lastIndex = STYLE_REG.lastIndex = 0;\n  var result;\n  while ((result = STYLE_REG.exec(text)) != null) {\n    var matchedIndex = result.index;\n    if (matchedIndex > lastIndex) {\n      pushTokens(contentBlock, text.substring(lastIndex, matchedIndex), style, wrapInfo);\n    }\n    pushTokens(contentBlock, result[2], style, wrapInfo, result[1]);\n    lastIndex = STYLE_REG.lastIndex;\n  }\n  if (lastIndex < text.length) {\n    pushTokens(contentBlock, text.substring(lastIndex, text.length), style, wrapInfo);\n  }\n  var pendingList = [];\n  var calculatedHeight = 0;\n  var calculatedWidth = 0;\n  var stlPadding = style.padding;\n  var truncate = overflow === 'truncate';\n  var truncateLine = style.lineOverflow === 'truncate';\n  function finishLine(line, lineWidth, lineHeight) {\n    line.width = lineWidth;\n    line.lineHeight = lineHeight;\n    calculatedHeight += lineHeight;\n    calculatedWidth = Math.max(calculatedWidth, lineWidth);\n  }\n  outer: for (var i = 0; i < contentBlock.lines.length; i++) {\n    var line = contentBlock.lines[i];\n    var lineHeight = 0;\n    var lineWidth = 0;\n    for (var j = 0; j < line.tokens.length; j++) {\n      var token = line.tokens[j];\n      var tokenStyle = token.styleName && style.rich[token.styleName] || {};\n      var textPadding = token.textPadding = tokenStyle.padding;\n      var paddingH = textPadding ? textPadding[1] + textPadding[3] : 0;\n      var font = token.font = tokenStyle.font || style.font;\n      token.contentHeight = getLineHeight(font);\n      var tokenHeight = retrieve2(tokenStyle.height, token.contentHeight);\n      token.innerHeight = tokenHeight;\n      textPadding && (tokenHeight += textPadding[0] + textPadding[2]);\n      token.height = tokenHeight;\n      token.lineHeight = retrieve3(tokenStyle.lineHeight, style.lineHeight, tokenHeight);\n      token.align = tokenStyle && tokenStyle.align || style.align;\n      token.verticalAlign = tokenStyle && tokenStyle.verticalAlign || 'middle';\n      if (truncateLine && topHeight != null && calculatedHeight + token.lineHeight > topHeight) {\n        if (j > 0) {\n          line.tokens = line.tokens.slice(0, j);\n          finishLine(line, lineWidth, lineHeight);\n          contentBlock.lines = contentBlock.lines.slice(0, i + 1);\n        } else {\n          contentBlock.lines = contentBlock.lines.slice(0, i);\n        }\n        break outer;\n      }\n      var styleTokenWidth = tokenStyle.width;\n      var tokenWidthNotSpecified = styleTokenWidth == null || styleTokenWidth === 'auto';\n      if (typeof styleTokenWidth === 'string' && styleTokenWidth.charAt(styleTokenWidth.length - 1) === '%') {\n        token.percentWidth = styleTokenWidth;\n        pendingList.push(token);\n        token.contentWidth = getWidth(token.text, font);\n      } else {\n        if (tokenWidthNotSpecified) {\n          var textBackgroundColor = tokenStyle.backgroundColor;\n          var bgImg = textBackgroundColor && textBackgroundColor.image;\n          if (bgImg) {\n            bgImg = imageHelper.findExistImage(bgImg);\n            if (imageHelper.isImageReady(bgImg)) {\n              token.width = Math.max(token.width, bgImg.width * tokenHeight / bgImg.height);\n            }\n          }\n        }\n        var remainTruncWidth = truncate && topWidth != null ? topWidth - lineWidth : null;\n        if (remainTruncWidth != null && remainTruncWidth < token.width) {\n          if (!tokenWidthNotSpecified || remainTruncWidth < paddingH) {\n            token.text = '';\n            token.width = token.contentWidth = 0;\n          } else {\n            token.text = truncateText(token.text, remainTruncWidth - paddingH, font, style.ellipsis, {\n              minChar: style.truncateMinChar\n            });\n            token.width = token.contentWidth = getWidth(token.text, font);\n          }\n        } else {\n          token.contentWidth = getWidth(token.text, font);\n        }\n      }\n      token.width += paddingH;\n      lineWidth += token.width;\n      tokenStyle && (lineHeight = Math.max(lineHeight, token.lineHeight));\n    }\n    finishLine(line, lineWidth, lineHeight);\n  }\n  contentBlock.outerWidth = contentBlock.width = retrieve2(topWidth, calculatedWidth);\n  contentBlock.outerHeight = contentBlock.height = retrieve2(topHeight, calculatedHeight);\n  contentBlock.contentHeight = calculatedHeight;\n  contentBlock.contentWidth = calculatedWidth;\n  if (stlPadding) {\n    contentBlock.outerWidth += stlPadding[1] + stlPadding[3];\n    contentBlock.outerHeight += stlPadding[0] + stlPadding[2];\n  }\n  for (var i = 0; i < pendingList.length; i++) {\n    var token = pendingList[i];\n    var percentWidth = token.percentWidth;\n    token.width = parseInt(percentWidth, 10) / 100 * contentBlock.width;\n  }\n  return contentBlock;\n}\nfunction pushTokens(block, str, style, wrapInfo, styleName) {\n  var isEmptyStr = str === '';\n  var tokenStyle = styleName && style.rich[styleName] || {};\n  var lines = block.lines;\n  var font = tokenStyle.font || style.font;\n  var newLine = false;\n  var strLines;\n  var linesWidths;\n  if (wrapInfo) {\n    var tokenPadding = tokenStyle.padding;\n    var tokenPaddingH = tokenPadding ? tokenPadding[1] + tokenPadding[3] : 0;\n    if (tokenStyle.width != null && tokenStyle.width !== 'auto') {\n      var outerWidth_1 = parsePercent(tokenStyle.width, wrapInfo.width) + tokenPaddingH;\n      if (lines.length > 0) {\n        if (outerWidth_1 + wrapInfo.accumWidth > wrapInfo.width) {\n          strLines = str.split('\\n');\n          newLine = true;\n        }\n      }\n      wrapInfo.accumWidth = outerWidth_1;\n    } else {\n      var res = wrapText(str, font, wrapInfo.width, wrapInfo.breakAll, wrapInfo.accumWidth);\n      wrapInfo.accumWidth = res.accumWidth + tokenPaddingH;\n      linesWidths = res.linesWidths;\n      strLines = res.lines;\n    }\n  } else {\n    strLines = str.split('\\n');\n  }\n  for (var i = 0; i < strLines.length; i++) {\n    var text = strLines[i];\n    var token = new RichTextToken();\n    token.styleName = styleName;\n    token.text = text;\n    token.isLineHolder = !text && !isEmptyStr;\n    if (typeof tokenStyle.width === 'number') {\n      token.width = tokenStyle.width;\n    } else {\n      token.width = linesWidths ? linesWidths[i] : getWidth(text, font);\n    }\n    if (!i && !newLine) {\n      var tokens = (lines[lines.length - 1] || (lines[0] = new RichTextLine())).tokens;\n      var tokensLen = tokens.length;\n      tokensLen === 1 && tokens[0].isLineHolder ? tokens[0] = token : (text || !tokensLen || isEmptyStr) && tokens.push(token);\n    } else {\n      lines.push(new RichTextLine([token]));\n    }\n  }\n}\nfunction isAlphabeticLetter(ch) {\n  var code = ch.charCodeAt(0);\n  return code >= 0x20 && code <= 0x24F || code >= 0x370 && code <= 0x10FF || code >= 0x1200 && code <= 0x13FF || code >= 0x1E00 && code <= 0x206F;\n}\nvar breakCharMap = reduce(',&?/;] '.split(''), function (obj, ch) {\n  obj[ch] = true;\n  return obj;\n}, {});\nfunction isWordBreakChar(ch) {\n  if (isAlphabeticLetter(ch)) {\n    if (breakCharMap[ch]) {\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction wrapText(text, font, lineWidth, isBreakAll, lastAccumWidth) {\n  var lines = [];\n  var linesWidths = [];\n  var line = '';\n  var currentWord = '';\n  var currentWordWidth = 0;\n  var accumWidth = 0;\n  for (var i = 0; i < text.length; i++) {\n    var ch = text.charAt(i);\n    if (ch === '\\n') {\n      if (currentWord) {\n        line += currentWord;\n        accumWidth += currentWordWidth;\n      }\n      lines.push(line);\n      linesWidths.push(accumWidth);\n      line = '';\n      currentWord = '';\n      currentWordWidth = 0;\n      accumWidth = 0;\n      continue;\n    }\n    var chWidth = getWidth(ch, font);\n    var inWord = isBreakAll ? false : !isWordBreakChar(ch);\n    if (!lines.length ? lastAccumWidth + accumWidth + chWidth > lineWidth : accumWidth + chWidth > lineWidth) {\n      if (!accumWidth) {\n        if (inWord) {\n          lines.push(currentWord);\n          linesWidths.push(currentWordWidth);\n          currentWord = ch;\n          currentWordWidth = chWidth;\n        } else {\n          lines.push(ch);\n          linesWidths.push(chWidth);\n        }\n      } else if (line || currentWord) {\n        if (inWord) {\n          if (!line) {\n            line = currentWord;\n            currentWord = '';\n            currentWordWidth = 0;\n            accumWidth = currentWordWidth;\n          }\n          lines.push(line);\n          linesWidths.push(accumWidth - currentWordWidth);\n          currentWord += ch;\n          currentWordWidth += chWidth;\n          line = '';\n          accumWidth = currentWordWidth;\n        } else {\n          if (currentWord) {\n            line += currentWord;\n            currentWord = '';\n            currentWordWidth = 0;\n          }\n          lines.push(line);\n          linesWidths.push(accumWidth);\n          line = ch;\n          accumWidth = chWidth;\n        }\n      }\n      continue;\n    }\n    accumWidth += chWidth;\n    if (inWord) {\n      currentWord += ch;\n      currentWordWidth += chWidth;\n    } else {\n      if (currentWord) {\n        line += currentWord;\n        currentWord = '';\n        currentWordWidth = 0;\n      }\n      line += ch;\n    }\n  }\n  if (!lines.length && !line) {\n    line = text;\n    currentWord = '';\n    currentWordWidth = 0;\n  }\n  if (currentWord) {\n    line += currentWord;\n  }\n  if (line) {\n    lines.push(line);\n    linesWidths.push(accumWidth);\n  }\n  if (lines.length === 1) {\n    accumWidth += lastAccumWidth;\n  }\n  return {\n    accumWidth: accumWidth,\n    lines: lines,\n    linesWidths: linesWidths\n  };\n}", "export function buildPath(ctx, shape) {\n  var x = shape.x;\n  var y = shape.y;\n  var width = shape.width;\n  var height = shape.height;\n  var r = shape.r;\n  var r1;\n  var r2;\n  var r3;\n  var r4;\n  if (width < 0) {\n    x = x + width;\n    width = -width;\n  }\n  if (height < 0) {\n    y = y + height;\n    height = -height;\n  }\n  if (typeof r === 'number') {\n    r1 = r2 = r3 = r4 = r;\n  } else if (r instanceof Array) {\n    if (r.length === 1) {\n      r1 = r2 = r3 = r4 = r[0];\n    } else if (r.length === 2) {\n      r1 = r3 = r[0];\n      r2 = r4 = r[1];\n    } else if (r.length === 3) {\n      r1 = r[0];\n      r2 = r4 = r[1];\n      r3 = r[2];\n    } else {\n      r1 = r[0];\n      r2 = r[1];\n      r3 = r[2];\n      r4 = r[3];\n    }\n  } else {\n    r1 = r2 = r3 = r4 = 0;\n  }\n  var total;\n  if (r1 + r2 > width) {\n    total = r1 + r2;\n    r1 *= width / total;\n    r2 *= width / total;\n  }\n  if (r3 + r4 > width) {\n    total = r3 + r4;\n    r3 *= width / total;\n    r4 *= width / total;\n  }\n  if (r2 + r3 > height) {\n    total = r2 + r3;\n    r2 *= height / total;\n    r3 *= height / total;\n  }\n  if (r1 + r4 > height) {\n    total = r1 + r4;\n    r1 *= height / total;\n    r4 *= height / total;\n  }\n  ctx.moveTo(x + r1, y);\n  ctx.lineTo(x + width - r2, y);\n  r2 !== 0 && ctx.arc(x + width - r2, y + r2, r2, -Math.PI / 2, 0);\n  ctx.lineTo(x + width, y + height - r3);\n  r3 !== 0 && ctx.arc(x + width - r3, y + height - r3, r3, 0, Math.PI / 2);\n  ctx.lineTo(x + r4, y + height);\n  r4 !== 0 && ctx.arc(x + r4, y + height - r4, r4, Math.PI / 2, Math.PI);\n  ctx.lineTo(x, y + r1);\n  r1 !== 0 && ctx.arc(x + r1, y + r1, r1, Math.PI, Math.PI * 1.5);\n}", "var round = Math.round;\nexport function subPixelOptimizeLine(outputShape, inputShape, style) {\n  if (!inputShape) {\n    return;\n  }\n  var x1 = inputShape.x1;\n  var x2 = inputShape.x2;\n  var y1 = inputShape.y1;\n  var y2 = inputShape.y2;\n  outputShape.x1 = x1;\n  outputShape.x2 = x2;\n  outputShape.y1 = y1;\n  outputShape.y2 = y2;\n  var lineWidth = style && style.lineWidth;\n  if (!lineWidth) {\n    return outputShape;\n  }\n  if (round(x1 * 2) === round(x2 * 2)) {\n    outputShape.x1 = outputShape.x2 = subPixelOptimize(x1, lineWidth, true);\n  }\n  if (round(y1 * 2) === round(y2 * 2)) {\n    outputShape.y1 = outputShape.y2 = subPixelOptimize(y1, lineWidth, true);\n  }\n  return outputShape;\n}\nexport function subPixelOptimizeRect(outputShape, inputShape, style) {\n  if (!inputShape) {\n    return;\n  }\n  var originX = inputShape.x;\n  var originY = inputShape.y;\n  var originWidth = inputShape.width;\n  var originHeight = inputShape.height;\n  outputShape.x = originX;\n  outputShape.y = originY;\n  outputShape.width = originWidth;\n  outputShape.height = originHeight;\n  var lineWidth = style && style.lineWidth;\n  if (!lineWidth) {\n    return outputShape;\n  }\n  outputShape.x = subPixelOptimize(originX, lineWidth, true);\n  outputShape.y = subPixelOptimize(originY, lineWidth, true);\n  outputShape.width = Math.max(subPixelOptimize(originX + originWidth, lineWidth, false) - outputShape.x, originWidth === 0 ? 0 : 1);\n  outputShape.height = Math.max(subPixelOptimize(originY + originHeight, lineWidth, false) - outputShape.y, originHeight === 0 ? 0 : 1);\n  return outputShape;\n}\nexport function subPixelOptimize(position, lineWidth, positiveOrNegative) {\n  if (!lineWidth) {\n    return position;\n  }\n  var doubledPosition = round(position * 2);\n  return (doubledPosition + round(lineWidth)) % 2 === 0 ? doubledPosition / 2 : (doubledPosition + (positiveOrNegative ? 1 : -1)) / 2;\n}", "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as roundRectHelper from '../helper/roundRect.js';\nimport { subPixelOptimizeRect } from '../helper/subPixelOptimize.js';\nvar RectShape = function () {\n  function RectShape() {\n    this.x = 0;\n    this.y = 0;\n    this.width = 0;\n    this.height = 0;\n  }\n  return RectShape;\n}();\nexport { RectShape };\nvar subPixelOptimizeOutputShape = {};\nvar Rect = function (_super) {\n  __extends(Rect, _super);\n  function Rect(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Rect.prototype.getDefaultShape = function () {\n    return new RectShape();\n  };\n  Rect.prototype.buildPath = function (ctx, shape) {\n    var x;\n    var y;\n    var width;\n    var height;\n    if (this.subPixelOptimize) {\n      var optimizedShape = subPixelOptimizeRect(subPixelOptimizeOutputShape, shape, this.style);\n      x = optimizedShape.x;\n      y = optimizedShape.y;\n      width = optimizedShape.width;\n      height = optimizedShape.height;\n      optimizedShape.r = shape.r;\n      shape = optimizedShape;\n    } else {\n      x = shape.x;\n      y = shape.y;\n      width = shape.width;\n      height = shape.height;\n    }\n    if (!shape.r) {\n      ctx.rect(x, y, width, height);\n    } else {\n      roundRectHelper.buildPath(ctx, shape);\n    }\n  };\n  Rect.prototype.isZeroArea = function () {\n    return !this.shape.width || !this.shape.height;\n  };\n  return Rect;\n}(Path);\nRect.prototype.type = 'rect';\nexport default Rect;", "import { __extends } from \"tslib\";\nimport { parseRichText, parsePlainText } from './helper/parseText.js';\nimport TSpan from './TSpan.js';\nimport { retrieve2, each, normalizeCssArray, trim, retrieve3, extend, keys, defaults } from '../core/util.js';\nimport { adjustTextX, adjustTextY } from '../contain/text.js';\nimport ZRImage from './Image.js';\nimport Rect from './shape/Rect.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport Displayable, { DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_SIZE } from '../core/platform.js';\nvar DEFAULT_RICH_TEXT_COLOR = {\n  fill: '#000'\n};\nvar DEFAULT_STROKE_LINE_WIDTH = 2;\nexport var DEFAULT_TEXT_ANIMATION_PROPS = {\n  style: defaults({\n    fill: true,\n    stroke: true,\n    fillOpacity: true,\n    strokeOpacity: true,\n    lineWidth: true,\n    fontSize: true,\n    lineHeight: true,\n    width: true,\n    height: true,\n    textShadowColor: true,\n    textShadowBlur: true,\n    textShadowOffsetX: true,\n    textShadowOffsetY: true,\n    backgroundColor: true,\n    padding: true,\n    borderColor: true,\n    borderWidth: true,\n    borderRadius: true\n  }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nvar ZRText = function (_super) {\n  __extends(ZRText, _super);\n  function ZRText(opts) {\n    var _this = _super.call(this) || this;\n    _this.type = 'text';\n    _this._children = [];\n    _this._defaultStyle = DEFAULT_RICH_TEXT_COLOR;\n    _this.attr(opts);\n    return _this;\n  }\n  ZRText.prototype.childrenRef = function () {\n    return this._children;\n  };\n  ZRText.prototype.update = function () {\n    _super.prototype.update.call(this);\n    if (this.styleChanged()) {\n      this._updateSubTexts();\n    }\n    for (var i = 0; i < this._children.length; i++) {\n      var child = this._children[i];\n      child.zlevel = this.zlevel;\n      child.z = this.z;\n      child.z2 = this.z2;\n      child.culling = this.culling;\n      child.cursor = this.cursor;\n      child.invisible = this.invisible;\n    }\n  };\n  ZRText.prototype.updateTransform = function () {\n    var innerTransformable = this.innerTransformable;\n    if (innerTransformable) {\n      innerTransformable.updateTransform();\n      if (innerTransformable.transform) {\n        this.transform = innerTransformable.transform;\n      }\n    } else {\n      _super.prototype.updateTransform.call(this);\n    }\n  };\n  ZRText.prototype.getLocalTransform = function (m) {\n    var innerTransformable = this.innerTransformable;\n    return innerTransformable ? innerTransformable.getLocalTransform(m) : _super.prototype.getLocalTransform.call(this, m);\n  };\n  ZRText.prototype.getComputedTransform = function () {\n    if (this.__hostTarget) {\n      this.__hostTarget.getComputedTransform();\n      this.__hostTarget.updateInnerText(true);\n    }\n    return _super.prototype.getComputedTransform.call(this);\n  };\n  ZRText.prototype._updateSubTexts = function () {\n    this._childCursor = 0;\n    normalizeTextStyle(this.style);\n    this.style.rich ? this._updateRichTexts() : this._updatePlainTexts();\n    this._children.length = this._childCursor;\n    this.styleUpdated();\n  };\n  ZRText.prototype.addSelfToZr = function (zr) {\n    _super.prototype.addSelfToZr.call(this, zr);\n    for (var i = 0; i < this._children.length; i++) {\n      this._children[i].__zr = zr;\n    }\n  };\n  ZRText.prototype.removeSelfFromZr = function (zr) {\n    _super.prototype.removeSelfFromZr.call(this, zr);\n    for (var i = 0; i < this._children.length; i++) {\n      this._children[i].__zr = null;\n    }\n  };\n  ZRText.prototype.getBoundingRect = function () {\n    if (this.styleChanged()) {\n      this._updateSubTexts();\n    }\n    if (!this._rect) {\n      var tmpRect = new BoundingRect(0, 0, 0, 0);\n      var children = this._children;\n      var tmpMat = [];\n      var rect = null;\n      for (var i = 0; i < children.length; i++) {\n        var child = children[i];\n        var childRect = child.getBoundingRect();\n        var transform = child.getLocalTransform(tmpMat);\n        if (transform) {\n          tmpRect.copy(childRect);\n          tmpRect.applyTransform(transform);\n          rect = rect || tmpRect.clone();\n          rect.union(tmpRect);\n        } else {\n          rect = rect || childRect.clone();\n          rect.union(childRect);\n        }\n      }\n      this._rect = rect || tmpRect;\n    }\n    return this._rect;\n  };\n  ZRText.prototype.setDefaultTextStyle = function (defaultTextStyle) {\n    this._defaultStyle = defaultTextStyle || DEFAULT_RICH_TEXT_COLOR;\n  };\n  ZRText.prototype.setTextContent = function (textContent) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error('Can\\'t attach text on another text');\n    }\n  };\n  ZRText.prototype._mergeStyle = function (targetStyle, sourceStyle) {\n    if (!sourceStyle) {\n      return targetStyle;\n    }\n    var sourceRich = sourceStyle.rich;\n    var targetRich = targetStyle.rich || sourceRich && {};\n    extend(targetStyle, sourceStyle);\n    if (sourceRich && targetRich) {\n      this._mergeRich(targetRich, sourceRich);\n      targetStyle.rich = targetRich;\n    } else if (targetRich) {\n      targetStyle.rich = targetRich;\n    }\n    return targetStyle;\n  };\n  ZRText.prototype._mergeRich = function (targetRich, sourceRich) {\n    var richNames = keys(sourceRich);\n    for (var i = 0; i < richNames.length; i++) {\n      var richName = richNames[i];\n      targetRich[richName] = targetRich[richName] || {};\n      extend(targetRich[richName], sourceRich[richName]);\n    }\n  };\n  ZRText.prototype.getAnimationStyleProps = function () {\n    return DEFAULT_TEXT_ANIMATION_PROPS;\n  };\n  ZRText.prototype._getOrCreateChild = function (Ctor) {\n    var child = this._children[this._childCursor];\n    if (!child || !(child instanceof Ctor)) {\n      child = new Ctor();\n    }\n    this._children[this._childCursor++] = child;\n    child.__zr = this.__zr;\n    child.parent = this;\n    return child;\n  };\n  ZRText.prototype._updatePlainTexts = function () {\n    var style = this.style;\n    var textFont = style.font || DEFAULT_FONT;\n    var textPadding = style.padding;\n    var text = getStyleText(style);\n    var contentBlock = parsePlainText(text, style);\n    var needDrawBg = needDrawBackground(style);\n    var bgColorDrawn = !!style.backgroundColor;\n    var outerHeight = contentBlock.outerHeight;\n    var outerWidth = contentBlock.outerWidth;\n    var contentWidth = contentBlock.contentWidth;\n    var textLines = contentBlock.lines;\n    var lineHeight = contentBlock.lineHeight;\n    var defaultStyle = this._defaultStyle;\n    var baseX = style.x || 0;\n    var baseY = style.y || 0;\n    var textAlign = style.align || defaultStyle.align || 'left';\n    var verticalAlign = style.verticalAlign || defaultStyle.verticalAlign || 'top';\n    var textX = baseX;\n    var textY = adjustTextY(baseY, contentBlock.contentHeight, verticalAlign);\n    if (needDrawBg || textPadding) {\n      var boxX = adjustTextX(baseX, outerWidth, textAlign);\n      var boxY = adjustTextY(baseY, outerHeight, verticalAlign);\n      needDrawBg && this._renderBackground(style, style, boxX, boxY, outerWidth, outerHeight);\n    }\n    textY += lineHeight / 2;\n    if (textPadding) {\n      textX = getTextXForPadding(baseX, textAlign, textPadding);\n      if (verticalAlign === 'top') {\n        textY += textPadding[0];\n      } else if (verticalAlign === 'bottom') {\n        textY -= textPadding[2];\n      }\n    }\n    var defaultLineWidth = 0;\n    var useDefaultFill = false;\n    var textFill = getFill('fill' in style ? style.fill : (useDefaultFill = true, defaultStyle.fill));\n    var textStroke = getStroke('stroke' in style ? style.stroke : !bgColorDrawn && (!defaultStyle.autoStroke || useDefaultFill) ? (defaultLineWidth = DEFAULT_STROKE_LINE_WIDTH, defaultStyle.stroke) : null);\n    var hasShadow = style.textShadowBlur > 0;\n    var fixedBoundingRect = style.width != null && (style.overflow === 'truncate' || style.overflow === 'break' || style.overflow === 'breakAll');\n    var calculatedLineHeight = contentBlock.calculatedLineHeight;\n    for (var i = 0; i < textLines.length; i++) {\n      var el = this._getOrCreateChild(TSpan);\n      var subElStyle = el.createStyle();\n      el.useStyle(subElStyle);\n      subElStyle.text = textLines[i];\n      subElStyle.x = textX;\n      subElStyle.y = textY;\n      if (textAlign) {\n        subElStyle.textAlign = textAlign;\n      }\n      subElStyle.textBaseline = 'middle';\n      subElStyle.opacity = style.opacity;\n      subElStyle.strokeFirst = true;\n      if (hasShadow) {\n        subElStyle.shadowBlur = style.textShadowBlur || 0;\n        subElStyle.shadowColor = style.textShadowColor || 'transparent';\n        subElStyle.shadowOffsetX = style.textShadowOffsetX || 0;\n        subElStyle.shadowOffsetY = style.textShadowOffsetY || 0;\n      }\n      subElStyle.stroke = textStroke;\n      subElStyle.fill = textFill;\n      if (textStroke) {\n        subElStyle.lineWidth = style.lineWidth || defaultLineWidth;\n        subElStyle.lineDash = style.lineDash;\n        subElStyle.lineDashOffset = style.lineDashOffset || 0;\n      }\n      subElStyle.font = textFont;\n      setSeparateFont(subElStyle, style);\n      textY += lineHeight;\n      if (fixedBoundingRect) {\n        el.setBoundingRect(new BoundingRect(adjustTextX(subElStyle.x, style.width, subElStyle.textAlign), adjustTextY(subElStyle.y, calculatedLineHeight, subElStyle.textBaseline), contentWidth, calculatedLineHeight));\n      }\n    }\n  };\n  ZRText.prototype._updateRichTexts = function () {\n    var style = this.style;\n    var text = getStyleText(style);\n    var contentBlock = parseRichText(text, style);\n    var contentWidth = contentBlock.width;\n    var outerWidth = contentBlock.outerWidth;\n    var outerHeight = contentBlock.outerHeight;\n    var textPadding = style.padding;\n    var baseX = style.x || 0;\n    var baseY = style.y || 0;\n    var defaultStyle = this._defaultStyle;\n    var textAlign = style.align || defaultStyle.align;\n    var verticalAlign = style.verticalAlign || defaultStyle.verticalAlign;\n    var boxX = adjustTextX(baseX, outerWidth, textAlign);\n    var boxY = adjustTextY(baseY, outerHeight, verticalAlign);\n    var xLeft = boxX;\n    var lineTop = boxY;\n    if (textPadding) {\n      xLeft += textPadding[3];\n      lineTop += textPadding[0];\n    }\n    var xRight = xLeft + contentWidth;\n    if (needDrawBackground(style)) {\n      this._renderBackground(style, style, boxX, boxY, outerWidth, outerHeight);\n    }\n    var bgColorDrawn = !!style.backgroundColor;\n    for (var i = 0; i < contentBlock.lines.length; i++) {\n      var line = contentBlock.lines[i];\n      var tokens = line.tokens;\n      var tokenCount = tokens.length;\n      var lineHeight = line.lineHeight;\n      var remainedWidth = line.width;\n      var leftIndex = 0;\n      var lineXLeft = xLeft;\n      var lineXRight = xRight;\n      var rightIndex = tokenCount - 1;\n      var token = void 0;\n      while (leftIndex < tokenCount && (token = tokens[leftIndex], !token.align || token.align === 'left')) {\n        this._placeToken(token, style, lineHeight, lineTop, lineXLeft, 'left', bgColorDrawn);\n        remainedWidth -= token.width;\n        lineXLeft += token.width;\n        leftIndex++;\n      }\n      while (rightIndex >= 0 && (token = tokens[rightIndex], token.align === 'right')) {\n        this._placeToken(token, style, lineHeight, lineTop, lineXRight, 'right', bgColorDrawn);\n        remainedWidth -= token.width;\n        lineXRight -= token.width;\n        rightIndex--;\n      }\n      lineXLeft += (contentWidth - (lineXLeft - xLeft) - (xRight - lineXRight) - remainedWidth) / 2;\n      while (leftIndex <= rightIndex) {\n        token = tokens[leftIndex];\n        this._placeToken(token, style, lineHeight, lineTop, lineXLeft + token.width / 2, 'center', bgColorDrawn);\n        lineXLeft += token.width;\n        leftIndex++;\n      }\n      lineTop += lineHeight;\n    }\n  };\n  ZRText.prototype._placeToken = function (token, style, lineHeight, lineTop, x, textAlign, parentBgColorDrawn) {\n    var tokenStyle = style.rich[token.styleName] || {};\n    tokenStyle.text = token.text;\n    var verticalAlign = token.verticalAlign;\n    var y = lineTop + lineHeight / 2;\n    if (verticalAlign === 'top') {\n      y = lineTop + token.height / 2;\n    } else if (verticalAlign === 'bottom') {\n      y = lineTop + lineHeight - token.height / 2;\n    }\n    var needDrawBg = !token.isLineHolder && needDrawBackground(tokenStyle);\n    needDrawBg && this._renderBackground(tokenStyle, style, textAlign === 'right' ? x - token.width : textAlign === 'center' ? x - token.width / 2 : x, y - token.height / 2, token.width, token.height);\n    var bgColorDrawn = !!tokenStyle.backgroundColor;\n    var textPadding = token.textPadding;\n    if (textPadding) {\n      x = getTextXForPadding(x, textAlign, textPadding);\n      y -= token.height / 2 - textPadding[0] - token.innerHeight / 2;\n    }\n    var el = this._getOrCreateChild(TSpan);\n    var subElStyle = el.createStyle();\n    el.useStyle(subElStyle);\n    var defaultStyle = this._defaultStyle;\n    var useDefaultFill = false;\n    var defaultLineWidth = 0;\n    var textFill = getFill('fill' in tokenStyle ? tokenStyle.fill : 'fill' in style ? style.fill : (useDefaultFill = true, defaultStyle.fill));\n    var textStroke = getStroke('stroke' in tokenStyle ? tokenStyle.stroke : 'stroke' in style ? style.stroke : !bgColorDrawn && !parentBgColorDrawn && (!defaultStyle.autoStroke || useDefaultFill) ? (defaultLineWidth = DEFAULT_STROKE_LINE_WIDTH, defaultStyle.stroke) : null);\n    var hasShadow = tokenStyle.textShadowBlur > 0 || style.textShadowBlur > 0;\n    subElStyle.text = token.text;\n    subElStyle.x = x;\n    subElStyle.y = y;\n    if (hasShadow) {\n      subElStyle.shadowBlur = tokenStyle.textShadowBlur || style.textShadowBlur || 0;\n      subElStyle.shadowColor = tokenStyle.textShadowColor || style.textShadowColor || 'transparent';\n      subElStyle.shadowOffsetX = tokenStyle.textShadowOffsetX || style.textShadowOffsetX || 0;\n      subElStyle.shadowOffsetY = tokenStyle.textShadowOffsetY || style.textShadowOffsetY || 0;\n    }\n    subElStyle.textAlign = textAlign;\n    subElStyle.textBaseline = 'middle';\n    subElStyle.font = token.font || DEFAULT_FONT;\n    subElStyle.opacity = retrieve3(tokenStyle.opacity, style.opacity, 1);\n    setSeparateFont(subElStyle, tokenStyle);\n    if (textStroke) {\n      subElStyle.lineWidth = retrieve3(tokenStyle.lineWidth, style.lineWidth, defaultLineWidth);\n      subElStyle.lineDash = retrieve2(tokenStyle.lineDash, style.lineDash);\n      subElStyle.lineDashOffset = style.lineDashOffset || 0;\n      subElStyle.stroke = textStroke;\n    }\n    if (textFill) {\n      subElStyle.fill = textFill;\n    }\n    var textWidth = token.contentWidth;\n    var textHeight = token.contentHeight;\n    el.setBoundingRect(new BoundingRect(adjustTextX(subElStyle.x, textWidth, subElStyle.textAlign), adjustTextY(subElStyle.y, textHeight, subElStyle.textBaseline), textWidth, textHeight));\n  };\n  ZRText.prototype._renderBackground = function (style, topStyle, x, y, width, height) {\n    var textBackgroundColor = style.backgroundColor;\n    var textBorderWidth = style.borderWidth;\n    var textBorderColor = style.borderColor;\n    var isImageBg = textBackgroundColor && textBackgroundColor.image;\n    var isPlainOrGradientBg = textBackgroundColor && !isImageBg;\n    var textBorderRadius = style.borderRadius;\n    var self = this;\n    var rectEl;\n    var imgEl;\n    if (isPlainOrGradientBg || style.lineHeight || textBorderWidth && textBorderColor) {\n      rectEl = this._getOrCreateChild(Rect);\n      rectEl.useStyle(rectEl.createStyle());\n      rectEl.style.fill = null;\n      var rectShape = rectEl.shape;\n      rectShape.x = x;\n      rectShape.y = y;\n      rectShape.width = width;\n      rectShape.height = height;\n      rectShape.r = textBorderRadius;\n      rectEl.dirtyShape();\n    }\n    if (isPlainOrGradientBg) {\n      var rectStyle = rectEl.style;\n      rectStyle.fill = textBackgroundColor || null;\n      rectStyle.fillOpacity = retrieve2(style.fillOpacity, 1);\n    } else if (isImageBg) {\n      imgEl = this._getOrCreateChild(ZRImage);\n      imgEl.onload = function () {\n        self.dirtyStyle();\n      };\n      var imgStyle = imgEl.style;\n      imgStyle.image = textBackgroundColor.image;\n      imgStyle.x = x;\n      imgStyle.y = y;\n      imgStyle.width = width;\n      imgStyle.height = height;\n    }\n    if (textBorderWidth && textBorderColor) {\n      var rectStyle = rectEl.style;\n      rectStyle.lineWidth = textBorderWidth;\n      rectStyle.stroke = textBorderColor;\n      rectStyle.strokeOpacity = retrieve2(style.strokeOpacity, 1);\n      rectStyle.lineDash = style.borderDash;\n      rectStyle.lineDashOffset = style.borderDashOffset || 0;\n      rectEl.strokeContainThreshold = 0;\n      if (rectEl.hasFill() && rectEl.hasStroke()) {\n        rectStyle.strokeFirst = true;\n        rectStyle.lineWidth *= 2;\n      }\n    }\n    var commonStyle = (rectEl || imgEl).style;\n    commonStyle.shadowBlur = style.shadowBlur || 0;\n    commonStyle.shadowColor = style.shadowColor || 'transparent';\n    commonStyle.shadowOffsetX = style.shadowOffsetX || 0;\n    commonStyle.shadowOffsetY = style.shadowOffsetY || 0;\n    commonStyle.opacity = retrieve3(style.opacity, topStyle.opacity, 1);\n  };\n  ZRText.makeFont = function (style) {\n    var font = '';\n    if (hasSeparateFont(style)) {\n      font = [style.fontStyle, style.fontWeight, parseFontSize(style.fontSize), style.fontFamily || 'sans-serif'].join(' ');\n    }\n    return font && trim(font) || style.textFont || style.font;\n  };\n  return ZRText;\n}(Displayable);\nvar VALID_TEXT_ALIGN = {\n  left: true,\n  right: 1,\n  center: 1\n};\nvar VALID_TEXT_VERTICAL_ALIGN = {\n  top: 1,\n  bottom: 1,\n  middle: 1\n};\nvar FONT_PARTS = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily'];\nexport function parseFontSize(fontSize) {\n  if (typeof fontSize === 'string' && (fontSize.indexOf('px') !== -1 || fontSize.indexOf('rem') !== -1 || fontSize.indexOf('em') !== -1)) {\n    return fontSize;\n  } else if (!isNaN(+fontSize)) {\n    return fontSize + 'px';\n  } else {\n    return DEFAULT_FONT_SIZE + 'px';\n  }\n}\nfunction setSeparateFont(targetStyle, sourceStyle) {\n  for (var i = 0; i < FONT_PARTS.length; i++) {\n    var fontProp = FONT_PARTS[i];\n    var val = sourceStyle[fontProp];\n    if (val != null) {\n      targetStyle[fontProp] = val;\n    }\n  }\n}\nexport function hasSeparateFont(style) {\n  return style.fontSize != null || style.fontFamily || style.fontWeight;\n}\nexport function normalizeTextStyle(style) {\n  normalizeStyle(style);\n  each(style.rich, normalizeStyle);\n  return style;\n}\nfunction normalizeStyle(style) {\n  if (style) {\n    style.font = ZRText.makeFont(style);\n    var textAlign = style.align;\n    textAlign === 'middle' && (textAlign = 'center');\n    style.align = textAlign == null || VALID_TEXT_ALIGN[textAlign] ? textAlign : 'left';\n    var verticalAlign = style.verticalAlign;\n    verticalAlign === 'center' && (verticalAlign = 'middle');\n    style.verticalAlign = verticalAlign == null || VALID_TEXT_VERTICAL_ALIGN[verticalAlign] ? verticalAlign : 'top';\n    var textPadding = style.padding;\n    if (textPadding) {\n      style.padding = normalizeCssArray(style.padding);\n    }\n  }\n}\nfunction getStroke(stroke, lineWidth) {\n  return stroke == null || lineWidth <= 0 || stroke === 'transparent' || stroke === 'none' ? null : stroke.image || stroke.colorStops ? '#000' : stroke;\n}\nfunction getFill(fill) {\n  return fill == null || fill === 'none' ? null : fill.image || fill.colorStops ? '#000' : fill;\n}\nfunction getTextXForPadding(x, textAlign, textPadding) {\n  return textAlign === 'right' ? x - textPadding[1] : textAlign === 'center' ? x + textPadding[3] / 2 - textPadding[1] / 2 : x + textPadding[3];\n}\nfunction getStyleText(style) {\n  var text = style.text;\n  text != null && (text += '');\n  return text;\n}\nfunction needDrawBackground(style) {\n  return !!(style.backgroundColor || style.lineHeight || style.borderWidth && style.borderColor);\n}\nexport default ZRText;", "import { __extends } from \"tslib\";\nimport Path from './Path.js';\nvar CompoundPath = function (_super) {\n  __extends(CompoundPath, _super);\n  function CompoundPath() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'compound';\n    return _this;\n  }\n  CompoundPath.prototype._updatePathDirty = function () {\n    var paths = this.shape.paths;\n    var dirtyPath = this.shapeChanged();\n    for (var i = 0; i < paths.length; i++) {\n      dirtyPath = dirtyPath || paths[i].shapeChanged();\n    }\n    if (dirtyPath) {\n      this.dirtyShape();\n    }\n  };\n  CompoundPath.prototype.beforeBrush = function () {\n    this._updatePathDirty();\n    var paths = this.shape.paths || [];\n    var scale = this.getGlobalScale();\n    for (var i = 0; i < paths.length; i++) {\n      if (!paths[i].path) {\n        paths[i].createPathProxy();\n      }\n      paths[i].path.setScale(scale[0], scale[1], paths[i].segmentIgnoreThreshold);\n    }\n  };\n  CompoundPath.prototype.buildPath = function (ctx, shape) {\n    var paths = shape.paths || [];\n    for (var i = 0; i < paths.length; i++) {\n      paths[i].buildPath(ctx, paths[i].shape, true);\n    }\n  };\n  CompoundPath.prototype.afterBrush = function () {\n    var paths = this.shape.paths || [];\n    for (var i = 0; i < paths.length; i++) {\n      paths[i].pathUpdated();\n    }\n  };\n  CompoundPath.prototype.getBoundingRect = function () {\n    this._updatePathDirty.call(this);\n    return Path.prototype.getBoundingRect.call(this);\n  };\n  return CompoundPath;\n}(Path);\nexport default CompoundPath;", "var LN2 = Math.log(2);\nfunction determinant(rows, rank, rowStart, rowMask, colMask, detCache) {\n  var cacheKey = rowMask + '-' + colMask;\n  var fullRank = rows.length;\n  if (detCache.hasOwnProperty(cacheKey)) {\n    return detCache[cacheKey];\n  }\n  if (rank === 1) {\n    var colStart = Math.round(Math.log((1 << fullRank) - 1 & ~colMask) / LN2);\n    return rows[rowStart][colStart];\n  }\n  var subRowMask = rowMask | 1 << rowStart;\n  var subRowStart = rowStart + 1;\n  while (rowMask & 1 << subRowStart) {\n    subRowStart++;\n  }\n  var sum = 0;\n  for (var j = 0, colLocalIdx = 0; j < fullRank; j++) {\n    var colTag = 1 << j;\n    if (!(colTag & colMask)) {\n      sum += (colLocalIdx % 2 ? -1 : 1) * rows[rowStart][j] * determinant(rows, rank - 1, subRowStart, subRowMask, colMask | colTag, detCache);\n      colLocalIdx++;\n    }\n  }\n  detCache[cacheKey] = sum;\n  return sum;\n}\nexport function buildTransformer(src, dest) {\n  var mA = [[src[0], src[1], 1, 0, 0, 0, -dest[0] * src[0], -dest[0] * src[1]], [0, 0, 0, src[0], src[1], 1, -dest[1] * src[0], -dest[1] * src[1]], [src[2], src[3], 1, 0, 0, 0, -dest[2] * src[2], -dest[2] * src[3]], [0, 0, 0, src[2], src[3], 1, -dest[3] * src[2], -dest[3] * src[3]], [src[4], src[5], 1, 0, 0, 0, -dest[4] * src[4], -dest[4] * src[5]], [0, 0, 0, src[4], src[5], 1, -dest[5] * src[4], -dest[5] * src[5]], [src[6], src[7], 1, 0, 0, 0, -dest[6] * src[6], -dest[6] * src[7]], [0, 0, 0, src[6], src[7], 1, -dest[7] * src[6], -dest[7] * src[7]]];\n  var detCache = {};\n  var det = determinant(mA, 8, 0, 0, 0, detCache);\n  if (det === 0) {\n    return;\n  }\n  var vh = [];\n  for (var i = 0; i < 8; i++) {\n    for (var j = 0; j < 8; j++) {\n      vh[j] == null && (vh[j] = 0);\n      vh[j] += ((i + j) % 2 ? -1 : 1) * determinant(mA, 7, i === 0 ? 1 : 0, 1 << i, 1 << j, detCache) / det * dest[i];\n    }\n  }\n  return function (out, srcPointX, srcPointY) {\n    var pk = srcPointX * vh[6] + srcPointY * vh[7] + 1;\n    out[0] = (srcPointX * vh[0] + srcPointY * vh[1] + vh[2]) / pk;\n    out[1] = (srcPointX * vh[3] + srcPointY * vh[4] + vh[5]) / pk;\n  };\n}", "import env from './env.js';\nimport { buildTransformer } from './fourPointsTransform.js';\nvar EVENT_SAVED_PROP = '___zrEVENTSAVED';\nvar _calcOut = [];\nexport function transformLocalCoord(out, elFrom, elTarget, inX, inY) {\n  return transformCoordWithViewport(_calcOut, elFrom, inX, inY, true) && transformCoordWithViewport(out, elTarget, _calcOut[0], _calcOut[1]);\n}\nexport function transformCoordWithViewport(out, el, inX, inY, inverse) {\n  if (el.getBoundingClientRect && env.domSupported && !isCanvasEl(el)) {\n    var saved = el[EVENT_SAVED_PROP] || (el[EVENT_SAVED_PROP] = {});\n    var markers = prepareCoordMarkers(el, saved);\n    var transformer = preparePointerTransformer(markers, saved, inverse);\n    if (transformer) {\n      transformer(out, inX, inY);\n      return true;\n    }\n  }\n  return false;\n}\nfunction prepareCoordMarkers(el, saved) {\n  var markers = saved.markers;\n  if (markers) {\n    return markers;\n  }\n  markers = saved.markers = [];\n  var propLR = ['left', 'right'];\n  var propTB = ['top', 'bottom'];\n  for (var i = 0; i < 4; i++) {\n    var marker = document.createElement('div');\n    var stl = marker.style;\n    var idxLR = i % 2;\n    var idxTB = (i >> 1) % 2;\n    stl.cssText = ['position: absolute', 'visibility: hidden', 'padding: 0', 'margin: 0', 'border-width: 0', 'user-select: none', 'width:0', 'height:0', propLR[idxLR] + ':0', propTB[idxTB] + ':0', propLR[1 - idxLR] + ':auto', propTB[1 - idxTB] + ':auto', ''].join('!important;');\n    el.appendChild(marker);\n    markers.push(marker);\n  }\n  return markers;\n}\nfunction preparePointerTransformer(markers, saved, inverse) {\n  var transformerName = inverse ? 'invTrans' : 'trans';\n  var transformer = saved[transformerName];\n  var oldSrcCoords = saved.srcCoords;\n  var srcCoords = [];\n  var destCoords = [];\n  var oldCoordTheSame = true;\n  for (var i = 0; i < 4; i++) {\n    var rect = markers[i].getBoundingClientRect();\n    var ii = 2 * i;\n    var x = rect.left;\n    var y = rect.top;\n    srcCoords.push(x, y);\n    oldCoordTheSame = oldCoordTheSame && oldSrcCoords && x === oldSrcCoords[ii] && y === oldSrcCoords[ii + 1];\n    destCoords.push(markers[i].offsetLeft, markers[i].offsetTop);\n  }\n  return oldCoordTheSame && transformer ? transformer : (saved.srcCoords = srcCoords, saved[transformerName] = inverse ? buildTransformer(destCoords, srcCoords) : buildTransformer(srcCoords, destCoords));\n}\nexport function isCanvasEl(el) {\n  return el.nodeName.toUpperCase() === 'CANVAS';\n}\nvar replaceReg = /([&<>\"'])/g;\nvar replaceMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  '\\'': '&#39;'\n};\nexport function encodeHTML(source) {\n  return source == null ? '' : (source + '').replace(replaceReg, function (str, c) {\n    return replaceMap[c];\n  });\n}", "import env from '../core/env.js';\nvar requestAnimationFrame;\nrequestAnimationFrame = env.hasGlobalWindow && (window.requestAnimationFrame && window.requestAnimationFrame.bind(window) || window.msRequestAnimationFrame && window.msRequestAnimationFrame.bind(window) || window.mozRequestAnimationFrame || window.webkitRequestAnimationFrame) || function (func) {\n  return setTimeout(func, 16);\n};\nexport default requestAnimationFrame;"], "mappings": ";;;;;AAAO,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;AAC1B,IAAI,eAAe,oBAAoB,QAAQ;AACtD,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,qBAAqB;AACzB,SAAS,gBAAgB,QAAQ;AAC/B,MAAIA,OAAM,CAAC;AACX,MAAI,OAAO,SAAS,aAAa;AAC/B,WAAOA;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,OAAO,OAAO,aAAa,IAAI,EAAE;AACrC,QAAI,QAAQ,OAAO,WAAW,CAAC,IAAI,UAAU;AAC7C,IAAAA,KAAI,IAAI,IAAI;AAAA,EACd;AACA,SAAOA;AACT;AACO,IAAI,yBAAyB,gBAAgB,kBAAkB;AAC/D,IAAI,cAAc;AAAA,EACvB,cAAc,WAAY;AACxB,WAAO,OAAO,aAAa,eAAe,SAAS,cAAc,QAAQ;AAAA,EAC3E;AAAA,EACA,aAAa,2BAAY;AACvB,QAAI;AACJ,QAAI;AACJ,WAAO,SAAU,MAAM,MAAM;AAC3B,UAAI,CAAC,MAAM;AACT,YAAI,SAAS,YAAY,aAAa;AACtC,eAAO,UAAU,OAAO,WAAW,IAAI;AAAA,MACzC;AACA,UAAI,MAAM;AACR,YAAI,gBAAgB,MAAM;AACxB,wBAAc,KAAK,OAAO,QAAQ;AAAA,QACpC;AACA,eAAO,KAAK,YAAY,IAAI;AAAA,MAC9B,OAAO;AACL,eAAO,QAAQ;AACf,eAAO,QAAQ;AACf,YAAI,MAAM,UAAU,KAAK,IAAI;AAC7B,YAAI,WAAW,OAAO,CAAC,IAAI,CAAC,KAAK;AACjC,YAAI,QAAQ;AACZ,YAAI,KAAK,QAAQ,MAAM,KAAK,GAAG;AAC7B,kBAAQ,WAAW,KAAK;AAAA,QAC1B,OAAO;AACL,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,gBAAI,eAAe,uBAAuB,KAAK,CAAC,CAAC;AACjD,qBAAS,gBAAgB,OAAO,WAAW,eAAe;AAAA,UAC5D;AAAA,QACF;AACA,eAAO;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE;AAAA,EACF,WAAW,SAAU,KAAK,QAAQ,SAAS;AACzC,QAAI,QAAQ,IAAI,MAAM;AACtB,UAAM,SAAS;AACf,UAAM,UAAU;AAChB,UAAM,MAAM;AACZ,WAAO;AAAA,EACT;AACF;AACO,SAAS,eAAe,iBAAiB;AAC9C,WAAS,OAAO,aAAa;AAC3B,QAAI,gBAAgB,GAAG,GAAG;AACxB,kBAAY,GAAG,IAAI,gBAAgB,GAAG;AAAA,IACxC;AAAA,EACF;AACF;;;ACtEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,IAAI,iBAAiB,OAAO,CAAC,YAAY,UAAU,QAAQ,SAAS,kBAAkB,iBAAiB,SAAS,QAAQ,GAAG,SAAU,KAAK,KAAK;AAC7I,MAAI,aAAa,MAAM,GAAG,IAAI;AAC9B,SAAO;AACT,GAAG,CAAC,CAAC;AACL,IAAI,cAAc,OAAO,CAAC,QAAQ,SAAS,gBAAgB,SAAS,UAAU,SAAS,UAAU,WAAW,SAAS,GAAG,SAAU,KAAK,KAAK;AAC1I,MAAI,aAAa,MAAM,QAAQ,IAAI;AACnC,SAAO;AACT,GAAG,CAAC,CAAC;AACL,IAAI,cAAc,OAAO,UAAU;AACnC,IAAI,aAAa,MAAM;AACvB,IAAI,gBAAgB,WAAW;AAC/B,IAAI,eAAe,WAAW;AAC9B,IAAI,cAAc,WAAW;AAC7B,IAAI,YAAY,WAAW;AAC3B,IAAI,eAAe,WAAY;AAAC,EAAE;AAClC,IAAI,gBAAgB,eAAe,aAAa,YAAY;AAC5D,IAAI,WAAW;AACf,IAAI,UAAU;AACP,SAAS,OAAO;AACrB,SAAO;AACT;AACO,SAAS,WAAW;AACzB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EACzB;AACA,MAAI,OAAO,YAAY,aAAa;AAClC,YAAQ,MAAM,MAAM,SAAS,IAAI;AAAA,EACnC;AACF;AACO,SAAS,MAAM,QAAQ;AAC5B,MAAI,UAAU,QAAQ,OAAO,WAAW,UAAU;AAChD,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AACb,MAAI,UAAU,YAAY,KAAK,MAAM;AACrC,MAAI,YAAY,kBAAkB;AAChC,QAAI,CAAC,YAAY,MAAM,GAAG;AACxB,eAAS,CAAC;AACV,eAAS,IAAI,GAAGC,OAAM,OAAO,QAAQ,IAAIA,MAAK,KAAK;AACjD,eAAO,CAAC,IAAI,MAAM,OAAO,CAAC,CAAC;AAAA,MAC7B;AAAA,IACF;AAAA,EACF,WAAW,YAAY,OAAO,GAAG;AAC/B,QAAI,CAAC,YAAY,MAAM,GAAG;AACxB,UAAI,OAAO,OAAO;AAClB,UAAI,KAAK,MAAM;AACb,iBAAS,KAAK,KAAK,MAAM;AAAA,MAC3B,OAAO;AACL,iBAAS,IAAI,KAAK,OAAO,MAAM;AAC/B,iBAAS,IAAI,GAAGA,OAAM,OAAO,QAAQ,IAAIA,MAAK,KAAK;AACjD,iBAAO,CAAC,IAAI,OAAO,CAAC;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAW,CAAC,eAAe,OAAO,KAAK,CAAC,YAAY,MAAM,KAAK,CAAC,MAAM,MAAM,GAAG;AAC7E,aAAS,CAAC;AACV,aAAS,OAAO,QAAQ;AACtB,UAAI,OAAO,eAAe,GAAG,KAAK,QAAQ,UAAU;AAClD,eAAO,GAAG,IAAI,MAAM,OAAO,GAAG,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,MAAM,QAAQ,QAAQ,WAAW;AAC/C,MAAI,CAAC,SAAS,MAAM,KAAK,CAAC,SAAS,MAAM,GAAG;AAC1C,WAAO,YAAY,MAAM,MAAM,IAAI;AAAA,EACrC;AACA,WAAS,OAAO,QAAQ;AACtB,QAAI,OAAO,eAAe,GAAG,KAAK,QAAQ,UAAU;AAClD,UAAI,aAAa,OAAO,GAAG;AAC3B,UAAI,aAAa,OAAO,GAAG;AAC3B,UAAI,SAAS,UAAU,KAAK,SAAS,UAAU,KAAK,CAAC,QAAQ,UAAU,KAAK,CAAC,QAAQ,UAAU,KAAK,CAAC,MAAM,UAAU,KAAK,CAAC,MAAM,UAAU,KAAK,CAAC,gBAAgB,UAAU,KAAK,CAAC,gBAAgB,UAAU,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,YAAY,UAAU,GAAG;AACpQ,cAAM,YAAY,YAAY,SAAS;AAAA,MACzC,WAAW,aAAa,EAAE,OAAO,SAAS;AACxC,eAAO,GAAG,IAAI,MAAM,OAAO,GAAG,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,SAAS,kBAAkB,WAAW;AACpD,MAAI,SAAS,iBAAiB,CAAC;AAC/B,WAAS,IAAI,GAAGA,OAAM,iBAAiB,QAAQ,IAAIA,MAAK,KAAK;AAC3D,aAAS,MAAM,QAAQ,iBAAiB,CAAC,GAAG,SAAS;AAAA,EACvD;AACA,SAAO;AACT;AACO,SAAS,OAAO,QAAQ,QAAQ;AACrC,MAAI,OAAO,QAAQ;AACjB,WAAO,OAAO,QAAQ,MAAM;AAAA,EAC9B,OAAO;AACL,aAAS,OAAO,QAAQ;AACtB,UAAI,OAAO,eAAe,GAAG,KAAK,QAAQ,UAAU;AAClD,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,SAAS,QAAQ,QAAQ,SAAS;AAChD,MAAI,UAAU,KAAK,MAAM;AACzB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,QAAI,MAAM,QAAQ,CAAC;AACnB,QAAI,UAAU,OAAO,GAAG,KAAK,OAAO,OAAO,GAAG,KAAK,MAAM;AACvD,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AACO,IAAI,eAAe,YAAY;AAC/B,SAAS,QAAQ,OAAO,OAAO;AACpC,MAAI,OAAO;AACT,QAAI,MAAM,SAAS;AACjB,aAAO,MAAM,QAAQ,KAAK;AAAA,IAC5B;AACA,aAAS,IAAI,GAAGA,OAAM,MAAM,QAAQ,IAAIA,MAAK,KAAK;AAChD,UAAI,MAAM,CAAC,MAAM,OAAO;AACtB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,SAAS,OAAO,WAAW;AACzC,MAAI,iBAAiB,MAAM;AAC3B,WAAS,IAAI;AAAA,EAAC;AACd,IAAE,YAAY,UAAU;AACxB,QAAM,YAAY,IAAI,EAAE;AACxB,WAAS,QAAQ,gBAAgB;AAC/B,QAAI,eAAe,eAAe,IAAI,GAAG;AACvC,YAAM,UAAU,IAAI,IAAI,eAAe,IAAI;AAAA,IAC7C;AAAA,EACF;AACA,QAAM,UAAU,cAAc;AAC9B,QAAM,aAAa;AACrB;AACO,SAAS,MAAM,QAAQ,QAAQ,UAAU;AAC9C,WAAS,eAAe,SAAS,OAAO,YAAY;AACpD,WAAS,eAAe,SAAS,OAAO,YAAY;AACpD,MAAI,OAAO,qBAAqB;AAC9B,QAAI,UAAU,OAAO,oBAAoB,MAAM;AAC/C,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,MAAM,QAAQ,CAAC;AACnB,UAAI,QAAQ,eAAe;AACzB,YAAI,WAAW,OAAO,GAAG,KAAK,OAAO,OAAO,GAAG,KAAK,MAAM;AACxD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,aAAS,QAAQ,QAAQ,QAAQ;AAAA,EACnC;AACF;AACO,SAAS,YAAY,MAAM;AAChC,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,SAAO,OAAO,KAAK,WAAW;AAChC;AACO,SAAS,KAAK,KAAK,IAAI,SAAS;AACrC,MAAI,EAAE,OAAO,KAAK;AAChB;AAAA,EACF;AACA,MAAI,IAAI,WAAW,IAAI,YAAY,eAAe;AAChD,QAAI,QAAQ,IAAI,OAAO;AAAA,EACzB,WAAW,IAAI,WAAW,CAAC,IAAI,QAAQ;AACrC,aAAS,IAAI,GAAGA,OAAM,IAAI,QAAQ,IAAIA,MAAK,KAAK;AAC9C,SAAG,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,IACjC;AAAA,EACF,OAAO;AACL,aAAS,OAAO,KAAK;AACnB,UAAI,IAAI,eAAe,GAAG,GAAG;AAC3B,WAAG,KAAK,SAAS,IAAI,GAAG,GAAG,KAAK,GAAG;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACF;AACO,SAAS,IAAI,KAAK,IAAI,SAAS;AACpC,MAAI,CAAC,KAAK;AACR,WAAO,CAAC;AAAA,EACV;AACA,MAAI,CAAC,IAAI;AACP,WAAO,MAAM,GAAG;AAAA,EAClB;AACA,MAAI,IAAI,OAAO,IAAI,QAAQ,WAAW;AACpC,WAAO,IAAI,IAAI,IAAI,OAAO;AAAA,EAC5B,OAAO;AACL,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAGA,OAAM,IAAI,QAAQ,IAAIA,MAAK,KAAK;AAC9C,aAAO,KAAK,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AACF;AACO,SAAS,OAAO,KAAK,IAAI,MAAM,SAAS;AAC7C,MAAI,EAAE,OAAO,KAAK;AAChB;AAAA,EACF;AACA,WAAS,IAAI,GAAGA,OAAM,IAAI,QAAQ,IAAIA,MAAK,KAAK;AAC9C,WAAO,GAAG,KAAK,SAAS,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,EAC9C;AACA,SAAO;AACT;AACO,SAAS,OAAO,KAAK,IAAI,SAAS;AACvC,MAAI,CAAC,KAAK;AACR,WAAO,CAAC;AAAA,EACV;AACA,MAAI,CAAC,IAAI;AACP,WAAO,MAAM,GAAG;AAAA,EAClB;AACA,MAAI,IAAI,UAAU,IAAI,WAAW,cAAc;AAC7C,WAAO,IAAI,OAAO,IAAI,OAAO;AAAA,EAC/B,OAAO;AACL,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAGA,OAAM,IAAI,QAAQ,IAAIA,MAAK,KAAK;AAC9C,UAAI,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;AACpC,eAAO,KAAK,IAAI,CAAC,CAAC;AAAA,MACpB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACO,SAAS,KAAK,KAAK,IAAI,SAAS;AACrC,MAAI,EAAE,OAAO,KAAK;AAChB;AAAA,EACF;AACA,WAAS,IAAI,GAAGA,OAAM,IAAI,QAAQ,IAAIA,MAAK,KAAK;AAC9C,QAAI,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;AACpC,aAAO,IAAI,CAAC;AAAA,IACd;AAAA,EACF;AACF;AACO,SAAS,KAAK,KAAK;AACxB,MAAI,CAAC,KAAK;AACR,WAAO,CAAC;AAAA,EACV;AACA,MAAI,OAAO,MAAM;AACf,WAAO,OAAO,KAAK,GAAG;AAAA,EACxB;AACA,MAAI,UAAU,CAAC;AACf,WAAS,OAAO,KAAK;AACnB,QAAI,IAAI,eAAe,GAAG,GAAG;AAC3B,cAAQ,KAAK,GAAG;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,MAAM,SAAS;AACnC,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,SAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,SAAO,WAAY;AACjB,WAAO,KAAK,MAAM,SAAS,KAAK,OAAO,YAAY,KAAK,SAAS,CAAC,CAAC;AAAA,EACrE;AACF;AACO,IAAI,OAAO,iBAAiB,WAAW,cAAc,IAAI,IAAI,cAAc,KAAK,KAAK,cAAc,IAAI,IAAI;AAClH,SAAS,MAAM,MAAM;AACnB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,SAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,SAAO,WAAY;AACjB,WAAO,KAAK,MAAM,MAAM,KAAK,OAAO,YAAY,KAAK,SAAS,CAAC,CAAC;AAAA,EAClE;AACF;AAEO,SAAS,QAAQ,OAAO;AAC7B,MAAI,MAAM,SAAS;AACjB,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC5B;AACA,SAAO,YAAY,KAAK,KAAK,MAAM;AACrC;AACO,SAAS,WAAW,OAAO;AAChC,SAAO,OAAO,UAAU;AAC1B;AACO,SAAS,SAAS,OAAO;AAC9B,SAAO,OAAO,UAAU;AAC1B;AACO,SAAS,aAAa,OAAO;AAClC,SAAO,YAAY,KAAK,KAAK,MAAM;AACrC;AACO,SAAS,SAAS,OAAO;AAC9B,SAAO,OAAO,UAAU;AAC1B;AACO,SAAS,SAAS,OAAO;AAC9B,MAAI,OAAO,OAAO;AAClB,SAAO,SAAS,cAAc,CAAC,CAAC,SAAS,SAAS;AACpD;AACO,SAAS,gBAAgB,OAAO;AACrC,SAAO,CAAC,CAAC,eAAe,YAAY,KAAK,KAAK,CAAC;AACjD;AACO,SAAS,aAAa,OAAO;AAClC,SAAO,CAAC,CAAC,YAAY,YAAY,KAAK,KAAK,CAAC;AAC9C;AACO,SAAS,MAAM,OAAO;AAC3B,SAAO,OAAO,UAAU,YAAY,OAAO,MAAM,aAAa,YAAY,OAAO,MAAM,kBAAkB;AAC3G;AACO,SAAS,iBAAiB,OAAO;AACtC,SAAO,MAAM,cAAc;AAC7B;AACO,SAAS,qBAAqB,OAAO;AAC1C,SAAO,MAAM,SAAS;AACxB;AACO,SAAS,SAAS,OAAO;AAC9B,SAAO,YAAY,KAAK,KAAK,MAAM;AACrC;AACO,SAAS,MAAM,OAAO;AAC3B,SAAO,UAAU;AACnB;AACO,SAAS,WAAW;AACzB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EACzB;AACA,WAAS,IAAI,GAAGC,OAAM,KAAK,QAAQ,IAAIA,MAAK,KAAK;AAC/C,QAAI,KAAK,CAAC,KAAK,MAAM;AACnB,aAAO,KAAK,CAAC;AAAA,IACf;AAAA,EACF;AACF;AACO,SAAS,UAAU,QAAQ,QAAQ;AACxC,SAAO,UAAU,OAAO,SAAS;AACnC;AACO,SAAS,UAAU,QAAQ,QAAQ,QAAQ;AAChD,SAAO,UAAU,OAAO,SAAS,UAAU,OAAO,SAAS;AAC7D;AACO,SAAS,MAAM,KAAK;AACzB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,SAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,SAAO,YAAY,MAAM,KAAK,IAAI;AACpC;AACO,SAAS,kBAAkB,KAAK;AACrC,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC5B;AACA,MAAIA,OAAM,IAAI;AACd,MAAIA,SAAQ,GAAG;AACb,WAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,EACxC,WAAWA,SAAQ,GAAG;AACpB,WAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,EACxC;AACA,SAAO;AACT;AACO,SAAS,OAAO,WAAW,SAAS;AACzC,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB;AACF;AACO,SAAS,KAAK,KAAK;AACxB,MAAI,OAAO,MAAM;AACf,WAAO;AAAA,EACT,WAAW,OAAO,IAAI,SAAS,YAAY;AACzC,WAAO,IAAI,KAAK;AAAA,EAClB,OAAO;AACL,WAAO,IAAI,QAAQ,sCAAsC,EAAE;AAAA,EAC7D;AACF;AACA,IAAI,eAAe;AACZ,SAAS,eAAe,KAAK;AAClC,MAAI,YAAY,IAAI;AACtB;AACO,SAAS,YAAY,KAAK;AAC/B,SAAO,IAAI,YAAY;AACzB;AACA,IAAI,cAAc,WAAY;AAC5B,WAASC,eAAc;AACrB,SAAK,OAAO,CAAC;AAAA,EACf;AACA,EAAAA,aAAY,UAAU,QAAQ,IAAI,SAAU,KAAK;AAC/C,QAAI,UAAU,KAAK,IAAI,GAAG;AAC1B,QAAI,SAAS;AACX,aAAO,KAAK,KAAK,GAAG;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,aAAY,UAAU,MAAM,SAAU,KAAK;AACzC,WAAO,KAAK,KAAK,eAAe,GAAG;AAAA,EACrC;AACA,EAAAA,aAAY,UAAU,MAAM,SAAU,KAAK;AACzC,WAAO,KAAK,KAAK,GAAG;AAAA,EACtB;AACA,EAAAA,aAAY,UAAU,MAAM,SAAU,KAAK,OAAO;AAChD,SAAK,KAAK,GAAG,IAAI;AACjB,WAAO;AAAA,EACT;AACA,EAAAA,aAAY,UAAU,OAAO,WAAY;AACvC,WAAO,KAAK,KAAK,IAAI;AAAA,EACvB;AACA,EAAAA,aAAY,UAAU,UAAU,SAAU,UAAU;AAClD,QAAI,OAAO,KAAK;AAChB,aAAS,OAAO,MAAM;AACpB,UAAI,KAAK,eAAe,GAAG,GAAG;AAC5B,iBAAS,KAAK,GAAG,GAAG,GAAG;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT,EAAE;AACF,IAAI,uBAAuB,OAAO,QAAQ;AAC1C,SAAS,iBAAiB;AACxB,SAAO,uBAAuB,oBAAI,IAAI,IAAI,IAAI,YAAY;AAC5D;AACA,IAAI,UAAU,WAAY;AACxB,WAASC,SAAQ,KAAK;AACpB,QAAI,QAAQ,QAAQ,GAAG;AACvB,SAAK,OAAO,eAAe;AAC3B,QAAI,UAAU;AACd,mBAAeA,WAAU,IAAI,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK;AACjE,aAAS,MAAM,OAAO,KAAK;AACzB,cAAQ,QAAQ,IAAI,OAAO,GAAG,IAAI,QAAQ,IAAI,KAAK,KAAK;AAAA,IAC1D;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,SAAS,SAAU,KAAK;AACxC,WAAO,KAAK,KAAK,IAAI,GAAG;AAAA,EAC1B;AACA,EAAAA,SAAQ,UAAU,MAAM,SAAU,KAAK;AACrC,WAAO,KAAK,KAAK,IAAI,GAAG;AAAA,EAC1B;AACA,EAAAA,SAAQ,UAAU,MAAM,SAAU,KAAK,OAAO;AAC5C,SAAK,KAAK,IAAI,KAAK,KAAK;AACxB,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,UAAU,OAAO,SAAU,IAAI,SAAS;AAC9C,SAAK,KAAK,QAAQ,SAAU,OAAO,KAAK;AACtC,SAAG,KAAK,SAAS,OAAO,GAAG;AAAA,IAC7B,CAAC;AAAA,EACH;AACA,EAAAA,SAAQ,UAAU,OAAO,WAAY;AACnC,QAAIC,QAAO,KAAK,KAAK,KAAK;AAC1B,WAAO,uBAAuB,MAAM,KAAKA,KAAI,IAAIA;AAAA,EACnD;AACA,EAAAD,SAAQ,UAAU,YAAY,SAAU,KAAK;AAC3C,SAAK,KAAK,QAAQ,EAAE,GAAG;AAAA,EACzB;AACA,SAAOA;AACT,EAAE;AAEK,SAAS,cAAc,KAAK;AACjC,SAAO,IAAI,QAAQ,GAAG;AACxB;AACO,SAAS,YAAY,GAAG,GAAG;AAChC,MAAI,WAAW,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM;AACpD,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,aAAS,CAAC,IAAI,EAAE,CAAC;AAAA,EACnB;AACA,MAAI,SAAS,EAAE;AACf,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,aAAS,IAAI,MAAM,IAAI,EAAE,CAAC;AAAA,EAC5B;AACA,SAAO;AACT;AACO,SAAS,aAAa,OAAO,YAAY;AAC9C,MAAI;AACJ,MAAI,OAAO,QAAQ;AACjB,UAAM,OAAO,OAAO,KAAK;AAAA,EAC3B,OAAO;AACL,QAAI,YAAY,WAAY;AAAA,IAAC;AAC7B,cAAU,YAAY;AACtB,UAAM,IAAI,UAAU;AAAA,EACtB;AACA,MAAI,YAAY;AACd,WAAO,KAAK,UAAU;AAAA,EACxB;AACA,SAAO;AACT;AACO,SAAS,kBAAkB,KAAK;AACrC,MAAI,WAAW,IAAI;AACnB,WAAS,mBAAmB;AAC5B,WAAS,aAAa;AACtB,WAAS,0BAA0B;AACnC,WAAS,uBAAuB,IAAI;AACtC;AACO,SAAS,OAAO,KAAK,MAAM;AAChC,SAAO,IAAI,eAAe,IAAI;AAChC;AACO,SAAS,OAAO;AAAC;AACjB,IAAI,mBAAmB,MAAM,KAAK;;;ACrezC,IAAI,UAAU,2BAAY;AACxB,WAASE,WAAU;AACjB,SAAK,UAAU;AACf,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AACA,SAAOA;AACT,EAAE;AACF,IAAI,MAAM,2BAAY;AACpB,WAASC,OAAM;AACb,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAC5B,SAAK,kBAAkB,OAAO,WAAW;AAAA,EAC3C;AACA,SAAOA;AACT,EAAE;AACF,IAAI,MAAM,IAAI,IAAI;AAClB,IAAI,OAAO,OAAO,YAAY,OAAO,GAAG,sBAAsB,YAAY;AACxE,MAAI,MAAM;AACV,MAAI,uBAAuB;AAC7B,WAAW,OAAO,aAAa,eAAe,OAAO,SAAS,aAAa;AACzE,MAAI,SAAS;AACf,WAAW,OAAO,cAAc,aAAa;AAC3C,MAAI,OAAO;AACX,MAAI,eAAe;AACrB,OAAO;AACL,SAAO,UAAU,WAAW,GAAG;AACjC;AACA,SAAS,OAAO,IAAIC,MAAK;AACvB,MAAI,UAAUA,KAAI;AAClB,MAAI,UAAU,GAAG,MAAM,mBAAmB;AAC1C,MAAI,KAAK,GAAG,MAAM,gBAAgB,KAAK,GAAG,MAAM,2BAA2B;AAC3E,MAAI,OAAO,GAAG,MAAM,iBAAiB;AACrC,MAAI,SAAS,kBAAkB,KAAK,EAAE;AACtC,MAAI,SAAS;AACX,YAAQ,UAAU;AAClB,YAAQ,UAAU,QAAQ,CAAC;AAAA,EAC7B;AACA,MAAI,IAAI;AACN,YAAQ,KAAK;AACb,YAAQ,UAAU,GAAG,CAAC;AAAA,EACxB;AACA,MAAI,MAAM;AACR,YAAQ,OAAO;AACf,YAAQ,UAAU,KAAK,CAAC;AACxB,YAAQ,UAAU,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,EAC7C;AACA,MAAI,QAAQ;AACV,YAAQ,SAAS;AAAA,EACnB;AACA,EAAAA,KAAI,eAAe,OAAO,YAAY;AACtC,EAAAA,KAAI,uBAAuB,kBAAkB,UAAU,CAAC,QAAQ,MAAM,CAAC,QAAQ;AAC/E,EAAAA,KAAI,yBAAyB,mBAAmB,WAAW,QAAQ,QAAQ,QAAQ,MAAM,CAAC,QAAQ,WAAW;AAC7G,EAAAA,KAAI,eAAe,OAAO,aAAa;AACvC,MAAI,QAAQ,SAAS,gBAAgB;AACrC,EAAAA,KAAI,wBAAwB,QAAQ,MAAM,gBAAgB,SAAS,QAAQ,QAAQ,qBAAqB,UAAU,SAAS,IAAI,gBAAgB,KAAK,oBAAoB,UAAU,EAAE,iBAAiB;AACrM,EAAAA,KAAI,qBAAqBA,KAAI,wBAAwB,QAAQ,MAAM,CAAC,QAAQ,WAAW;AACzF;AACA,IAAO,cAAQ;;;ACpEf;AAAA;AAAA,eAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,SAAS,SAAS;AACvB,SAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC1B;AACO,SAAS,SAAS,KAAK;AAC5B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AACO,SAAS,KAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC/B,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACvC,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACvC,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACvC,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACvC,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AAC/C,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AAC/C,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AACO,SAAS,UAAU,KAAK,GAAG,GAAG;AACnC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AACO,SAAS,OAAO,KAAK,GAAG,KAAK;AAClC,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,KAAK,KAAK,IAAI,GAAG;AACrB,MAAI,KAAK,KAAK,IAAI,GAAG;AACrB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK;AACzB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK;AACzB,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK;AACzB,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK;AACzB,SAAO;AACT;AACO,SAAS,MAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACT;AACO,SAAS,OAAO,KAAK,GAAG;AAC7B,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,KAAK,KAAK,KAAK;AACzB,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,QAAM,IAAM;AACZ,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,KAAK,KAAK,MAAM,KAAK,OAAO;AACjC,MAAI,CAAC,KAAK,KAAK,MAAM,KAAK,OAAO;AACjC,SAAO;AACT;AACO,SAASA,OAAM,GAAG;AACvB,MAAI,IAAI,OAAO;AACf,OAAK,GAAG,CAAC;AACT,SAAO;AACT;;;ACjGA;AAAA;AAAA;AAAA;AAAA,eAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAAC;AAAA,EAAA;AAAA;AAAA,eAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAO,SAASF,QAAO,GAAG,GAAG;AAC3B,MAAI,KAAK,MAAM;AACb,QAAI;AAAA,EACN;AACA,MAAI,KAAK,MAAM;AACb,QAAI;AAAA,EACN;AACA,SAAO,CAAC,GAAG,CAAC;AACd;AACO,SAASD,MAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AACO,SAASD,OAAM,GAAG;AACvB,SAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpB;AACO,SAAS,IAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC/B,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,SAAO;AACT;AACO,SAAS,YAAY,KAAK,IAAI,IAAI,GAAG;AAC1C,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI;AACzB,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI;AACzB,SAAO;AACT;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC/B,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,SAAO;AACT;AACO,SAAS,IAAI,GAAG;AACrB,SAAO,KAAK,KAAK,UAAU,CAAC,CAAC;AAC/B;AACO,IAAI,SAAS;AACb,SAAS,UAAU,GAAG;AAC3B,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjC;AACO,IAAI,eAAe;AACnB,SAASG,KAAI,KAAK,IAAI,IAAI;AAC/B,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,SAAO;AACT;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC/B,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,SAAO;AACT;AACO,SAAS,IAAI,IAAI,IAAI;AAC1B,SAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrC;AACO,SAASC,OAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACT;AACO,SAAS,UAAU,KAAK,GAAG;AAChC,MAAI,IAAI,IAAI,CAAC;AACb,MAAI,MAAM,GAAG;AACX,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACX,OAAO;AACL,QAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,QAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAAA,EAClB;AACA,SAAO;AACT;AACO,SAAS,SAAS,IAAI,IAAI;AAC/B,SAAO,KAAK,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE;AACxF;AACO,IAAI,OAAO;AACX,SAAS,eAAe,IAAI,IAAI;AACrC,UAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAC5E;AACO,IAAI,aAAa;AACjB,SAAS,OAAO,KAAK,GAAG;AAC7B,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,SAAO;AACT;AACO,SAAS,KAAK,KAAK,IAAI,IAAI,GAAG;AACnC,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC;AAClC,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC;AAClC,SAAO;AACT;AACO,SAAS,eAAe,KAAK,GAAG,GAAG;AACxC,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,SAAO;AACT;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC/B,MAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,MAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,SAAO;AACT;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC/B,MAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,MAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,SAAO;AACT;;;AC5GA;AAAA;AAAA;AAAA;AAAA,cAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAI,QAAQ,2BAAY;AACtB,WAASC,OAAM,KAAK;AAClB,SAAK,QAAQ;AAAA,EACf;AACA,SAAOA;AACT,EAAE;AAEF,IAAI,aAAa,WAAY;AAC3B,WAASC,cAAa;AACpB,SAAK,OAAO;AAAA,EACd;AACA,EAAAA,YAAW,UAAU,SAAS,SAAU,KAAK;AAC3C,QAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,SAAK,YAAY,KAAK;AACtB,WAAO;AAAA,EACT;AACA,EAAAA,YAAW,UAAU,cAAc,SAAU,OAAO;AAClD,QAAI,CAAC,KAAK,MAAM;AACd,WAAK,OAAO,KAAK,OAAO;AAAA,IAC1B,OAAO;AACL,WAAK,KAAK,OAAO;AACjB,YAAM,OAAO,KAAK;AAClB,YAAM,OAAO;AACb,WAAK,OAAO;AAAA,IACd;AACA,SAAK;AAAA,EACP;AACA,EAAAA,YAAW,UAAU,SAAS,SAAU,OAAO;AAC7C,QAAI,OAAO,MAAM;AACjB,QAAI,OAAO,MAAM;AACjB,QAAI,MAAM;AACR,WAAK,OAAO;AAAA,IACd,OAAO;AACL,WAAK,OAAO;AAAA,IACd;AACA,QAAI,MAAM;AACR,WAAK,OAAO;AAAA,IACd,OAAO;AACL,WAAK,OAAO;AAAA,IACd;AACA,UAAM,OAAO,MAAM,OAAO;AAC1B,SAAK;AAAA,EACP;AACA,EAAAA,YAAW,UAAU,MAAM,WAAY;AACrC,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,YAAW,UAAU,QAAQ,WAAY;AACvC,SAAK,OAAO,KAAK,OAAO;AACxB,SAAK,OAAO;AAAA,EACd;AACA,SAAOA;AACT,EAAE;AAEF,IAAI,MAAM,WAAY;AACpB,WAASC,KAAI,SAAS;AACpB,SAAK,QAAQ,IAAI,WAAW;AAC5B,SAAK,WAAW;AAChB,SAAK,OAAO,CAAC;AACb,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,KAAI,UAAU,MAAM,SAAU,KAAK,OAAO;AACxC,QAAI,OAAO,KAAK;AAChB,QAAIC,OAAM,KAAK;AACf,QAAI,UAAU;AACd,QAAIA,KAAI,GAAG,KAAK,MAAM;AACpB,UAAIC,OAAM,KAAK,IAAI;AACnB,UAAI,QAAQ,KAAK;AACjB,UAAIA,QAAO,KAAK,YAAYA,OAAM,GAAG;AACnC,YAAI,iBAAiB,KAAK;AAC1B,aAAK,OAAO,cAAc;AAC1B,eAAOD,KAAI,eAAe,GAAG;AAC7B,kBAAU,eAAe;AACzB,aAAK,oBAAoB;AAAA,MAC3B;AACA,UAAI,OAAO;AACT,cAAM,QAAQ;AAAA,MAChB,OAAO;AACL,gBAAQ,IAAI,MAAM,KAAK;AAAA,MACzB;AACA,YAAM,MAAM;AACZ,WAAK,YAAY,KAAK;AACtB,MAAAA,KAAI,GAAG,IAAI;AAAA,IACb;AACA,WAAO;AAAA,EACT;AACA,EAAAD,KAAI,UAAU,MAAM,SAAU,KAAK;AACjC,QAAI,QAAQ,KAAK,KAAK,GAAG;AACzB,QAAI,OAAO,KAAK;AAChB,QAAI,SAAS,MAAM;AACjB,UAAI,UAAU,KAAK,MAAM;AACvB,aAAK,OAAO,KAAK;AACjB,aAAK,YAAY,KAAK;AAAA,MACxB;AACA,aAAO,MAAM;AAAA,IACf;AAAA,EACF;AACA,EAAAA,KAAI,UAAU,QAAQ,WAAY;AAChC,SAAK,MAAM,MAAM;AACjB,SAAK,OAAO,CAAC;AAAA,EACf;AACA,EAAAA,KAAI,UAAU,MAAM,WAAY;AAC9B,WAAO,KAAK,MAAM,IAAI;AAAA,EACxB;AACA,SAAOA;AACT,EAAE;AACF,IAAO,cAAQ;;;ADxGf,IAAI,iBAAiB;AAAA,EACnB,eAAe,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,gBAAgB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACjC,QAAQ,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EACvB,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/B,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1B,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1B,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC3B,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,EACpB,kBAAkB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACnC,QAAQ,CAAC,GAAG,GAAG,KAAK,CAAC;AAAA,EACrB,cAAc,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EAC9B,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EACxB,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAC7B,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EAC7B,aAAa,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EAC7B,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EACzB,kBAAkB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACnC,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC7B,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAC1B,QAAQ,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EACvB,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC;AAAA,EACzB,YAAY,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EAC3B,iBAAiB,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EACjC,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC7B,aAAa,CAAC,GAAG,KAAK,GAAG,CAAC;AAAA,EAC1B,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC7B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EAC9B,kBAAkB,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,EACjC,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EAC7B,cAAc,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EAC9B,WAAW,CAAC,KAAK,GAAG,GAAG,CAAC;AAAA,EACxB,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/B,gBAAgB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACjC,iBAAiB,CAAC,IAAI,IAAI,KAAK,CAAC;AAAA,EAChC,iBAAiB,CAAC,IAAI,IAAI,IAAI,CAAC;AAAA,EAC/B,iBAAiB,CAAC,IAAI,IAAI,IAAI,CAAC;AAAA,EAC/B,iBAAiB,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EAChC,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EAC7B,YAAY,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EAC5B,eAAe,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EAC9B,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC5B,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC5B,cAAc,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAC9B,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAC5B,eAAe,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChC,eAAe,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,EAC9B,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EAC1B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/B,QAAQ,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EACvB,aAAa,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EAC7B,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACzB,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;AAAA,EACtB,eAAe,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EAC/B,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACzB,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC7B,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC5B,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAC5B,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC;AAAA,EACxB,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1B,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1B,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC7B,iBAAiB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAClC,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EAC5B,gBAAgB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACjC,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,wBAAwB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACzC,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,eAAe,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChC,iBAAiB,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EACjC,gBAAgB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACjC,kBAAkB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACnC,kBAAkB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACnC,kBAAkB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACnC,eAAe,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChC,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AAAA,EACrB,aAAa,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,EAC5B,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1B,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EAC1B,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;AAAA,EACvB,oBAAoB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACrC,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC;AAAA,EAC3B,gBAAgB,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EAChC,gBAAgB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACjC,kBAAkB,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAClC,mBAAmB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACpC,qBAAqB,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EACpC,mBAAmB,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EACnC,mBAAmB,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EACnC,gBAAgB,CAAC,IAAI,IAAI,KAAK,CAAC;AAAA,EAC/B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC7B,eAAe,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChC,QAAQ,CAAC,GAAG,GAAG,KAAK,CAAC;AAAA,EACrB,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC5B,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EACxB,aAAa,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EAC7B,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EACzB,aAAa,CAAC,KAAK,IAAI,GAAG,CAAC;AAAA,EAC3B,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC3B,iBAAiB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAClC,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,iBAAiB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAClC,iBAAiB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAClC,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EACxB,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACzB,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACzB,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/B,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC;AAAA,EACpB,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAC7B,eAAe,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAC9B,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC3B,cAAc,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EAC9B,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,EAC3B,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC7B,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EACzB,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC3B,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC5B,aAAa,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EAC7B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC9B,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACzB,eAAe,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EAC9B,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAC7B,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACxB,QAAQ,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EACvB,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC5B,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EACzB,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAC7B,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC3B,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1B,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1B,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/B,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EACzB,eAAe,CAAC,KAAK,KAAK,IAAI,CAAC;AACjC;AACA,SAAS,aAAa,GAAG;AACvB,MAAI,KAAK,MAAM,CAAC;AAChB,SAAO,IAAI,IAAI,IAAI,IAAI,MAAM,MAAM;AACrC;AACA,SAAS,cAAc,GAAG;AACxB,MAAI,KAAK,MAAM,CAAC;AAChB,SAAO,IAAI,IAAI,IAAI,IAAI,MAAM,MAAM;AACrC;AACA,SAAS,cAAc,GAAG;AACxB,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACjC;AACA,SAAS,YAAY,KAAK;AACxB,MAAI,MAAM;AACV,MAAI,IAAI,UAAU,IAAI,OAAO,IAAI,SAAS,CAAC,MAAM,KAAK;AACpD,WAAO,aAAa,WAAW,GAAG,IAAI,MAAM,GAAG;AAAA,EACjD;AACA,SAAO,aAAa,SAAS,KAAK,EAAE,CAAC;AACvC;AACA,SAAS,cAAc,KAAK;AAC1B,MAAI,MAAM;AACV,MAAI,IAAI,UAAU,IAAI,OAAO,IAAI,SAAS,CAAC,MAAM,KAAK;AACpD,WAAO,cAAc,WAAW,GAAG,IAAI,GAAG;AAAA,EAC5C;AACA,SAAO,cAAc,WAAW,GAAG,CAAC;AACtC;AACA,SAAS,YAAY,IAAI,IAAI,GAAG;AAC9B,MAAI,IAAI,GAAG;AACT,SAAK;AAAA,EACP,WAAW,IAAI,GAAG;AAChB,SAAK;AAAA,EACP;AACA,MAAI,IAAI,IAAI,GAAG;AACb,WAAO,MAAM,KAAK,MAAM,IAAI;AAAA,EAC9B;AACA,MAAI,IAAI,IAAI,GAAG;AACb,WAAO;AAAA,EACT;AACA,MAAI,IAAI,IAAI,GAAG;AACb,WAAO,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK;AAAA,EACxC;AACA,SAAO;AACT;AACA,SAAS,WAAW,GAAG,GAAG,GAAG;AAC3B,SAAO,KAAK,IAAI,KAAK;AACvB;AACA,SAAS,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAG;AAChC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AACA,SAAS,SAAS,KAAK,GAAG;AACxB,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AACA,IAAI,aAAa,IAAI,YAAI,EAAE;AAC3B,IAAI,iBAAiB;AACrB,SAAS,WAAW,UAAU,SAAS;AACrC,MAAI,gBAAgB;AAClB,aAAS,gBAAgB,OAAO;AAAA,EAClC;AACA,mBAAiB,WAAW,IAAI,UAAU,kBAAkB,QAAQ,MAAM,CAAC;AAC7E;AACO,SAAS,MAAM,UAAU,SAAS;AACvC,MAAI,CAAC,UAAU;AACb;AAAA,EACF;AACA,YAAU,WAAW,CAAC;AACtB,MAAI,SAAS,WAAW,IAAI,QAAQ;AACpC,MAAI,QAAQ;AACV,WAAO,SAAS,SAAS,MAAM;AAAA,EACjC;AACA,aAAW,WAAW;AACtB,MAAI,MAAM,SAAS,QAAQ,MAAM,EAAE,EAAE,YAAY;AACjD,MAAI,OAAO,gBAAgB;AACzB,aAAS,SAAS,eAAe,GAAG,CAAC;AACrC,eAAW,UAAU,OAAO;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,SAAS,IAAI;AACjB,MAAI,IAAI,OAAO,CAAC,MAAM,KAAK;AACzB,QAAI,WAAW,KAAK,WAAW,GAAG;AAChC,UAAI,KAAK,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AACrC,UAAI,EAAE,MAAM,KAAK,MAAM,OAAQ;AAC7B,gBAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AAAA,MACF;AACA,cAAQ,UAAU,KAAK,SAAU,KAAK,KAAK,SAAU,GAAG,KAAK,OAAQ,KAAK,QAAS,GAAG,KAAK,MAAO,KAAK,OAAQ,GAAG,WAAW,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,KAAM,CAAC;AACrK,iBAAW,UAAU,OAAO;AAC5B,aAAO;AAAA,IACT,WAAW,WAAW,KAAK,WAAW,GAAG;AACvC,UAAI,KAAK,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AACrC,UAAI,EAAE,MAAM,KAAK,MAAM,WAAW;AAChC,gBAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AAAA,MACF;AACA,cAAQ,UAAU,KAAK,aAAa,KAAK,KAAK,UAAW,GAAG,KAAK,KAAM,WAAW,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,MAAO,CAAC;AAC3H,iBAAW,UAAU,OAAO;AAC5B,aAAO;AAAA,IACT;AACA;AAAA,EACF;AACA,MAAI,KAAK,IAAI,QAAQ,GAAG;AACxB,MAAI,KAAK,IAAI,QAAQ,GAAG;AACxB,MAAI,OAAO,MAAM,KAAK,MAAM,QAAQ;AAClC,QAAI,QAAQ,IAAI,OAAO,GAAG,EAAE;AAC5B,QAAI,SAAS,IAAI,OAAO,KAAK,GAAG,MAAM,KAAK,EAAE,EAAE,MAAM,GAAG;AACxD,QAAI,QAAQ;AACZ,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,YAAI,OAAO,WAAW,GAAG;AACvB,iBAAO,OAAO,WAAW,IAAI,QAAQ,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,QAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,QACpH;AACA,gBAAQ,cAAc,OAAO,IAAI,CAAC;AAAA,MACpC,KAAK;AACH,YAAI,OAAO,UAAU,GAAG;AACtB,kBAAQ,SAAS,YAAY,OAAO,CAAC,CAAC,GAAG,YAAY,OAAO,CAAC,CAAC,GAAG,YAAY,OAAO,CAAC,CAAC,GAAG,OAAO,WAAW,IAAI,QAAQ,cAAc,OAAO,CAAC,CAAC,CAAC;AAC/I,qBAAW,UAAU,OAAO;AAC5B,iBAAO;AAAA,QACT,OAAO;AACL,kBAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,OAAO,WAAW,GAAG;AACvB,kBAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AAAA,QACF;AACA,eAAO,CAAC,IAAI,cAAc,OAAO,CAAC,CAAC;AACnC,kBAAU,QAAQ,OAAO;AACzB,mBAAW,UAAU,OAAO;AAC5B,eAAO;AAAA,MACT,KAAK;AACH,YAAI,OAAO,WAAW,GAAG;AACvB,kBAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AAAA,QACF;AACA,kBAAU,QAAQ,OAAO;AACzB,mBAAW,UAAU,OAAO;AAC5B,eAAO;AAAA,MACT;AACE;AAAA,IACJ;AAAA,EACF;AACA,UAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AACF;AACA,SAAS,UAAU,MAAM,MAAM;AAC7B,MAAI,KAAK,WAAW,KAAK,CAAC,CAAC,IAAI,MAAM,OAAO,MAAM;AAClD,MAAI,IAAI,cAAc,KAAK,CAAC,CAAC;AAC7B,MAAI,IAAI,cAAc,KAAK,CAAC,CAAC;AAC7B,MAAI,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC9C,MAAI,KAAK,IAAI,IAAI;AACjB,SAAO,QAAQ,CAAC;AAChB,UAAQ,MAAM,aAAa,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,aAAa,YAAY,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,aAAa,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AACnK,MAAI,KAAK,WAAW,GAAG;AACrB,SAAK,CAAC,IAAI,KAAK,CAAC;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AACA,MAAI,IAAI,KAAK,CAAC,IAAI;AAClB,MAAI,IAAI,KAAK,CAAC,IAAI;AAClB,MAAI,IAAI,KAAK,CAAC,IAAI;AAClB,MAAI,OAAO,KAAK,IAAI,GAAG,GAAG,CAAC;AAC3B,MAAI,OAAO,KAAK,IAAI,GAAG,GAAG,CAAC;AAC3B,MAAI,QAAQ,OAAO;AACnB,MAAI,KAAK,OAAO,QAAQ;AACxB,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,GAAG;AACf,QAAI;AACJ,QAAI;AAAA,EACN,OAAO;AACL,QAAI,IAAI,KAAK;AACX,UAAI,SAAS,OAAO;AAAA,IACtB,OAAO;AACL,UAAI,SAAS,IAAI,OAAO;AAAA,IAC1B;AACA,QAAI,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK;AAC5C,QAAI,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK;AAC5C,QAAI,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK;AAC5C,QAAI,MAAM,MAAM;AACd,UAAI,SAAS;AAAA,IACf,WAAW,MAAM,MAAM;AACrB,UAAI,IAAI,IAAI,SAAS;AAAA,IACvB,WAAW,MAAM,MAAM;AACrB,UAAI,IAAI,IAAI,SAAS;AAAA,IACvB;AACA,QAAI,IAAI,GAAG;AACT,WAAK;AAAA,IACP;AACA,QAAI,IAAI,GAAG;AACT,WAAK;AAAA,IACP;AAAA,EACF;AACA,MAAI,OAAO,CAAC,IAAI,KAAK,GAAG,CAAC;AACzB,MAAI,KAAK,CAAC,KAAK,MAAM;AACnB,SAAK,KAAK,KAAK,CAAC,CAAC;AAAA,EACnB;AACA,SAAO;AACT;AACO,SAAS,KAAK,OAAO,OAAO;AACjC,MAAI,WAAW,MAAM,KAAK;AAC1B,MAAI,UAAU;AACZ,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAI,QAAQ,GAAG;AACb,iBAAS,CAAC,IAAI,SAAS,CAAC,KAAK,IAAI,SAAS;AAAA,MAC5C,OAAO;AACL,iBAAS,CAAC,KAAK,MAAM,SAAS,CAAC,KAAK,QAAQ,SAAS,CAAC,IAAI;AAAA,MAC5D;AACA,UAAI,SAAS,CAAC,IAAI,KAAK;AACrB,iBAAS,CAAC,IAAI;AAAA,MAChB,WAAW,SAAS,CAAC,IAAI,GAAG;AAC1B,iBAAS,CAAC,IAAI;AAAA,MAChB;AAAA,IACF;AACA,WAAO,UAAU,UAAU,SAAS,WAAW,IAAI,SAAS,KAAK;AAAA,EACnE;AACF;AACO,SAAS,MAAM,OAAO;AAC3B,MAAI,WAAW,MAAM,KAAK;AAC1B,MAAI,UAAU;AACZ,aAAS,KAAK,OAAO,SAAS,CAAC,KAAK,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS,EAAE,EAAE,MAAM,CAAC;AAAA,EACnG;AACF;AACO,SAAS,SAAS,iBAAiB,QAAQ,KAAK;AACrD,MAAI,EAAE,UAAU,OAAO,WAAW,EAAE,mBAAmB,KAAK,mBAAmB,IAAI;AACjF;AAAA,EACF;AACA,QAAM,OAAO,CAAC;AACd,MAAI,QAAQ,mBAAmB,OAAO,SAAS;AAC/C,MAAI,YAAY,KAAK,MAAM,KAAK;AAChC,MAAI,aAAa,KAAK,KAAK,KAAK;AAChC,MAAI,YAAY,OAAO,SAAS;AAChC,MAAI,aAAa,OAAO,UAAU;AAClC,MAAI,KAAK,QAAQ;AACjB,MAAI,CAAC,IAAI,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AACjE,MAAI,CAAC,IAAI,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AACjE,MAAI,CAAC,IAAI,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AACjE,MAAI,CAAC,IAAI,cAAc,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAClE,SAAO;AACT;AACO,IAAI,iBAAiB;AACrB,SAASG,MAAK,iBAAiB,QAAQ,YAAY;AACxD,MAAI,EAAE,UAAU,OAAO,WAAW,EAAE,mBAAmB,KAAK,mBAAmB,IAAI;AACjF;AAAA,EACF;AACA,MAAI,QAAQ,mBAAmB,OAAO,SAAS;AAC/C,MAAI,YAAY,KAAK,MAAM,KAAK;AAChC,MAAI,aAAa,KAAK,KAAK,KAAK;AAChC,MAAI,YAAY,MAAM,OAAO,SAAS,CAAC;AACvC,MAAI,aAAa,MAAM,OAAO,UAAU,CAAC;AACzC,MAAI,KAAK,QAAQ;AACjB,MAAI,QAAQ,UAAU,CAAC,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,cAAc,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM;AAC3Q,SAAO,aAAa;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACN;AACO,IAAI,aAAaA;AACjB,SAAS,UAAU,OAAO,GAAG,GAAG,GAAG;AACxC,MAAI,WAAW,MAAM,KAAK;AAC1B,MAAI,OAAO;AACT,eAAW,UAAU,QAAQ;AAC7B,SAAK,SAAS,SAAS,CAAC,IAAI,cAAc,CAAC;AAC3C,SAAK,SAAS,SAAS,CAAC,IAAI,cAAc,CAAC;AAC3C,SAAK,SAAS,SAAS,CAAC,IAAI,cAAc,CAAC;AAC3C,WAAO,UAAU,UAAU,QAAQ,GAAG,MAAM;AAAA,EAC9C;AACF;AACO,SAAS,YAAY,OAAO,OAAO;AACxC,MAAI,WAAW,MAAM,KAAK;AAC1B,MAAI,YAAY,SAAS,MAAM;AAC7B,aAAS,CAAC,IAAI,cAAc,KAAK;AACjC,WAAO,UAAU,UAAU,MAAM;AAAA,EACnC;AACF;AACO,SAAS,UAAU,UAAU,MAAM;AACxC,MAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;AACjC;AAAA,EACF;AACA,MAAI,WAAW,SAAS,CAAC,IAAI,MAAM,SAAS,CAAC,IAAI,MAAM,SAAS,CAAC;AACjE,MAAI,SAAS,UAAU,SAAS,UAAU,SAAS,QAAQ;AACzD,gBAAY,MAAM,SAAS,CAAC;AAAA,EAC9B;AACA,SAAO,OAAO,MAAM,WAAW;AACjC;AACO,SAAS,IAAI,OAAO,eAAe;AACxC,MAAI,MAAM,MAAM,KAAK;AACrB,SAAO,OAAO,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,KAAK,gBAAgB;AAClH;AACO,SAAS,SAAS;AACvB,SAAO,UAAU,CAAC,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,GAAG,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,GAAG,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,CAAC,GAAG,KAAK;AAC7H;;;AEtbA,IAAI,gBAAgB,SAAU,GAAG,GAAG;AAClC,kBAAgB,OAAO,kBAAkB;AAAA,IACvC,WAAW,CAAC;AAAA,EACd,aAAa,SAAS,SAAUC,IAAGC,IAAG;AACpC,IAAAD,GAAE,YAAYC;AAAA,EAChB,KAAK,SAAUD,IAAGC,IAAG;AACnB,aAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAC7E;AACA,SAAO,cAAc,GAAG,CAAC;AAC3B;AACO,SAAS,UAAU,GAAG,GAAG;AAC9B,MAAI,OAAO,MAAM,cAAc,MAAM,KAAM,OAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AACnI,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AACZ,SAAK,cAAc;AAAA,EACrB;AACA,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACpF;;;AC/BA,IAAI,YAAmB;AACvB,IAAI,UAAU;AACd,SAAS,gBAAgB,KAAK;AAC5B,SAAO,MAAM,WAAW,MAAM,CAAC;AACjC;AACA,IAAI,WAAW,CAAC;AAChB,IAAI,eAAe,CAAC;AACpB,IAAI,kBAAyB,OAAO;AACpC,IAAI,MAAM,KAAK;AACf,IAAI,gBAAgB,WAAY;AAC9B,WAASC,iBAAgB;AAAA,EAAC;AAC1B,EAAAA,eAAc,UAAU,oBAAoB,SAAU,GAAG;AACvD,WAAOA,eAAc,kBAAkB,MAAM,CAAC;AAAA,EAChD;AACA,EAAAA,eAAc,UAAU,cAAc,SAAU,KAAK;AACnD,SAAK,IAAI,IAAI,CAAC;AACd,SAAK,IAAI,IAAI,CAAC;AAAA,EAChB;AACA,EAAAA,eAAc,UAAU,WAAW,SAAU,KAAK;AAChD,SAAK,SAAS,IAAI,CAAC;AACnB,SAAK,SAAS,IAAI,CAAC;AAAA,EACrB;AACA,EAAAA,eAAc,UAAU,UAAU,SAAU,KAAK;AAC/C,SAAK,QAAQ,IAAI,CAAC;AAClB,SAAK,QAAQ,IAAI,CAAC;AAAA,EACpB;AACA,EAAAA,eAAc,UAAU,YAAY,SAAU,KAAK;AACjD,SAAK,UAAU,IAAI,CAAC;AACpB,SAAK,UAAU,IAAI,CAAC;AAAA,EACtB;AACA,EAAAA,eAAc,UAAU,qBAAqB,WAAY;AACvD,WAAO,gBAAgB,KAAK,QAAQ,KAAK,gBAAgB,KAAK,CAAC,KAAK,gBAAgB,KAAK,CAAC,KAAK,gBAAgB,KAAK,SAAS,CAAC,KAAK,gBAAgB,KAAK,SAAS,CAAC,KAAK,gBAAgB,KAAK,KAAK,KAAK,gBAAgB,KAAK,KAAK;AAAA,EAClO;AACA,EAAAA,eAAc,UAAU,kBAAkB,WAAY;AACpD,QAAI,kBAAkB,KAAK,UAAU,KAAK,OAAO;AACjD,QAAI,qBAAqB,KAAK,mBAAmB;AACjD,QAAI,IAAI,KAAK;AACb,QAAI,EAAE,sBAAsB,kBAAkB;AAC5C,UAAI,GAAG;AACL,kBAAU,CAAC;AACX,aAAK,eAAe;AAAA,MACtB;AACA;AAAA,IACF;AACA,QAAI,KAAY,OAAO;AACvB,QAAI,oBAAoB;AACtB,WAAK,kBAAkB,CAAC;AAAA,IAC1B,OAAO;AACL,gBAAU,CAAC;AAAA,IACb;AACA,QAAI,iBAAiB;AACnB,UAAI,oBAAoB;AACtB,QAAO,IAAI,GAAG,iBAAiB,CAAC;AAAA,MAClC,OAAO;AACL,QAAO,KAAK,GAAG,eAAe;AAAA,MAChC;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,yBAAyB,CAAC;AAAA,EACjC;AACA,EAAAA,eAAc,UAAU,2BAA2B,SAAU,GAAG;AAC9D,QAAI,mBAAmB,KAAK;AAC5B,QAAI,oBAAoB,QAAQ,qBAAqB,GAAG;AACtD,WAAK,eAAe,QAAQ;AAC5B,UAAI,OAAO,SAAS,CAAC,IAAI,IAAI,KAAK;AAClC,UAAI,OAAO,SAAS,CAAC,IAAI,IAAI,KAAK;AAClC,UAAI,OAAO,SAAS,CAAC,IAAI,QAAQ,mBAAmB,QAAQ,SAAS,CAAC,KAAK;AAC3E,UAAI,OAAO,SAAS,CAAC,IAAI,QAAQ,mBAAmB,QAAQ,SAAS,CAAC,KAAK;AAC3E,QAAE,CAAC,KAAK;AACR,QAAE,CAAC,KAAK;AACR,QAAE,CAAC,KAAK;AACR,QAAE,CAAC,KAAK;AAAA,IACV;AACA,SAAK,eAAe,KAAK,gBAAuB,OAAO;AACvD,IAAO,OAAO,KAAK,cAAc,CAAC;AAAA,EACpC;AACA,EAAAA,eAAc,UAAU,uBAAuB,WAAY;AACzD,QAAI,gBAAgB;AACpB,QAAI,YAAY,CAAC;AACjB,WAAO,eAAe;AACpB,gBAAU,KAAK,aAAa;AAC5B,sBAAgB,cAAc;AAAA,IAChC;AACA,WAAO,gBAAgB,UAAU,IAAI,GAAG;AACtC,oBAAc,gBAAgB;AAAA,IAChC;AACA,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,eAAc,UAAU,oBAAoB,SAAU,GAAG;AACvD,QAAI,CAAC,GAAG;AACN;AAAA,IACF;AACA,QAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjC,QAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjC,QAAI,WAAW,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpC,QAAI,SAAS,KAAK,KAAK,IAAI,WAAW,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC3D,SAAK,KAAK,KAAK,EAAE,IAAI,KAAK,IAAI,MAAM;AACpC,SAAK,KAAK,KAAK,EAAE;AACjB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,WAAW,CAAC;AACjB,SAAK,IAAI,CAAC,EAAE,CAAC;AACb,SAAK,IAAI,CAAC,EAAE,CAAC;AACb,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AACA,EAAAA,eAAc,UAAU,qBAAqB,WAAY;AACvD,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,QAAI,SAAS,KAAK;AAClB,QAAI,IAAI,KAAK;AACb,QAAI,UAAU,OAAO,WAAW;AAC9B,MAAO,IAAI,cAAc,OAAO,cAAc,CAAC;AAC/C,UAAI;AAAA,IACN;AACA,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAI,MAAM,IAAI;AACZ,sBAAgB,CAAC,IAAI;AACrB,sBAAgB,CAAC,IAAI;AACrB,MAAO,IAAI,cAAc,GAAG,eAAe;AAC3C,mBAAa,CAAC,KAAK;AACnB,mBAAa,CAAC,KAAK;AACnB,UAAI;AAAA,IACN;AACA,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AACA,EAAAA,eAAc,UAAU,iBAAiB,SAAU,KAAK;AACtD,QAAI,IAAI,KAAK;AACb,UAAM,OAAO,CAAC;AACd,QAAI,CAAC,GAAG;AACN,UAAI,CAAC,IAAI;AACT,UAAI,CAAC,IAAI;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5C,QAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5C,QAAI,EAAE,CAAC,IAAI,GAAG;AACZ,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IACjB;AACA,QAAI,EAAE,CAAC,IAAI,GAAG;AACZ,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,UAAU,wBAAwB,SAAU,GAAG,GAAG;AAC9D,QAAI,KAAK,CAAC,GAAG,CAAC;AACd,QAAI,eAAe,KAAK;AACxB,QAAI,cAAc;AAChB,MAAO,eAAe,IAAI,IAAI,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,UAAU,yBAAyB,SAAU,GAAG,GAAG;AAC/D,QAAI,KAAK,CAAC,GAAG,CAAC;AACd,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACb,MAAO,eAAe,IAAI,IAAI,SAAS;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,UAAU,eAAe,WAAY;AACjD,QAAI,IAAI,KAAK;AACb,WAAO,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,SAAS,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,QAAQ,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI;AAAA,EAC3G;AACA,EAAAA,eAAc,UAAU,gBAAgB,SAAU,QAAQ;AACxD,kBAAc,MAAM,MAAM;AAAA,EAC5B;AACA,EAAAA,eAAc,oBAAoB,SAAU,QAAQ,GAAG;AACrD,QAAI,KAAK,CAAC;AACV,QAAI,KAAK,OAAO,WAAW;AAC3B,QAAI,KAAK,OAAO,WAAW;AAC3B,QAAI,KAAK,OAAO;AAChB,QAAI,KAAK,OAAO;AAChB,QAAI,KAAK,OAAO;AAChB,QAAI,KAAK,OAAO;AAChB,QAAI,WAAW,OAAO,YAAY;AAClC,QAAI,IAAI,OAAO;AACf,QAAI,IAAI,OAAO;AACf,QAAI,QAAQ,OAAO,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI;AACpD,QAAI,QAAQ,OAAO,QAAQ,KAAK,IAAI,CAAC,OAAO,KAAK,IAAI;AACrD,QAAI,MAAM,MAAM,MAAM,IAAI;AACxB,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK;AACd,QAAE,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,KAAK;AAC/B,QAAE,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,KAAK;AAAA,IACjC,OAAO;AACL,QAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AAAA,IAChB;AACA,MAAE,CAAC,IAAI;AACP,MAAE,CAAC,IAAI;AACP,MAAE,CAAC,IAAI,QAAQ;AACf,MAAE,CAAC,IAAI,QAAQ;AACf,gBAAmB,OAAO,GAAG,GAAG,QAAQ;AACxC,MAAE,CAAC,KAAK,KAAK;AACb,MAAE,CAAC,KAAK,KAAK;AACb,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,mBAAmB,WAAY;AAC3C,QAAI,QAAQA,eAAc;AAC1B,UAAM,SAAS,MAAM,SAAS,MAAM,mBAAmB;AACvD,UAAM,IAAI,MAAM,IAAI,MAAM,UAAU,MAAM,UAAU,MAAM,QAAQ,MAAM,QAAQ,MAAM,WAAW,MAAM,UAAU,MAAM,UAAU;AAAA,EACnI,EAAE;AACF,SAAOA;AACT,EAAE;AAEK,IAAI,sBAAsB,CAAC,KAAK,KAAK,WAAW,WAAW,WAAW,WAAW,YAAY,UAAU,UAAU,SAAS,OAAO;AACjI,SAAS,cAAc,QAAQ,QAAQ;AAC5C,WAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,QAAI,WAAW,oBAAoB,CAAC;AACpC,WAAO,QAAQ,IAAI,OAAO,QAAQ;AAAA,EACpC;AACF;AACA,IAAO,wBAAQ;;;AC1Nf,IAAI,cAAc;AAAA,EAChB,QAAQ,SAAU,GAAG;AACnB,WAAO;AAAA,EACT;AAAA,EACA,aAAa,SAAU,GAAG;AACxB,WAAO,IAAI;AAAA,EACb;AAAA,EACA,cAAc,SAAU,GAAG;AACzB,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,gBAAgB,SAAU,GAAG;AAC3B,SAAK,KAAK,KAAK,GAAG;AAChB,aAAO,MAAM,IAAI;AAAA,IACnB;AACA,WAAO,QAAQ,EAAE,KAAK,IAAI,KAAK;AAAA,EACjC;AAAA,EACA,SAAS,SAAU,GAAG;AACpB,WAAO,IAAI,IAAI;AAAA,EACjB;AAAA,EACA,UAAU,SAAU,GAAG;AACrB,WAAO,EAAE,IAAI,IAAI,IAAI;AAAA,EACvB;AAAA,EACA,YAAY,SAAU,GAAG;AACvB,SAAK,KAAK,KAAK,GAAG;AAChB,aAAO,MAAM,IAAI,IAAI;AAAA,IACvB;AACA,WAAO,QAAQ,KAAK,KAAK,IAAI,IAAI;AAAA,EACnC;AAAA,EACA,WAAW,SAAU,GAAG;AACtB,WAAO,IAAI,IAAI,IAAI;AAAA,EACrB;AAAA,EACA,YAAY,SAAU,GAAG;AACvB,WAAO,IAAI,EAAE,IAAI,IAAI,IAAI;AAAA,EAC3B;AAAA,EACA,cAAc,SAAU,GAAG;AACzB,SAAK,KAAK,KAAK,GAAG;AAChB,aAAO,MAAM,IAAI,IAAI,IAAI;AAAA,IAC3B;AACA,WAAO,SAAS,KAAK,KAAK,IAAI,IAAI,IAAI;AAAA,EACxC;AAAA,EACA,WAAW,SAAU,GAAG;AACtB,WAAO,IAAI,IAAI,IAAI,IAAI;AAAA,EACzB;AAAA,EACA,YAAY,SAAU,GAAG;AACvB,WAAO,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,EAC/B;AAAA,EACA,cAAc,SAAU,GAAG;AACzB,SAAK,KAAK,KAAK,GAAG;AAChB,aAAO,MAAM,IAAI,IAAI,IAAI,IAAI;AAAA,IAC/B;AACA,WAAO,QAAQ,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI;AAAA,EAC3C;AAAA,EACA,cAAc,SAAU,GAAG;AACzB,WAAO,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AAAA,EACrC;AAAA,EACA,eAAe,SAAU,GAAG;AAC1B,WAAO,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AAAA,EACjC;AAAA,EACA,iBAAiB,SAAU,GAAG;AAC5B,WAAO,OAAO,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EACxC;AAAA,EACA,eAAe,SAAU,GAAG;AAC1B,WAAO,MAAM,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC;AAAA,EAC3C;AAAA,EACA,gBAAgB,SAAU,GAAG;AAC3B,WAAO,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC;AAAA,EAC9C;AAAA,EACA,kBAAkB,SAAU,GAAG;AAC7B,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,SAAK,KAAK,KAAK,GAAG;AAChB,aAAO,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC;AAAA,IACnC;AACA,WAAO,OAAO,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,IAAI;AAAA,EAC9C;AAAA,EACA,YAAY,SAAU,GAAG;AACvB,WAAO,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAAA,EAChC;AAAA,EACA,aAAa,SAAU,GAAG;AACxB,WAAO,KAAK,KAAK,IAAI,EAAE,IAAI,CAAC;AAAA,EAC9B;AAAA,EACA,eAAe,SAAU,GAAG;AAC1B,SAAK,KAAK,KAAK,GAAG;AAChB,aAAO,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI;AAAA,IACxC;AACA,WAAO,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI;AAAA,EAC9C;AAAA,EACA,WAAW,SAAU,GAAG;AACtB,QAAI;AACJ,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,IAAI,GAAG;AACf,UAAI;AACJ,UAAI,IAAI;AAAA,IACV,OAAO;AACL,UAAI,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AAAA,IACvC;AACA,WAAO,EAAE,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC;AAAA,EAChF;AAAA,EACA,YAAY,SAAU,GAAG;AACvB,QAAI;AACJ,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,IAAI,GAAG;AACf,UAAI;AACJ,UAAI,IAAI;AAAA,IACV,OAAO;AACL,UAAI,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AAAA,IACvC;AACA,WAAO,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI;AAAA,EAC5E;AAAA,EACA,cAAc,SAAU,GAAG;AACzB,QAAI;AACJ,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,IAAI,GAAG;AACf,UAAI;AACJ,UAAI,IAAI;AAAA,IACV,OAAO;AACL,UAAI,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AAAA,IACvC;AACA,SAAK,KAAK,KAAK,GAAG;AAChB,aAAO,QAAQ,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC;AAAA,IACtF;AACA,WAAO,IAAI,KAAK,IAAI,GAAG,OAAO,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,MAAM;AAAA,EACzF;AAAA,EACA,QAAQ,SAAU,GAAG;AACnB,QAAI,IAAI;AACR,WAAO,IAAI,MAAM,IAAI,KAAK,IAAI;AAAA,EAChC;AAAA,EACA,SAAS,SAAU,GAAG;AACpB,QAAI,IAAI;AACR,WAAO,EAAE,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,EACvC;AAAA,EACA,WAAW,SAAU,GAAG;AACtB,QAAI,IAAI,UAAU;AAClB,SAAK,KAAK,KAAK,GAAG;AAChB,aAAO,OAAO,IAAI,MAAM,IAAI,KAAK,IAAI;AAAA,IACvC;AACA,WAAO,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,EACnD;AAAA,EACA,UAAU,SAAU,GAAG;AACrB,WAAO,IAAI,YAAY,UAAU,IAAI,CAAC;AAAA,EACxC;AAAA,EACA,WAAW,SAAU,GAAG;AACtB,QAAI,IAAI,IAAI,MAAM;AAChB,aAAO,SAAS,IAAI;AAAA,IACtB,WAAW,IAAI,IAAI,MAAM;AACvB,aAAO,UAAU,KAAK,MAAM,QAAQ,IAAI;AAAA,IAC1C,WAAW,IAAI,MAAM,MAAM;AACzB,aAAO,UAAU,KAAK,OAAO,QAAQ,IAAI;AAAA,IAC3C,OAAO;AACL,aAAO,UAAU,KAAK,QAAQ,QAAQ,IAAI;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,aAAa,SAAU,GAAG;AACxB,QAAI,IAAI,KAAK;AACX,aAAO,YAAY,SAAS,IAAI,CAAC,IAAI;AAAA,IACvC;AACA,WAAO,YAAY,UAAU,IAAI,IAAI,CAAC,IAAI,MAAM;AAAA,EAClD;AACF;AACA,IAAO,iBAAQ;;;ACvLf,IAAI,UAAU,KAAK;AACnB,IAAI,WAAW,KAAK;AACpB,IAAIC,WAAU;AACd,IAAI,kBAAkB;AACtB,IAAI,aAAa,SAAS,CAAC;AAC3B,IAAI,YAAY,IAAI;AACpB,IAAI,MAAMC,QAAS;AACnB,IAAI,MAAMA,QAAS;AACnB,IAAI,MAAMA,QAAS;AACnB,SAAS,aAAa,KAAK;AACzB,SAAO,MAAM,CAACD,YAAW,MAAMA;AACjC;AACA,SAASE,iBAAgB,KAAK;AAC5B,SAAO,MAAMF,YAAW,MAAM,CAACA;AACjC;AACO,SAAS,QAAQ,IAAI,IAAI,IAAI,IAAI,GAAG;AACzC,MAAI,OAAO,IAAI;AACf,SAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI,OAAO;AAC/E;AACO,SAAS,kBAAkB,IAAI,IAAI,IAAI,IAAI,GAAG;AACnD,MAAI,OAAO,IAAI;AACf,SAAO,OAAO,KAAK,MAAM,OAAO,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,IAAI;AAC9E;AACO,SAAS,YAAY,IAAI,IAAI,IAAI,IAAI,KAAKG,QAAO;AACtD,MAAI,IAAI,KAAK,KAAK,KAAK,MAAM;AAC7B,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI;AAC3B,MAAI,IAAI,KAAK,KAAK;AAClB,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI;AACR,MAAI,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;AACtC,QAAI,aAAa,CAAC,GAAG;AACnB,MAAAA,OAAM,CAAC,IAAI;AAAA,IACb,OAAO;AACL,UAAI,KAAK,CAAC,IAAI;AACd,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACf;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,OAAO,IAAI,IAAI,IAAI,IAAI;AAC3B,QAAI,aAAa,IAAI,GAAG;AACtB,UAAI,IAAI,IAAI;AACZ,UAAI,KAAK,CAAC,IAAI,IAAI;AAClB,UAAI,KAAK,CAAC,IAAI;AACd,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACf;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACf;AAAA,IACF,WAAW,OAAO,GAAG;AACnB,UAAI,WAAW,SAAS,IAAI;AAC5B,UAAI,KAAK,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI;AACjC,UAAI,KAAK,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI;AACjC,UAAI,KAAK,GAAG;AACV,aAAK,CAAC,QAAQ,CAAC,IAAI,SAAS;AAAA,MAC9B,OAAO;AACL,aAAK,QAAQ,IAAI,SAAS;AAAA,MAC5B;AACA,UAAI,KAAK,GAAG;AACV,aAAK,CAAC,QAAQ,CAAC,IAAI,SAAS;AAAA,MAC9B,OAAO;AACL,aAAK,QAAQ,IAAI,SAAS;AAAA,MAC5B;AACA,UAAI,MAAM,CAAC,KAAK,KAAK,QAAQ,IAAI;AACjC,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACf;AAAA,IACF,OAAO;AACL,UAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI,SAAS,IAAI,IAAI,CAAC;AACzD,UAAI,QAAQ,KAAK,KAAK,CAAC,IAAI;AAC3B,UAAI,QAAQ,SAAS,CAAC;AACtB,UAAI,MAAM,KAAK,IAAI,KAAK;AACxB,UAAI,MAAM,CAAC,IAAI,IAAI,QAAQ,QAAQ,IAAI;AACvC,UAAI,MAAM,CAAC,IAAI,SAAS,MAAM,aAAa,KAAK,IAAI,KAAK,OAAO,IAAI;AACpE,UAAI,MAAM,CAAC,IAAI,SAAS,MAAM,aAAa,KAAK,IAAI,KAAK,OAAO,IAAI;AACpE,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACf;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACf;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,aAAa,IAAI,IAAI,IAAI,IAAIC,UAAS;AACpD,MAAI,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI;AAC/B,MAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AACvC,MAAI,IAAI,IAAI,KAAK,IAAI;AACrB,MAAI,IAAI;AACR,MAAI,aAAa,CAAC,GAAG;AACnB,QAAIF,iBAAgB,CAAC,GAAG;AACtB,UAAI,KAAK,CAAC,IAAI;AACd,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAE,SAAQ,GAAG,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,OAAO,IAAI,IAAI,IAAI,IAAI;AAC3B,QAAI,aAAa,IAAI,GAAG;AACtB,MAAAA,SAAQ,CAAC,IAAI,CAAC,KAAK,IAAI;AAAA,IACzB,WAAW,OAAO,GAAG;AACnB,UAAI,WAAW,SAAS,IAAI;AAC5B,UAAI,MAAM,CAAC,IAAI,aAAa,IAAI;AAChC,UAAI,MAAM,CAAC,IAAI,aAAa,IAAI;AAChC,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,SAAQ,GAAG,IAAI;AAAA,MACjB;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,SAAQ,GAAG,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,eAAe,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK;AACrD,MAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,MAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,MAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,MAAI,QAAQ,MAAM,OAAO,IAAI;AAC7B,MAAI,QAAQ,MAAM,OAAO,IAAI;AAC7B,MAAI,SAAS,OAAO,QAAQ,IAAI;AAChC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACX;AACO,SAAS,kBAAkB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK;AAC3E,MAAI;AACJ,MAAI,WAAW;AACf,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,WAAS,KAAK,GAAG,KAAK,GAAG,MAAM,MAAM;AACnC,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AACnC,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AACnC,SAAK,WAAa,KAAK,GAAG;AAC1B,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,UAAI;AAAA,IACN;AAAA,EACF;AACA,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,QAAI,WAAW,iBAAiB;AAC9B;AAAA,IACF;AACA,WAAO,IAAI;AACX,WAAO,IAAI;AACX,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC,SAAK,WAAa,KAAK,GAAG;AAC1B,QAAI,QAAQ,KAAK,KAAK,GAAG;AACvB,UAAI;AACJ,UAAI;AAAA,IACN,OAAO;AACL,UAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC,UAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC,WAAK,WAAa,KAAK,GAAG;AAC1B,UAAI,QAAQ,KAAK,KAAK,GAAG;AACvB,YAAI;AACJ,YAAI;AAAA,MACN,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK;AACP,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC;AAClC,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC;AAAA,EACpC;AACA,SAAO,SAAS,CAAC;AACnB;AACO,SAAS,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW;AACrE,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI,OAAO,IAAI;AACf,WAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACnC,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC;AACjC,QAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC;AACjC,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,IAAI;AACb,SAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAChC,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO;AACT;AACO,SAAS,YAAY,IAAI,IAAI,IAAI,GAAG;AACzC,MAAI,OAAO,IAAI;AACf,SAAO,QAAQ,OAAO,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI;AACnD;AACO,SAAS,sBAAsB,IAAI,IAAI,IAAI,GAAG;AACnD,SAAO,MAAM,IAAI,MAAM,KAAK,MAAM,KAAK,KAAK;AAC9C;AACO,SAAS,gBAAgB,IAAI,IAAI,IAAI,KAAKD,QAAO;AACtD,MAAI,IAAI,KAAK,IAAI,KAAK;AACtB,MAAI,IAAI,KAAK,KAAK;AAClB,MAAI,IAAI,KAAK;AACb,MAAI,IAAI;AACR,MAAI,aAAa,CAAC,GAAG;AACnB,QAAID,iBAAgB,CAAC,GAAG;AACtB,UAAI,KAAK,CAAC,IAAI;AACd,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAC,OAAM,GAAG,IAAI;AAAA,MACf;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,OAAO,IAAI,IAAI,IAAI,IAAI;AAC3B,QAAI,aAAa,IAAI,GAAG;AACtB,UAAI,KAAK,CAAC,KAAK,IAAI;AACnB,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACf;AAAA,IACF,WAAW,OAAO,GAAG;AACnB,UAAI,WAAW,SAAS,IAAI;AAC5B,UAAI,MAAM,CAAC,IAAI,aAAa,IAAI;AAChC,UAAI,MAAM,CAAC,IAAI,aAAa,IAAI;AAChC,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACf;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,kBAAkB,IAAI,IAAI,IAAI;AAC5C,MAAI,UAAU,KAAK,KAAK,IAAI;AAC5B,MAAI,YAAY,GAAG;AACjB,WAAO;AAAA,EACT,OAAO;AACL,YAAQ,KAAK,MAAM;AAAA,EACrB;AACF;AACO,SAAS,mBAAmB,IAAI,IAAI,IAAI,GAAG,KAAK;AACrD,MAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,MAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,MAAI,QAAQ,MAAM,OAAO,IAAI;AAC7B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACX;AACO,SAAS,sBAAsB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK;AACvE,MAAI;AACJ,MAAI,WAAW;AACf,MAAI,IAAI;AACR,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,WAAS,KAAK,GAAG,KAAK,GAAG,MAAM,MAAM;AACnC,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,EAAE;AACnC,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,EAAE;AACnC,QAAI,KAAK,WAAa,KAAK,GAAG;AAC9B,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,UAAI;AAAA,IACN;AAAA,EACF;AACA,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,QAAI,WAAW,iBAAiB;AAC9B;AAAA,IACF;AACA,QAAI,OAAO,IAAI;AACf,QAAI,OAAO,IAAI;AACf,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,IAAI;AACrC,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,IAAI;AACrC,QAAI,KAAK,WAAa,KAAK,GAAG;AAC9B,QAAI,QAAQ,KAAK,KAAK,GAAG;AACvB,UAAI;AACJ,UAAI;AAAA,IACN,OAAO;AACL,UAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,IAAI;AACrC,UAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,IAAI;AACrC,UAAI,KAAK,WAAa,KAAK,GAAG;AAC9B,UAAI,QAAQ,KAAK,KAAK,GAAG;AACvB,YAAI;AACJ,YAAI;AAAA,MACN,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK;AACP,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC;AAClC,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC;AAAA,EACpC;AACA,SAAO,SAAS,CAAC;AACnB;AACO,SAAS,gBAAgB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW;AACjE,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI,OAAO,IAAI;AACf,WAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACnC,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC;AACjC,QAAI,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC;AACjC,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,IAAI;AACb,SAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAChC,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO;AACT;;;ACrUA,IAAI,SAAS;AACN,SAAS,sBAAsB,gBAAgB;AACpD,MAAI,QAAQ,kBAAkB,OAAO,KAAK,cAAc;AACxD,MAAI,OAAO;AACT,QAAI,SAAS,MAAM,CAAC,EAAE,MAAM,GAAG;AAC/B,QAAI,MAAM,CAAC,KAAK,OAAO,CAAC,CAAC;AACzB,QAAI,MAAM,CAAC,KAAK,OAAO,CAAC,CAAC;AACzB,QAAI,MAAM,CAAC,KAAK,OAAO,CAAC,CAAC;AACzB,QAAI,MAAM,CAAC,KAAK,OAAO,CAAC,CAAC;AACzB,QAAI,MAAM,MAAM,MAAM,MAAM,GAAG,GAAG;AAChC;AAAA,IACF;AACA,QAAI,UAAU,CAAC;AACf,WAAO,SAAU,GAAG;AAClB,aAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,YAAY,GAAG,KAAK,KAAK,GAAG,GAAG,OAAO,KAAK,QAAQ,GAAG,KAAK,KAAK,GAAG,QAAQ,CAAC,CAAC;AAAA,IAChH;AAAA,EACF;AACF;;;AChBA,IAAI,OAAO,WAAY;AACrB,WAASE,MAAK,MAAM;AAClB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,QAAQ,KAAK,QAAQ;AAC1B,SAAK,SAAS,KAAK,SAAS;AAC5B,SAAK,OAAO,KAAK,QAAQ;AACzB,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,UAAU,KAAK,UAAU,KAAK,MAAM;AAAA,EAC3C;AACA,EAAAA,MAAK,UAAU,OAAO,SAAU,YAAY,WAAW;AACrD,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,aAAa,aAAa,KAAK;AACpC,WAAK,UAAU;AAAA,IACjB;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,eAAe;AACpB;AAAA,IACF;AACA,QAAI,OAAO,KAAK;AAChB,QAAI,cAAc,aAAa,KAAK,aAAa,KAAK;AACtD,QAAI,UAAU,cAAc;AAC5B,QAAI,UAAU,GAAG;AACf,gBAAU;AAAA,IACZ;AACA,cAAU,KAAK,IAAI,SAAS,CAAC;AAC7B,QAAI,aAAa,KAAK;AACtB,QAAI,WAAW,aAAa,WAAW,OAAO,IAAI;AAClD,SAAK,QAAQ,QAAQ;AACrB,QAAI,YAAY,GAAG;AACjB,UAAI,KAAK,MAAM;AACb,YAAI,YAAY,cAAc;AAC9B,aAAK,aAAa,aAAa;AAC/B,aAAK,cAAc;AACnB,aAAK,UAAU;AAAA,MACjB,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,EAAAA,MAAK,UAAU,QAAQ,WAAY;AACjC,SAAK,UAAU;AAAA,EACjB;AACA,EAAAA,MAAK,UAAU,SAAS,WAAY;AAClC,SAAK,UAAU;AAAA,EACjB;AACA,EAAAA,MAAK,UAAU,YAAY,SAAU,QAAQ;AAC3C,SAAK,SAAS;AACd,SAAK,aAAa,WAAW,MAAM,IAAI,SAAS,eAAY,MAAM,KAAK,sBAAsB,MAAM;AAAA,EACrG;AACA,SAAOA;AACT,EAAE;AACF,IAAO,eAAQ;;;ACzDf,IAAI,YAAY,KAAK;AACd,SAAS,eAAe,OAAO;AACpC,MAAI;AACJ,MAAI,CAAC,SAAS,UAAU,eAAe;AACrC,YAAQ;AAAA,EACV,WAAW,OAAO,UAAU,YAAY,MAAM,QAAQ,MAAM,IAAI,IAAI;AAClE,QAAI,MAAM,MAAM,KAAK;AACrB,QAAI,KAAK;AACP,cAAQ,SAAS,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI;AACxD,gBAAU,IAAI,CAAC;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,SAAS,WAAW,OAAO,IAAI;AAAA,EACjC;AACF;AACA,IAAIC,WAAU;AACP,SAASC,cAAa,WAAW;AACtC,SAAO,YAAYD,YAAW,YAAY,CAACA;AAC7C;AACO,SAAS,OAAO,WAAW;AAChC,SAAO,UAAU,YAAY,GAAG,IAAI;AACtC;AACO,SAAS,OAAO,WAAW;AAChC,SAAO,UAAU,YAAY,GAAG,IAAI;AACtC;AAIO,SAAS,aAAa,GAAG;AAC9B,SAAO,YAAY,OAAO,EAAE,CAAC,CAAC,IAAI,MAAM,OAAO,EAAE,CAAC,CAAC,IAAI,MAAM,OAAO,EAAE,CAAC,CAAC,IAAI,MAAM,OAAO,EAAE,CAAC,CAAC,IAAI,MAAM,OAAO,EAAE,CAAC,CAAC,IAAI,MAAM,OAAO,EAAE,CAAC,CAAC,IAAI;AAC7I;AACO,IAAI,uBAAuB;AAAA,EAChC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AACV;AACO,SAAS,YAAY,GAAG,YAAY,cAAc;AACvD,MAAI,iBAAiB,OAAO;AAC1B,SAAK,aAAa;AAAA,EACpB,WAAW,iBAAiB,UAAU;AACpC,SAAK,aAAa;AAAA,EACpB;AACA,SAAO;AACT;AACO,SAAS,UAAU,OAAO;AAC/B,SAAO,UAAU,MAAM,cAAc,MAAM,iBAAiB,MAAM;AACpE;AACO,SAAS,aAAa,aAAa;AACxC,MAAI,QAAQ,YAAY;AACxB,MAAI,cAAc,YAAY,eAAe;AAC7C,SAAO,CAAC,MAAM,cAAc,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,MAAM,iBAAiB,GAAG,QAAQ,CAAC,IAAI,MAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE,KAAK,GAAG;AACvL;AAWO,SAAS,eAAe,KAAK;AAClC,SAAO,OAAO,CAAC,CAAC,IAAI;AACtB;AACO,SAAS,aAAa,KAAK;AAChC,SAAO,OAAO,CAAC,CAAC,IAAI;AACtB;AACO,SAAS,UAAU,KAAK;AAC7B,SAAO,eAAe,GAAG,KAAK,aAAa,GAAG;AAChD;AACO,SAAS,iBAAiB,KAAK;AACpC,SAAO,IAAI,SAAS;AACtB;AACO,SAAS,iBAAiB,KAAK;AACpC,SAAO,IAAI,SAAS;AACtB;AACO,SAAS,WAAW,KAAK;AAC9B,SAAO,QAAQ,IAAI,SAAS,YAAY,IAAI,SAAS;AACvD;AACO,SAAS,SAAS,IAAI;AAC3B,SAAO,UAAU,KAAK;AACxB;AACO,SAAS,iBAAiB,IAAI;AACnC,MAAIE,SAAQ,GAAG,eAAe;AAC9B,MAAI,OAAO,KAAK,IAAIA,OAAM,CAAC,GAAGA,OAAM,CAAC,CAAC;AACtC,SAAO,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,CAAC;AAC7D;AACO,SAAS,sBAAsB,WAAW;AAC/C,MAAI,IAAI,UAAU,KAAK;AACvB,MAAI,IAAI,UAAU,KAAK;AACvB,MAAI,YAAY,UAAU,YAAY,KAAK;AAC3C,MAAI,SAAS,UAAU,UAAU,QAAQ,CAAC;AAC1C,MAAI,SAAS,UAAU,UAAU,QAAQ,CAAC;AAC1C,MAAI,QAAQ,UAAU,SAAS;AAC/B,MAAI,QAAQ,UAAU,SAAS;AAC/B,MAAI,MAAM,CAAC;AACX,MAAI,KAAK,GAAG;AACV,QAAI,KAAK,eAAe,IAAI,QAAQ,IAAI,KAAK;AAAA,EAC/C;AACA,MAAI,UAAU;AACZ,QAAI,KAAK,YAAY,WAAW,GAAG;AAAA,EACrC;AACA,MAAI,WAAW,KAAK,WAAW,GAAG;AAChC,QAAI,KAAK,WAAW,SAAS,MAAM,SAAS,GAAG;AAAA,EACjD;AACA,MAAI,SAAS,OAAO;AAClB,QAAI,KAAK,UAAU,UAAU,QAAQ,gBAAgB,IAAI,UAAU,UAAU,QAAQ,gBAAgB,IAAI,MAAM;AAAA,EACjH;AACA,SAAO,IAAI,KAAK,GAAG;AACrB;AACO,IAAI,eAAe,WAAY;AACpC,MAAI,YAAI,mBAAmB,WAAW,OAAO,IAAI,GAAG;AAClD,WAAO,SAAU,KAAK;AACpB,aAAO,OAAO,KAAK,SAAS,mBAAmB,GAAG,CAAC,CAAC;AAAA,IACtD;AAAA,EACF;AACA,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO,SAAU,KAAK;AACpB,aAAO,OAAO,KAAK,GAAG,EAAE,SAAS,QAAQ;AAAA,IAC3C;AAAA,EACF;AACA,SAAO,SAAU,KAAK;AACpB,QAAI,MAAuC;AACzC,eAAS,6DAA8D;AAAA,IACzE;AACA,WAAO;AAAA,EACT;AACF,EAAE;;;AC/HF,IAAI,aAAa,MAAM,UAAU;AACjC,SAAS,kBAAkB,IAAI,IAAI,SAAS;AAC1C,UAAQ,KAAK,MAAM,UAAU;AAC/B;AACA,SAAS,mBAAmB,KAAK,IAAI,IAAI,SAAS;AAChD,MAAIC,OAAM,GAAG;AACb,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,QAAI,CAAC,IAAI,kBAAkB,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EAClD;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,KAAK,IAAI,IAAI,SAAS;AAChD,MAAIA,OAAM,GAAG;AACb,MAAIC,QAAOD,QAAO,GAAG,CAAC,EAAE;AACxB,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,QAAI,CAAC,IAAI,CAAC,GAAG;AACX,UAAI,CAAC,IAAI,CAAC;AAAA,IACZ;AACA,aAAS,IAAI,GAAG,IAAIC,OAAM,KAAK;AAC7B,UAAI,CAAC,EAAE,CAAC,IAAI,kBAAkB,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO;AAAA,IAC3D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,WAAW,KAAK,IAAI,IAAI,MAAM;AACrC,MAAID,OAAM,GAAG;AACb,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,QAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI;AAAA,EAC3B;AACA,SAAO;AACT;AACA,SAAS,WAAW,KAAK,IAAI,IAAI,MAAM;AACrC,MAAIA,OAAM,GAAG;AACb,MAAIC,QAAOD,QAAO,GAAG,CAAC,EAAE;AACxB,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,QAAI,CAAC,IAAI,CAAC,GAAG;AACX,UAAI,CAAC,IAAI,CAAC;AAAA,IACZ;AACA,aAAS,IAAI,GAAG,IAAIC,OAAM,KAAK;AAC7B,UAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI;AAAA,IACpC;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,MAAM,MAAM;AAClC,MAAI,OAAO,KAAK;AAChB,MAAI,OAAO,KAAK;AAChB,MAAI,aAAa,OAAO,OAAO,OAAO;AACtC,MAAI,aAAa,KAAK,IAAI,MAAM,IAAI;AACpC,MAAI,OAAO,WAAW,aAAa,CAAC,KAAK;AAAA,IACvC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAClB,QAAQ;AAAA,EACV;AACA,WAAS,IAAI,YAAY,IAAI,KAAK,IAAI,MAAM,IAAI,GAAG,KAAK;AACtD,eAAW,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK,MAAM,MAAM;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AACA,SAAS,UAAU,MAAM,MAAM,QAAQ;AACrC,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,MAAM;AAC5B;AAAA,EACF;AACA,MAAI,UAAU,KAAK;AACnB,MAAI,UAAU,KAAK;AACnB,MAAI,YAAY,SAAS;AACvB,QAAI,mBAAmB,UAAU;AACjC,QAAI,kBAAkB;AACpB,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,eAAS,IAAI,SAAS,IAAI,SAAS,KAAK;AACtC,aAAK,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,MAC7D;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE;AAC9B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,WAAW,GAAG;AAChB,UAAI,MAAM,KAAK,CAAC,CAAC,GAAG;AAClB,aAAK,CAAC,IAAI,KAAK,CAAC;AAAA,MAClB;AAAA,IACF,OAAO;AACL,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,YAAI,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG;AACrB,eAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACO,SAAS,WAAW,OAAO;AAChC,MAAI,YAAY,KAAK,GAAG;AACtB,QAAID,OAAM,MAAM;AAChB,QAAI,YAAY,MAAM,CAAC,CAAC,GAAG;AACzB,UAAI,MAAM,CAAC;AACX,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,YAAI,KAAK,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,MACpC;AACA,aAAO;AAAA,IACT;AACA,WAAO,WAAW,KAAK,KAAK;AAAA,EAC9B;AACA,SAAO;AACT;AACA,SAAS,YAAY,MAAM;AACzB,OAAK,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,CAAC,KAAK;AACjC,OAAK,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,CAAC,KAAK;AACjC,OAAK,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,CAAC,KAAK;AACjC,OAAK,CAAC,IAAI,KAAK,CAAC,KAAK,OAAO,IAAI,KAAK,CAAC;AACtC,SAAO,UAAU,KAAK,KAAK,GAAG,IAAI;AACpC;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,YAAY,SAAS,MAAM,CAAC,CAAC,IAAI,IAAI;AAC9C;AACA,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;AAC1B,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,6BAA6B;AACjC,IAAI,6BAA6B;AACjC,IAAI,oBAAoB;AACxB,SAAS,oBAAoB,SAAS;AACpC,SAAO,YAAY,8BAA8B,YAAY;AAC/D;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,YAAY,uBAAuB,YAAY;AACxD;AACA,IAAI,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;AACzB,IAAI,QAAQ,WAAY;AACtB,WAASE,OAAM,UAAU;AACvB,SAAK,YAAY,CAAC;AAClB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,OAAM,UAAU,aAAa,WAAY;AACvC,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,OAAM,UAAU,cAAc,WAAY;AACxC,SAAK,YAAY;AACjB,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,YAAY;AAAA,IAClC;AAAA,EACF;AACA,EAAAA,OAAM,UAAU,eAAe,WAAY;AACzC,WAAO,KAAK,UAAU,UAAU;AAAA,EAClC;AACA,EAAAA,OAAM,UAAU,mBAAmB,WAAY;AAC7C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,OAAM,UAAU,cAAc,SAAU,MAAM,UAAU,QAAQ;AAC9D,SAAK,aAAa;AAClB,QAAI,YAAY,KAAK;AACrB,QAAIF,OAAM,UAAU;AACpB,QAAI,WAAW;AACf,QAAI,UAAU;AACd,QAAI,QAAQ;AACZ,QAAI,YAAY,QAAQ,GAAG;AACzB,UAAI,WAAW,cAAc,QAAQ;AACrC,gBAAU;AACV,UAAI,aAAa,KAAK,CAAC,SAAS,SAAS,CAAC,CAAC,KAAK,aAAa,KAAK,CAAC,SAAS,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG;AAC3F,mBAAW;AAAA,MACb;AAAA,IACF,OAAO;AACL,UAAI,SAAS,QAAQ,KAAK,CAAC,MAAM,QAAQ,GAAG;AAC1C,kBAAU;AAAA,MACZ,WAAW,SAAS,QAAQ,GAAG;AAC7B,YAAI,CAAC,MAAM,CAAC,QAAQ,GAAG;AACrB,oBAAU;AAAA,QACZ,OAAO;AACL,cAAI,aAAmB,MAAM,QAAQ;AACrC,cAAI,YAAY;AACd,oBAAQ;AACR,sBAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF,WAAW,iBAAiB,QAAQ,GAAG;AACrC,YAAI,iBAAiB,OAAO,CAAC,GAAG,KAAK;AACrC,uBAAe,aAAa,IAAI,SAAS,YAAY,SAAU,WAAW;AACxE,iBAAO;AAAA,YACL,QAAQ,UAAU;AAAA,YAClB,OAAa,MAAM,UAAU,KAAK;AAAA,UACpC;AAAA,QACF,CAAC;AACD,YAAI,iBAAiB,QAAQ,GAAG;AAC9B,oBAAU;AAAA,QACZ,WAAW,iBAAiB,QAAQ,GAAG;AACrC,oBAAU;AAAA,QACZ;AACA,gBAAQ;AAAA,MACV;AAAA,IACF;AACA,QAAIA,SAAQ,GAAG;AACb,WAAK,UAAU;AAAA,IACjB,WAAW,YAAY,KAAK,WAAW,YAAY,mBAAmB;AACpE,iBAAW;AAAA,IACb;AACA,SAAK,WAAW,KAAK,YAAY;AACjC,QAAI,KAAK;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,IACX;AACA,QAAI,QAAQ;AACV,SAAG,SAAS;AACZ,SAAG,aAAa,WAAW,MAAM,IAAI,SAAS,eAAY,MAAM,KAAK,sBAAsB,MAAM;AAAA,IACnG;AACA,cAAU,KAAK,EAAE;AACjB,WAAO;AAAA,EACT;AACA,EAAAE,OAAM,UAAU,UAAU,SAAU,SAAS,eAAe;AAC1D,QAAI,MAAM,KAAK;AACf,QAAI,KAAK,YAAY;AACnB,UAAI,KAAK,SAAU,GAAG,GAAG;AACvB,eAAO,EAAE,OAAO,EAAE;AAAA,MACpB,CAAC;AAAA,IACH;AACA,QAAI,UAAU,KAAK;AACnB,QAAI,SAAS,IAAI;AACjB,QAAI,SAAS,IAAI,SAAS,CAAC;AAC3B,QAAI,aAAa,KAAK;AACtB,QAAI,QAAQ,iBAAiB,OAAO;AACpC,QAAIC,cAAa,oBAAoB,OAAO;AAC5C,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAI,KAAK,IAAI,CAAC;AACd,UAAI,QAAQ,GAAG;AACf,UAAI,YAAY,OAAO;AACvB,SAAG,UAAU,GAAG,OAAO;AACvB,UAAI,CAAC,YAAY;AACf,YAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,oBAAU,OAAO,WAAW,OAAO;AAAA,QACrC,WAAWA,aAAY;AACrB,yBAAe,MAAM,YAAY,UAAU,UAAU;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,cAAc,YAAY,8BAA8B,iBAAiB,KAAK,aAAa,KAAK,cAAc,aAAa,KAAK,YAAY,cAAc,WAAW,CAAC,cAAc,WAAW;AAClM,WAAK,iBAAiB;AACtB,UAAI,aAAa,IAAI,CAAC,EAAE;AACxB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAI,YAAY,mBAAmB;AACjC,cAAI,CAAC,EAAE,gBAAgB,IAAI,CAAC,EAAE,QAAQ;AAAA,QACxC,WAAW,YAAY,kBAAkB;AACvC,cAAI,CAAC,EAAE,gBAAgB,WAAW,CAAC,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,EAAE;AAAA,QACpE,WAAW,iBAAiB,OAAO,GAAG;AACpC,cAAI,CAAC,EAAE,gBAAgB,YAAY,sBAAsB,WAAW,CAAC,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,EAAE,IAAI,WAAW,CAAC,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,EAAE;AAAA,QACrJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,EAAAD,OAAM,UAAU,OAAO,SAAU,QAAQ,SAAS;AAChD,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AACA,QAAI,KAAK,kBAAkB,KAAK,eAAe,WAAW;AACxD,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,aAAa,KAAK,kBAAkB;AACxC,QAAI,WAAW,aAAa,kBAAkB;AAC9C,QAAI,UAAU,KAAK;AACnB,QAAI,YAAY,KAAK;AACrB,QAAI,SAAS,UAAU;AACvB,QAAI,WAAW,KAAK;AACpB,QAAI,eAAe,YAAY;AAC/B,QAAI;AACJ,QAAI,YAAY,KAAK;AACrB,QAAIE,WAAU,KAAK;AACnB,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW,GAAG;AAChB,cAAQ,YAAY,UAAU,CAAC;AAAA,IACjC,OAAO;AACL,UAAI,UAAU,GAAG;AACf,mBAAW;AAAA,MACb,WAAW,UAAU,KAAK,UAAU;AAClC,YAAIC,SAAQD,SAAQ,YAAY,GAAG,SAAS,CAAC;AAC7C,aAAK,WAAWC,QAAO,YAAY,GAAG,YAAY;AAChD,cAAI,UAAU,QAAQ,EAAE,WAAW,SAAS;AAC1C;AAAA,UACF;AAAA,QACF;AACA,mBAAWD,SAAQ,UAAU,SAAS,CAAC;AAAA,MACzC,OAAO;AACL,aAAK,WAAW,WAAW,WAAW,QAAQ,YAAY;AACxD,cAAI,UAAU,QAAQ,EAAE,UAAU,SAAS;AACzC;AAAA,UACF;AAAA,QACF;AACA,mBAAWA,SAAQ,WAAW,GAAG,SAAS,CAAC;AAAA,MAC7C;AACA,kBAAY,UAAU,WAAW,CAAC;AAClC,cAAQ,UAAU,QAAQ;AAAA,IAC5B;AACA,QAAI,EAAE,SAAS,YAAY;AACzB;AAAA,IACF;AACA,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,QAAI,WAAW,UAAU,UAAU,MAAM;AACzC,QAAI,IAAI,aAAa,IAAI,IAAIA,UAAS,UAAU,MAAM,WAAW,UAAU,CAAC;AAC5E,QAAI,UAAU,YAAY;AACxB,UAAI,UAAU,WAAW,CAAC;AAAA,IAC5B;AACA,QAAI,YAAY,aAAa,KAAK,iBAAiB,eAAe,UAAU,OAAO,QAAQ;AAC3F,SAAK,iBAAiB,OAAO,KAAK,iBAAiB,CAAC,WAAW;AAC7D,kBAAY,KAAK,iBAAiB,CAAC;AAAA,IACrC;AACA,QAAI,KAAK,UAAU;AACjB,aAAO,QAAQ,IAAI,IAAI,IAAI,MAAM,WAAW,UAAU;AAAA,IACxD,WAAW,iBAAiB,OAAO,GAAG;AACpC,kBAAY,sBAAsB,mBAAmB,WAAW,MAAM,QAAQ,GAAG,UAAU,QAAQ,GAAG,CAAC,IAAI,mBAAmB,WAAW,MAAM,QAAQ,GAAG,UAAU,QAAQ,GAAG,CAAC;AAAA,IAClL,WAAW,oBAAoB,OAAO,GAAG;AACvC,UAAI,MAAM,MAAM,QAAQ;AACxB,UAAI,YAAY,UAAU,QAAQ;AAClC,UAAI,qBAAqB,YAAY;AACrC,aAAO,QAAQ,IAAI;AAAA,QACjB,MAAM,qBAAqB,WAAW;AAAA,QACtC,GAAG,kBAAkB,IAAI,GAAG,UAAU,GAAG,CAAC;AAAA,QAC1C,GAAG,kBAAkB,IAAI,GAAG,UAAU,GAAG,CAAC;AAAA,QAC1C,YAAY,IAAI,IAAI,YAAY,SAAU,WAAW,KAAK;AACxD,cAAI,gBAAgB,UAAU,WAAW,GAAG;AAC5C,iBAAO;AAAA,YACL,QAAQ,kBAAkB,UAAU,QAAQ,cAAc,QAAQ,CAAC;AAAA,YACnE,OAAO,YAAY,mBAAmB,CAAC,GAAG,UAAU,OAAO,cAAc,OAAO,CAAC,CAAC;AAAA,UACpF;AAAA,QACF,CAAC;AAAA,QACD,QAAQ,UAAU;AAAA,MACpB;AACA,UAAI,oBAAoB;AACtB,eAAO,QAAQ,EAAE,KAAK,kBAAkB,IAAI,IAAI,UAAU,IAAI,CAAC;AAC/D,eAAO,QAAQ,EAAE,KAAK,kBAAkB,IAAI,IAAI,UAAU,IAAI,CAAC;AAAA,MACjE,OAAO;AACL,eAAO,QAAQ,EAAE,IAAI,kBAAkB,IAAI,GAAG,UAAU,GAAG,CAAC;AAAA,MAC9D;AAAA,IACF,WAAW,cAAc;AACvB,yBAAmB,WAAW,MAAM,QAAQ,GAAG,UAAU,QAAQ,GAAG,CAAC;AACrE,UAAI,CAAC,YAAY;AACf,eAAO,QAAQ,IAAI,YAAY,SAAS;AAAA,MAC1C;AAAA,IACF,OAAO;AACL,UAAI,QAAQ,kBAAkB,MAAM,QAAQ,GAAG,UAAU,QAAQ,GAAG,CAAC;AACrE,UAAI,YAAY;AACd,aAAK,iBAAiB;AAAA,MACxB,OAAO;AACL,eAAO,QAAQ,IAAI;AAAA,MACrB;AAAA,IACF;AACA,QAAI,YAAY;AACd,WAAK,aAAa,MAAM;AAAA,IAC1B;AAAA,EACF;AACA,EAAAF,OAAM,UAAU,eAAe,SAAU,QAAQ;AAC/C,QAAI,UAAU,KAAK;AACnB,QAAI,WAAW,KAAK;AACpB,QAAI,gBAAgB,KAAK;AACzB,QAAI,YAAY,mBAAmB;AACjC,aAAO,QAAQ,IAAI,OAAO,QAAQ,IAAI;AAAA,IACxC,WAAW,YAAY,kBAAkB;AACvC,MAAM,MAAM,OAAO,QAAQ,GAAG,OAAO;AACrC,iBAAW,SAAS,SAAS,eAAe,CAAC;AAC7C,aAAO,QAAQ,IAAI,YAAY,OAAO;AAAA,IACxC,WAAW,YAAY,qBAAqB;AAC1C,iBAAW,OAAO,QAAQ,GAAG,OAAO,QAAQ,GAAG,eAAe,CAAC;AAAA,IACjE,WAAW,YAAY,qBAAqB;AAC1C,iBAAW,OAAO,QAAQ,GAAG,OAAO,QAAQ,GAAG,eAAe,CAAC;AAAA,IACjE;AAAA,EACF;AACA,SAAOA;AACT,EAAE;AACF,IAAI,WAAW,WAAY;AACzB,WAASI,UAAS,QAAQ,MAAM,wBAAwB,YAAY;AAClE,SAAK,UAAU,CAAC;AAChB,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,QAAI,QAAQ,YAAY;AACtB,eAAS,kDAAmD;AAC5D;AAAA,IACF;AACA,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB;AAAA,EACxB;AACA,EAAAA,UAAS,UAAU,aAAa,WAAY;AAC1C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,UAAS,UAAU,WAAW,WAAY;AACxC,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,UAAS,UAAU,UAAU,WAAY;AACvC,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,UAAS,UAAU,YAAY,WAAY;AACzC,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,UAAS,UAAU,eAAe,SAAU,QAAQ;AAClD,SAAK,UAAU;AAAA,EACjB;AACA,EAAAA,UAAS,UAAU,OAAO,SAAU,MAAM,OAAO,QAAQ;AACvD,WAAO,KAAK,aAAa,MAAM,OAAO,KAAK,KAAK,GAAG,MAAM;AAAA,EAC3D;AACA,EAAAA,UAAS,UAAU,eAAe,SAAU,MAAM,OAAO,WAAW,QAAQ;AAC1E,QAAI,SAAS,KAAK;AAClB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,WAAW,UAAU,CAAC;AAC1B,UAAI,QAAQ,OAAO,QAAQ;AAC3B,UAAI,CAAC,OAAO;AACV,gBAAQ,OAAO,QAAQ,IAAI,IAAI,MAAM,QAAQ;AAC7C,YAAI,eAAe;AACnB,YAAI,gBAAgB,KAAK,kBAAkB,QAAQ;AACnD,YAAI,eAAe;AACjB,cAAI,kBAAkB,cAAc;AACpC,cAAI,cAAc,gBAAgB,gBAAgB,SAAS,CAAC;AAC5D,yBAAe,eAAe,YAAY;AAC1C,cAAI,cAAc,YAAY,oBAAoB,cAAc;AAC9D,2BAAe,YAAY,YAAY;AAAA,UACzC;AAAA,QACF,OAAO;AACL,yBAAe,KAAK,QAAQ,QAAQ;AAAA,QACtC;AACA,YAAI,gBAAgB,MAAM;AACxB;AAAA,QACF;AACA,YAAI,OAAO,GAAG;AACZ,gBAAM,YAAY,GAAG,WAAW,YAAY,GAAG,MAAM;AAAA,QACvD;AACA,aAAK,WAAW,KAAK,QAAQ;AAAA,MAC/B;AACA,YAAM,YAAY,MAAM,WAAW,MAAM,QAAQ,CAAC,GAAG,MAAM;AAAA,IAC7D;AACA,SAAK,WAAW,KAAK,IAAI,KAAK,UAAU,IAAI;AAC5C,WAAO;AAAA,EACT;AACA,EAAAA,UAAS,UAAU,QAAQ,WAAY;AACrC,SAAK,MAAM,MAAM;AACjB,SAAK,UAAU;AAAA,EACjB;AACA,EAAAA,UAAS,UAAU,SAAS,WAAY;AACtC,SAAK,MAAM,OAAO;AAClB,SAAK,UAAU;AAAA,EACjB;AACA,EAAAA,UAAS,UAAU,WAAW,WAAY;AACxC,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AACA,EAAAA,UAAS,UAAU,WAAW,SAAU,UAAU;AAChD,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AACA,EAAAA,UAAS,UAAU,gBAAgB,WAAY;AAC7C,SAAK,mBAAmB;AACxB,SAAK,QAAQ;AACb,QAAI,WAAW,KAAK;AACpB,QAAI,UAAU;AACZ,UAAIN,OAAM,SAAS;AACnB,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,iBAAS,CAAC,EAAE,KAAK,IAAI;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACA,EAAAM,UAAS,UAAU,mBAAmB,WAAY;AAChD,SAAK,mBAAmB;AACxB,QAAI,YAAY,KAAK;AACrB,QAAI,cAAc,KAAK;AACvB,QAAI,WAAW;AACb,gBAAU,WAAW,KAAK,KAAK;AAAA,IACjC;AACA,SAAK,QAAQ;AACb,QAAI,aAAa;AACf,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,oBAAY,CAAC,EAAE,KAAK,IAAI;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,EAAAA,UAAS,UAAU,qBAAqB,WAAY;AAClD,QAAI,SAAS,KAAK;AAClB,QAAI,aAAa,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,aAAO,WAAW,CAAC,CAAC,EAAE,YAAY;AAAA,IACpC;AAAA,EACF;AACA,EAAAA,UAAS,UAAU,oBAAoB,SAAU,WAAW;AAC1D,QAAI;AACJ,QAAI,oBAAoB,KAAK;AAC7B,QAAI,mBAAmB;AACrB,eAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,YAAI,QAAQ,kBAAkB,CAAC,EAAE,SAAS,SAAS;AACnD,YAAI,OAAO;AACT,0BAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,EAAAA,UAAS,UAAU,QAAQ,SAAU,QAAQ;AAC3C,QAAI,KAAK,WAAW,GAAG;AACrB;AAAA,IACF;AACA,SAAK,WAAW;AAChB,QAAIC,QAAO;AACX,QAAI,SAAS,CAAC;AACd,QAAI,UAAU,KAAK,YAAY;AAC/B,aAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,UAAI,WAAW,KAAK,WAAW,CAAC;AAChC,UAAI,QAAQ,KAAK,QAAQ,QAAQ;AACjC,UAAI,gBAAgB,KAAK,kBAAkB,QAAQ;AACnD,UAAI,MAAM,MAAM;AAChB,UAAI,SAAS,IAAI;AACjB,YAAM,QAAQ,SAAS,aAAa;AACpC,UAAI,MAAM,aAAa,GAAG;AACxB,YAAI,CAAC,KAAK,kBAAkB,MAAM,UAAU;AAC1C,cAAI,SAAS,IAAI,SAAS,CAAC;AAC3B,cAAI,QAAQ;AACV,YAAAA,MAAK,QAAQ,MAAM,QAAQ,IAAI,OAAO;AAAA,UACxC;AACA,gBAAM,YAAY;AAAA,QACpB,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,UAAU,KAAK,QAAQ;AAChC,UAAI,OAAO,IAAI,aAAK;AAAA,QAClB,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX,OAAO,KAAK,UAAU;AAAA,QACtB,SAAS,SAAU,SAAS;AAC1B,UAAAA,MAAK,WAAW;AAChB,cAAI,oBAAoBA,MAAK;AAC7B,cAAI,mBAAmB;AACrB,gBAAI,2BAA2B;AAC/B,qBAASC,KAAI,GAAGA,KAAI,kBAAkB,QAAQA,MAAK;AACjD,kBAAI,kBAAkBA,EAAC,EAAE,OAAO;AAC9B,2CAA2B;AAC3B;AAAA,cACF;AAAA,YACF;AACA,gBAAI,CAAC,0BAA0B;AAC7B,cAAAD,MAAK,qBAAqB;AAAA,YAC5B;AAAA,UACF;AACA,mBAASC,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACtC,mBAAOA,EAAC,EAAE,KAAKD,MAAK,SAAS,OAAO;AAAA,UACtC;AACA,cAAI,cAAcA,MAAK;AACvB,cAAI,aAAa;AACf,qBAASC,KAAI,GAAGA,KAAI,YAAY,QAAQA,MAAK;AAC3C,0BAAYA,EAAC,EAAED,MAAK,SAAS,OAAO;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW,WAAY;AACrB,UAAAA,MAAK,cAAc;AAAA,QACrB;AAAA,MACF,CAAC;AACD,WAAK,QAAQ;AACb,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,QAAQ,IAAI;AAAA,MAC7B;AACA,UAAI,QAAQ;AACV,aAAK,UAAU,MAAM;AAAA,MACvB;AAAA,IACF,OAAO;AACL,WAAK,cAAc;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACA,EAAAD,UAAS,UAAU,OAAO,SAAU,eAAe;AACjD,QAAI,CAAC,KAAK,OAAO;AACf;AAAA,IACF;AACA,QAAI,OAAO,KAAK;AAChB,QAAI,eAAe;AACjB,WAAK,QAAQ,CAAC;AAAA,IAChB;AACA,SAAK,iBAAiB;AAAA,EACxB;AACA,EAAAA,UAAS,UAAU,QAAQ,SAAU,MAAM;AACzC,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AACA,EAAAA,UAAS,UAAU,SAAS,SAAU,IAAI;AACxC,QAAI,IAAI;AACN,UAAI,CAAC,KAAK,aAAa;AACrB,aAAK,cAAc,CAAC;AAAA,MACtB;AACA,WAAK,YAAY,KAAK,EAAE;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AACA,EAAAA,UAAS,UAAU,OAAO,SAAU,IAAI;AACtC,QAAI,IAAI;AACN,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,WAAW,CAAC;AAAA,MACnB;AACA,WAAK,SAAS,KAAK,EAAE;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,UAAS,UAAU,UAAU,SAAU,IAAI;AACzC,QAAI,IAAI;AACN,UAAI,CAAC,KAAK,aAAa;AACrB,aAAK,cAAc,CAAC;AAAA,MACtB;AACA,WAAK,YAAY,KAAK,EAAE;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AACA,EAAAA,UAAS,UAAU,UAAU,WAAY;AACvC,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,UAAS,UAAU,WAAW,SAAU,UAAU;AAChD,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AACA,EAAAA,UAAS,UAAU,YAAY,WAAY;AACzC,QAAI,QAAQ;AACZ,WAAO,IAAI,KAAK,YAAY,SAAU,KAAK;AACzC,aAAO,MAAM,QAAQ,GAAG;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,EAAAA,UAAS,UAAU,aAAa,SAAU,WAAW,eAAe;AAClE,QAAI,CAAC,UAAU,UAAU,CAAC,KAAK,OAAO;AACpC,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK;AAClB,QAAI,aAAa,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,QAAQ,OAAO,UAAU,CAAC,CAAC;AAC/B,UAAI,SAAS,CAAC,MAAM,WAAW,GAAG;AAChC,YAAI,eAAe;AACjB,gBAAM,KAAK,KAAK,SAAS,CAAC;AAAA,QAC5B,WAAW,KAAK,aAAa,GAAG;AAC9B,gBAAM,KAAK,KAAK,SAAS,CAAC;AAAA,QAC5B;AACA,cAAM,YAAY;AAAA,MACpB;AAAA,IACF;AACA,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAI,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,WAAW,GAAG;AACvC,qBAAa;AACb;AAAA,MACF;AAAA,IACF;AACA,QAAI,YAAY;AACd,WAAK,iBAAiB;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,UAAS,UAAU,SAAS,SAAU,QAAQ,WAAW,aAAa;AACpE,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,gBAAY,aAAa,KAAK;AAC9B,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,WAAW,UAAU,CAAC;AAC1B,UAAI,QAAQ,KAAK,QAAQ,QAAQ;AACjC,UAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC;AAAA,MACF;AACA,UAAI,MAAM,MAAM;AAChB,UAAI,KAAK,IAAI,cAAc,IAAI,IAAI,SAAS,CAAC;AAC7C,UAAI,IAAI;AACN,eAAO,QAAQ,IAAI,WAAW,GAAG,QAAQ;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AACA,EAAAA,UAAS,UAAU,qBAAqB,SAAU,YAAY,WAAW;AACvE,gBAAY,aAAa,KAAK,UAAU;AACxC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,WAAW,UAAU,CAAC;AAC1B,UAAI,QAAQ,KAAK,QAAQ,QAAQ;AACjC,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,MAAM,MAAM;AAChB,UAAI,IAAI,SAAS,GAAG;AAClB,YAAI,SAAS,IAAI,IAAI;AACrB,cAAM,YAAY,OAAO,MAAM,WAAW,QAAQ,CAAC;AACnD,cAAM,QAAQ,KAAK,UAAU,MAAM,iBAAiB,CAAC;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT,EAAE;AACF,IAAO,mBAAQ;;;AC7rBf,IAAI,QAAQ,WAAY;AACtB,WAASG,OAAM,GAAG,GAAG;AACnB,SAAK,IAAI,KAAK;AACd,SAAK,IAAI,KAAK;AAAA,EAChB;AACA,EAAAA,OAAM,UAAU,OAAO,SAAU,OAAO;AACtC,SAAK,IAAI,MAAM;AACf,SAAK,IAAI,MAAM;AACf,WAAO;AAAA,EACT;AACA,EAAAA,OAAM,UAAU,QAAQ,WAAY;AAClC,WAAO,IAAIA,OAAM,KAAK,GAAG,KAAK,CAAC;AAAA,EACjC;AACA,EAAAA,OAAM,UAAU,MAAM,SAAU,GAAG,GAAG;AACpC,SAAK,IAAI;AACT,SAAK,IAAI;AACT,WAAO;AAAA,EACT;AACA,EAAAA,OAAM,UAAU,QAAQ,SAAU,OAAO;AACvC,WAAO,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK;AAAA,EAChD;AACA,EAAAA,OAAM,UAAU,MAAM,SAAU,OAAO;AACrC,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAChB,WAAO;AAAA,EACT;AACA,EAAAA,OAAM,UAAU,QAAQ,SAAU,QAAQ;AACxC,SAAK,KAAK;AACV,SAAK,KAAK;AAAA,EACZ;AACA,EAAAA,OAAM,UAAU,cAAc,SAAU,OAAO,QAAQ;AACrD,SAAK,KAAK,MAAM,IAAI;AACpB,SAAK,KAAK,MAAM,IAAI;AAAA,EACtB;AACA,EAAAA,OAAM,UAAU,MAAM,SAAU,OAAO;AACrC,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAChB,WAAO;AAAA,EACT;AACA,EAAAA,OAAM,UAAU,MAAM,SAAU,OAAO;AACrC,WAAO,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM;AAAA,EAC3C;AACA,EAAAA,OAAM,UAAU,MAAM,WAAY;AAChC,WAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,EACpD;AACA,EAAAA,OAAM,UAAU,YAAY,WAAY;AACtC,WAAO,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AAAA,EACzC;AACA,EAAAA,OAAM,UAAU,YAAY,WAAY;AACtC,QAAIC,OAAM,KAAK,IAAI;AACnB,SAAK,KAAKA;AACV,SAAK,KAAKA;AACV,WAAO;AAAA,EACT;AACA,EAAAD,OAAM,UAAU,WAAW,SAAU,OAAO;AAC1C,QAAI,KAAK,KAAK,IAAI,MAAM;AACxB,QAAI,KAAK,KAAK,IAAI,MAAM;AACxB,WAAO,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAAA,EACpC;AACA,EAAAA,OAAM,UAAU,iBAAiB,SAAU,OAAO;AAChD,QAAI,KAAK,KAAK,IAAI,MAAM;AACxB,QAAI,KAAK,KAAK,IAAI,MAAM;AACxB,WAAO,KAAK,KAAK,KAAK;AAAA,EACxB;AACA,EAAAA,OAAM,UAAU,SAAS,WAAY;AACnC,SAAK,IAAI,CAAC,KAAK;AACf,SAAK,IAAI,CAAC,KAAK;AACf,WAAO;AAAA,EACT;AACA,EAAAA,OAAM,UAAU,YAAY,SAAU,GAAG;AACvC,QAAI,CAAC,GAAG;AACN;AAAA,IACF;AACA,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,SAAK,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,SAAK,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,WAAO;AAAA,EACT;AACA,EAAAA,OAAM,UAAU,UAAU,SAAU,KAAK;AACvC,QAAI,CAAC,IAAI,KAAK;AACd,QAAI,CAAC,IAAI,KAAK;AACd,WAAO;AAAA,EACT;AACA,EAAAA,OAAM,UAAU,YAAY,SAAU,OAAO;AAC3C,SAAK,IAAI,MAAM,CAAC;AAChB,SAAK,IAAI,MAAM,CAAC;AAAA,EAClB;AACA,EAAAA,OAAM,MAAM,SAAU,GAAG,GAAG,GAAG;AAC7B,MAAE,IAAI;AACN,MAAE,IAAI;AAAA,EACR;AACA,EAAAA,OAAM,OAAO,SAAU,GAAG,IAAI;AAC5B,MAAE,IAAI,GAAG;AACT,MAAE,IAAI,GAAG;AAAA,EACX;AACA,EAAAA,OAAM,MAAM,SAAU,GAAG;AACvB,WAAO,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAAA,EACxC;AACA,EAAAA,OAAM,YAAY,SAAU,GAAG;AAC7B,WAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,EAC7B;AACA,EAAAA,OAAM,MAAM,SAAU,IAAI,IAAI;AAC5B,WAAO,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAAA,EACjC;AACA,EAAAA,OAAM,MAAM,SAAU,KAAK,IAAI,IAAI;AACjC,QAAI,IAAI,GAAG,IAAI,GAAG;AAClB,QAAI,IAAI,GAAG,IAAI,GAAG;AAAA,EACpB;AACA,EAAAA,OAAM,MAAM,SAAU,KAAK,IAAI,IAAI;AACjC,QAAI,IAAI,GAAG,IAAI,GAAG;AAClB,QAAI,IAAI,GAAG,IAAI,GAAG;AAAA,EACpB;AACA,EAAAA,OAAM,QAAQ,SAAU,KAAK,IAAI,QAAQ;AACvC,QAAI,IAAI,GAAG,IAAI;AACf,QAAI,IAAI,GAAG,IAAI;AAAA,EACjB;AACA,EAAAA,OAAM,cAAc,SAAU,KAAK,IAAI,IAAI,QAAQ;AACjD,QAAI,IAAI,GAAG,IAAI,GAAG,IAAI;AACtB,QAAI,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA,EACxB;AACA,EAAAA,OAAM,OAAO,SAAU,KAAK,IAAI,IAAI,GAAG;AACrC,QAAI,OAAO,IAAI;AACf,QAAI,IAAI,OAAO,GAAG,IAAI,IAAI,GAAG;AAC7B,QAAI,IAAI,OAAO,GAAG,IAAI,IAAI,GAAG;AAAA,EAC/B;AACA,SAAOA;AACT,EAAE;AACF,IAAO,gBAAQ;;;AC9Hf,IAAI,UAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,KAAK,IAAI,cAAM;AACnB,IAAI,KAAK,IAAI,cAAM;AACnB,IAAI,KAAK,IAAI,cAAM;AACnB,IAAI,KAAK,IAAI,cAAM;AACnB,IAAI,QAAQ,IAAI,cAAM;AACtB,IAAI,QAAQ,IAAI,cAAM;AACtB,IAAI,eAAe,WAAY;AAC7B,WAASE,cAAa,GAAG,GAAG,OAAO,QAAQ;AACzC,QAAI,QAAQ,GAAG;AACb,UAAI,IAAI;AACR,cAAQ,CAAC;AAAA,IACX;AACA,QAAI,SAAS,GAAG;AACd,UAAI,IAAI;AACR,eAAS,CAAC;AAAA,IACZ;AACA,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AACA,EAAAA,cAAa,UAAU,QAAQ,SAAU,OAAO;AAC9C,QAAI,IAAI,QAAQ,MAAM,GAAG,KAAK,CAAC;AAC/B,QAAI,IAAI,QAAQ,MAAM,GAAG,KAAK,CAAC;AAC/B,QAAI,SAAS,KAAK,CAAC,KAAK,SAAS,KAAK,KAAK,GAAG;AAC5C,WAAK,QAAQ,QAAQ,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,IAAI;AAAA,IACrE,OAAO;AACL,WAAK,QAAQ,MAAM;AAAA,IACrB;AACA,QAAI,SAAS,KAAK,CAAC,KAAK,SAAS,KAAK,MAAM,GAAG;AAC7C,WAAK,SAAS,QAAQ,MAAM,IAAI,MAAM,QAAQ,KAAK,IAAI,KAAK,MAAM,IAAI;AAAA,IACxE,OAAO;AACL,WAAK,SAAS,MAAM;AAAA,IACtB;AACA,SAAK,IAAI;AACT,SAAK,IAAI;AAAA,EACX;AACA,EAAAA,cAAa,UAAU,iBAAiB,SAAU,GAAG;AACnD,IAAAA,cAAa,eAAe,MAAM,MAAM,CAAC;AAAA,EAC3C;AACA,EAAAA,cAAa,UAAU,qBAAqB,SAAU,GAAG;AACvD,QAAI,IAAI;AACR,QAAI,KAAK,EAAE,QAAQ,EAAE;AACrB,QAAI,KAAK,EAAE,SAAS,EAAE;AACtB,QAAI,IAAW,OAAO;AACtB,IAAO,UAAU,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;AACnC,IAAO,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AAC3B,IAAO,UAAU,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACjC,WAAO;AAAA,EACT;AACA,EAAAA,cAAa,UAAU,YAAY,SAAU,GAAG,KAAK;AACnD,QAAI,CAAC,GAAG;AACN,aAAO;AAAA,IACT;AACA,QAAI,EAAE,aAAaA,gBAAe;AAChC,UAAIA,cAAa,OAAO,CAAC;AAAA,IAC3B;AACA,QAAI,IAAI;AACR,QAAI,MAAM,EAAE;AACZ,QAAI,MAAM,EAAE,IAAI,EAAE;AAClB,QAAI,MAAM,EAAE;AACZ,QAAI,MAAM,EAAE,IAAI,EAAE;AAClB,QAAI,MAAM,EAAE;AACZ,QAAI,MAAM,EAAE,IAAI,EAAE;AAClB,QAAI,MAAM,EAAE;AACZ,QAAI,MAAM,EAAE,IAAI,EAAE;AAClB,QAAI,UAAU,EAAE,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM;AAC7D,QAAI,KAAK;AACP,UAAI,OAAO;AACX,UAAI,OAAO;AACX,UAAI,KAAK,KAAK,IAAI,MAAM,GAAG;AAC3B,UAAI,KAAK,KAAK,IAAI,MAAM,GAAG;AAC3B,UAAI,KAAK,KAAK,IAAI,MAAM,GAAG;AAC3B,UAAI,KAAK,KAAK,IAAI,MAAM,GAAG;AAC3B,UAAI,KAAK,KAAK,IAAI,IAAI,EAAE;AACxB,UAAI,KAAK,KAAK,IAAI,IAAI,EAAE;AACxB,UAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,YAAI,KAAK,MAAM;AACb,iBAAO;AACP,cAAI,KAAK,IAAI;AACX,0BAAM,IAAI,OAAO,CAAC,IAAI,CAAC;AAAA,UACzB,OAAO;AACL,0BAAM,IAAI,OAAO,IAAI,CAAC;AAAA,UACxB;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,KAAK,MAAM;AACb,iBAAO;AACP,cAAI,KAAK,IAAI;AACX,0BAAM,IAAI,OAAO,IAAI,CAAC;AAAA,UACxB,OAAO;AACL,0BAAM,IAAI,OAAO,CAAC,IAAI,CAAC;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,YAAI,KAAK,MAAM;AACb,iBAAO;AACP,cAAI,KAAK,IAAI;AACX,0BAAM,IAAI,OAAO,GAAG,CAAC,EAAE;AAAA,UACzB,OAAO;AACL,0BAAM,IAAI,OAAO,GAAG,EAAE;AAAA,UACxB;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,KAAK,MAAM;AACb,iBAAO;AACP,cAAI,KAAK,IAAI;AACX,0BAAM,IAAI,OAAO,GAAG,EAAE;AAAA,UACxB,OAAO;AACL,0BAAM,IAAI,OAAO,GAAG,CAAC,EAAE;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK;AACP,oBAAM,KAAK,KAAK,UAAU,QAAQ,KAAK;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AACA,EAAAA,cAAa,UAAU,UAAU,SAAU,GAAG,GAAG;AAC/C,QAAI,OAAO;AACX,WAAO,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK;AAAA,EACtF;AACA,EAAAA,cAAa,UAAU,QAAQ,WAAY;AACzC,WAAO,IAAIA,cAAa,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EACjE;AACA,EAAAA,cAAa,UAAU,OAAO,SAAU,OAAO;AAC7C,IAAAA,cAAa,KAAK,MAAM,KAAK;AAAA,EAC/B;AACA,EAAAA,cAAa,UAAU,QAAQ,WAAY;AACzC,WAAO;AAAA,MACL,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACA,EAAAA,cAAa,UAAU,WAAW,WAAY;AAC5C,WAAO,SAAS,KAAK,CAAC,KAAK,SAAS,KAAK,CAAC,KAAK,SAAS,KAAK,KAAK,KAAK,SAAS,KAAK,MAAM;AAAA,EAC7F;AACA,EAAAA,cAAa,UAAU,SAAS,WAAY;AAC1C,WAAO,KAAK,UAAU,KAAK,KAAK,WAAW;AAAA,EAC7C;AACA,EAAAA,cAAa,SAAS,SAAU,MAAM;AACpC,WAAO,IAAIA,cAAa,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EACjE;AACA,EAAAA,cAAa,OAAO,SAAU,QAAQ,QAAQ;AAC5C,WAAO,IAAI,OAAO;AAClB,WAAO,IAAI,OAAO;AAClB,WAAO,QAAQ,OAAO;AACtB,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,EAAAA,cAAa,iBAAiB,SAAU,QAAQ,QAAQ,GAAG;AACzD,QAAI,CAAC,GAAG;AACN,UAAI,WAAW,QAAQ;AACrB,QAAAA,cAAa,KAAK,QAAQ,MAAM;AAAA,MAClC;AACA;AAAA,IACF;AACA,QAAI,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,IAAI,OAAO;AAC9D,UAAI,KAAK,EAAE,CAAC;AACZ,UAAI,KAAK,EAAE,CAAC;AACZ,UAAI,KAAK,EAAE,CAAC;AACZ,UAAI,KAAK,EAAE,CAAC;AACZ,aAAO,IAAI,OAAO,IAAI,KAAK;AAC3B,aAAO,IAAI,OAAO,IAAI,KAAK;AAC3B,aAAO,QAAQ,OAAO,QAAQ;AAC9B,aAAO,SAAS,OAAO,SAAS;AAChC,UAAI,OAAO,QAAQ,GAAG;AACpB,eAAO,KAAK,OAAO;AACnB,eAAO,QAAQ,CAAC,OAAO;AAAA,MACzB;AACA,UAAI,OAAO,SAAS,GAAG;AACrB,eAAO,KAAK,OAAO;AACnB,eAAO,SAAS,CAAC,OAAO;AAAA,MAC1B;AACA;AAAA,IACF;AACA,OAAG,IAAI,GAAG,IAAI,OAAO;AACrB,OAAG,IAAI,GAAG,IAAI,OAAO;AACrB,OAAG,IAAI,GAAG,IAAI,OAAO,IAAI,OAAO;AAChC,OAAG,IAAI,GAAG,IAAI,OAAO,IAAI,OAAO;AAChC,OAAG,UAAU,CAAC;AACd,OAAG,UAAU,CAAC;AACd,OAAG,UAAU,CAAC;AACd,OAAG,UAAU,CAAC;AACd,WAAO,IAAI,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC,WAAO,IAAI,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC,QAAI,OAAO,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC,QAAI,OAAO,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC,WAAO,QAAQ,OAAO,OAAO;AAC7B,WAAO,SAAS,OAAO,OAAO;AAAA,EAChC;AACA,SAAOA;AACT,EAAE;AACF,IAAO,uBAAQ;;;ACxMf,IAAI,WAAW,WAAY;AACzB,WAASC,UAAS,iBAAiB;AACjC,QAAI,iBAAiB;AACnB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AACA,EAAAA,UAAS,UAAU,KAAK,SAAU,OAAO,OAAO,SAAS,SAAS;AAChE,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,CAAC;AAAA,IACrB;AACA,QAAI,KAAK,KAAK;AACd,QAAI,OAAO,UAAU,YAAY;AAC/B,gBAAU;AACV,gBAAU;AACV,cAAQ;AAAA,IACV;AACA,QAAI,CAAC,WAAW,CAAC,OAAO;AACtB,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB,KAAK;AAC1B,QAAI,SAAS,QAAQ,kBAAkB,eAAe,gBAAgB;AACpE,cAAQ,eAAe,eAAe,KAAK;AAAA,IAC7C;AACA,QAAI,CAAC,GAAG,KAAK,GAAG;AACd,SAAG,KAAK,IAAI,CAAC;AAAA,IACf;AACA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK,EAAE,QAAQ,KAAK;AACzC,UAAI,GAAG,KAAK,EAAE,CAAC,EAAE,MAAM,SAAS;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,OAAO;AAAA,MACT,GAAG;AAAA,MACH;AAAA,MACA,KAAK,WAAW;AAAA,MAChB,YAAY,QAAQ;AAAA,IACtB;AACA,QAAI,YAAY,GAAG,KAAK,EAAE,SAAS;AACnC,QAAI,WAAW,GAAG,KAAK,EAAE,SAAS;AAClC,gBAAY,SAAS,aAAa,GAAG,KAAK,EAAE,OAAO,WAAW,GAAG,IAAI,IAAI,GAAG,KAAK,EAAE,KAAK,IAAI;AAC5F,WAAO;AAAA,EACT;AACA,EAAAA,UAAS,UAAU,WAAW,SAAU,WAAW;AACjD,QAAI,KAAK,KAAK;AACd,WAAO,CAAC,MAAM,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,SAAS,EAAE;AAAA,EACjD;AACA,EAAAA,UAAS,UAAU,MAAM,SAAU,WAAW,SAAS;AACrD,QAAI,KAAK,KAAK;AACd,QAAI,CAAC,IAAI;AACP,aAAO;AAAA,IACT;AACA,QAAI,CAAC,WAAW;AACd,WAAK,aAAa,CAAC;AACnB,aAAO;AAAA,IACT;AACA,QAAI,SAAS;AACX,UAAI,GAAG,SAAS,GAAG;AACjB,YAAI,UAAU,CAAC;AACf,iBAAS,IAAI,GAAG,IAAI,GAAG,SAAS,EAAE,QAAQ,IAAI,GAAG,KAAK;AACpD,cAAI,GAAG,SAAS,EAAE,CAAC,EAAE,MAAM,SAAS;AAClC,oBAAQ,KAAK,GAAG,SAAS,EAAE,CAAC,CAAC;AAAA,UAC/B;AAAA,QACF;AACA,WAAG,SAAS,IAAI;AAAA,MAClB;AACA,UAAI,GAAG,SAAS,KAAK,GAAG,SAAS,EAAE,WAAW,GAAG;AAC/C,eAAO,GAAG,SAAS;AAAA,MACrB;AAAA,IACF,OAAO;AACL,aAAO,GAAG,SAAS;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,UAAS,UAAU,UAAU,SAAU,WAAW;AAChD,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IAC7B;AACA,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,KAAK,WAAW,SAAS;AAClC,QAAI,iBAAiB,KAAK;AAC1B,QAAI,IAAI;AACN,UAAI,SAAS,KAAK;AAClB,UAAIC,OAAM,GAAG;AACb,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,YAAI,QAAQ,GAAG,CAAC;AAChB,YAAI,kBAAkB,eAAe,UAAU,MAAM,SAAS,QAAQ,CAAC,eAAe,OAAO,WAAW,MAAM,KAAK,GAAG;AACpH;AAAA,QACF;AACA,gBAAQ,QAAQ;AAAA,UACd,KAAK;AACH,kBAAM,EAAE,KAAK,MAAM,GAAG;AACtB;AAAA,UACF,KAAK;AACH,kBAAM,EAAE,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC;AAC/B;AAAA,UACF,KAAK;AACH,kBAAM,EAAE,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AACxC;AAAA,UACF;AACE,kBAAM,EAAE,MAAM,MAAM,KAAK,IAAI;AAC7B;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,sBAAkB,eAAe,gBAAgB,eAAe,aAAa,SAAS;AACtF,WAAO;AAAA,EACT;AACA,EAAAD,UAAS,UAAU,qBAAqB,SAAU,MAAM;AACtD,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IAC7B;AACA,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,KAAK,WAAW,IAAI;AAC7B,QAAI,iBAAiB,KAAK;AAC1B,QAAI,IAAI;AACN,UAAI,SAAS,KAAK;AAClB,UAAI,MAAM,KAAK,SAAS,CAAC;AACzB,UAAIC,OAAM,GAAG;AACb,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,YAAI,QAAQ,GAAG,CAAC;AAChB,YAAI,kBAAkB,eAAe,UAAU,MAAM,SAAS,QAAQ,CAAC,eAAe,OAAO,MAAM,MAAM,KAAK,GAAG;AAC/G;AAAA,QACF;AACA,gBAAQ,QAAQ;AAAA,UACd,KAAK;AACH,kBAAM,EAAE,KAAK,GAAG;AAChB;AAAA,UACF,KAAK;AACH,kBAAM,EAAE,KAAK,KAAK,KAAK,CAAC,CAAC;AACzB;AAAA,UACF,KAAK;AACH,kBAAM,EAAE,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAClC;AAAA,UACF;AACE,kBAAM,EAAE,MAAM,KAAK,KAAK,MAAM,GAAG,SAAS,CAAC,CAAC;AAC5C;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,sBAAkB,eAAe,gBAAgB,eAAe,aAAa,IAAI;AACjF,WAAO;AAAA,EACT;AACA,SAAOD;AACT,EAAE;AACF,IAAO,mBAAQ;;;ACnJf,IAAI,iBAAiB,CAAC;AACf,SAAS,SAAS,MAAM,MAAM;AACnC,SAAO,QAAQ;AACf,MAAI,cAAc,eAAe,IAAI;AACrC,MAAI,CAAC,aAAa;AAChB,kBAAc,eAAe,IAAI,IAAI,IAAI,YAAI,GAAG;AAAA,EAClD;AACA,MAAI,QAAQ,YAAY,IAAI,IAAI;AAChC,MAAI,SAAS,MAAM;AACjB,YAAQ,YAAY,YAAY,MAAM,IAAI,EAAE;AAC5C,gBAAY,IAAI,MAAM,KAAK;AAAA,EAC7B;AACA,SAAO;AACT;AACO,SAAS,qBAAqB,MAAM,MAAM,WAAW,cAAc;AACxE,MAAI,QAAQ,SAAS,MAAM,IAAI;AAC/B,MAAI,SAAS,cAAc,IAAI;AAC/B,MAAI,IAAI,YAAY,GAAG,OAAO,SAAS;AACvC,MAAI,IAAIE,aAAY,GAAG,QAAQ,YAAY;AAC3C,MAAI,OAAO,IAAI,qBAAa,GAAG,GAAG,OAAO,MAAM;AAC/C,SAAO;AACT;AACO,SAAS,gBAAgB,MAAM,MAAM,WAAW,cAAc;AACnE,MAAI,cAAc,QAAQ,MAAM,IAAI,MAAM,IAAI;AAC9C,MAAIC,OAAM,UAAU;AACpB,MAAIA,SAAQ,GAAG;AACb,WAAO,qBAAqB,UAAU,CAAC,GAAG,MAAM,WAAW,YAAY;AAAA,EACzE,OAAO;AACL,QAAI,aAAa,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,OAAO,qBAAqB,UAAU,CAAC,GAAG,MAAM,WAAW,YAAY;AAC3E,YAAM,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,MAAM,IAAI;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AACF;AACO,SAAS,YAAY,GAAG,OAAO,WAAW;AAC/C,MAAI,cAAc,SAAS;AACzB,SAAK;AAAA,EACP,WAAW,cAAc,UAAU;AACjC,SAAK,QAAQ;AAAA,EACf;AACA,SAAO;AACT;AACO,SAASD,aAAY,GAAG,QAAQ,eAAe;AACpD,MAAI,kBAAkB,UAAU;AAC9B,SAAK,SAAS;AAAA,EAChB,WAAW,kBAAkB,UAAU;AACrC,SAAK;AAAA,EACP;AACA,SAAO;AACT;AACO,SAAS,cAAc,MAAM;AAClC,SAAO,SAAS,KAAK,IAAI;AAC3B;AAIO,SAAS,aAAa,OAAO,UAAU;AAC5C,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,MAAM,YAAY,GAAG,KAAK,GAAG;AAC/B,aAAO,WAAW,KAAK,IAAI,MAAM;AAAA,IACnC;AACA,WAAO,WAAW,KAAK;AAAA,EACzB;AACA,SAAO;AACT;AACO,SAAS,sBAAsB,KAAK,MAAM,MAAM;AACrD,MAAI,eAAe,KAAK,YAAY;AACpC,MAAIE,YAAW,KAAK,YAAY,OAAO,KAAK,WAAW;AACvD,MAAI,SAAS,KAAK;AAClB,MAAI,QAAQ,KAAK;AACjB,MAAI,aAAa,SAAS;AAC1B,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,MAAI,YAAY;AAChB,MAAI,oBAAoB;AACxB,MAAI,wBAAwB,OAAO;AACjC,SAAK,aAAa,aAAa,CAAC,GAAG,KAAK,KAAK;AAC7C,SAAK,aAAa,aAAa,CAAC,GAAG,KAAK,MAAM;AAC9C,gBAAY;AACZ,wBAAoB;AAAA,EACtB,OAAO;AACL,YAAQ,cAAc;AAAA,MACpB,KAAK;AACH,aAAKA;AACL,aAAK;AACL,oBAAY;AACZ,4BAAoB;AACpB;AAAA,MACF,KAAK;AACH,aAAKA,YAAW;AAChB,aAAK;AACL,4BAAoB;AACpB;AAAA,MACF,KAAK;AACH,aAAK,QAAQ;AACb,aAAKA;AACL,oBAAY;AACZ,4BAAoB;AACpB;AAAA,MACF,KAAK;AACH,aAAK,QAAQ;AACb,aAAK,SAASA;AACd,oBAAY;AACZ;AAAA,MACF,KAAK;AACH,aAAK,QAAQ;AACb,aAAK;AACL,oBAAY;AACZ,4BAAoB;AACpB;AAAA,MACF,KAAK;AACH,aAAKA;AACL,aAAK;AACL,4BAAoB;AACpB;AAAA,MACF,KAAK;AACH,aAAK,QAAQA;AACb,aAAK;AACL,oBAAY;AACZ,4BAAoB;AACpB;AAAA,MACF,KAAK;AACH,aAAK,QAAQ;AACb,aAAKA;AACL,oBAAY;AACZ;AAAA,MACF,KAAK;AACH,aAAK,QAAQ;AACb,aAAK,SAASA;AACd,oBAAY;AACZ,4BAAoB;AACpB;AAAA,MACF,KAAK;AACH,aAAKA;AACL,aAAKA;AACL;AAAA,MACF,KAAK;AACH,aAAK,QAAQA;AACb,aAAKA;AACL,oBAAY;AACZ;AAAA,MACF,KAAK;AACH,aAAKA;AACL,aAAK,SAASA;AACd,4BAAoB;AACpB;AAAA,MACF,KAAK;AACH,aAAK,QAAQA;AACb,aAAK,SAASA;AACd,oBAAY;AACZ,4BAAoB;AACpB;AAAA,IACJ;AAAA,EACF;AACA,QAAM,OAAO,CAAC;AACd,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AACZ,MAAI,gBAAgB;AACpB,SAAO;AACT;;;ACpKA,IAAI,MAAM;AACV,IAAI,YAAI,iBAAiB;AACvB,QAAM,KAAK,IAAI,OAAO,oBAAoB,OAAO,UAAU,OAAO,OAAO,aAAa,OAAO,OAAO,eAAe,GAAG,CAAC;AACzH;AAEO,IAAI,mBAAmB;AACvB,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;;;ACV1B,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;;;ACOxB,IAAI,yBAAyB;AACpC,IAAI,sBAAsB,oBAAoB,OAAO,CAAC,QAAQ,CAAC;AAC/D,IAAI,yBAAyB,OAAO,qBAAqB,SAAU,KAAK,KAAK;AAC3E,MAAI,GAAG,IAAI;AACX,SAAO;AACT,GAAG;AAAA,EACD,QAAQ;AACV,CAAC;AACD,IAAI,oBAAoB,CAAC;AACzB,IAAI,kBAAkB,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACjD,IAAI,UAAU,WAAY;AACxB,WAASC,SAAQ,OAAO;AACtB,SAAK,KAAK,KAAK;AACf,SAAK,YAAY,CAAC;AAClB,SAAK,gBAAgB,CAAC;AACtB,SAAK,SAAS,CAAC;AACf,SAAK,MAAM,KAAK;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,QAAQ,SAAU,OAAO;AACzC,SAAK,KAAK,KAAK;AAAA,EACjB;AACA,EAAAA,SAAQ,UAAU,QAAQ,SAAU,IAAI,IAAI,GAAG;AAC7C,YAAQ,KAAK,WAAW;AAAA,MACtB,KAAK;AACH,aAAK;AACL;AAAA,MACF,KAAK;AACH,aAAK;AACL;AAAA,IACJ;AACA,QAAI,IAAI,KAAK;AACb,QAAI,CAAC,GAAG;AACN,UAAI,KAAK,YAAY,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IACxC;AACA,MAAE,CAAC,KAAK;AACR,MAAE,CAAC,KAAK;AACR,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,eAAe,WAAY;AAAA,EAAC;AAC9C,EAAAA,SAAQ,UAAU,cAAc,WAAY;AAAA,EAAC;AAC7C,EAAAA,SAAQ,UAAU,SAAS,WAAY;AACrC,SAAK,gBAAgB;AACrB,QAAI,KAAK,SAAS;AAChB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,kBAAkB,SAAU,aAAa;AACzD,QAAI,SAAS,KAAK;AAClB,QAAI,WAAW,CAAC,OAAO,UAAU,cAAc;AAC7C,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,CAAC;AAAA,MACrB;AACA,UAAI,aAAa,KAAK;AACtB,UAAI,UAAU,WAAW;AACzB,UAAI,qBAAqB,OAAO;AAChC,UAAI,YAAY;AAChB,UAAI,oBAAoB;AACxB,UAAI,mBAAmB;AACvB,yBAAmB,SAAS,UAAU,OAAO;AAC7C,UAAI,cAAc;AAClB,yBAAmB,cAAc,MAAM;AACvC,UAAI,WAAW,YAAY,MAAM;AAC/B,YAAI,aAAa;AACjB,YAAI,WAAW,YAAY;AACzB,qBAAW,KAAK,WAAW,UAAU;AAAA,QACvC,OAAO;AACL,qBAAW,KAAK,KAAK,gBAAgB,CAAC;AAAA,QACxC;AACA,YAAI,CAAC,SAAS;AACZ,qBAAW,eAAe,KAAK,SAAS;AAAA,QAC1C;AACA,YAAI,KAAK,uBAAuB;AAC9B,eAAK,sBAAsB,mBAAmB,YAAY,UAAU;AAAA,QACtE,OAAO;AACL,gCAAsB,mBAAmB,YAAY,UAAU;AAAA,QACjE;AACA,2BAAmB,IAAI,kBAAkB;AACzC,2BAAmB,IAAI,kBAAkB;AACzC,oBAAY,kBAAkB;AAC9B,4BAAoB,kBAAkB;AACtC,YAAI,aAAa,WAAW;AAC5B,YAAI,cAAc,WAAW,YAAY,MAAM;AAC7C,cAAI,aAAa;AACjB,cAAI,aAAa;AACjB,cAAI,eAAe,UAAU;AAC3B,yBAAa,WAAW,QAAQ;AAChC,yBAAa,WAAW,SAAS;AAAA,UACnC,OAAO;AACL,yBAAa,aAAa,WAAW,CAAC,GAAG,WAAW,KAAK;AACzD,yBAAa,aAAa,WAAW,CAAC,GAAG,WAAW,MAAM;AAAA,UAC5D;AACA,wBAAc;AACd,6BAAmB,UAAU,CAAC,mBAAmB,IAAI,cAAc,UAAU,IAAI,WAAW;AAC5F,6BAAmB,UAAU,CAAC,mBAAmB,IAAI,cAAc,UAAU,IAAI,WAAW;AAAA,QAC9F;AAAA,MACF;AACA,UAAI,WAAW,YAAY,MAAM;AAC/B,2BAAmB,WAAW,WAAW;AAAA,MAC3C;AACA,UAAI,aAAa,WAAW;AAC5B,UAAI,YAAY;AACd,2BAAmB,KAAK,WAAW,CAAC;AACpC,2BAAmB,KAAK,WAAW,CAAC;AACpC,YAAI,CAAC,aAAa;AAChB,6BAAmB,UAAU,CAAC,WAAW,CAAC;AAC1C,6BAAmB,UAAU,CAAC,WAAW,CAAC;AAAA,QAC5C;AAAA,MACF;AACA,UAAI,WAAW,WAAW,UAAU,OAAO,OAAO,WAAW,aAAa,YAAY,WAAW,SAAS,QAAQ,QAAQ,KAAK,IAAI,WAAW;AAC9I,UAAI,wBAAwB,KAAK,2BAA2B,KAAK,yBAAyB,CAAC;AAC3F,UAAI,WAAW;AACf,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,UAAI,YAAY,KAAK,gBAAgB,GAAG;AACtC,mBAAW,WAAW;AACtB,qBAAa,WAAW;AACxB,YAAI,YAAY,QAAQ,aAAa,QAAQ;AAC3C,qBAAW,KAAK,kBAAkB;AAAA,QACpC;AACA,YAAI,cAAc,QAAQ,eAAe,QAAQ;AAC/C,uBAAa,KAAK,oBAAoB,QAAQ;AAC9C,uBAAa;AAAA,QACf;AAAA,MACF,OAAO;AACL,mBAAW,WAAW;AACtB,qBAAa,WAAW;AACxB,YAAI,YAAY,QAAQ,aAAa,QAAQ;AAC3C,qBAAW,KAAK,eAAe;AAAA,QACjC;AACA,YAAI,cAAc,QAAQ,eAAe,QAAQ;AAC/C,uBAAa,KAAK,iBAAiB,QAAQ;AAC3C,uBAAa;AAAA,QACf;AAAA,MACF;AACA,iBAAW,YAAY;AACvB,UAAI,aAAa,sBAAsB,QAAQ,eAAe,sBAAsB,UAAU,eAAe,sBAAsB,cAAc,cAAc,sBAAsB,SAAS,sBAAsB,sBAAsB,eAAe;AACvP,2BAAmB;AACnB,8BAAsB,OAAO;AAC7B,8BAAsB,SAAS;AAC/B,8BAAsB,aAAa;AACnC,8BAAsB,QAAQ;AAC9B,8BAAsB,gBAAgB;AACtC,eAAO,oBAAoB,qBAAqB;AAAA,MAClD;AACA,aAAO,WAAW;AAClB,UAAI,kBAAkB;AACpB,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,kBAAkB,WAAY;AAC9C,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,UAAU,oBAAoB,WAAY;AAChD,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,UAAU,sBAAsB,SAAU,UAAU;AAC1D,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,UAAU,iBAAiB,WAAY;AAC7C,WAAO,KAAK,QAAQ,KAAK,KAAK,WAAW,IAAI,oBAAoB;AAAA,EACnE;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,UAAU;AACvD,QAAI,kBAAkB,KAAK,QAAQ,KAAK,KAAK,mBAAmB;AAChE,QAAI,WAAW,OAAO,oBAAoB,YAAY,MAAM,eAAe;AAC3E,QAAI,CAAC,UAAU;AACb,iBAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,IAC9B;AACA,QAAI,QAAQ,SAAS,CAAC;AACtB,QAAI,SAAS,KAAK,KAAK,WAAW;AAClC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAS,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI,QAAQ,IAAI;AAAA,IAChE;AACA,aAAS,CAAC,IAAI;AACd,WAAO,UAAU,UAAU,MAAM;AAAA,EACnC;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,IAAI,SAAS;AAAA,EAAC;AACrD,EAAAA,SAAQ,UAAU,SAAS,SAAU,KAAK,OAAO;AAC/C,QAAI,QAAQ,cAAc;AACxB,WAAK,cAAc,KAAK;AAAA,IAC1B,WAAW,QAAQ,eAAe;AAChC,WAAK,eAAe,KAAK;AAAA,IAC3B,WAAW,QAAQ,YAAY;AAC7B,WAAK,YAAY,KAAK;AAAA,IACxB,WAAW,QAAQ,SAAS;AAC1B,WAAK,QAAQ,KAAK,SAAS,CAAC;AAC5B,aAAO,KAAK,OAAO,KAAK;AAAA,IAC1B,OAAO;AACL,WAAK,GAAG,IAAI;AAAA,IACd;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,OAAO,WAAY;AACnC,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,OAAO,WAAY;AACnC,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,OAAO,SAAU,UAAU,OAAO;AAClD,QAAI,OAAO,aAAa,UAAU;AAChC,WAAK,OAAO,UAAU,KAAK;AAAA,IAC7B,WAAW,SAAS,QAAQ,GAAG;AAC7B,UAAI,MAAM;AACV,UAAI,UAAU,KAAK,GAAG;AACtB,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,MAAM,QAAQ,CAAC;AACnB,aAAK,OAAO,KAAK,SAAS,GAAG,CAAC;AAAA,MAChC;AAAA,IACF;AACA,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,UAAU,2BAA2B,SAAU,SAAS;AAC9D,SAAK,mBAAmB,OAAO;AAC/B,QAAI,cAAc,KAAK;AACvB,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,UAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,UAAI,sBAAsB,SAAS;AACnC,UAAI,SAAS,QAAQ,KAAK,uBAAuB,wBAAwB,wBAAwB;AAC/F;AAAA,MACF;AACA,UAAI,aAAa,SAAS;AAC1B,UAAI,SAAS,aAAa,YAAY,UAAU,IAAI;AACpD,eAAS,OAAO,MAAM;AAAA,IACxB;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,qBAAqB,SAAU,SAAS;AACxD,QAAI,cAAc,KAAK;AACvB,QAAI,CAAC,aAAa;AAChB,oBAAc,KAAK,eAAe,CAAC;AAAA,IACrC;AACA,QAAI,QAAQ,cAAc,CAAC,YAAY,YAAY;AACjD,kBAAY,aAAa,KAAK;AAAA,IAChC;AACA,SAAK,qBAAqB,SAAS,aAAa,mBAAmB;AAAA,EACrE;AACA,EAAAA,SAAQ,UAAU,uBAAuB,SAAU,SAAS,aAAa,aAAa;AACpF,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAI,MAAM,YAAY,CAAC;AACvB,UAAI,QAAQ,GAAG,KAAK,QAAQ,EAAE,OAAO,cAAc;AACjD,oBAAY,GAAG,IAAI,KAAK,GAAG;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,WAAW,WAAY;AACvC,WAAO,KAAK,cAAc,SAAS;AAAA,EACrC;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,MAAM;AAC3C,WAAO,KAAK,OAAO,IAAI;AAAA,EACzB;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,MAAM;AAC9C,QAAI,SAAS,KAAK;AAClB,QAAI,CAAC,OAAO,IAAI,GAAG;AACjB,aAAO,IAAI,IAAI,CAAC;AAAA,IAClB;AACA,WAAO,OAAO,IAAI;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,aAAa;AACrD,SAAK,SAAS,wBAAwB,OAAO,WAAW;AAAA,EAC1D;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,WAAW,mBAAmB,aAAa,oBAAoB;AACpG,QAAI,gBAAgB,cAAc;AAClC,QAAI,YAAY,KAAK,SAAS;AAC9B,QAAI,CAAC,aAAa,eAAe;AAC/B;AAAA,IACF;AACA,QAAI,gBAAgB,KAAK;AACzB,QAAI,eAAe,KAAK;AACxB,QAAI,QAAQ,eAAe,SAAS,KAAK,MAAM,qBAAqB,cAAc,WAAW,IAAI;AAC/F;AAAA,IACF;AACA,QAAI;AACJ,QAAI,KAAK,cAAc,CAAC,eAAe;AACrC,cAAQ,KAAK,WAAW,SAAS;AAAA,IACnC;AACA,QAAI,CAAC,OAAO;AACV,cAAQ,KAAK,UAAU,KAAK,OAAO,SAAS;AAAA,IAC9C;AACA,QAAI,CAAC,SAAS,CAAC,eAAe;AAC5B,eAAS,WAAW,YAAY,cAAc;AAC9C;AAAA,IACF;AACA,QAAI,CAAC,eAAe;AAClB,WAAK,yBAAyB,KAAK;AAAA,IACrC;AACA,QAAI,gBAAgB,CAAC,EAAE,SAAS,MAAM,cAAc;AACpD,QAAI,eAAe;AACjB,WAAK,sBAAsB,IAAI;AAAA,IACjC;AACA,SAAK,eAAe,WAAW,OAAO,KAAK,cAAc,mBAAmB,CAAC,eAAe,CAAC,KAAK,aAAa,gBAAgB,aAAa,WAAW,GAAG,YAAY;AACtK,QAAI,cAAc,KAAK;AACvB,QAAI,YAAY,KAAK;AACrB,QAAI,aAAa;AACf,kBAAY,SAAS,WAAW,mBAAmB,aAAa,aAAa;AAAA,IAC/E;AACA,QAAI,WAAW;AACb,gBAAU,SAAS,WAAW,mBAAmB,aAAa,aAAa;AAAA,IAC7E;AACA,QAAI,eAAe;AACjB,WAAK,gBAAgB,CAAC;AACtB,WAAK,eAAe,CAAC;AAAA,IACvB,OAAO;AACL,UAAI,CAAC,mBAAmB;AACtB,aAAK,gBAAgB,CAAC,SAAS;AAAA,MACjC,OAAO;AACL,aAAK,cAAc,KAAK,SAAS;AAAA,MACnC;AAAA,IACF;AACA,SAAK,wBAAwB;AAC7B,SAAK,WAAW;AAChB,QAAI,CAAC,iBAAiB,KAAK,WAAW;AACpC,WAAK,sBAAsB,KAAK;AAChC,WAAK,WAAW,CAAC;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,UAAU,YAAY,SAAU,QAAQ,aAAa,oBAAoB;AAC/E,QAAI,CAAC,OAAO,QAAQ;AAClB,WAAK,YAAY;AAAA,IACnB,OAAO;AACL,UAAI,eAAe,CAAC;AACpB,UAAI,gBAAgB,KAAK;AACzB,UAAIC,OAAM,OAAO;AACjB,UAAI,YAAYA,SAAQ,cAAc;AACtC,UAAI,WAAW;AACb,iBAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,cAAI,OAAO,CAAC,MAAM,cAAc,CAAC,GAAG;AAClC,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,WAAW;AACb;AAAA,MACF;AACA,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,YAAI,YAAY,OAAO,CAAC;AACxB,YAAI,WAAW;AACf,YAAI,KAAK,YAAY;AACnB,qBAAW,KAAK,WAAW,WAAW,MAAM;AAAA,QAC9C;AACA,YAAI,CAAC,UAAU;AACb,qBAAW,KAAK,OAAO,SAAS;AAAA,QAClC;AACA,YAAI,UAAU;AACZ,uBAAa,KAAK,QAAQ;AAAA,QAC5B;AAAA,MACF;AACA,UAAI,eAAe,aAAaA,OAAM,CAAC;AACvC,UAAI,gBAAgB,CAAC,EAAE,gBAAgB,aAAa,cAAc;AAClE,UAAI,eAAe;AACjB,aAAK,sBAAsB,IAAI;AAAA,MACjC;AACA,UAAI,cAAc,KAAK,aAAa,YAAY;AAChD,UAAI,eAAe,KAAK;AACxB,WAAK,yBAAyB,WAAW;AACzC,WAAK,eAAe,OAAO,KAAK,GAAG,GAAG,aAAa,KAAK,cAAc,OAAO,CAAC,eAAe,CAAC,KAAK,aAAa,gBAAgB,aAAa,WAAW,GAAG,YAAY;AACvK,UAAI,cAAc,KAAK;AACvB,UAAI,YAAY,KAAK;AACrB,UAAI,aAAa;AACf,oBAAY,UAAU,QAAQ,aAAa,aAAa;AAAA,MAC1D;AACA,UAAI,WAAW;AACb,kBAAU,UAAU,QAAQ,aAAa,aAAa;AAAA,MACxD;AACA,WAAK,wBAAwB;AAC7B,WAAK,gBAAgB,OAAO,MAAM;AAClC,WAAK,WAAW;AAChB,UAAI,CAAC,iBAAiB,KAAK,WAAW;AACpC,aAAK,sBAAsB,KAAK;AAChC,aAAK,WAAW,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,EAAAD,SAAQ,UAAU,0BAA0B,WAAY;AACtD,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,UAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,UAAI,SAAS,YAAY;AACvB,iBAAS,aAAa,KAAK,SAAS,UAAU,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,OAAO;AAC/C,QAAI,MAAM,QAAQ,KAAK,eAAe,KAAK;AAC3C,QAAI,OAAO,GAAG;AACZ,UAAI,gBAAgB,KAAK,cAAc,MAAM;AAC7C,oBAAc,OAAO,KAAK,CAAC;AAC3B,WAAK,UAAU,aAAa;AAAA,IAC9B;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,eAAe,SAAU,UAAU,UAAU,UAAU;AACvE,QAAI,gBAAgB,KAAK,cAAc,MAAM;AAC7C,QAAI,MAAM,QAAQ,eAAe,QAAQ;AACzC,QAAI,iBAAiB,QAAQ,eAAe,QAAQ,KAAK;AACzD,QAAI,OAAO,GAAG;AACZ,UAAI,CAAC,gBAAgB;AACnB,sBAAc,GAAG,IAAI;AAAA,MACvB,OAAO;AACL,sBAAc,OAAO,KAAK,CAAC;AAAA,MAC7B;AAAA,IACF,WAAW,YAAY,CAAC,gBAAgB;AACtC,oBAAc,KAAK,QAAQ;AAAA,IAC7B;AACA,SAAK,UAAU,aAAa;AAAA,EAC9B;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,OAAO,QAAQ;AACvD,QAAI,QAAQ;AACV,WAAK,SAAS,OAAO,IAAI;AAAA,IAC3B,OAAO;AACL,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,eAAe,SAAU,QAAQ;AACjD,QAAI,cAAc,CAAC;AACnB,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,QAAQ,OAAO,CAAC;AACpB,aAAO,aAAa,KAAK;AACzB,UAAI,MAAM,YAAY;AACpB,2BAAmB,oBAAoB,CAAC;AACxC,eAAO,kBAAkB,MAAM,UAAU;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,kBAAkB;AACpB,kBAAY,aAAa;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,UAAU,iBAAiB,SAAU,WAAW,OAAO,aAAa,mBAAmB,YAAY,cAAc;AACvH,QAAI,uBAAuB,EAAE,SAAS;AACtC,QAAI,SAAS,MAAM,YAAY;AAC7B,WAAK,aAAa,OAAO,CAAC,GAAG,oBAAoB,KAAK,aAAa,YAAY,UAAU;AACzF,aAAO,KAAK,YAAY,MAAM,UAAU;AAAA,IAC1C,WAAW,sBAAsB;AAC/B,UAAI,YAAY,YAAY;AAC1B,aAAK,aAAa,YAAY;AAAA,MAChC;AAAA,IACF;AACA,QAAI,mBAAmB,CAAC;AACxB,QAAI,gBAAgB;AACpB,aAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,UAAI,MAAM,oBAAoB,CAAC;AAC/B,UAAI,sBAAsB,cAAc,uBAAuB,GAAG;AAClE,UAAI,SAAS,MAAM,GAAG,KAAK,MAAM;AAC/B,YAAI,qBAAqB;AACvB,0BAAgB;AAChB,2BAAiB,GAAG,IAAI,MAAM,GAAG;AAAA,QACnC,OAAO;AACL,eAAK,GAAG,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACF,WAAW,sBAAsB;AAC/B,YAAI,YAAY,GAAG,KAAK,MAAM;AAC5B,cAAI,qBAAqB;AACvB,4BAAgB;AAChB,6BAAiB,GAAG,IAAI,YAAY,GAAG;AAAA,UACzC,OAAO;AACL,iBAAK,GAAG,IAAI,YAAY,GAAG;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,YAAY;AACf,eAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,YAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,YAAI,aAAa,SAAS;AAC1B,YAAI,CAAC,SAAS,QAAQ,GAAG;AACvB,mBAAS,mBAAmB,cAAc,SAAS,aAAa,UAAU,IAAI,SAAS,WAAW;AAAA,QACpG;AAAA,MACF;AAAA,IACF;AACA,QAAI,eAAe;AACjB,WAAK,iBAAiB,WAAW,kBAAkB,YAAY;AAAA,IACjE;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,aAAa;AAC1D,QAAI,YAAY,QAAQ,CAAC,YAAY,cAAc;AACjD,UAAI,MAAuC;AACzC,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AACA;AAAA,IACF;AACA,QAAI,gBAAgB,MAAM;AACxB,UAAI,MAAuC;AACzC,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AACA;AAAA,IACF;AACA,QAAI,KAAK,KAAK;AACd,QAAI,IAAI;AACN,kBAAY,YAAY,EAAE;AAAA,IAC5B;AACA,gBAAY,OAAO;AACnB,gBAAY,eAAe;AAAA,EAC7B;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,aAAa;AAC1D,QAAI,YAAY,MAAM;AACpB,kBAAY,iBAAiB,YAAY,IAAI;AAAA,IAC/C;AACA,gBAAY,OAAO;AACnB,gBAAY,eAAe;AAAA,EAC7B;AACA,EAAAA,SAAQ,UAAU,cAAc,WAAY;AAC1C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,UAAU;AAClD,QAAI,KAAK,aAAa,KAAK,cAAc,UAAU;AACjD,WAAK,eAAe;AAAA,IACtB;AACA,SAAK,iBAAiB,QAAQ;AAC9B,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,iBAAiB,WAAY;AAC7C,QAAI,WAAW,KAAK;AACpB,QAAI,UAAU;AACZ,WAAK,iBAAiB,QAAQ;AAC9B,WAAK,YAAY;AACjB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,iBAAiB,WAAY;AAC7C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,SAAQ,UAAU,iBAAiB,SAAU,QAAQ;AACnD,QAAI,sBAAsB,KAAK;AAC/B,QAAI,wBAAwB,QAAQ;AAClC;AAAA,IACF;AACA,QAAI,uBAAuB,wBAAwB,QAAQ;AACzD,WAAK,kBAAkB;AAAA,IACzB;AACA,QAAI,MAAuC;AACzC,UAAI,OAAO,QAAQ,CAAC,OAAO,cAAc;AACvC,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AAAA,IACF;AACA,WAAO,qBAAqB,IAAI,sBAAc;AAC9C,SAAK,iBAAiB,MAAM;AAC5B,SAAK,eAAe;AACpB,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,gBAAgB,SAAU,KAAK;AAC/C,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,CAAC;AAAA,IACrB;AACA,WAAO,KAAK,YAAY,GAAG;AAC3B,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,mBAAmB,WAAY;AAC/C,SAAK,aAAa;AAClB,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,oBAAoB,WAAY;AAChD,QAAI,SAAS,KAAK;AAClB,QAAI,QAAQ;AACV,aAAO,qBAAqB;AAC5B,WAAK,iBAAiB,MAAM;AAC5B,WAAK,eAAe;AACpB,WAAK,yBAAyB;AAC9B,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,mBAAmB,WAAY;AAC/C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,WAAW;AACxD,QAAI,KAAK,cAAc,KAAK,eAAe,WAAW;AACpD,WAAK,oBAAoB;AAAA,IAC3B;AACA,SAAK,iBAAiB,SAAS;AAC/B,SAAK,aAAa;AAClB,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,sBAAsB,WAAY;AAClD,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACb,WAAK,iBAAiB,SAAS;AAC/B,WAAK,aAAa;AAClB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,aAAa,WAAY;AACzC,SAAK,WAAW;AAChB,QAAI,KAAK,KAAK;AACd,QAAI,IAAI;AACN,UAAI,KAAK,WAAW;AAClB,WAAG,aAAa;AAAA,MAClB,OAAO;AACL,WAAG,QAAQ;AAAA,MACb;AAAA,IACF;AACA,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,WAAW;AAAA,IAC/B;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,QAAQ,WAAY;AACpC,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,wBAAwB,SAAU,SAAS;AAC3D,SAAK,YAAY;AACjB,QAAI,cAAc,KAAK;AACvB,QAAI,YAAY,KAAK;AACrB,QAAI,aAAa;AACf,kBAAY,YAAY;AAAA,IAC1B;AACA,QAAI,WAAW;AACb,gBAAU,YAAY;AAAA,IACxB;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,IAAI;AAC5C,QAAI,KAAK,SAAS,IAAI;AACpB;AAAA,IACF;AACA,SAAK,OAAO;AACZ,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACb,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,WAAG,UAAU,YAAY,UAAU,CAAC,CAAC;AAAA,MACvC;AAAA,IACF;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,YAAY,EAAE;AAAA,IAC/B;AACA,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,YAAY,EAAE;AAAA,IAClC;AACA,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,YAAY,EAAE;AAAA,IAChC;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,IAAI;AACjD,QAAI,CAAC,KAAK,MAAM;AACd;AAAA,IACF;AACA,SAAK,OAAO;AACZ,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACb,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,WAAG,UAAU,eAAe,UAAU,CAAC,CAAC;AAAA,MAC1C;AAAA,IACF;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,iBAAiB,EAAE;AAAA,IACpC;AACA,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,iBAAiB,EAAE;AAAA,IACvC;AACA,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,iBAAiB,EAAE;AAAA,IACrC;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,UAAU,SAAU,KAAK,MAAM,wBAAwB;AACvE,QAAI,SAAS,MAAM,KAAK,GAAG,IAAI;AAC/B,QAAI,MAAuC;AACzC,UAAI,CAAC,QAAQ;AACX,iBAAS,eAAe,MAAM,iCAAiC,KAAK,EAAE;AACtE;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW,IAAI,iBAAS,QAAQ,MAAM,sBAAsB;AAChE,YAAQ,SAAS,aAAa;AAC9B,SAAK,YAAY,UAAU,GAAG;AAC9B,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,UAAU,KAAK;AACvD,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AACT,aAAS,OAAO,WAAY;AAC1B,SAAG,sBAAsB,GAAG;AAAA,IAC9B,CAAC,EAAE,KAAK,WAAY;AAClB,UAAI,YAAY,GAAG;AACnB,UAAI,MAAM,QAAQ,WAAW,QAAQ;AACrC,UAAI,OAAO,GAAG;AACZ,kBAAU,OAAO,KAAK,CAAC;AAAA,MACzB;AAAA,IACF,CAAC;AACD,SAAK,UAAU,KAAK,QAAQ;AAC5B,QAAI,IAAI;AACN,SAAG,UAAU,YAAY,QAAQ;AAAA,IACnC;AACA,UAAM,GAAG,OAAO;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,wBAAwB,SAAU,KAAK;AACvD,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,gBAAgB,SAAU,OAAO,eAAe;AAChE,QAAI,YAAY,KAAK;AACrB,QAAIC,OAAM,UAAU;AACpB,QAAI,gBAAgB,CAAC;AACrB,aAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,UAAI,WAAW,UAAU,CAAC;AAC1B,UAAI,CAAC,SAAS,UAAU,SAAS,OAAO;AACtC,iBAAS,KAAK,aAAa;AAAA,MAC7B,OAAO;AACL,sBAAc,KAAK,QAAQ;AAAA,MAC7B;AAAA,IACF;AACA,SAAK,YAAY;AACjB,WAAO;AAAA,EACT;AACA,EAAAD,SAAQ,UAAU,YAAY,SAAU,QAAQ,KAAK,gBAAgB;AACnE,cAAU,MAAM,QAAQ,KAAK,cAAc;AAAA,EAC7C;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,QAAQ,KAAK,gBAAgB;AACrE,cAAU,MAAM,QAAQ,KAAK,gBAAgB,IAAI;AAAA,EACnD;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,WAAW,QAAQ,KAAK,gBAAgB;AACrF,QAAI,YAAY,UAAU,MAAM,QAAQ,KAAK,cAAc;AAC3D,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAU,CAAC,EAAE,wBAAwB;AAAA,IACvC;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,kBAAkB,WAAY;AAC9C,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,UAAU,eAAe,WAAY;AAC3C,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,mBAAmB,WAAY;AACrC,QAAI,UAAUA,SAAQ;AACtB,YAAQ,OAAO;AACf,YAAQ,OAAO;AACf,YAAQ,SAAS,QAAQ,SAAS,QAAQ,UAAU,QAAQ,YAAY,QAAQ,WAAW,QAAQ,aAAa,QAAQ,YAAY;AACpI,YAAQ,UAAU;AAClB,QAAI,OAAO,CAAC;AACZ,aAAS,mBAAmB,KAAK,MAAM,MAAM;AAC3C,UAAI,CAAC,KAAK,MAAM,OAAO,IAAI,GAAG;AAC5B,gBAAQ,KAAK,kBAAkB,MAAM,iCAAiC,OAAO,SAAS,OAAO,WAAW;AACxG,aAAK,MAAM,OAAO,IAAI,IAAI;AAAA,MAC5B;AAAA,IACF;AACA,aAAS,qBAAqB,KAAK,YAAY,MAAM,MAAM;AACzD,aAAO,eAAe,SAAS,KAAK;AAAA,QAClC,KAAK,WAAY;AACf,cAAI,MAAuC;AACzC,+BAAmB,KAAK,MAAM,IAAI;AAAA,UACpC;AACA,cAAI,CAAC,KAAK,UAAU,GAAG;AACrB,gBAAI,MAAM,KAAK,UAAU,IAAI,CAAC;AAC9B,yBAAa,MAAM,GAAG;AAAA,UACxB;AACA,iBAAO,KAAK,UAAU;AAAA,QACxB;AAAA,QACA,KAAK,SAAU,KAAK;AAClB,cAAI,MAAuC;AACzC,+BAAmB,KAAK,MAAM,IAAI;AAAA,UACpC;AACA,eAAK,IAAI,IAAI,IAAI,CAAC;AAClB,eAAK,IAAI,IAAI,IAAI,CAAC;AAClB,eAAK,UAAU,IAAI;AACnB,uBAAa,MAAM,GAAG;AAAA,QACxB;AAAA,MACF,CAAC;AACD,eAAS,aAAaE,OAAM,KAAK;AAC/B,eAAO,eAAe,KAAK,GAAG;AAAA,UAC5B,KAAK,WAAY;AACf,mBAAOA,MAAK,IAAI;AAAA,UAClB;AAAA,UACA,KAAK,SAAU,KAAK;AAClB,YAAAA,MAAK,IAAI,IAAI;AAAA,UACf;AAAA,QACF,CAAC;AACD,eAAO,eAAe,KAAK,GAAG;AAAA,UAC5B,KAAK,WAAY;AACf,mBAAOA,MAAK,IAAI;AAAA,UAClB;AAAA,UACA,KAAK,SAAU,KAAK;AAClB,YAAAA,MAAK,IAAI,IAAI;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,OAAO,gBAAgB;AACzB,2BAAqB,YAAY,cAAc,KAAK,GAAG;AACvD,2BAAqB,SAAS,gBAAgB,UAAU,QAAQ;AAChE,2BAAqB,UAAU,iBAAiB,WAAW,SAAS;AAAA,IACtE;AAAA,EACF,EAAE;AACF,SAAOF;AACT,EAAE;AACF,MAAM,SAAS,gBAAQ;AACvB,MAAM,SAAS,qBAAa;AAC5B,SAAS,UAAU,YAAY,QAAQ,KAAK,gBAAgB,SAAS;AACnE,QAAM,OAAO,CAAC;AACd,MAAI,YAAY,CAAC;AACjB,mBAAiB,YAAY,IAAI,YAAY,QAAQ,KAAK,gBAAgB,WAAW,OAAO;AAC5F,MAAI,cAAc,UAAU;AAC5B,MAAI,eAAe;AACnB,MAAI,UAAU,IAAI;AAClB,MAAI,aAAa,IAAI;AACrB,MAAI,SAAS,WAAY;AACvB,mBAAe;AACf;AACA,QAAI,eAAe,GAAG;AACpB,qBAAe,WAAW,QAAQ,IAAI,cAAc,WAAW;AAAA,IACjE;AAAA,EACF;AACA,MAAI,YAAY,WAAY;AAC1B;AACA,QAAI,eAAe,GAAG;AACpB,qBAAe,WAAW,QAAQ,IAAI,cAAc,WAAW;AAAA,IACjE;AAAA,EACF;AACA,MAAI,CAAC,aAAa;AAChB,eAAW,QAAQ;AAAA,EACrB;AACA,MAAI,UAAU,SAAS,KAAK,IAAI,QAAQ;AACtC,cAAU,CAAC,EAAE,OAAO,SAAUG,SAAQ,SAAS;AAC7C,UAAI,OAAO,OAAO;AAAA,IACpB,CAAC;AAAA,EACH;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,WAAW,UAAU,CAAC;AAC1B,QAAI,QAAQ;AACV,eAAS,KAAK,MAAM;AAAA,IACtB;AACA,QAAI,WAAW;AACb,eAAS,QAAQ,SAAS;AAAA,IAC5B;AACA,QAAI,IAAI,OAAO;AACb,eAAS,SAAS,IAAI,QAAQ;AAAA,IAChC;AACA,aAAS,MAAM,IAAI,MAAM;AAAA,EAC3B;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,QAAQF,MAAK;AAC3C,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,WAAO,CAAC,IAAI,OAAO,CAAC;AAAA,EACtB;AACF;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,YAAY,MAAM,CAAC,CAAC;AAC7B;AACA,SAAS,UAAU,QAAQ,QAAQ,KAAK;AACtC,MAAI,YAAY,OAAO,GAAG,CAAC,GAAG;AAC5B,QAAI,CAAC,YAAY,OAAO,GAAG,CAAC,GAAG;AAC7B,aAAO,GAAG,IAAI,CAAC;AAAA,IACjB;AACA,QAAI,aAAa,OAAO,GAAG,CAAC,GAAG;AAC7B,UAAIA,OAAM,OAAO,GAAG,EAAE;AACtB,UAAI,OAAO,GAAG,EAAE,WAAWA,MAAK;AAC9B,eAAO,GAAG,IAAI,IAAI,OAAO,GAAG,EAAE,YAAYA,IAAG;AAC7C,uBAAe,OAAO,GAAG,GAAG,OAAO,GAAG,GAAGA,IAAG;AAAA,MAC9C;AAAA,IACF,OAAO;AACL,UAAI,YAAY,OAAO,GAAG;AAC1B,UAAI,YAAY,OAAO,GAAG;AAC1B,UAAI,OAAO,UAAU;AACrB,UAAI,UAAU,SAAS,GAAG;AACxB,YAAI,OAAO,UAAU,CAAC,EAAE;AACxB,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,cAAI,CAAC,UAAU,CAAC,GAAG;AACjB,sBAAU,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,UACxD,OAAO;AACL,2BAAe,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI;AAAA,UACjD;AAAA,QACF;AAAA,MACF,OAAO;AACL,uBAAe,WAAW,WAAW,IAAI;AAAA,MAC3C;AACA,gBAAU,SAAS,UAAU;AAAA,IAC/B;AAAA,EACF,OAAO;AACL,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AACF;AACA,SAAS,YAAY,MAAM,MAAM;AAC/B,SAAO,SAAS,QAAQ,YAAY,IAAI,KAAK,YAAY,IAAI,KAAK,cAAc,MAAM,IAAI;AAC5F;AACA,SAAS,cAAc,MAAM,MAAM;AACjC,MAAIA,OAAM,KAAK;AACf,MAAIA,SAAQ,KAAK,QAAQ;AACvB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,QAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,YAAY,QAAQ,YAAY,QAAQ,KAAK,gBAAgB,WAAW,SAAS;AACzG,MAAI,aAAa,KAAK,MAAM;AAC5B,MAAI,WAAW,IAAI;AACnB,MAAI,QAAQ,IAAI;AAChB,MAAI,WAAW,IAAI;AACnB,MAAI,aAAa,IAAI;AACrB,MAAI,aAAa,CAAC,SAAS,cAAc;AACzC,MAAI,kBAAkB,WAAW;AACjC,MAAI,gBAAgB,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,QAAI,WAAW,WAAW,CAAC;AAC3B,QAAI,YAAY,OAAO,QAAQ;AAC/B,QAAI,aAAa,QAAQ,WAAW,QAAQ,KAAK,SAAS,cAAc,eAAe,QAAQ,IAAI;AACjG,UAAI,SAAS,SAAS,KAAK,CAAC,YAAY,SAAS,KAAK,CAAC,iBAAiB,SAAS,GAAG;AAClF,YAAI,QAAQ;AACV,cAAI,CAAC,SAAS;AACZ,uBAAW,QAAQ,IAAI;AACvB,uBAAW,sBAAsB,MAAM;AAAA,UACzC;AACA;AAAA,QACF;AACA,yBAAiB,YAAY,UAAU,WAAW,QAAQ,GAAG,WAAW,KAAK,kBAAkB,eAAe,QAAQ,GAAG,WAAW,OAAO;AAAA,MAC7I,OAAO;AACL,sBAAc,KAAK,QAAQ;AAAA,MAC7B;AAAA,IACF,WAAW,CAAC,SAAS;AACnB,iBAAW,QAAQ,IAAI;AACvB,iBAAW,sBAAsB,MAAM;AACvC,oBAAc,KAAK,QAAQ;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,SAAS,cAAc;AAC3B,MAAI,CAAC,YAAY,QAAQ;AACvB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,UAAI,WAAW,gBAAgB,CAAC;AAChC,UAAI,SAAS,eAAe,QAAQ;AAClC,YAAI,aAAa,SAAS,WAAW,aAAa;AAClD,YAAI,YAAY;AACd,cAAI,MAAM,QAAQ,iBAAiB,QAAQ;AAC3C,0BAAgB,OAAO,KAAK,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,IAAI,OAAO;AACd,oBAAgB,OAAO,eAAe,SAAU,KAAK;AACnD,aAAO,CAAC,YAAY,OAAO,GAAG,GAAG,WAAW,GAAG,CAAC;AAAA,IAClD,CAAC;AACD,aAAS,cAAc;AAAA,EACzB;AACA,MAAI,SAAS,KAAK,IAAI,SAAS,CAAC,UAAU,QAAQ;AAChD,QAAI,iBAAiB;AACrB,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,QAAI,SAAS;AACX,uBAAiB,CAAC;AAClB,UAAI,YAAY;AACd,yBAAiB,CAAC;AAAA,MACpB;AACA,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAI,WAAW,cAAc,CAAC;AAC9B,uBAAe,QAAQ,IAAI,WAAW,QAAQ;AAC9C,YAAI,YAAY;AACd,yBAAe,QAAQ,IAAI,OAAO,QAAQ;AAAA,QAC5C,OAAO;AACL,qBAAW,QAAQ,IAAI,OAAO,QAAQ;AAAA,QACxC;AAAA,MACF;AAAA,IACF,WAAW,YAAY;AACrB,oBAAc,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAI,WAAW,cAAc,CAAC;AAC9B,oBAAY,QAAQ,IAAI,WAAW,WAAW,QAAQ,CAAC;AACvD,kBAAU,YAAY,QAAQ,QAAQ;AAAA,MACxC;AAAA,IACF;AACA,QAAI,WAAW,IAAI,iBAAS,YAAY,OAAO,OAAO,WAAW,OAAO,iBAAiB,SAAUG,WAAU;AAC3G,aAAOA,UAAS,eAAe;AAAA,IACjC,CAAC,IAAI,IAAI;AACT,aAAS,aAAa;AACtB,QAAI,IAAI,OAAO;AACb,eAAS,QAAQ,IAAI;AAAA,IACvB;AACA,QAAI,cAAc,gBAAgB;AAChC,eAAS,aAAa,GAAG,gBAAgB,aAAa;AAAA,IACxD;AACA,QAAI,aAAa;AACf,eAAS,aAAa,GAAG,aAAa,aAAa;AAAA,IACrD;AACA,aAAS,aAAa,YAAY,OAAO,MAAM,UAAU,UAAU,iBAAiB,QAAQ,aAAa,EAAE,MAAM,SAAS,CAAC;AAC3H,eAAW,YAAY,UAAU,MAAM;AACvC,cAAU,KAAK,QAAQ;AAAA,EACzB;AACF;AACA,IAAO,kBAAQ;;;ACt9Bf,IAAI,kBAAkB,gBAAgB,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE;AAC5D,IAAI,uBAAuB;AAAA,EAChC,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,aAAa;AAAA,EACb,SAAS;AAAA,EACT,OAAO;AACT;AACO,IAAI,iCAAiC;AAAA,EAC1C,OAAO;AAAA,IACL,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,eAAe;AAAA,IACf,aAAa;AAAA,IACb,SAAS;AAAA,EACX;AACF;AACA,qBAAqB,eAAe,IAAI;AACxC,IAAIC,uBAAsB,CAAC,KAAK,MAAM,WAAW;AACjD,IAAI,qCAAqC,CAAC,WAAW;AACrD,IAAI,cAAc,SAAU,QAAQ;AAClC,YAAUC,cAAa,MAAM;AAC7B,WAASA,aAAY,OAAO;AAC1B,WAAO,OAAO,KAAK,MAAM,KAAK,KAAK;AAAA,EACrC;AACA,EAAAA,aAAY,UAAU,QAAQ,SAAU,OAAO;AAC7C,QAAI,UAAU,KAAK,KAAK;AACxB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,MAAM,QAAQ,CAAC;AACnB,UAAI,QAAQ,SAAS;AACnB,aAAK,SAAS,MAAM,GAAG,CAAC;AAAA,MAC1B,OAAO;AACL,eAAO,UAAU,OAAO,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC;AAAA,MACpD;AAAA,IACF;AACA,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,SAAS,CAAC,CAAC;AAAA,IAClB;AAAA,EACF;AACA,EAAAA,aAAY,UAAU,cAAc,WAAY;AAAA,EAAC;AACjD,EAAAA,aAAY,UAAU,aAAa,WAAY;AAAA,EAAC;AAChD,EAAAA,aAAY,UAAU,mBAAmB,WAAY;AAAA,EAAC;AACtD,EAAAA,aAAY,UAAU,kBAAkB,WAAY;AAAA,EAAC;AACrD,EAAAA,aAAY,UAAU,kBAAkB,SAAU,WAAW,YAAY,kBAAkB,mBAAmB;AAC5G,QAAI,IAAI,KAAK;AACb,QAAI,KAAK,UAAU,KAAK,aAAa,KAAK,MAAM,YAAY,KAAK,KAAK,WAAW,oBAAoB,MAAM,WAAW,UAAU,KAAK,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;AACxJ,aAAO;AAAA,IACT;AACA,QAAI,oBAAoB,KAAK,aAAa;AACxC,eAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,EAAE,GAAG;AAChD,YAAI,KAAK,YAAY,CAAC,EAAE,WAAW,GAAG;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,QAAI,qBAAqB,KAAK,QAAQ;AACpC,UAAI,WAAW,KAAK;AACpB,aAAO,UAAU;AACf,YAAI,SAAS,QAAQ;AACnB,iBAAO;AAAA,QACT;AACA,mBAAW,SAAS;AAAA,MACtB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,EAAAA,aAAY,UAAU,UAAU,SAAU,GAAG,GAAG;AAC9C,WAAO,KAAK,YAAY,GAAG,CAAC;AAAA,EAC9B;AACA,EAAAA,aAAY,UAAU,WAAW,SAAU,IAAI,SAAS;AACtD,OAAG,KAAK,SAAS,IAAI;AAAA,EACvB;AACA,EAAAA,aAAY,UAAU,cAAc,SAAU,GAAG,GAAG;AAClD,QAAI,QAAQ,KAAK,sBAAsB,GAAG,CAAC;AAC3C,QAAI,OAAO,KAAK,gBAAgB;AAChC,WAAO,KAAK,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EACxC;AACA,EAAAA,aAAY,UAAU,eAAe,WAAY;AAC/C,QAAI,OAAO,KAAK;AAChB,QAAI,CAAC,KAAK,cAAc,KAAK,SAAS;AACpC,UAAI,YAAY,KAAK;AACrB,UAAI,SAAS,KAAK,gBAAgB;AAClC,UAAI,QAAQ,KAAK;AACjB,UAAI,aAAa,MAAM,cAAc;AACrC,UAAI,gBAAgB,MAAM,iBAAiB;AAC3C,UAAI,gBAAgB,MAAM,iBAAiB;AAC3C,aAAO,KAAK,eAAe,KAAK,aAAa,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACxE,UAAI,WAAW;AACb,6BAAa,eAAe,MAAM,QAAQ,SAAS;AAAA,MACrD,OAAO;AACL,aAAK,KAAK,MAAM;AAAA,MAClB;AACA,UAAI,cAAc,iBAAiB,eAAe;AAChD,aAAK,SAAS,aAAa,IAAI,KAAK,IAAI,aAAa;AACrD,aAAK,UAAU,aAAa,IAAI,KAAK,IAAI,aAAa;AACtD,aAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,gBAAgB,UAAU;AAC7D,aAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,gBAAgB,UAAU;AAAA,MAC/D;AACA,UAAI,YAAY,KAAK;AACrB,UAAI,CAAC,KAAK,OAAO,GAAG;AAClB,aAAK,IAAI,KAAK,MAAM,KAAK,IAAI,SAAS;AACtC,aAAK,IAAI,KAAK,MAAM,KAAK,IAAI,SAAS;AACtC,aAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,IAAI,YAAY,CAAC;AACrD,aAAK,SAAS,KAAK,KAAK,KAAK,SAAS,IAAI,YAAY,CAAC;AAAA,MACzD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,EAAAA,aAAY,UAAU,mBAAmB,SAAU,WAAW;AAC5D,QAAI,WAAW;AACb,WAAK,iBAAiB,KAAK,kBAAkB,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACxE,WAAK,eAAe,KAAK,SAAS;AAAA,IACpC,OAAO;AACL,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AACA,EAAAA,aAAY,UAAU,mBAAmB,WAAY;AACnD,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,aAAY,UAAU,eAAe,SAAU,MAAM;AACnD,WAAO,KAAK,QAAQ,SAAS,IAAI;AAAA,EACnC;AACA,EAAAA,aAAY,UAAU,wBAAwB,SAAU,WAAW;AACjE,QAAI,cAAc,SAAS;AACzB,WAAK,WAAW;AAAA,IAClB,OAAO;AACL,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACA,EAAAA,aAAY,UAAU,SAAS,SAAU,KAAK,OAAO;AACnD,QAAI,QAAQ,SAAS;AACnB,aAAO,UAAU,OAAO,KAAK,MAAM,KAAK,KAAK;AAAA,IAC/C,OAAO;AACL,UAAI,CAAC,KAAK,OAAO;AACf,aAAK,SAAS,KAAK;AAAA,MACrB,OAAO;AACL,aAAK,SAAS,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,EAAAA,aAAY,UAAU,WAAW,SAAU,UAAU,OAAO;AAC1D,QAAI,OAAO,aAAa,UAAU;AAChC,WAAK,MAAM,QAAQ,IAAI;AAAA,IACzB,OAAO;AACL,aAAO,KAAK,OAAO,QAAQ;AAAA,IAC7B;AACA,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AACA,EAAAA,aAAY,UAAU,aAAa,SAAU,WAAW;AACtD,QAAI,CAAC,WAAW;AACd,WAAK,WAAW;AAAA,IAClB;AACA,SAAK,WAAW;AAChB,QAAI,KAAK,OAAO;AACd,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACA,EAAAA,aAAY,UAAU,QAAQ,WAAY;AACxC,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,aAAY,UAAU,eAAe,WAAY;AAC/C,WAAO,CAAC,EAAE,KAAK,UAAU;AAAA,EAC3B;AACA,EAAAA,aAAY,UAAU,eAAe,WAAY;AAC/C,SAAK,WAAW,CAAC;AAAA,EACnB;AACA,EAAAA,aAAY,UAAU,cAAc,SAAU,KAAK;AACjD,WAAO,aAAa,sBAAsB,GAAG;AAAA,EAC/C;AACA,EAAAA,aAAY,UAAU,WAAW,SAAU,KAAK;AAC9C,QAAI,CAAC,IAAI,eAAe,GAAG;AACzB,YAAM,KAAK,YAAY,GAAG;AAAA,IAC5B;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,eAAe;AAAA,IACtB,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AACA,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,aAAY,UAAU,gBAAgB,SAAU,KAAK;AACnD,WAAO,IAAI,eAAe;AAAA,EAC5B;AACA,EAAAA,aAAY,UAAU,qBAAqB,SAAU,SAAS;AAC5D,WAAO,UAAU,mBAAmB,KAAK,MAAM,OAAO;AACtD,QAAI,cAAc,KAAK;AACvB,QAAI,QAAQ,SAAS,CAAC,YAAY,OAAO;AACvC,kBAAY,QAAQ,KAAK,YAAY,KAAK,YAAY,GAAG,KAAK,KAAK;AAAA,IACrE;AACA,SAAK,qBAAqB,SAAS,aAAaD,oBAAmB;AAAA,EACrE;AACA,EAAAC,aAAY,UAAU,iBAAiB,SAAU,WAAW,OAAO,aAAa,mBAAmB,YAAY,cAAc;AAC3H,WAAO,UAAU,eAAe,KAAK,MAAM,WAAW,OAAO,aAAa,mBAAmB,YAAY,YAAY;AACrH,QAAI,uBAAuB,EAAE,SAAS;AACtC,QAAI;AACJ,QAAI,SAAS,MAAM,OAAO;AACxB,UAAI,YAAY;AACd,YAAI,mBAAmB;AACrB,wBAAc,MAAM;AAAA,QACtB,OAAO;AACL,wBAAc,KAAK,YAAY,KAAK,YAAY,GAAG,YAAY,KAAK;AACpE,eAAK,YAAY,aAAa,MAAM,KAAK;AAAA,QAC3C;AAAA,MACF,OAAO;AACL,sBAAc,KAAK,YAAY,KAAK,YAAY,GAAG,oBAAoB,KAAK,QAAQ,YAAY,KAAK;AACrG,aAAK,YAAY,aAAa,MAAM,KAAK;AAAA,MAC3C;AAAA,IACF,WAAW,sBAAsB;AAC/B,oBAAc,YAAY;AAAA,IAC5B;AACA,QAAI,aAAa;AACf,UAAI,YAAY;AACd,YAAI,cAAc,KAAK;AACvB,aAAK,QAAQ,KAAK,YAAY,uBAAuB,CAAC,IAAI,WAAW;AACrE,YAAI,sBAAsB;AACxB,cAAI,cAAc,KAAK,WAAW;AAClC,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,gBAAI,MAAM,YAAY,CAAC;AACvB,gBAAI,OAAO,aAAa;AACtB,0BAAY,GAAG,IAAI,YAAY,GAAG;AAClC,mBAAK,MAAM,GAAG,IAAI,YAAY,GAAG;AAAA,YACnC;AAAA,UACF;AAAA,QACF;AACA,YAAI,aAAa,KAAK,WAAW;AACjC,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,cAAI,MAAM,WAAW,CAAC;AACtB,eAAK,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG;AAAA,QAClC;AACA,aAAK,iBAAiB,WAAW;AAAA,UAC/B,OAAO;AAAA,QACT,GAAG,cAAc,KAAK,uBAAuB,CAAC;AAAA,MAChD,OAAO;AACL,aAAK,SAAS,WAAW;AAAA,MAC3B;AAAA,IACF;AACA,QAAI,aAAa,KAAK,YAAY,qCAAqCD;AACvE,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAI,MAAM,WAAW,CAAC;AACtB,UAAI,SAAS,MAAM,GAAG,KAAK,MAAM;AAC/B,aAAK,GAAG,IAAI,MAAM,GAAG;AAAA,MACvB,WAAW,sBAAsB;AAC/B,YAAI,YAAY,GAAG,KAAK,MAAM;AAC5B,eAAK,GAAG,IAAI,YAAY,GAAG;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,EAAAC,aAAY,UAAU,eAAe,SAAU,QAAQ;AACrD,QAAI,cAAc,OAAO,UAAU,aAAa,KAAK,MAAM,MAAM;AACjE,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,MAAM,OAAO;AACf,sBAAc,eAAe,CAAC;AAC9B,aAAK,YAAY,aAAa,MAAM,KAAK;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,aAAa;AACf,kBAAY,QAAQ;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,aAAY,UAAU,cAAc,SAAU,aAAa,aAAa;AACtE,WAAO,aAAa,WAAW;AAC/B,WAAO;AAAA,EACT;AACA,EAAAA,aAAY,UAAU,yBAAyB,WAAY;AACzD,WAAO;AAAA,EACT;AACA,EAAAA,aAAY,mBAAmB,WAAY;AACzC,QAAI,YAAYA,aAAY;AAC5B,cAAU,OAAO;AACjB,cAAU,YAAY;AACtB,cAAU,IAAI;AACd,cAAU,KAAK;AACf,cAAU,SAAS;AACnB,cAAU,UAAU;AACpB,cAAU,SAAS;AACnB,cAAU,YAAY;AACtB,cAAU,cAAc;AACxB,cAAU,QAAQ;AAClB,cAAU,qBAAqB;AAC/B,cAAU,UAAU,aAAa;AAAA,EACnC,EAAE;AACF,SAAOA;AACT,EAAE,eAAO;AACT,IAAI,UAAU,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACzC,IAAI,WAAW,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC1C,SAAS,oBAAoB,IAAI,OAAO,QAAQ;AAC9C,UAAQ,KAAK,GAAG,gBAAgB,CAAC;AACjC,MAAI,GAAG,WAAW;AAChB,YAAQ,eAAe,GAAG,SAAS;AAAA,EACrC;AACA,WAAS,QAAQ;AACjB,WAAS,SAAS;AAClB,SAAO,CAAC,QAAQ,UAAU,QAAQ;AACpC;AACA,IAAO,sBAAQ;;;AC/Sf,IAAIC,WAAU,KAAK;AACnB,IAAIC,WAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,MAAM,KAAK,KAAK;AACpB,IAAI,QAAaC,QAAO;AACxB,IAAI,MAAWA,QAAO;AACtB,IAAI,YAAiBA,QAAO;AACrB,SAAS,WAAW,QAAQC,MAAKC,MAAK;AAC3C,MAAI,OAAO,WAAW,GAAG;AACvB;AAAA,EACF;AACA,MAAI,IAAI,OAAO,CAAC;AAChB,MAAI,OAAO,EAAE,CAAC;AACd,MAAI,QAAQ,EAAE,CAAC;AACf,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,SAAS,EAAE,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,OAAO,CAAC;AACZ,WAAOJ,SAAQ,MAAM,EAAE,CAAC,CAAC;AACzB,YAAQC,SAAQ,OAAO,EAAE,CAAC,CAAC;AAC3B,UAAMD,SAAQ,KAAK,EAAE,CAAC,CAAC;AACvB,aAASC,SAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EAC/B;AACA,EAAAE,KAAI,CAAC,IAAI;AACT,EAAAA,KAAI,CAAC,IAAI;AACT,EAAAC,KAAI,CAAC,IAAI;AACT,EAAAA,KAAI,CAAC,IAAI;AACX;AACO,SAAS,SAAS,IAAI,IAAI,IAAI,IAAID,MAAKC,MAAK;AACjD,EAAAD,KAAI,CAAC,IAAIH,SAAQ,IAAI,EAAE;AACvB,EAAAG,KAAI,CAAC,IAAIH,SAAQ,IAAI,EAAE;AACvB,EAAAI,KAAI,CAAC,IAAIH,SAAQ,IAAI,EAAE;AACvB,EAAAG,KAAI,CAAC,IAAIH,SAAQ,IAAI,EAAE;AACzB;AACA,IAAI,OAAO,CAAC;AACZ,IAAI,OAAO,CAAC;AACL,SAAS,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAIE,MAAKC,MAAK;AAClE,MAAIC,gBAAqB;AACzB,MAAIC,WAAgB;AACpB,MAAI,IAAID,cAAa,IAAI,IAAI,IAAI,IAAI,IAAI;AACzC,EAAAF,KAAI,CAAC,IAAI;AACT,EAAAA,KAAI,CAAC,IAAI;AACT,EAAAC,KAAI,CAAC,IAAI;AACT,EAAAA,KAAI,CAAC,IAAI;AACT,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,IAAIE,SAAQ,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC;AACvC,IAAAH,KAAI,CAAC,IAAIH,SAAQ,GAAGG,KAAI,CAAC,CAAC;AAC1B,IAAAC,KAAI,CAAC,IAAIH,SAAQ,GAAGG,KAAI,CAAC,CAAC;AAAA,EAC5B;AACA,MAAIC,cAAa,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,IAAIC,SAAQ,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC;AACvC,IAAAH,KAAI,CAAC,IAAIH,SAAQ,GAAGG,KAAI,CAAC,CAAC;AAC1B,IAAAC,KAAI,CAAC,IAAIH,SAAQ,GAAGG,KAAI,CAAC,CAAC;AAAA,EAC5B;AACA,EAAAD,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAC,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAD,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAC,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAD,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAC,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAD,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAC,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC7B;AACO,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAID,MAAKC,MAAK;AAC9D,MAAIG,qBAA0B;AAC9B,MAAIC,eAAoB;AACxB,MAAI,KAAKP,SAAQD,SAAQO,mBAAkB,IAAI,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;AAC7D,MAAI,KAAKN,SAAQD,SAAQO,mBAAkB,IAAI,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;AAC7D,MAAI,IAAIC,aAAY,IAAI,IAAI,IAAI,EAAE;AAClC,MAAI,IAAIA,aAAY,IAAI,IAAI,IAAI,EAAE;AAClC,EAAAL,KAAI,CAAC,IAAIH,SAAQ,IAAI,IAAI,CAAC;AAC1B,EAAAG,KAAI,CAAC,IAAIH,SAAQ,IAAI,IAAI,CAAC;AAC1B,EAAAI,KAAI,CAAC,IAAIH,SAAQ,IAAI,IAAI,CAAC;AAC1B,EAAAG,KAAI,CAAC,IAAIH,SAAQ,IAAI,IAAI,CAAC;AAC5B;AACO,SAAS,QAAQ,GAAG,GAAG,IAAI,IAAI,YAAY,UAAU,eAAeE,MAAKC,MAAK;AACnF,MAAI,UAAe;AACnB,MAAI,UAAe;AACnB,MAAI,OAAO,KAAK,IAAI,aAAa,QAAQ;AACzC,MAAI,OAAO,MAAM,QAAQ,OAAO,MAAM;AACpC,IAAAD,KAAI,CAAC,IAAI,IAAI;AACb,IAAAA,KAAI,CAAC,IAAI,IAAI;AACb,IAAAC,KAAI,CAAC,IAAI,IAAI;AACb,IAAAA,KAAI,CAAC,IAAI,IAAI;AACb;AAAA,EACF;AACA,QAAM,CAAC,IAAI,QAAQ,UAAU,IAAI,KAAK;AACtC,QAAM,CAAC,IAAI,QAAQ,UAAU,IAAI,KAAK;AACtC,MAAI,CAAC,IAAI,QAAQ,QAAQ,IAAI,KAAK;AAClC,MAAI,CAAC,IAAI,QAAQ,QAAQ,IAAI,KAAK;AAClC,UAAQD,MAAK,OAAO,GAAG;AACvB,UAAQC,MAAK,OAAO,GAAG;AACvB,eAAa,aAAa;AAC1B,MAAI,aAAa,GAAG;AAClB,iBAAa,aAAa;AAAA,EAC5B;AACA,aAAW,WAAW;AACtB,MAAI,WAAW,GAAG;AAChB,eAAW,WAAW;AAAA,EACxB;AACA,MAAI,aAAa,YAAY,CAAC,eAAe;AAC3C,gBAAY;AAAA,EACd,WAAW,aAAa,YAAY,eAAe;AACjD,kBAAc;AAAA,EAChB;AACA,MAAI,eAAe;AACjB,QAAI,MAAM;AACV,eAAW;AACX,iBAAa;AAAA,EACf;AACA,WAAS,QAAQ,GAAG,QAAQ,UAAU,SAAS,KAAK,KAAK,GAAG;AAC1D,QAAI,QAAQ,YAAY;AACtB,gBAAU,CAAC,IAAI,QAAQ,KAAK,IAAI,KAAK;AACrC,gBAAU,CAAC,IAAI,QAAQ,KAAK,IAAI,KAAK;AACrC,cAAQD,MAAK,WAAWA,IAAG;AAC3B,cAAQC,MAAK,WAAWA,IAAG;AAAA,IAC7B;AAAA,EACF;AACF;;;ACrHA,IAAI,MAAM;AAAA,EACR,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAI,UAAU,CAAC;AACf,IAAI,UAAU,CAAC;AACf,IAAIK,OAAM,CAAC;AACX,IAAIC,OAAM,CAAC;AACX,IAAIC,QAAO,CAAC;AACZ,IAAIC,QAAO,CAAC;AACZ,IAAIC,WAAU,KAAK;AACnB,IAAIC,WAAU,KAAK;AACnB,IAAIC,WAAU,KAAK;AACnB,IAAIC,WAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,KAAK,KAAK;AACd,IAAIC,OAAM,KAAK;AACf,IAAI,gBAAgB,OAAO,iBAAiB;AAC5C,IAAI,YAAY,CAAC;AACjB,SAAS,OAAO,QAAQ;AACtB,MAAI,IAAI,KAAK,MAAM,SAAS,KAAK,GAAG,IAAI;AACxC,SAAO,IAAI,IAAI;AACjB;AACO,SAAS,mBAAmB,QAAQ,eAAe;AACxD,MAAI,gBAAgB,OAAO,OAAO,CAAC,CAAC;AACpC,MAAI,gBAAgB,GAAG;AACrB,qBAAiBA;AAAA,EACnB;AACA,MAAI,QAAQ,gBAAgB,OAAO,CAAC;AACpC,MAAI,cAAc,OAAO,CAAC;AAC1B,iBAAe;AACf,MAAI,CAAC,iBAAiB,cAAc,iBAAiBA,MAAK;AACxD,kBAAc,gBAAgBA;AAAA,EAChC,WAAW,iBAAiB,gBAAgB,eAAeA,MAAK;AAC9D,kBAAc,gBAAgBA;AAAA,EAChC,WAAW,CAAC,iBAAiB,gBAAgB,aAAa;AACxD,kBAAc,iBAAiBA,OAAM,OAAO,gBAAgB,WAAW;AAAA,EACzE,WAAW,iBAAiB,gBAAgB,aAAa;AACvD,kBAAc,iBAAiBA,OAAM,OAAO,cAAc,aAAa;AAAA,EACzE;AACA,SAAO,CAAC,IAAI;AACZ,SAAO,CAAC,IAAI;AACd;AACA,IAAI,YAAY,WAAY;AAC1B,WAASC,WAAU,aAAa;AAC9B,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,QAAI,aAAa;AACf,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,OAAO,CAAC;AAAA,IACf;AAAA,EACF;AACA,EAAAA,WAAU,UAAU,kBAAkB,WAAY;AAChD,SAAK;AAAA,EACP;AACA,EAAAA,WAAU,UAAU,aAAa,WAAY;AAC3C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,WAAU,UAAU,WAAW,SAAU,IAAI,IAAI,wBAAwB;AACvE,6BAAyB,0BAA0B;AACnD,QAAI,yBAAyB,GAAG;AAC9B,WAAK,MAAM,QAAQ,yBAAyB,mBAAM,EAAE,KAAK;AACzD,WAAK,MAAM,QAAQ,yBAAyB,mBAAM,EAAE,KAAK;AAAA,IAC3D;AAAA,EACF;AACA,EAAAA,WAAU,UAAU,SAAS,SAAUC,MAAK;AAC1C,SAAK,MAAMA;AAAA,EACb;AACA,EAAAD,WAAU,UAAU,aAAa,SAAU,KAAK;AAC9C,SAAK,OAAO;AAAA,EACd;AACA,EAAAA,WAAU,UAAU,aAAa,WAAY;AAC3C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,WAAU,UAAU,YAAY,WAAY;AAC1C,SAAK,QAAQ,KAAK,KAAK,UAAU;AACjC,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,QAAQ,WAAY;AACtC,QAAI,KAAK,WAAW;AAClB,WAAK,OAAO;AAAA,IACd;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,cAAc;AACnB,WAAK,WAAW;AAAA,IAClB;AACA,SAAK;AAAA,EACP;AACA,EAAAA,WAAU,UAAU,SAAS,SAAU,GAAG,GAAG;AAC3C,SAAK,eAAe;AACpB,SAAK,QAAQ,IAAI,GAAG,GAAG,CAAC;AACxB,SAAK,QAAQ,KAAK,KAAK,OAAO,GAAG,CAAC;AAClC,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,SAAS,SAAU,GAAG,GAAG;AAC3C,QAAI,KAAK,QAAQ,IAAI,KAAK,GAAG;AAC7B,QAAI,KAAK,QAAQ,IAAI,KAAK,GAAG;AAC7B,QAAI,aAAa,KAAK,KAAK,OAAO,KAAK,KAAK;AAC5C,SAAK,QAAQ,IAAI,GAAG,GAAG,CAAC;AACxB,QAAI,KAAK,QAAQ,YAAY;AAC3B,WAAK,KAAK,OAAO,GAAG,CAAC;AAAA,IACvB;AACA,QAAI,YAAY;AACd,WAAK,MAAM;AACX,WAAK,MAAM;AACX,WAAK,iBAAiB;AAAA,IACxB,OAAO;AACL,UAAI,KAAK,KAAK,KAAK,KAAK;AACxB,UAAI,KAAK,KAAK,gBAAgB;AAC5B,aAAK,cAAc;AACnB,aAAK,cAAc;AACnB,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,gBAAgB,SAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACpE,SAAK,eAAe;AACpB,SAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC1C,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IAChD;AACA,SAAK,MAAM;AACX,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,mBAAmB,SAAU,IAAI,IAAI,IAAI,IAAI;AAC/D,SAAK,eAAe;AACpB,SAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,IAAI,EAAE;AAClC,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,iBAAiB,IAAI,IAAI,IAAI,EAAE;AAAA,IAC3C;AACA,SAAK,MAAM;AACX,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,MAAM,SAAU,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe;AAClF,SAAK,eAAe;AACpB,cAAU,CAAC,IAAI;AACf,cAAU,CAAC,IAAI;AACf,uBAAmB,WAAW,aAAa;AAC3C,iBAAa,UAAU,CAAC;AACxB,eAAW,UAAU,CAAC;AACtB,QAAI,QAAQ,WAAW;AACvB,SAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,YAAY,OAAO,GAAG,gBAAgB,IAAI,CAAC;AAC7E,SAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,IAAI,GAAG,YAAY,UAAU,aAAa;AACzE,SAAK,MAAMH,SAAQ,QAAQ,IAAI,IAAI;AACnC,SAAK,MAAMC,SAAQ,QAAQ,IAAI,IAAI;AACnC,WAAO;AAAA,EACT;AACA,EAAAE,WAAU,UAAU,QAAQ,SAAU,IAAI,IAAI,IAAI,IAAI,QAAQ;AAC5D,SAAK,eAAe;AACpB,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,MAAM;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,OAAO,SAAU,GAAG,GAAG,GAAG,GAAG;AAC/C,SAAK,eAAe;AACpB,SAAK,QAAQ,KAAK,KAAK,KAAK,GAAG,GAAG,GAAG,CAAC;AACtC,SAAK,QAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAC9B,WAAO;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,YAAY,WAAY;AAC1C,SAAK,eAAe;AACpB,SAAK,QAAQ,IAAI,CAAC;AAClB,QAAI,MAAM,KAAK;AACf,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AACP,UAAI,UAAU;AAAA,IAChB;AACA,SAAK,MAAM;AACX,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,OAAO,SAAU,KAAK;AACxC,WAAO,IAAI,KAAK;AAChB,SAAK,SAAS;AAAA,EAChB;AACA,EAAAA,WAAU,UAAU,SAAS,SAAU,KAAK;AAC1C,WAAO,IAAI,OAAO;AAClB,SAAK,SAAS;AAAA,EAChB;AACA,EAAAA,WAAU,UAAU,MAAM,WAAY;AACpC,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,WAAU,UAAU,UAAU,SAAU,MAAM;AAC5C,QAAIE,OAAM,KAAK;AACf,QAAI,EAAE,KAAK,QAAQ,KAAK,KAAK,WAAWA,SAAQ,eAAe;AAC7D,WAAK,OAAO,IAAI,aAAaA,IAAG;AAAA,IAClC;AACA,aAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,WAAK,KAAK,CAAC,IAAI,KAAK,CAAC;AAAA,IACvB;AACA,SAAK,OAAOA;AAAA,EACd;AACA,EAAAF,WAAU,UAAU,aAAa,SAAU,MAAM;AAC/C,QAAI,EAAE,gBAAgB,QAAQ;AAC5B,aAAO,CAAC,IAAI;AAAA,IACd;AACA,QAAIE,OAAM,KAAK;AACf,QAAI,aAAa;AACjB,QAAI,SAAS,KAAK;AAClB,aAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,oBAAc,KAAK,CAAC,EAAE,IAAI;AAAA,IAC5B;AACA,QAAI,iBAAiB,KAAK,gBAAgB,cAAc;AACtD,WAAK,OAAO,IAAI,aAAa,SAAS,UAAU;AAAA,IAClD;AACA,aAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC5B,UAAI,iBAAiB,KAAK,CAAC,EAAE;AAC7B,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,aAAK,KAAK,QAAQ,IAAI,eAAe,CAAC;AAAA,MACxC;AAAA,IACF;AACA,SAAK,OAAO;AAAA,EACd;AACA,EAAAF,WAAU,UAAU,UAAU,SAAU,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACnE,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,OAAO,UAAU,SAAS,KAAK,QAAQ;AAC9C,WAAK,YAAY;AACjB,aAAO,KAAK;AAAA,IACd;AACA,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,WAAK,KAAK,MAAM,IAAI,UAAU,CAAC;AAAA,IACjC;AAAA,EACF;AACA,EAAAA,WAAU,UAAU,iBAAiB,WAAY;AAC/C,QAAI,KAAK,iBAAiB,GAAG;AAC3B,WAAK,QAAQ,KAAK,KAAK,OAAO,KAAK,aAAa,KAAK,WAAW;AAChE,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AACA,EAAAA,WAAU,UAAU,cAAc,WAAY;AAC5C,QAAI,EAAE,KAAK,gBAAgB,QAAQ;AACjC,UAAI,UAAU,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAClC,gBAAQ,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,MAC1B;AACA,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AACA,EAAAA,WAAU,UAAU,WAAW,WAAY;AACzC,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,SAAK,eAAe;AACpB,QAAI,OAAO,KAAK;AAChB,QAAI,gBAAgB,OAAO;AACzB,WAAK,SAAS,KAAK;AACnB,UAAI,iBAAiB,KAAK,OAAO,IAAI;AACnC,aAAK,OAAO,IAAI,aAAa,IAAI;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACA,EAAAA,WAAU,UAAU,kBAAkB,WAAY;AAChD,IAAAT,KAAI,CAAC,IAAIA,KAAI,CAAC,IAAIE,MAAK,CAAC,IAAIA,MAAK,CAAC,IAAI,OAAO;AAC7C,IAAAD,KAAI,CAAC,IAAIA,KAAI,CAAC,IAAIE,MAAK,CAAC,IAAIA,MAAK,CAAC,IAAI,CAAC,OAAO;AAC9C,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI;AACJ,SAAK,IAAI,GAAG,IAAI,KAAK,QAAO;AAC1B,UAAI,MAAM,KAAK,GAAG;AAClB,UAAI,UAAU,MAAM;AACpB,UAAI,SAAS;AACX,aAAK,KAAK,CAAC;AACX,aAAK,KAAK,IAAI,CAAC;AACf,aAAK;AACL,aAAK;AAAA,MACP;AACA,cAAQ,KAAK;AAAA,QACX,KAAK,IAAI;AACP,eAAK,KAAK,KAAK,GAAG;AAClB,eAAK,KAAK,KAAK,GAAG;AAClB,UAAAD,MAAK,CAAC,IAAI;AACV,UAAAA,MAAK,CAAC,IAAI;AACV,UAAAC,MAAK,CAAC,IAAI;AACV,UAAAA,MAAK,CAAC,IAAI;AACV;AAAA,QACF,KAAK,IAAI;AACP,mBAAS,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAGD,OAAMC,KAAI;AACjD,eAAK,KAAK,GAAG;AACb,eAAK,KAAK,GAAG;AACb;AAAA,QACF,KAAK,IAAI;AACP,oBAAU,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAGD,OAAMC,KAAI;AAC9F,eAAK,KAAK,GAAG;AACb,eAAK,KAAK,GAAG;AACb;AAAA,QACF,KAAK,IAAI;AACP,wBAAc,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAGD,OAAMC,KAAI;AAC5E,eAAK,KAAK,GAAG;AACb,eAAK,KAAK,GAAG;AACb;AAAA,QACF,KAAK,IAAI;AACP,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,aAAa,KAAK,GAAG;AACzB,cAAI,WAAW,KAAK,GAAG,IAAI;AAC3B,eAAK;AACL,cAAI,gBAAgB,CAAC,KAAK,GAAG;AAC7B,cAAI,SAAS;AACX,iBAAKG,SAAQ,UAAU,IAAI,KAAK;AAChC,iBAAKC,SAAQ,UAAU,IAAI,KAAK;AAAA,UAClC;AACA,kBAAQ,IAAI,IAAI,IAAI,IAAI,YAAY,UAAU,eAAeL,OAAMC,KAAI;AACvE,eAAKG,SAAQ,QAAQ,IAAI,KAAK;AAC9B,eAAKC,SAAQ,QAAQ,IAAI,KAAK;AAC9B;AAAA,QACF,KAAK,IAAI;AACP,eAAK,KAAK,KAAK,GAAG;AAClB,eAAK,KAAK,KAAK,GAAG;AAClB,cAAI,QAAQ,KAAK,GAAG;AACpB,cAAI,SAAS,KAAK,GAAG;AACrB,mBAAS,IAAI,IAAI,KAAK,OAAO,KAAK,QAAQL,OAAMC,KAAI;AACpD;AAAA,QACF,KAAK,IAAI;AACP,eAAK;AACL,eAAK;AACL;AAAA,MACJ;AACA,MAAK,IAAIH,MAAKA,MAAKE,KAAI;AACvB,MAAK,IAAID,MAAKA,MAAKE,KAAI;AAAA,IACzB;AACA,QAAI,MAAM,GAAG;AACX,MAAAH,KAAI,CAAC,IAAIA,KAAI,CAAC,IAAIC,KAAI,CAAC,IAAIA,KAAI,CAAC,IAAI;AAAA,IACtC;AACA,WAAO,IAAI,qBAAaD,KAAI,CAAC,GAAGA,KAAI,CAAC,GAAGC,KAAI,CAAC,IAAID,KAAI,CAAC,GAAGC,KAAI,CAAC,IAAID,KAAI,CAAC,CAAC;AAAA,EAC1E;AACA,EAAAS,WAAU,UAAU,mBAAmB,WAAY;AACjD,QAAI,OAAO,KAAK;AAChB,QAAIE,OAAM,KAAK;AACf,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,CAAC;AAAA,IACtB;AACA,QAAI,aAAa,KAAK;AACtB,QAAI,eAAe;AACnB,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAIA,QAAM;AACxB,UAAI,MAAM,KAAK,GAAG;AAClB,UAAI,UAAU,MAAM;AACpB,UAAI,SAAS;AACX,aAAK,KAAK,CAAC;AACX,aAAK,KAAK,IAAI,CAAC;AACf,aAAK;AACL,aAAK;AAAA,MACP;AACA,UAAI,IAAI;AACR,cAAQ,KAAK;AAAA,QACX,KAAK,IAAI;AACP,eAAK,KAAK,KAAK,GAAG;AAClB,eAAK,KAAK,KAAK,GAAG;AAClB;AAAA,QACF,KAAK,IAAI,GACP;AACE,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK;AACd,cAAI,KAAK,KAAK;AACd,cAAI,QAAQ,EAAE,IAAI,MAAM,QAAQ,EAAE,IAAI,MAAM,MAAMA,OAAM,GAAG;AACzD,gBAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC/B,iBAAK;AACL,iBAAK;AAAA,UACP;AACA;AAAA,QACF;AAAA,QACF,KAAK,IAAI,GACP;AACE,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAClD,eAAK;AACL,eAAK;AACL;AAAA,QACF;AAAA,QACF,KAAK,IAAI,GACP;AACE,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,gBAAgB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC9C,eAAK;AACL,eAAK;AACL;AAAA,QACF;AAAA,QACF,KAAK,IAAI;AACP,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,aAAa,KAAK,GAAG;AACzB,cAAI,QAAQ,KAAK,GAAG;AACpB,cAAI,WAAW,QAAQ;AACvB,eAAK;AACL,cAAI,gBAAgB,CAAC,KAAK,GAAG;AAC7B,cAAI,SAAS;AACX,iBAAKL,SAAQ,UAAU,IAAI,KAAK;AAChC,iBAAKC,SAAQ,UAAU,IAAI,KAAK;AAAA,UAClC;AACA,cAAIF,SAAQ,IAAI,EAAE,IAAID,SAAQI,MAAK,KAAK,IAAI,KAAK,CAAC;AAClD,eAAKF,SAAQ,QAAQ,IAAI,KAAK;AAC9B,eAAKC,SAAQ,QAAQ,IAAI,KAAK;AAC9B;AAAA,QACF,KAAK,IAAI,GACP;AACE,eAAK,KAAK,KAAK,GAAG;AAClB,eAAK,KAAK,KAAK,GAAG;AAClB,cAAI,QAAQ,KAAK,GAAG;AACpB,cAAI,SAAS,KAAK,GAAG;AACrB,cAAI,QAAQ,IAAI,SAAS;AACzB;AAAA,QACF;AAAA,QACF,KAAK,IAAI,GACP;AACE,cAAI,KAAK,KAAK;AACd,cAAI,KAAK,KAAK;AACd,cAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC/B,eAAK;AACL,eAAK;AACL;AAAA,QACF;AAAA,MACJ;AACA,UAAI,KAAK,GAAG;AACV,mBAAW,UAAU,IAAI;AACzB,wBAAgB;AAAA,MAClB;AAAA,IACF;AACA,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AACA,EAAAE,WAAU,UAAU,cAAc,SAAU,KAAK,SAAS;AACxD,QAAI,IAAI,KAAK;AACb,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAIE,OAAM,KAAK;AACf,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW,UAAU;AACzB,QAAI;AACJ,QAAI;AACJ,QAAI,cAAc;AAClB,QAAI,WAAW;AACf,QAAI;AACJ,QAAI,gBAAgB;AACpB,QAAI;AACJ,QAAI;AACJ,QAAI,UAAU;AACZ,UAAI,CAAC,KAAK,aAAa;AACrB,aAAK,iBAAiB;AAAA,MACxB;AACA,mBAAa,KAAK;AAClB,qBAAe,KAAK;AACpB,wBAAkB,UAAU;AAC5B,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF;AAAA,IACF;AACA,OAAI,UAAS,IAAI,GAAG,IAAIA,QAAM;AAC5B,UAAI,MAAM,EAAE,GAAG;AACf,UAAI,UAAU,MAAM;AACpB,UAAI,SAAS;AACX,aAAK,EAAE,CAAC;AACR,aAAK,EAAE,IAAI,CAAC;AACZ,aAAK;AACL,aAAK;AAAA,MACP;AACA,UAAI,QAAQ,IAAI,KAAK,gBAAgB,GAAG;AACtC,YAAI,OAAO,YAAY,UAAU;AACjC,wBAAgB;AAAA,MAClB;AACA,cAAQ,KAAK;AAAA,QACX,KAAK,IAAI;AACP,eAAK,KAAK,EAAE,GAAG;AACf,eAAK,KAAK,EAAE,GAAG;AACf,cAAI,OAAO,IAAI,EAAE;AACjB;AAAA,QACF,KAAK,IAAI,GACP;AACE,cAAI,EAAE,GAAG;AACT,cAAI,EAAE,GAAG;AACT,cAAI,KAAK,QAAQ,IAAI,EAAE;AACvB,cAAI,KAAK,QAAQ,IAAI,EAAE;AACvB,cAAI,KAAK,MAAM,KAAK,IAAI;AACtB,gBAAI,UAAU;AACZ,kBAAI,IAAI,WAAW,UAAU;AAC7B,kBAAI,cAAc,IAAI,iBAAiB;AACrC,oBAAI,KAAK,kBAAkB,eAAe;AAC1C,oBAAI,OAAO,MAAM,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,KAAK,IAAI,CAAC;AACrD,sBAAM;AAAA,cACR;AACA,6BAAe;AAAA,YACjB;AACA,gBAAI,OAAO,GAAG,CAAC;AACf,iBAAK;AACL,iBAAK;AACL,4BAAgB;AAAA,UAClB,OAAO;AACL,gBAAI,KAAK,KAAK,KAAK,KAAK;AACxB,gBAAI,KAAK,eAAe;AACtB,2BAAa;AACb,2BAAa;AACb,8BAAgB;AAAA,YAClB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACF,KAAK,IAAI,GACP;AACE,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,UAAU;AACZ,gBAAI,IAAI,WAAW,UAAU;AAC7B,gBAAI,cAAc,IAAI,iBAAiB;AACrC,kBAAI,KAAK,kBAAkB,eAAe;AAC1C,6BAAe,IAAI,IAAI,IAAI,IAAI,GAAG,OAAO;AACzC,6BAAe,IAAI,IAAI,IAAI,IAAI,GAAG,OAAO;AACzC,kBAAI,cAAc,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AACxF,oBAAM;AAAA,YACR;AACA,2BAAe;AAAA,UACjB;AACA,cAAI,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACxC,eAAK;AACL,eAAK;AACL;AAAA,QACF;AAAA,QACF,KAAK,IAAI,GACP;AACE,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,UAAU;AACZ,gBAAI,IAAI,WAAW,UAAU;AAC7B,gBAAI,cAAc,IAAI,iBAAiB;AACrC,kBAAI,KAAK,kBAAkB,eAAe;AAC1C,iCAAmB,IAAI,IAAI,IAAI,GAAG,OAAO;AACzC,iCAAmB,IAAI,IAAI,IAAI,GAAG,OAAO;AACzC,kBAAI,iBAAiB,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AACnE,oBAAM;AAAA,YACR;AACA,2BAAe;AAAA,UACjB;AACA,cAAI,iBAAiB,IAAI,IAAI,IAAI,EAAE;AACnC,eAAK;AACL,eAAK;AACL;AAAA,QACF;AAAA,QACF,KAAK,IAAI;AACP,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,aAAa,EAAE,GAAG;AACtB,cAAI,QAAQ,EAAE,GAAG;AACjB,cAAI,MAAM,EAAE,GAAG;AACf,cAAI,gBAAgB,CAAC,EAAE,GAAG;AAC1B,cAAI,IAAI,KAAK,KAAK,KAAK;AACvB,cAAI,YAAY,QAAQ,KAAK,EAAE,IAAI;AACnC,cAAI,WAAW,aAAa;AAC5B,cAAI,aAAa;AACjB,cAAI,UAAU;AACZ,gBAAI,IAAI,WAAW,UAAU;AAC7B,gBAAI,cAAc,IAAI,iBAAiB;AACrC,yBAAW,aAAa,SAAS,kBAAkB,eAAe;AAClE,2BAAa;AAAA,YACf;AACA,2BAAe;AAAA,UACjB;AACA,cAAI,aAAa,IAAI,SAAS;AAC5B,gBAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,KAAK,YAAY,UAAU,aAAa;AAAA,UACtE,OAAO;AACL,gBAAI,IAAI,IAAI,IAAI,GAAG,YAAY,UAAU,aAAa;AAAA,UACxD;AACA,cAAI,YAAY;AACd,kBAAM;AAAA,UACR;AACA,cAAI,SAAS;AACX,iBAAKL,SAAQ,UAAU,IAAI,KAAK;AAChC,iBAAKC,SAAQ,UAAU,IAAI,KAAK;AAAA,UAClC;AACA,eAAKD,SAAQ,QAAQ,IAAI,KAAK;AAC9B,eAAKC,SAAQ,QAAQ,IAAI,KAAK;AAC9B;AAAA,QACF,KAAK,IAAI;AACP,eAAK,KAAK,EAAE,CAAC;AACb,eAAK,KAAK,EAAE,IAAI,CAAC;AACjB,cAAI,EAAE,GAAG;AACT,cAAI,EAAE,GAAG;AACT,cAAI,QAAQ,EAAE,GAAG;AACjB,cAAI,SAAS,EAAE,GAAG;AAClB,cAAI,UAAU;AACZ,gBAAI,IAAI,WAAW,UAAU;AAC7B,gBAAI,cAAc,IAAI,iBAAiB;AACrC,kBAAI,MAAM,kBAAkB;AAC5B,kBAAI,OAAO,GAAG,CAAC;AACf,kBAAI,OAAO,IAAIH,SAAQ,KAAK,KAAK,GAAG,CAAC;AACrC,qBAAO;AACP,kBAAI,MAAM,GAAG;AACX,oBAAI,OAAO,IAAI,OAAO,IAAIA,SAAQ,KAAK,MAAM,CAAC;AAAA,cAChD;AACA,qBAAO;AACP,kBAAI,MAAM,GAAG;AACX,oBAAI,OAAO,IAAIC,SAAQ,QAAQ,KAAK,CAAC,GAAG,IAAI,MAAM;AAAA,cACpD;AACA,qBAAO;AACP,kBAAI,MAAM,GAAG;AACX,oBAAI,OAAO,GAAG,IAAIA,SAAQ,SAAS,KAAK,CAAC,CAAC;AAAA,cAC5C;AACA,oBAAM;AAAA,YACR;AACA,2BAAe;AAAA,UACjB;AACA,cAAI,KAAK,GAAG,GAAG,OAAO,MAAM;AAC5B;AAAA,QACF,KAAK,IAAI;AACP,cAAI,UAAU;AACZ,gBAAI,IAAI,WAAW,UAAU;AAC7B,gBAAI,cAAc,IAAI,iBAAiB;AACrC,kBAAI,KAAK,kBAAkB,eAAe;AAC1C,kBAAI,OAAO,MAAM,IAAI,KAAK,KAAK,GAAG,MAAM,IAAI,KAAK,KAAK,CAAC;AACvD,oBAAM;AAAA,YACR;AACA,2BAAe;AAAA,UACjB;AACA,cAAI,UAAU;AACd,eAAK;AACL,eAAK;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,EAAAI,WAAU,UAAU,QAAQ,WAAY;AACtC,QAAI,WAAW,IAAIA,WAAU;AAC7B,QAAI,OAAO,KAAK;AAChB,aAAS,OAAO,KAAK,QAAQ,KAAK,MAAM,IAAI,MAAM,UAAU,MAAM,KAAK,IAAI;AAC3E,aAAS,OAAO,KAAK;AACrB,WAAO;AAAA,EACT;AACA,EAAAA,WAAU,MAAM;AAChB,EAAAA,WAAU,mBAAmB,WAAY;AACvC,QAAI,QAAQA,WAAU;AACtB,UAAM,YAAY;AAClB,UAAM,MAAM;AACZ,UAAM,MAAM;AACZ,UAAM,iBAAiB;AACvB,UAAM,WAAW;AAAA,EACnB,EAAE;AACF,SAAOA;AACT,EAAE;AACF,IAAO,oBAAQ;;;ACxrBf,IAAI,mBAAmB,IAAI,YAAI,EAAE;AAC1B,SAAS,eAAe,eAAe;AAC5C,MAAI,OAAO,kBAAkB,UAAU;AACrC,QAAI,eAAe,iBAAiB,IAAI,aAAa;AACrD,WAAO,gBAAgB,aAAa;AAAA,EACtC,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACO,SAAS,oBAAoB,eAAe,OAAO,QAAQ,QAAQ,WAAW;AACnF,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACT,WAAW,OAAO,kBAAkB,UAAU;AAC5C,QAAI,SAAS,MAAM,iBAAiB,iBAAiB,CAAC,QAAQ;AAC5D,aAAO;AAAA,IACT;AACA,QAAI,eAAe,iBAAiB,IAAI,aAAa;AACrD,QAAI,cAAc;AAAA,MAChB;AAAA,MACA,IAAI;AAAA,MACJ;AAAA,IACF;AACA,QAAI,cAAc;AAChB,cAAQ,aAAa;AACrB,OAAC,aAAa,KAAK,KAAK,aAAa,QAAQ,KAAK,WAAW;AAAA,IAC/D,OAAO;AACL,cAAQ,YAAY,UAAU,eAAe,aAAa,WAAW;AACrE,YAAM,eAAe;AACrB,uBAAiB,IAAI,eAAe,MAAM,iBAAiB;AAAA,QACzD;AAAA,QACA,SAAS,CAAC,WAAW;AAAA,MACvB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc;AACrB,MAAI,eAAe,KAAK;AACxB,OAAK,SAAS,KAAK,UAAU,KAAK,iBAAiB;AACnD,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,QAAQ,KAAK;AACpD,QAAI,cAAc,aAAa,QAAQ,CAAC;AACxC,QAAI,KAAK,YAAY;AACrB,UAAM,GAAG,MAAM,YAAY,SAAS;AACpC,gBAAY,OAAO,MAAM;AAAA,EAC3B;AACA,eAAa,QAAQ,SAAS;AAChC;AACO,SAAS,aAAa,OAAO;AAClC,SAAO,SAAS,MAAM,SAAS,MAAM;AACvC;;;ACrDA,SAAS,UAAU,KAAK;AACtB,SAAO,SAAS,GAAG;AACrB;AACO,SAAS,qBAAqB,KAAK,KAAK,MAAM;AACnD,MAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI;AAChC,MAAI,KAAK,IAAI,MAAM,OAAO,IAAI,IAAI;AAClC,MAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI;AAChC,MAAI,KAAK,IAAI,MAAM,OAAO,IAAI,IAAI;AAClC,MAAI,CAAC,IAAI,QAAQ;AACf,QAAI,IAAI,KAAK,QAAQ,KAAK;AAC1B,SAAK,KAAK,KAAK,QAAQ,KAAK;AAC5B,QAAI,IAAI,KAAK,SAAS,KAAK;AAC3B,SAAK,KAAK,KAAK,SAAS,KAAK;AAAA,EAC/B;AACA,MAAI,UAAU,CAAC,IAAI,IAAI;AACvB,OAAK,UAAU,EAAE,IAAI,KAAK;AAC1B,MAAI,UAAU,CAAC,IAAI,IAAI;AACvB,OAAK,UAAU,EAAE,IAAI,KAAK;AAC1B,MAAI,iBAAiB,IAAI,qBAAqB,GAAG,GAAG,IAAI,EAAE;AAC1D,SAAO;AACT;AACO,SAAS,qBAAqB,KAAK,KAAK,MAAM;AACnD,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAIG,OAAM,KAAK,IAAI,OAAO,MAAM;AAChC,MAAI,IAAI,IAAI,KAAK,OAAO,MAAM,IAAI;AAClC,MAAI,IAAI,IAAI,KAAK,OAAO,MAAM,IAAI;AAClC,MAAI,IAAI,IAAI,KAAK,OAAO,MAAM,IAAI;AAClC,MAAI,CAAC,IAAI,QAAQ;AACf,QAAI,IAAI,QAAQ,KAAK;AACrB,QAAI,IAAI,SAAS,KAAK;AACtB,QAAI,IAAIA;AAAA,EACV;AACA,MAAI,UAAU,CAAC,IAAI,IAAI;AACvB,MAAI,UAAU,CAAC,IAAI,IAAI;AACvB,MAAI,KAAK,KAAK,UAAU,CAAC,IAAI,IAAI;AACjC,MAAI,iBAAiB,IAAI,qBAAqB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC9D,SAAO;AACT;AACO,SAAS,kBAAkB,KAAK,KAAK,MAAM;AAChD,MAAI,iBAAiB,IAAI,SAAS,WAAW,qBAAqB,KAAK,KAAK,IAAI,IAAI,qBAAqB,KAAK,KAAK,IAAI;AACvH,MAAI,aAAa,IAAI;AACrB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,mBAAe,aAAa,WAAW,CAAC,EAAE,QAAQ,WAAW,CAAC,EAAE,KAAK;AAAA,EACvE;AACA,SAAO;AACT;AACO,SAAS,kBAAkB,WAAW,eAAe;AAC1D,MAAI,cAAc,iBAAiB,CAAC,aAAa,CAAC,eAAe;AAC/D,WAAO;AAAA,EACT;AACA,MAAI,CAAC,aAAa,CAAC,iBAAiB,UAAU,WAAW,cAAc,QAAQ;AAC7E,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,UAAU,CAAC,MAAM,cAAc,CAAC,GAAG;AACrC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,SAAS,KAAK,EAAE;AACzB;AACO,SAAS,QAAQ,MAAM,OAAO,MAAM;AACzC,MAAI,KAAK,CAAC,SAAS,QAAQ,EAAE,KAAK;AAClC,MAAI,MAAM,CAAC,eAAe,cAAc,EAAE,KAAK;AAC/C,MAAI,MAAM,CAAC,eAAe,YAAY,EAAE,KAAK;AAC7C,MAAI,MAAM,CAAC,gBAAgB,eAAe,EAAE,KAAK;AACjD,MAAI,KAAK,EAAE,KAAK,QAAQ,KAAK,EAAE,MAAM,QAAQ;AAC3C,WAAO,WAAW,KAAK,EAAE,CAAC;AAAA,EAC5B;AACA,MAAI,MAAM,SAAS,YAAY,iBAAiB,IAAI;AACpD,UAAQ,KAAK,GAAG,KAAK,WAAW,IAAI,EAAE,CAAC,KAAK,WAAW,KAAK,MAAM,EAAE,CAAC,MAAM,WAAW,IAAI,GAAG,CAAC,KAAK,MAAM,WAAW,IAAI,GAAG,CAAC,KAAK,KAAK;AACxI;;;AC1EO,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,GAAG;AAC7D,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI;AACxH,WAAO;AAAA,EACT;AACA,MAAI,OAAO,IAAI;AACb,UAAM,KAAK,OAAO,KAAK;AACvB,UAAM,KAAK,KAAK,KAAK,OAAO,KAAK;AAAA,EACnC,OAAO;AACL,WAAO,KAAK,IAAI,IAAI,EAAE,KAAK,KAAK;AAAA,EAClC;AACA,MAAI,MAAM,KAAK,IAAI,IAAI;AACvB,MAAI,KAAK,MAAM,OAAO,KAAK,KAAK;AAChC,SAAO,MAAM,KAAK,IAAI,KAAK;AAC7B;;;AClBO,SAASC,eAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,GAAG;AAC7E,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,KAAK;AACT,MAAI,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI;AAChP,WAAO;AAAA,EACT;AACA,MAAI,IAAU,kBAAkB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI;AAC1E,SAAO,KAAK,KAAK;AACnB;;;ACVO,SAASC,eAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,GAAG;AACrE,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,KAAK;AACT,MAAI,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI;AACpL,WAAO;AAAA,EACT;AACA,MAAI,IAAI,sBAAsB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI;AAChE,SAAO,KAAK,KAAK;AACnB;;;ACXA,IAAIC,OAAM,KAAK,KAAK;AACb,SAAS,gBAAgB,OAAO;AACrC,WAASA;AACT,MAAI,QAAQ,GAAG;AACb,aAASA;AAAA,EACX;AACA,SAAO;AACT;;;ACNA,IAAIC,OAAM,KAAK,KAAK;AACb,SAASC,eAAc,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe,WAAW,GAAG,GAAG;AAC7F,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,KAAK;AACT,OAAK;AACL,OAAK;AACL,MAAI,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC/B,MAAI,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,KAAK,IAAI,aAAa,QAAQ,IAAID,OAAM,MAAM;AAChD,WAAO;AAAA,EACT;AACA,MAAI,eAAe;AACjB,QAAI,MAAM;AACV,iBAAa,gBAAgB,QAAQ;AACrC,eAAW,gBAAgB,GAAG;AAAA,EAChC,OAAO;AACL,iBAAa,gBAAgB,UAAU;AACvC,eAAW,gBAAgB,QAAQ;AAAA,EACrC;AACA,MAAI,aAAa,UAAU;AACzB,gBAAYA;AAAA,EACd;AACA,MAAI,QAAQ,KAAK,MAAM,GAAG,CAAC;AAC3B,MAAI,QAAQ,GAAG;AACb,aAASA;AAAA,EACX;AACA,SAAO,SAAS,cAAc,SAAS,YAAY,QAAQA,QAAO,cAAc,QAAQA,QAAO;AACjG;;;AChCe,SAAR,YAA6B,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACxD,MAAI,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI;AACxC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,IAAI;AACb,WAAO;AAAA,EACT;AACA,MAAI,KAAK,IAAI,OAAO,KAAK;AACzB,MAAI,MAAM,KAAK,KAAK,IAAI;AACxB,MAAI,MAAM,KAAK,MAAM,GAAG;AACtB,UAAM,KAAK,KAAK,MAAM;AAAA,EACxB;AACA,MAAI,KAAK,KAAK,KAAK,MAAM;AACzB,SAAO,OAAO,IAAI,WAAW,KAAK,IAAI,MAAM;AAC9C;;;ACPA,IAAIE,OAAM,kBAAU;AACpB,IAAIC,OAAM,KAAK,KAAK;AACpB,IAAIC,WAAU;AACd,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,KAAK,IAAI,IAAI,CAAC,IAAIA;AAC3B;AACA,IAAI,QAAQ,CAAC,IAAI,IAAI,EAAE;AACvB,IAAI,UAAU,CAAC,IAAI,EAAE;AACrB,SAAS,cAAc;AACrB,MAAI,MAAM,QAAQ,CAAC;AACnB,UAAQ,CAAC,IAAI,QAAQ,CAAC;AACtB,UAAQ,CAAC,IAAI;AACf;AACA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AAC1D,MAAI,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI;AAChF,WAAO;AAAA,EACT;AACA,MAAI,SAAe,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK;AACvD,MAAI,WAAW,GAAG;AAChB,WAAO;AAAA,EACT,OAAO;AACL,QAAI,IAAI;AACR,QAAI,WAAW;AACf,QAAI,MAAM;AACV,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAI,IAAI,MAAM,CAAC;AACf,UAAI,OAAO,MAAM,KAAK,MAAM,IAAI,MAAM;AACtC,UAAI,KAAW,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC;AACxC,UAAI,KAAK,GAAG;AACV;AAAA,MACF;AACA,UAAI,WAAW,GAAG;AAChB,mBAAiB,aAAa,IAAI,IAAI,IAAI,IAAI,OAAO;AACrD,YAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,WAAW,GAAG;AAC3C,sBAAY;AAAA,QACd;AACA,cAAY,QAAQ,IAAI,IAAI,IAAI,IAAI,QAAQ,CAAC,CAAC;AAC9C,YAAI,WAAW,GAAG;AAChB,gBAAY,QAAQ,IAAI,IAAI,IAAI,IAAI,QAAQ,CAAC,CAAC;AAAA,QAChD;AAAA,MACF;AACA,UAAI,aAAa,GAAG;AAClB,YAAI,IAAI,QAAQ,CAAC,GAAG;AAClB,eAAK,MAAM,KAAK,OAAO,CAAC;AAAA,QAC1B,WAAW,IAAI,QAAQ,CAAC,GAAG;AACzB,eAAK,MAAM,MAAM,OAAO,CAAC;AAAA,QAC3B,OAAO;AACL,eAAK,KAAK,MAAM,OAAO,CAAC;AAAA,QAC1B;AAAA,MACF,OAAO;AACL,YAAI,IAAI,QAAQ,CAAC,GAAG;AAClB,eAAK,MAAM,KAAK,OAAO,CAAC;AAAA,QAC1B,OAAO;AACL,eAAK,KAAK,MAAM,OAAO,CAAC;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAiB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACtD,MAAI,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI;AAC5D,WAAO;AAAA,EACT;AACA,MAAI,SAAe,gBAAgB,IAAI,IAAI,IAAI,GAAG,KAAK;AACvD,MAAI,WAAW,GAAG;AAChB,WAAO;AAAA,EACT,OAAO;AACL,QAAI,IAAU,kBAAkB,IAAI,IAAI,EAAE;AAC1C,QAAI,KAAK,KAAK,KAAK,GAAG;AACpB,UAAI,IAAI;AACR,UAAI,KAAW,YAAY,IAAI,IAAI,IAAI,CAAC;AACxC,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAI,OAAO,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,IAAI,MAAM;AACpD,YAAI,KAAW,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC;AAC/C,YAAI,KAAK,GAAG;AACV;AAAA,QACF;AACA,YAAI,MAAM,CAAC,IAAI,GAAG;AAChB,eAAK,KAAK,KAAK,OAAO,CAAC;AAAA,QACzB,OAAO;AACL,eAAK,KAAK,KAAK,OAAO,CAAC;AAAA,QACzB;AAAA,MACF;AACA,aAAO;AAAA,IACT,OAAO;AACL,UAAI,OAAO,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,IAAI,MAAM;AACpD,UAAI,KAAW,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC;AAC/C,UAAI,KAAK,GAAG;AACV,eAAO;AAAA,MACT;AACA,aAAO,KAAK,KAAK,OAAO,CAAC;AAAA,IAC3B;AAAA,EACF;AACF;AACA,SAAS,WAAW,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe,GAAG,GAAG;AACxE,OAAK;AACL,MAAI,IAAI,KAAK,IAAI,CAAC,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AACjC,QAAM,CAAC,IAAI,CAAC;AACZ,QAAM,CAAC,IAAI;AACX,MAAI,SAAS,KAAK,IAAI,aAAa,QAAQ;AAC3C,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,MAAI,UAAUD,OAAM,MAAM;AACxB,iBAAa;AACb,eAAWA;AACX,QAAI,MAAM,gBAAgB,IAAI;AAC9B,QAAI,KAAK,MAAM,CAAC,IAAI,MAAM,KAAK,MAAM,CAAC,IAAI,IAAI;AAC5C,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,aAAa,UAAU;AACzB,QAAI,QAAQ;AACZ,iBAAa;AACb,eAAW;AAAA,EACb;AACA,MAAI,aAAa,GAAG;AAClB,kBAAcA;AACd,gBAAYA;AAAA,EACd;AACA,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,KAAK,MAAM,CAAC;AAChB,QAAI,KAAK,KAAK,GAAG;AACf,UAAI,QAAQ,KAAK,MAAM,GAAG,EAAE;AAC5B,UAAI,MAAM,gBAAgB,IAAI;AAC9B,UAAI,QAAQ,GAAG;AACb,gBAAQA,OAAM;AAAA,MAChB;AACA,UAAI,SAAS,cAAc,SAAS,YAAY,QAAQA,QAAO,cAAc,QAAQA,QAAO,UAAU;AACpG,YAAI,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK;AAChD,gBAAM,CAAC;AAAA,QACT;AACA,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,YAAY,MAAM,WAAW,UAAU,GAAG,GAAG;AACpD,MAAI,OAAO,KAAK;AAChB,MAAIE,OAAM,KAAK,IAAI;AACnB,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI;AACJ,MAAI;AACJ,WAAS,IAAI,GAAG,IAAIA,QAAM;AACxB,QAAI,MAAM,KAAK,GAAG;AAClB,QAAI,UAAU,MAAM;AACpB,QAAI,QAAQH,KAAI,KAAK,IAAI,GAAG;AAC1B,UAAI,CAAC,UAAU;AACb,aAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,MACvC;AAAA,IACF;AACA,QAAI,SAAS;AACX,WAAK,KAAK,CAAC;AACX,WAAK,KAAK,IAAI,CAAC;AACf,WAAK;AACL,WAAK;AAAA,IACP;AACA,YAAQ,KAAK;AAAA,MACX,KAAKA,KAAI;AACP,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb,aAAK;AACL,aAAK;AACL;AAAA,MACF,KAAKA,KAAI;AACP,YAAI,UAAU;AACZ,cAAS,cAAc,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;AACrE,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,eAAK,YAAY,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK;AAAA,QAC1D;AACA,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACF,KAAKA,KAAI;AACP,YAAI,UAAU;AACZ,cAAUI,eAAc,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;AAClH,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,eAAK,aAAa,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK;AAAA,QACvG;AACA,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACF,KAAKJ,KAAI;AACP,YAAI,UAAU;AACZ,cAAcI,eAAc,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;AAChG,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,eAAK,iBAAiB,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK;AAAA,QACrF;AACA,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACF,KAAKJ,KAAI;AACP,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,QAAQ,KAAK,GAAG;AACpB,YAAI,SAAS,KAAK,GAAG;AACrB,aAAK;AACL,YAAI,gBAAgB,CAAC,EAAE,IAAI,KAAK,GAAG;AACnC,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAC5B,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAC5B,YAAI,CAAC,SAAS;AACZ,eAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,QACvC,OAAO;AACL,eAAK;AACL,eAAK;AAAA,QACP;AACA,YAAI,MAAM,IAAI,MAAM,KAAK,KAAK;AAC9B,YAAI,UAAU;AACZ,cAAQI,eAAc,IAAI,IAAI,IAAI,OAAO,QAAQ,QAAQ,eAAe,WAAW,IAAI,CAAC,GAAG;AACzF,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,eAAK,WAAW,IAAI,IAAI,IAAI,OAAO,QAAQ,QAAQ,eAAe,IAAI,CAAC;AAAA,QACzE;AACA,aAAK,KAAK,IAAI,QAAQ,MAAM,IAAI,KAAK;AACrC,aAAK,KAAK,IAAI,QAAQ,MAAM,IAAI,KAAK;AACrC;AAAA,MACF,KAAKJ,KAAI;AACP,aAAK,KAAK,KAAK,GAAG;AAClB,aAAK,KAAK,KAAK,GAAG;AAClB,YAAI,QAAQ,KAAK,GAAG;AACpB,YAAI,SAAS,KAAK,GAAG;AACrB,aAAK,KAAK;AACV,aAAK,KAAK;AACV,YAAI,UAAU;AACZ,cAAS,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,CAAC,KAAU,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,CAAC,KAAU,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,CAAC,KAAU,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,CAAC,GAAG;AAC5N,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,eAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AACrC,eAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,QACvC;AACA;AAAA,MACF,KAAKA,KAAI;AACP,YAAI,UAAU;AACZ,cAAS,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,CAAC,GAAG;AACvD,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,eAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,QACvC;AACA,aAAK;AACL,aAAK;AACL;AAAA,IACJ;AAAA,EACF;AACA,MAAI,CAAC,YAAY,CAAC,cAAc,IAAI,EAAE,GAAG;AACvC,SAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC,KAAK;AAAA,EAC5C;AACA,SAAO,MAAM;AACf;AACO,SAAS,QAAQ,WAAW,GAAG,GAAG;AACvC,SAAO,YAAY,WAAW,GAAG,OAAO,GAAG,CAAC;AAC9C;AACO,SAASI,eAAc,WAAW,WAAW,GAAG,GAAG;AACxD,SAAO,YAAY,WAAW,WAAW,MAAM,GAAG,CAAC;AACrD;;;ACnRO,IAAI,qBAAqB,SAAS;AAAA,EACvC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,aAAa;AAAA,EACb,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,aAAa;AACf,GAAG,oBAAoB;AAChB,IAAI,+BAA+B;AAAA,EACxC,OAAO,SAAS;AAAA,IACd,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,aAAa;AAAA,IACb,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,YAAY;AAAA,EACd,GAAG,+BAA+B,KAAK;AACzC;AACA,IAAI,iBAAiB,oBAAoB,OAAO,CAAC,aAAa,WAAW,KAAK,MAAM,UAAU,QAAQ,CAAC;AACvG,IAAI,OAAO,SAAU,QAAQ;AAC3B,YAAUC,OAAM,MAAM;AACtB,WAASA,MAAK,MAAM;AAClB,WAAO,OAAO,KAAK,MAAM,IAAI,KAAK;AAAA,EACpC;AACA,EAAAA,MAAK,UAAU,SAAS,WAAY;AAClC,QAAI,QAAQ;AACZ,WAAO,UAAU,OAAO,KAAK,IAAI;AACjC,QAAI,QAAQ,KAAK;AACjB,QAAI,MAAM,OAAO;AACf,UAAI,UAAU,KAAK,WAAW,KAAK,YAAY,IAAIA,MAAK;AACxD,UAAI,QAAQ,cAAcA,MAAK,UAAU,WAAW;AAClD,gBAAQ,YAAY,SAAU,KAAK;AACjC,gBAAM,UAAU,KAAK,MAAM,KAAK;AAAA,QAClC;AAAA,MACF;AACA,cAAQ,SAAS;AACjB,UAAI,eAAe,QAAQ;AAC3B,eAAS,OAAO,OAAO;AACrB,YAAI,aAAa,GAAG,MAAM,MAAM,GAAG,GAAG;AACpC,uBAAa,GAAG,IAAI,MAAM,GAAG;AAAA,QAC/B;AAAA,MACF;AACA,mBAAa,OAAO,MAAM,OAAO,MAAM,QAAQ;AAC/C,mBAAa,QAAQ;AACrB,mBAAa,cAAc;AAC3B,YAAM,gBAAgB,aAAa,SAAS;AAC5C,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,EAAE,GAAG;AAC9C,gBAAQ,eAAe,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC;AAAA,MACrD;AACA,cAAQ,WAAW;AAAA,IACrB,WAAW,KAAK,UAAU;AACxB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AAC3C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,MAAK,UAAU,QAAQ,SAAU,OAAO;AACtC,QAAI,UAAU,KAAK,KAAK;AACxB,SAAK,QAAQ,KAAK,gBAAgB;AAClC,QAAI,eAAe,KAAK,gBAAgB;AACxC,QAAI,cAAc;AAChB,WAAK,SAAS,YAAY;AAAA,IAC5B;AACA,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,MAAM,QAAQ,CAAC;AACnB,UAAI,QAAQ,MAAM,GAAG;AACrB,UAAI,QAAQ,SAAS;AACnB,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS,KAAK;AAAA,QACrB,OAAO;AACL,iBAAO,KAAK,OAAO,KAAK;AAAA,QAC1B;AAAA,MACF,WAAW,QAAQ,SAAS;AAC1B,eAAO,KAAK,OAAO,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,UAAU,OAAO,KAAK,MAAM,KAAK,KAAK;AAAA,MAC/C;AAAA,IACF;AACA,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,SAAS,CAAC,CAAC;AAAA,IAClB;AAAA,EACF;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AAC3C,WAAO;AAAA,EACT;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AAC3C,WAAO,CAAC;AAAA,EACV;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AAC3C,WAAO,KAAK,QAAQ;AAAA,EACtB;AACA,EAAAA,MAAK,UAAU,oBAAoB,WAAY;AAC7C,QAAI,WAAW,KAAK,MAAM;AAC1B,QAAI,aAAa,QAAQ;AACvB,UAAI,SAAS,QAAQ,GAAG;AACtB,YAAI,UAAU,IAAI,UAAU,CAAC;AAC7B,YAAI,UAAU,KAAK;AACjB,iBAAO;AAAA,QACT,WAAW,UAAU,KAAK;AACxB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,WAAW,UAAU;AACnB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,EAAAA,MAAK,UAAU,sBAAsB,SAAU,UAAU;AACvD,QAAI,WAAW,KAAK,MAAM;AAC1B,QAAI,SAAS,QAAQ,GAAG;AACtB,UAAI,KAAK,KAAK;AACd,UAAI,aAAa,CAAC,EAAE,MAAM,GAAG,WAAW;AACxC,UAAI,cAAc,IAAI,UAAU,CAAC,IAAI;AACrC,UAAI,eAAe,aAAa;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,EAAAA,MAAK,UAAU,YAAY,SAAU,KAAK,UAAU,SAAS;AAAA,EAAC;AAC9D,EAAAA,MAAK,UAAU,cAAc,WAAY;AACvC,SAAK,WAAW,CAAC;AAAA,EACnB;AACA,EAAAA,MAAK,UAAU,sBAAsB,SAAU,SAAS;AACtD,KAAC,KAAK,QAAQ,KAAK,gBAAgB;AACnC,SAAK,KAAK,UAAU;AACpB,SAAK,UAAU,KAAK,MAAM,KAAK,OAAO,OAAO;AAC7C,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AAC3C,SAAK,OAAO,IAAI,kBAAU,KAAK;AAAA,EACjC;AACA,EAAAA,MAAK,UAAU,YAAY,WAAY;AACrC,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,MAAM;AACnB,WAAO,EAAE,UAAU,QAAQ,WAAW,UAAU,EAAE,MAAM,YAAY;AAAA,EACtE;AACA,EAAAA,MAAK,UAAU,UAAU,WAAY;AACnC,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,MAAM;AACjB,WAAO,QAAQ,QAAQ,SAAS;AAAA,EAClC;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AAC3C,QAAI,OAAO,KAAK;AAChB,QAAI,QAAQ,KAAK;AACjB,QAAI,kBAAkB,CAAC;AACvB,QAAI,iBAAiB;AACnB,UAAI,cAAc;AAClB,UAAI,CAAC,KAAK,MAAM;AACd,sBAAc;AACd,aAAK,gBAAgB;AAAA,MACvB;AACA,UAAI,OAAO,KAAK;AAChB,UAAI,eAAe,KAAK,UAAU,mBAAmB;AACnD,aAAK,UAAU;AACf,aAAK,UAAU,MAAM,KAAK,OAAO,KAAK;AACtC,aAAK,YAAY;AAAA,MACnB;AACA,aAAO,KAAK,gBAAgB;AAAA,IAC9B;AACA,SAAK,QAAQ;AACb,QAAI,KAAK,UAAU,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,GAAG;AACxD,UAAI,aAAa,KAAK,gBAAgB,KAAK,cAAc,KAAK,MAAM;AACpE,UAAI,KAAK,WAAW,iBAAiB;AACnC,mBAAW,KAAK,IAAI;AACpB,YAAI,YAAY,MAAM,gBAAgB,KAAK,aAAa,IAAI;AAC5D,YAAI,IAAI,MAAM;AACd,YAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,cAAI,yBAAyB,KAAK;AAClC,cAAI,KAAK,IAAI,GAAG,0BAA0B,OAAO,IAAI,sBAAsB;AAAA,QAC7E;AACA,YAAI,YAAY,OAAO;AACrB,qBAAW,SAAS,IAAI;AACxB,qBAAW,UAAU,IAAI;AACzB,qBAAW,KAAK,IAAI,YAAY;AAChC,qBAAW,KAAK,IAAI,YAAY;AAAA,QAClC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,EAAAA,MAAK,UAAU,UAAU,SAAU,GAAG,GAAG;AACvC,QAAI,WAAW,KAAK,sBAAsB,GAAG,CAAC;AAC9C,QAAI,OAAO,KAAK,gBAAgB;AAChC,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,CAAC;AACd,QAAI,SAAS,CAAC;AACd,QAAI,KAAK,QAAQ,GAAG,CAAC,GAAG;AACtB,UAAI,YAAY,KAAK;AACrB,UAAI,KAAK,UAAU,GAAG;AACpB,YAAI,YAAY,MAAM;AACtB,YAAI,YAAY,MAAM,gBAAgB,KAAK,aAAa,IAAI;AAC5D,YAAI,YAAY,OAAO;AACrB,cAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,wBAAY,KAAK,IAAI,WAAW,KAAK,sBAAsB;AAAA,UAC7D;AACA,cAAgBC,eAAc,WAAW,YAAY,WAAW,GAAG,CAAC,GAAG;AACrE,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,QAAQ,GAAG;AAClB,eAAmB,QAAQ,WAAW,GAAG,CAAC;AAAA,MAC5C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,EAAAD,MAAK,UAAU,aAAa,WAAY;AACtC,SAAK,WAAW;AAChB,QAAI,KAAK,OAAO;AACd,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,WAAW;AAAA,IAC3B;AACA,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,MAAK,UAAU,QAAQ,WAAY;AACjC,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EAClB;AACA,EAAAA,MAAK,UAAU,eAAe,SAAU,MAAM;AAC5C,WAAO,KAAK,QAAQ,SAAS,IAAI;AAAA,EACnC;AACA,EAAAA,MAAK,UAAU,wBAAwB,SAAU,WAAW;AAC1D,QAAI,cAAc,SAAS;AACzB,WAAK,WAAW;AAAA,IAClB,WAAW,cAAc,SAAS;AAChC,WAAK,WAAW;AAAA,IAClB,OAAO;AACL,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACA,EAAAA,MAAK,UAAU,SAAS,SAAU,KAAK,OAAO;AAC5C,QAAI,QAAQ,SAAS;AACnB,WAAK,SAAS,KAAK;AAAA,IACrB,OAAO;AACL,aAAO,UAAU,OAAO,KAAK,MAAM,KAAK,KAAK;AAAA,IAC/C;AAAA,EACF;AACA,EAAAA,MAAK,UAAU,WAAW,SAAU,UAAU,OAAO;AACnD,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,OAAO;AACV,cAAQ,KAAK,QAAQ,CAAC;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,YAAM,QAAQ,IAAI;AAAA,IACpB,OAAO;AACL,aAAO,OAAO,QAAQ;AAAA,IACxB;AACA,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AACA,EAAAA,MAAK,UAAU,eAAe,WAAY;AACxC,WAAO,CAAC,EAAE,KAAK,UAAU;AAAA,EAC3B;AACA,EAAAA,MAAK,UAAU,cAAc,SAAU,KAAK;AAC1C,WAAO,aAAa,oBAAoB,GAAG;AAAA,EAC7C;AACA,EAAAA,MAAK,UAAU,qBAAqB,SAAU,SAAS;AACrD,WAAO,UAAU,mBAAmB,KAAK,MAAM,OAAO;AACtD,QAAI,cAAc,KAAK;AACvB,QAAI,QAAQ,SAAS,CAAC,YAAY,OAAO;AACvC,kBAAY,QAAQ,OAAO,CAAC,GAAG,KAAK,KAAK;AAAA,IAC3C;AAAA,EACF;AACA,EAAAA,MAAK,UAAU,iBAAiB,SAAU,WAAW,OAAO,aAAa,mBAAmB,YAAY,cAAc;AACpH,WAAO,UAAU,eAAe,KAAK,MAAM,WAAW,OAAO,aAAa,mBAAmB,YAAY,YAAY;AACrH,QAAI,uBAAuB,EAAE,SAAS;AACtC,QAAI;AACJ,QAAI,SAAS,MAAM,OAAO;AACxB,UAAI,YAAY;AACd,YAAI,mBAAmB;AACrB,wBAAc,MAAM;AAAA,QACtB,OAAO;AACL,wBAAc,OAAO,CAAC,GAAG,YAAY,KAAK;AAC1C,iBAAO,aAAa,MAAM,KAAK;AAAA,QACjC;AAAA,MACF,OAAO;AACL,sBAAc,OAAO,CAAC,GAAG,oBAAoB,KAAK,QAAQ,YAAY,KAAK;AAC3E,eAAO,aAAa,MAAM,KAAK;AAAA,MACjC;AAAA,IACF,WAAW,sBAAsB;AAC/B,oBAAc,YAAY;AAAA,IAC5B;AACA,QAAI,aAAa;AACf,UAAI,YAAY;AACd,aAAK,QAAQ,OAAO,CAAC,GAAG,KAAK,KAAK;AAClC,YAAI,0BAA0B,CAAC;AAC/B,YAAI,YAAY,KAAK,WAAW;AAChC,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAI,MAAM,UAAU,CAAC;AACrB,cAAI,OAAO,YAAY,GAAG,MAAM,UAAU;AACxC,iBAAK,MAAM,GAAG,IAAI,YAAY,GAAG;AAAA,UACnC,OAAO;AACL,oCAAwB,GAAG,IAAI,YAAY,GAAG;AAAA,UAChD;AAAA,QACF;AACA,aAAK,iBAAiB,WAAW;AAAA,UAC/B,OAAO;AAAA,QACT,GAAG,YAAY;AAAA,MACjB,OAAO;AACL,aAAK,QAAQ;AACb,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,EAAAA,MAAK,UAAU,eAAe,SAAU,QAAQ;AAC9C,QAAI,cAAc,OAAO,UAAU,aAAa,KAAK,MAAM,MAAM;AACjE,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,MAAM,OAAO;AACf,sBAAc,eAAe,CAAC;AAC9B,aAAK,YAAY,aAAa,MAAM,KAAK;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,aAAa;AACf,kBAAY,QAAQ;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,MAAK,UAAU,yBAAyB,WAAY;AAClD,WAAO;AAAA,EACT;AACA,EAAAA,MAAK,UAAU,aAAa,WAAY;AACtC,WAAO;AAAA,EACT;AACA,EAAAA,MAAK,SAAS,SAAU,cAAc;AACpC,QAAI,MAAM,SAAUE,SAAQ;AAC1B,gBAAUC,MAAKD,OAAM;AACrB,eAASC,KAAI,MAAM;AACjB,YAAI,QAAQD,QAAO,KAAK,MAAM,IAAI,KAAK;AACvC,qBAAa,QAAQ,aAAa,KAAK,KAAK,OAAO,IAAI;AACvD,eAAO;AAAA,MACT;AACA,MAAAC,KAAI,UAAU,kBAAkB,WAAY;AAC1C,eAAO,MAAM,aAAa,KAAK;AAAA,MACjC;AACA,MAAAA,KAAI,UAAU,kBAAkB,WAAY;AAC1C,eAAO,MAAM,aAAa,KAAK;AAAA,MACjC;AACA,aAAOA;AAAA,IACT,EAAEH,KAAI;AACN,aAAS,OAAO,cAAc;AAC5B,UAAI,OAAO,aAAa,GAAG,MAAM,YAAY;AAC3C,YAAI,UAAU,GAAG,IAAI,aAAa,GAAG;AAAA,MACvC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,EAAAA,MAAK,mBAAmB,WAAY;AAClC,QAAI,YAAYA,MAAK;AACrB,cAAU,OAAO;AACjB,cAAU,yBAAyB;AACnC,cAAU,yBAAyB;AACnC,cAAU,mBAAmB;AAC7B,cAAU,YAAY;AACtB,cAAU,UAAU,aAAa,oBAAoB;AAAA,EACvD,EAAE;AACF,SAAOA;AACT,EAAE,mBAAW;AACb,IAAO,eAAQ;;;ACxXR,IAAI,sBAAsB,SAAS;AAAA,EACxC,GAAG;AAAA,EACH,GAAG;AACL,GAAG,oBAAoB;AAChB,IAAI,gCAAgC;AAAA,EACzC,OAAO,SAAS;AAAA,IACd,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG,+BAA+B,KAAK;AACzC;AACA,SAAS,YAAY,QAAQ;AAC3B,SAAO,CAAC,EAAE,UAAU,OAAO,WAAW,YAAY,OAAO,SAAS,OAAO;AAC3E;AACA,IAAI,UAAU,SAAU,QAAQ;AAC9B,YAAUI,UAAS,MAAM;AACzB,WAASA,WAAU;AACjB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC7D;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,KAAK;AAC7C,WAAO,aAAa,qBAAqB,GAAG;AAAA,EAC9C;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,KAAK;AAC1C,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,MAAM,GAAG;AACpB,QAAI,QAAQ,MAAM;AAChB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,YAAY,MAAM,KAAK,IAAI,MAAM,QAAQ,KAAK;AAChE,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,QAAQ,UAAU,WAAW;AAC5C,QAAI,eAAe,MAAM,QAAQ;AACjC,QAAI,gBAAgB,MAAM;AACxB,aAAO,YAAY,GAAG;AAAA,IACxB,OAAO;AACL,aAAO,YAAY,GAAG,IAAI,YAAY,QAAQ,IAAI;AAAA,IACpD;AAAA,EACF;AACA,EAAAA,SAAQ,UAAU,WAAW,WAAY;AACvC,WAAO,KAAK,SAAS,OAAO;AAAA,EAC9B;AACA,EAAAA,SAAQ,UAAU,YAAY,WAAY;AACxC,WAAO,KAAK,SAAS,QAAQ;AAAA,EAC/B;AACA,EAAAA,SAAQ,UAAU,yBAAyB,WAAY;AACrD,WAAO;AAAA,EACT;AACA,EAAAA,SAAQ,UAAU,kBAAkB,WAAY;AAC9C,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,QAAQ,IAAI,qBAAa,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG,KAAK,SAAS,GAAG,KAAK,UAAU,CAAC;AAAA,IAC7F;AACA,WAAO,KAAK;AAAA,EACd;AACA,SAAOA;AACT,EAAE,mBAAW;AACb,QAAQ,UAAU,OAAO;AACzB,IAAO,gBAAQ;;;AC9DR,IAAI,sBAAsB,SAAS;AAAA,EACxC,aAAa;AAAA,EACb,MAAM;AAAA,EACN,GAAG;AAAA,EACH,GAAG;AAAA,EACH,WAAW;AAAA,EACX,cAAc;AAAA,EACd,YAAY;AACd,GAAG,kBAAkB;AACrB,IAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAUC,QAAO,MAAM;AACvB,WAASA,SAAQ;AACf,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC7D;AACA,EAAAA,OAAM,UAAU,YAAY,WAAY;AACtC,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,MAAM;AACnB,WAAO,UAAU,QAAQ,WAAW,UAAU,MAAM,YAAY;AAAA,EAClE;AACA,EAAAA,OAAM,UAAU,UAAU,WAAY;AACpC,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,MAAM;AACjB,WAAO,QAAQ,QAAQ,SAAS;AAAA,EAClC;AACA,EAAAA,OAAM,UAAU,cAAc,SAAU,KAAK;AAC3C,WAAO,aAAa,qBAAqB,GAAG;AAAA,EAC9C;AACA,EAAAA,OAAM,UAAU,kBAAkB,SAAU,MAAM;AAChD,SAAK,QAAQ;AAAA,EACf;AACA,EAAAA,OAAM,UAAU,kBAAkB,WAAY;AAC5C,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,KAAK,OAAO;AACf,UAAI,OAAO,MAAM;AACjB,cAAQ,OAAO,QAAQ,KAAK,OAAO;AACnC,UAAI,OAAO,gBAAgB,MAAM,MAAM,MAAM,MAAM,WAAW,MAAM,YAAY;AAChF,WAAK,KAAK,MAAM,KAAK;AACrB,WAAK,KAAK,MAAM,KAAK;AACrB,UAAI,KAAK,UAAU,GAAG;AACpB,YAAI,IAAI,MAAM;AACd,aAAK,KAAK,IAAI;AACd,aAAK,KAAK,IAAI;AACd,aAAK,SAAS;AACd,aAAK,UAAU;AAAA,MACjB;AACA,WAAK,QAAQ;AAAA,IACf;AACA,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,OAAM,mBAAmB,WAAY;AACnC,QAAI,aAAaA,OAAM;AACvB,eAAW,qBAAqB;AAAA,EAClC,EAAE;AACF,SAAOA;AACT,EAAE,mBAAW;AACb,MAAM,UAAU,OAAO;AACvB,IAAO,gBAAQ;;;AC7DR,SAAS,kBAAkB,UAAU,WAAW;AACrD,MAAI,CAAC,YAAY,aAAa,WAAW,EAAE,YAAY,IAAI;AACzD,WAAO;AAAA,EACT;AACA,SAAO,aAAa,WAAW,CAAC,IAAI,WAAW,IAAI,SAAS,IAAI,aAAa,WAAW,CAAC,SAAS,IAAI,SAAS,QAAQ,IAAI,CAAC,QAAQ,IAAI,QAAQ,QAAQ,IAAI,WAAW;AACzK;AACO,SAAS,YAAY,IAAI;AAC9B,MAAI,QAAQ,GAAG;AACf,MAAI,WAAW,MAAM,YAAY,MAAM,YAAY,KAAK,kBAAkB,MAAM,UAAU,MAAM,SAAS;AACzG,MAAI,iBAAiB,MAAM;AAC3B,MAAI,UAAU;AACZ,QAAI,cAAc,MAAM,iBAAiB,GAAG,eAAe,GAAG,aAAa,IAAI;AAC/E,QAAI,eAAe,gBAAgB,GAAG;AACpC,iBAAW,IAAI,UAAU,SAAU,QAAQ;AACzC,eAAO,SAAS;AAAA,MAClB,CAAC;AACD,wBAAkB;AAAA,IACpB;AAAA,EACF;AACA,SAAO,CAAC,UAAU,cAAc;AAClC;;;ACVA,IAAI,mBAAmB,IAAI,kBAAU,IAAI;AACzC,SAAS,eAAe,OAAO;AAC7B,MAAI,SAAS,MAAM;AACnB,SAAO,EAAE,UAAU,QAAQ,WAAW,UAAU,EAAE,MAAM,YAAY;AACtE;AACA,SAAS,uBAAuB,cAAc;AAC5C,SAAO,OAAO,iBAAiB,YAAY,iBAAiB;AAC9D;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,OAAO,MAAM;AACjB,SAAO,QAAQ,QAAQ,SAAS;AAClC;AACA,SAAS,WAAW,KAAK,OAAO;AAC9B,MAAI,MAAM,eAAe,QAAQ,MAAM,gBAAgB,GAAG;AACxD,QAAI,sBAAsB,IAAI;AAC9B,QAAI,cAAc,MAAM,cAAc,MAAM;AAC5C,QAAI,KAAK;AACT,QAAI,cAAc;AAAA,EACpB,OAAO;AACL,QAAI,KAAK;AAAA,EACX;AACF;AACA,SAAS,aAAa,KAAK,OAAO;AAChC,MAAI,MAAM,iBAAiB,QAAQ,MAAM,kBAAkB,GAAG;AAC5D,QAAI,sBAAsB,IAAI;AAC9B,QAAI,cAAc,MAAM,gBAAgB,MAAM;AAC9C,QAAI,OAAO;AACX,QAAI,cAAc;AAAA,EACpB,OAAO;AACL,QAAI,OAAO;AAAA,EACb;AACF;AACO,SAAS,oBAAoB,KAAK,SAAS,IAAI;AACpD,MAAI,QAAQ,oBAAoB,QAAQ,OAAO,QAAQ,SAAS,EAAE;AAClE,MAAI,aAAa,KAAK,GAAG;AACvB,QAAI,gBAAgB,IAAI,cAAc,OAAO,QAAQ,UAAU,QAAQ;AACvE,QAAI,OAAO,cAAc,cAAc,iBAAiB,cAAc,cAAc;AAClF,UAAI,SAAS,IAAI,UAAU;AAC3B,aAAO,cAAc,QAAQ,KAAK,GAAG,QAAQ,KAAK,CAAC;AACnD,aAAO,WAAW,GAAG,IAAI,QAAQ,YAAY,KAAK,gBAAgB;AAClE,aAAO,UAAU,QAAQ,UAAU,GAAG,QAAQ,UAAU,CAAC;AACzD,oBAAc,aAAa,MAAM;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,UAAU,KAAK,IAAI,OAAO,SAAS;AAC1C,MAAI;AACJ,MAAI,YAAY,eAAe,KAAK;AACpC,MAAI,UAAU,aAAa,KAAK;AAChC,MAAI,gBAAgB,MAAM;AAC1B,MAAI,aAAa,gBAAgB;AACjC,MAAI,YAAY,CAAC,GAAG;AACpB,OAAK,CAAC,GAAG,UAAU,eAAe,WAAW;AAC3C,OAAG,gBAAgB;AAAA,EACrB;AACA,MAAI,OAAO,GAAG,QAAQ;AACtB,MAAI,YAAY,GAAG;AACnB,MAAI,CAAC,SAAS;AACZ,QAAI,OAAO,MAAM;AACjB,QAAI,SAAS,MAAM;AACnB,QAAI,kBAAkB,WAAW,CAAC,CAAC,KAAK;AACxC,QAAI,oBAAoB,aAAa,CAAC,CAAC,OAAO;AAC9C,QAAI,iBAAiB,WAAW,CAAC,CAAC,KAAK;AACvC,QAAI,mBAAmB,aAAa,CAAC,CAAC,OAAO;AAC7C,QAAI,eAAe;AACnB,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,QAAI,gBAAgB;AACpB,QAAI,OAAO;AACX,QAAI,mBAAmB,mBAAmB;AACxC,aAAO,GAAG,gBAAgB;AAAA,IAC5B;AACA,QAAI,iBAAiB;AACnB,qBAAe,YAAY,kBAAkB,KAAK,MAAM,IAAI,IAAI,GAAG;AACnE,SAAG,uBAAuB;AAAA,IAC5B;AACA,QAAI,mBAAmB;AACrB,uBAAiB,YAAY,kBAAkB,KAAK,QAAQ,IAAI,IAAI,GAAG;AACvE,SAAG,yBAAyB;AAAA,IAC9B;AACA,QAAI,gBAAgB;AAClB,oBAAc,aAAa,CAAC,GAAG,sBAAsB,oBAAoB,KAAK,MAAM,EAAE,IAAI,GAAG;AAC7F,SAAG,sBAAsB;AAAA,IAC3B;AACA,QAAI,kBAAkB;AACpB,sBAAgB,aAAa,CAAC,GAAG,wBAAwB,oBAAoB,KAAK,QAAQ,EAAE,IAAI,GAAG;AACnG,SAAG,wBAAwB;AAAA,IAC7B;AACA,QAAI,iBAAiB;AACnB,UAAI,YAAY;AAAA,IAClB,WAAW,gBAAgB;AACzB,UAAI,aAAa;AACf,YAAI,YAAY;AAAA,MAClB,OAAO;AACL,kBAAU;AAAA,MACZ;AAAA,IACF;AACA,QAAI,mBAAmB;AACrB,UAAI,cAAc;AAAA,IACpB,WAAW,kBAAkB;AAC3B,UAAI,eAAe;AACjB,YAAI,cAAc;AAAA,MACpB,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,MAAIC,SAAQ,GAAG,eAAe;AAC9B,OAAK,SAASA,OAAM,CAAC,GAAGA,OAAM,CAAC,GAAG,GAAG,sBAAsB;AAC3D,MAAI;AACJ,MAAI;AACJ,MAAI,IAAI,eAAe,MAAM,UAAU;AACrC,SAAK,YAAY,EAAE,GAAG,WAAW,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AAAA,EAC/D;AACA,MAAI,eAAe;AACnB,MAAI,aAAa,YAAY,mBAAmB;AAC9C,SAAK,OAAO,IAAI,GAAG;AACnB,QAAI,YAAY;AACd,WAAK,WAAW,IAAI;AAAA,IACtB,OAAO;AACL,WAAK,WAAW,GAAG;AACnB,qBAAe;AAAA,IACjB;AACA,SAAK,MAAM;AACX,OAAG,UAAU,MAAM,GAAG,OAAO,OAAO;AACpC,SAAK,SAAS;AACd,OAAG,YAAY;AAAA,EACjB;AACA,MAAI,cAAc;AAChB,SAAK,YAAY,KAAK,aAAa,gBAAgB,CAAC;AAAA,EACtD;AACA,MAAI,UAAU;AACZ,QAAI,YAAY,QAAQ;AACxB,QAAI,iBAAiB;AAAA,EACvB;AACA,MAAI,CAAC,SAAS;AACZ,QAAI,MAAM,aAAa;AACrB,UAAI,WAAW;AACb,qBAAa,KAAK,KAAK;AAAA,MACzB;AACA,UAAI,SAAS;AACX,mBAAW,KAAK,KAAK;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,SAAS;AACX,mBAAW,KAAK,KAAK;AAAA,MACvB;AACA,UAAI,WAAW;AACb,qBAAa,KAAK,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU;AACZ,QAAI,YAAY,CAAC,CAAC;AAAA,EACpB;AACF;AACA,SAAS,WAAW,KAAK,IAAI,OAAO;AAClC,MAAI,QAAQ,GAAG,UAAU,oBAAoB,MAAM,OAAO,GAAG,SAAS,IAAI,GAAG,MAAM;AACnF,MAAI,CAAC,SAAS,CAAC,aAAa,KAAK,GAAG;AAClC;AAAA,EACF;AACA,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,QAAQ,GAAG,SAAS;AACxB,MAAI,SAAS,GAAG,UAAU;AAC1B,MAAI,SAAS,MAAM,QAAQ,MAAM;AACjC,MAAI,SAAS,QAAQ,UAAU,MAAM;AACnC,YAAQ,SAAS;AAAA,EACnB,WAAW,UAAU,QAAQ,SAAS,MAAM;AAC1C,aAAS,QAAQ;AAAA,EACnB,WAAW,SAAS,QAAQ,UAAU,MAAM;AAC1C,YAAQ,MAAM;AACd,aAAS,MAAM;AAAA,EACjB;AACA,MAAI,MAAM,UAAU,MAAM,SAAS;AACjC,QAAI,KAAK,MAAM,MAAM;AACrB,QAAI,KAAK,MAAM,MAAM;AACrB,QAAI,UAAU,OAAO,IAAI,IAAI,MAAM,QAAQ,MAAM,SAAS,GAAG,GAAG,OAAO,MAAM;AAAA,EAC/E,WAAW,MAAM,MAAM,MAAM,IAAI;AAC/B,QAAI,KAAK,MAAM;AACf,QAAI,KAAK,MAAM;AACf,QAAI,SAAS,QAAQ;AACrB,QAAI,UAAU,SAAS;AACvB,QAAI,UAAU,OAAO,IAAI,IAAI,QAAQ,SAAS,GAAG,GAAG,OAAO,MAAM;AAAA,EACnE,OAAO;AACL,QAAI,UAAU,OAAO,GAAG,GAAG,OAAO,MAAM;AAAA,EAC1C;AACF;AACA,SAAS,UAAU,KAAK,IAAI,OAAO;AACjC,MAAI;AACJ,MAAI,OAAO,MAAM;AACjB,UAAQ,SAAS,QAAQ;AACzB,MAAI,MAAM;AACR,QAAI,OAAO,MAAM,QAAQ;AACzB,QAAI,YAAY,MAAM;AACtB,QAAI,eAAe,MAAM;AACzB,QAAI,WAAW;AACf,QAAI,iBAAiB;AACrB,QAAI,IAAI,eAAe,MAAM,UAAU;AACrC,WAAK,YAAY,EAAE,GAAG,WAAW,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AAAA,IAC/D;AACA,QAAI,UAAU;AACZ,UAAI,YAAY,QAAQ;AACxB,UAAI,iBAAiB;AAAA,IACvB;AACA,QAAI,MAAM,aAAa;AACrB,UAAI,eAAe,KAAK,GAAG;AACzB,YAAI,WAAW,MAAM,MAAM,GAAG,MAAM,CAAC;AAAA,MACvC;AACA,UAAI,aAAa,KAAK,GAAG;AACvB,YAAI,SAAS,MAAM,MAAM,GAAG,MAAM,CAAC;AAAA,MACrC;AAAA,IACF,OAAO;AACL,UAAI,aAAa,KAAK,GAAG;AACvB,YAAI,SAAS,MAAM,MAAM,GAAG,MAAM,CAAC;AAAA,MACrC;AACA,UAAI,eAAe,KAAK,GAAG;AACzB,YAAI,WAAW,MAAM,MAAM,GAAG,MAAM,CAAC;AAAA,MACvC;AAAA,IACF;AACA,QAAI,UAAU;AACZ,UAAI,YAAY,CAAC,CAAC;AAAA,IACpB;AAAA,EACF;AACF;AACA,IAAI,sBAAsB,CAAC,cAAc,iBAAiB,eAAe;AACzE,IAAI,eAAe,CAAC,CAAC,WAAW,MAAM,GAAG,CAAC,YAAY,OAAO,GAAG,CAAC,cAAc,EAAE,CAAC;AAClF,SAAS,gBAAgB,KAAK,OAAO,WAAW,aAAa,OAAO;AAClE,MAAI,eAAe;AACnB,MAAI,CAAC,aAAa;AAChB,gBAAY,aAAa,CAAC;AAC1B,QAAI,UAAU,WAAW;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,eAAe,MAAM,YAAY,UAAU,SAAS;AACtD,mBAAe,KAAK,KAAK;AACzB,mBAAe;AACf,QAAI,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS,CAAC,GAAG,CAAC;AACpD,QAAI,cAAc,MAAM,OAAO,IAAI,qBAAqB,UAAU;AAAA,EACpE;AACA,MAAI,eAAe,MAAM,UAAU,UAAU,OAAO;AAClD,QAAI,CAAC,cAAc;AACjB,qBAAe,KAAK,KAAK;AACzB,qBAAe;AAAA,IACjB;AACA,QAAI,2BAA2B,MAAM,SAAS,qBAAqB;AAAA,EACrE;AACA,WAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,QAAI,WAAW,oBAAoB,CAAC;AACpC,QAAI,eAAe,MAAM,QAAQ,MAAM,UAAU,QAAQ,GAAG;AAC1D,UAAI,CAAC,cAAc;AACjB,uBAAe,KAAK,KAAK;AACzB,uBAAe;AAAA,MACjB;AACA,UAAI,QAAQ,IAAI,IAAI,OAAO,MAAM,QAAQ,KAAK;AAAA,IAChD;AAAA,EACF;AACA,MAAI,eAAe,MAAM,gBAAgB,UAAU,aAAa;AAC9D,QAAI,CAAC,cAAc;AACjB,qBAAe,KAAK,KAAK;AACzB,qBAAe;AAAA,IACjB;AACA,QAAI,cAAc,MAAM,eAAe,qBAAqB;AAAA,EAC9D;AACA,SAAO;AACT;AACA,SAAS,2BAA2B,KAAK,IAAI,QAAQ,aAAa,OAAO;AACvE,MAAI,QAAQ,SAAS,IAAI,MAAM,OAAO;AACtC,MAAI,YAAY,cAAc,OAAO,UAAU,SAAS,QAAQ,MAAM,OAAO,KAAK,CAAC;AACnF,MAAI,UAAU,WAAW;AACvB,WAAO;AAAA,EACT;AACA,MAAI,eAAe,gBAAgB,KAAK,OAAO,WAAW,aAAa,KAAK;AAC5E,MAAI,eAAe,MAAM,SAAS,UAAU,MAAM;AAChD,QAAI,CAAC,cAAc;AACjB,qBAAe,KAAK,KAAK;AACzB,qBAAe;AAAA,IACjB;AACA,2BAAuB,MAAM,IAAI,MAAM,IAAI,YAAY,MAAM;AAAA,EAC/D;AACA,MAAI,eAAe,MAAM,WAAW,UAAU,QAAQ;AACpD,QAAI,CAAC,cAAc;AACjB,qBAAe,KAAK,KAAK;AACzB,qBAAe;AAAA,IACjB;AACA,2BAAuB,MAAM,MAAM,MAAM,IAAI,cAAc,MAAM;AAAA,EACnE;AACA,MAAI,eAAe,MAAM,YAAY,UAAU,SAAS;AACtD,QAAI,CAAC,cAAc;AACjB,qBAAe,KAAK,KAAK;AACzB,qBAAe;AAAA,IACjB;AACA,QAAI,cAAc,MAAM,WAAW,OAAO,IAAI,MAAM;AAAA,EACtD;AACA,MAAI,GAAG,UAAU,GAAG;AAClB,QAAI,YAAY,MAAM;AACtB,QAAI,eAAe,aAAa,MAAM,iBAAiB,GAAG,eAAe,GAAG,aAAa,IAAI;AAC7F,QAAI,IAAI,cAAc,cAAc;AAClC,UAAI,CAAC,cAAc;AACjB,uBAAe,KAAK,KAAK;AACzB,uBAAe;AAAA,MACjB;AACA,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,QAAI,OAAO,aAAa,CAAC;AACzB,QAAI,WAAW,KAAK,CAAC;AACrB,QAAI,eAAe,MAAM,QAAQ,MAAM,UAAU,QAAQ,GAAG;AAC1D,UAAI,CAAC,cAAc;AACjB,uBAAe,KAAK,KAAK;AACzB,uBAAe;AAAA,MACjB;AACA,UAAI,QAAQ,IAAI,MAAM,QAAQ,KAAK,KAAK,CAAC;AAAA,IAC3C;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,KAAK,IAAI,QAAQ,aAAa,OAAO;AAC3D,SAAO,gBAAgB,KAAK,SAAS,IAAI,MAAM,OAAO,GAAG,UAAU,SAAS,QAAQ,MAAM,OAAO,GAAG,aAAa,KAAK;AACxH;AACA,SAAS,oBAAoB,KAAK,IAAI;AACpC,MAAI,IAAI,GAAG;AACX,MAAIC,OAAM,IAAI,OAAO;AACrB,MAAI,GAAG;AACL,QAAI,aAAaA,OAAM,EAAE,CAAC,GAAGA,OAAM,EAAE,CAAC,GAAGA,OAAM,EAAE,CAAC,GAAGA,OAAM,EAAE,CAAC,GAAGA,OAAM,EAAE,CAAC,GAAGA,OAAM,EAAE,CAAC,CAAC;AAAA,EACzF,OAAO;AACL,QAAI,aAAaA,MAAK,GAAG,GAAGA,MAAK,GAAG,CAAC;AAAA,EACvC;AACF;AACA,SAAS,iBAAiB,WAAW,KAAK,OAAO;AAC/C,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,WAAW,UAAU,CAAC;AAC1B,iBAAa,cAAc,SAAS,WAAW;AAC/C,wBAAoB,KAAK,QAAQ;AACjC,QAAI,UAAU;AACd,aAAS,UAAU,KAAK,SAAS,KAAK;AACtC,QAAI,KAAK;AAAA,EACX;AACA,QAAM,aAAa;AACrB;AACA,SAAS,mBAAmB,IAAI,IAAI;AAClC,MAAI,MAAM,IAAI;AACZ,WAAO,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC;AAAA,EACtH,WAAW,CAAC,MAAM,CAAC,IAAI;AACrB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AACrB,IAAI,wBAAwB;AAC5B,SAAS,aAAa,OAAO;AAC3B,MAAI,UAAU,aAAa,KAAK;AAChC,MAAI,YAAY,eAAe,KAAK;AACpC,SAAO,EAAE,MAAM,YAAY,EAAE,CAAC,UAAU,CAAC,cAAc,WAAW,OAAO,MAAM,SAAS,YAAY,aAAa,OAAO,MAAM,WAAW,YAAY,MAAM,gBAAgB,KAAK,MAAM,gBAAgB,KAAK,MAAM,cAAc;AACjO;AACA,SAAS,eAAe,KAAK,OAAO;AAClC,QAAM,aAAa,IAAI,KAAK;AAC5B,QAAM,eAAe,IAAI,OAAO;AAChC,QAAM,YAAY;AAClB,QAAM,cAAc;AACtB;AACA,SAAS,SAAS,IAAI,SAAS;AAC7B,SAAO,UAAU,GAAG,gBAAgB,GAAG,QAAQ,GAAG;AACpD;AACO,SAAS,YAAY,KAAK,IAAI;AACnC,QAAM,KAAK,IAAI;AAAA,IACb,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,EACd,GAAG,IAAI;AACT;AACO,SAAS,MAAM,KAAK,IAAI,OAAO,QAAQ;AAC5C,MAAI,IAAI,GAAG;AACX,MAAI,CAAC,GAAG,gBAAgB,MAAM,WAAW,MAAM,YAAY,OAAO,KAAK,GAAG;AACxE,OAAG,WAAW,CAAC;AACf,OAAG,eAAe;AAClB;AAAA,EACF;AACA,MAAI,YAAY,GAAG;AACnB,MAAI,kBAAkB,MAAM;AAC5B,MAAI,oBAAoB;AACxB,MAAI,gBAAgB;AACpB,MAAI,CAAC,mBAAmB,kBAAkB,WAAW,eAAe,GAAG;AACrE,QAAI,mBAAmB,gBAAgB,QAAQ;AAC7C,qBAAe,KAAK,KAAK;AACzB,UAAI,QAAQ;AACZ,sBAAgB,oBAAoB;AACpC,YAAM,kBAAkB;AACxB,YAAM,aAAa;AACnB,YAAM,SAAS;AAAA,IACjB;AACA,QAAI,aAAa,UAAU,QAAQ;AACjC,qBAAe,KAAK,KAAK;AACzB,UAAI,KAAK;AACT,uBAAiB,WAAW,KAAK,KAAK;AACtC,0BAAoB;AAAA,IACtB;AACA,UAAM,kBAAkB;AAAA,EAC1B;AACA,MAAI,MAAM,YAAY;AACpB,OAAG,eAAe;AAClB;AAAA,EACF;AACA,KAAG,eAAe,GAAG,YAAY;AACjC,KAAG,iBAAiB;AACpB,MAAI,SAAS,MAAM;AACnB,MAAI,CAAC,QAAQ;AACX,oBAAgB,oBAAoB;AAAA,EACtC;AACA,MAAI,eAAe,cAAc,gBAAQ,GAAG,aAAa,aAAa,GAAG,KAAK;AAC9E,MAAI,qBAAqB,mBAAmB,GAAG,OAAO,SAAS,GAAG;AAChE,mBAAe,KAAK,KAAK;AACzB,wBAAoB,KAAK,EAAE;AAAA,EAC7B,WAAW,CAAC,cAAc;AACxB,mBAAe,KAAK,KAAK;AAAA,EAC3B;AACA,MAAI,QAAQ,SAAS,IAAI,MAAM,OAAO;AACtC,MAAI,cAAc,cAAM;AACtB,QAAI,MAAM,iBAAiB,gBAAgB;AACzC,sBAAgB;AAChB,YAAM,eAAe;AAAA,IACvB;AACA,+BAA2B,KAAK,IAAI,QAAQ,eAAe,KAAK;AAChE,QAAI,CAAC,gBAAgB,CAAC,MAAM,aAAa,CAAC,MAAM,aAAa;AAC3D,UAAI,UAAU;AAAA,IAChB;AACA,cAAU,KAAK,IAAI,OAAO,YAAY;AACtC,QAAI,cAAc;AAChB,YAAM,YAAY,MAAM,QAAQ;AAChC,YAAM,cAAc,MAAM,UAAU;AAAA,IACtC;AAAA,EACF,OAAO;AACL,QAAI,cAAc,eAAO;AACvB,UAAI,MAAM,iBAAiB,gBAAgB;AACzC,wBAAgB;AAChB,cAAM,eAAe;AAAA,MACvB;AACA,iCAA2B,KAAK,IAAI,QAAQ,eAAe,KAAK;AAChE,gBAAU,KAAK,IAAI,KAAK;AAAA,IAC1B,WAAW,cAAc,eAAS;AAChC,UAAI,MAAM,iBAAiB,iBAAiB;AAC1C,wBAAgB;AAChB,cAAM,eAAe;AAAA,MACvB;AACA,qBAAe,KAAK,IAAI,QAAQ,eAAe,KAAK;AACpD,iBAAW,KAAK,IAAI,KAAK;AAAA,IAC3B,WAAW,GAAG,yBAAyB;AACrC,UAAI,MAAM,iBAAiB,uBAAuB;AAChD,wBAAgB;AAChB,cAAM,eAAe;AAAA,MACvB;AACA,uBAAiB,KAAK,IAAI,KAAK;AAAA,IACjC;AAAA,EACF;AACA,MAAI,gBAAgB,QAAQ;AAC1B,mBAAe,KAAK,KAAK;AAAA,EAC3B;AACA,KAAG,gBAAgB;AACnB,KAAG,cAAc,GAAG,WAAW;AAC/B,QAAM,SAAS;AACf,KAAG,UAAU;AACb,KAAG,eAAe;AACpB;AACA,SAAS,iBAAiB,KAAK,IAAI,OAAO;AACxC,MAAI,eAAe,GAAG,gBAAgB;AACtC,MAAI,uBAAuB,GAAG,wBAAwB;AACtD,MAAI,KAAK;AACT,MAAI,aAAa;AAAA,IACf,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,WAAW,MAAM;AAAA,IACjB,YAAY,MAAM;AAAA,IAClB,SAAS,MAAM;AAAA,EACjB;AACA,MAAI;AACJ,MAAIC;AACJ,OAAK,IAAI,GAAG,UAAU,GAAGA,OAAM,aAAa,QAAQ,IAAIA,MAAK,KAAK;AAChE,QAAI,cAAc,aAAa,CAAC;AAChC,gBAAY,eAAe,YAAY,YAAY;AACnD,gBAAY,iBAAiB;AAC7B,UAAM,KAAK,aAAa,YAAY,MAAMA,OAAM,CAAC;AACjD,gBAAY,gBAAgB;AAC5B,gBAAY,cAAc,YAAY,WAAW;AACjD,eAAW,SAAS;AAAA,EACtB;AACA,WAAS,MAAM,GAAG,QAAQ,qBAAqB,QAAQ,MAAM,OAAO,OAAO;AACzE,QAAI,cAAc,qBAAqB,GAAG;AAC1C,gBAAY,eAAe,YAAY,YAAY;AACnD,gBAAY,iBAAiB;AAC7B,UAAM,KAAK,aAAa,YAAY,QAAQ,QAAQ,CAAC;AACrD,gBAAY,gBAAgB;AAC5B,gBAAY,cAAc,YAAY,WAAW;AACjD,eAAW,SAAS;AAAA,EACtB;AACA,KAAG,0BAA0B;AAC7B,KAAG,WAAW;AACd,MAAI,QAAQ;AACd;;;AChgBA,IAAI,YAAY;AACT,SAAS,aAAa,MAAM,gBAAgB,MAAM,UAAU,SAAS;AAC1E,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,MAAI,aAAa,OAAO,IAAI,MAAM,IAAI;AACtC,YAAU,uBAAuB,gBAAgB,MAAM,UAAU,OAAO;AACxE,WAAS,IAAI,GAAGC,OAAM,UAAU,QAAQ,IAAIA,MAAK,KAAK;AACpD,cAAU,CAAC,IAAI,mBAAmB,UAAU,CAAC,GAAG,OAAO;AAAA,EACzD;AACA,SAAO,UAAU,KAAK,IAAI;AAC5B;AACA,SAAS,uBAAuB,gBAAgB,MAAM,UAAU,SAAS;AACvE,YAAU,WAAW,CAAC;AACtB,MAAI,eAAe,OAAO,CAAC,GAAG,OAAO;AACrC,eAAa,OAAO;AACpB,aAAW,UAAU,UAAU,KAAK;AACpC,eAAa,gBAAgB,UAAU,QAAQ,eAAe,CAAC;AAC/D,MAAI,UAAU,aAAa,UAAU,UAAU,QAAQ,SAAS,CAAC;AACjE,eAAa,cAAc,SAAS,KAAK,IAAI;AAC7C,MAAI,eAAe,aAAa,eAAe,SAAS,KAAK,IAAI;AACjE,eAAa,cAAc,UAAU,QAAQ,aAAa,EAAE;AAC5D,MAAI,eAAe,iBAAiB,KAAK,IAAI,GAAG,iBAAiB,CAAC;AAClE,WAAS,IAAI,GAAG,IAAI,WAAW,gBAAgB,cAAc,KAAK;AAChE,oBAAgB;AAAA,EAClB;AACA,MAAI,gBAAgB,SAAS,UAAU,IAAI;AAC3C,MAAI,gBAAgB,cAAc;AAChC,eAAW;AACX,oBAAgB;AAAA,EAClB;AACA,iBAAe,iBAAiB;AAChC,eAAa,WAAW;AACxB,eAAa,gBAAgB;AAC7B,eAAa,eAAe;AAC5B,eAAa,iBAAiB;AAC9B,SAAO;AACT;AACA,SAAS,mBAAmB,UAAU,SAAS;AAC7C,MAAI,iBAAiB,QAAQ;AAC7B,MAAI,OAAO,QAAQ;AACnB,MAAI,eAAe,QAAQ;AAC3B,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,MAAI,YAAY,SAAS,UAAU,IAAI;AACvC,MAAI,aAAa,gBAAgB;AAC/B,WAAO;AAAA,EACT;AACA,WAAS,IAAI,KAAI,KAAK;AACpB,QAAI,aAAa,gBAAgB,KAAK,QAAQ,eAAe;AAC3D,kBAAY,QAAQ;AACpB;AAAA,IACF;AACA,QAAI,YAAY,MAAM,IAAI,eAAe,UAAU,cAAc,QAAQ,cAAc,QAAQ,WAAW,IAAI,YAAY,IAAI,KAAK,MAAM,SAAS,SAAS,eAAe,SAAS,IAAI;AACvL,eAAW,SAAS,OAAO,GAAG,SAAS;AACvC,gBAAY,SAAS,UAAU,IAAI;AAAA,EACrC;AACA,MAAI,aAAa,IAAI;AACnB,eAAW,QAAQ;AAAA,EACrB;AACA,SAAO;AACT;AACA,SAAS,eAAe,MAAM,cAAc,cAAc,aAAa;AACrE,MAAI,QAAQ;AACZ,MAAI,IAAI;AACR,WAASA,OAAM,KAAK,QAAQ,IAAIA,QAAO,QAAQ,cAAc,KAAK;AAChE,QAAI,WAAW,KAAK,WAAW,CAAC;AAChC,aAAS,KAAK,YAAY,YAAY,MAAM,eAAe;AAAA,EAC7D;AACA,SAAO;AACT;AACO,SAAS,eAAe,MAAM,OAAO;AAC1C,UAAQ,SAAS,QAAQ;AACzB,MAAI,WAAW,MAAM;AACrB,MAAI,UAAU,MAAM;AACpB,MAAI,OAAO,MAAM;AACjB,MAAI,WAAW,aAAa;AAC5B,MAAI,uBAAuB,cAAc,IAAI;AAC7C,MAAI,aAAa,UAAU,MAAM,YAAY,oBAAoB;AACjE,MAAI,eAAe,CAAC,CAAC,MAAM;AAC3B,MAAI,uBAAuB,MAAM,iBAAiB;AAClD,MAAI,QAAQ,MAAM;AAClB,MAAI;AACJ,MAAI,SAAS,SAAS,aAAa,WAAW,aAAa,aAAa;AACtE,YAAQ,OAAO,SAAS,MAAM,MAAM,MAAM,OAAO,aAAa,YAAY,CAAC,EAAE,QAAQ,CAAC;AAAA,EACxF,OAAO;AACL,YAAQ,OAAO,KAAK,MAAM,IAAI,IAAI,CAAC;AAAA,EACrC;AACA,MAAI,gBAAgB,MAAM,SAAS;AACnC,MAAI,SAAS,UAAU,MAAM,QAAQ,aAAa;AAClD,MAAI,gBAAgB,UAAU,sBAAsB;AAClD,QAAI,YAAY,KAAK,MAAM,SAAS,UAAU;AAC9C,YAAQ,MAAM,MAAM,GAAG,SAAS;AAAA,EAClC;AACA,MAAI,QAAQ,YAAY,SAAS,MAAM;AACrC,QAAI,UAAU,uBAAuB,OAAO,MAAM,MAAM,UAAU;AAAA,MAChE,SAAS,MAAM;AAAA,MACf,aAAa,MAAM;AAAA,IACrB,CAAC;AACD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,CAAC,IAAI,mBAAmB,MAAM,CAAC,GAAG,OAAO;AAAA,IACjD;AAAA,EACF;AACA,MAAI,cAAc;AAClB,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,mBAAe,KAAK,IAAI,SAAS,MAAM,CAAC,GAAG,IAAI,GAAG,YAAY;AAAA,EAChE;AACA,MAAI,SAAS,MAAM;AACjB,YAAQ;AAAA,EACV;AACA,MAAI,aAAa;AACjB,MAAI,SAAS;AACX,mBAAe,QAAQ,CAAC,IAAI,QAAQ,CAAC;AACrC,kBAAc,QAAQ,CAAC,IAAI,QAAQ,CAAC;AACpC,aAAS,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACjC;AACA,MAAI,cAAc;AAChB,iBAAa;AAAA,EACf;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,gBAAgB,2BAAY;AAC9B,WAASC,iBAAgB;AAAA,EAAC;AAC1B,SAAOA;AACT,EAAE;AACF,IAAI,eAAe,2BAAY;AAC7B,WAASC,cAAa,QAAQ;AAC5B,SAAK,SAAS,CAAC;AACf,QAAI,QAAQ;AACV,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AACA,SAAOA;AACT,EAAE;AACF,IAAI,uBAAuB,2BAAY;AACrC,WAASC,wBAAuB;AAC9B,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,QAAQ,CAAC;AAAA,EAChB;AACA,SAAOA;AACT,EAAE;AAEK,SAAS,cAAc,MAAM,OAAO;AACzC,MAAI,eAAe,IAAI,qBAAqB;AAC5C,UAAQ,SAAS,QAAQ;AACzB,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,MAAI,WAAW,MAAM;AACrB,MAAI,YAAY,MAAM;AACtB,MAAI,WAAW,MAAM;AACrB,MAAI,YAAY,aAAa,WAAW,aAAa,eAAe,YAAY,OAAO;AAAA,IACrF,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,UAAU,aAAa;AAAA,EACzB,IAAI;AACJ,MAAI,YAAY,UAAU,YAAY;AACtC,MAAI;AACJ,UAAQ,SAAS,UAAU,KAAK,IAAI,MAAM,MAAM;AAC9C,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,WAAW;AAC5B,iBAAW,cAAc,KAAK,UAAU,WAAW,YAAY,GAAG,OAAO,QAAQ;AAAA,IACnF;AACA,eAAW,cAAc,OAAO,CAAC,GAAG,OAAO,UAAU,OAAO,CAAC,CAAC;AAC9D,gBAAY,UAAU;AAAA,EACxB;AACA,MAAI,YAAY,KAAK,QAAQ;AAC3B,eAAW,cAAc,KAAK,UAAU,WAAW,KAAK,MAAM,GAAG,OAAO,QAAQ;AAAA,EAClF;AACA,MAAI,cAAc,CAAC;AACnB,MAAI,mBAAmB;AACvB,MAAI,kBAAkB;AACtB,MAAI,aAAa,MAAM;AACvB,MAAI,WAAW,aAAa;AAC5B,MAAI,eAAe,MAAM,iBAAiB;AAC1C,WAAS,WAAWC,OAAMC,YAAWC,aAAY;AAC/C,IAAAF,MAAK,QAAQC;AACb,IAAAD,MAAK,aAAaE;AAClB,wBAAoBA;AACpB,sBAAkB,KAAK,IAAI,iBAAiBD,UAAS;AAAA,EACvD;AACA,QAAO,UAAS,IAAI,GAAG,IAAI,aAAa,MAAM,QAAQ,KAAK;AACzD,QAAI,OAAO,aAAa,MAAM,CAAC;AAC/B,QAAI,aAAa;AACjB,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,UAAI,QAAQ,KAAK,OAAO,CAAC;AACzB,UAAI,aAAa,MAAM,aAAa,MAAM,KAAK,MAAM,SAAS,KAAK,CAAC;AACpE,UAAI,cAAc,MAAM,cAAc,WAAW;AACjD,UAAI,WAAW,cAAc,YAAY,CAAC,IAAI,YAAY,CAAC,IAAI;AAC/D,UAAI,OAAO,MAAM,OAAO,WAAW,QAAQ,MAAM;AACjD,YAAM,gBAAgB,cAAc,IAAI;AACxC,UAAI,cAAc,UAAU,WAAW,QAAQ,MAAM,aAAa;AAClE,YAAM,cAAc;AACpB,sBAAgB,eAAe,YAAY,CAAC,IAAI,YAAY,CAAC;AAC7D,YAAM,SAAS;AACf,YAAM,aAAa,UAAU,WAAW,YAAY,MAAM,YAAY,WAAW;AACjF,YAAM,QAAQ,cAAc,WAAW,SAAS,MAAM;AACtD,YAAM,gBAAgB,cAAc,WAAW,iBAAiB;AAChE,UAAI,gBAAgB,aAAa,QAAQ,mBAAmB,MAAM,aAAa,WAAW;AACxF,YAAI,IAAI,GAAG;AACT,eAAK,SAAS,KAAK,OAAO,MAAM,GAAG,CAAC;AACpC,qBAAW,MAAM,WAAW,UAAU;AACtC,uBAAa,QAAQ,aAAa,MAAM,MAAM,GAAG,IAAI,CAAC;AAAA,QACxD,OAAO;AACL,uBAAa,QAAQ,aAAa,MAAM,MAAM,GAAG,CAAC;AAAA,QACpD;AACA,cAAM;AAAA,MACR;AACA,UAAI,kBAAkB,WAAW;AACjC,UAAI,yBAAyB,mBAAmB,QAAQ,oBAAoB;AAC5E,UAAI,OAAO,oBAAoB,YAAY,gBAAgB,OAAO,gBAAgB,SAAS,CAAC,MAAM,KAAK;AACrG,cAAM,eAAe;AACrB,oBAAY,KAAK,KAAK;AACtB,cAAM,eAAe,SAAS,MAAM,MAAM,IAAI;AAAA,MAChD,OAAO;AACL,YAAI,wBAAwB;AAC1B,cAAI,sBAAsB,WAAW;AACrC,cAAI,QAAQ,uBAAuB,oBAAoB;AACvD,cAAI,OAAO;AACT,oBAAoB,eAAe,KAAK;AACxC,gBAAgB,aAAa,KAAK,GAAG;AACnC,oBAAM,QAAQ,KAAK,IAAI,MAAM,OAAO,MAAM,QAAQ,cAAc,MAAM,MAAM;AAAA,YAC9E;AAAA,UACF;AAAA,QACF;AACA,YAAI,mBAAmB,YAAY,YAAY,OAAO,WAAW,YAAY;AAC7E,YAAI,oBAAoB,QAAQ,mBAAmB,MAAM,OAAO;AAC9D,cAAI,CAAC,0BAA0B,mBAAmB,UAAU;AAC1D,kBAAM,OAAO;AACb,kBAAM,QAAQ,MAAM,eAAe;AAAA,UACrC,OAAO;AACL,kBAAM,OAAO,aAAa,MAAM,MAAM,mBAAmB,UAAU,MAAM,MAAM,UAAU;AAAA,cACvF,SAAS,MAAM;AAAA,YACjB,CAAC;AACD,kBAAM,QAAQ,MAAM,eAAe,SAAS,MAAM,MAAM,IAAI;AAAA,UAC9D;AAAA,QACF,OAAO;AACL,gBAAM,eAAe,SAAS,MAAM,MAAM,IAAI;AAAA,QAChD;AAAA,MACF;AACA,YAAM,SAAS;AACf,mBAAa,MAAM;AACnB,qBAAe,aAAa,KAAK,IAAI,YAAY,MAAM,UAAU;AAAA,IACnE;AACA,eAAW,MAAM,WAAW,UAAU;AAAA,EACxC;AACA,eAAa,aAAa,aAAa,QAAQ,UAAU,UAAU,eAAe;AAClF,eAAa,cAAc,aAAa,SAAS,UAAU,WAAW,gBAAgB;AACtF,eAAa,gBAAgB;AAC7B,eAAa,eAAe;AAC5B,MAAI,YAAY;AACd,iBAAa,cAAc,WAAW,CAAC,IAAI,WAAW,CAAC;AACvD,iBAAa,eAAe,WAAW,CAAC,IAAI,WAAW,CAAC;AAAA,EAC1D;AACA,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,QAAI,QAAQ,YAAY,CAAC;AACzB,QAAI,eAAe,MAAM;AACzB,UAAM,QAAQ,SAAS,cAAc,EAAE,IAAI,MAAM,aAAa;AAAA,EAChE;AACA,SAAO;AACT;AACA,SAAS,WAAW,OAAO,KAAK,OAAO,UAAU,WAAW;AAC1D,MAAI,aAAa,QAAQ;AACzB,MAAI,aAAa,aAAa,MAAM,KAAK,SAAS,KAAK,CAAC;AACxD,MAAI,QAAQ,MAAM;AAClB,MAAI,OAAO,WAAW,QAAQ,MAAM;AACpC,MAAI,UAAU;AACd,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU;AACZ,QAAI,eAAe,WAAW;AAC9B,QAAI,gBAAgB,eAAe,aAAa,CAAC,IAAI,aAAa,CAAC,IAAI;AACvE,QAAI,WAAW,SAAS,QAAQ,WAAW,UAAU,QAAQ;AAC3D,UAAI,eAAe,aAAa,WAAW,OAAO,SAAS,KAAK,IAAI;AACpE,UAAI,MAAM,SAAS,GAAG;AACpB,YAAI,eAAe,SAAS,aAAa,SAAS,OAAO;AACvD,qBAAW,IAAI,MAAM,IAAI;AACzB,oBAAU;AAAA,QACZ;AAAA,MACF;AACA,eAAS,aAAa;AAAA,IACxB,OAAO;AACL,UAAI,MAAM,SAAS,KAAK,MAAM,SAAS,OAAO,SAAS,UAAU,SAAS,UAAU;AACpF,eAAS,aAAa,IAAI,aAAa;AACvC,oBAAc,IAAI;AAClB,iBAAW,IAAI;AAAA,IACjB;AAAA,EACF,OAAO;AACL,eAAW,IAAI,MAAM,IAAI;AAAA,EAC3B;AACA,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,OAAO,SAAS,CAAC;AACrB,QAAI,QAAQ,IAAI,cAAc;AAC9B,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,UAAM,eAAe,CAAC,QAAQ,CAAC;AAC/B,QAAI,OAAO,WAAW,UAAU,UAAU;AACxC,YAAM,QAAQ,WAAW;AAAA,IAC3B,OAAO;AACL,YAAM,QAAQ,cAAc,YAAY,CAAC,IAAI,SAAS,MAAM,IAAI;AAAA,IAClE;AACA,QAAI,CAAC,KAAK,CAAC,SAAS;AAClB,UAAI,UAAU,MAAM,MAAM,SAAS,CAAC,MAAM,MAAM,CAAC,IAAI,IAAI,aAAa,IAAI;AAC1E,UAAI,YAAY,OAAO;AACvB,oBAAc,KAAK,OAAO,CAAC,EAAE,eAAe,OAAO,CAAC,IAAI,SAAS,QAAQ,CAAC,aAAa,eAAe,OAAO,KAAK,KAAK;AAAA,IACzH,OAAO;AACL,YAAM,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;AAAA,IACtC;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,IAAI;AAC9B,MAAI,OAAO,GAAG,WAAW,CAAC;AAC1B,SAAO,QAAQ,MAAQ,QAAQ,OAAS,QAAQ,OAAS,QAAQ,QAAU,QAAQ,QAAU,QAAQ,QAAU,QAAQ,QAAU,QAAQ;AAC3I;AACA,IAAI,eAAe,OAAO,UAAU,MAAM,EAAE,GAAG,SAAU,KAAK,IAAI;AAChE,MAAI,EAAE,IAAI;AACV,SAAO;AACT,GAAG,CAAC,CAAC;AACL,SAAS,gBAAgB,IAAI;AAC3B,MAAI,mBAAmB,EAAE,GAAG;AAC1B,QAAI,aAAa,EAAE,GAAG;AACpB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,SAAS,MAAM,MAAM,WAAW,YAAY,gBAAgB;AACnE,MAAI,QAAQ,CAAC;AACb,MAAI,cAAc,CAAC;AACnB,MAAI,OAAO;AACX,MAAI,cAAc;AAClB,MAAI,mBAAmB;AACvB,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,KAAK,KAAK,OAAO,CAAC;AACtB,QAAI,OAAO,MAAM;AACf,UAAI,aAAa;AACf,gBAAQ;AACR,sBAAc;AAAA,MAChB;AACA,YAAM,KAAK,IAAI;AACf,kBAAY,KAAK,UAAU;AAC3B,aAAO;AACP,oBAAc;AACd,yBAAmB;AACnB,mBAAa;AACb;AAAA,IACF;AACA,QAAI,UAAU,SAAS,IAAI,IAAI;AAC/B,QAAI,SAAS,aAAa,QAAQ,CAAC,gBAAgB,EAAE;AACrD,QAAI,CAAC,MAAM,SAAS,iBAAiB,aAAa,UAAU,YAAY,aAAa,UAAU,WAAW;AACxG,UAAI,CAAC,YAAY;AACf,YAAI,QAAQ;AACV,gBAAM,KAAK,WAAW;AACtB,sBAAY,KAAK,gBAAgB;AACjC,wBAAc;AACd,6BAAmB;AAAA,QACrB,OAAO;AACL,gBAAM,KAAK,EAAE;AACb,sBAAY,KAAK,OAAO;AAAA,QAC1B;AAAA,MACF,WAAW,QAAQ,aAAa;AAC9B,YAAI,QAAQ;AACV,cAAI,CAAC,MAAM;AACT,mBAAO;AACP,0BAAc;AACd,+BAAmB;AACnB,yBAAa;AAAA,UACf;AACA,gBAAM,KAAK,IAAI;AACf,sBAAY,KAAK,aAAa,gBAAgB;AAC9C,yBAAe;AACf,8BAAoB;AACpB,iBAAO;AACP,uBAAa;AAAA,QACf,OAAO;AACL,cAAI,aAAa;AACf,oBAAQ;AACR,0BAAc;AACd,+BAAmB;AAAA,UACrB;AACA,gBAAM,KAAK,IAAI;AACf,sBAAY,KAAK,UAAU;AAC3B,iBAAO;AACP,uBAAa;AAAA,QACf;AAAA,MACF;AACA;AAAA,IACF;AACA,kBAAc;AACd,QAAI,QAAQ;AACV,qBAAe;AACf,0BAAoB;AAAA,IACtB,OAAO;AACL,UAAI,aAAa;AACf,gBAAQ;AACR,sBAAc;AACd,2BAAmB;AAAA,MACrB;AACA,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,CAAC,MAAM,UAAU,CAAC,MAAM;AAC1B,WAAO;AACP,kBAAc;AACd,uBAAmB;AAAA,EACrB;AACA,MAAI,aAAa;AACf,YAAQ;AAAA,EACV;AACA,MAAI,MAAM;AACR,UAAM,KAAK,IAAI;AACf,gBAAY,KAAK,UAAU;AAAA,EAC7B;AACA,MAAI,MAAM,WAAW,GAAG;AACtB,kBAAc;AAAA,EAChB;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC5bO,SAAS,UAAU,KAAK,OAAO;AACpC,MAAI,IAAI,MAAM;AACd,MAAI,IAAI,MAAM;AACd,MAAI,QAAQ,MAAM;AAClB,MAAI,SAAS,MAAM;AACnB,MAAI,IAAI,MAAM;AACd,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,GAAG;AACb,QAAI,IAAI;AACR,YAAQ,CAAC;AAAA,EACX;AACA,MAAI,SAAS,GAAG;AACd,QAAI,IAAI;AACR,aAAS,CAAC;AAAA,EACZ;AACA,MAAI,OAAO,MAAM,UAAU;AACzB,SAAK,KAAK,KAAK,KAAK;AAAA,EACtB,WAAW,aAAa,OAAO;AAC7B,QAAI,EAAE,WAAW,GAAG;AAClB,WAAK,KAAK,KAAK,KAAK,EAAE,CAAC;AAAA,IACzB,WAAW,EAAE,WAAW,GAAG;AACzB,WAAK,KAAK,EAAE,CAAC;AACb,WAAK,KAAK,EAAE,CAAC;AAAA,IACf,WAAW,EAAE,WAAW,GAAG;AACzB,WAAK,EAAE,CAAC;AACR,WAAK,KAAK,EAAE,CAAC;AACb,WAAK,EAAE,CAAC;AAAA,IACV,OAAO;AACL,WAAK,EAAE,CAAC;AACR,WAAK,EAAE,CAAC;AACR,WAAK,EAAE,CAAC;AACR,WAAK,EAAE,CAAC;AAAA,IACV;AAAA,EACF,OAAO;AACL,SAAK,KAAK,KAAK,KAAK;AAAA,EACtB;AACA,MAAI;AACJ,MAAI,KAAK,KAAK,OAAO;AACnB,YAAQ,KAAK;AACb,UAAM,QAAQ;AACd,UAAM,QAAQ;AAAA,EAChB;AACA,MAAI,KAAK,KAAK,OAAO;AACnB,YAAQ,KAAK;AACb,UAAM,QAAQ;AACd,UAAM,QAAQ;AAAA,EAChB;AACA,MAAI,KAAK,KAAK,QAAQ;AACpB,YAAQ,KAAK;AACb,UAAM,SAAS;AACf,UAAM,SAAS;AAAA,EACjB;AACA,MAAI,KAAK,KAAK,QAAQ;AACpB,YAAQ,KAAK;AACb,UAAM,SAAS;AACf,UAAM,SAAS;AAAA,EACjB;AACA,MAAI,OAAO,IAAI,IAAI,CAAC;AACpB,MAAI,OAAO,IAAI,QAAQ,IAAI,CAAC;AAC5B,SAAO,KAAK,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC;AAC/D,MAAI,OAAO,IAAI,OAAO,IAAI,SAAS,EAAE;AACrC,SAAO,KAAK,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,SAAS,IAAI,IAAI,GAAG,KAAK,KAAK,CAAC;AACvE,MAAI,OAAO,IAAI,IAAI,IAAI,MAAM;AAC7B,SAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,EAAE;AACrE,MAAI,OAAO,GAAG,IAAI,EAAE;AACpB,SAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG;AAChE;;;ACrEA,IAAI,QAAQ,KAAK;AACV,SAAS,qBAAqB,aAAa,YAAY,OAAO;AACnE,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AACA,MAAI,KAAK,WAAW;AACpB,MAAI,KAAK,WAAW;AACpB,MAAI,KAAK,WAAW;AACpB,MAAI,KAAK,WAAW;AACpB,cAAY,KAAK;AACjB,cAAY,KAAK;AACjB,cAAY,KAAK;AACjB,cAAY,KAAK;AACjB,MAAI,YAAY,SAAS,MAAM;AAC/B,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,GAAG;AACnC,gBAAY,KAAK,YAAY,KAAK,iBAAiB,IAAI,WAAW,IAAI;AAAA,EACxE;AACA,MAAI,MAAM,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,GAAG;AACnC,gBAAY,KAAK,YAAY,KAAK,iBAAiB,IAAI,WAAW,IAAI;AAAA,EACxE;AACA,SAAO;AACT;AACO,SAAS,qBAAqB,aAAa,YAAY,OAAO;AACnE,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AACA,MAAI,UAAU,WAAW;AACzB,MAAI,UAAU,WAAW;AACzB,MAAI,cAAc,WAAW;AAC7B,MAAI,eAAe,WAAW;AAC9B,cAAY,IAAI;AAChB,cAAY,IAAI;AAChB,cAAY,QAAQ;AACpB,cAAY,SAAS;AACrB,MAAI,YAAY,SAAS,MAAM;AAC/B,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,cAAY,IAAI,iBAAiB,SAAS,WAAW,IAAI;AACzD,cAAY,IAAI,iBAAiB,SAAS,WAAW,IAAI;AACzD,cAAY,QAAQ,KAAK,IAAI,iBAAiB,UAAU,aAAa,WAAW,KAAK,IAAI,YAAY,GAAG,gBAAgB,IAAI,IAAI,CAAC;AACjI,cAAY,SAAS,KAAK,IAAI,iBAAiB,UAAU,cAAc,WAAW,KAAK,IAAI,YAAY,GAAG,iBAAiB,IAAI,IAAI,CAAC;AACpI,SAAO;AACT;AACO,SAAS,iBAAiB,UAAU,WAAW,oBAAoB;AACxE,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,MAAM,WAAW,CAAC;AACxC,UAAQ,kBAAkB,MAAM,SAAS,KAAK,MAAM,IAAI,kBAAkB,KAAK,mBAAmB,qBAAqB,IAAI,OAAO;AACpI;;;ACjDA,IAAI,YAAY,2BAAY;AAC1B,WAASE,aAAY;AACnB,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AACA,SAAOA;AACT,EAAE;AAEF,IAAI,8BAA8B,CAAC;AACnC,IAAI,OAAO,SAAU,QAAQ;AAC3B,YAAUC,OAAM,MAAM;AACtB,WAASA,MAAK,MAAM;AAClB,WAAO,OAAO,KAAK,MAAM,IAAI,KAAK;AAAA,EACpC;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AAC3C,WAAO,IAAI,UAAU;AAAA,EACvB;AACA,EAAAA,MAAK,UAAU,YAAY,SAAU,KAAK,OAAO;AAC/C,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,kBAAkB;AACzB,UAAI,iBAAiB,qBAAqB,6BAA6B,OAAO,KAAK,KAAK;AACxF,UAAI,eAAe;AACnB,UAAI,eAAe;AACnB,cAAQ,eAAe;AACvB,eAAS,eAAe;AACxB,qBAAe,IAAI,MAAM;AACzB,cAAQ;AAAA,IACV,OAAO;AACL,UAAI,MAAM;AACV,UAAI,MAAM;AACV,cAAQ,MAAM;AACd,eAAS,MAAM;AAAA,IACjB;AACA,QAAI,CAAC,MAAM,GAAG;AACZ,UAAI,KAAK,GAAG,GAAG,OAAO,MAAM;AAAA,IAC9B,OAAO;AACL,MAAgB,UAAU,KAAK,KAAK;AAAA,IACtC;AAAA,EACF;AACA,EAAAA,MAAK,UAAU,aAAa,WAAY;AACtC,WAAO,CAAC,KAAK,MAAM,SAAS,CAAC,KAAK,MAAM;AAAA,EAC1C;AACA,SAAOA;AACT,EAAE,YAAI;AACN,KAAK,UAAU,OAAO;AACtB,IAAO,eAAQ;;;AC5Cf,IAAI,0BAA0B;AAAA,EAC5B,MAAM;AACR;AACA,IAAI,4BAA4B;AACzB,IAAI,+BAA+B;AAAA,EACxC,OAAO,SAAS;AAAA,IACd,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB,GAAG,+BAA+B,KAAK;AACzC;AACA,IAAI,SAAS,SAAU,QAAQ;AAC7B,YAAUC,SAAQ,MAAM;AACxB,WAASA,QAAO,MAAM;AACpB,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,OAAO;AACb,UAAM,YAAY,CAAC;AACnB,UAAM,gBAAgB;AACtB,UAAM,KAAK,IAAI;AACf,WAAO;AAAA,EACT;AACA,EAAAA,QAAO,UAAU,cAAc,WAAY;AACzC,WAAO,KAAK;AAAA,EACd;AACA,EAAAA,QAAO,UAAU,SAAS,WAAY;AACpC,WAAO,UAAU,OAAO,KAAK,IAAI;AACjC,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,gBAAgB;AAAA,IACvB;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,UAAI,QAAQ,KAAK,UAAU,CAAC;AAC5B,YAAM,SAAS,KAAK;AACpB,YAAM,IAAI,KAAK;AACf,YAAM,KAAK,KAAK;AAChB,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,KAAK;AACpB,YAAM,YAAY,KAAK;AAAA,IACzB;AAAA,EACF;AACA,EAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC7C,QAAI,qBAAqB,KAAK;AAC9B,QAAI,oBAAoB;AACtB,yBAAmB,gBAAgB;AACnC,UAAI,mBAAmB,WAAW;AAChC,aAAK,YAAY,mBAAmB;AAAA,MACtC;AAAA,IACF,OAAO;AACL,aAAO,UAAU,gBAAgB,KAAK,IAAI;AAAA,IAC5C;AAAA,EACF;AACA,EAAAA,QAAO,UAAU,oBAAoB,SAAU,GAAG;AAChD,QAAI,qBAAqB,KAAK;AAC9B,WAAO,qBAAqB,mBAAmB,kBAAkB,CAAC,IAAI,OAAO,UAAU,kBAAkB,KAAK,MAAM,CAAC;AAAA,EACvH;AACA,EAAAA,QAAO,UAAU,uBAAuB,WAAY;AAClD,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,qBAAqB;AACvC,WAAK,aAAa,gBAAgB,IAAI;AAAA,IACxC;AACA,WAAO,OAAO,UAAU,qBAAqB,KAAK,IAAI;AAAA,EACxD;AACA,EAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC7C,SAAK,eAAe;AACpB,uBAAmB,KAAK,KAAK;AAC7B,SAAK,MAAM,OAAO,KAAK,iBAAiB,IAAI,KAAK,kBAAkB;AACnE,SAAK,UAAU,SAAS,KAAK;AAC7B,SAAK,aAAa;AAAA,EACpB;AACA,EAAAA,QAAO,UAAU,cAAc,SAAU,IAAI;AAC3C,WAAO,UAAU,YAAY,KAAK,MAAM,EAAE;AAC1C,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,WAAK,UAAU,CAAC,EAAE,OAAO;AAAA,IAC3B;AAAA,EACF;AACA,EAAAA,QAAO,UAAU,mBAAmB,SAAU,IAAI;AAChD,WAAO,UAAU,iBAAiB,KAAK,MAAM,EAAE;AAC/C,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,WAAK,UAAU,CAAC,EAAE,OAAO;AAAA,IAC3B;AAAA,EACF;AACA,EAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC7C,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,CAAC,KAAK,OAAO;AACf,UAAIC,WAAU,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACzC,UAAI,WAAW,KAAK;AACpB,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AACX,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,QAAQ,SAAS,CAAC;AACtB,YAAI,YAAY,MAAM,gBAAgB;AACtC,YAAI,YAAY,MAAM,kBAAkB,MAAM;AAC9C,YAAI,WAAW;AACb,UAAAA,SAAQ,KAAK,SAAS;AACtB,UAAAA,SAAQ,eAAe,SAAS;AAChC,iBAAO,QAAQA,SAAQ,MAAM;AAC7B,eAAK,MAAMA,QAAO;AAAA,QACpB,OAAO;AACL,iBAAO,QAAQ,UAAU,MAAM;AAC/B,eAAK,MAAM,SAAS;AAAA,QACtB;AAAA,MACF;AACA,WAAK,QAAQ,QAAQA;AAAA,IACvB;AACA,WAAO,KAAK;AAAA,EACd;AACA,EAAAD,QAAO,UAAU,sBAAsB,SAAU,kBAAkB;AACjE,SAAK,gBAAgB,oBAAoB;AAAA,EAC3C;AACA,EAAAA,QAAO,UAAU,iBAAiB,SAAU,aAAa;AACvD,QAAI,MAAuC;AACzC,YAAM,IAAI,MAAM,mCAAoC;AAAA,IACtD;AAAA,EACF;AACA,EAAAA,QAAO,UAAU,cAAc,SAAU,aAAa,aAAa;AACjE,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AACA,QAAI,aAAa,YAAY;AAC7B,QAAI,aAAa,YAAY,QAAQ,cAAc,CAAC;AACpD,WAAO,aAAa,WAAW;AAC/B,QAAI,cAAc,YAAY;AAC5B,WAAK,WAAW,YAAY,UAAU;AACtC,kBAAY,OAAO;AAAA,IACrB,WAAW,YAAY;AACrB,kBAAY,OAAO;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACA,EAAAA,QAAO,UAAU,aAAa,SAAU,YAAY,YAAY;AAC9D,QAAI,YAAY,KAAK,UAAU;AAC/B,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,WAAW,UAAU,CAAC;AAC1B,iBAAW,QAAQ,IAAI,WAAW,QAAQ,KAAK,CAAC;AAChD,aAAO,WAAW,QAAQ,GAAG,WAAW,QAAQ,CAAC;AAAA,IACnD;AAAA,EACF;AACA,EAAAA,QAAO,UAAU,yBAAyB,WAAY;AACpD,WAAO;AAAA,EACT;AACA,EAAAA,QAAO,UAAU,oBAAoB,SAAU,MAAM;AACnD,QAAI,QAAQ,KAAK,UAAU,KAAK,YAAY;AAC5C,QAAI,CAAC,SAAS,EAAE,iBAAiB,OAAO;AACtC,cAAQ,IAAI,KAAK;AAAA,IACnB;AACA,SAAK,UAAU,KAAK,cAAc,IAAI;AACtC,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AACf,WAAO;AAAA,EACT;AACA,EAAAA,QAAO,UAAU,oBAAoB,WAAY;AAC/C,QAAI,QAAQ,KAAK;AACjB,QAAI,WAAW,MAAM,QAAQ;AAC7B,QAAI,cAAc,MAAM;AACxB,QAAI,OAAO,aAAa,KAAK;AAC7B,QAAI,eAAe,eAAe,MAAM,KAAK;AAC7C,QAAI,aAAa,mBAAmB,KAAK;AACzC,QAAI,eAAe,CAAC,CAAC,MAAM;AAC3B,QAAI,cAAc,aAAa;AAC/B,QAAI,aAAa,aAAa;AAC9B,QAAI,eAAe,aAAa;AAChC,QAAI,YAAY,aAAa;AAC7B,QAAI,aAAa,aAAa;AAC9B,QAAI,eAAe,KAAK;AACxB,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,YAAY,MAAM,SAAS,aAAa,SAAS;AACrD,QAAI,gBAAgB,MAAM,iBAAiB,aAAa,iBAAiB;AACzE,QAAI,QAAQ;AACZ,QAAI,QAAQE,aAAY,OAAO,aAAa,eAAe,aAAa;AACxE,QAAI,cAAc,aAAa;AAC7B,UAAI,OAAO,YAAY,OAAO,YAAY,SAAS;AACnD,UAAI,OAAOA,aAAY,OAAO,aAAa,aAAa;AACxD,oBAAc,KAAK,kBAAkB,OAAO,OAAO,MAAM,MAAM,YAAY,WAAW;AAAA,IACxF;AACA,aAAS,aAAa;AACtB,QAAI,aAAa;AACf,cAAQ,mBAAmB,OAAO,WAAW,WAAW;AACxD,UAAI,kBAAkB,OAAO;AAC3B,iBAAS,YAAY,CAAC;AAAA,MACxB,WAAW,kBAAkB,UAAU;AACrC,iBAAS,YAAY,CAAC;AAAA,MACxB;AAAA,IACF;AACA,QAAI,mBAAmB;AACvB,QAAI,iBAAiB;AACrB,QAAI,WAAW,QAAQ,UAAU,QAAQ,MAAM,QAAQ,iBAAiB,MAAM,aAAa,KAAK;AAChG,QAAI,aAAa,UAAU,YAAY,QAAQ,MAAM,SAAS,CAAC,iBAAiB,CAAC,aAAa,cAAc,mBAAmB,mBAAmB,2BAA2B,aAAa,UAAU,IAAI;AACxM,QAAIC,aAAY,MAAM,iBAAiB;AACvC,QAAI,oBAAoB,MAAM,SAAS,SAAS,MAAM,aAAa,cAAc,MAAM,aAAa,WAAW,MAAM,aAAa;AAClI,QAAI,uBAAuB,aAAa;AACxC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,KAAK,KAAK,kBAAkB,aAAK;AACrC,UAAI,aAAa,GAAG,YAAY;AAChC,SAAG,SAAS,UAAU;AACtB,iBAAW,OAAO,UAAU,CAAC;AAC7B,iBAAW,IAAI;AACf,iBAAW,IAAI;AACf,UAAI,WAAW;AACb,mBAAW,YAAY;AAAA,MACzB;AACA,iBAAW,eAAe;AAC1B,iBAAW,UAAU,MAAM;AAC3B,iBAAW,cAAc;AACzB,UAAIA,YAAW;AACb,mBAAW,aAAa,MAAM,kBAAkB;AAChD,mBAAW,cAAc,MAAM,mBAAmB;AAClD,mBAAW,gBAAgB,MAAM,qBAAqB;AACtD,mBAAW,gBAAgB,MAAM,qBAAqB;AAAA,MACxD;AACA,iBAAW,SAAS;AACpB,iBAAW,OAAO;AAClB,UAAI,YAAY;AACd,mBAAW,YAAY,MAAM,aAAa;AAC1C,mBAAW,WAAW,MAAM;AAC5B,mBAAW,iBAAiB,MAAM,kBAAkB;AAAA,MACtD;AACA,iBAAW,OAAO;AAClB,sBAAgB,YAAY,KAAK;AACjC,eAAS;AACT,UAAI,mBAAmB;AACrB,WAAG,gBAAgB,IAAI,qBAAa,YAAY,WAAW,GAAG,MAAM,OAAO,WAAW,SAAS,GAAGD,aAAY,WAAW,GAAG,sBAAsB,WAAW,YAAY,GAAG,cAAc,oBAAoB,CAAC;AAAA,MACjN;AAAA,IACF;AAAA,EACF;AACA,EAAAF,QAAO,UAAU,mBAAmB,WAAY;AAC9C,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,aAAa,KAAK;AAC7B,QAAI,eAAe,cAAc,MAAM,KAAK;AAC5C,QAAI,eAAe,aAAa;AAChC,QAAI,aAAa,aAAa;AAC9B,QAAI,cAAc,aAAa;AAC/B,QAAI,cAAc,MAAM;AACxB,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,eAAe,KAAK;AACxB,QAAI,YAAY,MAAM,SAAS,aAAa;AAC5C,QAAI,gBAAgB,MAAM,iBAAiB,aAAa;AACxD,QAAI,OAAO,YAAY,OAAO,YAAY,SAAS;AACnD,QAAI,OAAOE,aAAY,OAAO,aAAa,aAAa;AACxD,QAAI,QAAQ;AACZ,QAAI,UAAU;AACd,QAAI,aAAa;AACf,eAAS,YAAY,CAAC;AACtB,iBAAW,YAAY,CAAC;AAAA,IAC1B;AACA,QAAI,SAAS,QAAQ;AACrB,QAAI,mBAAmB,KAAK,GAAG;AAC7B,WAAK,kBAAkB,OAAO,OAAO,MAAM,MAAM,YAAY,WAAW;AAAA,IAC1E;AACA,QAAI,eAAe,CAAC,CAAC,MAAM;AAC3B,aAAS,IAAI,GAAG,IAAI,aAAa,MAAM,QAAQ,KAAK;AAClD,UAAI,OAAO,aAAa,MAAM,CAAC;AAC/B,UAAI,SAAS,KAAK;AAClB,UAAI,aAAa,OAAO;AACxB,UAAI,aAAa,KAAK;AACtB,UAAI,gBAAgB,KAAK;AACzB,UAAI,YAAY;AAChB,UAAI,YAAY;AAChB,UAAI,aAAa;AACjB,UAAI,aAAa,aAAa;AAC9B,UAAI,QAAQ;AACZ,aAAO,YAAY,eAAe,QAAQ,OAAO,SAAS,GAAG,CAAC,MAAM,SAAS,MAAM,UAAU,SAAS;AACpG,aAAK,YAAY,OAAO,OAAO,YAAY,SAAS,WAAW,QAAQ,YAAY;AACnF,yBAAiB,MAAM;AACvB,qBAAa,MAAM;AACnB;AAAA,MACF;AACA,aAAO,cAAc,MAAM,QAAQ,OAAO,UAAU,GAAG,MAAM,UAAU,UAAU;AAC/E,aAAK,YAAY,OAAO,OAAO,YAAY,SAAS,YAAY,SAAS,YAAY;AACrF,yBAAiB,MAAM;AACvB,sBAAc,MAAM;AACpB;AAAA,MACF;AACA,oBAAc,gBAAgB,YAAY,UAAU,SAAS,cAAc,iBAAiB;AAC5F,aAAO,aAAa,YAAY;AAC9B,gBAAQ,OAAO,SAAS;AACxB,aAAK,YAAY,OAAO,OAAO,YAAY,SAAS,YAAY,MAAM,QAAQ,GAAG,UAAU,YAAY;AACvG,qBAAa,MAAM;AACnB;AAAA,MACF;AACA,iBAAW;AAAA,IACb;AAAA,EACF;AACA,EAAAF,QAAO,UAAU,cAAc,SAAU,OAAO,OAAO,YAAY,SAAS,GAAG,WAAW,oBAAoB;AAC5G,QAAI,aAAa,MAAM,KAAK,MAAM,SAAS,KAAK,CAAC;AACjD,eAAW,OAAO,MAAM;AACxB,QAAI,gBAAgB,MAAM;AAC1B,QAAI,IAAI,UAAU,aAAa;AAC/B,QAAI,kBAAkB,OAAO;AAC3B,UAAI,UAAU,MAAM,SAAS;AAAA,IAC/B,WAAW,kBAAkB,UAAU;AACrC,UAAI,UAAU,aAAa,MAAM,SAAS;AAAA,IAC5C;AACA,QAAI,aAAa,CAAC,MAAM,gBAAgB,mBAAmB,UAAU;AACrE,kBAAc,KAAK,kBAAkB,YAAY,OAAO,cAAc,UAAU,IAAI,MAAM,QAAQ,cAAc,WAAW,IAAI,MAAM,QAAQ,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,MAAM,OAAO,MAAM,MAAM;AACnM,QAAI,eAAe,CAAC,CAAC,WAAW;AAChC,QAAI,cAAc,MAAM;AACxB,QAAI,aAAa;AACf,UAAI,mBAAmB,GAAG,WAAW,WAAW;AAChD,WAAK,MAAM,SAAS,IAAI,YAAY,CAAC,IAAI,MAAM,cAAc;AAAA,IAC/D;AACA,QAAI,KAAK,KAAK,kBAAkB,aAAK;AACrC,QAAI,aAAa,GAAG,YAAY;AAChC,OAAG,SAAS,UAAU;AACtB,QAAI,eAAe,KAAK;AACxB,QAAI,iBAAiB;AACrB,QAAI,mBAAmB;AACvB,QAAI,WAAW,QAAQ,UAAU,aAAa,WAAW,OAAO,UAAU,QAAQ,MAAM,QAAQ,iBAAiB,MAAM,aAAa,KAAK;AACzI,QAAI,aAAa,UAAU,YAAY,aAAa,WAAW,SAAS,YAAY,QAAQ,MAAM,SAAS,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,aAAa,cAAc,mBAAmB,mBAAmB,2BAA2B,aAAa,UAAU,IAAI;AAC5Q,QAAIG,aAAY,WAAW,iBAAiB,KAAK,MAAM,iBAAiB;AACxE,eAAW,OAAO,MAAM;AACxB,eAAW,IAAI;AACf,eAAW,IAAI;AACf,QAAIA,YAAW;AACb,iBAAW,aAAa,WAAW,kBAAkB,MAAM,kBAAkB;AAC7E,iBAAW,cAAc,WAAW,mBAAmB,MAAM,mBAAmB;AAChF,iBAAW,gBAAgB,WAAW,qBAAqB,MAAM,qBAAqB;AACtF,iBAAW,gBAAgB,WAAW,qBAAqB,MAAM,qBAAqB;AAAA,IACxF;AACA,eAAW,YAAY;AACvB,eAAW,eAAe;AAC1B,eAAW,OAAO,MAAM,QAAQ;AAChC,eAAW,UAAU,UAAU,WAAW,SAAS,MAAM,SAAS,CAAC;AACnE,oBAAgB,YAAY,UAAU;AACtC,QAAI,YAAY;AACd,iBAAW,YAAY,UAAU,WAAW,WAAW,MAAM,WAAW,gBAAgB;AACxF,iBAAW,WAAW,UAAU,WAAW,UAAU,MAAM,QAAQ;AACnE,iBAAW,iBAAiB,MAAM,kBAAkB;AACpD,iBAAW,SAAS;AAAA,IACtB;AACA,QAAI,UAAU;AACZ,iBAAW,OAAO;AAAA,IACpB;AACA,QAAI,YAAY,MAAM;AACtB,QAAI,aAAa,MAAM;AACvB,OAAG,gBAAgB,IAAI,qBAAa,YAAY,WAAW,GAAG,WAAW,WAAW,SAAS,GAAGD,aAAY,WAAW,GAAG,YAAY,WAAW,YAAY,GAAG,WAAW,UAAU,CAAC;AAAA,EACxL;AACA,EAAAF,QAAO,UAAU,oBAAoB,SAAU,OAAO,UAAU,GAAG,GAAG,OAAO,QAAQ;AACnF,QAAI,sBAAsB,MAAM;AAChC,QAAI,kBAAkB,MAAM;AAC5B,QAAI,kBAAkB,MAAM;AAC5B,QAAI,YAAY,uBAAuB,oBAAoB;AAC3D,QAAI,sBAAsB,uBAAuB,CAAC;AAClD,QAAI,mBAAmB,MAAM;AAC7B,QAAII,QAAO;AACX,QAAI;AACJ,QAAI;AACJ,QAAI,uBAAuB,MAAM,cAAc,mBAAmB,iBAAiB;AACjF,eAAS,KAAK,kBAAkB,YAAI;AACpC,aAAO,SAAS,OAAO,YAAY,CAAC;AACpC,aAAO,MAAM,OAAO;AACpB,UAAI,YAAY,OAAO;AACvB,gBAAU,IAAI;AACd,gBAAU,IAAI;AACd,gBAAU,QAAQ;AAClB,gBAAU,SAAS;AACnB,gBAAU,IAAI;AACd,aAAO,WAAW;AAAA,IACpB;AACA,QAAI,qBAAqB;AACvB,UAAI,YAAY,OAAO;AACvB,gBAAU,OAAO,uBAAuB;AACxC,gBAAU,cAAc,UAAU,MAAM,aAAa,CAAC;AAAA,IACxD,WAAW,WAAW;AACpB,cAAQ,KAAK,kBAAkB,aAAO;AACtC,YAAM,SAAS,WAAY;AACzB,QAAAA,MAAK,WAAW;AAAA,MAClB;AACA,UAAI,WAAW,MAAM;AACrB,eAAS,QAAQ,oBAAoB;AACrC,eAAS,IAAI;AACb,eAAS,IAAI;AACb,eAAS,QAAQ;AACjB,eAAS,SAAS;AAAA,IACpB;AACA,QAAI,mBAAmB,iBAAiB;AACtC,UAAI,YAAY,OAAO;AACvB,gBAAU,YAAY;AACtB,gBAAU,SAAS;AACnB,gBAAU,gBAAgB,UAAU,MAAM,eAAe,CAAC;AAC1D,gBAAU,WAAW,MAAM;AAC3B,gBAAU,iBAAiB,MAAM,oBAAoB;AACrD,aAAO,yBAAyB;AAChC,UAAI,OAAO,QAAQ,KAAK,OAAO,UAAU,GAAG;AAC1C,kBAAU,cAAc;AACxB,kBAAU,aAAa;AAAA,MACzB;AAAA,IACF;AACA,QAAI,eAAe,UAAU,OAAO;AACpC,gBAAY,aAAa,MAAM,cAAc;AAC7C,gBAAY,cAAc,MAAM,eAAe;AAC/C,gBAAY,gBAAgB,MAAM,iBAAiB;AACnD,gBAAY,gBAAgB,MAAM,iBAAiB;AACnD,gBAAY,UAAU,UAAU,MAAM,SAAS,SAAS,SAAS,CAAC;AAAA,EACpE;AACA,EAAAJ,QAAO,WAAW,SAAU,OAAO;AACjC,QAAI,OAAO;AACX,QAAI,gBAAgB,KAAK,GAAG;AAC1B,aAAO,CAAC,MAAM,WAAW,MAAM,YAAY,cAAc,MAAM,QAAQ,GAAG,MAAM,cAAc,YAAY,EAAE,KAAK,GAAG;AAAA,IACtH;AACA,WAAO,QAAQ,KAAK,IAAI,KAAK,MAAM,YAAY,MAAM;AAAA,EACvD;AACA,SAAOA;AACT,EAAE,mBAAW;AACb,IAAI,mBAAmB;AAAA,EACrB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAI,4BAA4B;AAAA,EAC9B,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,QAAQ;AACV;AACA,IAAI,aAAa,CAAC,aAAa,cAAc,YAAY,YAAY;AAC9D,SAAS,cAAc,UAAU;AACtC,MAAI,OAAO,aAAa,aAAa,SAAS,QAAQ,IAAI,MAAM,MAAM,SAAS,QAAQ,KAAK,MAAM,MAAM,SAAS,QAAQ,IAAI,MAAM,KAAK;AACtI,WAAO;AAAA,EACT,WAAW,CAAC,MAAM,CAAC,QAAQ,GAAG;AAC5B,WAAO,WAAW;AAAA,EACpB,OAAO;AACL,WAAO,oBAAoB;AAAA,EAC7B;AACF;AACA,SAAS,gBAAgB,aAAa,aAAa;AACjD,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,QAAI,WAAW,WAAW,CAAC;AAC3B,QAAI,MAAM,YAAY,QAAQ;AAC9B,QAAI,OAAO,MAAM;AACf,kBAAY,QAAQ,IAAI;AAAA,IAC1B;AAAA,EACF;AACF;AACO,SAAS,gBAAgB,OAAO;AACrC,SAAO,MAAM,YAAY,QAAQ,MAAM,cAAc,MAAM;AAC7D;AACO,SAAS,mBAAmB,OAAO;AACxC,iBAAe,KAAK;AACpB,OAAK,MAAM,MAAM,cAAc;AAC/B,SAAO;AACT;AACA,SAAS,eAAe,OAAO;AAC7B,MAAI,OAAO;AACT,UAAM,OAAO,OAAO,SAAS,KAAK;AAClC,QAAI,YAAY,MAAM;AACtB,kBAAc,aAAa,YAAY;AACvC,UAAM,QAAQ,aAAa,QAAQ,iBAAiB,SAAS,IAAI,YAAY;AAC7E,QAAI,gBAAgB,MAAM;AAC1B,sBAAkB,aAAa,gBAAgB;AAC/C,UAAM,gBAAgB,iBAAiB,QAAQ,0BAA0B,aAAa,IAAI,gBAAgB;AAC1G,QAAI,cAAc,MAAM;AACxB,QAAI,aAAa;AACf,YAAM,UAAU,kBAAkB,MAAM,OAAO;AAAA,IACjD;AAAA,EACF;AACF;AACA,SAAS,UAAU,QAAQ,WAAW;AACpC,SAAO,UAAU,QAAQ,aAAa,KAAK,WAAW,iBAAiB,WAAW,SAAS,OAAO,OAAO,SAAS,OAAO,aAAa,SAAS;AACjJ;AACA,SAAS,QAAQ,MAAM;AACrB,SAAO,QAAQ,QAAQ,SAAS,SAAS,OAAO,KAAK,SAAS,KAAK,aAAa,SAAS;AAC3F;AACA,SAAS,mBAAmB,GAAG,WAAW,aAAa;AACrD,SAAO,cAAc,UAAU,IAAI,YAAY,CAAC,IAAI,cAAc,WAAW,IAAI,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,IAAI,IAAI,YAAY,CAAC;AAC9I;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,OAAO,MAAM;AACjB,UAAQ,SAAS,QAAQ;AACzB,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO;AACjC,SAAO,CAAC,EAAE,MAAM,mBAAmB,MAAM,cAAc,MAAM,eAAe,MAAM;AACpF;AACA,IAAO,eAAQ;;;AClff,IAAI,eAAe,SAAU,QAAQ;AACnC,YAAUK,eAAc,MAAM;AAC9B,WAASA,gBAAe;AACtB,QAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,UAAM,OAAO;AACb,WAAO;AAAA,EACT;AACA,EAAAA,cAAa,UAAU,mBAAmB,WAAY;AACpD,QAAI,QAAQ,KAAK,MAAM;AACvB,QAAI,YAAY,KAAK,aAAa;AAClC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,kBAAY,aAAa,MAAM,CAAC,EAAE,aAAa;AAAA,IACjD;AACA,QAAI,WAAW;AACb,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACA,EAAAA,cAAa,UAAU,cAAc,WAAY;AAC/C,SAAK,iBAAiB;AACtB,QAAI,QAAQ,KAAK,MAAM,SAAS,CAAC;AACjC,QAAIC,SAAQ,KAAK,eAAe;AAChC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,CAAC,MAAM,CAAC,EAAE,MAAM;AAClB,cAAM,CAAC,EAAE,gBAAgB;AAAA,MAC3B;AACA,YAAM,CAAC,EAAE,KAAK,SAASA,OAAM,CAAC,GAAGA,OAAM,CAAC,GAAG,MAAM,CAAC,EAAE,sBAAsB;AAAA,IAC5E;AAAA,EACF;AACA,EAAAD,cAAa,UAAU,YAAY,SAAU,KAAK,OAAO;AACvD,QAAI,QAAQ,MAAM,SAAS,CAAC;AAC5B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,CAAC,EAAE,UAAU,KAAK,MAAM,CAAC,EAAE,OAAO,IAAI;AAAA,IAC9C;AAAA,EACF;AACA,EAAAA,cAAa,UAAU,aAAa,WAAY;AAC9C,QAAI,QAAQ,KAAK,MAAM,SAAS,CAAC;AACjC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,CAAC,EAAE,YAAY;AAAA,IACvB;AAAA,EACF;AACA,EAAAA,cAAa,UAAU,kBAAkB,WAAY;AACnD,SAAK,iBAAiB,KAAK,IAAI;AAC/B,WAAO,aAAK,UAAU,gBAAgB,KAAK,IAAI;AAAA,EACjD;AACA,SAAOA;AACT,EAAE,YAAI;AACN,IAAO,uBAAQ;;;AChDf,IAAI,MAAM,KAAK,IAAI,CAAC;AACpB,SAAS,YAAY,MAAM,MAAM,UAAU,SAAS,SAAS,UAAU;AACrE,MAAI,WAAW,UAAU,MAAM;AAC/B,MAAI,WAAW,KAAK;AACpB,MAAI,SAAS,eAAe,QAAQ,GAAG;AACrC,WAAO,SAAS,QAAQ;AAAA,EAC1B;AACA,MAAI,SAAS,GAAG;AACd,QAAI,WAAW,KAAK,MAAM,KAAK,KAAK,KAAK,YAAY,IAAI,CAAC,OAAO,IAAI,GAAG;AACxE,WAAO,KAAK,QAAQ,EAAE,QAAQ;AAAA,EAChC;AACA,MAAI,aAAa,UAAU,KAAK;AAChC,MAAI,cAAc,WAAW;AAC7B,SAAO,UAAU,KAAK,aAAa;AACjC;AAAA,EACF;AACA,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,cAAc,GAAG,IAAI,UAAU,KAAK;AAClD,QAAI,SAAS,KAAK;AAClB,QAAI,EAAE,SAAS,UAAU;AACvB,cAAQ,cAAc,IAAI,KAAK,KAAK,KAAK,QAAQ,EAAE,CAAC,IAAI,YAAY,MAAM,OAAO,GAAG,aAAa,YAAY,UAAU,QAAQ,QAAQ;AACvI;AAAA,IACF;AAAA,EACF;AACA,WAAS,QAAQ,IAAI;AACrB,SAAO;AACT;AACO,SAAS,iBAAiB,KAAK,MAAM;AAC1C,MAAI,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;AACxiB,MAAI,WAAW,CAAC;AAChB,MAAI,MAAM,YAAY,IAAI,GAAG,GAAG,GAAG,GAAG,QAAQ;AAC9C,MAAI,QAAQ,GAAG;AACb;AAAA,EACF;AACA,MAAI,KAAK,CAAC;AACV,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,SAAG,CAAC,KAAK,SAAS,GAAG,CAAC,IAAI;AAC1B,SAAG,CAAC,OAAO,IAAI,KAAK,IAAI,KAAK,KAAK,YAAY,IAAI,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,QAAQ,IAAI,MAAM,KAAK,CAAC;AAAA,IAChH;AAAA,EACF;AACA,SAAO,SAAU,KAAK,WAAW,WAAW;AAC1C,QAAI,KAAK,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,IAAI;AACjD,QAAI,CAAC,KAAK,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAC3D,QAAI,CAAC,KAAK,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAAA,EAC7D;AACF;;;AC5CA,IAAI,mBAAmB;AACvB,IAAI,WAAW,CAAC;AACT,SAAS,oBAAoB,KAAK,QAAQ,UAAU,KAAK,KAAK;AACnE,SAAO,2BAA2B,UAAU,QAAQ,KAAK,KAAK,IAAI,KAAK,2BAA2B,KAAK,UAAU,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAC3I;AACO,SAAS,2BAA2B,KAAK,IAAI,KAAK,KAAK,SAAS;AACrE,MAAI,GAAG,yBAAyB,YAAI,gBAAgB,CAAC,WAAW,EAAE,GAAG;AACnE,QAAI,QAAQ,GAAG,gBAAgB,MAAM,GAAG,gBAAgB,IAAI,CAAC;AAC7D,QAAI,UAAU,oBAAoB,IAAI,KAAK;AAC3C,QAAI,cAAc,0BAA0B,SAAS,OAAO,OAAO;AACnE,QAAI,aAAa;AACf,kBAAY,KAAK,KAAK,GAAG;AACzB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,IAAI,OAAO;AACtC,MAAI,UAAU,MAAM;AACpB,MAAI,SAAS;AACX,WAAO;AAAA,EACT;AACA,YAAU,MAAM,UAAU,CAAC;AAC3B,MAAI,SAAS,CAAC,QAAQ,OAAO;AAC7B,MAAI,SAAS,CAAC,OAAO,QAAQ;AAC7B,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,SAAS,SAAS,cAAc,KAAK;AACzC,QAAI,MAAM,OAAO;AACjB,QAAI,QAAQ,IAAI;AAChB,QAAI,SAAS,KAAK,KAAK;AACvB,QAAI,UAAU,CAAC,sBAAsB,sBAAsB,cAAc,aAAa,mBAAmB,qBAAqB,WAAW,YAAY,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,IAAI,MAAM,OAAO,IAAI,KAAK,IAAI,SAAS,OAAO,IAAI,KAAK,IAAI,SAAS,EAAE,EAAE,KAAK,aAAa;AACjR,OAAG,YAAY,MAAM;AACrB,YAAQ,KAAK,MAAM;AAAA,EACrB;AACA,SAAO;AACT;AACA,SAAS,0BAA0B,SAAS,OAAO,SAAS;AAC1D,MAAI,kBAAkB,UAAU,aAAa;AAC7C,MAAI,cAAc,MAAM,eAAe;AACvC,MAAI,eAAe,MAAM;AACzB,MAAI,YAAY,CAAC;AACjB,MAAI,aAAa,CAAC;AAClB,MAAI,kBAAkB;AACtB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,OAAO,QAAQ,CAAC,EAAE,sBAAsB;AAC5C,QAAI,KAAK,IAAI;AACb,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,cAAU,KAAK,GAAG,CAAC;AACnB,sBAAkB,mBAAmB,gBAAgB,MAAM,aAAa,EAAE,KAAK,MAAM,aAAa,KAAK,CAAC;AACxG,eAAW,KAAK,QAAQ,CAAC,EAAE,YAAY,QAAQ,CAAC,EAAE,SAAS;AAAA,EAC7D;AACA,SAAO,mBAAmB,cAAc,eAAe,MAAM,YAAY,WAAW,MAAM,eAAe,IAAI,UAAU,iBAAiB,YAAY,SAAS,IAAI,iBAAiB,WAAW,UAAU;AACzM;AACO,SAAS,WAAW,IAAI;AAC7B,SAAO,GAAG,SAAS,YAAY,MAAM;AACvC;AACA,IAAI,aAAa;AACjB,IAAI,aAAa;AAAA,EACf,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAM;AACR;AACO,SAAS,WAAW,QAAQ;AACjC,SAAO,UAAU,OAAO,MAAM,SAAS,IAAI,QAAQ,YAAY,SAAU,KAAK,GAAG;AAC/E,WAAO,WAAW,CAAC;AAAA,EACrB,CAAC;AACH;;;ACtEA,IAAI;AACJ,wBAAwB,YAAI,oBAAoB,OAAO,yBAAyB,OAAO,sBAAsB,KAAK,MAAM,KAAK,OAAO,2BAA2B,OAAO,wBAAwB,KAAK,MAAM,KAAK,OAAO,4BAA4B,OAAO,gCAAgC,SAAU,MAAM;AACtS,SAAO,WAAW,MAAM,EAAE;AAC5B;AACA,IAAO,gCAAQ;", "names": ["map", "len", "len", "MapPolyfill", "HashMap", "keys", "Browser", "Env", "env", "clone", "clone", "copy", "create", "mul", "scale", "lerp", "Entry", "LinkedList", "LRU", "map", "len", "lerp", "d", "b", "Transformable", "EPSILON", "create", "isNotAroundZero", "roots", "extrema", "Clip", "EPSILON", "isAroundZero", "scale", "len", "len2", "Track", "isGradient", "mathMin", "start", "Animator", "self", "i", "Point", "len", "BoundingRect", "Eventful", "len", "adjustTextY", "len", "distance", "Element", "len", "self", "target", "animator", "PRIMARY_STATES_KEYS", "Displayable", "mathMin", "mathMax", "create", "min", "max", "cubicExtrema", "cubicAt", "quadraticExtremum", "quadraticAt", "min", "max", "min2", "max2", "mathMin", "mathMax", "mathCos", "mathSin", "PI2", "PathProxy", "dpr", "len", "min", "containStroke", "containStroke", "PI2", "PI2", "containStroke", "CMD", "PI2", "EPSILON", "len", "containStroke", "Path", "containStroke", "_super", "Sub", "ZRImage", "TSpan", "scale", "dpr", "len", "len", "RichTextToken", "RichTextLine", "RichTextContentBlock", "line", "lineWidth", "lineHeight", "RectShape", "Rect", "ZRText", "tmpRect", "adjustTextY", "<PERSON><PERSON><PERSON><PERSON>", "self", "CompoundPath", "scale"]}