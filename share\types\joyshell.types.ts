import { ICenterSetting, NetworkConfig } from "./settings.types";
import Electron from "electron";

export interface JoyShell {
  readonly AppVersion: string;
  readonly Settings: ICenterSetting;
  GetSettings(): ICenterSetting;
  WhenReady(): Promise<void>;
  Send(channel: string, ...args: any[]): void;
  SetUserConfig(cfgOrKey: Record<string, any> | string, value?: any): void;
  GetUserConfig<T>(name: string, defval?: T): T;
  GetNetworkConfig(): NetworkConfig;
  SetNetworkConfig(info: NetworkConfig): void;
  GetServerURL(): string;
  GetAddon(name: string): any;
  ShowSaveDialog(options: Electron.SaveDialogOptions): Promise<Electron.SaveDialogReturnValue>;
  ShowOpenDialog(optioins: Electron.OpenDialogOptions): Promise<Electron.OpenDialogReturnValue>;
  OpenUrl(url: string): void;
}

export interface ICenterServer {
  start(): Promise<void>;
  stop(): Promise<void>;
  on(event: string, listener: (...args: any[]) => void): this;
}
