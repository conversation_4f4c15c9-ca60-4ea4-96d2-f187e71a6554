import { Injectable } from '@angular/core';
import { NzMessageService } from 'ng-zorro-antd/message';

type IMsgType = 'success' | 'error' | 'info' | 'warning' | 'loading';
@Injectable({
  providedIn: 'root',
})
export class MessageService {
  constructor(private nzMessageService: NzMessageService) {}

  create(type: IMsgType, content: string) {
    this.nzMessageService.create(type, content);
  }

  loading(content: string) {
    const ref = this.nzMessageService.loading(content);
    return {
      ref,
      close: () => this.nzMessageService.remove(ref.messageId),
    };
  }
}
