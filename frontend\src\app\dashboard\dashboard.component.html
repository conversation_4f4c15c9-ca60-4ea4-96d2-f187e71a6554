<nav>
    <div class="nav-bar">
        <img class="logo" src="assets/images/joytest_logo.png" alt="">
        <div>
            <span class="time">{{time | customDate: 'date'}}</span>
            <div class="setting">
                <span nz-icon nzType="setting" nzTheme="outline" nz-dropdown [nzDropdownMenu]="menu" nzTrigger="click"></span>
            </div>
            <nz-dropdown-menu #menu="nzDropdownMenu">
                <ul nz-menu>
                  <li nz-menu-item (click)="openSetting()">系统设置</li>
                  <li nz-menu-item (click)="goToOperationLog()">操作日志</li>
                  <li nz-menu-item nzDanger (click)="logout()">注销</li>
                </ul>
              </nz-dropdown-menu>
        </div>
    </div>
    <div class="tabs">
        <div class="back-btn" *ngIf="showBackBtn">
            <a nz-button nzType="link" (click)="onBackClick()">
                <span nz-icon nzType="left-square" nzTheme="fill"></span>
                返回
              </a>
        </div>
        <nz-tabset nzCentered nzSize="large" [nzTabBarGutter]="50" [(nzSelectedIndex)]="current_tab_index">
            <nz-tab nzTitle="首页" (nzClick)="onTabClick('home')"></nz-tab>
            <nz-tab nzTitle="考场管理" (nzClick)="onTabClick('room')"></nz-tab>
            <nz-tab nzTitle="考试管理" (nzClick)="onTabClick('schedule')"></nz-tab>
          </nz-tabset>
    </div>
</nav>
<main>
    <div class="detail">
        <router-outlet></router-outlet>
    </div>
</main>
<div class="version-info" *ngIf="appVersion && current_tab_index === 0">V {{ appVersion }}</div>