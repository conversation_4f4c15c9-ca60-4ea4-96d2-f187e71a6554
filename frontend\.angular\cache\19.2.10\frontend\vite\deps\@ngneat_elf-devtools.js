import {
  capitalize,
  getRegistry,
  getStore,
  getStoresSnapshot,
  registry$
} from "./chunk-XOFDNCKA.js";
import "./chunk-VCKIMKJI.js";
import "./chunk-7OW3M5NO.js";
import {
  Subject,
  skip
} from "./chunk-XEUTWJEE.js";
import "./chunk-XWLXMCJQ.js";

// node_modules/.pnpm/@ngneat+elf-devtools@1.3.0/node_modules/@ngneat/elf-devtools/index.js
var externalEvents$ = new Subject();
function send(action) {
  externalEvents$.next(action);
}
function devTools(options = {}) {
  if (!window.__REDUX_DEVTOOLS_EXTENSION__) return;
  let lock = false;
  const instance = window.__REDUX_DEVTOOLS_EXTENSION__.connect(options);
  const subscriptions = /* @__PURE__ */ new Map();
  const send2 = (action) => {
    instance.send(action, getStoresSnapshot());
  };
  subscriptions.set("externalSend", externalEvents$.subscribe(send2));
  const addStore = (store) => {
    const name = store.name;
    const displayName = capitalize(name);
    send2({
      type: `[${displayName}] - @Init`
    });
    const update = store.pipe(skip(1)).subscribe(() => {
      if (lock) {
        lock = false;
        return;
      }
      options.preAction?.();
      send2({
        type: `[${displayName}] - Update`
      });
    });
    subscriptions.set(name, update);
  };
  getRegistry().forEach(addStore);
  if (options.actionsDispatcher) {
    subscriptions.set("actionsDispatcher", options.actionsDispatcher.subscribe((action) => {
      send2(action);
    }));
  }
  const subscription = registry$.subscribe(({
    store,
    type
  }) => {
    const name = store.name;
    const displayName = capitalize(name);
    if (options.logTrace) {
      const msg = `[${displayName}] - ${type}`;
      console.groupCollapsed(msg);
      console.trace();
      console.groupEnd();
    }
    if (type === "add") {
      addStore(store);
    }
    if (type === "remove") {
      subscriptions.get(name)?.unsubscribe();
      subscriptions.delete(name);
      send2({
        type: `Remove ${displayName}`
      });
    }
  });
  const devtoolsDispose = instance.subscribe((message) => {
    if (message.type === "DISPATCH") {
      const payloadType = message.payload.type;
      if (payloadType === "COMMIT") {
        instance.init(getStoresSnapshot());
        return;
      }
      if (payloadType === "JUMP_TO_STATE" || payloadType === "JUMP_TO_ACTION") {
        const state = JSON.parse(message.state);
        for (const [name, value] of Object.entries(state)) {
          lock = true;
          getStore(name)?.update(() => value);
        }
        options.postTimelineUpdate?.();
      }
    }
  });
  return {
    unsubscribe() {
      subscription.unsubscribe();
      instance.unsubscribe();
      subscriptions.forEach((sub) => sub.unsubscribe());
      devtoolsDispose();
    }
  };
}
export {
  devTools,
  send
};
//# sourceMappingURL=@ngneat_elf-devtools.js.map
