import * as crypto from 'crypto';

const algorithm = 'aes-256-cfb';

export function encryptText(text: string, password: string) {
  const hash = crypto.createHash('sha256');
  hash.update(password);
  const key = hash.digest();
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  const enc = [iv, cipher.update(text, 'utf8')];
  enc.push(cipher.final());
  return Buffer.concat(enc).toString('base64');
}

export function decryptText(text: string, password: string) {
  const hash = crypto.createHash('sha256');
  hash.update(password);
  const key = hash.digest();

  const contents = Buffer.from(text, 'base64');
  const iv = contents.slice(0, 16);
  const edata = contents.slice(16);

  const decipher = crypto.createDecipheriv(algorithm, key, iv);
  let res = decipher.update(edata, undefined, 'utf8');
  res += decipher.final('utf8');

  return res;
}
