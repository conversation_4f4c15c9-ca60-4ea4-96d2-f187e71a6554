import { Injectable } from '@angular/core';
import { createStore } from '@ngneat/elf';
import {
  selectAllEntities,
  setEntities,
  withEntities,
} from '@ngneat/elf-entities';
import { Subscription, switchMap, tap, timer } from 'rxjs';
import { RoomHttpService } from '../../http/room.service';

export interface IRoomData {
  name: string;
  sn_code: string;
  host: string;
  address: string;
  app_version: string;
  is_online: boolean;
}

const roomStore = createStore(
  { name: 'rooms' },
  withEntities<IRoomData, 'sn_code'>({ idKey: 'sn_code' })
);

@Injectable({ providedIn: 'root' })
export class RoomDataService {
  rooms$ = roomStore.pipe(selectAllEntities());

  roomRefreshSub: Subscription;
  constructor(private roomHttp: RoomHttpService) {}

  reqRooms() {
    return this.roomHttp.getAllRooms().pipe(
      tap((res) => {
        if (res.status === 'success') {
          roomStore.update(setEntities(res.data));
        }
      })
    );
  }
  reqRoomsInterval() {
    if (this.roomRefreshSub) {
      return;
    }
    this.roomRefreshSub = timer(0, 1000 * 5)
      .pipe(switchMap(() => this.reqRooms()))
      .subscribe();
  }
}
