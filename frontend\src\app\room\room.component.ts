import { CommonModule } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { RoomHttpService } from '../core/http/room.service';
import { IHttpProject } from '../core/http/exam.service';
import { ModalService } from '../core/services/modal.service';
import { CustomDatePipe } from '../shared/pipes/custom-date.pipe';
import { URLService } from '../core/http/url.service';
import { ProjectDataService, RoomDataService } from '../core/data';

interface IRoom {
  sn_code: string;
  host: string;
  address: string;
  app_version: string;
  is_online: boolean;
  checked: boolean;
}
@Component({
    selector: 'app-room',
    imports: [CommonModule, NzTableModule, NzButtonModule, CustomDatePipe],
    templateUrl: './room.component.html',
    styleUrls: ['./room.component.scss']
})
export class RoomComponent implements OnInit {
  server_ip = '';
  rooms: IRoom[] = [];
  project: IHttpProject | null = null;
  online_num = 0;
  allChecked = false;
  indeterminate = false;
  checked_num = 0;

  constructor(
    private roomHttp: RoomHttpService,
    private modalService: ModalService,
    private urlService: URLService,
    private projectData: ProjectDataService,
    private roomData: RoomDataService
  ) {}

  ngOnInit(): void {
    this.roomData.rooms$.subscribe((rooms) => {
      const tempRooms = []
      for (const r of rooms) {
        const cr = this.rooms.find(room => room.sn_code === r.sn_code);
        tempRooms.push({...r, checked: cr ? cr.checked : false})
      }
      this.rooms = tempRooms;
      this.online_num = rooms.filter((r) => r.is_online).length;
      this.refreshCheckedStatus();
    });
    this.roomData.reqRooms().subscribe();
    this.projectData.formalProject$.subscribe(
      (project) => (this.project = project)
    );
    const server_url = this.urlService.getExternalServerUrl();
    const matchList = server_url.match(/^(http|https):\/\/([\d\.]+):(\d+)$/);
    if (!matchList) {
      this.server_ip = server_url
      return;
    }
    const ip = matchList[2];
    const ssl_port = this.urlService.config?.SERVER_SSL_PORT
    const server_port =  ssl_port === 19101 ? '': ":" + ssl_port
    this.server_ip = ip + server_port;
  }

  refreshCheckedStatus(): void {
    this.checked_num = this.rooms.filter((value) => value.checked).length;
    const allChecked = this.rooms.every((value) => value.checked === true);
    const allUnChecked = this.rooms.every((value) => !value.checked);
    this.allChecked = allChecked;
    this.indeterminate = !allChecked && !allUnChecked;
  }
  onAllChecked(e: boolean) {
    this.rooms.forEach((value) => (value.checked = e));
    this.refreshCheckedStatus();
  }
  onItemChecked(e: boolean, item: IRoom) {
    item.checked = e;
    this.refreshCheckedStatus();
  }
  remove(sn_code?: string) {
    const room_codes = sn_code
      ? [sn_code]
      : this.rooms
          .filter((value) => value.checked)
          .map((value) => value.sn_code);
    if (room_codes.length === 0) {
      console.warn('no room selected');
      return;
    }
    this.modalService.confirm(
      sn_code ? '是否确认移除该考场？' : '是否确认移除已选的考场？',
      () => {
        this.roomHttp.removeRoom(room_codes).subscribe((res) => {
          if (res.status === "success") {            
            this.rooms = this.rooms.filter(
              (value) => !room_codes.includes(value.sn_code)
            );
            this.refreshCheckedStatus();
          } else {
            this.modalService.error("提示", "移除考场失败！");
          }
        });
      }
    );
  }
}
