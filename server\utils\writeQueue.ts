class WriteQueue {
  queue: {
    operation: () => any;
    resolve: (value?: any) => void;
    reject: (reason?: any) => void;
  }[];
  isProcessing: boolean;

  constructor() {
    this.queue = [];
    this.isProcessing = false;
  }

  /**
   * 添加写操作到队列
   * @param {Function} operation 返回Promise的写操作函数
   * @returns {Promise} 返回操作结果的Promise
   */
  enqueue(operation: () => any) {
    return new Promise((resolve, reject) => {
      // 将操作包装起来，包括resolve和reject
      this.queue.push({
        operation,
        resolve,
        reject,
      });

      // 如果当前没有在处理队列，则开始处理
      if (!this.isProcessing) {
        this.processQueue();
      }
    });
  }

  /**
   * 处理队列中的操作
   */
  async processQueue() {
    if (this.queue.length === 0) {
      this.isProcessing = false;
      return;
    }

    this.isProcessing = true;
    const task = this.queue.shift();

    if (task) {
      try {
        // 执行操作并等待结果
        const result = await task.operation();
        task.resolve(result);
      } catch (error) {
        task.reject(error);
      } finally {
        // 处理下一个任务
        // In Node.js environment, setImmediate is a good choice for this.
        // In browser environment, you might use Promise.resolve().then() or setTimeout(..., 0)
        if (typeof setImmediate === 'function') {
          setImmediate(() => this.processQueue());
        } else {
          Promise.resolve().then(() => this.processQueue());
        }
      }
    }
  }
}

class EnhancedWriteQueue extends WriteQueue {
  drainCallbacks: ((value?: any) => void)[];
  constructor() {
    super();
    this.drainCallbacks = [];
  }

  /**
   * 等待所有排队的操作完成
   * @returns {Promise}
   */
  drain() {
    if (this.queue.length === 0 && !this.isProcessing) {
      return Promise.resolve();
    }

    return new Promise((resolve) => {
      this.drainCallbacks.push(resolve);
    });
  }

  async processQueue() {
    await super.processQueue();

    // 检查是否所有操作都已完成
    if (
      this.queue.length === 0 &&
      !this.isProcessing &&
      this.drainCallbacks.length > 0
    ) {
      this.drainCallbacks.forEach((cb) => cb());
      this.drainCallbacks = [];
    }
  }
}

// 创建全局写队列实例
const globalWriteQueue = new EnhancedWriteQueue();

export default globalWriteQueue;
