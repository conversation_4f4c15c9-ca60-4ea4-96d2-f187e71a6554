import { Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { HomeComponent } from './home/<USER>';
import { RoomComponent } from './room/room.component';
import { ExamComponent } from './exam/exam.component';
import { ScheduleComponent } from './exam/schedule/schedule.component';
import { RegisterCardComponent } from './login/register-card.component';
import { LoginCardComponent } from './login/login-card.component';
import { RoomMonitorComponent } from './exam/room-monitor/room-monitor.component';
import { OperationLogComponent } from './dashboard/operation-log/operation-log.component';

export const routes: Routes = [
  { path: '', redirectTo: '/login', pathMatch: 'full' },
  {
    path: 'login',
    component: LoginComponent,
    children: [
      { path: '', component: LoginCardComponent },
      { path: 'register', component: RegisterCardComponent },
    ],
  },
  {
    path: 'dashboard',
    component: DashboardComponent,
    children: [
      { path: '', redirectTo: 'home', pathMatch: 'full' },
      { path: 'log', component: OperationLogComponent },
      { path: 'home', component: HomeComponent },
      { path: 'room', component: RoomComponent },
      { path: 'schedule', component: ScheduleComponent },
      {
        path: 'schedule/:id',
        component: ExamComponent,
        children: [
          { path: '', redirectTo: 'schedule', pathMatch: 'full' },
          { path: 'center', component: ScheduleComponent },
          { path: 'monitor', component: RoomMonitorComponent },
        ],
      },
    ],
  },
];
