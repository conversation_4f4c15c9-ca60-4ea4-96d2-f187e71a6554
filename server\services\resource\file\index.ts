import serverTime from '@services/serverTime';
import { getBasePath } from '@server/config/env';
import { PackageFileManager } from './package-file.manager';
import { ExamFileManager } from './exam-file.manager';

export const EXAM_BASE_PATH = getBasePath('exams');
export const FORM_BASE_PATH = getBasePath('forms');
export const PACKAGE_BASE_PATH = getBasePath('packages');
export const TEMP_BASE_PATH = getBasePath('temp');

export const examFileManager = new ExamFileManager(EXAM_BASE_PATH, serverTime);
export const packageFileManager = new PackageFileManager(
  PACKAGE_BASE_PATH,
  serverTime,
);
