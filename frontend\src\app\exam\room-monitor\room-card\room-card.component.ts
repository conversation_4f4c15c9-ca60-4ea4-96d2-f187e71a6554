import { CommonModule } from '@angular/common';
import { Component, Input, ViewChild, type OnInit } from '@angular/core';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzStepsModule } from 'ng-zorro-antd/steps';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { RoomData } from '../room-monitor.component';
import { ChartPieComponent } from '@app/shared/components/chart-pie.component';
import { ExamHttpService, SessionStatus } from '@app/core/http/exam.service';
import { MessageService } from '@app/core/services/message.service';
import { ModalService } from '@app/core/services/modal.service';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { CustomDatePipe } from '@app/shared/pipes/custom-date.pipe';
import { formatFileSize } from '@app/utils/formatFileSize';

enum ESessionStatus {
  NotStart = 0,
  Enter = 1,
  Test = 2,
  End = 3,
  Upload = 4,
  Backup = 5,
}

interface RoomPackageInfo {
  id: string;
  name: string;
  created_at: number;
  size: string;
}
@Component({
    selector: 'app-room-card',
    imports: [
        CommonModule,
        NzTableModule,
        NzButtonModule,
        NzStepsModule,
        NzIconModule,
        NzPopoverModule,
        NzDropDownModule,
        NzDividerModule,
        ChartPieComponent,
        CustomDatePipe
    ],
    templateUrl: './room-card.component.html',
    styleUrls: ['./room-card.component.scss']
})
export class RoomCardComponent implements OnInit {
  roomData: RoomData;
  breakRuleCount: 0;
  currentStatus = 0;
  entryCount: { total: number; absent: number; test: number } = {} as any;
  roomPkgList: RoomPackageInfo[] = [];
  @Input() set room(data: RoomData) {
    this.roomData = data;
    this.breakRuleCount = this.roomData.event_list.reduce((pre, cur) => {
      if (+cur.type < 500) {
        pre ++
      }
      return pre;
    },0)
    this.currentStatus =
      {
        [SessionStatus.notEnter]: ESessionStatus.NotStart,
        [SessionStatus.enter]: ESessionStatus.Enter,
        [SessionStatus.test]: ESessionStatus.Test,
        [SessionStatus.pause]: ESessionStatus.Test,
        [SessionStatus.end]: ESessionStatus.End,
        [SessionStatus.centerUploaded]: ESessionStatus.Upload,
      }[data.status] - 1;
    this.entryCount = {
      total: data.entry_status.total,
      absent: data.entry_status.absent,
      test:
        data.entry_status.login +
        data.entry_status.testing +
        data.entry_status.finished,
    };
  }

  @ViewChild('tplTitle')  tplTitle: any;
  @ViewChild('tplContent')  tplBody: any;
  @ViewChild('tplFooter')  tplFooter: any;

  constructor(
    private examHttpService: ExamHttpService,
    private msgService: MessageService,
    private modalService: ModalService
  ) {}
  ngOnInit(): void {}

  exportResult() {
    this.selectDirPath().then((path) => {
      if (!path) {
        return;
      }
      this.examHttpService
        .exportExamResult(this.roomData.schedule_id, path, this.roomData.id)
        .subscribe({
          next: (res) => {
            if (res.status === 'error') {
              console.log('Room: export exam result failed: ', path);
              this.msgService.create('error', '导出失败!');
            } else {
              console.log('Room: export exam result: ', path);
              this.msgService.create('success', '导出成功!');
            }
          },
        });
    });
  }

  checkRoomPkg() {
    this.examHttpService.getRoomPackageList(this.roomData.schedule_id, this.roomData.id).subscribe((res) => {
      if(res.status === 'error') {
        return this.msgService.create('error', '获取数据包列表失败！')
      }
      const list = res.data
      if (!list.length) {
        return this.msgService.create('error', '无可导出的数据包！')
      }
      if (list.length === 1) {
        this.exportRoomPkg(list[0].id);
      } else {
        this.roomPkgList = list.map(f => ({
          ...f,
          size: formatFileSize(f.size), 
        }));
        this.modalService.createTemplate(this.tplTitle, this.tplBody, this.tplFooter, {nzWidth: 1000})
      }
    })
  }

  exportRoomPkg(file_id: string) {
    this.selectDirPath().then((path) => {
      if (!path) {
        return;
      };
      this.examHttpService
      .exportRoomPackage(this.roomData.schedule_id, this.roomData.id, path, file_id)
      .subscribe((res) => {
        if (res.status === 'error') {
          console.log('Room: export room package failed: ', path);
          this.msgService.create('error', '导出失败!');
        } else {
          console.log('Room: export room package: ', path);
          this.msgService.create('success', '导出成功!');
        }
      });
    })
  }

  async selectDirPath() {
    if (joyshell) {
      const r = await joyshell.ShowOpenDialog({
        title: '请选择导出目录',
        properties: ['openDirectory'],
      });
      if (!r.canceled) {
        return r.filePaths[0];
      }
    }
    return null;
  }
}
