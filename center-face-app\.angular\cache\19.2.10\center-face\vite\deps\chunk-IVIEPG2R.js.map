{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@ant-design+colors@7.1.0/node_modules/@ant-design/colors/es/generate.js", "../../../../../../node_modules/.pnpm/@ant-design+colors@7.1.0/node_modules/@ant-design/colors/es/presets.js", "../../../../../../node_modules/.pnpm/@ant-design+icons-angular@19.0.0_@angular+common@19.2.9_@angular+core@19.2.9_rxjs@7.8.1_zone._ublps276jfbqs35mm7onqclahy/node_modules/@ant-design/icons-angular/fesm2022/ant-design-icons-angular.mjs", "../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-icon.mjs"], "sourcesContent": ["import { inputToRGB, rgbToHex, rgbToHsv } from '@ctrl/tinycolor';\nvar hueStep = 2; // 色相阶梯\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\nvar lightColorCount = 5; // 浅色数量，主色上\nvar darkColorCount = 4; // 深色数量，主色下\n// 暗色主题颜色映射关系表\nvar darkColorMap = [{\n  index: 7,\n  opacity: 0.15\n}, {\n  index: 6,\n  opacity: 0.25\n}, {\n  index: 5,\n  opacity: 0.3\n}, {\n  index: 5,\n  opacity: 0.45\n}, {\n  index: 5,\n  opacity: 0.65\n}, {\n  index: 5,\n  opacity: 0.85\n}, {\n  index: 4,\n  opacity: 0.9\n}, {\n  index: 3,\n  opacity: 0.95\n}, {\n  index: 2,\n  opacity: 0.97\n}, {\n  index: 1,\n  opacity: 0.98\n}];\n// Wrapper function ported from TinyColor.prototype.toHsv\n// Keep it here because of `hsv.h * 360`\nfunction toHsv(_ref) {\n  var r = _ref.r,\n    g = _ref.g,\n    b = _ref.b;\n  var hsv = rgbToHsv(r, g, b);\n  return {\n    h: hsv.h * 360,\n    s: hsv.s,\n    v: hsv.v\n  };\n}\n\n// Wrapper function ported from TinyColor.prototype.toHexString\n// Keep it here because of the prefix `#`\nfunction toHex(_ref2) {\n  var r = _ref2.r,\n    g = _ref2.g,\n    b = _ref2.b;\n  return \"#\".concat(rgbToHex(r, g, b, false));\n}\n\n// Wrapper function ported from TinyColor.prototype.mix, not treeshakable.\n// Amount in range [0, 1]\n// Assume color1 & color2 has no alpha, since the following src code did so.\nfunction mix(rgb1, rgb2, amount) {\n  var p = amount / 100;\n  var rgb = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b\n  };\n  return rgb;\n}\nfunction getHue(hsv, i, light) {\n  var hue;\n  // 根据色相不同，色相转向不同\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n  return hue;\n}\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n  var saturation;\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  }\n  // 边界值修正\n  if (saturation > 1) {\n    saturation = 1;\n  }\n  // 第一格的 s 限制在 0.06-0.1 之间\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n  return Number(saturation.toFixed(2));\n}\nfunction getValue(hsv, i, light) {\n  var value;\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n  if (value > 1) {\n    value = 1;\n  }\n  return Number(value.toFixed(2));\n}\nexport default function generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = inputToRGB(color);\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var hsv = toHsv(pColor);\n    var colorString = toHex(inputToRGB({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    }));\n    patterns.push(colorString);\n  }\n  patterns.push(toHex(pColor));\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _hsv = toHsv(pColor);\n    var _colorString = toHex(inputToRGB({\n      h: getHue(_hsv, _i),\n      s: getSaturation(_hsv, _i),\n      v: getValue(_hsv, _i)\n    }));\n    patterns.push(_colorString);\n  }\n\n  // dark theme patterns\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref3) {\n      var index = _ref3.index,\n        opacity = _ref3.opacity;\n      var darkColorString = toHex(mix(inputToRGB(opts.backgroundColor || '#141414'), inputToRGB(patterns[index]), opacity * 100));\n      return darkColorString;\n    });\n  }\n  return patterns;\n}", "// Generated by script. Do NOT modify!\n\nexport var presetPrimaryColors = {\n  \"red\": \"#F5222D\",\n  \"volcano\": \"#FA541C\",\n  \"orange\": \"#FA8C16\",\n  \"gold\": \"#FAAD14\",\n  \"yellow\": \"#FADB14\",\n  \"lime\": \"#A0D911\",\n  \"green\": \"#52C41A\",\n  \"cyan\": \"#13C2C2\",\n  \"blue\": \"#1677FF\",\n  \"geekblue\": \"#2F54EB\",\n  \"purple\": \"#722ED1\",\n  \"magenta\": \"#EB2F96\",\n  \"grey\": \"#666666\"\n};\nexport var red = [\"#fff1f0\", \"#ffccc7\", \"#ffa39e\", \"#ff7875\", \"#ff4d4f\", \"#f5222d\", \"#cf1322\", \"#a8071a\", \"#820014\", \"#5c0011\"];\nred.primary = red[5];\nexport var volcano = [\"#fff2e8\", \"#ffd8bf\", \"#ffbb96\", \"#ff9c6e\", \"#ff7a45\", \"#fa541c\", \"#d4380d\", \"#ad2102\", \"#871400\", \"#610b00\"];\nvolcano.primary = volcano[5];\nexport var orange = [\"#fff7e6\", \"#ffe7ba\", \"#ffd591\", \"#ffc069\", \"#ffa940\", \"#fa8c16\", \"#d46b08\", \"#ad4e00\", \"#873800\", \"#612500\"];\norange.primary = orange[5];\nexport var gold = [\"#fffbe6\", \"#fff1b8\", \"#ffe58f\", \"#ffd666\", \"#ffc53d\", \"#faad14\", \"#d48806\", \"#ad6800\", \"#874d00\", \"#613400\"];\ngold.primary = gold[5];\nexport var yellow = [\"#feffe6\", \"#ffffb8\", \"#fffb8f\", \"#fff566\", \"#ffec3d\", \"#fadb14\", \"#d4b106\", \"#ad8b00\", \"#876800\", \"#614700\"];\nyellow.primary = yellow[5];\nexport var lime = [\"#fcffe6\", \"#f4ffb8\", \"#eaff8f\", \"#d3f261\", \"#bae637\", \"#a0d911\", \"#7cb305\", \"#5b8c00\", \"#3f6600\", \"#254000\"];\nlime.primary = lime[5];\nexport var green = [\"#f6ffed\", \"#d9f7be\", \"#b7eb8f\", \"#95de64\", \"#73d13d\", \"#52c41a\", \"#389e0d\", \"#237804\", \"#135200\", \"#092b00\"];\ngreen.primary = green[5];\nexport var cyan = [\"#e6fffb\", \"#b5f5ec\", \"#87e8de\", \"#5cdbd3\", \"#36cfc9\", \"#13c2c2\", \"#08979c\", \"#006d75\", \"#00474f\", \"#002329\"];\ncyan.primary = cyan[5];\nexport var blue = [\"#e6f4ff\", \"#bae0ff\", \"#91caff\", \"#69b1ff\", \"#4096ff\", \"#1677ff\", \"#0958d9\", \"#003eb3\", \"#002c8c\", \"#001d66\"];\nblue.primary = blue[5];\nexport var geekblue = [\"#f0f5ff\", \"#d6e4ff\", \"#adc6ff\", \"#85a5ff\", \"#597ef7\", \"#2f54eb\", \"#1d39c4\", \"#10239e\", \"#061178\", \"#030852\"];\ngeekblue.primary = geekblue[5];\nexport var purple = [\"#f9f0ff\", \"#efdbff\", \"#d3adf7\", \"#b37feb\", \"#9254de\", \"#722ed1\", \"#531dab\", \"#391085\", \"#22075e\", \"#120338\"];\npurple.primary = purple[5];\nexport var magenta = [\"#fff0f6\", \"#ffd6e7\", \"#ffadd2\", \"#ff85c0\", \"#f759ab\", \"#eb2f96\", \"#c41d7f\", \"#9e1068\", \"#780650\", \"#520339\"];\nmagenta.primary = magenta[5];\nexport var grey = [\"#a6a6a6\", \"#999999\", \"#8c8c8c\", \"#808080\", \"#737373\", \"#666666\", \"#404040\", \"#1a1a1a\", \"#000000\", \"#000000\"];\ngrey.primary = grey[5];\nexport var gray = grey;\nexport var presetPalettes = {\n  red: red,\n  volcano: volcano,\n  orange: orange,\n  gold: gold,\n  yellow: yellow,\n  lime: lime,\n  green: green,\n  cyan: cyan,\n  blue: blue,\n  geekblue: geekblue,\n  purple: purple,\n  magenta: magenta,\n  grey: grey\n};\nexport var redDark = [\"#2a1215\", \"#431418\", \"#58181c\", \"#791a1f\", \"#a61d24\", \"#d32029\", \"#e84749\", \"#f37370\", \"#f89f9a\", \"#fac8c3\"];\nredDark.primary = redDark[5];\nexport var volcanoDark = [\"#2b1611\", \"#441d12\", \"#592716\", \"#7c3118\", \"#aa3e19\", \"#d84a1b\", \"#e87040\", \"#f3956a\", \"#f8b692\", \"#fad4bc\"];\nvolcanoDark.primary = volcanoDark[5];\nexport var orangeDark = [\"#2b1d11\", \"#442a11\", \"#593815\", \"#7c4a15\", \"#aa6215\", \"#d87a16\", \"#e89a3c\", \"#f3b765\", \"#f8cf8d\", \"#fae3b7\"];\norangeDark.primary = orangeDark[5];\nexport var goldDark = [\"#2b2111\", \"#443111\", \"#594214\", \"#7c5914\", \"#aa7714\", \"#d89614\", \"#e8b339\", \"#f3cc62\", \"#f8df8b\", \"#faedb5\"];\ngoldDark.primary = goldDark[5];\nexport var yellowDark = [\"#2b2611\", \"#443b11\", \"#595014\", \"#7c6e14\", \"#aa9514\", \"#d8bd14\", \"#e8d639\", \"#f3ea62\", \"#f8f48b\", \"#fafab5\"];\nyellowDark.primary = yellowDark[5];\nexport var limeDark = [\"#1f2611\", \"#2e3c10\", \"#3e4f13\", \"#536d13\", \"#6f9412\", \"#8bbb11\", \"#a9d134\", \"#c9e75d\", \"#e4f88b\", \"#f0fab5\"];\nlimeDark.primary = limeDark[5];\nexport var greenDark = [\"#162312\", \"#1d3712\", \"#274916\", \"#306317\", \"#3c8618\", \"#49aa19\", \"#6abe39\", \"#8fd460\", \"#b2e58b\", \"#d5f2bb\"];\ngreenDark.primary = greenDark[5];\nexport var cyanDark = [\"#112123\", \"#113536\", \"#144848\", \"#146262\", \"#138585\", \"#13a8a8\", \"#33bcb7\", \"#58d1c9\", \"#84e2d8\", \"#b2f1e8\"];\ncyanDark.primary = cyanDark[5];\nexport var blueDark = [\"#111a2c\", \"#112545\", \"#15325b\", \"#15417e\", \"#1554ad\", \"#1668dc\", \"#3c89e8\", \"#65a9f3\", \"#8dc5f8\", \"#b7dcfa\"];\nblueDark.primary = blueDark[5];\nexport var geekblueDark = [\"#131629\", \"#161d40\", \"#1c2755\", \"#203175\", \"#263ea0\", \"#2b4acb\", \"#5273e0\", \"#7f9ef3\", \"#a8c1f8\", \"#d2e0fa\"];\ngeekblueDark.primary = geekblueDark[5];\nexport var purpleDark = [\"#1a1325\", \"#24163a\", \"#301c4d\", \"#3e2069\", \"#51258f\", \"#642ab5\", \"#854eca\", \"#ab7ae0\", \"#cda8f0\", \"#ebd7fa\"];\npurpleDark.primary = purpleDark[5];\nexport var magentaDark = [\"#291321\", \"#40162f\", \"#551c3b\", \"#75204f\", \"#a02669\", \"#cb2b83\", \"#e0529c\", \"#f37fb7\", \"#f8a8cc\", \"#fad2e3\"];\nmagentaDark.primary = magentaDark[5];\nexport var greyDark = [\"#151515\", \"#1f1f1f\", \"#2d2d2d\", \"#393939\", \"#494949\", \"#5a5a5a\", \"#6a6a6a\", \"#7b7b7b\", \"#888888\", \"#969696\"];\ngreyDark.primary = greyDark[5];\nexport var presetDarkPalettes = {\n  red: redDark,\n  volcano: volcanoDark,\n  orange: orangeDark,\n  gold: goldDark,\n  yellow: yellowDark,\n  lime: limeDark,\n  green: greenDark,\n  cyan: cyanDark,\n  blue: blueDark,\n  geekblue: geekblueDark,\n  purple: purpleDark,\n  magenta: magentaDark,\n  grey: greyDark\n};", "import * as i0 from '@angular/core';\nimport { isDevMode, InjectionToken, SecurityContext, Injectable, Optional, Inject, inject, ElementRef, Renderer2, Directive, Input, NgModule, makeEnvironmentProviders } from '@angular/core';\nimport { generate } from '@ant-design/colors';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport { Subject, of, Observable } from 'rxjs';\nimport { map, tap, finalize, catchError, share, filter, take } from 'rxjs/operators';\nimport * as i2 from '@angular/platform-browser';\nconst ANT_ICON_ANGULAR_CONSOLE_PREFIX = '[@ant-design/icons-angular]:';\nfunction error(message) {\n  console.error(`${ANT_ICON_ANGULAR_CONSOLE_PREFIX} ${message}.`);\n}\nfunction warn(message) {\n  if (isDevMode()) {\n    console.warn(`${ANT_ICON_ANGULAR_CONSOLE_PREFIX} ${message}.`);\n  }\n}\nfunction getSecondaryColor(primaryColor) {\n  return generate(primaryColor)[0];\n}\nfunction withSuffix(name, theme) {\n  switch (theme) {\n    case 'fill':\n      return `${name}-fill`;\n    case 'outline':\n      return `${name}-o`;\n    case 'twotone':\n      return `${name}-twotone`;\n    case undefined:\n      return name;\n    default:\n      throw new Error(`${ANT_ICON_ANGULAR_CONSOLE_PREFIX}Theme \"${theme}\" is not a recognized theme!`);\n  }\n}\nfunction withSuffixAndColor(name, theme, pri, sec) {\n  return `${withSuffix(name, theme)}-${pri}-${sec}`;\n}\nfunction mapAbbrToTheme(abbr) {\n  return abbr === 'o' ? 'outline' : abbr;\n}\nfunction alreadyHasAThemeSuffix(name) {\n  return name.endsWith('-fill') || name.endsWith('-o') || name.endsWith('-twotone');\n}\nfunction isIconDefinition(target) {\n  return typeof target === 'object' && typeof target.name === 'string' && (typeof target.theme === 'string' || target.theme === undefined) && typeof target.icon === 'string';\n}\n/**\n * Get an `IconDefinition` object from abbreviation type, like `account-book-fill`.\n * @param str\n */\nfunction getIconDefinitionFromAbbr(str) {\n  const arr = str.split('-');\n  const theme = mapAbbrToTheme(arr.splice(arr.length - 1, 1)[0]);\n  const name = arr.join('-');\n  return {\n    name,\n    theme,\n    icon: ''\n  };\n}\nfunction cloneSVG(svg) {\n  return svg.cloneNode(true);\n}\n/**\n * Parse inline SVG string and replace colors with placeholders. For twotone icons only.\n */\nfunction replaceFillColor(raw) {\n  return raw.replace(/['\"]#333['\"]/g, '\"primaryColor\"').replace(/['\"]#E6E6E6['\"]/g, '\"secondaryColor\"').replace(/['\"]#D9D9D9['\"]/g, '\"secondaryColor\"').replace(/['\"]#D8D8D8['\"]/g, '\"secondaryColor\"');\n}\n/**\n * Split a name with namespace in it into a tuple like [ name, namespace ].\n */\nfunction getNameAndNamespace(type) {\n  const split = type.split(':');\n  switch (split.length) {\n    case 1:\n      return [type, ''];\n    case 2:\n      return [split[1], split[0]];\n    default:\n      throw new Error(`${ANT_ICON_ANGULAR_CONSOLE_PREFIX}The icon type ${type} is not valid!`);\n  }\n}\nfunction hasNamespace(type) {\n  return getNameAndNamespace(type)[1] !== '';\n}\nfunction NameSpaceIsNotSpecifyError() {\n  return new Error(`${ANT_ICON_ANGULAR_CONSOLE_PREFIX}Type should have a namespace. Try \"namespace:${name}\".`);\n}\nfunction IconNotFoundError(icon) {\n  return new Error(`${ANT_ICON_ANGULAR_CONSOLE_PREFIX}the icon ${icon} does not exist or is not registered.`);\n}\nfunction HttpModuleNotImport() {\n  error(`you need to import \"HttpClientModule\" to use dynamic importing.`);\n  return null;\n}\nfunction UrlNotSafeError(url) {\n  return new Error(`${ANT_ICON_ANGULAR_CONSOLE_PREFIX}The url \"${url}\" is unsafe.`);\n}\nfunction SVGTagNotFoundError() {\n  return new Error(`${ANT_ICON_ANGULAR_CONSOLE_PREFIX}<svg> tag not found.`);\n}\nfunction DynamicLoadingTimeoutError() {\n  return new Error(`${ANT_ICON_ANGULAR_CONSOLE_PREFIX}Importing timeout error.`);\n}\nconst JSONP_HANDLER_NAME = '__ant_icon_load';\nconst ANT_ICONS = new InjectionToken('ant_icons');\nclass IconService {\n  set twoToneColor({\n    primaryColor,\n    secondaryColor\n  }) {\n    this._twoToneColorPalette.primaryColor = primaryColor;\n    this._twoToneColorPalette.secondaryColor = secondaryColor || getSecondaryColor(primaryColor);\n  }\n  get twoToneColor() {\n    // Make a copy to avoid unexpected changes.\n    return {\n      ...this._twoToneColorPalette\n    };\n  }\n  /**\n   * Disable dynamic loading (support static loading only).\n   */\n  get _disableDynamicLoading() {\n    return false;\n  }\n  constructor(_rendererFactory, _handler, _document, sanitizer, _antIcons) {\n    this._rendererFactory = _rendererFactory;\n    this._handler = _handler;\n    this._document = _document;\n    this.sanitizer = sanitizer;\n    this._antIcons = _antIcons;\n    this.defaultTheme = 'outline';\n    /**\n     * All icon definitions would be registered here.\n     */\n    this._svgDefinitions = new Map();\n    /**\n     * Cache all rendered icons. Icons are identified by name, theme,\n     * and for twotone icons, primary color and secondary color.\n     */\n    this._svgRenderedDefinitions = new Map();\n    this._inProgressFetches = new Map();\n    /**\n     * Url prefix for fetching inline SVG by dynamic importing.\n     */\n    this._assetsUrlRoot = '';\n    this._twoToneColorPalette = {\n      primaryColor: '#333333',\n      secondaryColor: '#E6E6E6'\n    };\n    /** A flag indicates whether jsonp loading is enabled. */\n    this._enableJsonpLoading = false;\n    this._jsonpIconLoad$ = new Subject();\n    this._renderer = this._rendererFactory.createRenderer(null, null);\n    if (this._handler) {\n      this._http = new HttpClient(this._handler);\n    }\n    if (this._antIcons) {\n      this.addIcon(...this._antIcons);\n    }\n  }\n  /**\n   * Call this method to switch to jsonp like loading.\n   */\n  useJsonpLoading() {\n    if (!this._enableJsonpLoading) {\n      this._enableJsonpLoading = true;\n      window[JSONP_HANDLER_NAME] = icon => {\n        this._jsonpIconLoad$.next(icon);\n      };\n    } else {\n      warn('You are already using jsonp loading.');\n    }\n  }\n  /**\n   * Change the prefix of the inline svg resources, so they could be deployed elsewhere, like CDN.\n   * @param prefix\n   */\n  changeAssetsSource(prefix) {\n    this._assetsUrlRoot = prefix.endsWith('/') ? prefix : prefix + '/';\n  }\n  /**\n   * Add icons provided by ant design.\n   * @param icons\n   */\n  addIcon(...icons) {\n    icons.forEach(icon => {\n      this._svgDefinitions.set(withSuffix(icon.name, icon.theme), icon);\n    });\n  }\n  /**\n   * Register an icon. Namespace is required.\n   * @param type\n   * @param literal\n   */\n  addIconLiteral(type, literal) {\n    const [_, namespace] = getNameAndNamespace(type);\n    if (!namespace) {\n      throw NameSpaceIsNotSpecifyError();\n    }\n    this.addIcon({\n      name: type,\n      icon: literal\n    });\n  }\n  /**\n   * Remove all cache.\n   */\n  clear() {\n    this._svgDefinitions.clear();\n    this._svgRenderedDefinitions.clear();\n  }\n  /**\n   * Get a rendered `SVGElement`.\n   * @param icon\n   * @param twoToneColor\n   */\n  getRenderedContent(icon, twoToneColor) {\n    // If `icon` is a `IconDefinition`, go to the next step. If not, try to fetch it from cache.\n    const definition = isIconDefinition(icon) ? icon : this._svgDefinitions.get(icon) || null;\n    if (!definition && this._disableDynamicLoading) {\n      throw IconNotFoundError(icon);\n    }\n    // If `icon` is a `IconDefinition` of successfully fetch, wrap it in an `Observable`.\n    // Otherwise try to fetch it from remote.\n    const $iconDefinition = definition ? of(definition) : this._loadIconDynamically(icon);\n    // If finally get an `IconDefinition`, render and return it. Otherwise throw an error.\n    return $iconDefinition.pipe(map(i => {\n      if (!i) {\n        throw IconNotFoundError(icon);\n      }\n      return this._loadSVGFromCacheOrCreateNew(i, twoToneColor);\n    }));\n  }\n  getCachedIcons() {\n    return this._svgDefinitions;\n  }\n  /**\n   * Get raw svg and assemble a `IconDefinition` object.\n   * @param type\n   */\n  _loadIconDynamically(type) {\n    // If developer doesn't provide HTTP module nor enable jsonp loading, just throw an error.\n    if (!this._http && !this._enableJsonpLoading) {\n      return of(HttpModuleNotImport());\n    }\n    // If multi directive ask for the same icon at the same time,\n    // request should only be fired once.\n    let inProgress = this._inProgressFetches.get(type);\n    if (!inProgress) {\n      const [name, namespace] = getNameAndNamespace(type);\n      // If the string has a namespace within, create a simple `IconDefinition`.\n      const icon = namespace ? {\n        name: type,\n        icon: ''\n      } : getIconDefinitionFromAbbr(name);\n      const suffix = this._enableJsonpLoading ? '.js' : '.svg';\n      const url = (namespace ? `${this._assetsUrlRoot}assets/${namespace}/${name}` : `${this._assetsUrlRoot}assets/${icon.theme}/${icon.name}`) + suffix;\n      const safeUrl = this.sanitizer.sanitize(SecurityContext.URL, url);\n      if (!safeUrl) {\n        throw UrlNotSafeError(url);\n      }\n      const source = !this._enableJsonpLoading ? this._http.get(safeUrl, {\n        responseType: 'text'\n      }).pipe(map(literal => ({\n        ...icon,\n        icon: literal\n      }))) : this._loadIconDynamicallyWithJsonp(icon, safeUrl);\n      inProgress = source.pipe(tap(definition => this.addIcon(definition)), finalize(() => this._inProgressFetches.delete(type)), catchError(() => of(null)), share());\n      this._inProgressFetches.set(type, inProgress);\n    }\n    return inProgress;\n  }\n  _loadIconDynamicallyWithJsonp(icon, url) {\n    return new Observable(subscriber => {\n      const loader = this._document.createElement('script');\n      const timer = setTimeout(() => {\n        clean();\n        subscriber.error(DynamicLoadingTimeoutError());\n      }, 6000);\n      loader.src = url;\n      function clean() {\n        loader.parentNode.removeChild(loader);\n        clearTimeout(timer);\n      }\n      this._document.body.appendChild(loader);\n      this._jsonpIconLoad$.pipe(filter(i => i.name === icon.name && i.theme === icon.theme), take(1)).subscribe(i => {\n        subscriber.next(i);\n        clean();\n      });\n    });\n  }\n  /**\n   * Render a new `SVGElement` for a given `IconDefinition`, or make a copy from cache.\n   * @param icon\n   * @param twoToneColor\n   */\n  _loadSVGFromCacheOrCreateNew(icon, twoToneColor) {\n    let svg;\n    const pri = twoToneColor || this._twoToneColorPalette.primaryColor;\n    const sec = getSecondaryColor(pri) || this._twoToneColorPalette.secondaryColor;\n    const key = icon.theme === 'twotone' ? withSuffixAndColor(icon.name, icon.theme, pri, sec) : icon.theme === undefined ? icon.name : withSuffix(icon.name, icon.theme);\n    // Try to make a copy from cache.\n    const cached = this._svgRenderedDefinitions.get(key);\n    if (cached) {\n      svg = cached.icon;\n    } else {\n      svg = this._setSVGAttribute(this._colorizeSVGIcon(\n      // Icons provided by ant design should be refined to remove preset colors.\n      this._createSVGElementFromString(hasNamespace(icon.name) ? icon.icon : replaceFillColor(icon.icon)), icon.theme === 'twotone', pri, sec));\n      // Cache it.\n      this._svgRenderedDefinitions.set(key, {\n        ...icon,\n        icon: svg\n      });\n    }\n    return cloneSVG(svg);\n  }\n  _createSVGElementFromString(str) {\n    const div = this._document.createElement('div');\n    div.innerHTML = str;\n    const svg = div.querySelector('svg');\n    if (!svg) {\n      throw SVGTagNotFoundError;\n    }\n    return svg;\n  }\n  _setSVGAttribute(svg) {\n    this._renderer.setAttribute(svg, 'width', '1em');\n    this._renderer.setAttribute(svg, 'height', '1em');\n    return svg;\n  }\n  _colorizeSVGIcon(svg, twotone, pri, sec) {\n    if (twotone) {\n      const children = svg.childNodes;\n      const length = children.length;\n      for (let i = 0; i < length; i++) {\n        const child = children[i];\n        if (child.getAttribute('fill') === 'secondaryColor') {\n          this._renderer.setAttribute(child, 'fill', sec);\n        } else {\n          this._renderer.setAttribute(child, 'fill', pri);\n        }\n      }\n    }\n    this._renderer.setAttribute(svg, 'fill', 'currentColor');\n    return svg;\n  }\n  static {\n    this.ɵfac = function IconService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IconService)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(i1.HttpBackend, 8), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(ANT_ICONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: IconService,\n      factory: IconService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IconService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.RendererFactory2\n  }, {\n    type: i1.HttpBackend,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i2.DomSanitizer\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANT_ICONS]\n    }]\n  }], null);\n})();\nfunction checkMeta(prev, after) {\n  return prev.type === after.type && prev.theme === after.theme && prev.twoToneColor === after.twoToneColor;\n}\nclass IconDirective {\n  constructor(_iconService) {\n    this._iconService = _iconService;\n    this._elementRef = inject(ElementRef);\n    this._renderer = inject(Renderer2);\n  }\n  ngOnChanges(changes) {\n    if (changes.type || changes.theme || changes.twoToneColor) {\n      this._changeIcon();\n    }\n  }\n  /**\n   * Render a new icon in the current element. Remove the icon when `type` is falsy.\n   */\n  _changeIcon() {\n    return new Promise(resolve => {\n      if (!this.type) {\n        this._clearSVGElement();\n        resolve(null);\n        return;\n      }\n      const beforeMeta = this._getSelfRenderMeta();\n      this._iconService.getRenderedContent(this._parseIconType(this.type, this.theme), this.twoToneColor).subscribe(svg => {\n        // avoid race condition\n        // see https://github.com/ant-design/ant-design-icons/issues/315\n        const afterMeta = this._getSelfRenderMeta();\n        if (checkMeta(beforeMeta, afterMeta)) {\n          this._setSVGElement(svg);\n          resolve(svg);\n        } else {\n          resolve(null);\n        }\n      });\n    });\n  }\n  _getSelfRenderMeta() {\n    return {\n      type: this.type,\n      theme: this.theme,\n      twoToneColor: this.twoToneColor\n    };\n  }\n  /**\n   * Parse a icon to the standard form, an `IconDefinition` or a string like 'account-book-fill` (with a theme suffixed).\n   * If namespace is specified, ignore theme because it meaningless for users' icons.\n   *\n   * @param type\n   * @param theme\n   */\n  _parseIconType(type, theme) {\n    if (isIconDefinition(type)) {\n      return type;\n    } else {\n      const [name, namespace] = getNameAndNamespace(type);\n      if (namespace) {\n        return type;\n      }\n      if (alreadyHasAThemeSuffix(name)) {\n        if (!!theme) {\n          warn(`'type' ${name} already gets a theme inside so 'theme' ${theme} would be ignored`);\n        }\n        return name;\n      } else {\n        return withSuffix(name, theme || this._iconService.defaultTheme);\n      }\n    }\n  }\n  _setSVGElement(svg) {\n    this._clearSVGElement();\n    this._renderer.appendChild(this._elementRef.nativeElement, svg);\n  }\n  _clearSVGElement() {\n    const el = this._elementRef.nativeElement;\n    const children = el.childNodes;\n    const length = children.length;\n    for (let i = length - 1; i >= 0; i--) {\n      const child = children[i];\n      if (child.tagName?.toLowerCase() === 'svg') {\n        this._renderer.removeChild(el, child);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function IconDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IconDirective)(i0.ɵɵdirectiveInject(IconService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: IconDirective,\n      selectors: [[\"\", \"antIcon\", \"\"]],\n      inputs: {\n        type: \"type\",\n        theme: \"theme\",\n        twoToneColor: \"twoToneColor\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IconDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[antIcon]'\n    }]\n  }], () => [{\n    type: IconService\n  }], {\n    type: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    twoToneColor: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @deprecated Please use `IconDirective` instead, will be removed in v20.\n */\nclass IconModule {\n  static {\n    this.ɵfac = function IconModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IconModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: IconModule,\n      imports: [IconDirective],\n      exports: [IconDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IconModule, [{\n    type: NgModule,\n    args: [{\n      imports: [IconDirective],\n      exports: [IconDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Provide icon definitions in root\n *\n * @param icons Icon definitions\n */\nfunction provideAntIcons(icons) {\n  return makeEnvironmentProviders([{\n    provide: ANT_ICONS,\n    useValue: icons\n  }]);\n}\nconst manifest = {\n  fill: ['alipay-square', 'aliwangwang', 'alipay-circle', 'alert', 'amazon-circle', 'android', 'apple', 'amazon-square', 'appstore', 'api', 'bank', 'behance-circle', 'behance-square', 'bell', 'bilibili', 'audio', 'book', 'account-book', 'box-plot', 'bug', 'backward', 'build', 'calculator', 'calendar', 'bulb', 'camera', 'caret-down', 'caret-right', 'caret-left', 'carry-out', 'car', 'caret-up', 'chrome', 'ci-circle', 'check-square', 'clock-circle', 'close-circle', 'close-square', 'cloud', 'code-sandbox-circle', 'code', 'codepen-square', 'code-sandbox-square', 'codepen-circle', 'compass', 'check-circle', 'contacts', 'container', 'credit-card', 'control', 'crown', 'copy', 'copyright-circle', 'customer-service', 'dashboard', 'database', 'delete', 'diff', 'dingtalk-circle', 'dingtalk-square', 'discord', 'dislike', 'dollar-circle', 'down-circle', 'down-square', 'dribbble-square', 'dribbble-circle', 'edit', 'environment', 'euro-circle', 'experiment', 'eye', 'eye-invisible', 'exclamation-circle', 'dropbox-circle', 'fast-backward', 'facebook', 'file-excel', 'file-exclamation', 'file', 'file-image', 'file-markdown', 'file-pdf', 'file-ppt', 'fast-forward', 'dropbox-square', 'file-text', 'file-zip', 'file-unknown', 'file-word', 'filter', 'fire', 'flag', 'folder-add', 'folder', 'folder-open', 'file-add', 'frown', 'fund', 'forward', 'funnel-plot', 'gift', 'gitlab', 'gold', 'github', 'golden', 'google-plus-square', 'google-plus-circle', 'google-square', 'heart', 'format-painter', 'highlight', 'home', 'hourglass', 'hdd', 'html5', 'idcard', 'google-circle', 'ie-square', 'insurance', 'instagram', 'ie-circle', 'layout', 'left-circle', 'interaction', 'left-square', 'linkedin', 'like', 'mail', 'mac-command', 'medicine-box', 'medium-circle', 'medium-square', 'meh', 'lock', 'merge', 'message', 'minus-circle', 'minus-square', 'mobile', 'moon', 'muted', 'notification', 'pay-circle', 'phone', 'pinterest', 'play-circle', 'pie-chart', 'pause-circle', 'picture', 'play-square', 'pound-circle', 'plus-circle', 'plus-square', 'printer', 'product', 'profile', 'property-safety', 'project', 'qq-circle', 'question-circle', 'qq-square', 'reconciliation', 'red-envelope', 'reddit-circle', 'read', 'reddit-square', 'right-circle', 'right-square', 'rest', 'money-collect', 'safety-certificate', 'rocket', 'schedule', 'security-scan', 'pushpin', 'setting', 'save', 'shopping', 'shop', 'signal', 'open-a-i', 'signature', 'sketch-circle', 'skype', 'slack-circle', 'sketch-square', 'slack-square', 'skin', 'sliders', 'smile', 'snippets', 'sound', 'spotify', 'star', 'step-backward', 'step-forward', 'stop', 'sun', 'info-circle', 'tablet', 'tag', 'taobao-circle', 'tags', 'thunderbolt', 'taobao-square', 'tik-tok', 'tool', 'trademark-circle', 'trophy', 'twitch', 'robot', 'truck', 'twitter-square', 'twitter-circle', 'unlock', 'up-circle', 'switcher', 'up-square', 'usb', 'video-camera', 'wallet', 'wechat', 'weibo-circle', 'windows', 'yahoo', 'youtube', 'yuque', 'zhihu-circle', 'zhihu-square', 'wechat-work', 'weibo-square', 'x', 'warning'],\n  outline: ['aim', 'alert', 'align-right', 'align-center', 'aliwangwang', 'amazon', 'ant-design', 'api', 'ant-cloud', 'align-left', 'apple', 'alipay-circle', 'appstore-add', 'alipay', 'arrow-down', 'arrows-alt', 'apartment', 'audio', 'audio-muted', 'arrow-up', 'android', 'aliyun', 'area-chart', 'arrow-left', 'backward', 'bank', 'baidu', 'barcode', 'bars', 'behance-square', 'behance', 'bell', 'bilibili', 'bg-colors', 'block', 'bold', 'border-bottom', 'border-horizontal', 'border-left', 'border', 'border-inner', 'bar-chart', 'border-right', 'border-verticle', 'account-book', 'alibaba', 'branches', 'border-top', 'arrow-right', 'bug', 'audit', 'calendar', 'calculator', 'camera', 'box-plot', 'car', 'bulb', 'book', 'caret-right', 'carry-out', 'caret-left', 'caret-down', 'check', 'check-square', 'chrome', 'caret-up', 'clock-circle', 'ci', 'ci-circle', 'close', 'close-circle', 'close-square', 'cloud-download', 'cloud', 'cloud-server', 'cloud-sync', 'code', 'check-circle', 'codepen-circle', 'code-sandbox', 'codepen', 'cluster', 'column-height', 'cloud-upload', 'column-width', 'compress', 'compass', 'console-sql', 'appstore', 'contacts', 'coffee', 'container', 'copy', 'control', 'copyright-circle', 'clear', 'build', 'dash', 'database', 'border-outer', 'delete', 'dashboard', 'delete-row', 'delivered-procedure', 'deployment-unit', 'delete-column', 'desktop', 'diff', 'dingtalk', 'dingding', 'dislike', 'disconnect', 'comment', 'docker', 'dollar-circle', 'double-left', 'dollar', 'dot-chart', 'dot-net', 'double-right', 'down-circle', 'down', 'borderless-table', 'drag', 'dribbble-square', 'dribbble', 'down-square', 'ellipsis', 'dropbox', 'enter', 'euro-circle', 'environment', 'exception', 'euro', 'customer-service', 'exclamation-circle', 'expand-alt', 'exclamation', 'expand', 'export', 'experiment', 'eye-invisible', 'eye', 'facebook', 'fast-forward', 'fall', 'download', 'field-binary', 'field-number', 'credit-card', 'field-string', 'field-time', 'file-excel', 'file-exclamation', 'file-add', 'file-done', 'crown', 'discord', 'file-markdown', 'file', 'file-pdf', 'file-jpg', 'file-gif', 'file-sync', 'file-protect', 'file-search', 'file-unknown', 'file-word', 'file-text', 'file-zip', 'filter', 'flag', 'fire', 'folder-add', 'folder-open', 'folder', 'font-size', 'edit', 'file-ppt', 'form', 'folder-view', 'copyright', 'fork', 'forward', 'function', 'fullscreen-exit', 'fund', 'fund-projection-screen', 'funnel-plot', 'gateway', 'gift', 'gif', 'font-colors', 'frown', 'global', 'gitlab', 'gold', 'google', 'google-plus', 'group', 'hdd', 'harmony-o-s', 'heart', 'heat-map', 'highlight', 'home', 'html5', 'format-painter', 'ie', 'idcard', 'github', 'hourglass', 'holder', 'fullscreen', 'info-circle', 'fund-view', 'inbox', 'insert-row-left', 'info', 'import', 'insert-row-above', 'insert-row-right', 'interaction', 'italic', 'java', 'issues-close', 'java-script', 'kubernetes', 'instagram', 'laptop', 'layout', 'insurance', 'left-square', 'line-chart', 'left-circle', 'line', 'like', 'loading', 'loading-3-quarters', 'linux', 'line-height', 'left', 'lock', 'mac-command', 'mail', 'link', 'man', 'medicine-box', 'medium-workmark', 'medium', 'meh', 'logout', 'menu-fold', 'login', 'linkedin', 'message', 'menu', 'minus-circle', 'key', 'minus-square', 'mobile', 'money-collect', 'fast-backward', 'monitor', 'moon', 'more', 'node-index', 'node-collapse', 'node-expand', 'merge', 'menu-unfold', 'number', 'merge-cells', 'notification', 'ordered-list', 'paper-clip', 'partition', 'muted', 'pause', 'one-to-one', 'pay-circle', 'percentage', 'pic-center', 'pic-right', 'pic-left', 'pause-circle', 'pie-chart', 'picture', 'play-circle', 'play-square', 'plus', 'plus-square', 'plus-circle', 'pound-circle', 'pound', 'poweroff', 'profile', 'printer', 'project', 'product', 'phone', 'property-safety', 'python', 'pinterest', 'pushpin', 'qrcode', 'question-circle', 'radius-bottomleft', 'radius-bottomright', 'pull-request', 'question', 'radius-setting', 'radar-chart', 'radius-upleft', 'radius-upright', 'read', 'reconciliation', 'red-envelope', 'reddit', 'redo', 'reload', 'retweet', 'rest', 'right-circle', 'right-square', 'right', 'robot', 'rise', 'rocket', 'rollback', 'rotate-right', 'safety-certificate', 'rotate-left', 'qq', 'ruby', 'save', 'scan', 'security-scan', 'scissor', 'schedule', 'select', 'send', 'minus', 'shake', 'setting', 'shop', 'shopping', 'shopping-cart', 'shrink', 'sisternode', 'sketch', 'skin', 'skype', 'slack', 'slack-square', 'sliders', 'small-dash', 'smile', 'snippets', 'file-image', 'solution', 'sort-ascending', 'split-cells', 'sort-descending', 'signature', 'spotify', 'search', 'star', 'step-forward', 'stock', 'sound', 'step-backward', 'share-alt', 'stop', 'sun', 'strikethrough', 'swap-left', 'swap', 'switcher', 'sync', 'table', 'tag', 'tablet', 'taobao-circle', 'taobao', 'team', 'subnode', 'thunderbolt', 'tags', 'tool', 'trademark-circle', 'tik-tok', 'to-top', 'swap-right', 'translation', 'safety', 'truck', 'twitch', 'underline', 'undo', 'unlock', 'ungroup', 'trademark', 'twitter', 'open-a-i', 'up', 'up-square', 'upload', 'usb', 'trophy', 'user', 'user-delete', 'usergroup-add', 'usergroup-delete', 'unordered-list', 'verified', 'vertical-left', 'vertical-right', 'video-camera', 'video-camera-add', 'vertical-align-top', 'user-add', 'history', 'insert-row-below', 'wechat', 'wechat-work', 'warning', 'weibo-circle', 'weibo', 'wifi', 'weibo-square', 'windows', 'whats-app', 'woman', 'user-switch', 'yahoo', 'x', 'yuque', 'zhihu', 'youtube', 'vertical-align-middle', 'up-circle', 'transaction', 'zoom-out', 'zoom-in', 'vertical-align-bottom', 'wallet'],\n  twotone: ['account-book', 'alert', 'api', 'audio', 'bank', 'bell', 'book', 'appstore', 'bug', 'bulb', 'calendar', 'calculator', 'car', 'camera', 'box-plot', 'carry-out', 'ci-circle', 'ci', 'check-square', 'clock-circle', 'close-circle', 'close-square', 'check-circle', 'cloud', 'code', 'compass', 'contacts', 'container', 'copy', 'build', 'credit-card', 'copyright', 'customer-service', 'crown', 'dashboard', 'delete', 'diff', 'database', 'dollar-circle', 'dollar', 'down-circle', 'dislike', 'copyright-circle', 'down-square', 'edit', 'euro-circle', 'environment', 'euro', 'exclamation-circle', 'experiment', 'eye-invisible', 'eye', 'file-add', 'file-excel', 'file-exclamation', 'control', 'file-image', 'file-markdown', 'file', 'file-text', 'file-unknown', 'file-zip', 'filter', 'fire', 'file-ppt', 'flag', 'folder-open', 'folder-add', 'folder', 'frown', 'fund', 'funnel-plot', 'gift', 'gold', 'file-word', 'hdd', 'heart', 'file-pdf', 'home', 'hourglass', 'html5', 'idcard', 'highlight', 'info-circle', 'insurance', 'layout', 'interaction', 'left-square', 'like', 'left-circle', 'lock', 'mail', 'medicine-box', 'meh', 'minus-square', 'minus-circle', 'mobile', 'money-collect', 'notification', 'message', 'pause-circle', 'phone', 'picture', 'play-circle', 'play-square', 'pie-chart', 'plus-circle', 'pound-circle', 'plus-square', 'printer', 'project', 'profile', 'property-safety', 'pushpin', 'question-circle', 'reconciliation', 'red-envelope', 'rest', 'right-circle', 'right-square', 'rocket', 'safety-certificate', 'save', 'security-scan', 'schedule', 'setting', 'shop', 'shopping', 'skin', 'sliders', 'snippets', 'star', 'stop', 'switcher', 'tablet', 'tag', 'tags', 'sound', 'tool', 'trophy', 'thunderbolt', 'unlock', 'up-square', 'usb', 'smile', 'video-camera', 'wallet', 'warning', 'trademark-circle', 'up-circle']\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ANT_ICONS, ANT_ICON_ANGULAR_CONSOLE_PREFIX, DynamicLoadingTimeoutError, HttpModuleNotImport, IconDirective, IconModule, IconNotFoundError, IconService, NameSpaceIsNotSpecifyError, SVGTagNotFoundError, UrlNotSafeError, alreadyHasAThemeSuffix, cloneSVG, error, getIconDefinitionFromAbbr, getNameAndNamespace, getSecondaryColor, hasNamespace, isIconDefinition, manifest, mapAbbrToTheme, provideAntIcons, replaceFillColor, warn, withSuffix, withSuffixAndColor };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injectable, numberAttribute, booleanAttribute, Input, Directive, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { Subject, from } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { IconService, IconDirective } from '@ant-design/icons-angular';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { DOCUMENT } from '@angular/common';\nimport { HttpBackend } from '@angular/common/http';\nimport { BarsOutline, CalendarOutline, CaretUpFill, CaretUpOutline, CaretDownFill, CaretDownOutline, CheckCircleFill, CheckCircleOutline, CheckOutline, ClockCircleOutline, CloseCircleOutline, CloseCircleFill, CloseOutline, CopyOutline, DeleteOutline, DoubleLeftOutline, DoubleRightOutline, DownOutline, EditOutline, EllipsisOutline, ExclamationCircleFill, ExclamationCircleOutline, EyeOutline, FileFill, FileOutline, FilterFill, InfoCircleFill, InfoCircleOutline, LeftOutline, LoadingOutline, PaperClipOutline, QuestionCircleOutline, RightOutline, RotateRightOutline, RotateLeftOutline, StarFill, SearchOutline, UploadOutline, VerticalAlignTopOutline, UpOutline, SwapOutline, SwapRightOutline, ZoomInOutline, ZoomOutOutline } from '@ant-design/icons-angular/icons';\nimport * as i1 from '@angular/platform-browser';\nimport * as i2 from 'ng-zorro-antd/core/config';\nimport * as i3 from '@angular/cdk/platform';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NZ_ICONS_USED_BY_ZORRO = [BarsOutline, CalendarOutline, CaretUpFill, CaretUpOutline, CaretDownFill, CaretDownOutline, CheckCircleFill, CheckCircleOutline, CheckOutline, ClockCircleOutline, CloseCircleOutline, CloseCircleFill, CloseOutline, CopyOutline, DeleteOutline, DoubleLeftOutline, DoubleRightOutline, DownOutline, EditOutline, EllipsisOutline, ExclamationCircleFill, ExclamationCircleOutline, EyeOutline, FileFill, FileOutline, FilterFill, InfoCircleFill, InfoCircleOutline, LeftOutline, LoadingOutline, PaperClipOutline, QuestionCircleOutline, RightOutline, RotateRightOutline, RotateLeftOutline, StarFill, SearchOutline, StarFill, UploadOutline, VerticalAlignTopOutline, UpOutline, SwapOutline, SwapRightOutline, ZoomInOutline, ZoomOutOutline];\nconst NZ_ICONS = new InjectionToken('nz_icons');\nconst NZ_ICON_DEFAULT_TWOTONE_COLOR = new InjectionToken('nz_icon_default_twotone_color');\nconst DEFAULT_TWOTONE_COLOR = '#1890ff';\n/**\n * It should be a global singleton, otherwise registered icons could not be found.\n */\nclass NzIconService extends IconService {\n  nzConfigService;\n  platform;\n  configUpdated$ = new Subject();\n  get _disableDynamicLoading() {\n    return !this.platform.isBrowser;\n  }\n  iconfontCache = new Set();\n  subscription = null;\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n      this.subscription = null;\n    }\n  }\n  normalizeSvgElement(svg) {\n    if (!svg.getAttribute('viewBox')) {\n      this._renderer.setAttribute(svg, 'viewBox', '0 0 1024 1024');\n    }\n    if (!svg.getAttribute('width') || !svg.getAttribute('height')) {\n      this._renderer.setAttribute(svg, 'width', '1em');\n      this._renderer.setAttribute(svg, 'height', '1em');\n    }\n    if (!svg.getAttribute('fill')) {\n      this._renderer.setAttribute(svg, 'fill', 'currentColor');\n    }\n  }\n  fetchFromIconfont(opt) {\n    const {\n      scriptUrl\n    } = opt;\n    if (this._document && !this.iconfontCache.has(scriptUrl)) {\n      const script = this._renderer.createElement('script');\n      this._renderer.setAttribute(script, 'src', scriptUrl);\n      this._renderer.setAttribute(script, 'data-namespace', scriptUrl.replace(/^(https?|http):/g, ''));\n      this._renderer.appendChild(this._document.body, script);\n      this.iconfontCache.add(scriptUrl);\n    }\n  }\n  createIconfontIcon(type) {\n    return this._createSVGElementFromString(`<svg><use xlink:href=\"${type}\"></svg>`);\n  }\n  constructor(rendererFactory, sanitizer, nzConfigService, platform) {\n    super(rendererFactory, inject(HttpBackend, {\n      optional: true\n    }),\n    // TODO: fix the type\n    inject(DOCUMENT), sanitizer, [...NZ_ICONS_USED_BY_ZORRO, ...(inject(NZ_ICONS, {\n      optional: true\n    }) || [])]);\n    this.nzConfigService = nzConfigService;\n    this.platform = platform;\n    this.onConfigChange();\n    this.configDefaultTwotoneColor();\n    this.configDefaultTheme();\n  }\n  onConfigChange() {\n    this.subscription = this.nzConfigService.getConfigChangeEventForComponent('icon').subscribe(() => {\n      this.configDefaultTwotoneColor();\n      this.configDefaultTheme();\n      this.configUpdated$.next();\n    });\n  }\n  configDefaultTheme() {\n    const iconConfig = this.getConfig();\n    this.defaultTheme = iconConfig.nzTheme || 'outline';\n  }\n  configDefaultTwotoneColor() {\n    const iconConfig = this.getConfig();\n    const defaultTwotoneColor = iconConfig.nzTwotoneColor || DEFAULT_TWOTONE_COLOR;\n    let primaryColor = DEFAULT_TWOTONE_COLOR;\n    if (defaultTwotoneColor) {\n      if (defaultTwotoneColor.startsWith('#')) {\n        primaryColor = defaultTwotoneColor;\n      } else {\n        warn('Twotone color must be a hex color!');\n      }\n    }\n    this.twoToneColor = {\n      primaryColor\n    };\n  }\n  getConfig() {\n    return this.nzConfigService.getConfigForComponent('icon') || {};\n  }\n  static ɵfac = function NzIconService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzIconService)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(i1.DomSanitizer), i0.ɵɵinject(i2.NzConfigService), i0.ɵɵinject(i3.Platform));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzIconService,\n    factory: NzIconService.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzIconService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.RendererFactory2\n  }, {\n    type: i1.DomSanitizer\n  }, {\n    type: i2.NzConfigService\n  }, {\n    type: i3.Platform\n  }], null);\n})();\nconst NZ_ICONS_PATCH = new InjectionToken('nz_icons_patch');\nclass NzIconPatchService {\n  rootIconService;\n  patched = false;\n  extraIcons = inject(NZ_ICONS_PATCH, {\n    self: true\n  });\n  constructor(rootIconService) {\n    this.rootIconService = rootIconService;\n  }\n  doPatch() {\n    if (this.patched) {\n      return;\n    }\n    this.extraIcons.forEach(iconDefinition => this.rootIconService.addIcon(iconDefinition));\n    this.patched = true;\n  }\n  static ɵfac = function NzIconPatchService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzIconPatchService)(i0.ɵɵinject(NzIconService));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzIconPatchService,\n    factory: NzIconPatchService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzIconPatchService, [{\n    type: Injectable\n  }], () => [{\n    type: NzIconService\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzIconDirective extends IconDirective {\n  ngZone;\n  changeDetectorRef;\n  iconService;\n  renderer;\n  cacheClassName = null;\n  set nzSpin(value) {\n    this.spin = value;\n  }\n  nzRotate = 0;\n  set nzType(value) {\n    this.type = value;\n  }\n  set nzTheme(value) {\n    this.theme = value;\n  }\n  set nzTwotoneColor(value) {\n    this.twoToneColor = value;\n  }\n  set nzIconfont(value) {\n    this.iconfont = value;\n  }\n  hostClass;\n  el;\n  iconfont;\n  spin = false;\n  destroy$ = new Subject();\n  constructor(ngZone, changeDetectorRef, iconService, renderer) {\n    super(iconService);\n    this.ngZone = ngZone;\n    this.changeDetectorRef = changeDetectorRef;\n    this.iconService = iconService;\n    this.renderer = renderer;\n    const iconPatch = inject(NzIconPatchService, {\n      optional: true\n    });\n    if (iconPatch) {\n      iconPatch.doPatch();\n    }\n    this.el = this._elementRef.nativeElement;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzType,\n      nzTwotoneColor,\n      nzSpin,\n      nzTheme,\n      nzRotate\n    } = changes;\n    if (nzType || nzTwotoneColor || nzSpin || nzTheme) {\n      this.changeIcon2();\n    } else if (nzRotate) {\n      this.handleRotate(this.el.firstChild);\n    } else {\n      this._setSVGElement(this.iconService.createIconfontIcon(`#${this.iconfont}`));\n    }\n  }\n  /**\n   * If custom content is provided, try to normalize SVG elements.\n   */\n  ngAfterContentChecked() {\n    if (!this.type) {\n      const children = this.el.children;\n      let length = children.length;\n      if (!this.type && children.length) {\n        while (length--) {\n          const child = children[length];\n          if (child.tagName.toLowerCase() === 'svg') {\n            this.iconService.normalizeSvgElement(child);\n          }\n        }\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n  }\n  /**\n   * Replacement of `changeIcon` for more modifications.\n   */\n  changeIcon2() {\n    this.setClassName();\n    // The Angular zone is left deliberately before the SVG is set\n    // since `_changeIcon` spawns asynchronous tasks as promise and\n    // HTTP calls. This is used to reduce the number of change detections\n    // while the icon is being loaded dynamically.\n    this.ngZone.runOutsideAngular(() => {\n      from(this._changeIcon()).pipe(takeUntil(this.destroy$)).subscribe({\n        next: svgOrRemove => {\n          // Get back into the Angular zone after completing all the tasks.\n          // Since we manually run change detection locally, we have to re-enter\n          // the zone because the change detection might also be run on other local\n          // components, leading them to handle template functions outside of the Angular zone.\n          this.ngZone.run(() => {\n            // The _changeIcon method would call Renderer to remove the element of the old icon,\n            // which would call `markElementAsRemoved` eventually,\n            // so we should call `detectChanges` to tell Angular remove the DOM node.\n            // #7186\n            this.changeDetectorRef.detectChanges();\n            if (svgOrRemove) {\n              this.setSVGData(svgOrRemove);\n              this.handleSpin(svgOrRemove);\n              this.handleRotate(svgOrRemove);\n            }\n          });\n        },\n        error: warn\n      });\n    });\n  }\n  handleSpin(svg) {\n    if (this.spin || this.type === 'loading') {\n      this.renderer.addClass(svg, 'anticon-spin');\n    } else {\n      this.renderer.removeClass(svg, 'anticon-spin');\n    }\n  }\n  handleRotate(svg) {\n    if (this.nzRotate) {\n      this.renderer.setAttribute(svg, 'style', `transform: rotate(${this.nzRotate}deg)`);\n    } else {\n      this.renderer.removeAttribute(svg, 'style');\n    }\n  }\n  setClassName() {\n    if (this.cacheClassName) {\n      this.renderer.removeClass(this.el, this.cacheClassName);\n    }\n    this.cacheClassName = `anticon-${this.type}`;\n    this.renderer.addClass(this.el, this.cacheClassName);\n  }\n  setSVGData(svg) {\n    this.renderer.setAttribute(svg, 'data-icon', this.type);\n    this.renderer.setAttribute(svg, 'aria-hidden', 'true');\n  }\n  static ɵfac = function NzIconDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzIconDirective)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzIconService), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzIconDirective,\n    selectors: [[\"nz-icon\"], [\"\", \"nz-icon\", \"\"]],\n    hostAttrs: [1, \"anticon\"],\n    inputs: {\n      nzSpin: [2, \"nzSpin\", \"nzSpin\", booleanAttribute],\n      nzRotate: [2, \"nzRotate\", \"nzRotate\", numberAttribute],\n      nzType: \"nzType\",\n      nzTheme: \"nzTheme\",\n      nzTwotoneColor: \"nzTwotoneColor\",\n      nzIconfont: \"nzIconfont\"\n    },\n    exportAs: [\"nzIcon\"],\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzIconDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'nz-icon,[nz-icon]',\n      exportAs: 'nzIcon',\n      host: {\n        class: 'anticon'\n      }\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzIconService\n  }, {\n    type: i0.Renderer2\n  }], {\n    nzSpin: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzRotate: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzTheme: [{\n      type: Input\n    }],\n    nzTwotoneColor: [{\n      type: Input\n    }],\n    nzIconfont: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Provide icon definitions for NzIcon in root\n *\n * @param icons Icon definitions\n */\nconst provideNzIcons = icons => {\n  return makeEnvironmentProviders([{\n    provide: NZ_ICONS,\n    useValue: icons\n  }]);\n};\n/**\n * Provide icon definitions for NzIcon in feature module or standalone component\n *\n * @param icons Icon definitions\n */\nconst provideNzIconsPatch = icons => {\n  return [NzIconPatchService, {\n    provide: NZ_ICONS_PATCH,\n    useValue: icons\n  }];\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzIconModule {\n  static forRoot(icons) {\n    return {\n      ngModule: NzIconModule,\n      providers: [provideNzIcons(icons)]\n    };\n  }\n  static forChild(icons) {\n    return {\n      ngModule: NzIconModule,\n      providers: [provideNzIconsPatch(icons)]\n    };\n  }\n  static ɵfac = function NzIconModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzIconModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzIconModule,\n    imports: [NzIconDirective],\n    exports: [NzIconDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzIconModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzIconDirective],\n      exports: [NzIconDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULT_TWOTONE_COLOR, NZ_ICONS, NZ_ICONS_PATCH, NZ_ICONS_USED_BY_ZORRO, NZ_ICON_DEFAULT_TWOTONE_COLOR, NzIconDirective, NzIconModule, NzIconPatchService, NzIconService, provideNzIcons, provideNzIconsPatch };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAI,UAAU;AACd,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AAErB,IAAI,eAAe,CAAC;AAAA,EAClB,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,CAAC;AAGD,SAAS,MAAM,MAAM;AACnB,MAAI,IAAI,KAAK,GACX,IAAI,KAAK,GACT,IAAI,KAAK;AACX,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,SAAO;AAAA,IACL,GAAG,IAAI,IAAI;AAAA,IACX,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AAIA,SAAS,MAAM,OAAO;AACpB,MAAI,IAAI,MAAM,GACZ,IAAI,MAAM,GACV,IAAI,MAAM;AACZ,SAAO,IAAI,OAAO,SAAS,GAAG,GAAG,GAAG,KAAK,CAAC;AAC5C;AAKA,SAAS,IAAI,MAAM,MAAM,QAAQ;AAC/B,MAAI,IAAI,SAAS;AACjB,MAAI,MAAM;AAAA,IACR,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,EAClC;AACA,SAAO;AACT;AACA,SAAS,OAAO,KAAK,GAAG,OAAO;AAC7B,MAAI;AAEJ,MAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK;AACvD,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;AAAA,EAChF,OAAO;AACL,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;AAAA,EAChF;AACA,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT,WAAW,OAAO,KAAK;AACrB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,cAAc,KAAK,GAAG,OAAO;AAEpC,MAAI,IAAI,MAAM,KAAK,IAAI,MAAM,GAAG;AAC9B,WAAO,IAAI;AAAA,EACb;AACA,MAAI;AACJ,MAAI,OAAO;AACT,iBAAa,IAAI,IAAI,iBAAiB;AAAA,EACxC,WAAW,MAAM,gBAAgB;AAC/B,iBAAa,IAAI,IAAI;AAAA,EACvB,OAAO;AACL,iBAAa,IAAI,IAAI,kBAAkB;AAAA,EACzC;AAEA,MAAI,aAAa,GAAG;AAClB,iBAAa;AAAA,EACf;AAEA,MAAI,SAAS,MAAM,mBAAmB,aAAa,KAAK;AACtD,iBAAa;AAAA,EACf;AACA,MAAI,aAAa,MAAM;AACrB,iBAAa;AAAA,EACf;AACA,SAAO,OAAO,WAAW,QAAQ,CAAC,CAAC;AACrC;AACA,SAAS,SAAS,KAAK,GAAG,OAAO;AAC/B,MAAI;AACJ,MAAI,OAAO;AACT,YAAQ,IAAI,IAAI,kBAAkB;AAAA,EACpC,OAAO;AACL,YAAQ,IAAI,IAAI,kBAAkB;AAAA,EACpC;AACA,MAAI,QAAQ,GAAG;AACb,YAAQ;AAAA,EACV;AACA,SAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAChC;AACe,SAAR,SAA0B,OAAO;AACtC,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAChF,MAAI,WAAW,CAAC;AAChB,MAAI,SAAS,WAAW,KAAK;AAC7B,WAAS,IAAI,iBAAiB,IAAI,GAAG,KAAK,GAAG;AAC3C,QAAI,MAAM,MAAM,MAAM;AACtB,QAAI,cAAc,MAAM,WAAW;AAAA,MACjC,GAAG,OAAO,KAAK,GAAG,IAAI;AAAA,MACtB,GAAG,cAAc,KAAK,GAAG,IAAI;AAAA,MAC7B,GAAG,SAAS,KAAK,GAAG,IAAI;AAAA,IAC1B,CAAC,CAAC;AACF,aAAS,KAAK,WAAW;AAAA,EAC3B;AACA,WAAS,KAAK,MAAM,MAAM,CAAC;AAC3B,WAAS,KAAK,GAAG,MAAM,gBAAgB,MAAM,GAAG;AAC9C,QAAI,OAAO,MAAM,MAAM;AACvB,QAAI,eAAe,MAAM,WAAW;AAAA,MAClC,GAAG,OAAO,MAAM,EAAE;AAAA,MAClB,GAAG,cAAc,MAAM,EAAE;AAAA,MACzB,GAAG,SAAS,MAAM,EAAE;AAAA,IACtB,CAAC,CAAC;AACF,aAAS,KAAK,YAAY;AAAA,EAC5B;AAGA,MAAI,KAAK,UAAU,QAAQ;AACzB,WAAO,aAAa,IAAI,SAAU,OAAO;AACvC,UAAI,QAAQ,MAAM,OAChB,UAAU,MAAM;AAClB,UAAI,kBAAkB,MAAM,IAAI,WAAW,KAAK,mBAAmB,SAAS,GAAG,WAAW,SAAS,KAAK,CAAC,GAAG,UAAU,GAAG,CAAC;AAC1H,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,SAAO;AACT;;;ACjJO,IAAI,MAAM,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC9H,IAAI,UAAU,IAAI,CAAC;AACZ,IAAI,UAAU,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAClI,QAAQ,UAAU,QAAQ,CAAC;AACpB,IAAI,SAAS,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACjI,OAAO,UAAU,OAAO,CAAC;AAClB,IAAI,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/H,KAAK,UAAU,KAAK,CAAC;AACd,IAAI,SAAS,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACjI,OAAO,UAAU,OAAO,CAAC;AAClB,IAAI,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/H,KAAK,UAAU,KAAK,CAAC;AACd,IAAI,QAAQ,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAChI,MAAM,UAAU,MAAM,CAAC;AAChB,IAAI,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/H,KAAK,UAAU,KAAK,CAAC;AACd,IAAI,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/H,KAAK,UAAU,KAAK,CAAC;AACd,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,SAAS,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACjI,OAAO,UAAU,OAAO,CAAC;AAClB,IAAI,UAAU,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAClI,QAAQ,UAAU,QAAQ,CAAC;AACpB,IAAI,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/H,KAAK,UAAU,KAAK,CAAC;AAiBd,IAAI,UAAU,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAClI,QAAQ,UAAU,QAAQ,CAAC;AACpB,IAAI,cAAc,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACtI,YAAY,UAAU,YAAY,CAAC;AAC5B,IAAI,aAAa,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACrI,WAAW,UAAU,WAAW,CAAC;AAC1B,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,aAAa,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACrI,WAAW,UAAU,WAAW,CAAC;AAC1B,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,YAAY,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACpI,UAAU,UAAU,UAAU,CAAC;AACxB,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,eAAe,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACvI,aAAa,UAAU,aAAa,CAAC;AAC9B,IAAI,aAAa,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACrI,WAAW,UAAU,WAAW,CAAC;AAC1B,IAAI,cAAc,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACtI,YAAY,UAAU,YAAY,CAAC;AAC5B,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;;;AC3E7B,IAAM,kCAAkC;AACxC,SAAS,MAAM,SAAS;AACtB,UAAQ,MAAM,GAAG,+BAA+B,IAAI,OAAO,GAAG;AAChE;AACA,SAASA,MAAK,SAAS;AACrB,MAAI,UAAU,GAAG;AACf,YAAQ,KAAK,GAAG,+BAA+B,IAAI,OAAO,GAAG;AAAA,EAC/D;AACF;AACA,SAAS,kBAAkB,cAAc;AACvC,SAAO,SAAS,YAAY,EAAE,CAAC;AACjC;AACA,SAAS,WAAWC,OAAM,OAAO;AAC/B,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,aAAO,GAAGA,KAAI;AAAA,IAChB,KAAK;AACH,aAAO,GAAGA,KAAI;AAAA,IAChB,KAAK;AACH,aAAO,GAAGA,KAAI;AAAA,IAChB,KAAK;AACH,aAAOA;AAAA,IACT;AACE,YAAM,IAAI,MAAM,GAAG,+BAA+B,UAAU,KAAK,8BAA8B;AAAA,EACnG;AACF;AACA,SAAS,mBAAmBA,OAAM,OAAO,KAAK,KAAK;AACjD,SAAO,GAAG,WAAWA,OAAM,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG;AACjD;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,SAAS,MAAM,YAAY;AACpC;AACA,SAAS,uBAAuBA,OAAM;AACpC,SAAOA,MAAK,SAAS,OAAO,KAAKA,MAAK,SAAS,IAAI,KAAKA,MAAK,SAAS,UAAU;AAClF;AACA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,OAAO,WAAW,YAAY,OAAO,OAAO,SAAS,aAAa,OAAO,OAAO,UAAU,YAAY,OAAO,UAAU,WAAc,OAAO,OAAO,SAAS;AACrK;AAKA,SAAS,0BAA0B,KAAK;AACtC,QAAM,MAAM,IAAI,MAAM,GAAG;AACzB,QAAM,QAAQ,eAAe,IAAI,OAAO,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC;AAC7D,QAAMA,QAAO,IAAI,KAAK,GAAG;AACzB,SAAO;AAAA,IACL,MAAAA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR;AACF;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,IAAI,UAAU,IAAI;AAC3B;AAIA,SAAS,iBAAiB,KAAK;AAC7B,SAAO,IAAI,QAAQ,iBAAiB,gBAAgB,EAAE,QAAQ,oBAAoB,kBAAkB,EAAE,QAAQ,oBAAoB,kBAAkB,EAAE,QAAQ,oBAAoB,kBAAkB;AACtM;AAIA,SAAS,oBAAoB,MAAM;AACjC,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,UAAQ,MAAM,QAAQ;AAAA,IACpB,KAAK;AACH,aAAO,CAAC,MAAM,EAAE;AAAA,IAClB,KAAK;AACH,aAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,IAC5B;AACE,YAAM,IAAI,MAAM,GAAG,+BAA+B,iBAAiB,IAAI,gBAAgB;AAAA,EAC3F;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,oBAAoB,IAAI,EAAE,CAAC,MAAM;AAC1C;AACA,SAAS,6BAA6B;AACpC,SAAO,IAAI,MAAM,GAAG,+BAA+B,gDAAgD,IAAI,IAAI;AAC7G;AACA,SAAS,kBAAkB,MAAM;AAC/B,SAAO,IAAI,MAAM,GAAG,+BAA+B,YAAY,IAAI,uCAAuC;AAC5G;AACA,SAAS,sBAAsB;AAC7B,QAAM,iEAAiE;AACvE,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,MAAM,GAAG,+BAA+B,YAAY,GAAG,cAAc;AAClF;AACA,SAAS,sBAAsB;AAC7B,SAAO,IAAI,MAAM,GAAG,+BAA+B,sBAAsB;AAC3E;AACA,SAAS,6BAA6B;AACpC,SAAO,IAAI,MAAM,GAAG,+BAA+B,0BAA0B;AAC/E;AACA,IAAM,qBAAqB;AAC3B,IAAM,YAAY,IAAI,eAAe,WAAW;AAChD,IAAM,eAAN,MAAM,aAAY;AAAA,EAChB,IAAI,aAAa;AAAA,IACf;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,qBAAqB,eAAe;AACzC,SAAK,qBAAqB,iBAAiB,kBAAkB,kBAAkB,YAAY;AAAA,EAC7F;AAAA,EACA,IAAI,eAAe;AAEjB,WAAO,mBACF,KAAK;AAAA,EAEZ;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,yBAAyB;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,YAAY,kBAAkB,UAAU,WAAW,WAAW,WAAW;AACvE,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,eAAe;AAIpB,SAAK,kBAAkB,oBAAI,IAAI;AAK/B,SAAK,0BAA0B,oBAAI,IAAI;AACvC,SAAK,qBAAqB,oBAAI,IAAI;AAIlC,SAAK,iBAAiB;AACtB,SAAK,uBAAuB;AAAA,MAC1B,cAAc;AAAA,MACd,gBAAgB;AAAA,IAClB;AAEA,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB,IAAI,QAAQ;AACnC,SAAK,YAAY,KAAK,iBAAiB,eAAe,MAAM,IAAI;AAChE,QAAI,KAAK,UAAU;AACjB,WAAK,QAAQ,IAAI,WAAW,KAAK,QAAQ;AAAA,IAC3C;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,QAAQ,GAAG,KAAK,SAAS;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,sBAAsB;AAC3B,aAAO,kBAAkB,IAAI,UAAQ;AACnC,aAAK,gBAAgB,KAAK,IAAI;AAAA,MAChC;AAAA,IACF,OAAO;AACL,MAAAD,MAAK,sCAAsC;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,QAAQ;AACzB,SAAK,iBAAiB,OAAO,SAAS,GAAG,IAAI,SAAS,SAAS;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,UAAM,QAAQ,UAAQ;AACpB,WAAK,gBAAgB,IAAI,WAAW,KAAK,MAAM,KAAK,KAAK,GAAG,IAAI;AAAA,IAClE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,MAAM,SAAS;AAC5B,UAAM,CAAC,GAAG,SAAS,IAAI,oBAAoB,IAAI;AAC/C,QAAI,CAAC,WAAW;AACd,YAAM,2BAA2B;AAAA,IACnC;AACA,SAAK,QAAQ;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACN,SAAK,gBAAgB,MAAM;AAC3B,SAAK,wBAAwB,MAAM;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,MAAM,cAAc;AAErC,UAAM,aAAa,iBAAiB,IAAI,IAAI,OAAO,KAAK,gBAAgB,IAAI,IAAI,KAAK;AACrF,QAAI,CAAC,cAAc,KAAK,wBAAwB;AAC9C,YAAM,kBAAkB,IAAI;AAAA,IAC9B;AAGA,UAAM,kBAAkB,aAAa,GAAG,UAAU,IAAI,KAAK,qBAAqB,IAAI;AAEpF,WAAO,gBAAgB,KAAK,IAAI,OAAK;AACnC,UAAI,CAAC,GAAG;AACN,cAAM,kBAAkB,IAAI;AAAA,MAC9B;AACA,aAAO,KAAK,6BAA6B,GAAG,YAAY;AAAA,IAC1D,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,MAAM;AAEzB,QAAI,CAAC,KAAK,SAAS,CAAC,KAAK,qBAAqB;AAC5C,aAAO,GAAG,oBAAoB,CAAC;AAAA,IACjC;AAGA,QAAI,aAAa,KAAK,mBAAmB,IAAI,IAAI;AACjD,QAAI,CAAC,YAAY;AACf,YAAM,CAACC,OAAM,SAAS,IAAI,oBAAoB,IAAI;AAElD,YAAM,OAAO,YAAY;AAAA,QACvB,MAAM;AAAA,QACN,MAAM;AAAA,MACR,IAAI,0BAA0BA,KAAI;AAClC,YAAM,SAAS,KAAK,sBAAsB,QAAQ;AAClD,YAAM,OAAO,YAAY,GAAG,KAAK,cAAc,UAAU,SAAS,IAAIA,KAAI,KAAK,GAAG,KAAK,cAAc,UAAU,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM;AAC5I,YAAM,UAAU,KAAK,UAAU,SAAS,gBAAgB,KAAK,GAAG;AAChE,UAAI,CAAC,SAAS;AACZ,cAAM,gBAAgB,GAAG;AAAA,MAC3B;AACA,YAAM,SAAS,CAAC,KAAK,sBAAsB,KAAK,MAAM,IAAI,SAAS;AAAA,QACjE,cAAc;AAAA,MAChB,CAAC,EAAE,KAAK,IAAI,aAAY,iCACnB,OADmB;AAAA,QAEtB,MAAM;AAAA,MACR,EAAE,CAAC,IAAI,KAAK,8BAA8B,MAAM,OAAO;AACvD,mBAAa,OAAO,KAAK,IAAI,gBAAc,KAAK,QAAQ,UAAU,CAAC,GAAG,SAAS,MAAM,KAAK,mBAAmB,OAAO,IAAI,CAAC,GAAG,WAAW,MAAM,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC;AAC/J,WAAK,mBAAmB,IAAI,MAAM,UAAU;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AAAA,EACA,8BAA8B,MAAM,KAAK;AACvC,WAAO,IAAI,WAAW,gBAAc;AAClC,YAAM,SAAS,KAAK,UAAU,cAAc,QAAQ;AACpD,YAAM,QAAQ,WAAW,MAAM;AAC7B,cAAM;AACN,mBAAW,MAAM,2BAA2B,CAAC;AAAA,MAC/C,GAAG,GAAI;AACP,aAAO,MAAM;AACb,eAAS,QAAQ;AACf,eAAO,WAAW,YAAY,MAAM;AACpC,qBAAa,KAAK;AAAA,MACpB;AACA,WAAK,UAAU,KAAK,YAAY,MAAM;AACtC,WAAK,gBAAgB,KAAK,OAAO,OAAK,EAAE,SAAS,KAAK,QAAQ,EAAE,UAAU,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,OAAK;AAC7G,mBAAW,KAAK,CAAC;AACjB,cAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,MAAM,cAAc;AAC/C,QAAI;AACJ,UAAM,MAAM,gBAAgB,KAAK,qBAAqB;AACtD,UAAM,MAAM,kBAAkB,GAAG,KAAK,KAAK,qBAAqB;AAChE,UAAM,MAAM,KAAK,UAAU,YAAY,mBAAmB,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG,IAAI,KAAK,UAAU,SAAY,KAAK,OAAO,WAAW,KAAK,MAAM,KAAK,KAAK;AAEpK,UAAM,SAAS,KAAK,wBAAwB,IAAI,GAAG;AACnD,QAAI,QAAQ;AACV,YAAM,OAAO;AAAA,IACf,OAAO;AACL,YAAM,KAAK,iBAAiB,KAAK;AAAA;AAAA,QAEjC,KAAK,4BAA4B,aAAa,KAAK,IAAI,IAAI,KAAK,OAAO,iBAAiB,KAAK,IAAI,CAAC;AAAA,QAAG,KAAK,UAAU;AAAA,QAAW;AAAA,QAAK;AAAA,MAAG,CAAC;AAExI,WAAK,wBAAwB,IAAI,KAAK,iCACjC,OADiC;AAAA,QAEpC,MAAM;AAAA,MACR,EAAC;AAAA,IACH;AACA,WAAO,SAAS,GAAG;AAAA,EACrB;AAAA,EACA,4BAA4B,KAAK;AAC/B,UAAM,MAAM,KAAK,UAAU,cAAc,KAAK;AAC9C,QAAI,YAAY;AAChB,UAAM,MAAM,IAAI,cAAc,KAAK;AACnC,QAAI,CAAC,KAAK;AACR,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,KAAK;AACpB,SAAK,UAAU,aAAa,KAAK,SAAS,KAAK;AAC/C,SAAK,UAAU,aAAa,KAAK,UAAU,KAAK;AAChD,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,KAAK,SAAS,KAAK,KAAK;AACvC,QAAI,SAAS;AACX,YAAM,WAAW,IAAI;AACrB,YAAM,SAAS,SAAS;AACxB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAM,QAAQ,SAAS,CAAC;AACxB,YAAI,MAAM,aAAa,MAAM,MAAM,kBAAkB;AACnD,eAAK,UAAU,aAAa,OAAO,QAAQ,GAAG;AAAA,QAChD,OAAO;AACL,eAAK,UAAU,aAAa,OAAO,QAAQ,GAAG;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AACA,SAAK,UAAU,aAAa,KAAK,QAAQ,cAAc;AACvD,WAAO;AAAA,EACT;AAaF;AAXI,aAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,SAAO,KAAK,qBAAqB,cAAgB,SAAY,gBAAgB,GAAM,SAAY,aAAa,CAAC,GAAM,SAAS,UAAU,CAAC,GAAM,SAAY,YAAY,GAAM,SAAS,WAAW,CAAC,CAAC;AACnM;AAGA,aAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,aAAY;AAAA,EACrB,YAAY;AACd,CAAC;AA7PL,IAAM,cAAN;AAAA,CAgQC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,UAAU,MAAM,OAAO;AAC9B,SAAO,KAAK,SAAS,MAAM,QAAQ,KAAK,UAAU,MAAM,SAAS,KAAK,iBAAiB,MAAM;AAC/F;AACA,IAAM,iBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,cAAc;AACxB,SAAK,eAAe;AACpB,SAAK,cAAc,OAAO,UAAU;AACpC,SAAK,YAAY,OAAO,SAAS;AAAA,EACnC;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,cAAc;AACzD,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,IAAI,QAAQ,aAAW;AAC5B,UAAI,CAAC,KAAK,MAAM;AACd,aAAK,iBAAiB;AACtB,gBAAQ,IAAI;AACZ;AAAA,MACF;AACA,YAAM,aAAa,KAAK,mBAAmB;AAC3C,WAAK,aAAa,mBAAmB,KAAK,eAAe,KAAK,MAAM,KAAK,KAAK,GAAG,KAAK,YAAY,EAAE,UAAU,SAAO;AAGnH,cAAM,YAAY,KAAK,mBAAmB;AAC1C,YAAI,UAAU,YAAY,SAAS,GAAG;AACpC,eAAK,eAAe,GAAG;AACvB,kBAAQ,GAAG;AAAA,QACb,OAAO;AACL,kBAAQ,IAAI;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,cAAc,KAAK;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,MAAM,OAAO;AAC1B,QAAI,iBAAiB,IAAI,GAAG;AAC1B,aAAO;AAAA,IACT,OAAO;AACL,YAAM,CAACA,OAAM,SAAS,IAAI,oBAAoB,IAAI;AAClD,UAAI,WAAW;AACb,eAAO;AAAA,MACT;AACA,UAAI,uBAAuBA,KAAI,GAAG;AAChC,YAAI,CAAC,CAAC,OAAO;AACX,UAAAD,MAAK,UAAUC,KAAI,2CAA2C,KAAK,mBAAmB;AAAA,QACxF;AACA,eAAOA;AAAA,MACT,OAAO;AACL,eAAO,WAAWA,OAAM,SAAS,KAAK,aAAa,YAAY;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,KAAK;AAClB,SAAK,iBAAiB;AACtB,SAAK,UAAU,YAAY,KAAK,YAAY,eAAe,GAAG;AAAA,EAChE;AAAA,EACA,mBAAmB;AACjB,UAAM,KAAK,KAAK,YAAY;AAC5B,UAAM,WAAW,GAAG;AACpB,UAAM,SAAS,SAAS;AACxB,aAAS,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACpC,YAAM,QAAQ,SAAS,CAAC;AACxB,UAAI,MAAM,SAAS,YAAY,MAAM,OAAO;AAC1C,aAAK,UAAU,YAAY,IAAI,KAAK;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAkBF;AAhBI,eAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,SAAO,KAAK,qBAAqB,gBAAkB,kBAAkB,WAAW,CAAC;AACnF;AAGA,eAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,EAC/B,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,cAAc;AAAA,EAChB;AAAA,EACA,UAAU,CAAI,oBAAoB;AACpC,CAAC;AAjGL,IAAM,gBAAN;AAAA,CAoGC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,cAAN,MAAM,YAAW;AAgBjB;AAdI,YAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,SAAO,KAAK,qBAAqB,aAAY;AAC/C;AAGA,YAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS,CAAC,aAAa;AAAA,EACvB,SAAS,CAAC,aAAa;AACzB,CAAC;AAGD,YAAK,OAAyB,iBAAiB,CAAC,CAAC;AAdrD,IAAM,aAAN;AAAA,CAiBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa;AAAA,MACvB,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACphBH,IAAM,yBAAyB,CAAC,aAAa,iBAAiB,aAAa,gBAAgB,eAAe,kBAAkB,iBAAiB,oBAAoB,cAAc,oBAAoB,oBAAoB,iBAAiB,cAAc,aAAa,eAAe,mBAAmB,oBAAoB,aAAa,aAAa,iBAAiB,uBAAuB,0BAA0B,YAAY,UAAU,aAAa,YAAY,gBAAgB,mBAAmB,aAAa,gBAAgB,kBAAkB,uBAAuB,cAAc,oBAAoB,mBAAmB,UAAU,eAAe,UAAU,eAAe,yBAAyB,WAAW,aAAa,kBAAkB,eAAe,cAAc;AACpvB,IAAM,WAAW,IAAI,eAAe,UAAU;AAC9C,IAAM,gCAAgC,IAAI,eAAe,+BAA+B;AACxF,IAAM,wBAAwB;AAI9B,IAAM,iBAAN,MAAM,uBAAsB,YAAY;AAAA,EACtC;AAAA,EACA;AAAA,EACA,iBAAiB,IAAI,QAAQ;AAAA,EAC7B,IAAI,yBAAyB;AAC3B,WAAO,CAAC,KAAK,SAAS;AAAA,EACxB;AAAA,EACA,gBAAgB,oBAAI,IAAI;AAAA,EACxB,eAAe;AAAA,EACf,cAAc;AACZ,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,YAAY;AAC9B,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,oBAAoB,KAAK;AACvB,QAAI,CAAC,IAAI,aAAa,SAAS,GAAG;AAChC,WAAK,UAAU,aAAa,KAAK,WAAW,eAAe;AAAA,IAC7D;AACA,QAAI,CAAC,IAAI,aAAa,OAAO,KAAK,CAAC,IAAI,aAAa,QAAQ,GAAG;AAC7D,WAAK,UAAU,aAAa,KAAK,SAAS,KAAK;AAC/C,WAAK,UAAU,aAAa,KAAK,UAAU,KAAK;AAAA,IAClD;AACA,QAAI,CAAC,IAAI,aAAa,MAAM,GAAG;AAC7B,WAAK,UAAU,aAAa,KAAK,QAAQ,cAAc;AAAA,IACzD;AAAA,EACF;AAAA,EACA,kBAAkB,KAAK;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,aAAa,CAAC,KAAK,cAAc,IAAI,SAAS,GAAG;AACxD,YAAM,SAAS,KAAK,UAAU,cAAc,QAAQ;AACpD,WAAK,UAAU,aAAa,QAAQ,OAAO,SAAS;AACpD,WAAK,UAAU,aAAa,QAAQ,kBAAkB,UAAU,QAAQ,oBAAoB,EAAE,CAAC;AAC/F,WAAK,UAAU,YAAY,KAAK,UAAU,MAAM,MAAM;AACtD,WAAK,cAAc,IAAI,SAAS;AAAA,IAClC;AAAA,EACF;AAAA,EACA,mBAAmB,MAAM;AACvB,WAAO,KAAK,4BAA4B,yBAAyB,IAAI,UAAU;AAAA,EACjF;AAAA,EACA,YAAY,iBAAiB,WAAW,iBAAiB,UAAU;AACjE;AAAA,MAAM;AAAA,MAAiB,OAAO,aAAa;AAAA,QACzC,UAAU;AAAA,MACZ,CAAC;AAAA;AAAA,MAED,OAAO,QAAQ;AAAA,MAAG;AAAA,MAAW,CAAC,GAAG,wBAAwB,GAAI,OAAO,UAAU;AAAA,QAC5E,UAAU;AAAA,MACZ,CAAC,KAAK,CAAC,CAAE;AAAA,IAAC;AACV,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,0BAA0B;AAC/B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,iBAAiB;AACf,SAAK,eAAe,KAAK,gBAAgB,iCAAiC,MAAM,EAAE,UAAU,MAAM;AAChG,WAAK,0BAA0B;AAC/B,WAAK,mBAAmB;AACxB,WAAK,eAAe,KAAK;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,UAAM,aAAa,KAAK,UAAU;AAClC,SAAK,eAAe,WAAW,WAAW;AAAA,EAC5C;AAAA,EACA,4BAA4B;AAC1B,UAAM,aAAa,KAAK,UAAU;AAClC,UAAM,sBAAsB,WAAW,kBAAkB;AACzD,QAAI,eAAe;AACnB,QAAI,qBAAqB;AACvB,UAAI,oBAAoB,WAAW,GAAG,GAAG;AACvC,uBAAe;AAAA,MACjB,OAAO;AACL,aAAK,oCAAoC;AAAA,MAC3C;AAAA,IACF;AACA,SAAK,eAAe;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AACV,WAAO,KAAK,gBAAgB,sBAAsB,MAAM,KAAK,CAAC;AAAA,EAChE;AASF;AARE,cArFI,gBAqFG,QAAO,SAAS,sBAAsB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,gBAAkB,SAAY,gBAAgB,GAAM,SAAY,YAAY,GAAM,SAAY,eAAe,GAAM,SAAY,QAAQ,CAAC;AAC3K;AACA,cAxFI,gBAwFG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,eAAc;AAAA,EACvB,YAAY;AACd,CAAC;AA5FH,IAAM,gBAAN;AAAA,CA8FC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,iBAAiB,IAAI,eAAe,gBAAgB;AAC1D,IAAM,sBAAN,MAAM,oBAAmB;AAAA,EACvB;AAAA,EACA,UAAU;AAAA,EACV,aAAa,OAAO,gBAAgB;AAAA,IAClC,MAAM;AAAA,EACR,CAAC;AAAA,EACD,YAAY,iBAAiB;AAC3B,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,UAAU;AACR,QAAI,KAAK,SAAS;AAChB;AAAA,IACF;AACA,SAAK,WAAW,QAAQ,oBAAkB,KAAK,gBAAgB,QAAQ,cAAc,CAAC;AACtF,SAAK,UAAU;AAAA,EACjB;AAQF;AAPE,cAhBI,qBAgBG,QAAO,SAAS,2BAA2B,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,qBAAuB,SAAS,aAAa,CAAC;AACjF;AACA,cAnBI,qBAmBG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,oBAAmB;AAC9B,CAAC;AAtBH,IAAM,qBAAN;AAAA,CAwBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,mBAAN,MAAM,yBAAwB,cAAc;AAAA,EAC1C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB,IAAI,OAAO,OAAO;AAChB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW;AAAA,EACX,IAAI,OAAO,OAAO;AAChB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,IAAI,eAAe,OAAO;AACxB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,WAAW,IAAI,QAAQ;AAAA,EACvB,YAAY,QAAQ,mBAAmB,aAAa,UAAU;AAC5D,UAAM,WAAW;AACjB,SAAK,SAAS;AACd,SAAK,oBAAoB;AACzB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,UAAM,YAAY,OAAO,oBAAoB;AAAA,MAC3C,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,WAAW;AACb,gBAAU,QAAQ;AAAA,IACpB;AACA,SAAK,KAAK,KAAK,YAAY;AAAA,EAC7B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU,kBAAkB,UAAU,SAAS;AACjD,WAAK,YAAY;AAAA,IACnB,WAAW,UAAU;AACnB,WAAK,aAAa,KAAK,GAAG,UAAU;AAAA,IACtC,OAAO;AACL,WAAK,eAAe,KAAK,YAAY,mBAAmB,IAAI,KAAK,QAAQ,EAAE,CAAC;AAAA,IAC9E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AACtB,QAAI,CAAC,KAAK,MAAM;AACd,YAAM,WAAW,KAAK,GAAG;AACzB,UAAI,SAAS,SAAS;AACtB,UAAI,CAAC,KAAK,QAAQ,SAAS,QAAQ;AACjC,eAAO,UAAU;AACf,gBAAM,QAAQ,SAAS,MAAM;AAC7B,cAAI,MAAM,QAAQ,YAAY,MAAM,OAAO;AACzC,iBAAK,YAAY,oBAAoB,KAAK;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,aAAa;AAKlB,SAAK,OAAO,kBAAkB,MAAM;AAClC,WAAK,KAAK,YAAY,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU;AAAA,QAChE,MAAM,iBAAe;AAKnB,eAAK,OAAO,IAAI,MAAM;AAKpB,iBAAK,kBAAkB,cAAc;AACrC,gBAAI,aAAa;AACf,mBAAK,WAAW,WAAW;AAC3B,mBAAK,WAAW,WAAW;AAC3B,mBAAK,aAAa,WAAW;AAAA,YAC/B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,WAAW,KAAK;AACd,QAAI,KAAK,QAAQ,KAAK,SAAS,WAAW;AACxC,WAAK,SAAS,SAAS,KAAK,cAAc;AAAA,IAC5C,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,cAAc;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,aAAa,KAAK;AAChB,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,aAAa,KAAK,SAAS,qBAAqB,KAAK,QAAQ,MAAM;AAAA,IACnF,OAAO;AACL,WAAK,SAAS,gBAAgB,KAAK,OAAO;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,KAAK,gBAAgB;AACvB,WAAK,SAAS,YAAY,KAAK,IAAI,KAAK,cAAc;AAAA,IACxD;AACA,SAAK,iBAAiB,WAAW,KAAK,IAAI;AAC1C,SAAK,SAAS,SAAS,KAAK,IAAI,KAAK,cAAc;AAAA,EACrD;AAAA,EACA,WAAW,KAAK;AACd,SAAK,SAAS,aAAa,KAAK,aAAa,KAAK,IAAI;AACtD,SAAK,SAAS,aAAa,KAAK,eAAe,MAAM;AAAA,EACvD;AAmBF;AAlBE,cAvII,kBAuIG,QAAO,SAAS,wBAAwB,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,kBAAoB,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,aAAa,GAAM,kBAAqB,SAAS,CAAC;AACxM;AACA,cA1II,kBA0IG,QAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,SAAS,GAAG,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,EAC5C,WAAW,CAAC,GAAG,SAAS;AAAA,EACxB,QAAQ;AAAA,IACN,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,IAChD,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,IACrD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,YAAY;AAAA,EACd;AAAA,EACA,UAAU,CAAC,QAAQ;AAAA,EACnB,UAAU,CAAI,4BAA+B,oBAAoB;AACnE,CAAC;AAxJH,IAAM,kBAAN;AAAA,CA0JC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAWH,IAAM,iBAAiB,WAAS;AAC9B,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AAMA,IAAM,sBAAsB,WAAS;AACnC,SAAO,CAAC,oBAAoB;AAAA,IAC1B,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACH;AAMA,IAAM,gBAAN,MAAM,cAAa;AAAA,EACjB,OAAO,QAAQ,OAAO;AACpB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,eAAe,KAAK,CAAC;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO,SAAS,OAAO;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,oBAAoB,KAAK,CAAC;AAAA,IACxC;AAAA,EACF;AAUF;AATE,cAbI,eAaG,QAAO,SAAS,qBAAqB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,eAAc;AACjD;AACA,cAhBI,eAgBG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AAAA,EACN,SAAS,CAAC,eAAe;AAAA,EACzB,SAAS,CAAC,eAAe;AAC3B,CAAC;AACD,cArBI,eAqBG,QAAyB,iBAAiB,CAAC,CAAC;AArBrD,IAAM,eAAN;AAAA,CAuBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe;AAAA,MACzB,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["warn", "name"]}