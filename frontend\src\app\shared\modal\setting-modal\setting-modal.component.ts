import { NzButtonModule } from 'ng-zorro-antd/button';
import { CommonModule } from '@angular/common';
import { Component, inject, type OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { CenterHttpService } from '@app/core/http/center.service';

@Component({
    selector: 'app-setting-modal',
    imports: [
        CommonModule,
        FormsModule,
        NzFormModule,
        NzSelectModule,
        NzButtonModule
    ],
    templateUrl: './setting-modal.component.html',
    styleUrls: ['./setting-modal.component.scss']
})
export class SettingModalComponent implements OnInit {
  selected_ip = '';

  IPs: string[] = [];
  
  btnLoading = false;
  readonly #modal = inject(NzModalRef);

  constructor(private centerHttp: CenterHttpService) {}
  ngOnInit(): void { 
    this.centerHttp.getSettings().subscribe(res => {
      if (res.status === 'success') {
        this.IPs = res.data.ip.ipList;
        this.selected_ip = res.data.ip.binding_ip;
      }

    })
  }


  close(result?: any): void {
    this.#modal.destroy(result);
  }

  saveSetting() {
    this.btnLoading = true;
    this.centerHttp
      .setSettings({binding_ip: this.selected_ip}).subscribe(res => {
        this.btnLoading = false;
        if (res.status === 'success') {
          this.close(true);
        }
      })
  }
}
