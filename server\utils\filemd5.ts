import * as crypto from 'crypto';
import * as fs from 'node:fs';

// fileMd5: md5 for files and their options
export function fileMd5(path: string): Promise<string> {
  return new Promise<string>((resolve, reject) => {
    if (!fs.lstatSync(path).isDirectory()) {
      const stream = fs.createReadStream(path);
      const hash = crypto.createHash('md5');
      stream.on('error', function (err) {
        reject(err);
      });

      stream.on('data', function (data) {
        hash.update(data);
      });

      stream.on('close', function () {
        resolve(hash.digest('hex'));
      });
    } else {
      reject(new Error(path + '  - Is a directory'));
    }
  });
}
