import {
  isTemplateRef
} from "./chunk-TMVECAY5.js";
import {
  Directive,
  Input,
  NgModule,
  TemplateRef,
  ViewContainerRef,
  setClassMetadata,
  ɵɵNgOnChangesFeature,
  ɵɵdefineDirective,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject
} from "./chunk-WG6BS3HR.js";
import {
  __publicField
} from "./chunk-R2YX2K3C.js";

// node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-outlet.mjs
var _NzStringTemplateOutletDirective = class _NzStringTemplateOutletDirective {
  viewContainer;
  templateRef;
  embeddedViewRef = null;
  context = new NzStringTemplateOutletContext();
  nzStringTemplateOutletContext = null;
  nzStringTemplateOutlet = null;
  static ngTemplateContextGuard(_dir, _ctx) {
    return true;
  }
  recreateView() {
    this.viewContainer.clear();
    if (isTemplateRef(this.nzStringTemplateOutlet)) {
      this.embeddedViewRef = this.viewContainer.createEmbeddedView(this.nzStringTemplateOutlet, this.nzStringTemplateOutletContext);
    } else {
      this.embeddedViewRef = this.viewContainer.createEmbeddedView(this.templateRef, this.context);
    }
  }
  updateContext() {
    const newCtx = isTemplateRef(this.nzStringTemplateOutlet) ? this.nzStringTemplateOutletContext : this.context;
    const oldCtx = this.embeddedViewRef.context;
    if (newCtx) {
      for (const propName of Object.keys(newCtx)) {
        oldCtx[propName] = newCtx[propName];
      }
    }
  }
  constructor(viewContainer, templateRef) {
    this.viewContainer = viewContainer;
    this.templateRef = templateRef;
  }
  ngOnChanges(changes) {
    const {
      nzStringTemplateOutletContext,
      nzStringTemplateOutlet
    } = changes;
    const shouldRecreateView = () => {
      let shouldOutletRecreate = false;
      if (nzStringTemplateOutlet) {
        shouldOutletRecreate = nzStringTemplateOutlet.firstChange || isTemplateRef(nzStringTemplateOutlet.previousValue) || isTemplateRef(nzStringTemplateOutlet.currentValue);
      }
      const hasContextShapeChanged = (ctxChange) => {
        const prevCtxKeys = Object.keys(ctxChange.previousValue || {});
        const currCtxKeys = Object.keys(ctxChange.currentValue || {});
        if (prevCtxKeys.length === currCtxKeys.length) {
          for (const propName of currCtxKeys) {
            if (prevCtxKeys.indexOf(propName) === -1) {
              return true;
            }
          }
          return false;
        } else {
          return true;
        }
      };
      const shouldContextRecreate = nzStringTemplateOutletContext && hasContextShapeChanged(nzStringTemplateOutletContext);
      return shouldContextRecreate || shouldOutletRecreate;
    };
    if (nzStringTemplateOutlet) {
      this.context.$implicit = nzStringTemplateOutlet.currentValue;
    }
    const recreateView = shouldRecreateView();
    if (recreateView) {
      this.recreateView();
    } else {
      this.updateContext();
    }
  }
};
__publicField(_NzStringTemplateOutletDirective, "ɵfac", function NzStringTemplateOutletDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _NzStringTemplateOutletDirective)(ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(TemplateRef));
});
__publicField(_NzStringTemplateOutletDirective, "ɵdir", ɵɵdefineDirective({
  type: _NzStringTemplateOutletDirective,
  selectors: [["", "nzStringTemplateOutlet", ""]],
  inputs: {
    nzStringTemplateOutletContext: "nzStringTemplateOutletContext",
    nzStringTemplateOutlet: "nzStringTemplateOutlet"
  },
  exportAs: ["nzStringTemplateOutlet"],
  features: [ɵɵNgOnChangesFeature]
}));
var NzStringTemplateOutletDirective = _NzStringTemplateOutletDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzStringTemplateOutletDirective, [{
    type: Directive,
    args: [{
      selector: "[nzStringTemplateOutlet]",
      exportAs: "nzStringTemplateOutlet"
    }]
  }], () => [{
    type: ViewContainerRef
  }, {
    type: TemplateRef
  }], {
    nzStringTemplateOutletContext: [{
      type: Input
    }],
    nzStringTemplateOutlet: [{
      type: Input
    }]
  });
})();
var NzStringTemplateOutletContext = class {
  $implicit;
};
var _NzOutletModule = class _NzOutletModule {
};
__publicField(_NzOutletModule, "ɵfac", function NzOutletModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _NzOutletModule)();
});
__publicField(_NzOutletModule, "ɵmod", ɵɵdefineNgModule({
  type: _NzOutletModule,
  imports: [NzStringTemplateOutletDirective],
  exports: [NzStringTemplateOutletDirective]
}));
__publicField(_NzOutletModule, "ɵinj", ɵɵdefineInjector({}));
var NzOutletModule = _NzOutletModule;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzOutletModule, [{
    type: NgModule,
    args: [{
      imports: [NzStringTemplateOutletDirective],
      exports: [NzStringTemplateOutletDirective]
    }]
  }], null, null);
})();

export {
  NzStringTemplateOutletDirective,
  NzOutletModule
};
//# sourceMappingURL=chunk-PLN5JSQQ.js.map
