import db from './sqlite.db';
import { Sequelize } from 'sequelize';
import fs from 'node:fs';
import path from 'node:path';

jest.setTimeout(10000); // 增加 Jest 的超时时间以处理异步操作

// const MockCenterId = 'center_test';
// const MockProjectId = 'project_test';
// const MockDBPath = path.join(
//   './data/db',
//   `${MockCenterId}_${MockProjectId}.db`,
// );
// jest.mock('@server/config/env', () => ({
//   getUserConfig: jest.fn().mockReturnValue({
//     is_active: true,
//     center_id: 'center_test',
//     project_id: 'project_test',
//   }),
//   data_path: './data/db',
// }));

const MAX_CONCURRENT_OPERATIONS = 5000;

async function insertRecord(sequelize: Sequelize, table: string, data: any) {
  const query = `INSERT INTO ${table} (${Object.keys(data).join(
    ', ',
  )}) VALUES (${Object.values(data)
    .map(() => '?')
    .join(', ')})`;
  await sequelize.query(query, { replacements: Object.values(data) });
}

describe('SQLite Write Concurrency Performance Test', () => {
  beforeAll(async () => {
    await db.open();
    await db.sequelize.query('DROP TABLE IF EXISTS test_table;');

    await db.sequelize.query(`
      CREATE TABLE IF NOT EXISTS test_table (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        value INTEGER
      );
    `);
  });

  afterAll(async () => {
    // await db.sequelize.query('DROP TABLE IF EXISTS test_table;');
    await db.close();
    // fs.unlinkSync(MockDBPath);
  });

  test('should handle concurrent writes efficiently', async () => {
    const insertPromises: Promise<void>[] = [];
    console.time('Concurrent DB Write Operations');

    // 并发写入数据
    for (let i = 0; i < MAX_CONCURRENT_OPERATIONS; i++) {
      insertPromises.push(
        insertRecord(db.sequelize, 'test_table', {
          name: `name_${i + 1}`,
          value: i,
        }),
      );
    }

    // 等待所有并发操作完成
    await Promise.all(insertPromises);
    console.timeEnd('Concurrent DB Write Operations');

    // 断言：检查数据库中是否存在所有插入的数据
    const [result]: any = await db.sequelize.query(
      'SELECT COUNT(*) as count FROM test_table',
    );
    const count = result[0].count;

    // console.log(`Concurrent writes completed count: ${count}`);
    expect(count).toBe(MAX_CONCURRENT_OPERATIONS); // 确保所有数据插入成功
  });

  test('should handle concurrent updates efficiently', async () => {
    const updatePromises: Promise<any>[] = [];
    console.time('Concurrent DB Update Operations');
    // 并发更新数据
    for (let i = 0; i < MAX_CONCURRENT_OPERATIONS; i++) {
      updatePromises.push(
        db.sequelize.query(`UPDATE test_table SET value = ? WHERE id = ?`, {
          replacements: [0, i + 1],
        }),
      );
    }

    // 等待所有并发操作完成
    await Promise.all(updatePromises);
    console.timeEnd('Concurrent DB Update Operations');

    // 断言：检查数据库中是否所有更新的数据被正确更新
    const [result]: any = await db.sequelize.query(
      'SELECT SUM(value) as total FROM test_table',
    );
    const total = result[0].total;

    // console.log(`Concurrent updates completed with total value: ${total}`);
    expect(total).toBe(0); // 检查总值是否符合预期
  });
});
