import { Res } from '@share-types/center.types';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CenterHttpService {
  center_name: string;
  center_address: string;
  constructor(private http: HttpClient) {}

  public getCenterStatus() {
    return this.http.get<Res<ICenterData>>('/data/center');
  }

  public login(data: any) {
    return this.http
      .post<Res<{ center_name: string; center_address: string }>>(
        '/data/login',
        data
      )
      .pipe(
        tap((res) => {
          if (res.status === 'success') {
            const data = res.data;
            this.center_name = data.center_name;
            this.center_address = data.center_address;
          }
        })
      );
  }

  public logout() {
    return this.http.get('/data/logout');
  }

  public register(data: any) {
    return this.http.post<Res<IHttpRegisterInfo>>('/data/register', data);
  }

  public getSchedule(id: number): Observable<any> {
    return this.http.get('/data/schedule');
  }

  public getLogs(): Observable<any> {
    return this.http.get('/data/logs');
  }

  public getSettings(){
    return this.http.get<Res<IHttpSettingInfo>>('/data/settings');
  }

  public setSettings(data: ISetting){
    return this.http.post<Res<void>>('/data/settings', data);
  }
}

interface IHttpSettingInfo {
  ip: {
    binding_ip: string;
    ipList: string[];
  };
}

interface ISetting {
  binding_ip: string;
}


interface IHttpRegisterInfo {
  center_name: string;
  center_address: string;
  projects: {
    formal: IProjectData;
    test: IProjectData;
  };
}
interface IProjectData {
  project_id: string;
  name: string;
  start: string;
  end: string;
}

interface ICenterData {
  center_id: string;
  center_name: string;
  center_address: string;
  project_id: string;
  project_name: string;
}
