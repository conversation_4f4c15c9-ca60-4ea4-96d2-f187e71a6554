import {
  NzIconDirective,
  NzIconModule
} from "./chunk-IVIEPG2R.js";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Injectable,
  Input,
  NgModule,
  ViewEncapsulation,
  setClassMetadata,
  ɵɵNgOnChangesFeature,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate
} from "./chunk-WG6BS3HR.js";
import {
  BehaviorSubject,
  ReplaySubject
} from "./chunk-MGFUBWXT.js";
import {
  __publicField
} from "./chunk-R2YX2K3C.js";

// node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-form.mjs
function NzFormItemFeedbackIconComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "nz-icon", 0);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("nzType", ctx_r0.iconType);
  }
}
var _NzFormStatusService = class _NzFormStatusService {
  formStatusChanges = new ReplaySubject(1);
};
__publicField(_NzFormStatusService, "ɵfac", function NzFormStatusService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _NzFormStatusService)();
});
__publicField(_NzFormStatusService, "ɵprov", ɵɵdefineInjectable({
  token: _NzFormStatusService,
  factory: _NzFormStatusService.ɵfac
}));
var NzFormStatusService = _NzFormStatusService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzFormStatusService, [{
    type: Injectable
  }], null, null);
})();
var _NzFormNoStatusService = class _NzFormNoStatusService {
  noFormStatus = new BehaviorSubject(false);
};
__publicField(_NzFormNoStatusService, "ɵfac", function NzFormNoStatusService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _NzFormNoStatusService)();
});
__publicField(_NzFormNoStatusService, "ɵprov", ɵɵdefineInjectable({
  token: _NzFormNoStatusService,
  factory: _NzFormNoStatusService.ɵfac
}));
var NzFormNoStatusService = _NzFormNoStatusService;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzFormNoStatusService, [{
    type: Injectable
  }], null, null);
})();
var iconTypeMap = {
  error: "close-circle-fill",
  validating: "loading",
  success: "check-circle-fill",
  warning: "exclamation-circle-fill"
};
var _NzFormItemFeedbackIconComponent = class _NzFormItemFeedbackIconComponent {
  cdr;
  status = "";
  constructor(cdr) {
    this.cdr = cdr;
  }
  iconType = null;
  ngOnChanges(_changes) {
    this.updateIcon();
  }
  updateIcon() {
    this.iconType = this.status ? iconTypeMap[this.status] : null;
    this.cdr.markForCheck();
  }
};
__publicField(_NzFormItemFeedbackIconComponent, "ɵfac", function NzFormItemFeedbackIconComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _NzFormItemFeedbackIconComponent)(ɵɵdirectiveInject(ChangeDetectorRef));
});
__publicField(_NzFormItemFeedbackIconComponent, "ɵcmp", ɵɵdefineComponent({
  type: _NzFormItemFeedbackIconComponent,
  selectors: [["nz-form-item-feedback-icon"]],
  hostAttrs: [1, "ant-form-item-feedback-icon"],
  hostVars: 8,
  hostBindings: function NzFormItemFeedbackIconComponent_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("ant-form-item-feedback-icon-error", ctx.status === "error")("ant-form-item-feedback-icon-warning", ctx.status === "warning")("ant-form-item-feedback-icon-success", ctx.status === "success")("ant-form-item-feedback-icon-validating", ctx.status === "validating");
    }
  },
  inputs: {
    status: "status"
  },
  exportAs: ["nzFormFeedbackIcon"],
  features: [ɵɵNgOnChangesFeature],
  decls: 1,
  vars: 1,
  consts: [[3, "nzType"]],
  template: function NzFormItemFeedbackIconComponent_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵtemplate(0, NzFormItemFeedbackIconComponent_Conditional_0_Template, 1, 1, "nz-icon", 0);
    }
    if (rf & 2) {
      ɵɵconditional(ctx.iconType ? 0 : -1);
    }
  },
  dependencies: [NzIconModule, NzIconDirective],
  encapsulation: 2,
  changeDetection: 0
}));
var NzFormItemFeedbackIconComponent = _NzFormItemFeedbackIconComponent;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzFormItemFeedbackIconComponent, [{
    type: Component,
    args: [{
      selector: "nz-form-item-feedback-icon",
      exportAs: "nzFormFeedbackIcon",
      imports: [NzIconModule],
      preserveWhitespaces: false,
      encapsulation: ViewEncapsulation.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `
    @if (iconType) {
      <nz-icon [nzType]="iconType" />
    }
  `,
      host: {
        class: "ant-form-item-feedback-icon",
        "[class.ant-form-item-feedback-icon-error]": 'status==="error"',
        "[class.ant-form-item-feedback-icon-warning]": 'status==="warning"',
        "[class.ant-form-item-feedback-icon-success]": 'status==="success"',
        "[class.ant-form-item-feedback-icon-validating]": 'status==="validating"'
      }
    }]
  }], () => [{
    type: ChangeDetectorRef
  }], {
    status: [{
      type: Input
    }]
  });
})();
var _NzFormPatchModule = class _NzFormPatchModule {
};
__publicField(_NzFormPatchModule, "ɵfac", function NzFormPatchModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _NzFormPatchModule)();
});
__publicField(_NzFormPatchModule, "ɵmod", ɵɵdefineNgModule({
  type: _NzFormPatchModule,
  imports: [NzFormItemFeedbackIconComponent],
  exports: [NzFormItemFeedbackIconComponent]
}));
__publicField(_NzFormPatchModule, "ɵinj", ɵɵdefineInjector({
  imports: [NzFormItemFeedbackIconComponent]
}));
var NzFormPatchModule = _NzFormPatchModule;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzFormPatchModule, [{
    type: NgModule,
    args: [{
      imports: [NzFormItemFeedbackIconComponent],
      exports: [NzFormItemFeedbackIconComponent]
    }]
  }], null, null);
})();

export {
  NzFormStatusService,
  NzFormNoStatusService,
  NzFormItemFeedbackIconComponent
};
//# sourceMappingURL=chunk-Q7SCY6PA.js.map
