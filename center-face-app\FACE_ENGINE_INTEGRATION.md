# 人脸识别引擎集成文档

## 概述

本项目已成功集成了 Human.js 人脸识别库，现在支持在 MediaPipe (OpenCV) 和 Human.js 两种人脸识别引擎之间动态切换。

## 新增功能

### 1. 双引擎支持
- **MediaPipe (OpenCV)**: 原有的人脸识别引擎，基于 OpenCV 和 Haar 级联分类器
- **Human.js**: 新集成的现代人脸识别引擎，基于深度学习模型

### 2. 动态切换机制
- 用户可以在运行时通过设置菜单切换人脸识别引擎
- 切换过程无需重启应用，实现热切换
- 自动保存用户的引擎选择偏好

### 3. 统一接口
- 两种引擎使用相同的接口规范
- 保持现有的错误处理和状态管理机制
- 支持相同的检测选项（居中检测、大小检测、多人脸检测）

## 技术实现

### 核心文件

1. **face-detect-base.service.ts**: 定义了人脸检测服务的基础接口
2. **human-face-detect.service.ts**: Human.js 引擎的实现
3. **face-detect-factory.service.ts**: 引擎工厂服务，负责引擎切换
4. **settings.service.ts**: 扩展了设置服务以支持引擎选择

### 架构设计

```
FaceDetectBaseService (抽象基类)
├── FaceDetectService (MediaPipe 实现)
└── HumanFaceDetectService (Human.js 实现)

FaceDetectFactoryService (工厂服务)
├── 管理引擎实例
├── 处理引擎切换
└── 维护当前状态
```

## 使用方法

### 用户界面操作

1. 在人脸签到页面，点击左上角的设置图标
2. 在下拉菜单中找到"识别引擎"选项
3. 选择所需的引擎：
   - MediaPipe (OpenCV): 传统的人脸识别引擎
   - Human.js: 现代深度学习引擎
4. 系统会自动切换引擎并保存设置

### 引擎状态显示

- 在提示信息区域会显示当前使用的引擎名称
- 设置菜单中会标记当前选中的引擎

## 配置选项

### Human.js 配置

```typescript
const config = {
  modelBasePath: 'https://cdn.jsdelivr.net/npm/@vladmandic/human/models/',
  face: {
    enabled: true,
    detector: { enabled: true, rotation: false },
    mesh: { enabled: false },
    iris: { enabled: false },
    description: { enabled: false },
    emotion: { enabled: false },
    antispoof: { enabled: false },
    liveness: { enabled: false }
  },
  // ... 其他配置
};
```

### 默认设置

- 默认引擎: MediaPipe (OpenCV)
- 自动保存用户选择
- 支持引擎可用性检测

## 性能对比

### MediaPipe (OpenCV)
- **优点**: 轻量级，启动快，兼容性好
- **缺点**: 检测精度相对较低，对光照敏感
- **适用场景**: 资源受限环境，快速检测需求

### Human.js
- **优点**: 检测精度高，鲁棒性强，支持多种人脸特征
- **缺点**: 模型较大，初始化时间较长
- **适用场景**: 高精度要求，网络条件良好的环境

## 故障排除

### 常见问题

1. **Human.js 初始化失败**
   - 检查网络连接，确保能访问 CDN
   - 查看浏览器控制台错误信息
   - 尝试切换回 MediaPipe 引擎

2. **引擎切换失败**
   - 检查浏览器兼容性
   - 确保有足够的内存资源
   - 刷新页面重试

3. **检测精度问题**
   - 调整检测框大小
   - 改善光照条件
   - 尝试不同的引擎

### 调试信息

启用调试模式可以查看详细的引擎状态和错误信息：
- 在 URL 中添加 `?debug=1` 参数
- 查看右侧的调试日志面板

## 开发指南

### 添加新引擎

1. 继承 `FaceDetectBaseService` 基类
2. 实现所有抽象方法
3. 在 `FaceDetectFactoryService` 中注册新引擎
4. 更新用户界面选项

### 自定义配置

可以通过修改各引擎服务中的配置对象来调整检测参数：
- 检测阈值
- 模型路径
- 性能优化选项

## 依赖管理

### 新增依赖
- `@vladmandic/human`: Human.js 人脸识别库

### 构建配置
- 已在 `angular.json` 中添加 Human.js 到 `allowedCommonJsDependencies`
- 支持动态导入以优化初始加载时间

## 未来扩展

### 计划功能
1. 更多人脸识别引擎支持
2. 引擎性能监控
3. 自动引擎选择（基于设备性能）
4. 离线模型支持

### 贡献指南
欢迎提交 Issue 和 Pull Request 来改进人脸识别功能。
