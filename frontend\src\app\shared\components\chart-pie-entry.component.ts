import { CommonModule } from '@angular/common';
import { Component, Input, type OnInit } from '@angular/core';
import { NgxEchartsDirective, provideEchartsCore } from 'ngx-echarts';
import { EChartsOption } from 'echarts';
import { IHttpEntryStat } from '../../core/http/exam.service';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import { GridComponent, LegendComponent, TooltipComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { TitleComponent } from 'echarts/components';
echarts.use([Pie<PERSON>hart, GridComponent, CanvasRenderer, TitleComponent, LegendComponent, TooltipComponent]);

@Component({
    selector: 'app-chart-pie-entry',
    imports: [CommonModule, NgxEchartsDirective],
    providers: [provideEchartsCore({ echarts })],
    template: `<div
    echarts
    [options]="chartOption"
    [merge]="mergeOption"
    style="height: 200px; min-width: 400px"
  ></div>`,
    styles: [
        `
      :host {
        display: inline-block;
      }
    `,
    ]
})
export class ChartPieEntryComponent implements OnInit {
  chartOption: EChartsOption = {
    series: [
      {
        name: '考生统计',
        type: 'pie',
        label: {
          show: true,
          formatter: (params: any) => `${params.name} ${params.value} 人`,
        },
        labelLine: {
          show: true,
        },
        itemStyle: {
          color(params) {
            const colorMap: { [key: string]: string } = {
              '未入场': '#9da6b6', 
              '已签到': '#ffcc2f', 
              '已登录': '#878efe', 
              '考试中': '#5786ff', 
              '已完成': '#73d391', 
              '缺考': '#fd6464',
            };
            return colorMap[params.name] || '#505369';
          },
        }
      },
    ],
  };
  mergeOption: EChartsOption = {};
  _data: any = [];
  total: any = 0;
  @Input() set data(data: IHttpEntryStat) {
    this._data = [
      { value: data.not_enter, name: '未入场' },
      { value: data.register, name: '已签到' },
      { value: data.login, name: '已登录' },
      { value: data.testing, name: '考试中' },
      { value: data.finished, name: '已完成' },
      { value: data.absent, name: '缺考' },
    ];

    this.total = Object.values(data).reduce(
      (sum: any, item: any) => sum + item.value,
      0
    );

    this.mergeOption = {
      series: [
        {
          data: this._data,
        },
      ],
    };
  }
  ngOnInit(): void {}
}
