import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Res } from '@share-types/center.types';

@Injectable({
  providedIn: 'root',
})
export class RoomHttpService {
  constructor(private http: HttpClient) {}

  public register(data: any): Observable<any> {
    return this.http.post('/register', data);
  }

  public getAllRooms() {
    return this.http.get<Res<IHTTPRoom[]>>('/data/rooms');
  }

  public removeRoom(sn_codes: string[]) {
    return this.http.post<Res<string[]>>(`/data/rooms/delete`, { sn_codes });
  }
}

export interface IHTTPRoom {
  id: string;
  name: string;
  sn_code: string;
  host: string;
  address: string;
  app_version: string;
  is_online: boolean;
}
