{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@angular+core@19.2.9_rxjs@7.8.1_zone.js@0.15.0/node_modules/@angular/core/fesm2022/rxjs-interop.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, ɵRuntimeError as _RuntimeError, ɵgetOutputDestroyRef as _getOutputDestroyRef, Injector, effect, untracked, ɵmicrotaskEffect as _microtaskEffect, assertNotInReactiveContext, signal, computed, PendingTasks, resource } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi\n */\nfunction takeUntilDestroyed(destroyRef) {\n  if (!destroyRef) {\n    assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n  const destroyed$ = new Observable(observer => {\n    const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n    return unregisterFn;\n  });\n  return source => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n  source;\n  destroyed = false;\n  destroyRef = inject(DestroyRef);\n  constructor(source) {\n    this.source = source;\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  subscribe(callbackFn) {\n    if (this.destroyed) {\n      throw new _RuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: value => callbackFn(value)\n    });\n    return {\n      unsubscribe: () => subscription.unsubscribe()\n    };\n  }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi\n */\nfunction outputFromObservable(observable, opts) {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi\n */\nfunction outputToObservable(ref) {\n  const destroyRef = _getOutputDestroyRef(ref);\n  return new Observable(observer => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    destroyRef?.onDestroy(() => observer.complete());\n    const subscription = ref.subscribe(v => observer.next(v));\n    return () => subscription.unsubscribe();\n  });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = effect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\nfunction toObservableMicrotask(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = _microtaskEffect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n  typeof ngDevMode !== 'undefined' && ngDevMode && assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' + 'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n  const requiresCleanup = !options?.manualCleanup;\n  requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n  const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n  const equal = makeToSignalEqual(options?.equal);\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({\n      kind: 0 /* StateKind.NoValue */\n    }, {\n      equal\n    });\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal({\n      kind: 1 /* StateKind.Value */,\n      value: options?.initialValue\n    }, {\n      equal\n    });\n  }\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({\n      kind: 1 /* StateKind.Value */,\n      value\n    }),\n    error: error => {\n      if (options?.rejectErrors) {\n        // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n        // the error to end up as an uncaught exception.\n        throw error;\n      }\n      state.set({\n        kind: 2 /* StateKind.Error */,\n        error\n      });\n    }\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n  if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n    throw new _RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n  // Unsubscribe when the current context is destroyed, if requested.\n  cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case 1 /* StateKind.Value */:\n        return current.value;\n      case 2 /* StateKind.Error */:\n        throw current.error;\n      case 0 /* StateKind.NoValue */:\n        // This shouldn't really happen because the error is thrown on creation.\n        throw new _RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  }, {\n    equal: options?.equal\n  });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n  return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @experimental\n */\nfunction pendingUntilEvent(injector) {\n  if (injector === undefined) {\n    assertInInjectionContext(pendingUntilEvent);\n    injector = inject(Injector);\n  }\n  const taskService = injector.get(PendingTasks);\n  return sourceObservable => {\n    return new Observable(originalSubscriber => {\n      // create a new task on subscription\n      const removeTask = taskService.add();\n      let cleanedUp = false;\n      function cleanupTask() {\n        if (cleanedUp) {\n          return;\n        }\n        removeTask();\n        cleanedUp = true;\n      }\n      const innerSubscription = sourceObservable.subscribe({\n        next: v => {\n          originalSubscriber.next(v);\n          cleanupTask();\n        },\n        complete: () => {\n          originalSubscriber.complete();\n          cleanupTask();\n        },\n        error: e => {\n          originalSubscriber.error(e);\n          cleanupTask();\n        }\n      });\n      innerSubscription.add(() => {\n        originalSubscriber.unsubscribe();\n        cleanupTask();\n      });\n      return innerSubscription;\n    });\n  };\n}\nfunction rxResource(opts) {\n  opts?.injector || assertInInjectionContext(rxResource);\n  return resource({\n    ...opts,\n    loader: undefined,\n    stream: params => {\n      let sub;\n      // Track the abort listener so it can be removed if the Observable completes (as a memory\n      // optimization).\n      const onAbort = () => sub.unsubscribe();\n      params.abortSignal.addEventListener('abort', onAbort);\n      // Start off stream as undefined.\n      const stream = signal({\n        value: undefined\n      });\n      let resolve;\n      const promise = new Promise(r => resolve = r);\n      function send(value) {\n        stream.set(value);\n        resolve?.(stream);\n        resolve = undefined;\n      }\n      sub = opts.loader(params).subscribe({\n        next: value => send({\n          value\n        }),\n        error: error => send({\n          error\n        }),\n        complete: () => {\n          if (resolve) {\n            send({\n              error: new Error('Resource completed before producing a value')\n            });\n          }\n          params.abortSignal.removeEventListener('abort', onAbort);\n        }\n      });\n      return promise;\n    }\n  });\n}\nexport { outputFromObservable, outputToObservable, pendingUntilEvent, rxResource, takeUntilDestroyed, toObservable, toSignal, toObservableMicrotask as ɵtoObservableMicrotask };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,SAAS,mBAAmB,YAAY;AACtC,MAAI,CAAC,YAAY;AACf,6BAAyB,kBAAkB;AAC3C,iBAAa,OAAO,UAAU;AAAA,EAChC;AACA,QAAM,aAAa,IAAI,WAAW,cAAY;AAC5C,UAAM,eAAe,WAAW,UAAU,SAAS,KAAK,KAAK,QAAQ,CAAC;AACtE,WAAO;AAAA,EACT,CAAC;AACD,SAAO,YAAU;AACf,WAAO,OAAO,KAAK,UAAU,UAAU,CAAC;AAAA,EAC1C;AACF;AAQA,IAAM,0BAAN,MAA8B;AAAA,EAC5B;AAAA,EACA,YAAY;AAAA,EACZ,aAAa,OAAO,UAAU;AAAA,EAC9B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,WAAW,UAAU,MAAM;AAC9B,WAAK,YAAY;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EACA,UAAU,YAAY;AACpB,QAAI,KAAK,WAAW;AAClB,YAAM,IAAI,aAAc,KAAkD,aAAa,gGAAqG;AAAA,IAC9L;AAEA,UAAM,eAAe,KAAK,OAAO,KAAK,mBAAmB,KAAK,UAAU,CAAC,EAAE,UAAU;AAAA,MACnF,MAAM,WAAS,WAAW,KAAK;AAAA,IACjC,CAAC;AACD,WAAO;AAAA,MACL,aAAa,MAAM,aAAa,YAAY;AAAA,IAC9C;AAAA,EACF;AACF;AAyBA,SAAS,qBAAqB,YAAY,MAAM;AAC9C,eAAa,yBAAyB,oBAAoB;AAC1D,SAAO,IAAI,wBAAwB,UAAU;AAC/C;AAUA,SAAS,mBAAmB,KAAK;AAC/B,QAAM,aAAa,oBAAqB,GAAG;AAC3C,SAAO,IAAI,WAAW,cAAY;AAIhC,gBAAY,UAAU,MAAM,SAAS,SAAS,CAAC;AAC/C,UAAM,eAAe,IAAI,UAAU,OAAK,SAAS,KAAK,CAAC,CAAC;AACxD,WAAO,MAAM,aAAa,YAAY;AAAA,EACxC,CAAC;AACH;AAWA,SAAS,aAAa,QAAQ,SAAS;AACrC,GAAC,SAAS,YAAY,yBAAyB,YAAY;AAC3D,QAAM,WAAW,SAAS,YAAY,OAAO,QAAQ;AACrD,QAAM,UAAU,IAAI,cAAc,CAAC;AACnC,QAAM,UAAU,OAAO,MAAM;AAC3B,QAAI;AACJ,QAAI;AACF,cAAQ,OAAO;AAAA,IACjB,SAAS,KAAK;AACZ,gBAAU,MAAM,QAAQ,MAAM,GAAG,CAAC;AAClC;AAAA,IACF;AACA,cAAU,MAAM,QAAQ,KAAK,KAAK,CAAC;AAAA,EACrC,GAAG;AAAA,IACD;AAAA,IACA,eAAe;AAAA,EACjB,CAAC;AACD,WAAS,IAAI,UAAU,EAAE,UAAU,MAAM;AACvC,YAAQ,QAAQ;AAChB,YAAQ,SAAS;AAAA,EACnB,CAAC;AACD,SAAO,QAAQ,aAAa;AAC9B;AACA,SAAS,sBAAsB,QAAQ,SAAS;AAC9C,GAAC,SAAS,YAAY,yBAAyB,YAAY;AAC3D,QAAM,WAAW,SAAS,YAAY,OAAO,QAAQ;AACrD,QAAM,UAAU,IAAI,cAAc,CAAC;AACnC,QAAM,UAAU,gBAAiB,MAAM;AACrC,QAAI;AACJ,QAAI;AACF,cAAQ,OAAO;AAAA,IACjB,SAAS,KAAK;AACZ,gBAAU,MAAM,QAAQ,MAAM,GAAG,CAAC;AAClC;AAAA,IACF;AACA,cAAU,MAAM,QAAQ,KAAK,KAAK,CAAC;AAAA,EACrC,GAAG;AAAA,IACD;AAAA,IACA,eAAe;AAAA,EACjB,CAAC;AACD,WAAS,IAAI,UAAU,EAAE,UAAU,MAAM;AACvC,YAAQ,QAAQ;AAChB,YAAQ,SAAS;AAAA,EACnB,CAAC;AACD,SAAO,QAAQ,aAAa;AAC9B;AA0BA,SAAS,SAAS,QAAQ,SAAS;AACjC,SAAO,cAAc,eAAe,aAAa,2BAA2B,UAAU,6JAAkK;AACxP,QAAM,kBAAkB,CAAC,SAAS;AAClC,qBAAmB,CAAC,SAAS,YAAY,yBAAyB,QAAQ;AAC1E,QAAM,aAAa,kBAAkB,SAAS,UAAU,IAAI,UAAU,KAAK,OAAO,UAAU,IAAI;AAChG,QAAM,QAAQ,kBAAkB,SAAS,KAAK;AAG9C,MAAI;AACJ,MAAI,SAAS,aAAa;AAExB,YAAQ,OAAO;AAAA,MACb,MAAM;AAAA;AAAA,IACR,GAAG;AAAA,MACD;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AAEL,YAAQ,OAAO;AAAA,MACb,MAAM;AAAA,MACN,OAAO,SAAS;AAAA,IAClB,GAAG;AAAA,MACD;AAAA,IACF,CAAC;AAAA,EACH;AAOA,QAAM,MAAM,OAAO,UAAU;AAAA,IAC3B,MAAM,WAAS,MAAM,IAAI;AAAA,MACvB,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,IACD,OAAO,WAAS;AACd,UAAI,SAAS,cAAc;AAGzB,cAAM;AAAA,MACR;AACA,YAAM,IAAI;AAAA,QACR,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;AAAA,EAGF,CAAC;AACD,MAAI,SAAS,eAAe,MAAM,EAAE,SAAS,GAA2B;AACtE,UAAM,IAAI,aAAc,MAA6D,OAAO,cAAc,eAAe,cAAc,qFAAqF;AAAA,EAC9N;AAEA,cAAY,UAAU,IAAI,YAAY,KAAK,GAAG,CAAC;AAG/C,SAAO,SAAS,MAAM;AACpB,UAAM,UAAU,MAAM;AACtB,YAAQ,QAAQ,MAAM;AAAA,MACpB,KAAK;AACH,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH,cAAM,QAAQ;AAAA,MAChB,KAAK;AAEH,cAAM,IAAI,aAAc,MAA6D,OAAO,cAAc,eAAe,cAAc,qFAAqF;AAAA,IAChO;AAAA,EACF,GAAG;AAAA,IACD,OAAO,SAAS;AAAA,EAClB,CAAC;AACH;AACA,SAAS,kBAAkB,eAAe,OAAO,IAAI;AACnD,SAAO,CAAC,GAAG,MAAM,EAAE,SAAS,KAA2B,EAAE,SAAS,KAA2B,aAAa,EAAE,OAAO,EAAE,KAAK;AAC5H;AAWA,SAAS,kBAAkB,UAAU;AACnC,MAAI,aAAa,QAAW;AAC1B,6BAAyB,iBAAiB;AAC1C,eAAW,OAAO,QAAQ;AAAA,EAC5B;AACA,QAAM,cAAc,SAAS,IAAI,YAAY;AAC7C,SAAO,sBAAoB;AACzB,WAAO,IAAI,WAAW,wBAAsB;AAE1C,YAAM,aAAa,YAAY,IAAI;AACnC,UAAI,YAAY;AAChB,eAAS,cAAc;AACrB,YAAI,WAAW;AACb;AAAA,QACF;AACA,mBAAW;AACX,oBAAY;AAAA,MACd;AACA,YAAM,oBAAoB,iBAAiB,UAAU;AAAA,QACnD,MAAM,OAAK;AACT,6BAAmB,KAAK,CAAC;AACzB,sBAAY;AAAA,QACd;AAAA,QACA,UAAU,MAAM;AACd,6BAAmB,SAAS;AAC5B,sBAAY;AAAA,QACd;AAAA,QACA,OAAO,OAAK;AACV,6BAAmB,MAAM,CAAC;AAC1B,sBAAY;AAAA,QACd;AAAA,MACF,CAAC;AACD,wBAAkB,IAAI,MAAM;AAC1B,2BAAmB,YAAY;AAC/B,oBAAY;AAAA,MACd,CAAC;AACD,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AACA,SAAS,WAAW,MAAM;AACxB,QAAM,YAAY,yBAAyB,UAAU;AACrD,SAAO,SAAS,iCACX,OADW;AAAA,IAEd,QAAQ;AAAA,IACR,QAAQ,YAAU;AAChB,UAAI;AAGJ,YAAM,UAAU,MAAM,IAAI,YAAY;AACtC,aAAO,YAAY,iBAAiB,SAAS,OAAO;AAEpD,YAAM,SAAS,OAAO;AAAA,QACpB,OAAO;AAAA,MACT,CAAC;AACD,UAAI;AACJ,YAAM,UAAU,IAAI,QAAQ,OAAK,UAAU,CAAC;AAC5C,eAAS,KAAK,OAAO;AACnB,eAAO,IAAI,KAAK;AAChB,kBAAU,MAAM;AAChB,kBAAU;AAAA,MACZ;AACA,YAAM,KAAK,OAAO,MAAM,EAAE,UAAU;AAAA,QAClC,MAAM,WAAS,KAAK;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,QACD,OAAO,WAAS,KAAK;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,QACD,UAAU,MAAM;AACd,cAAI,SAAS;AACX,iBAAK;AAAA,cACH,OAAO,IAAI,MAAM,6CAA6C;AAAA,YAChE,CAAC;AAAA,UACH;AACA,iBAAO,YAAY,oBAAoB,SAAS,OAAO;AAAA,QACzD;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAAA,EACF,EAAC;AACH;", "names": []}