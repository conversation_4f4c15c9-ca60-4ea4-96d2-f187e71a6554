import { ICenterServer, JoyShell } from "@share-types/joyshell.types";

declare global {
  let joyshell: JoyShell;
}

export async function startServer() {
  console.info("Starting server ... ");

  const svr = require('./server.js');
  const app: ICenterServer = new svr.Server();

  app.start().catch(err => {
    console.error(err);
    process.exit(1);
  });
  joyshell.Send("ipc-server-started");
  app.on("error", (err) => {
    joyshell.Send("ipc-server-error", err);
  })
}

joyshell.WhenReady().then(() => {
  startServer();
});

export default startServer;