export interface Room {
  name: string;
  address: string;
  code: string;
  sn_code: string;
  venue_name: string;
  venue_address: string;
}

export interface Session {
  id: string;
  name: string;
  schedule_id: string;
  project_id: string;
  project: string;
  start: string;
  end: string;
  test: number;
  room_name: string;
  room_sn: string;
  seat_count: number;
  version: number;
}

export interface Subject {
  id: string;
  code: string;
  name: string;
  notice: {
    title: string;
    text: string;
  };
}

export interface Form {
  id: string;
  code?: string;
  name: string;
  subject_id: string;
  md5: string;
  password: string;
  key?: string;
  version: number;
  schedule_id: string;
}

export interface Entry {
  id: string;
  session_id: string;
  subject_id: string;
  full_name: string;
  gender: string;
  identity_id: string;
  permit: string;
  seat_number: number;
  extra: string;
  version: number;
  form_num?: number; // 云端分配试卷 对应第1套试卷 对应form数组相应科目顺序 可能为null

  form_id?: string;
  postpone_time?: number;
  started_at?: number;
  ended_at?: number;
  status?: number;

  entry_id?: string;
  photo?: Buffer | string; // 考生照片：如果有照片，如果有照片，则命名为`[id].jpg`, 其中`id`为`entry`唯一id, 文件读取后存入字段保存;
}

export interface Response {
  entry_id: string;
  session_id: string;
  form_content: string;
  response: string;
  state: string;
  update_at: number;
}

export type PackageType = 'exam_forms' | 'exam_package' | 'formpackages'; //云端试卷包/云端场次包/悦题试卷包
export interface Manifest {
  name: string;
  type: PackageType;
  version: string;
  created_at: string;
  // type=formpackages
  forms?: ManifestForm[];
}
export interface ManifestForm {
  subject_id: string;
  subject_code: string;
  password: string;
  file: string; // "xxxx.form"
  form_id: string;
  subject_name: string;
  form_name: string; // "xxxx"
  key: string;
  md5: string;
}

export interface Anomaly {
  code: number;
  desc: string;
  name: string;
}

export type Config = Record<string, unknown>;
