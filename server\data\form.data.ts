import { DbConnection } from './types';
import globalWriteQueue from '../utils/writeQueue';

export interface IFormData {
  form_id: string;
  schedule_id: string;
  subject_id: string;
  code: string;
  name: string;
  md5: string;
  password: string;
  form_time?: string;
  password_time?: string;
  form_publish_time?: string;
  password_publish_time?: string;
  version: number;
  status?: EFormStatus;
  created_at?: string;
  subject?: IFormSubject;
}

export interface IFormSubject {
  name: string;
  code: string;
  id: string;
}

export enum EFormStatus {
  CREATED = 0,
  PUBLISHED = 1,
  PASSWORD_PUBLISHED = 2,
}
export interface IFormDataAccess {
  findForms(whereFields?: Partial<IFormData>): Promise<IFormData[]>;
  updateForm(
    whereFields: Partial<IFormData>,
    fileds: Partial<IFormData>,
  ): Promise<void>;
  deleteForm(schedule_id: string, form_id: string): Promise<void>;
  addOrUpdateForm(form: IFormData): Promise<void>;
}

export class FormDataAccess implements IFormDataAccess {
  db: DbConnection;
  constructor(db: DbConnection) {
    this.db = db;
  }
  async findForms(whereFields?: Partial<IFormData>): Promise<IFormData[]> {
    const opts = whereFields
      ? { where: whereFields, raw: true }
      : { raw: true };
    const forms = await this.db.form.findAll(opts);
    for (const form of forms) {
      form.password = this.db.decryptField(form.password);
      const subject = await this.db.subject.findOne({
        where: { subject_id: form.subject_id },
      });
      if (subject) {
        form['subject'] = {
          name: subject.name,
          code: subject.code,
          id: subject.subject_id,
        };
      }
    }
    return forms;
  }

  async updateForm(
    whereFields: Partial<IFormData>,
    fields: Partial<IFormData>,
  ): Promise<void> {
    if (fields.password) {
      fields.password = this.db.encryptField(fields.password);
    }
    await globalWriteQueue.enqueue(() =>
      this.db.form.update({ ...fields }, { where: whereFields }),
    );
  }

  async deleteForm(schedule_id: string, form_id: string) {
    await globalWriteQueue.enqueue(() =>
      this.db.form.destroy({ where: { form_id, schedule_id } }),
    );
  }

  async addOrUpdateForm(form: IFormData): Promise<void> {
    form.password = this.db.encryptField(form.password);
    await globalWriteQueue.enqueue(async () => {
      const form_instance = await this.db.form.findOne({
        where: {
          form_id: form.form_id,
          schedule_id: form.schedule_id,
        },
      });
      if (form_instance) {
        await form_instance.update(form);
      } else {
        await this.db.form.create(form);
      }
    });
  }
}
