import {
  Sequelize,
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';

export default function (sequelize: Sequelize) {
  const Subject = sequelize.define<SubjectModelFields>(
    'subject',
    {
      subject_id: {
        type: DataTypes.STRING,
        primaryKey: true,
      },
      session_id: {
        type: DataTypes.STRING,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
      },
      code: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.INTEGER,
      },
    },
    {
      timestamps: false,
      underscored: true,
      freezeTableName: true,
      hooks: {
        beforeUpdate: (subject: SubjectModelFields, opt) => {},
        beforeCreate: (subject: SubjectModelFields, opt) => {},
      },
    },
  );
  return Subject;
}

export interface SubjectModelFields
  extends Model<
    InferAttributes<SubjectModelFields>,
    InferCreationAttributes<SubjectModelFields>
  > {
  subject_id: string;
  session_id: string;
  name: string;
  code: string;
  created_at: CreationOptional<number>;
}
