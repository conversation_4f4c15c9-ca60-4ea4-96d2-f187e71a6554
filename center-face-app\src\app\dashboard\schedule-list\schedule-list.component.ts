import { parseDateTime } from '@app/utils/parseDate';
import { CommonModule } from '@angular/common';
import { Component, signal, type OnInit, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CloudHttpService, CloudSchedule } from '@app/core/http/cloud.http';
import { ServerTimeService } from '@app/core/service/server-time.service';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { StatusTransformPipe } from '@app/shared/pipes/status-transform.pipe';
import { finalize } from 'rxjs';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { ModalService } from '@app/core/service/modal.service';
import { LoadingComponent } from '@app/shared/components/loading.component';
import { interval, Subscription } from 'rxjs';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { ScheduleStateService, Schedule, ScheduleStatus } from '@app/core/service/schedule-state.service';





@Component({
    selector: 'app-schedule-list',
    imports: [
        CommonModule,
        NzEmptyModule,
        NzIconModule,
        NzSpinModule,
        StatusTransformPipe,
        LoadingComponent
    ],
    templateUrl: './schedule-list.component.html',
    styleUrl: './schedule-list.component.scss'
})
export class ScheduleListComponent implements OnInit, OnDestroy {


  ScheduleStatus = ScheduleStatus;
  isEmpty = true;
  schedules: Schedule[] = [];
  groupedSchedules = signal<{date: string, schedules: Schedule[]}[]>([]);
  isLoading = false;
  refreshSubscription?: Subscription;
  private currentErrorModal?: NzModalRef;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private cloudHttp: CloudHttpService,
    private serverTime: ServerTimeService,
    private modalService: ModalService,
    private scheduleState: ScheduleStateService,
  ) {}

  ngOnInit(): void {
    this.loadScheduleList();
    
    this.refreshSubscription = interval(60000).subscribe(() => {
      this.loadScheduleList();
    });
  }

  ngOnDestroy(): void {
    this.refreshSubscription?.unsubscribe();
  }

  private loadScheduleList(): void {
    const loadingTimer = setTimeout(() => this.isLoading = true, 1000);
    this.cloudHttp.getScheduleList().pipe(finalize(() => {
      clearTimeout(loadingTimer)
      this.isLoading = false;
    })).subscribe({
      next: (res) => {
        if (this.currentErrorModal) {
          this.currentErrorModal.destroy();
        }
        this.schedules = res.data.map((s) => {
          const attendanceRate = s.total_count > 0 ? (s.signin_count / s.total_count) * 100 : 0;
          return {
            id: s.id,
            start: s.start,
            end: s.end,
            attendanceRate: Number.isInteger(attendanceRate) 
            ? `${attendanceRate}` 
            : `${attendanceRate.toFixed(2)}`,
            attended: s.signin_count,
            total: s.total_count,
            late_limit: s.late_limit,
            subjects: s.subjects.map(s => s.name),
            status: this.getStatus(s),
          }
        });
        
        this.scheduleState.setSchedules(this.schedules);
        
        this.groupedSchedules.set(this.groupByDate(this.schedules));
        this.isEmpty = this.groupedSchedules().length === 0;
      },
      error: (err) => {
        console.error('get schedule list error:', err);
        if (!this.currentErrorModal) {
          this.currentErrorModal = this.modalService.error('获取日程失败', '请稍后再试');
          if (this.currentErrorModal && this.currentErrorModal.afterClose) {
            this.currentErrorModal.afterClose.subscribe(() => {
              this.currentErrorModal = undefined;
            });
          }
        }
      }
    });
  }

  goSignIn(scheduleId: string): void {
    const status = this.schedules.find(s => s.id === scheduleId)?.status;
    if (status === ScheduleStatus.Completed) {
      this.modalService.error('考试已结束', '请选择其他考试');
      return;
    }
    this.router.navigate(['signin', scheduleId], { relativeTo: this.route });
  }

  getStatus(schedule: CloudSchedule): ScheduleStatus {
    const now = this.serverTime.getServerTimeValue();
    const isDateExpired = new Date(now) > new Date(schedule.end);
    const isDateStarted = new Date(now) > new Date(schedule.start);
    if (isDateStarted && !isDateExpired) {
      return ScheduleStatus.Active;
    }
    if (isDateExpired) {
      return ScheduleStatus.Completed;
    }
    return ScheduleStatus.NotStarted;
  }

  
  groupByDate(schedules: Schedule[]): {date: string, schedules: Schedule[]}[] {
    return schedules.reduce((acc, schedule) => {
      const D = parseDateTime(new Date(schedule.start));
      const date = `${D.YYYY}.${D.MM}.${D.DD}`;
      const index = acc.findIndex(item => item.date === date);
      if (index === -1) {
        acc.push({
          date,
          schedules: [schedule]
        });
      } else {
        acc[index].schedules.push(schedule);
      }
      return acc;
    }, [] as {date: string, schedules: Schedule[]}[]);
  }
}
