import { EventDataAccess } from './event.data';
import { FormDataAccess } from './form.data';
import { ProjectDataAccess } from './project.data';
import { ScheduleDataAccess } from './schedule.data';
import { SessionDataAccess } from './session.data';
import { SubjectDataAccess } from './subject.data';
import { LogDataAccess } from './log.data';
import { RoomDataAccess } from './room.data';
import { DBProvider } from './db';
import { DataAccess } from './types';
import { decryptText, encryptText } from '@server/utils/crypt-aes';

const db = DBProvider.getDb();
const ENCRYPT_KEY = 'sPjT4GWvMPaN2bylQQMVotguSfAcZOQv';

db.encryptField = (text: string) =>
  text ? encryptText(text, ENCRYPT_KEY) : text;
db.decryptField = (text: string) =>
  text ? decryptText(text, ENCRYPT_KEY) : text;

export const dataAccess: DataAccess = {
  project: new ProjectDataAccess(db),
  schedule: new ScheduleDataAccess(db),
  room: new RoomDataAccess(db),
  session: new SessionDataAccess(db),
  form: new FormDataAccess(db),
  subject: new SubjectDataAccess(db),
  event: new EventDataAccess(db),
  log: new LogDataAccess(db),
  loadDb: async () => await db.open(),
};
