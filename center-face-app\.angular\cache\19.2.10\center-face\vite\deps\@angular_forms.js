import {
  AbstractControl,
  AbstractControlDirective,
  AbstractFormGroupDirective,
  COMPOSITION_BUFFER_MODE,
  CheckboxControlValueAccessor,
  CheckboxRequiredValidator,
  ControlContainer,
  ControlEvent,
  DefaultValueAccessor,
  EmailValidator,
  FormArray,
  FormArrayName,
  FormBuilder,
  FormControl,
  FormControlDirective,
  FormControlName,
  FormGroup,
  FormGroupDirective,
  FormGroupName,
  FormRecord,
  FormResetEvent,
  FormSubmittedEvent,
  FormsModule,
  MaxLengthValidator,
  MaxValidator,
  MinLengthValidator,
  MinValidator,
  NG_ASYNC_VALIDATORS,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  NgControl,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  NgModelGroup,
  NgSelectOption,
  NonNullableFormBuilder,
  NumberValueAccessor,
  PatternValidator,
  PristineChangeEvent,
  RadioControlValueAccessor,
  RangeValueAccessor,
  ReactiveFormsModule,
  RequiredValidator,
  SelectControlValueAccessor,
  SelectMultipleControlValueAccessor,
  StatusChangeEvent,
  TouchedChangeEvent,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  VERSION,
  Validators,
  ValueChangeEvent,
  isFormArray,
  isFormControl,
  isFormGroup,
  isFormRecord,
  ɵInternalFormsSharedModule,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-J34PF5DY.js";
import "./chunk-HJH6A2CS.js";
import "./chunk-TFPGR3X4.js";
import "./chunk-65JSCSPH.js";
import "./chunk-LKXCLEDA.js";
import "./chunk-4V4B465N.js";
import "./chunk-MGFUBWXT.js";
import "./chunk-R2YX2K3C.js";
export {
  AbstractControl,
  AbstractControlDirective,
  AbstractFormGroupDirective,
  COMPOSITION_BUFFER_MODE,
  CheckboxControlValueAccessor,
  CheckboxRequiredValidator,
  ControlContainer,
  ControlEvent,
  DefaultValueAccessor,
  EmailValidator,
  FormArray,
  FormArrayName,
  FormBuilder,
  FormControl,
  FormControlDirective,
  FormControlName,
  FormGroup,
  FormGroupDirective,
  FormGroupName,
  FormRecord,
  FormResetEvent,
  FormSubmittedEvent,
  FormsModule,
  MaxLengthValidator,
  MaxValidator,
  MinLengthValidator,
  MinValidator,
  NG_ASYNC_VALIDATORS,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  NgControl,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  NgModelGroup,
  NgSelectOption,
  NonNullableFormBuilder,
  NumberValueAccessor,
  PatternValidator,
  PristineChangeEvent,
  RadioControlValueAccessor,
  RangeValueAccessor,
  ReactiveFormsModule,
  RequiredValidator,
  SelectControlValueAccessor,
  SelectMultipleControlValueAccessor,
  StatusChangeEvent,
  TouchedChangeEvent,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  VERSION,
  Validators,
  ValueChangeEvent,
  isFormArray,
  isFormControl,
  isFormGroup,
  isFormRecord,
  ɵInternalFormsSharedModule,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
};
