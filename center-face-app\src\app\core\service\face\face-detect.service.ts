import { Injectable } from '@angular/core';
import { BehaviorSubject, interval, Observable, of } from 'rxjs';
import { exhaustMap, filter, switchMap, takeUntil, distinctUntilChanged } from 'rxjs/operators';
import { FaceRect } from './open-cv.models';
import {
  FaceDetectBaseService,
  VideoDetectControl,
  VideoProcessingOptions,
  FaceDetectResult,
  FaceDetectResultCode,
  FaceDetectOptions,
  FaceDetectEngine
} from './face-detect-base.service';

declare let cv: any;

@Injectable({
  providedIn: 'root',
})
export class FaceDetectService extends FaceDetectBaseService {
  private detectControl$ = new BehaviorSubject<'stop' | 'start'>('stop');
  private stop$ = this.detectControl$.pipe(filter((control) => control === 'stop'));
  private videoProcess$ = this.detectControl$.pipe(
    distinctUntilChanged(),
    switchMap((control) =>
      control === 'start'
        ? interval(300).pipe(
            exhaustMap(() => of(this.detectFace())),
            takeUntil(this.stop$)
          )
        : of(false)
    ),
  ) as Observable<FaceDetectResult | false>;

  private faceOptions?: FaceDetectOptions;

  private video!: HTMLVideoElement;
  private videoHeight!: number;
  private videoWidth!: number;
  private canvasCtx!: CanvasRenderingContext2D;

  private srcMat: any;
  private grayMat: any;

  private faceClassifier: any;

  private detectFrame: { x: number, y: number, width: number, height: number } = { x: 0, y: 0, width: 0, height: 0 };

  constructor() {
    super();
  }

  getEngineName(): FaceDetectEngine {
    return 'mediapipe';
  }

  isReady(): boolean {
    return !!this.faceClassifier;
  }

  async initialize(): Promise<{ success: boolean; mode: string }> {
    // MediaPipe/OpenCV 初始化逻辑已在 setVideoDetectConfig 中处理
    return Promise.resolve({ success: true, mode: 'mediapipe' });
  }

  destroy(): void {
    if (this.srcMat) {
      this.srcMat.delete();
      this.srcMat = null;
    }
    if (this.grayMat) {
      this.grayMat.delete();
      this.grayMat = null;
    }
    if (this.faceClassifier) {
      this.faceClassifier.delete();
      this.faceClassifier = null;
    }
  }

  setVideoDetectConfig(options: VideoProcessingOptions): VideoDetectControl {
    const { video, detectFrame, videoWidth, videoHeight, faceOptions } = options;
    this.faceOptions = faceOptions;
    this.video = video;
    const canvas = document.createElement('canvas');
    this.videoWidth = videoWidth;
    this.videoHeight = videoHeight;
    canvas.width = this.videoWidth;
    canvas.height = this.videoHeight;
    this.canvasCtx = canvas.getContext('2d', { willReadFrequently: true })!;

    this.detectFrame = detectFrame;

    this.srcMat = new cv.Mat(detectFrame.width, detectFrame.height, cv.CV_8UC4);
    this.grayMat = new cv.Mat(detectFrame.width, detectFrame.height, cv.CV_8UC1);

    if (!this.faceClassifier) {
      this.faceClassifier = new cv.CascadeClassifier();
      this.faceClassifier.load('haarcascade_frontalface_default.xml');
    }

    return {
      stop: () => {
        this.detectControl$.next('stop');
      },
      start: () => {
        this.detectControl$.next('start');
      },
      faceDetect$: this.videoProcess$
    };
  }

  setFaceOptions(options: FaceDetectOptions) {
    this.faceOptions = options;
  }

  private detectFace(): FaceDetectResult {
    const { faceInCenter, faceSize, minFaceNum } = this.faceOptions || {};
    const result: FaceDetectResult = {
      code: FaceDetectResultCode.SUCCESS,
      image: null,
      faceImage: null,
    };
    if (!this.canvasCtx) {
      result.code = FaceDetectResultCode.NO_FACE;
      return result;
    }
    this.canvasCtx.drawImage(this.video, 0, 0, this.videoWidth, this.videoHeight);
    const { x, y, width, height } = this.detectFrame;
    const imageData = this.canvasCtx.getImageData(x, y, width, height);
    this.srcMat.data.set(imageData.data);
    cv.cvtColor(this.srcMat, this.grayMat, cv.COLOR_RGBA2GRAY);
    const faceVect = new cv.RectVector();
    const faceMat = new cv.Mat();
    cv.pyrDown(this.grayMat, faceMat);
    cv.pyrDown(faceMat, faceMat);
    this.faceClassifier.detectMultiScale(faceMat, faceVect);
    const faceCount = faceVect.size();
    if (faceCount === 0) {
      result.code = FaceDetectResultCode.NO_FACE;
      return result;
    }
    if (minFaceNum && faceCount > minFaceNum) {
      result.code = FaceDetectResultCode.MULTI_FACE;
      return result;
    }
    const faceRect = this.getFaceRect(faceMat, faceVect);

    if (faceInCenter && !this.isFaceCentered(faceRect, width, height)) {
      result.code = FaceDetectResultCode.FACE_NOT_IN_CENTER;
      return result;
    }
    if (faceSize && !this.isFaceSizeAppropriate(faceRect, width, height)) {
      result.code = FaceDetectResultCode.FACE_TOO_SMALL;
      return result;
    }
    result.image = this.ImageDateToBase64(imageData);
    // 截取人脸照片
    const faceImage = this.canvasCtx.getImageData(faceRect.x + x, faceRect.y + y, faceRect.width, faceRect.height);
    result.faceImage = this.ImageDateToBase64(faceImage);
    return result;
  }

  private ImageDateToBase64(imageData: ImageData): string {
    const canvas = document.createElement('canvas');
    canvas.width = imageData.width;
    canvas.height = imageData.height;
    const ctx = canvas.getContext('2d')!;
    ctx.putImageData(imageData, 0, 0);
    return canvas.toDataURL();
  }

  private getFaceRect(faceMat: any, faceVect: any): FaceRect {
    const size = faceMat.size();
    const faceRect = faceVect.get(0);
    const rect = new cv.Rect(faceRect.x, faceRect.y, faceRect.width, faceRect.height);
    const xRatio = this.detectFrame.width / size.width;
    const yRatio = this.detectFrame.height / size.height;
    const scaled = new cv.Rect(rect.x * xRatio, rect.y * yRatio, rect.width * xRatio, rect.height * yRatio);
    return scaled;
  }

  private isFaceCentered(faceRect: FaceRect, imageWidth: number, imageHeight: number): boolean {
    const left = faceRect.x;
    const right = imageWidth - (left + faceRect.width)
    const leftRate = left / imageWidth;
    const rightRate = right / imageWidth;
    const leftRightRate = Math.abs(leftRate - rightRate);
    return leftRightRate <= 0.2
  }

  private isFaceSizeAppropriate(faceRect: FaceRect, frameWidth: number, frameHeight: number): boolean {
    const faceArea = faceRect.width * faceRect.height;
    const frameArea = frameWidth * frameHeight;
    const faceRatio = faceArea / frameArea;
    
    return faceRatio >= 0.15
  }
}
