import { Pipe, PipeTransform } from '@angular/core';
@Pipe({ name: 'roomStatusFilter', standalone: true })
export class RoomStatusFilter implements PipeTransform {
  transform(value: any[], statusFilter: Map<any, boolean>): any {
    const checkedStatus = Array.from(statusFilter)
      .filter((item) => item[1])
      .map((item) => +item[0]);
    return value.filter((room) => checkedStatus.indexOf(room['status']) > -1);
  }
}
