import { Injectable, TemplateRef, ViewContainerRef } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import {
  ChooseFileModalComponent,
  IChooseFileModalData,
} from '../../shared/modal/choose-file-modal.component';
import {
  ISyncTimeModalData,
  SyncTimeModalComponent,
} from '../../shared/modal/sync-time-modal/sync-time-modal.component';
import { SettingModalComponent } from '@app/shared/modal/setting-modal/setting-modal.component';
import { ExamResultModalComponent, IExamResultModalData } from '@app/exam/exam-result-modal/exam-result-modal.component';

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  constructor(private nzModalService: NzModalService) {}

  confirm(content: string, onOk: any) {
    this.nzModalService.confirm({
      nzTitle: '提示',
      nzContent: content,
      nzOnOk: onOk,
    });
  }

  error(title: string, content: string) {
    this.nzModalService.error({
      nzTitle: title,
      nzContent: content,
    });
  }
  create(
    type: 'success' | 'error' | 'warning',
    options: {
      title?: string;
      content?: string;
      onOk?: any;
    }
  ) {
    const opts = {
      nzTitle: options.title,
      nzContent: options.content,
      nzOnOk: options.onOk
    };
    if (type === 'success') {
      this.nzModalService.success(opts);
    } else if (type === 'error') {
      this.nzModalService.error(opts);
    } else if (type === 'warning') {
      this.nzModalService.warning(opts);
    }
  }

  createTemplate(tplTitle: TemplateRef<{}>, tplContent: TemplateRef<{}>, tplFooter: TemplateRef<{}>, opts: any = {}) {
    const modal = this.nzModalService.create({
      nzTitle: tplTitle,
      nzContent: tplContent,
      nzFooter: tplFooter,
      nzWidth: 800,
      nzMaskClosable: false,
      ...opts
    })
    return modal;
  }

  chooseFileModal(
    viewContainerRef: ViewContainerRef,
    data: IChooseFileModalData
  ) {
    const modal = this.nzModalService.create<
      ChooseFileModalComponent,
      IChooseFileModalData
    >({
      nzTitle: '导入试卷/密码/答案包',
      nzViewContainerRef: viewContainerRef,
      nzContent: ChooseFileModalComponent,
      nzData: data,
      nzFooter: null,
      nzMaskClosable: false,
    });

    return modal;
  }

  syncTimeModal(viewContainerRef: ViewContainerRef, data: ISyncTimeModalData) {
    const modal = this.nzModalService.create<
      SyncTimeModalComponent,
      ISyncTimeModalData
    >({
      nzTitle: '',
      nzClosable: false,
      nzViewContainerRef: viewContainerRef,
      nzContent: SyncTimeModalComponent,
      nzData: data,
      nzFooter: null,
      nzMaskClosable: false,
      nzWidth: 650,
    });

    return modal;
  }

  settingModal(viewContainerRef: ViewContainerRef) {
    const modal = this.nzModalService.create<SettingModalComponent>({
      nzTitle: '系统设置',
      nzViewContainerRef: viewContainerRef,
      nzContent: SettingModalComponent,
      nzFooter: null,
      nzMaskClosable: false,
    });
    return modal;
  }

  examResultModal(data: IExamResultModalData) {
    const modal = this.nzModalService.create<ExamResultModalComponent,IExamResultModalData>({
      nzTitle: '导出考生答题数据',
      nzContent: ExamResultModalComponent,
      nzData: data,
      nzFooter: null,
      nzMaskClosable: false,
    });
    return modal;
  }
}
