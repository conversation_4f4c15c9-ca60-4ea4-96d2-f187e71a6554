import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerGesturesPlugin,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  Title,
  VERSION,
  disableDebugTools,
  enableDebugTools,
  provideClientHydration,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache
} from "./chunk-3LYAQPWM.js";
import {
  BrowserDomAdapter,
  BrowserGetTestability,
  BrowserModule,
  DomEventsPlugin,
  KeyEventsPlugin,
  bootstrapApplication,
  createApplication,
  platformBrowser,
  provideProtractorTestingSupport
} from "./chunk-LCCBQOZM.js";
import "./chunk-EC535SKK.js";
import {
  DomRendererFactory2,
  EVENT_MANAGER_PLUGINS,
  Event<PERSON>anager,
  EventManagerPlugin,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  SharedStylesHost
} from "./chunk-EEBMBYCQ.js";
import {
  getDOM
} from "./chunk-HJH6A2CS.js";
import "./chunk-TFPGR3X4.js";
import "./chunk-65JSCSPH.js";
import "./chunk-LKXCLEDA.js";
import "./chunk-4V4B465N.js";
import "./chunk-MGFUBWXT.js";
import "./chunk-R2YX2K3C.js";
export {
  BrowserModule,
  By,
  DomSanitizer,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  Title,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache,
  BrowserDomAdapter as ɵBrowserDomAdapter,
  BrowserGetTestability as ɵBrowserGetTestability,
  DomEventsPlugin as ɵDomEventsPlugin,
  DomRendererFactory2 as ɵDomRendererFactory2,
  DomSanitizerImpl as ɵDomSanitizerImpl,
  HammerGesturesPlugin as ɵHammerGesturesPlugin,
  KeyEventsPlugin as ɵKeyEventsPlugin,
  SharedStylesHost as ɵSharedStylesHost,
  getDOM as ɵgetDOM
};
