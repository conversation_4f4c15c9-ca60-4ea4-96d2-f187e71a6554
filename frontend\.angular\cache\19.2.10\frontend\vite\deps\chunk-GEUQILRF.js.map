{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-tooltip.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/overlay';\nimport { OverlayModule } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, inject, ElementRef, ViewContainerRef, Renderer2, PLATFORM_ID, Directive, ChangeDetectorRef, ViewChild, TemplateRef, booleanAttribute, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { zoomBigMotion } from 'ng-zorro-antd/core/animation';\nimport { isPresetColor } from 'ng-zorro-antd/core/color';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport * as i2 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i3 from 'ng-zorro-antd/core/overlay';\nimport { POSITION_MAP, DEFAULT_TOOLTIP_POSITIONS, getPlacementName, NzOverlayModule } from 'ng-zorro-antd/core/overlay';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport { isPlatformBrowser } from '@angular/common';\nimport { Subject, asapScheduler } from 'rxjs';\nimport { distinctUntilChanged, takeUntil, filter, delay } from 'rxjs/operators';\nimport { NzConfigService } from 'ng-zorro-antd/core/config';\nimport { toBoolean, isNotNil } from 'ng-zorro-antd/core/util';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"overlay\"];\nfunction NzToolTipComponent_ng_template_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.nzTitle);\n  }\n}\nfunction NzToolTipComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 6);\n    i0.ɵɵtemplate(5, NzToolTipComponent_ng_template_0_ng_container_5_Template, 2, 1, \"ng-container\", 7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r1.nzOverlayStyle);\n    i0.ɵɵclassMap(ctx_r1._classMap);\n    i0.ɵɵclassProp(\"ant-tooltip-rtl\", ctx_r1.dir === \"rtl\");\n    i0.ɵɵproperty(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation)(\"@zoomBigMotion\", \"active\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(ctx_r1._contentStyleMap);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(ctx_r1._contentStyleMap);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r1.nzTitle)(\"nzStringTemplateOutletContext\", ctx_r1.nzTitleContext);\n  }\n}\nclass NzTooltipBaseDirective {\n  componentType;\n  config;\n  cdkConnectedOverlayPush;\n  visibleChange = new EventEmitter();\n  /**\n   * This true title that would be used in other parts on this component.\n   */\n  get _title() {\n    return this.title || this.directiveTitle || null;\n  }\n  get _content() {\n    return this.content || this.directiveContent || null;\n  }\n  get _trigger() {\n    return typeof this.trigger !== 'undefined' ? this.trigger : 'hover';\n  }\n  get _placement() {\n    const p = this.placement;\n    return Array.isArray(p) && p.length > 0 ? p : typeof p === 'string' && p ? [p] : ['top'];\n  }\n  get _visible() {\n    return (typeof this.visible !== 'undefined' ? this.visible : this.internalVisible) || false;\n  }\n  get _mouseEnterDelay() {\n    return this.mouseEnterDelay || 0.15;\n  }\n  get _mouseLeaveDelay() {\n    return this.mouseLeaveDelay || 0.1;\n  }\n  get _overlayClassName() {\n    return this.overlayClassName || null;\n  }\n  get _overlayStyle() {\n    return this.overlayStyle || null;\n  }\n  get _overlayClickable() {\n    return this.overlayClickable ?? true;\n  }\n  internalVisible = false;\n  getProxyPropertyMap() {\n    return {\n      noAnimation: ['noAnimation', () => !!this.noAnimation]\n    };\n  }\n  component;\n  destroy$ = new Subject();\n  triggerDisposables = [];\n  delayTimer;\n  elementRef = inject(ElementRef);\n  hostView = inject(ViewContainerRef);\n  renderer = inject(Renderer2);\n  noAnimation = inject(NzNoAnimationDirective, {\n    host: true,\n    optional: true\n  });\n  nzConfigService = inject(NzConfigService);\n  platformId = inject(PLATFORM_ID);\n  constructor(componentType) {\n    this.componentType = componentType;\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.createComponent();\n      this.registerTriggers();\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      trigger\n    } = changes;\n    if (trigger && !trigger.isFirstChange()) {\n      this.registerTriggers();\n    }\n    if (this.component) {\n      this.updatePropertiesByChanges(changes);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    // Clear toggling timer. Issue #3875 #4317 #4386\n    this.clearTogglingTimer();\n    this.removeTriggerListeners();\n  }\n  show() {\n    this.component?.show();\n  }\n  hide() {\n    this.component?.hide();\n  }\n  /**\n   * Force the component to update its position.\n   */\n  updatePosition() {\n    if (this.component) {\n      this.component.updatePosition();\n    }\n  }\n  /**\n   * Create a dynamic tooltip component. This method can be override.\n   */\n  createComponent() {\n    const componentRef = this.hostView.createComponent(this.componentType);\n    this.component = componentRef.instance;\n    // Remove the component's DOM because it should be in the overlay container.\n    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), componentRef.location.nativeElement);\n    this.component.setOverlayOrigin(this.origin || this.elementRef);\n    this.initProperties();\n    const ngVisibleChange$ = this.component.nzVisibleChange.pipe(distinctUntilChanged());\n    ngVisibleChange$.pipe(takeUntil(this.destroy$)).subscribe(visible => {\n      this.internalVisible = visible;\n      this.visibleChange.emit(visible);\n    });\n    // In some cases, the rendering takes into account the height at which the `arrow` is in wrong place,\n    // so `cdk` sets the container position incorrectly.\n    // To avoid this, after placing the `arrow` in the correct position, we should `re-calculate` the position of the `overlay`.\n    ngVisibleChange$.pipe(filter(visible => visible), delay(0, asapScheduler), filter(() => Boolean(this.component?.overlay?.overlayRef)), takeUntil(this.destroy$)).subscribe(() => {\n      this.component?.updatePosition();\n    });\n  }\n  registerTriggers() {\n    // When the method gets invoked, all properties has been synced to the dynamic component.\n    // After removing the old API, we can just check the directive's own `nzTrigger`.\n    const el = this.elementRef.nativeElement;\n    const trigger = this.trigger;\n    this.removeTriggerListeners();\n    if (trigger === 'hover') {\n      let overlayElement;\n      this.triggerDisposables.push(this.renderer.listen(el, 'mouseenter', () => {\n        this.delayEnterLeave(true, true, this._mouseEnterDelay);\n      }));\n      this.triggerDisposables.push(this.renderer.listen(el, 'mouseleave', () => {\n        this.delayEnterLeave(true, false, this._mouseLeaveDelay);\n        if (this.component?.overlay.overlayRef && !overlayElement) {\n          overlayElement = this.component.overlay.overlayRef.overlayElement;\n          this.triggerDisposables.push(this.renderer.listen(overlayElement, 'mouseenter', () => {\n            this.delayEnterLeave(false, true, this._mouseEnterDelay);\n          }));\n          this.triggerDisposables.push(this.renderer.listen(overlayElement, 'mouseleave', () => {\n            this.delayEnterLeave(false, false, this._mouseLeaveDelay);\n          }));\n        }\n      }));\n    } else if (trigger === 'focus') {\n      this.triggerDisposables.push(this.renderer.listen(el, 'focusin', () => this.show()));\n      this.triggerDisposables.push(this.renderer.listen(el, 'focusout', () => this.hide()));\n    } else if (trigger === 'click') {\n      this.triggerDisposables.push(this.renderer.listen(el, 'click', e => {\n        e.preventDefault();\n        this.show();\n      }));\n    }\n    // Else do nothing because user wants to control the visibility programmatically.\n  }\n  updatePropertiesByChanges(changes) {\n    this.updatePropertiesByKeys(Object.keys(changes));\n  }\n  updatePropertiesByKeys(keys) {\n    const mappingProperties = {\n      // common mappings\n      title: ['nzTitle', () => this._title],\n      directiveTitle: ['nzTitle', () => this._title],\n      content: ['nzContent', () => this._content],\n      directiveContent: ['nzContent', () => this._content],\n      trigger: ['nzTrigger', () => this._trigger],\n      placement: ['nzPlacement', () => this._placement],\n      visible: ['nzVisible', () => this._visible],\n      mouseEnterDelay: ['nzMouseEnterDelay', () => this._mouseEnterDelay],\n      mouseLeaveDelay: ['nzMouseLeaveDelay', () => this._mouseLeaveDelay],\n      overlayClassName: ['nzOverlayClassName', () => this._overlayClassName],\n      overlayStyle: ['nzOverlayStyle', () => this._overlayStyle],\n      overlayClickable: ['nzOverlayClickable', () => this._overlayClickable],\n      arrowPointAtCenter: ['nzArrowPointAtCenter', () => this.arrowPointAtCenter],\n      cdkConnectedOverlayPush: ['cdkConnectedOverlayPush', () => this.cdkConnectedOverlayPush],\n      ...this.getProxyPropertyMap()\n    };\n    (keys || Object.keys(mappingProperties).filter(key => !key.startsWith('directive'))).forEach(property => {\n      if (mappingProperties[property]) {\n        const [name, valueFn] = mappingProperties[property];\n        this.updateComponentValue(name, valueFn());\n      }\n    });\n    this.component?.updateByDirective();\n  }\n  initProperties() {\n    this.updatePropertiesByKeys();\n  }\n  updateComponentValue(key, value) {\n    if (typeof value !== 'undefined') {\n      // @ts-ignore\n      this.component[key] = value;\n    }\n  }\n  delayEnterLeave(isOrigin, isEnter, delay = -1) {\n    if (this.delayTimer) {\n      this.clearTogglingTimer();\n    } else if (delay > 0) {\n      this.delayTimer = setTimeout(() => {\n        this.delayTimer = undefined;\n        isEnter ? this.show() : this.hide();\n      }, delay * 1000);\n    } else {\n      // `isOrigin` is used due to the tooltip will not hide immediately\n      // (may caused by the fade-out animation).\n      isEnter && isOrigin ? this.show() : this.hide();\n    }\n  }\n  removeTriggerListeners() {\n    this.triggerDisposables.forEach(dispose => dispose());\n    this.triggerDisposables.length = 0;\n  }\n  clearTogglingTimer() {\n    if (this.delayTimer) {\n      clearTimeout(this.delayTimer);\n      this.delayTimer = undefined;\n    }\n  }\n  static ɵfac = function NzTooltipBaseDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTooltipBaseDirective)(i0.ɵɵdirectiveInject(i0.Type));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTooltipBaseDirective,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTooltipBaseDirective, [{\n    type: Directive\n  }], () => [{\n    type: i0.Type\n  }], null);\n})();\nclass NzTooltipBaseComponent {\n  overlay;\n  noAnimation = inject(NzNoAnimationDirective, {\n    host: true,\n    optional: true\n  });\n  cdr = inject(ChangeDetectorRef);\n  directionality = inject(Directionality);\n  nzTitle = null;\n  nzContent = null;\n  nzArrowPointAtCenter = false;\n  nzOverlayClassName;\n  nzOverlayStyle = {};\n  nzOverlayClickable = true;\n  nzBackdrop = false;\n  nzMouseEnterDelay;\n  nzMouseLeaveDelay;\n  cdkConnectedOverlayPush = true;\n  nzVisibleChange = new Subject();\n  set nzVisible(value) {\n    const visible = toBoolean(value);\n    if (this._visible !== visible) {\n      this._visible = visible;\n      this.nzVisibleChange.next(visible);\n    }\n  }\n  get nzVisible() {\n    return this._visible;\n  }\n  _visible = false;\n  set nzTrigger(value) {\n    this._trigger = value;\n  }\n  get nzTrigger() {\n    return this._trigger;\n  }\n  _trigger = 'hover';\n  set nzPlacement(value) {\n    const preferredPosition = value.map(placement => POSITION_MAP[placement]);\n    this._positions = [...preferredPosition, ...DEFAULT_TOOLTIP_POSITIONS];\n  }\n  preferredPlacement = 'top';\n  origin;\n  dir = 'ltr';\n  _classMap = {};\n  _prefix = 'ant-tooltip';\n  _positions = [...DEFAULT_TOOLTIP_POSITIONS];\n  destroy$ = new Subject();\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.nzVisibleChange.complete();\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  show() {\n    if (this.nzVisible) {\n      return;\n    }\n    if (!this.isEmpty()) {\n      this.nzVisible = true;\n      this.nzVisibleChange.next(true);\n      this.cdr.detectChanges();\n    }\n    // for ltr for overlay to display tooltip in correct placement in rtl direction.\n    if (this.origin && this.overlay && this.overlay.overlayRef && this.overlay.overlayRef.getDirection() === 'rtl') {\n      this.overlay.overlayRef.setDirection('ltr');\n    }\n  }\n  hide() {\n    if (!this.nzVisible) {\n      return;\n    }\n    this.nzVisible = false;\n    this.nzVisibleChange.next(false);\n    this.cdr.detectChanges();\n  }\n  updateByDirective() {\n    this.updateStyles();\n    this.cdr.detectChanges();\n    Promise.resolve().then(() => {\n      this.updatePosition();\n      this.updateVisibilityByTitle();\n    });\n  }\n  /**\n   * Force the component to update its position.\n   */\n  updatePosition() {\n    if (this.origin && this.overlay && this.overlay.overlayRef) {\n      this.overlay.overlayRef.updatePosition();\n    }\n  }\n  onPositionChange(position) {\n    this.preferredPlacement = getPlacementName(position);\n    this.updateStyles();\n    // We have to trigger immediate change detection or the element would blink.\n    this.cdr.detectChanges();\n  }\n  setOverlayOrigin(origin) {\n    this.origin = origin;\n    this.cdr.markForCheck();\n  }\n  onClickOutside(event) {\n    if (!this.nzOverlayClickable) {\n      return;\n    }\n    const target = _getEventTarget(event);\n    if (!this.origin.nativeElement.contains(target) && this.nzTrigger !== null) {\n      this.hide();\n    }\n  }\n  /**\n   * Hide the component while the content is empty.\n   */\n  updateVisibilityByTitle() {\n    if (this.isEmpty()) {\n      this.hide();\n    }\n  }\n  updateStyles() {\n    this._classMap = {\n      ...this.transformClassListToMap(this.nzOverlayClassName),\n      [`${this._prefix}-placement-${this.preferredPlacement}`]: true\n    };\n  }\n  transformClassListToMap(klass) {\n    const result = {};\n    /**\n     * @see https://github.com/angular/angular/blob/f6e97763cfab9fa2bea6e6b1303b64f1b499c3ef/packages/common/src/directives/ng_class.ts#L92\n     */\n    const classes = klass !== null ? klass.split(/\\s+/) : [];\n    classes.forEach(className => result[className] = true);\n    return result;\n  }\n  static ɵfac = function NzTooltipBaseComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTooltipBaseComponent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTooltipBaseComponent,\n    viewQuery: function NzTooltipBaseComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlay = _t.first);\n      }\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTooltipBaseComponent, [{\n    type: Directive\n  }], null, {\n    overlay: [{\n      type: ViewChild,\n      args: ['overlay', {\n        static: false\n      }]\n    }]\n  });\n})();\nfunction isTooltipEmpty(value) {\n  return value instanceof TemplateRef ? false : value === '' || !isNotNil(value);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTooltipDirective extends NzTooltipBaseDirective {\n  /* eslint-disable @angular-eslint/no-input-rename, @angular-eslint/no-output-rename */\n  title;\n  titleContext = null;\n  directiveTitle;\n  trigger = 'hover';\n  placement = 'top';\n  origin;\n  visible;\n  mouseEnterDelay;\n  mouseLeaveDelay;\n  overlayClassName;\n  overlayStyle;\n  arrowPointAtCenter;\n  cdkConnectedOverlayPush = true;\n  nzTooltipColor;\n  directiveContent = null;\n  content = null;\n  overlayClickable;\n  visibleChange = new EventEmitter();\n  constructor() {\n    super(NzToolTipComponent);\n  }\n  getProxyPropertyMap() {\n    return {\n      ...super.getProxyPropertyMap(),\n      nzTooltipColor: ['nzColor', () => this.nzTooltipColor],\n      titleContext: ['nzTitleContext', () => this.titleContext]\n    };\n  }\n  static ɵfac = function NzTooltipDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTooltipDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTooltipDirective,\n    selectors: [[\"\", \"nz-tooltip\", \"\"]],\n    hostVars: 2,\n    hostBindings: function NzTooltipDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-tooltip-open\", ctx.visible);\n      }\n    },\n    inputs: {\n      title: [0, \"nzTooltipTitle\", \"title\"],\n      titleContext: [0, \"nzTooltipTitleContext\", \"titleContext\"],\n      directiveTitle: [0, \"nz-tooltip\", \"directiveTitle\"],\n      trigger: [0, \"nzTooltipTrigger\", \"trigger\"],\n      placement: [0, \"nzTooltipPlacement\", \"placement\"],\n      origin: [0, \"nzTooltipOrigin\", \"origin\"],\n      visible: [0, \"nzTooltipVisible\", \"visible\"],\n      mouseEnterDelay: [0, \"nzTooltipMouseEnterDelay\", \"mouseEnterDelay\"],\n      mouseLeaveDelay: [0, \"nzTooltipMouseLeaveDelay\", \"mouseLeaveDelay\"],\n      overlayClassName: [0, \"nzTooltipOverlayClassName\", \"overlayClassName\"],\n      overlayStyle: [0, \"nzTooltipOverlayStyle\", \"overlayStyle\"],\n      arrowPointAtCenter: [2, \"nzTooltipArrowPointAtCenter\", \"arrowPointAtCenter\", booleanAttribute],\n      cdkConnectedOverlayPush: [2, \"cdkConnectedOverlayPush\", \"cdkConnectedOverlayPush\", booleanAttribute],\n      nzTooltipColor: \"nzTooltipColor\"\n    },\n    outputs: {\n      visibleChange: \"nzTooltipVisibleChange\"\n    },\n    exportAs: [\"nzTooltip\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTooltipDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-tooltip]',\n      exportAs: 'nzTooltip',\n      host: {\n        '[class.ant-tooltip-open]': 'visible'\n      }\n    }]\n  }], () => [], {\n    title: [{\n      type: Input,\n      args: ['nzTooltipTitle']\n    }],\n    titleContext: [{\n      type: Input,\n      args: ['nzTooltipTitleContext']\n    }],\n    directiveTitle: [{\n      type: Input,\n      args: ['nz-tooltip']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['nzTooltipTrigger']\n    }],\n    placement: [{\n      type: Input,\n      args: ['nzTooltipPlacement']\n    }],\n    origin: [{\n      type: Input,\n      args: ['nzTooltipOrigin']\n    }],\n    visible: [{\n      type: Input,\n      args: ['nzTooltipVisible']\n    }],\n    mouseEnterDelay: [{\n      type: Input,\n      args: ['nzTooltipMouseEnterDelay']\n    }],\n    mouseLeaveDelay: [{\n      type: Input,\n      args: ['nzTooltipMouseLeaveDelay']\n    }],\n    overlayClassName: [{\n      type: Input,\n      args: ['nzTooltipOverlayClassName']\n    }],\n    overlayStyle: [{\n      type: Input,\n      args: ['nzTooltipOverlayStyle']\n    }],\n    arrowPointAtCenter: [{\n      type: Input,\n      args: [{\n        alias: 'nzTooltipArrowPointAtCenter',\n        transform: booleanAttribute\n      }]\n    }],\n    cdkConnectedOverlayPush: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzTooltipColor: [{\n      type: Input\n    }],\n    visibleChange: [{\n      type: Output,\n      args: ['nzTooltipVisibleChange']\n    }]\n  });\n})();\nclass NzToolTipComponent extends NzTooltipBaseComponent {\n  nzTitle = null;\n  nzTitleContext = null;\n  nzColor;\n  _contentStyleMap = {};\n  isEmpty() {\n    return isTooltipEmpty(this.nzTitle);\n  }\n  updateStyles() {\n    const isColorPreset = this.nzColor && isPresetColor(this.nzColor);\n    this._classMap = {\n      ...this.transformClassListToMap(this.nzOverlayClassName),\n      [`${this._prefix}-placement-${this.preferredPlacement}`]: true,\n      [`${this._prefix}-${this.nzColor}`]: isColorPreset\n    };\n    this._contentStyleMap = {\n      backgroundColor: !!this.nzColor && !isColorPreset ? this.nzColor : null,\n      '--antd-arrow-background-color': this.nzColor\n    };\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵNzToolTipComponent_BaseFactory;\n    return function NzToolTipComponent_Factory(__ngFactoryType__) {\n      return (ɵNzToolTipComponent_BaseFactory || (ɵNzToolTipComponent_BaseFactory = i0.ɵɵgetInheritedFactory(NzToolTipComponent)))(__ngFactoryType__ || NzToolTipComponent);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzToolTipComponent,\n    selectors: [[\"nz-tooltip\"]],\n    exportAs: [\"nzTooltipComponent\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 5,\n    consts: [[\"overlay\", \"cdkConnectedOverlay\"], [\"cdkConnectedOverlay\", \"\", \"nzConnectedOverlay\", \"\", 3, \"overlayOutsideClick\", \"detach\", \"positionChange\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPush\", \"nzArrowPointAtCenter\"], [1, \"ant-tooltip\", 3, \"nzNoAnimation\"], [1, \"ant-tooltip-content\"], [1, \"ant-tooltip-arrow\"], [1, \"ant-tooltip-arrow-content\"], [1, \"ant-tooltip-inner\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"]],\n    template: function NzToolTipComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, NzToolTipComponent_ng_template_0_Template, 6, 15, \"ng-template\", 1, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵlistener(\"overlayOutsideClick\", function NzToolTipComponent_Template_ng_template_overlayOutsideClick_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onClickOutside($event));\n        })(\"detach\", function NzToolTipComponent_Template_ng_template_detach_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.hide());\n        })(\"positionChange\", function NzToolTipComponent_Template_ng_template_positionChange_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onPositionChange($event));\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"cdkConnectedOverlayOrigin\", ctx.origin)(\"cdkConnectedOverlayOpen\", ctx._visible)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayPush\", ctx.cdkConnectedOverlayPush)(\"nzArrowPointAtCenter\", ctx.nzArrowPointAtCenter);\n      }\n    },\n    dependencies: [OverlayModule, i1.CdkConnectedOverlay, NzNoAnimationDirective, NzOutletModule, i2.NzStringTemplateOutletDirective, NzOverlayModule, i3.NzConnectedOverlayDirective],\n    encapsulation: 2,\n    data: {\n      animation: [zoomBigMotion]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzToolTipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-tooltip',\n      exportAs: 'nzTooltipComponent',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      animations: [zoomBigMotion],\n      template: `\n    <ng-template\n      #overlay=\"cdkConnectedOverlay\"\n      cdkConnectedOverlay\n      nzConnectedOverlay\n      [cdkConnectedOverlayOrigin]=\"origin\"\n      [cdkConnectedOverlayOpen]=\"_visible\"\n      [cdkConnectedOverlayPositions]=\"_positions\"\n      [cdkConnectedOverlayPush]=\"cdkConnectedOverlayPush\"\n      [nzArrowPointAtCenter]=\"nzArrowPointAtCenter\"\n      (overlayOutsideClick)=\"onClickOutside($event)\"\n      (detach)=\"hide()\"\n      (positionChange)=\"onPositionChange($event)\"\n    >\n      <div\n        class=\"ant-tooltip\"\n        [class.ant-tooltip-rtl]=\"dir === 'rtl'\"\n        [class]=\"_classMap\"\n        [style]=\"nzOverlayStyle\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [@zoomBigMotion]=\"'active'\"\n      >\n        <div class=\"ant-tooltip-content\">\n          <div class=\"ant-tooltip-arrow\">\n            <span class=\"ant-tooltip-arrow-content\" [style]=\"_contentStyleMap\"></span>\n          </div>\n          <div class=\"ant-tooltip-inner\" [style]=\"_contentStyleMap\">\n            <ng-container *nzStringTemplateOutlet=\"nzTitle; context: nzTitleContext\">{{ nzTitle }}</ng-container>\n          </div>\n        </div>\n      </div>\n    </ng-template>\n  `,\n      preserveWhitespaces: false,\n      imports: [OverlayModule, NzNoAnimationDirective, NzOutletModule, NzOverlayModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzToolTipModule {\n  static ɵfac = function NzToolTipModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzToolTipModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzToolTipModule,\n    imports: [NzToolTipComponent, NzTooltipDirective],\n    exports: [NzToolTipComponent, NzTooltipDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzToolTipComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzToolTipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzToolTipComponent, NzTooltipDirective],\n      exports: [NzToolTipComponent, NzTooltipDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzToolTipComponent, NzToolTipModule, NzTooltipBaseComponent, NzTooltipBaseDirective, NzTooltipDirective, isTooltipEmpty };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAM,MAAM,CAAC,SAAS;AACtB,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,gBAAgB,CAAC;AAClG,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,cAAc;AACnC,IAAG,WAAW,OAAO,SAAS;AAC9B,IAAG,YAAY,mBAAmB,OAAO,QAAQ,KAAK;AACtD,IAAG,WAAW,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa,EAAE,kBAAkB,QAAQ;AACvN,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,OAAO,gBAAgB;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,gBAAgB;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO,EAAE,iCAAiC,OAAO,cAAc;AAAA,EAChH;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA,EAIjC,IAAI,SAAS;AACX,WAAO,KAAK,SAAS,KAAK,kBAAkB;AAAA,EAC9C;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,KAAK,oBAAoB;AAAA,EAClD;AAAA,EACA,IAAI,WAAW;AACb,WAAO,OAAO,KAAK,YAAY,cAAc,KAAK,UAAU;AAAA,EAC9D;AAAA,EACA,IAAI,aAAa;AACf,UAAM,IAAI,KAAK;AACf,WAAO,MAAM,QAAQ,CAAC,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,MAAM,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK;AAAA,EACzF;AAAA,EACA,IAAI,WAAW;AACb,YAAQ,OAAO,KAAK,YAAY,cAAc,KAAK,UAAU,KAAK,oBAAoB;AAAA,EACxF;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,kBAAkB;AAAA,EAClB,sBAAsB;AACpB,WAAO;AAAA,MACL,aAAa,CAAC,eAAe,MAAM,CAAC,CAAC,KAAK,WAAW;AAAA,IACvD;AAAA,EACF;AAAA,EACA;AAAA,EACA,WAAW,IAAI,QAAQ;AAAA,EACvB,qBAAqB,CAAC;AAAA,EACtB;AAAA,EACA,aAAa,OAAO,UAAU;AAAA,EAC9B,WAAW,OAAO,gBAAgB;AAAA,EAClC,WAAW,OAAO,SAAS;AAAA,EAC3B,cAAc,OAAO,wBAAwB;AAAA,IAC3C,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,kBAAkB,OAAO,eAAe;AAAA,EACxC,aAAa,OAAO,WAAW;AAAA,EAC/B,YAAY,eAAe;AACzB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,CAAC,QAAQ,cAAc,GAAG;AACvC,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,0BAA0B,OAAO;AAAA,IACxC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAEvB,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,eAAe;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,UAAM,eAAe,KAAK,SAAS,gBAAgB,KAAK,aAAa;AACrE,SAAK,YAAY,aAAa;AAE9B,SAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,aAAa,SAAS,aAAa;AACtH,SAAK,UAAU,iBAAiB,KAAK,UAAU,KAAK,UAAU;AAC9D,SAAK,eAAe;AACpB,UAAM,mBAAmB,KAAK,UAAU,gBAAgB,KAAK,qBAAqB,CAAC;AACnF,qBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,aAAW;AACnE,WAAK,kBAAkB;AACvB,WAAK,cAAc,KAAK,OAAO;AAAA,IACjC,CAAC;AAID,qBAAiB,KAAK,OAAO,aAAW,OAAO,GAAG,MAAM,GAAG,aAAa,GAAG,OAAO,MAAM,QAAQ,KAAK,WAAW,SAAS,UAAU,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC/K,WAAK,WAAW,eAAe;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AAGjB,UAAM,KAAK,KAAK,WAAW;AAC3B,UAAM,UAAU,KAAK;AACrB,SAAK,uBAAuB;AAC5B,QAAI,YAAY,SAAS;AACvB,UAAI;AACJ,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,cAAc,MAAM;AACxE,aAAK,gBAAgB,MAAM,MAAM,KAAK,gBAAgB;AAAA,MACxD,CAAC,CAAC;AACF,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,cAAc,MAAM;AACxE,aAAK,gBAAgB,MAAM,OAAO,KAAK,gBAAgB;AACvD,YAAI,KAAK,WAAW,QAAQ,cAAc,CAAC,gBAAgB;AACzD,2BAAiB,KAAK,UAAU,QAAQ,WAAW;AACnD,eAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,gBAAgB,cAAc,MAAM;AACpF,iBAAK,gBAAgB,OAAO,MAAM,KAAK,gBAAgB;AAAA,UACzD,CAAC,CAAC;AACF,eAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,gBAAgB,cAAc,MAAM;AACpF,iBAAK,gBAAgB,OAAO,OAAO,KAAK,gBAAgB;AAAA,UAC1D,CAAC,CAAC;AAAA,QACJ;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,WAAW,YAAY,SAAS;AAC9B,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,WAAW,MAAM,KAAK,KAAK,CAAC,CAAC;AACnF,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,YAAY,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,IACtF,WAAW,YAAY,SAAS;AAC9B,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,SAAS,OAAK;AAClE,UAAE,eAAe;AACjB,aAAK,KAAK;AAAA,MACZ,CAAC,CAAC;AAAA,IACJ;AAAA,EAEF;AAAA,EACA,0BAA0B,SAAS;AACjC,SAAK,uBAAuB,OAAO,KAAK,OAAO,CAAC;AAAA,EAClD;AAAA,EACA,uBAAuB,MAAM;AAC3B,UAAM,oBAAoB;AAAA;AAAA,MAExB,OAAO,CAAC,WAAW,MAAM,KAAK,MAAM;AAAA,MACpC,gBAAgB,CAAC,WAAW,MAAM,KAAK,MAAM;AAAA,MAC7C,SAAS,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MAC1C,kBAAkB,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MACnD,SAAS,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MAC1C,WAAW,CAAC,eAAe,MAAM,KAAK,UAAU;AAAA,MAChD,SAAS,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MAC1C,iBAAiB,CAAC,qBAAqB,MAAM,KAAK,gBAAgB;AAAA,MAClE,iBAAiB,CAAC,qBAAqB,MAAM,KAAK,gBAAgB;AAAA,MAClE,kBAAkB,CAAC,sBAAsB,MAAM,KAAK,iBAAiB;AAAA,MACrE,cAAc,CAAC,kBAAkB,MAAM,KAAK,aAAa;AAAA,MACzD,kBAAkB,CAAC,sBAAsB,MAAM,KAAK,iBAAiB;AAAA,MACrE,oBAAoB,CAAC,wBAAwB,MAAM,KAAK,kBAAkB;AAAA,MAC1E,yBAAyB,CAAC,2BAA2B,MAAM,KAAK,uBAAuB;AAAA,OACpF,KAAK,oBAAoB;AAE9B,KAAC,QAAQ,OAAO,KAAK,iBAAiB,EAAE,OAAO,SAAO,CAAC,IAAI,WAAW,WAAW,CAAC,GAAG,QAAQ,cAAY;AACvG,UAAI,kBAAkB,QAAQ,GAAG;AAC/B,cAAM,CAAC,MAAM,OAAO,IAAI,kBAAkB,QAAQ;AAClD,aAAK,qBAAqB,MAAM,QAAQ,CAAC;AAAA,MAC3C;AAAA,IACF,CAAC;AACD,SAAK,WAAW,kBAAkB;AAAA,EACpC;AAAA,EACA,iBAAiB;AACf,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,qBAAqB,KAAK,OAAO;AAC/B,QAAI,OAAO,UAAU,aAAa;AAEhC,WAAK,UAAU,GAAG,IAAI;AAAA,IACxB;AAAA,EACF;AAAA,EACA,gBAAgB,UAAU,SAASA,SAAQ,IAAI;AAC7C,QAAI,KAAK,YAAY;AACnB,WAAK,mBAAmB;AAAA,IAC1B,WAAWA,SAAQ,GAAG;AACpB,WAAK,aAAa,WAAW,MAAM;AACjC,aAAK,aAAa;AAClB,kBAAU,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,MACpC,GAAGA,SAAQ,GAAI;AAAA,IACjB,OAAO;AAGL,iBAAW,WAAW,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,IAChD;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AACpD,SAAK,mBAAmB,SAAS;AAAA,EACnC;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,YAAY;AACnB,mBAAa,KAAK,UAAU;AAC5B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAA2B,kBAAqB,IAAI,CAAC;AAAA,EACxF;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA,cAAc,OAAO,wBAAwB;AAAA,IAC3C,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,MAAM,OAAO,iBAAiB;AAAA,EAC9B,iBAAiB,OAAO,cAAc;AAAA,EACtC,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,uBAAuB;AAAA,EACvB;AAAA,EACA,iBAAiB,CAAC;AAAA,EAClB,qBAAqB;AAAA,EACrB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA,0BAA0B;AAAA,EAC1B,kBAAkB,IAAI,QAAQ;AAAA,EAC9B,IAAI,UAAU,OAAO;AACnB,UAAM,UAAU,UAAU,KAAK;AAC/B,QAAI,KAAK,aAAa,SAAS;AAC7B,WAAK,WAAW;AAChB,WAAK,gBAAgB,KAAK,OAAO;AAAA,IACnC;AAAA,EACF;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AAAA,EACX,IAAI,UAAU,OAAO;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AAAA,EACX,IAAI,YAAY,OAAO;AACrB,UAAM,oBAAoB,MAAM,IAAI,eAAa,aAAa,SAAS,CAAC;AACxE,SAAK,aAAa,CAAC,GAAG,mBAAmB,GAAG,yBAAyB;AAAA,EACvE;AAAA,EACA,qBAAqB;AAAA,EACrB;AAAA,EACA,MAAM;AAAA,EACN,YAAY,CAAC;AAAA,EACb,UAAU;AAAA,EACV,aAAa,CAAC,GAAG,yBAAyB;AAAA,EAC1C,WAAW,IAAI,QAAQ;AAAA,EACvB,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,SAAS;AAC9B,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,WAAK,YAAY;AACjB,WAAK,gBAAgB,KAAK,IAAI;AAC9B,WAAK,IAAI,cAAc;AAAA,IACzB;AAEA,QAAI,KAAK,UAAU,KAAK,WAAW,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,aAAa,MAAM,OAAO;AAC9G,WAAK,QAAQ,WAAW,aAAa,KAAK;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,KAAK;AAC/B,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,oBAAoB;AAClB,SAAK,aAAa;AAClB,SAAK,IAAI,cAAc;AACvB,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,WAAK,eAAe;AACpB,WAAK,wBAAwB;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,UAAU,KAAK,WAAW,KAAK,QAAQ,YAAY;AAC1D,WAAK,QAAQ,WAAW,eAAe;AAAA,IACzC;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,qBAAqB,iBAAiB,QAAQ;AACnD,SAAK,aAAa;AAElB,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,iBAAiB,QAAQ;AACvB,SAAK,SAAS;AACd,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,CAAC,KAAK,oBAAoB;AAC5B;AAAA,IACF;AACA,UAAM,SAAS,gBAAgB,KAAK;AACpC,QAAI,CAAC,KAAK,OAAO,cAAc,SAAS,MAAM,KAAK,KAAK,cAAc,MAAM;AAC1E,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B;AACxB,QAAI,KAAK,QAAQ,GAAG;AAClB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,YAAY,iCACZ,KAAK,wBAAwB,KAAK,kBAAkB,IADxC;AAAA,MAEf,CAAC,GAAG,KAAK,OAAO,cAAc,KAAK,kBAAkB,EAAE,GAAG;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,wBAAwB,OAAO;AAC7B,UAAM,SAAS,CAAC;AAIhB,UAAM,UAAU,UAAU,OAAO,MAAM,MAAM,KAAK,IAAI,CAAC;AACvD,YAAQ,QAAQ,eAAa,OAAO,SAAS,IAAI,IAAI;AACrD,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,SAAS,6BAA6B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,MAChE;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,eAAe,OAAO;AAC7B,SAAO,iBAAiB,cAAc,QAAQ,UAAU,MAAM,CAAC,SAAS,KAAK;AAC/E;AAMA,IAAM,qBAAN,MAAM,4BAA2B,uBAAuB;AAAA;AAAA,EAEtD;AAAA,EACA,eAAe;AAAA,EACf;AAAA,EACA,UAAU;AAAA,EACV,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV;AAAA,EACA,gBAAgB,IAAI,aAAa;AAAA,EACjC,cAAc;AACZ,UAAM,kBAAkB;AAAA,EAC1B;AAAA,EACA,sBAAsB;AACpB,WAAO,iCACF,MAAM,oBAAoB,IADxB;AAAA,MAEL,gBAAgB,CAAC,WAAW,MAAM,KAAK,cAAc;AAAA,MACrD,cAAc,CAAC,kBAAkB,MAAM,KAAK,YAAY;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAClC,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,oBAAoB,IAAI,OAAO;AAAA,MAChD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,kBAAkB,OAAO;AAAA,MACpC,cAAc,CAAC,GAAG,yBAAyB,cAAc;AAAA,MACzD,gBAAgB,CAAC,GAAG,cAAc,gBAAgB;AAAA,MAClD,SAAS,CAAC,GAAG,oBAAoB,SAAS;AAAA,MAC1C,WAAW,CAAC,GAAG,sBAAsB,WAAW;AAAA,MAChD,QAAQ,CAAC,GAAG,mBAAmB,QAAQ;AAAA,MACvC,SAAS,CAAC,GAAG,oBAAoB,SAAS;AAAA,MAC1C,iBAAiB,CAAC,GAAG,4BAA4B,iBAAiB;AAAA,MAClE,iBAAiB,CAAC,GAAG,4BAA4B,iBAAiB;AAAA,MAClE,kBAAkB,CAAC,GAAG,6BAA6B,kBAAkB;AAAA,MACrE,cAAc,CAAC,GAAG,yBAAyB,cAAc;AAAA,MACzD,oBAAoB,CAAC,GAAG,+BAA+B,sBAAsB,gBAAgB;AAAA,MAC7F,yBAAyB,CAAC,GAAG,2BAA2B,2BAA2B,gBAAgB;AAAA,MACnG,gBAAgB;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,4BAA4B;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,4BAA2B,uBAAuB;AAAA,EACtD,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB;AAAA,EACA,mBAAmB,CAAC;AAAA,EACpB,UAAU;AACR,WAAO,eAAe,KAAK,OAAO;AAAA,EACpC;AAAA,EACA,eAAe;AACb,UAAM,gBAAgB,KAAK,WAAW,cAAc,KAAK,OAAO;AAChE,SAAK,YAAY,iCACZ,KAAK,wBAAwB,KAAK,kBAAkB,IADxC;AAAA,MAEf,CAAC,GAAG,KAAK,OAAO,cAAc,KAAK,kBAAkB,EAAE,GAAG;AAAA,MAC1D,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,OAAO,EAAE,GAAG;AAAA,IACvC;AACA,SAAK,mBAAmB;AAAA,MACtB,iBAAiB,CAAC,CAAC,KAAK,WAAW,CAAC,gBAAgB,KAAK,UAAU;AAAA,MACnE,iCAAiC,KAAK;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,UAAU,CAAC,oBAAoB;AAAA,IAC/B,UAAU,CAAI,0BAA0B;AAAA,IACxC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,qBAAqB,GAAG,CAAC,uBAAuB,IAAI,sBAAsB,IAAI,GAAG,uBAAuB,UAAU,kBAAkB,6BAA6B,2BAA2B,gCAAgC,2BAA2B,sBAAsB,GAAG,CAAC,GAAG,eAAe,GAAG,eAAe,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,CAAC;AAAA,IAC7f,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,WAAW,GAAG,2CAA2C,GAAG,IAAI,eAAe,GAAG,GAAM,sBAAsB;AACjH,QAAG,WAAW,uBAAuB,SAAS,uEAAuE,QAAQ;AAC3H,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,QAClD,CAAC,EAAE,UAAU,SAAS,4DAA4D;AAChF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,KAAK,CAAC;AAAA,QAClC,CAAC,EAAE,kBAAkB,SAAS,kEAAkE,QAAQ;AACtG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,QACpD,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,6BAA6B,IAAI,MAAM,EAAE,2BAA2B,IAAI,QAAQ,EAAE,gCAAgC,IAAI,UAAU,EAAE,2BAA2B,IAAI,uBAAuB,EAAE,wBAAwB,IAAI,oBAAoB;AAAA,MAC1P;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAkB,qBAAqB,wBAAwB,gBAAmB,iCAAiC,iBAAoB,2BAA2B;AAAA,IACjL,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,aAAa;AAAA,IAC3B;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,YAAY,CAAC,aAAa;AAAA,MAC1B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkCV,qBAAqB;AAAA,MACrB,SAAS,CAAC,eAAe,wBAAwB,gBAAgB,eAAe;AAAA,IAClF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,IAChD,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,EAClD,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,kBAAkB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,MAChD,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,IAClD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["delay"]}