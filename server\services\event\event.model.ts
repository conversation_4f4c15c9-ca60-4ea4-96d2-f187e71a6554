import { guid } from '@server/utils/utils';
import serverTime from '@services/serverTime';
import {
  AuthEventType,
  EventConfirmType,
  EventTypeNum,
} from '@share-types/auth-event.types';

type HandleFn = () => unknown;

export interface EventDetail {
  id: string;
  schedule_id: string;
  type: EventTypeNum;
  content: string;
  created_at: number;
  occurred_at: number;
}

export class AuthEvent {
  id: string;
  type: AuthEventType;

  schedule_id: string;
  entries: string[] = [];
  params: any;
  code: number; // 授权码
  confirmed: EventConfirmType = 'waiting'; // 授权状态
  handle: HandleFn | null;
  event: EventDetail;

  constructor(type: AuthEventType, event: Partial<EventDetail>) {
    this.type = type;
    this.schedule_id = event.schedule_id || '';
    this.event = {
      id: guid(),
      schedule_id: this.schedule_id,
      type: event.type as any,
      content: event.content || '',
      created_at: serverTime.now(),
      occurred_at: serverTime.timestamp(),
    };
  }

  get is_timeout() {
    const is_timeout = this.event.created_at + 60 * 15 < serverTime.now();
    if (is_timeout) {
      console.warn('auth event timeout: ', this.id);
    }
    return is_timeout;
  }

  onlineData() {}

  offlineData() {
    return {
      // sn: getRoom().sn_code,
      h_id: this.schedule_id,
      uuid: this.event.id,
      code: this.event.type,
      // s_id: this.event.session_id,
      entries: this.entries.toString(),
      content: this.event.content,
      p: JSON.stringify(this.params || {}),
      // 可选
      // d: +this.event.duration_mins,
      c: this.event.created_at,
      o: this.event.occurred_at,
    };
  }

  addHandle(handle: HandleFn) {
    this.handle = handle;
  }

  /** 授权同意处理函数 */
  async excute() {
    if (this.handle) {
      try {
        await this.handle();
      } catch (err) {
        console.error(`AuthEvent: event_id: ${this.id}`, err);
        throw err;
      }
    }
    this.handle = null;
  }
}
