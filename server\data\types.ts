import { Sequelize, ModelStatic } from 'sequelize';
import { EventModelFields } from '@data/model/event';
import { FormModelFields } from '@data/model/form';
import { LogModelFields } from '@data/model/log';
import { ProjectModelFields } from '@data/model/project';
import { RoomModelFields } from '@data/model/room';
import { ScheduleModelFields } from '@data/model/schedule';
import { SessionModelFields } from '@data/model/session';
import { SubjectModelFields } from '@data/model/subject';
import { IEventDataAccess } from './event.data';
import { IFormDataAccess } from './form.data';
import { ILogDataAccess } from './log.data';
import { IProjectDataAccess } from './project.data';
import { IRoomDataAccess } from './room.data';
import { IScheduleDataAccess } from './schedule.data';
import { ISessionDataAccess } from './session.data';
import { ISubjectDataAccess } from './subject.data';

export interface DbConnection {
  project: ModelStatic<ProjectModelFields>;
  schedule: ModelStatic<ScheduleModelFields>;
  session: ModelStatic<SessionModelFields>;
  room: ModelStatic<RoomModelFields>;
  subject: ModelStatic<SubjectModelFields>;
  form: ModelStatic<FormModelFields>;
  event: ModelStatic<EventModelFields>;
  log: ModelStatic<LogModelFields>;

  sequelize: Sequelize;
  db_path: string;
  db_name: string;
  open: () => Promise<boolean>;
  close: () => Promise<boolean>;
  encryptField: (text: string) => string;
  decryptField: (text: string) => string;
}

export interface DataAccess {
  project: IProjectDataAccess;
  schedule: IScheduleDataAccess;
  room: IRoomDataAccess;
  session: ISessionDataAccess;
  form: IFormDataAccess;
  subject: ISubjectDataAccess;
  event: IEventDataAccess;
  log: ILogDataAccess;
  loadDb: () => Promise<boolean>;
}
