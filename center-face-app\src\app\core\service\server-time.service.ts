import { Observable, BehaviorSubject, timer } from "rxjs";
import { share, map, switchMap, tap, filter, take, distinctUntilChanged } from "rxjs/operators";
import { Injectable, NgZone } from "@angular/core";
import { CloudHttpService } from "../http/cloud.http";

@Injectable({
  providedIn: "root",
})
export class ServerTimeService {
  private _is_time_synced$ = new BehaviorSubject(false); // 云端时间是否已同步
  private serverTime$!: Observable<number>;
  // serverTime以毫秒为单位
  private serverTime!: number;
  // 因为本地时间在切屏是会有很大的误差，通过deltaTime来消除这些误差
  private deltaTime = 0;

  constructor(private cloudService: CloudHttpService, private ngZone: NgZone) {}

  get is_time_synced$() {
    return this._is_time_synced$.pipe(distinctUntilChanged());
  }

  public getServerTime() {
    return this.cloudService.getCloudTime().pipe(
      tap((res) => {
          this.refreshTime(res.timestamp);
          this._is_time_synced$.next(true);
      })
    )
    // return this.http.get<ServerTime>(`/ts-api/v1/time`).pipe(
    //   tap((res) => {
    //     if (res.status === "success") {
    //       this.refreshTime(res.info.server_time);
    //     }
    //     if (res.status === "success" && res.info.is_time_synced) {
    //       this._is_time_synced$.next(true);
    //     }
    //   })
    // );
  }

  private create(): Observable<number> {
    // 每隔100ms计算一次时间
    const timeTick$ = this.getServerTime().pipe(
      switchMap(() => this.observableTimer(100)),
      map(() => {
        this.serverTime = this.absoluteTime() + this.deltaTime;
        return Math.trunc(this.serverTime / 1000);
      }),
      distinctUntilChanged(),
      map((time) => time * 1000)
    );

    // 60秒钟更新一次服务器时间
    timer(0, 60000)
      .pipe(switchMap(() => this.getServerTime()))
      .subscribe();

    return timeTick$.pipe(share());
  }

  observableTimer(ms: number, callback?: (diff: number) => void) {
    return new Observable<number>((subscriber) => {
      let counter = 0;
      let start = performance.now();
      const timeoutInstance = () => {
        const real = counter * ms;
        const ideal = performance.now() - start;
        counter++;
        const diff = ideal - real;
        subscriber.next(diff);
        if (callback) {
          callback(diff);
        }
        return setTimeout(timeoutInstance, Math.max(0, ms - diff));
      };
      let timerId: number;
      this.ngZone.runOutsideAngular(() => {
        timerId = timeoutInstance();
      });
      return function unsubscribe() {
        clearTimeout(timerId);
      };
    });
  }

  // 获得单个服务器时间值，以毫秒为单位
  public getServerTimeValue(): number {
    if (!this.serverTime$) {
      this.serverTime$ = this.create();
      console.log("create server time stream");
    }
    return this.serverTime;
  }

  // 获得服务器时间流
  public getServerTimeStream(): Observable<number> {
    if (!this.serverTime$) {
      this.serverTime$ = this.create();
      console.log("create server time stream");
    }
    return this.serverTime$;
  }

  /*
   * 输入输出都以毫秒为单位
   * 获得时间差=预期开始时间-目前时间
   * 结果为正，则为倒计时，为负，则表示玩开始比赛的时间
   * */
  public getDeltaTimeStream(startTime: number): Observable<number> {
    return this.getServerTimeStream().pipe(map((nowTime) => startTime - nowTime));
  }

  // 获取考试进程中所使用的时间
  public getUsedTimeStream(usedTime: number, lastStartTime: number): Observable<number> {
    return this.getServerTimeStream().pipe(map((nowTime) => usedTime * 1000 + (nowTime - lastStartTime * 1000)));
  }

  // ⏰ time(ms)
  public alarmClock(time: number) {
    return this.getDeltaTimeStream(time).pipe(
      map((num) => Math.ceil(num / 1000)),
      filter((num) => num <= 0),
      take(1)
    );
  }

  private refreshTime(serverTime: number) {
    this.serverTime = serverTime * 1000;
    // 通过记录服务器时间与本地时间的差值来计算时间
    this.deltaTime = this.serverTime - this.absoluteTime();
  }

  private absoluteTime() {
    return Math.trunc(window.performance.timeOrigin + window.performance.now());
  }
}
