<div class="container">
    <div class="header">
        <span class="title">
            <span class="mark"></span>
            考场列表
        </span>
        <div class="check-group">
            <nz-checkbox-wrapper  (nzOnChange)="updateStatusFilter($event)">
                <div nz-row>
                  <div nz-col>
                    <label nz-checkbox  [nzValue]="SessionStatus.notEnter" [ngModel]="statusFilter.get(SessionStatus.notEnter)"
                      >未进场</label
                    >
                  </div>
                  <div nz-col>
                    <label nz-checkbox  [nzValue]="SessionStatus.enter" [ngModel]="statusFilter.get(SessionStatus.enter)"
                      >进场中</label
                    >
                  </div>
                  <div nz-col>
                    <label nz-checkbox [nzValue]="SessionStatus.test" [ngModel]="statusFilter.get(SessionStatus.test)"
                      >考试中</label
                    >
                  </div>
                  <div nz-col>
                    <label nz-checkbox [nzValue]="SessionStatus.pause" [ngModel]="statusFilter.get(SessionStatus.pause)"
                      >暂停考试</label
                    >
                  </div>
                  <div nz-col>
                    <label nz-checkbox [nzValue]="SessionStatus.end" [ngModel]="statusFilter.get(SessionStatus.end)"
                      >考试结束</label
                    >
                  </div>
                  <div nz-col>
                    <label nz-checkbox [nzValue]="SessionStatus.centerUploaded " [ngModel]="statusFilter.get(SessionStatus.centerUploaded)"
                      >上传结果</label
                    >
                  </div>
                </div>
              </nz-checkbox-wrapper>
        </div>
    </div>
    <nz-divider></nz-divider>
    <div class="rooms">
        @for (room of rooms | listStatus: statusFilter; track $index) {
            <app-room-card [room]="room"></app-room-card>
        }
    </div>
</div>