import { ISubjectData } from '@data/subject.data';
import { ISessionData } from '@data/session.data';
import { DataAccess } from '@data/types';

interface ISchedule {
  id: string;
  start: string;
  end: string;
  test: 0 | 1;
  form_published: 0 | 1;
  password_published: 0 | 1;
  subjects: ISubjectData[];
  sessions: ISessionData[];
}
export class ScheduleService {
  dataAccess: DataAccess;
  constructor(dataAccess: DataAccess) {
    this.dataAccess = dataAccess;
  }

  async getAllSchedules() {
    const schedules = await this.dataAccess.schedule.getAllSchedules();
    const results: ISchedule[] = [];
    for (const s of schedules) {
      const r = await this.getSchedule(s.id);
      results.push(r as any);
    }
    return results;
  }

  async getSchedule(schedule_id: string) {
    const schedule = await this.dataAccess.schedule.get(schedule_id);
    const sessions = await this.dataAccess.session.getSessions({
      schedule_id,
    });
    const subjects = await this.dataAccess.subject.getAllSubjects();
    const events = await this.dataAccess.event.getEvents(
      sessions.map((s) => s.id),
    );
    const schedule_subjects = subjects.reduce((s, subject) => {
      if (sessions.find((s) => s.id === subject.session_id)) {
        s.push(subject);
      }
      return s;
    }, [] as ISubjectData[]);
    return { ...schedule, sessions, subjects: schedule_subjects, events };
  }

  async publishForm(schedule_id: string) {
    await this.dataAccess.schedule.update(
      { id: schedule_id },
      { form_published: 1 },
    );
  }

  async publishPassword(schedule_id: string) {
    await this.dataAccess.schedule.update(
      { id: schedule_id },
      { password_published: 1 },
    );
  }
}
