{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-progress.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { numberAttribute, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { __esDecorate, __runInitializers } from 'tslib';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { isNotNil, numberAttributeWithZeroFallback } from 'ng-zorro-antd/core/util';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = a0 => ({\n  $implicit: a0\n});\nfunction NzProgressComponent_ng_template_0_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"nzType\", ctx_r0.icon);\n  }\n}\nfunction NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formatter_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", formatter_r2(ctx_r0.nzPercent), \" \");\n  }\n}\nfunction NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_ng_container_0_Template, 2, 1, \"ng-container\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.formatter)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, ctx_r0.nzPercent));\n  }\n}\nfunction NzProgressComponent_ng_template_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵtemplate(1, NzProgressComponent_ng_template_0_Conditional_0_Conditional_1_Template, 1, 1, \"nz-icon\", 3)(2, NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_Template, 1, 4, \"ng-container\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional((ctx_r0.status === \"exception\" || ctx_r0.status === \"success\") && !ctx_r0.nzFormat ? 1 : 2);\n  }\n}\nfunction NzProgressComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzProgressComponent_ng_template_0_Conditional_0_Template, 3, 1, \"span\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r0.nzShowInfo ? 0 : -1);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_1_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 8);\n  }\n  if (rf & 2) {\n    const step_r3 = ctx.$implicit;\n    i0.ɵɵstyleMap(step_r3);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_1_ng_template_3_Template(rf, ctx) {}\nfunction NzProgressComponent_Conditional_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵrepeaterCreate(1, NzProgressComponent_Conditional_3_Conditional_1_For_2_Template, 1, 2, \"div\", 6, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵtemplate(3, NzProgressComponent_Conditional_3_Conditional_1_ng_template_3_Template, 0, 0, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const progressInfoTemplate_r4 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.steps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", progressInfoTemplate_r4);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.nzSuccessPercent, \"%\")(\"border-radius\", ctx_r0.nzStrokeLinecap === \"round\" ? \"100px\" : \"0\")(\"height\", ctx_r0.strokeWidth, \"px\");\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_2_ng_template_4_Template(rf, ctx) {}\nfunction NzProgressComponent_Conditional_3_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10);\n    i0.ɵɵelement(2, \"div\", 11);\n    i0.ɵɵtemplate(3, NzProgressComponent_Conditional_3_Conditional_2_Conditional_3_Template, 1, 6, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, NzProgressComponent_Conditional_3_Conditional_2_ng_template_4_Template, 0, 0, \"ng-template\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const progressInfoTemplate_r4 = i0.ɵɵreference(1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.nzPercent, \"%\")(\"border-radius\", ctx_r0.nzStrokeLinecap === \"round\" ? \"100px\" : \"0\")(\"background\", !ctx_r0.isGradient ? ctx_r0.nzStrokeColor : null)(\"background-image\", ctx_r0.isGradient ? ctx_r0.lineGradient : null)(\"height\", ctx_r0.strokeWidth, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.nzSuccessPercent || ctx_r0.nzSuccessPercent === 0 ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", progressInfoTemplate_r4);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, NzProgressComponent_Conditional_3_Conditional_1_Template, 4, 1, \"div\", 5)(2, NzProgressComponent_Conditional_3_Conditional_2_Template, 5, 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.isSteps ? 1 : 2);\n  }\n}\nfunction NzProgressComponent_Conditional_4_Conditional_2_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"stop\");\n  }\n  if (rf & 2) {\n    const i_r5 = ctx.$implicit;\n    i0.ɵɵattribute(\"offset\", i_r5.offset)(\"stop-color\", i_r5.color);\n  }\n}\nfunction NzProgressComponent_Conditional_4_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"defs\")(1, \"linearGradient\", 17);\n    i0.ɵɵrepeaterCreate(2, NzProgressComponent_Conditional_4_Conditional_2_For_3_Template, 1, 2, \":svg:stop\", null, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"gradient-\" + ctx_r0.gradientId);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.circleGradient);\n  }\n}\nfunction NzProgressComponent_Conditional_4_For_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 18);\n  }\n  if (rf & 2) {\n    const p_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(p_r6.strokePathStyle);\n    i0.ɵɵattribute(\"d\", ctx_r0.pathString)(\"stroke-linecap\", ctx_r0.nzStrokeLinecap)(\"stroke\", p_r6.stroke)(\"stroke-width\", ctx_r0.nzPercent ? ctx_r0.strokeWidth : 0);\n  }\n}\nfunction NzProgressComponent_Conditional_4_ng_template_6_Template(rf, ctx) {}\nfunction NzProgressComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 14);\n    i0.ɵɵtemplate(2, NzProgressComponent_Conditional_4_Conditional_2_Template, 4, 1, \":svg:defs\");\n    i0.ɵɵelement(3, \"path\", 15);\n    i0.ɵɵrepeaterCreate(4, NzProgressComponent_Conditional_4_For_5_Template, 1, 6, \":svg:path\", 16, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, NzProgressComponent_Conditional_4_ng_template_6_Template, 0, 0, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const progressInfoTemplate_r4 = i0.ɵɵreference(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.nzWidth, \"px\")(\"height\", ctx_r0.nzWidth, \"px\")(\"font-size\", ctx_r0.nzWidth * 0.15 + 6, \"px\");\n    i0.ɵɵclassProp(\"ant-progress-circle-gradient\", ctx_r0.isGradient);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.isGradient ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(ctx_r0.trailPathStyle);\n    i0.ɵɵattribute(\"stroke-width\", ctx_r0.strokeWidth)(\"d\", ctx_r0.pathString);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.progressCirclePath);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", progressInfoTemplate_r4);\n  }\n}\nfunction stripPercentToNumber(percent) {\n  return +percent.replace('%', '');\n}\nconst sortGradient = gradients => {\n  let tempArr = [];\n  Object.keys(gradients).forEach(key => {\n    const value = gradients[key];\n    const formatKey = stripPercentToNumber(key);\n    if (!isNaN(formatKey)) {\n      tempArr.push({\n        key: formatKey,\n        value\n      });\n    }\n  });\n  tempArr = tempArr.sort((a, b) => a.key - b.key);\n  return tempArr;\n};\nconst handleCircleGradient = strokeColor => sortGradient(strokeColor).map(({\n  key,\n  value\n}) => ({\n  offset: `${key}%`,\n  color: value\n}));\nconst handleLinearGradient = strokeColor => {\n  const {\n    from = '#1890ff',\n    to = '#1890ff',\n    direction = 'to right',\n    ...rest\n  } = strokeColor;\n  if (Object.keys(rest).length !== 0) {\n    const sortedGradients = sortGradient(rest).map(({\n      key,\n      value\n    }) => `${value} ${key}%`).join(', ');\n    return `linear-gradient(${direction}, ${sortedGradients})`;\n  }\n  return `linear-gradient(${direction}, ${from}, ${to})`;\n};\nlet gradientIdSeed = 0;\nconst NZ_CONFIG_MODULE_NAME = 'progress';\nconst statusIconNameMap = new Map([['success', 'check'], ['exception', 'close']]);\nconst statusColorMap = new Map([['normal', '#108ee9'], ['exception', '#ff5500'], ['success', '#87d068']]);\nconst defaultFormatter = p => `${p}%`;\nlet NzProgressComponent = (() => {\n  let _nzShowInfo_decorators;\n  let _nzShowInfo_initializers = [];\n  let _nzShowInfo_extraInitializers = [];\n  let _nzStrokeColor_decorators;\n  let _nzStrokeColor_initializers = [];\n  let _nzStrokeColor_extraInitializers = [];\n  let _nzSize_decorators;\n  let _nzSize_initializers = [];\n  let _nzSize_extraInitializers = [];\n  let _nzStrokeWidth_decorators;\n  let _nzStrokeWidth_initializers = [];\n  let _nzStrokeWidth_extraInitializers = [];\n  let _nzGapDegree_decorators;\n  let _nzGapDegree_initializers = [];\n  let _nzGapDegree_extraInitializers = [];\n  let _nzGapPosition_decorators;\n  let _nzGapPosition_initializers = [];\n  let _nzGapPosition_extraInitializers = [];\n  let _nzStrokeLinecap_decorators;\n  let _nzStrokeLinecap_initializers = [];\n  let _nzStrokeLinecap_extraInitializers = [];\n  return class NzProgressComponent {\n    static {\n      const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(null) : void 0;\n      _nzShowInfo_decorators = [WithConfig()];\n      _nzStrokeColor_decorators = [WithConfig()];\n      _nzSize_decorators = [WithConfig()];\n      _nzStrokeWidth_decorators = [WithConfig()];\n      _nzGapDegree_decorators = [WithConfig()];\n      _nzGapPosition_decorators = [WithConfig()];\n      _nzStrokeLinecap_decorators = [WithConfig()];\n      __esDecorate(null, null, _nzShowInfo_decorators, {\n        kind: \"field\",\n        name: \"nzShowInfo\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzShowInfo\" in obj,\n          get: obj => obj.nzShowInfo,\n          set: (obj, value) => {\n            obj.nzShowInfo = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzShowInfo_initializers, _nzShowInfo_extraInitializers);\n      __esDecorate(null, null, _nzStrokeColor_decorators, {\n        kind: \"field\",\n        name: \"nzStrokeColor\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzStrokeColor\" in obj,\n          get: obj => obj.nzStrokeColor,\n          set: (obj, value) => {\n            obj.nzStrokeColor = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzStrokeColor_initializers, _nzStrokeColor_extraInitializers);\n      __esDecorate(null, null, _nzSize_decorators, {\n        kind: \"field\",\n        name: \"nzSize\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzSize\" in obj,\n          get: obj => obj.nzSize,\n          set: (obj, value) => {\n            obj.nzSize = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzSize_initializers, _nzSize_extraInitializers);\n      __esDecorate(null, null, _nzStrokeWidth_decorators, {\n        kind: \"field\",\n        name: \"nzStrokeWidth\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzStrokeWidth\" in obj,\n          get: obj => obj.nzStrokeWidth,\n          set: (obj, value) => {\n            obj.nzStrokeWidth = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzStrokeWidth_initializers, _nzStrokeWidth_extraInitializers);\n      __esDecorate(null, null, _nzGapDegree_decorators, {\n        kind: \"field\",\n        name: \"nzGapDegree\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzGapDegree\" in obj,\n          get: obj => obj.nzGapDegree,\n          set: (obj, value) => {\n            obj.nzGapDegree = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzGapDegree_initializers, _nzGapDegree_extraInitializers);\n      __esDecorate(null, null, _nzGapPosition_decorators, {\n        kind: \"field\",\n        name: \"nzGapPosition\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzGapPosition\" in obj,\n          get: obj => obj.nzGapPosition,\n          set: (obj, value) => {\n            obj.nzGapPosition = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzGapPosition_initializers, _nzGapPosition_extraInitializers);\n      __esDecorate(null, null, _nzStrokeLinecap_decorators, {\n        kind: \"field\",\n        name: \"nzStrokeLinecap\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzStrokeLinecap\" in obj,\n          get: obj => obj.nzStrokeLinecap,\n          set: (obj, value) => {\n            obj.nzStrokeLinecap = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzStrokeLinecap_initializers, _nzStrokeLinecap_extraInitializers);\n      if (_metadata) Object.defineProperty(this, Symbol.metadata, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value: _metadata\n      });\n    }\n    cdr;\n    nzConfigService;\n    directionality;\n    _nzModuleName = NZ_CONFIG_MODULE_NAME;\n    nzShowInfo = __runInitializers(this, _nzShowInfo_initializers, true);\n    nzWidth = (__runInitializers(this, _nzShowInfo_extraInitializers), 132);\n    nzStrokeColor = __runInitializers(this, _nzStrokeColor_initializers, undefined);\n    nzSize = (__runInitializers(this, _nzStrokeColor_extraInitializers), __runInitializers(this, _nzSize_initializers, 'default'));\n    nzFormat = __runInitializers(this, _nzSize_extraInitializers);\n    nzSuccessPercent;\n    nzPercent = 0;\n    nzStrokeWidth = __runInitializers(this, _nzStrokeWidth_initializers, void 0);\n    nzGapDegree = (__runInitializers(this, _nzStrokeWidth_extraInitializers), __runInitializers(this, _nzGapDegree_initializers, void 0));\n    nzStatus = __runInitializers(this, _nzGapDegree_extraInitializers);\n    nzType = 'line';\n    nzGapPosition = __runInitializers(this, _nzGapPosition_initializers, 'top');\n    nzStrokeLinecap = (__runInitializers(this, _nzGapPosition_extraInitializers), __runInitializers(this, _nzStrokeLinecap_initializers, 'round'));\n    nzSteps = (__runInitializers(this, _nzStrokeLinecap_extraInitializers), 0);\n    steps = [];\n    /** Gradient style when `nzType` is `line`. */\n    lineGradient = null;\n    /** If user uses gradient color. */\n    isGradient = false;\n    /** If the linear progress is a step progress. */\n    isSteps = false;\n    /**\n     * Each progress whose `nzType` is circle or dashboard should have unique id to\n     * define `<linearGradient>`.\n     */\n    gradientId = gradientIdSeed++;\n    /** Paths to rendered in the template. */\n    progressCirclePath = [];\n    circleGradient;\n    trailPathStyle = null;\n    pathString;\n    icon;\n    dir = 'ltr';\n    get formatter() {\n      return this.nzFormat || defaultFormatter;\n    }\n    get status() {\n      return this.nzStatus || this.inferredStatus;\n    }\n    get strokeWidth() {\n      return this.nzStrokeWidth || (this.nzType === 'line' && this.nzSize !== 'small' ? 8 : 6);\n    }\n    get isCircleStyle() {\n      return this.nzType === 'circle' || this.nzType === 'dashboard';\n    }\n    cachedStatus = 'normal';\n    inferredStatus = 'normal';\n    destroy$ = new Subject();\n    constructor(cdr, nzConfigService, directionality) {\n      this.cdr = cdr;\n      this.nzConfigService = nzConfigService;\n      this.directionality = directionality;\n    }\n    ngOnChanges(changes) {\n      const {\n        nzSteps,\n        nzGapPosition,\n        nzStrokeLinecap,\n        nzStrokeColor,\n        nzGapDegree,\n        nzType,\n        nzStatus,\n        nzPercent,\n        nzSuccessPercent,\n        nzStrokeWidth\n      } = changes;\n      if (nzStatus) {\n        this.cachedStatus = this.nzStatus || this.cachedStatus;\n      }\n      if (nzPercent || nzSuccessPercent) {\n        const fillAll = parseInt(this.nzPercent.toString(), 10) >= 100;\n        if (fillAll) {\n          if (isNotNil(this.nzSuccessPercent) && this.nzSuccessPercent >= 100 || this.nzSuccessPercent === undefined) {\n            this.inferredStatus = 'success';\n          }\n        } else {\n          this.inferredStatus = this.cachedStatus;\n        }\n      }\n      if (nzStatus || nzPercent || nzSuccessPercent || nzStrokeColor) {\n        this.updateIcon();\n      }\n      if (nzStrokeColor) {\n        this.setStrokeColor();\n      }\n      if (nzGapPosition || nzStrokeLinecap || nzGapDegree || nzType || nzPercent || nzStrokeColor || nzStrokeColor) {\n        this.getCirclePaths();\n      }\n      if (nzPercent || nzSteps || nzStrokeWidth) {\n        this.isSteps = this.nzSteps > 0;\n        if (this.isSteps) {\n          this.getSteps();\n        }\n      }\n    }\n    ngOnInit() {\n      this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.updateIcon();\n        this.setStrokeColor();\n        this.getCirclePaths();\n      });\n      this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n        this.dir = direction;\n        this.cdr.detectChanges();\n      });\n      this.dir = this.directionality.value;\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    updateIcon() {\n      const ret = statusIconNameMap.get(this.status);\n      this.icon = ret ? ret + (this.isCircleStyle ? '-o' : '-circle-fill') : '';\n    }\n    /**\n     * Calculate step render configs.\n     */\n    getSteps() {\n      const current = Math.floor(this.nzSteps * (this.nzPercent / 100));\n      const stepWidth = this.nzSize === 'small' ? 2 : 14;\n      const steps = [];\n      for (let i = 0; i < this.nzSteps; i++) {\n        let color;\n        if (i <= current - 1) {\n          color = this.nzStrokeColor;\n        }\n        const stepStyle = {\n          backgroundColor: `${color}`,\n          width: `${stepWidth}px`,\n          height: `${this.strokeWidth}px`\n        };\n        steps.push(stepStyle);\n      }\n      this.steps = steps;\n    }\n    /**\n     * Calculate paths when the type is circle or dashboard.\n     */\n    getCirclePaths() {\n      if (!this.isCircleStyle) {\n        return;\n      }\n      const values = isNotNil(this.nzSuccessPercent) ? [this.nzSuccessPercent, this.nzPercent] : [this.nzPercent];\n      // Calculate shared styles.\n      const radius = 50 - this.strokeWidth / 2;\n      const gapPosition = this.nzGapPosition || (this.nzType === 'circle' ? 'top' : 'bottom');\n      const len = Math.PI * 2 * radius;\n      const gapDegree = this.nzGapDegree || (this.nzType === 'circle' ? 0 : 75);\n      let beginPositionX = 0;\n      let beginPositionY = -radius;\n      let endPositionX = 0;\n      let endPositionY = radius * -2;\n      switch (gapPosition) {\n        case 'left':\n          beginPositionX = -radius;\n          beginPositionY = 0;\n          endPositionX = radius * 2;\n          endPositionY = 0;\n          break;\n        case 'right':\n          beginPositionX = radius;\n          beginPositionY = 0;\n          endPositionX = radius * -2;\n          endPositionY = 0;\n          break;\n        case 'bottom':\n          beginPositionY = radius;\n          endPositionY = radius * 2;\n          break;\n        default:\n      }\n      this.pathString = `M 50,50 m ${beginPositionX},${beginPositionY}\n       a ${radius},${radius} 0 1 1 ${endPositionX},${-endPositionY}\n       a ${radius},${radius} 0 1 1 ${-endPositionX},${endPositionY}`;\n      this.trailPathStyle = {\n        strokeDasharray: `${len - gapDegree}px ${len}px`,\n        strokeDashoffset: `-${gapDegree / 2}px`,\n        transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s'\n      };\n      // Calculate styles for each path.\n      this.progressCirclePath = values.map((value, index) => {\n        const isSuccessPercent = values.length === 2 && index === 0;\n        return {\n          stroke: this.isGradient && !isSuccessPercent ? `url(#gradient-${this.gradientId})` : null,\n          strokePathStyle: {\n            stroke: !this.isGradient ? isSuccessPercent ? statusColorMap.get('success') : this.nzStrokeColor : null,\n            transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s',\n            strokeDasharray: `${(value || 0) / 100 * (len - gapDegree)}px ${len}px`,\n            strokeDashoffset: `-${gapDegree / 2}px`\n          }\n        };\n      }).reverse();\n    }\n    setStrokeColor() {\n      const color = this.nzStrokeColor;\n      const isGradient = this.isGradient = !!color && typeof color !== 'string';\n      if (isGradient && !this.isCircleStyle) {\n        this.lineGradient = handleLinearGradient(color);\n      } else if (isGradient && this.isCircleStyle) {\n        this.circleGradient = handleCircleGradient(this.nzStrokeColor);\n      } else {\n        this.lineGradient = null;\n        this.circleGradient = [];\n      }\n    }\n    static ɵfac = function NzProgressComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NzProgressComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i2.Directionality));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzProgressComponent,\n      selectors: [[\"nz-progress\"]],\n      inputs: {\n        nzShowInfo: \"nzShowInfo\",\n        nzWidth: \"nzWidth\",\n        nzStrokeColor: \"nzStrokeColor\",\n        nzSize: \"nzSize\",\n        nzFormat: \"nzFormat\",\n        nzSuccessPercent: [2, \"nzSuccessPercent\", \"nzSuccessPercent\", numberAttributeWithZeroFallback],\n        nzPercent: [2, \"nzPercent\", \"nzPercent\", numberAttribute],\n        nzStrokeWidth: [2, \"nzStrokeWidth\", \"nzStrokeWidth\", numberAttributeWithZeroFallback],\n        nzGapDegree: [2, \"nzGapDegree\", \"nzGapDegree\", numberAttributeWithZeroFallback],\n        nzStatus: \"nzStatus\",\n        nzType: \"nzType\",\n        nzGapPosition: \"nzGapPosition\",\n        nzStrokeLinecap: \"nzStrokeLinecap\",\n        nzSteps: [2, \"nzSteps\", \"nzSteps\", numberAttribute]\n      },\n      exportAs: [\"nzProgress\"],\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 5,\n      vars: 18,\n      consts: [[\"progressInfoTemplate\", \"\"], [1, \"ant-progress-inner\", 3, \"width\", \"height\", \"fontSize\", \"ant-progress-circle-gradient\"], [1, \"ant-progress-text\"], [3, \"nzType\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"], [1, \"ant-progress-steps-outer\"], [1, \"ant-progress-steps-item\", 3, \"style\"], [3, \"ngTemplateOutlet\"], [1, \"ant-progress-steps-item\"], [1, \"ant-progress-outer\"], [1, \"ant-progress-inner\"], [1, \"ant-progress-bg\"], [1, \"ant-progress-success-bg\", 3, \"width\", \"border-radius\", \"height\"], [1, \"ant-progress-success-bg\"], [\"viewBox\", \"0 0 100 100\", 1, \"ant-progress-circle\"], [\"stroke\", \"#f3f3f3\", \"fill-opacity\", \"0\", 1, \"ant-progress-circle-trail\"], [\"fill-opacity\", \"0\", 1, \"ant-progress-circle-path\", 3, \"style\"], [\"x1\", \"100%\", \"y1\", \"0%\", \"x2\", \"0%\", \"y2\", \"0%\", 3, \"id\"], [\"fill-opacity\", \"0\", 1, \"ant-progress-circle-path\"]],\n      template: function NzProgressComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzProgressComponent_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(2, \"div\");\n          i0.ɵɵtemplate(3, NzProgressComponent_Conditional_3_Template, 3, 1, \"div\")(4, NzProgressComponent_Conditional_4_Template, 7, 14, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(\"ant-progress ant-progress-status-\" + ctx.status);\n          i0.ɵɵclassProp(\"ant-progress-line\", ctx.nzType === \"line\")(\"ant-progress-small\", ctx.nzSize === \"small\")(\"ant-progress-default\", ctx.nzSize === \"default\")(\"ant-progress-show-info\", ctx.nzShowInfo)(\"ant-progress-circle\", ctx.isCircleStyle)(\"ant-progress-steps\", ctx.isSteps)(\"ant-progress-rtl\", ctx.dir === \"rtl\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.nzType === \"line\" ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.isCircleStyle ? 4 : -1);\n        }\n      },\n      dependencies: [NzIconModule, i3.NzIconDirective, NzOutletModule, i4.NzStringTemplateOutletDirective, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  };\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzProgressComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-progress',\n      exportAs: 'nzProgress',\n      preserveWhitespaces: false,\n      imports: [NzIconModule, NzOutletModule, NgTemplateOutlet],\n      template: `\n    <ng-template #progressInfoTemplate>\n      @if (nzShowInfo) {\n        <span class=\"ant-progress-text\">\n          @if ((status === 'exception' || status === 'success') && !nzFormat) {\n            <nz-icon [nzType]=\"icon\" />\n          } @else {\n            <ng-container *nzStringTemplateOutlet=\"formatter; context: { $implicit: nzPercent }; let formatter\">\n              {{ formatter(nzPercent) }}\n            </ng-container>\n          }\n        </span>\n      }\n    </ng-template>\n\n    <div\n      [class]=\"'ant-progress ant-progress-status-' + status\"\n      [class.ant-progress-line]=\"nzType === 'line'\"\n      [class.ant-progress-small]=\"nzSize === 'small'\"\n      [class.ant-progress-default]=\"nzSize === 'default'\"\n      [class.ant-progress-show-info]=\"nzShowInfo\"\n      [class.ant-progress-circle]=\"isCircleStyle\"\n      [class.ant-progress-steps]=\"isSteps\"\n      [class.ant-progress-rtl]=\"dir === 'rtl'\"\n    >\n      @if (nzType === 'line') {\n        <div>\n          <!-- normal line style -->\n          @if (isSteps) {\n            <div class=\"ant-progress-steps-outer\">\n              @for (step of steps; track $index) {\n                <div class=\"ant-progress-steps-item\" [style]=\"step\"></div>\n              }\n              <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n            </div>\n          } @else {\n            <div class=\"ant-progress-outer\">\n              <div class=\"ant-progress-inner\">\n                <div\n                  class=\"ant-progress-bg\"\n                  [style.width.%]=\"nzPercent\"\n                  [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                  [style.background]=\"!isGradient ? nzStrokeColor : null\"\n                  [style.background-image]=\"isGradient ? lineGradient : null\"\n                  [style.height.px]=\"strokeWidth\"\n                ></div>\n                @if (nzSuccessPercent || nzSuccessPercent === 0) {\n                  <div\n                    class=\"ant-progress-success-bg\"\n                    [style.width.%]=\"nzSuccessPercent\"\n                    [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                    [style.height.px]=\"strokeWidth\"\n                  ></div>\n                }\n              </div>\n            </div>\n            <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n          }\n        </div>\n      }\n      <!-- line progress -->\n\n      <!-- circle / dashboard progress -->\n\n      @if (isCircleStyle) {\n        <div\n          [style.width.px]=\"this.nzWidth\"\n          [style.height.px]=\"this.nzWidth\"\n          [style.fontSize.px]=\"this.nzWidth * 0.15 + 6\"\n          class=\"ant-progress-inner\"\n          [class.ant-progress-circle-gradient]=\"isGradient\"\n        >\n          <svg class=\"ant-progress-circle \" viewBox=\"0 0 100 100\">\n            @if (isGradient) {\n              <defs>\n                <linearGradient [id]=\"'gradient-' + gradientId\" x1=\"100%\" y1=\"0%\" x2=\"0%\" y2=\"0%\">\n                  @for (i of circleGradient; track $index) {\n                    <stop [attr.offset]=\"i.offset\" [attr.stop-color]=\"i.color\"></stop>\n                  }\n                </linearGradient>\n              </defs>\n            }\n\n            <path\n              class=\"ant-progress-circle-trail\"\n              stroke=\"#f3f3f3\"\n              fill-opacity=\"0\"\n              [attr.stroke-width]=\"strokeWidth\"\n              [attr.d]=\"pathString\"\n              [style]=\"trailPathStyle\"\n            ></path>\n            @for (p of progressCirclePath; track $index) {\n              <path\n                class=\"ant-progress-circle-path\"\n                fill-opacity=\"0\"\n                [attr.d]=\"pathString\"\n                [attr.stroke-linecap]=\"nzStrokeLinecap\"\n                [attr.stroke]=\"p.stroke\"\n                [attr.stroke-width]=\"nzPercent ? strokeWidth : 0\"\n                [style]=\"p.strokePathStyle\"\n              ></path>\n            }\n          </svg>\n          <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n        </div>\n      }\n    </div>\n  `\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.NzConfigService\n  }, {\n    type: i2.Directionality\n  }], {\n    nzShowInfo: [{\n      type: Input\n    }],\n    nzWidth: [{\n      type: Input\n    }],\n    nzStrokeColor: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzFormat: [{\n      type: Input\n    }],\n    nzSuccessPercent: [{\n      type: Input,\n      args: [{\n        transform: numberAttributeWithZeroFallback\n      }]\n    }],\n    nzPercent: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    nzStrokeWidth: [{\n      type: Input,\n      args: [{\n        transform: numberAttributeWithZeroFallback\n      }]\n    }],\n    nzGapDegree: [{\n      type: Input,\n      args: [{\n        transform: numberAttributeWithZeroFallback\n      }]\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzGapPosition: [{\n      type: Input\n    }],\n    nzStrokeLinecap: [{\n      type: Input\n    }],\n    nzSteps: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzProgressModule {\n  static ɵfac = function NzProgressModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzProgressModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzProgressModule,\n    imports: [NzProgressComponent],\n    exports: [NzProgressComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzProgressComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzProgressModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzProgressComponent],\n      exports: [NzProgressComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzProgressComponent, NzProgressModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,CAAC;AAAA,EAC9B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,IAAI;AAAA,EACrC;AACF;AACA,SAAS,sFAAsF,IAAI,KAAK;AACtG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,aAAa,OAAO,SAAS,GAAG,GAAG;AAAA,EAChE;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uFAAuF,GAAG,GAAG,gBAAgB,CAAC;AAAA,EACjI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,0BAA0B,OAAO,SAAS,EAAE,iCAAoC,gBAAgB,GAAG,KAAK,OAAO,SAAS,CAAC;AAAA,EACzI;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,WAAW,CAAC,EAAE,GAAG,wEAAwE,GAAG,GAAG,cAAc;AAC5M,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,eAAe,OAAO,WAAW,eAAe,OAAO,WAAW,cAAc,CAAC,OAAO,WAAW,IAAI,CAAC;AAAA,EAC7G;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,QAAQ,CAAC;AAAA,EAC5F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,aAAa,IAAI,EAAE;AAAA,EAC7C;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,OAAO;AAAA,EACvB;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AAAC;AAC1F,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,iBAAiB,GAAG,gEAAgE,GAAG,GAAG,OAAO,GAAM,sBAAsB;AAChI,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,eAAe,CAAC;AAC/G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,0BAA6B,YAAY,CAAC;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,KAAK;AAC1B,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,uBAAuB;AAAA,EAC3D;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,OAAO,kBAAkB,GAAG,EAAE,iBAAiB,OAAO,oBAAoB,UAAU,UAAU,GAAG,EAAE,UAAU,OAAO,aAAa,IAAI;AAAA,EAC/J;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AAAC;AAC1F,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,EAAE;AAC3C,IAAG,UAAU,GAAG,OAAO,EAAE;AACzB,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,OAAO,EAAE;AACxG,IAAG,aAAa,EAAE;AAClB,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,eAAe,CAAC;AAAA,EACjH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,0BAA6B,YAAY,CAAC;AAChD,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,SAAS,OAAO,WAAW,GAAG,EAAE,iBAAiB,OAAO,oBAAoB,UAAU,UAAU,GAAG,EAAE,cAAc,CAAC,OAAO,aAAa,OAAO,gBAAgB,IAAI,EAAE,oBAAoB,OAAO,aAAa,OAAO,eAAe,IAAI,EAAE,UAAU,OAAO,aAAa,IAAI;AAC1R,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,oBAAoB,OAAO,qBAAqB,IAAI,IAAI,EAAE;AAClF,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,uBAAuB;AAAA,EAC3D;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,0DAA0D,GAAG,EAAE;AAC7J,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,UAAU,IAAI,CAAC;AAAA,EACzC;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,UAAU,GAAG,MAAM;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,IAAG,YAAY,UAAU,KAAK,MAAM,EAAE,cAAc,KAAK,KAAK;AAAA,EAChE;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,MAAM,EAAE,GAAG,kBAAkB,EAAE;AACpD,IAAG,iBAAiB,GAAG,gEAAgE,GAAG,GAAG,aAAa,MAAS,sBAAsB;AACzI,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,MAAM,cAAc,OAAO,UAAU;AACnD,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,cAAc;AAAA,EACrC;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,KAAK,eAAe;AAClC,IAAG,YAAY,KAAK,OAAO,UAAU,EAAE,kBAAkB,OAAO,eAAe,EAAE,UAAU,KAAK,MAAM,EAAE,gBAAgB,OAAO,YAAY,OAAO,cAAc,CAAC;AAAA,EACnK;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AAAC;AAC5E,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,WAAW;AAC5F,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,iBAAiB,GAAG,kDAAkD,GAAG,GAAG,aAAa,IAAO,sBAAsB;AACzH,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,eAAe,CAAC;AACjG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,0BAA6B,YAAY,CAAC;AAChD,IAAG,YAAY,SAAS,OAAO,SAAS,IAAI,EAAE,UAAU,OAAO,SAAS,IAAI,EAAE,aAAa,OAAO,UAAU,OAAO,GAAG,IAAI;AAC1H,IAAG,YAAY,gCAAgC,OAAO,UAAU;AAChE,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,aAAa,IAAI,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,cAAc;AACnC,IAAG,YAAY,gBAAgB,OAAO,WAAW,EAAE,KAAK,OAAO,UAAU;AACzE,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,kBAAkB;AACvC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,uBAAuB;AAAA,EAC3D;AACF;AACA,SAAS,qBAAqB,SAAS;AACrC,SAAO,CAAC,QAAQ,QAAQ,KAAK,EAAE;AACjC;AACA,IAAM,eAAe,eAAa;AAChC,MAAI,UAAU,CAAC;AACf,SAAO,KAAK,SAAS,EAAE,QAAQ,SAAO;AACpC,UAAM,QAAQ,UAAU,GAAG;AAC3B,UAAM,YAAY,qBAAqB,GAAG;AAC1C,QAAI,CAAC,MAAM,SAAS,GAAG;AACrB,cAAQ,KAAK;AAAA,QACX,KAAK;AAAA,QACL;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,YAAU,QAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG;AAC9C,SAAO;AACT;AACA,IAAM,uBAAuB,iBAAe,aAAa,WAAW,EAAE,IAAI,CAAC;AAAA,EACzE;AAAA,EACA;AACF,OAAO;AAAA,EACL,QAAQ,GAAG,GAAG;AAAA,EACd,OAAO;AACT,EAAE;AACF,IAAM,uBAAuB,iBAAe;AAC1C,QAKI,kBAJF;AAAA,WAAO;AAAA,IACP,KAAK;AAAA,IACL,YAAY;AAAA,EA5OhB,IA8OM,IADC,iBACD,IADC;AAAA,IAHH;AAAA,IACA;AAAA,IACA;AAAA;AAGF,MAAI,OAAO,KAAK,IAAI,EAAE,WAAW,GAAG;AAClC,UAAM,kBAAkB,aAAa,IAAI,EAAE,IAAI,CAAC;AAAA,MAC9C;AAAA,MACA;AAAA,IACF,MAAM,GAAG,KAAK,IAAI,GAAG,GAAG,EAAE,KAAK,IAAI;AACnC,WAAO,mBAAmB,SAAS,KAAK,eAAe;AAAA,EACzD;AACA,SAAO,mBAAmB,SAAS,KAAK,IAAI,KAAK,EAAE;AACrD;AACA,IAAI,iBAAiB;AACrB,IAAM,wBAAwB;AAC9B,IAAM,oBAAoB,oBAAI,IAAI,CAAC,CAAC,WAAW,OAAO,GAAG,CAAC,aAAa,OAAO,CAAC,CAAC;AAChF,IAAM,iBAAiB,oBAAI,IAAI,CAAC,CAAC,UAAU,SAAS,GAAG,CAAC,aAAa,SAAS,GAAG,CAAC,WAAW,SAAS,CAAC,CAAC;AACxG,IAAM,mBAAmB,OAAK,GAAG,CAAC;AAClC,IAAI,uBAAuB,MAAM;AAC/B,MAAI;AACJ,MAAI,2BAA2B,CAAC;AAChC,MAAI,gCAAgC,CAAC;AACrC,MAAI;AACJ,MAAI,8BAA8B,CAAC;AACnC,MAAI,mCAAmC,CAAC;AACxC,MAAI;AACJ,MAAI,uBAAuB,CAAC;AAC5B,MAAI,4BAA4B,CAAC;AACjC,MAAI;AACJ,MAAI,8BAA8B,CAAC;AACnC,MAAI,mCAAmC,CAAC;AACxC,MAAI;AACJ,MAAI,4BAA4B,CAAC;AACjC,MAAI,iCAAiC,CAAC;AACtC,MAAI;AACJ,MAAI,8BAA8B,CAAC;AACnC,MAAI,mCAAmC,CAAC;AACxC,MAAI;AACJ,MAAI,gCAAgC,CAAC;AACrC,MAAI,qCAAqC,CAAC;AAC1C,SAAO,MAAMA,qBAAoB;AAAA,IAC/B,OAAO;AACL,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,uBAAO,OAAO,IAAI,IAAI;AAC1F,+BAAyB,CAAC,WAAW,CAAC;AACtC,kCAA4B,CAAC,WAAW,CAAC;AACzC,2BAAqB,CAAC,WAAW,CAAC;AAClC,kCAA4B,CAAC,WAAW,CAAC;AACzC,gCAA0B,CAAC,WAAW,CAAC;AACvC,kCAA4B,CAAC,WAAW,CAAC;AACzC,oCAA8B,CAAC,WAAW,CAAC;AAC3C,mBAAa,MAAM,MAAM,wBAAwB;AAAA,QAC/C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,gBAAgB;AAAA,UAC5B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,aAAa;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,0BAA0B,6BAA6B;AAC1D,mBAAa,MAAM,MAAM,2BAA2B;AAAA,QAClD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,mBAAmB;AAAA,UAC/B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,gBAAgB;AAAA,UACtB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,6BAA6B,gCAAgC;AAChE,mBAAa,MAAM,MAAM,oBAAoB;AAAA,QAC3C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,YAAY;AAAA,UACxB,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,SAAS;AAAA,UACf;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,sBAAsB,yBAAyB;AAClD,mBAAa,MAAM,MAAM,2BAA2B;AAAA,QAClD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,mBAAmB;AAAA,UAC/B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,gBAAgB;AAAA,UACtB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,6BAA6B,gCAAgC;AAChE,mBAAa,MAAM,MAAM,yBAAyB;AAAA,QAChD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,iBAAiB;AAAA,UAC7B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,cAAc;AAAA,UACpB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,2BAA2B,8BAA8B;AAC5D,mBAAa,MAAM,MAAM,2BAA2B;AAAA,QAClD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,mBAAmB;AAAA,UAC/B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,gBAAgB;AAAA,UACtB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,6BAA6B,gCAAgC;AAChE,mBAAa,MAAM,MAAM,6BAA6B;AAAA,QACpD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,qBAAqB;AAAA,UACjC,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,kBAAkB;AAAA,UACxB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,+BAA+B,kCAAkC;AACpE,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU;AAAA,QAC1D,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,aAAa,kBAAkB,MAAM,0BAA0B,IAAI;AAAA,IACnE,WAAW,kBAAkB,MAAM,6BAA6B,GAAG;AAAA,IACnE,gBAAgB,kBAAkB,MAAM,6BAA6B,MAAS;AAAA,IAC9E,UAAU,kBAAkB,MAAM,gCAAgC,GAAG,kBAAkB,MAAM,sBAAsB,SAAS;AAAA,IAC5H,WAAW,kBAAkB,MAAM,yBAAyB;AAAA,IAC5D;AAAA,IACA,YAAY;AAAA,IACZ,gBAAgB,kBAAkB,MAAM,6BAA6B,MAAM;AAAA,IAC3E,eAAe,kBAAkB,MAAM,gCAAgC,GAAG,kBAAkB,MAAM,2BAA2B,MAAM;AAAA,IACnI,WAAW,kBAAkB,MAAM,8BAA8B;AAAA,IACjE,SAAS;AAAA,IACT,gBAAgB,kBAAkB,MAAM,6BAA6B,KAAK;AAAA,IAC1E,mBAAmB,kBAAkB,MAAM,gCAAgC,GAAG,kBAAkB,MAAM,+BAA+B,OAAO;AAAA,IAC5I,WAAW,kBAAkB,MAAM,kCAAkC,GAAG;AAAA,IACxE,QAAQ,CAAC;AAAA;AAAA,IAET,eAAe;AAAA;AAAA,IAEf,aAAa;AAAA;AAAA,IAEb,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,IAKV,aAAa;AAAA;AAAA,IAEb,qBAAqB,CAAC;AAAA,IACtB;AAAA,IACA,iBAAiB;AAAA,IACjB;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,IAAI,YAAY;AACd,aAAO,KAAK,YAAY;AAAA,IAC1B;AAAA,IACA,IAAI,SAAS;AACX,aAAO,KAAK,YAAY,KAAK;AAAA,IAC/B;AAAA,IACA,IAAI,cAAc;AAChB,aAAO,KAAK,kBAAkB,KAAK,WAAW,UAAU,KAAK,WAAW,UAAU,IAAI;AAAA,IACxF;AAAA,IACA,IAAI,gBAAgB;AAClB,aAAO,KAAK,WAAW,YAAY,KAAK,WAAW;AAAA,IACrD;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,WAAW,IAAI,QAAQ;AAAA,IACvB,YAAY,KAAK,iBAAiB,gBAAgB;AAChD,WAAK,MAAM;AACX,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AAAA,IACxB;AAAA,IACA,YAAY,SAAS;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,UAAU;AACZ,aAAK,eAAe,KAAK,YAAY,KAAK;AAAA,MAC5C;AACA,UAAI,aAAa,kBAAkB;AACjC,cAAM,UAAU,SAAS,KAAK,UAAU,SAAS,GAAG,EAAE,KAAK;AAC3D,YAAI,SAAS;AACX,cAAI,SAAS,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,OAAO,KAAK,qBAAqB,QAAW;AAC1G,iBAAK,iBAAiB;AAAA,UACxB;AAAA,QACF,OAAO;AACL,eAAK,iBAAiB,KAAK;AAAA,QAC7B;AAAA,MACF;AACA,UAAI,YAAY,aAAa,oBAAoB,eAAe;AAC9D,aAAK,WAAW;AAAA,MAClB;AACA,UAAI,eAAe;AACjB,aAAK,eAAe;AAAA,MACtB;AACA,UAAI,iBAAiB,mBAAmB,eAAe,UAAU,aAAa,iBAAiB,eAAe;AAC5G,aAAK,eAAe;AAAA,MACtB;AACA,UAAI,aAAa,WAAW,eAAe;AACzC,aAAK,UAAU,KAAK,UAAU;AAC9B,YAAI,KAAK,SAAS;AAChB,eAAK,SAAS;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW;AACT,WAAK,gBAAgB,iCAAiC,qBAAqB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1H,aAAK,WAAW;AAChB,aAAK,eAAe;AACpB,aAAK,eAAe;AAAA,MACtB,CAAC;AACD,WAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,aAAK,MAAM;AACX,aAAK,IAAI,cAAc;AAAA,MACzB,CAAC;AACD,WAAK,MAAM,KAAK,eAAe;AAAA,IACjC;AAAA,IACA,cAAc;AACZ,WAAK,SAAS,KAAK;AACnB,WAAK,SAAS,SAAS;AAAA,IACzB;AAAA,IACA,aAAa;AACX,YAAM,MAAM,kBAAkB,IAAI,KAAK,MAAM;AAC7C,WAAK,OAAO,MAAM,OAAO,KAAK,gBAAgB,OAAO,kBAAkB;AAAA,IACzE;AAAA;AAAA;AAAA;AAAA,IAIA,WAAW;AACT,YAAM,UAAU,KAAK,MAAM,KAAK,WAAW,KAAK,YAAY,IAAI;AAChE,YAAM,YAAY,KAAK,WAAW,UAAU,IAAI;AAChD,YAAM,QAAQ,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,KAAK;AACrC,YAAI;AACJ,YAAI,KAAK,UAAU,GAAG;AACpB,kBAAQ,KAAK;AAAA,QACf;AACA,cAAM,YAAY;AAAA,UAChB,iBAAiB,GAAG,KAAK;AAAA,UACzB,OAAO,GAAG,SAAS;AAAA,UACnB,QAAQ,GAAG,KAAK,WAAW;AAAA,QAC7B;AACA,cAAM,KAAK,SAAS;AAAA,MACtB;AACA,WAAK,QAAQ;AAAA,IACf;AAAA;AAAA;AAAA;AAAA,IAIA,iBAAiB;AACf,UAAI,CAAC,KAAK,eAAe;AACvB;AAAA,MACF;AACA,YAAM,SAAS,SAAS,KAAK,gBAAgB,IAAI,CAAC,KAAK,kBAAkB,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS;AAE1G,YAAM,SAAS,KAAK,KAAK,cAAc;AACvC,YAAM,cAAc,KAAK,kBAAkB,KAAK,WAAW,WAAW,QAAQ;AAC9E,YAAM,MAAM,KAAK,KAAK,IAAI;AAC1B,YAAM,YAAY,KAAK,gBAAgB,KAAK,WAAW,WAAW,IAAI;AACtE,UAAI,iBAAiB;AACrB,UAAI,iBAAiB,CAAC;AACtB,UAAI,eAAe;AACnB,UAAI,eAAe,SAAS;AAC5B,cAAQ,aAAa;AAAA,QACnB,KAAK;AACH,2BAAiB,CAAC;AAClB,2BAAiB;AACjB,yBAAe,SAAS;AACxB,yBAAe;AACf;AAAA,QACF,KAAK;AACH,2BAAiB;AACjB,2BAAiB;AACjB,yBAAe,SAAS;AACxB,yBAAe;AACf;AAAA,QACF,KAAK;AACH,2BAAiB;AACjB,yBAAe,SAAS;AACxB;AAAA,QACF;AAAA,MACF;AACA,WAAK,aAAa,aAAa,cAAc,IAAI,cAAc;AAAA,WAC1D,MAAM,IAAI,MAAM,UAAU,YAAY,IAAI,CAAC,YAAY;AAAA,WACvD,MAAM,IAAI,MAAM,UAAU,CAAC,YAAY,IAAI,YAAY;AAC5D,WAAK,iBAAiB;AAAA,QACpB,iBAAiB,GAAG,MAAM,SAAS,MAAM,GAAG;AAAA,QAC5C,kBAAkB,IAAI,YAAY,CAAC;AAAA,QACnC,YAAY;AAAA,MACd;AAEA,WAAK,qBAAqB,OAAO,IAAI,CAAC,OAAO,UAAU;AACrD,cAAM,mBAAmB,OAAO,WAAW,KAAK,UAAU;AAC1D,eAAO;AAAA,UACL,QAAQ,KAAK,cAAc,CAAC,mBAAmB,iBAAiB,KAAK,UAAU,MAAM;AAAA,UACrF,iBAAiB;AAAA,YACf,QAAQ,CAAC,KAAK,aAAa,mBAAmB,eAAe,IAAI,SAAS,IAAI,KAAK,gBAAgB;AAAA,YACnG,YAAY;AAAA,YACZ,iBAAiB,IAAI,SAAS,KAAK,OAAO,MAAM,UAAU,MAAM,GAAG;AAAA,YACnE,kBAAkB,IAAI,YAAY,CAAC;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC,EAAE,QAAQ;AAAA,IACb;AAAA,IACA,iBAAiB;AACf,YAAM,QAAQ,KAAK;AACnB,YAAM,aAAa,KAAK,aAAa,CAAC,CAAC,SAAS,OAAO,UAAU;AACjE,UAAI,cAAc,CAAC,KAAK,eAAe;AACrC,aAAK,eAAe,qBAAqB,KAAK;AAAA,MAChD,WAAW,cAAc,KAAK,eAAe;AAC3C,aAAK,iBAAiB,qBAAqB,KAAK,aAAa;AAAA,MAC/D,OAAO;AACL,aAAK,eAAe;AACpB,aAAK,iBAAiB,CAAC;AAAA,MACzB;AAAA,IACF;AAAA,IACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,aAAO,KAAK,qBAAqBA,sBAAwB,kBAAqB,iBAAiB,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,cAAc,CAAC;AAAA,IACrL;AAAA,IACA,OAAO,OAAyB,kBAAkB;AAAA,MAChD,MAAMA;AAAA,MACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,MAC3B,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,kBAAkB,CAAC,GAAG,oBAAoB,oBAAoB,+BAA+B;AAAA,QAC7F,WAAW,CAAC,GAAG,aAAa,aAAa,eAAe;AAAA,QACxD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,+BAA+B;AAAA,QACpF,aAAa,CAAC,GAAG,eAAe,eAAe,+BAA+B;AAAA,QAC9E,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,SAAS,CAAC,GAAG,WAAW,WAAW,eAAe;AAAA,MACpD;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,UAAU,CAAI,oBAAoB;AAAA,MAClC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,GAAG,sBAAsB,GAAG,SAAS,UAAU,YAAY,8BAA8B,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG,2BAA2B,GAAG,OAAO,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,2BAA2B,GAAG,SAAS,iBAAiB,QAAQ,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,WAAW,eAAe,GAAG,qBAAqB,GAAG,CAAC,UAAU,WAAW,gBAAgB,KAAK,GAAG,2BAA2B,GAAG,CAAC,gBAAgB,KAAK,GAAG,4BAA4B,GAAG,OAAO,GAAG,CAAC,MAAM,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,gBAAgB,KAAK,GAAG,0BAA0B,CAAC;AAAA,MAC71B,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACpH,UAAG,eAAe,GAAG,KAAK;AAC1B,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,KAAK,EAAE,GAAG,4CAA4C,GAAG,IAAI,OAAO,CAAC;AACxI,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,sCAAsC,IAAI,MAAM;AAC9D,UAAG,YAAY,qBAAqB,IAAI,WAAW,MAAM,EAAE,sBAAsB,IAAI,WAAW,OAAO,EAAE,wBAAwB,IAAI,WAAW,SAAS,EAAE,0BAA0B,IAAI,UAAU,EAAE,uBAAuB,IAAI,aAAa,EAAE,sBAAsB,IAAI,OAAO,EAAE,oBAAoB,IAAI,QAAQ,KAAK;AACvT,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,WAAW,SAAS,IAAI,EAAE;AAC/C,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,gBAAgB,IAAI,EAAE;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAiB,iBAAiB,gBAAmB,iCAAiC,gBAAgB;AAAA,MACrH,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF,GAAG;AAAA,CACF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,SAAS,CAAC,cAAc,gBAAgB,gBAAgB;AAAA,MACxD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA4GZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,mBAAmB;AAAA,IAC7B,SAAS,CAAC,mBAAmB;AAAA,EAC/B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,mBAAmB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,mBAAmB;AAAA,MAC7B,SAAS,CAAC,mBAAmB;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["NzProgressComponent"]}