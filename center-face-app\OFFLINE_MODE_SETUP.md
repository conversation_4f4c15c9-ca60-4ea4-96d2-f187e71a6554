# Human.js 离线模式设置指南

## 概述

本指南将帮助您配置 Human.js 人脸识别引擎的离线模式，使应用能够在无网络连接的环境下正常工作。

## 快速开始

### 1. 下载模型文件

#### 方法一：使用 npm 脚本（推荐）

```bash
# 在项目根目录执行
cd center-face-app

# Linux/macOS
npm run download-models

# Windows
npm run download-models:win
```

#### 方法二：手动下载

1. 创建目录：
```bash
mkdir -p src/assets/human-models
```

2. 下载必需的模型文件：
   - [blazeface.json](https://cdn.jsdelivr.net/npm/@vladmandic/human/models/blazeface.json)
   - [blazeface.bin](https://cdn.jsdelivr.net/npm/@vladmandic/human/models/blazeface.bin)

3. 将文件保存到 `src/assets/human-models/` 目录

### 2. 验证安装

启动应用后：
1. 进入人脸签到页面
2. 点击设置图标
3. 查看"识别引擎"菜单
4. Human.js 引擎应显示"Offline ready"状态

## 详细配置

### 模型文件说明

| 文件名 | 大小 | 用途 | 必需 |
|--------|------|------|------|
| blazeface.json | ~0.1 MB | 人脸检测模型配置 | ✅ |
| blazeface.bin | ~0.4 MB | 人脸检测模型权重 | ✅ |

总下载大小：约 0.5 MB

### 目录结构

```
center-face-app/
├── src/
│   └── assets/
│       └── human-models/
│           ├── blazeface.json
│           ├── blazeface.bin
│           └── manifest.json (自动生成)
└── scripts/
    ├── download-human-models.js
    └── download-human-models.ps1
```

### 自动降级机制

系统会按以下优先级选择人脸识别引擎：

1. **Human.js 离线模式**：本地模型可用时
2. **Human.js 在线模式**：网络可用且本地模型不可用时
3. **MediaPipe 引擎**：Human.js 不可用时自动降级

### 状态指示器

#### 引擎状态显示

- **离线** (绿色)：使用本地模型
- **在线** (蓝色)：使用 CDN 模型
- **本地** (绿色)：MediaPipe 本地引擎
- **失败** (红色)：引擎初始化失败

#### 菜单标识

- **📱**：支持离线模式
- **✓**：当前选中的引擎
- **状态文本**：
  - "Offline ready"：离线就绪
  - "Online only"：仅在线可用
  - "Always available"：始终可用
  - "Unavailable"：不可用

## 故障排除

### 常见问题

#### 1. 模型下载失败

**症状**：下载脚本报错或下载中断

**解决方案**：
```bash
# 检查网络连接
ping cdn.jsdelivr.net

# 重新下载
npm run download-models

# Windows 用户可以尝试
npm run download-models:win -- -Force
```

#### 2. Human.js 引擎显示"Unavailable"

**可能原因**：
- 本地模型文件缺失
- 网络连接问题
- 浏览器兼容性问题

**解决步骤**：
1. 检查 `src/assets/human-models/` 目录是否存在模型文件
2. 验证文件完整性（查看 manifest.json）
3. 检查浏览器控制台错误信息
4. 尝试切换到 MediaPipe 引擎

#### 3. 离线模式不工作

**检查清单**：
- [ ] 模型文件已正确下载
- [ ] 文件路径正确：`/assets/human-models/`
- [ ] 应用已重新构建
- [ ] 浏览器缓存已清理

#### 4. 性能问题

**优化建议**：
- 在资源充足的环境使用 Human.js
- 在资源受限的环境使用 MediaPipe
- 根据网络状况自动选择引擎

### 调试信息

启用详细日志：
1. 在 URL 中添加 `?debug=1`
2. 打开浏览器开发者工具
3. 查看 Console 标签页的日志信息

### 手动验证

检查模型文件是否可访问：
```javascript
// 在浏览器控制台执行
fetch('/assets/human-models/blazeface.json')
  .then(response => console.log('Model accessible:', response.ok))
  .catch(error => console.error('Model not accessible:', error));
```

## 部署注意事项

### 生产环境

1. **确保模型文件包含在构建输出中**
2. **配置 Web 服务器正确提供静态文件**
3. **设置适当的缓存策略**

### CDN 部署

如果使用 CDN 部署：
```bash
# 确保模型文件上传到 CDN
rsync -av src/assets/human-models/ cdn:/path/to/assets/human-models/
```

### Docker 部署

在 Dockerfile 中：
```dockerfile
COPY src/assets/human-models/ /app/assets/human-models/
```

## 性能对比

| 引擎 | 初始化时间 | 检测精度 | 资源占用 | 离线支持 |
|------|------------|----------|----------|----------|
| MediaPipe | 快 (~100ms) | 中等 | 低 | ✅ |
| Human.js (离线) | 中等 (~500ms) | 高 | 中等 | ✅ |
| Human.js (在线) | 慢 (~2s) | 高 | 中等 | ❌ |

## 最佳实践

1. **开发环境**：使用在线模式进行开发和测试
2. **生产环境**：部署前下载模型文件以支持离线模式
3. **混合部署**：同时支持在线和离线模式，根据环境自动选择
4. **监控**：监控引擎切换和性能指标
5. **备份**：始终保持 MediaPipe 作为备用引擎

## 更新和维护

### 模型更新

当 Human.js 发布新版本时：
1. 更新 package.json 中的版本
2. 重新下载模型文件
3. 测试兼容性
4. 更新部署

### 定期检查

建议定期检查：
- 模型文件完整性
- 引擎性能指标
- 用户反馈和错误报告

## 技术支持

如果遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查网络连接状态
3. 验证模型文件完整性
4. 提供详细的错误描述和环境信息
