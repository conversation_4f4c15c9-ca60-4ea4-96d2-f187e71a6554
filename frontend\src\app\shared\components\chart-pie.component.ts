import { CommonModule } from '@angular/common';
import { Component, Input, type OnInit } from '@angular/core';
import { NgxEchartsDirective, provideEchartsCore  } from 'ngx-echarts';
import { EChartsOption } from 'echarts';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import { GridComponent, LegendComponent, TooltipComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { TitleComponent } from 'echarts/components';

echarts.use([Pie<PERSON>hart, GridComponent, CanvasRenderer, TitleComponent, LegendComponent, TooltipComponent]);

@Component({
    selector: 'app-chart-pie',
    imports: [CommonModule, NgxEchartsDirective],
    providers: [provideEchartsCore({ echarts })],
    template: `<div
    echarts
    [options]="chartOption"
    [merge]="mergeOption"
    [style.height]="height + 'px'"
    [style.width]="width + 'px'"
  ></div>`,
    styles: [
        `
      :host {
        display: inline-block;
      }
    `,
    ]
})
export class ChartPieComponent implements OnInit {
  chartOption: EChartsOption = {
    title: {
      text: '0%',
      left: 'center',
      top: 'center',
    },
    // legend: {
    //   orient: 'vertical',
    //   left: 'right',
    //   bottom: 'center',
    //   data: ['参考', '缺考'],
    // },
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: '参缺考',
        type: 'pie',
        radius: ['50%', '70%'], // 设置环形图的内半径和外半径
        // avoidLabelOverlap: true,
        label: {
          show: false,
          position: 'center',
        },
        // label: {
        //   show: true,
        //   position: 'center',
        //   formatter: (params: any) =>
        //     `${((params.value / this.total) * 100).toFixed(2)}%`,
        //   fontSize: '20',
        //   fontWeight: 'bold',
        // },
        // emphasis: {},
        labelLine: {
          show: false,
        },
        itemStyle: {
          color: (params: any) => ['#419CF8', '#C6E1FD'][params.dataIndex],
        },
        // data: [
        //   { value: 335, name: '参考' },
        //   { value: 310, name: '缺考' },
        // ],
      },
    ],
  };
  mergeOption: EChartsOption = {};
  _data: any = [];
  total: any = 0;
  width = 200;
  @Input() height = 200;
  @Input({ required: true }) set data(data: {
    total: number;
    test: number;
    absent: number;
  }) {
    this._data = [
      { value: data.test, name: '参考' },
      { value: data.absent, name: '缺考' },
    ];
    this.total = data.total;
    this.mergeOption = {
      title: {
        text: data.total
          ? Math.trunc((data.test / data.total) * 100) + '%'
          : '0%',
      },
      series: [
        {
          data: this._data,
        },
      ],
    };
  }
  ngOnInit(): void {}
}
