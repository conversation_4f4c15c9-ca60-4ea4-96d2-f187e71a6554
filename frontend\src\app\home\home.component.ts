import { CommonModule } from '@angular/common';
import { Component, OnDestroy, type OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NzListModule } from 'ng-zorro-antd/list';
import { IHttpSchedule } from '../core/http/exam.service';
import { CustomDatePipe } from '../shared/pipes/custom-date.pipe';
import {
  IRoomData,
  ProjectDataService,
  RoomDataService,
  ScheduleDataService,
} from '../core/data';
import { combineLatest, Subject, takeUntil } from 'rxjs';
import { parseDateTime } from '@app/utils/parseDate';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { ServerTimeService } from '@app/core/services/server-time.service';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
interface IUISchedule extends IHttpSchedule {
  subject_name: string[];
  tag: 'today' | 'tomorrow' | 'future';
  time_range: string;
  online_rooms: number;
}
@Component({
    selector: 'app-home',
    imports: [
        CommonModule,
        NzListModule,
        RouterLink,
        CustomDatePipe,
        NzIconModule,
    ],
    templateUrl: './home.component.html',
    styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit, OnDestroy {
  project: { name: string; start: string; end: string } = {} as any;

  center_name: string = '';
  rooms: { toatl: number; online: number; list: IRoomData[] } = {} as any;

  schedules: IUISchedule[] = [];

  subject_total: number = 0;
  entry_total: number = 0;

  unsubscribe$ = new Subject();
  constructor(
    private projectData: ProjectDataService,
    private roomData: RoomDataService,
    private scheduleData: ScheduleDataService,
    private timeService: ServerTimeService
  ) {}

  ngOnInit(): void {
    this.projectData.center$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((c) => {
        if (c) {
          this.center_name = c.center_name;
          console.log('center:', JSON.stringify(c));
        }
      });
    this.projectData.formalProject$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((p) => (this.project = p));
    this.roomData.rooms$.subscribe((rooms) => {
      this.rooms = {
        toatl: rooms.length,
        online: rooms.filter((r) => r.is_online).length,
        list: rooms,
      };
    });

    combineLatest([this.scheduleData.schedules$, this.roomData.rooms$])
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(([schedules, rooms]) => {
        this.schedules = schedules
          .filter((s) => {
            const now = this.timeService.getServerTimeValue();
            const oneWeekLater = now + 7 * 24 * 3600 * 1000;
            const start = new Date(s.start).getTime();
            const end = new Date(s.end).getTime();
            const isInOneWeek =
              (start >= now && start <= oneWeekLater) ||
              (end >= now && end <= oneWeekLater) ||
              (start <= now && end >= oneWeekLater);
            return !s.test && isInOneWeek;
          })
          .sort(
            (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()
          )
          .slice(0, 5)
          .map((s) => {
            const startTime = parseDateTime(new Date(s.start));
            const endTime = parseDateTime(new Date(s.end));
            const time_range = `${startTime.HH}:${startTime.FF}-${endTime.HH}:${endTime.FF}`;
            const now = new Date(this.timeService.getServerTimeValue());
            const start = new Date(s.start);
            const tomorrow = new Date(this.timeService.getServerTimeValue());
            tomorrow.setDate(tomorrow.getDate() + 1);
            const isSameDay = (a: Date, b: Date) =>
              a.toISOString().slice(0, 10) === b.toISOString().slice(0, 10);
            const tag = isSameDay(now, start)
              ? 'today'
              : isSameDay(tomorrow, start)
              ? 'tomorrow'
              : 'future';
            const online_rooms = s.sessions.reduce((pre, cur) => {
              const room = rooms.find((r) => r.sn_code === cur.room_sn);
              if (room?.is_online) {
                pre++;
              }
              return pre;
            }, 0);
            return {
              ...s,
              subject_name: Array.from(new Set(s.subjects.map(s => s.name))),
              time_range,
              tag,
              online_rooms,
            };
          });
        this.subject_total = this.schedules.reduce(
          (acc, cur) => acc + cur.subject_name.length,
          0
        );
        this.entry_total = this.schedules.reduce(
          (acc, cur) =>
            acc + cur.sessions.reduce((a, c) => a + c.entry_status.total, 0),
          0
        );
      });

    this.roomData.reqRoomsInterval();
    this.scheduleData.reqSchedules().subscribe();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(1);
    this.unsubscribe$.complete();
  }
}
