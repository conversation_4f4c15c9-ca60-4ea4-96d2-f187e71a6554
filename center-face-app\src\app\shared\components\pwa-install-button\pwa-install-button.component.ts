import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, combineLatest, filter } from 'rxjs';
import { PwaInstallService } from '../../../core/service/pwa-install.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { AuthService } from '@app/core/service/auth.service';
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';

@Component({
  selector: 'app-pwa-install-button',
  standalone: true,
  imports: [CommonModule],
  animations: [
    trigger('slideInOut', [
      state('void', style({
        transform: 'translateY(100%)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void => *', [
        animate('300ms ease-out')
      ]),
      transition('* => void', [
        animate('200ms ease-in')
      ])
    ])
  ],
  template: `
    <div *ngIf="showInstallButton && !isIOS && !showManualInstallPrompt"
         [@slideInOut]
         class="pwa-install-container">
      <div class="pwa-prompt">
        <div class="pwa-prompt-content">
          <div class="pwa-prompt-header">
            <img src="assets/images/icon-512x512.png" alt="应用图标" class="pwa-icon" />
            <span class="pwa-title">添加"悦考智签"到主屏幕</span>
          </div>
          <p class="pwa-description">安装此应用可以：</p>
          <ul class="pwa-benefits">
            <li>方便从桌面快速访问</li>
            <li>更快速的加载体验</li>
          </ul>
          
          <div class="pwa-permission-notice">
            <p class="pwa-notice-text">温馨提示：为确保顺利安装，请检查并开启浏览器的"安装应用"及"创建桌面快捷方式"相关权限。</p>
          </div>
          
          <!-- 非iOS设备显示安装按钮 -->
          <div class="pwa-buttons">
            <button class="pwa-install-btn" (click)="installPwa($event)">立即安装</button>
            <button class="pwa-close-btn" (click)="dismissPrompt()">暂不安装</button>
          </div>
        </div>
      </div>
    </div>

    <!-- iOS设备显示安装指引 -->
    <div *ngIf="showInstallButton && isIOS && !showManualInstallPrompt"
         [@slideInOut]
         class="pwa-install-container"> <!-- Re-using container for consistent slide-in -->
      <div class="pwa-prompt">
        <div class="pwa-prompt-content">
          <div class="pwa-prompt-header">
            <img src="assets/images/icon-512x512.png" alt="应用图标" class="pwa-icon" />
            <span class="pwa-title">添加"悦考智签"到主屏幕</span>
          </div>
          <div class="ios-install-guide">
            <div class="ios-guide-steps">
              <p>在iOS设备上安装:</p>
              <ol>
                <li *ngIf="serverCodeFromRoute">点击<strong>复制注册码</strong></li>
                <li>点击浏览器中的<strong>分享</strong>按钮</li>
                <li>在弹出菜单中选择<strong>添加到主屏幕</strong></li>
                <li>点击<strong>添加</strong>完成安装，在主屏幕打开并注册</li>
              </ol>
            </div>
            <div class="pwa-buttons">
              <button *ngIf="serverCodeFromRoute" 
                      class="pwa-copy-code-btn" 
                      (click)="copyRegistrationCode()">
                {{ copyButtonText }}
              </button>
              <button class="pwa-close-btn" (click)="dismissPrompt()">我知道了</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 手动安装提示 (非iOS) -->
    <div *ngIf="showManualInstallPrompt && !isIOS"
         [@slideInOut]
         class="manual-install-guide pwa-install-container">
      <div class="pwa-prompt">
        <div class="pwa-prompt-content">
          <div class="pwa-prompt-header">
            <img src="assets/images/icon-512x512.png" alt="应用图标" class="pwa-icon" />
            <span class="pwa-title">手动添加"悦考智签"到主屏幕</span>
          </div>
          <p class="pwa-description">您可以通过以下步骤手动安装此应用：</p>
          <div class="manual-guide-steps">
            <p><strong>Android:</strong></p>
            <ol>
              <li>打开浏览器菜单。</li>
              <li>选择 "添加到主屏幕" 或类似选项。</li>
            </ol>
          </div>
          <div class="pwa-buttons">
            <button class="pwa-close-btn" (click)="dismissManualPrompt()">我知道了</button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .pwa-install-container {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background-color: #fff;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
      padding: 15px;
      border-top-left-radius: 15px;
      border-top-right-radius: 15px;
      transform-origin: bottom center;
      will-change: transform, opacity;
    }
    
    .pwa-prompt {
      display: flex;
      justify-content: center;
    }
    
    .pwa-prompt-content {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 10px;
    }
    
    .pwa-prompt-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }
    
    .pwa-icon {
      width: 36px;
      height: 36px;
      margin-right: 10px;
      border-radius: 8px;
    }
    
    .pwa-title {
      font-size: 18px;
      font-weight: bold;
    }
    
    .pwa-description {
      margin: 5px 0;
      font-size: 14px;
    }
    
    .pwa-benefits {
      margin: 0 0 0 20px;
      padding: 0;
      font-size: 14px;
    }
    
    .pwa-benefits li {
      margin-bottom: 5px;
    }
    
    .pwa-buttons {
      display: flex;
      gap: 10px;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .pwa-install-btn {
      background-color: #1976d2;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 50px;
      font-weight: bold;
      font-size: 16px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      transition: background-color 0.2s ease;
      cursor: pointer;
    }
    
    .pwa-install-btn:hover {
      background-color: #1565c0;
    }
    
    .pwa-close-btn {
      background-color: #f5f5f5;
      border: 1px solid #ccc;
      padding: 10px 20px;
      border-radius: 50px;
      font-size: 16px;
      transition: background-color 0.2s ease;
      cursor: pointer;
    }
    
    .pwa-close-btn:hover {
      background-color: #e0e0e0;
    }

    .pwa-copy-code-btn {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 50px;
      font-size: 16px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      cursor: pointer;
    }

    .pwa-copy-code-btn:hover {
      background-color: #1890ff;
    }

    .pwa-copy-code-btn:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    
    .ios-install-guide {
      border: 1px solid #e2e2e2;
      border-radius: 8px;
      padding: 10px 15px;
      margin-bottom: 15px;
      background-color: #f9f9f9;
    }
    
    .ios-guide-steps {
      margin-bottom: 10px;
    }
    
    .ios-guide-steps p {
      margin: 5px 0;
      font-weight: bold;
    }
    
    .ios-guide-steps ol {
      margin: 10px 0 10px 20px;
      padding: 0;
    }
    
    .ios-guide-steps li {
      margin-bottom: 8px;
    }
    
    .ios-icon {
      font-size: 18px;
      vertical-align: middle;
      margin-left: 5px;
    }

    .manual-install-guide .pwa-prompt-content {
    }

    .manual-guide-steps {
      border: 1px solid #e2e2e2;
      border-radius: 8px;
      padding: 10px 15px;
      margin-bottom: 15px;
      background-color: #f9f9f9;
    }

    .manual-guide-steps p {
      margin: 5px 0;
      font-weight: bold;
    }

    .manual-guide-steps ol {
      margin: 10px 0 10px 20px;
      padding: 0;
    }

    .manual-guide-steps li {
      margin-bottom: 8px;
    }

    .pwa-permission-notice {
      display: flex;
      align-items: flex-start; /* 图标和文字顶部对齐 */
      background-color: #fff3e0; /* 浅橙色背景，表示提示/警告 */
      border: 1px solid #ffe0b2; /* 边框颜色，与背景协调 */
      border-radius: 8px;
      padding: 10px 15px;
      margin-top: 15px; /* 与上方元素的间距 */
      margin-bottom: 15px; /* 与下方按钮组的间距 */
    }


    .pwa-notice-text {
      font-size: 13px; /* 文本大小 */
      color: #4d4d4d; /* 文本颜色，深灰色 */
      line-height: 1.5;
      margin: 0; /* 重置段落的默认margin */
    }
  `]
})
export class PwaInstallButtonComponent implements OnInit, OnDestroy {
  @Input() daysToWaitBeforeShowingAgain = 7;
  @Output() installClick = new EventEmitter<void>();
  @Output() dismissClick = new EventEmitter<void>();
  
  showInstallButton = false;
  isIOS = false;
  showManualInstallPrompt = false;
  serverCodeFromRoute: string | null = null;
  copyButtonText = '复制注册码';
  private _manualInstallRequired = false;
  private destroy$ = new Subject<void>();
  private readonly storageKey = 'pwa-install-dismissed';
  private _canInstallPwa = false;
  
  constructor(
    private pwaInstallService: PwaInstallService,
    private authService: AuthService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    // 检测是否为iOS设备
    this.isIOS = this.pwaInstallService.isIOS;
  }
  
  ngOnInit(): void {
    // 检查是否已在独立模式下运行或用户已忽略 (最高优先级)
    if (this.pwaInstallService.isInStandaloneMode || this.shouldHidePrompt()) {
      this.showInstallButton = false;
      this.showManualInstallPrompt = false;
    }

    // 订阅 PWA 可安装状态
    this.pwaInstallService.canInstall
      .pipe(takeUntil(this.destroy$))
      .subscribe(canInstall => {
        this._canInstallPwa = canInstall;
        this.updateShowButtonState();
      });

    this.pwaInstallService.manualInstallRequired
      .pipe(takeUntil(this.destroy$))
      .subscribe(manualRequired => {
        this._manualInstallRequired = manualRequired;
        this.updateShowButtonState();
      });

    // 订阅登录状态变化
    this.authService.loginStateChange$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (!this.isIOS) {
          this.updateShowButtonState();
        }
      });

    // 订阅路由变化以获取当前激活路由的参数
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.extractServerCodeFromCurrentRoute();
      });

    // 初始加载时也要提取参数
    this.extractServerCodeFromCurrentRoute();
    
    // 设置初始状态
    this.updateShowButtonState();
  }
  
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateShowButtonState(): void {
    this.showInstallButton = false;
    this.showManualInstallPrompt = false;

    if (this.pwaInstallService.isInStandaloneMode || this.shouldHidePrompt() || (!this.authService.isLogin && !this.isIOS)) {
      return;
    }

    if (this.isIOS) {
      setTimeout(() => {
        if (!this.shouldHidePrompt() && !this.pwaInstallService.isInStandaloneMode) {
          this.showInstallButton = true;
        }
      }, 1000);
      return;
    }

    if (this._canInstallPwa) {
      setTimeout(() => {
        if (this.authService.isLogin && this._canInstallPwa && !this.shouldHidePrompt() && !this.pwaInstallService.isInStandaloneMode && !this.isIOS) {
          this.showInstallButton = true;
        }
      }, 500);
    }
  }
  
  installPwa(event: MouseEvent): void {
    this.installClick.emit();
    
    if (!this.isIOS) {
      if (this.pwaInstallService.deferredPrompt) {
        const deferredPrompt = this.pwaInstallService.deferredPrompt;
        try {
          deferredPrompt.prompt();
          deferredPrompt.userChoice.then((choiceResult: any) => {
            if (choiceResult.outcome === 'accepted') {
              console.log('User has accepted the installation');
              this.pwaInstallService.deferredPrompt = null;
              this.showInstallButton = false;
              this.showManualInstallPrompt = false;
            } else {
              console.log('User has declined the installation');
            }
          });
        } catch (error) {
          console.error('Error occurred while trying to install PWA:', error);
        }
      } else {
        console.warn('No installation prompt event available');
      }
    }
  }
  
  dismissPrompt(): void {
    this.showInstallButton = false;
    this.showManualInstallPrompt = false;
    this.dismissClick.emit();
    
    const now = new Date();
    const dismissedDate = now.toISOString();
    localStorage.setItem(this.storageKey, dismissedDate);
  }

  dismissManualPrompt(): void {
    this.showManualInstallPrompt = false;
    this.dismissPrompt();
  }
  
  private extractServerCodeFromCurrentRoute(): void {
    let currentRoute = this.router.routerState.root;

    while (currentRoute) {
      if (currentRoute.firstChild) {
        currentRoute = currentRoute.firstChild;
      } else {
        break;
      }
    }

    // 获取当前激活路由的参数和查询参数
    const params = currentRoute.snapshot.params;
    const queryParams = currentRoute.snapshot.queryParams;

    this.serverCodeFromRoute = queryParams['serverCode'] || params['server_code'] || params['r_server_code'] || null;
    console.log('serverCodeFromRoute:', this.serverCodeFromRoute);
  }

  async copyRegistrationCode(): Promise<void> {
    if (!this.serverCodeFromRoute) {
      return;
    }
    
    try {
      await navigator.clipboard.writeText(this.serverCodeFromRoute);
      const originalText = this.copyButtonText;
      this.copyButtonText = '已复制！';
      
      setTimeout(() => {
        this.copyButtonText = originalText;
      }, 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  }

  private shouldHidePrompt(): boolean {
    const dismissedDateStr = localStorage.getItem(this.storageKey);
    if (!dismissedDateStr) {
      return false;
    }
    
    const dismissedDate = new Date(dismissedDateStr);
    const now = new Date();
    const daysSinceDismissed = Math.floor(
      (now.getTime() - dismissedDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    return daysSinceDismissed < this.daysToWaitBeforeShowingAgain;
  }
} 