import path from "node:path";
import os from "node:os";
import fs from "node:fs";
import { <PERSON><PERSON>erWindow, app } from "electron";
import * as  IPC  from '@share-types/ipc.constants';
import * as constants from "@share-types/constants";
import * as crypt from "./utils/crypt";
import { Store } from "./utils/store";
import {CenterInfo} from "@share-types/center.types";
import { getArg } from "./utils/args";
import { NetworkConfig } from "@share-types/settings.types";

const pkg = require("./package.json");
export const APP_NAME = pkg.productName;
export const APP_VERSION = pkg.version;

interface ICenterSetting {
  APP_TITLE: string;
  APP_VERSION: string;
  APP_PATH: string;
  LOG_PATH: string;
  SERVER_PORT: number;
  SERVER_SSL_PORT: number;
  STAGE: string; // 'beta' | 'release'
  ENV: string; // 'test' | 'production' | 'stage1'

  LANG: string; // 语言: zh/en
  DEBUG_LEVEL: number;
  DATA_PATH: string;

  CLOUD_URL: string;
  UPDATE_URL: string;
  DISABLE_HA: boolean; // 禁止硬件加速
}

// User configuration
let store: Store = {} as Store;
const kUserConfigKey = "AtaJoyTest2019"; // key for settings.user.json

export const settings: ICenterSetting = load_settings()
export const DEBUG_LEVEL = settings.DEBUG_LEVEL;
export const APP_ICON = get_app_icon();
export const LOG_PATH = get_log_path();


function load_settings() {
  const cfg = loadConfigFile();

  const env = getArg("env") || process.env.JOY_ENV || cfg.ENV || "production";
  cfg.ENV = env;

  if (!cfg.DATA_PATH) {
    cfg.DATA_PATH = get_data_path();
    if (!["beta", "production"].includes(cfg.ENV)) {
      cfg.DATA_PATH = path.join(cfg.DATA_PATH, cfg.ENV);
    }
    fs.mkdirSync(cfg.DATA_PATH, { recursive: true });
  }

  store = new Store({
    configPath: cfg.DATA_PATH,
    configName: "settings.user",
    password: kUserConfigKey,
    secretKeys: ["center"],
    defaults: { v: 1 },
  });

  cfg.APP_VERSION = APP_VERSION;
  cfg.APP_PATH = app.getAppPath();
  cfg.DEBUG_LEVEL = process.env.JOY_DEBUG ? parseInt(process.env.JOY_DEBUG) : cfg.DEBUG_LEVEL;
  cfg.CLOUD_URL = cfg.CLOUD_URL || getCloudUrl(cfg.ENV);
  cfg.UPDATE_URL = cfg.UPDATE_URL || getUpdateUrl(cfg.ENV);
  cfg.SERVER_PORT = cfg.SERVER_PORT || 19100;
  cfg.SERVER_SSL_PORT = cfg.SERVER_SSL_PORT || cfg.SERVER_PORT + 1;
  return cfg
}

function loadConfigFile(){
  const config_name = "config.json"
  const configFiles = [
    path.join(process.resourcesPath, config_name),
    path.join(path.dirname(process.execPath), config_name),
    path.join(get_data_path(), config_name),
    path.join(os.homedir(), config_name),
  ];

  const cfg = {} as ICenterSetting;

  configFiles.forEach((f) => {
    if (fs.existsSync(f)) {
      try {
        const parsedConfig = JSON.parse(fs.readFileSync(f, "utf8"));
        merge(cfg, parsedConfig);
      } catch (e) {
        console.error(`Failed to parse: ${f}`, e);
      }
    }
  });

  return cfg;
}

function getCloudUrl(env: string): string {
  if (env === "test") {
    return constants.CLOUD_URL_TEST;
  }
  if (env === "stage1") {
    return constants.CLOUD_URL_STAGE1;
  }
  return constants.CLOUD_URL_PRODUCTION;
}

function getUpdateUrl(env: string): string {
  if (env === "test" || env === "stage1") {
    return constants.UPDATE_URL_STAGE1;
  }
  return constants.UPDATE_URL_PRODUCTION;
}

function getIpAddresses(): string[] {
  return Object.values(os.networkInterfaces())
  .flat()
  .filter(iface => iface?.family === 'IPv4' && !iface?.internal)
  .map(iface => iface?.address) as string[]
}

function ipAddress(): string {
  const cfg = getNetworkConfig();
  return cfg?.binding_ip || getIpAddresses()[0] || "127.0.0.1";
}

export function getServerUrl(): string {
  return "http://" + ipAddress() + ":" + settings.SERVER_PORT;
}

export function merge(target: any, partial: any) {
  const isObject = (o) => o !== null && typeof o === "object";
  for (const key of Object.keys(partial)) {
    if (isObject(target[key]) && isObject(partial[key])) {
      merge(target[key] as any, partial[key] as any);
    } else {
      if (typeof partial[key] === "undefined") {
        delete target[key];
      } else {
        target[key] = partial[key];
      }
    }
  }
}

function get_app_icon(): string {
  if (process.defaultApp) {
    return path.join(__dirname, "assets", "app");
  }
  return path.join(process.resourcesPath, "app");
}

export function getNetworkConfig(): NetworkConfig | null {
  try {
    const file_path = `${settings.DATA_PATH}/network.json`;
    return JSON.parse(fs.readFileSync(file_path, "utf8"));
  } catch (_err) {
    return null;
  }
}

export function setNetworkConfig(info: NetworkConfig) {
  try {
    const network_settings = getNetworkConfig() || {};
    Object.assign(network_settings, info);
    const file_path = `${settings.DATA_PATH}/network.json`;
    fs.writeFileSync(file_path, JSON.stringify(network_settings, null, 2), "utf8");
  } catch (err) {
    console.error("function setNetWorkInfo error\n" + err);
    throw err;
  }
}

function get_log_path() {
  let log_path = settings.LOG_PATH || path.join(settings.DATA_PATH, "logs");
  try {
    if (!fs.existsSync(log_path)) {
      fs.mkdirSync(log_path);
    }
  } catch (error) {
    console.error("failed to create log path", error);
    log_path = settings.DATA_PATH;
  }

  return path.join(log_path, "center.log");
}


function get_data_path() {
  if (process.env.JOY_DATAPATH) {
    return process.env.JOY_DATAPATH;
  }
  const isWin = process.platform === "win32";
  if (isWin) {
    const data_path = path.resolve(path.dirname(app.getPath("exe")), "../data/center");
    if (!fs.existsSync(data_path)) {
      fs.mkdirSync(data_path, { recursive: true });
    }
    return data_path;
  } else {
    // TODO
    console.warn("warning: data path not exists");
    return "";
  }
}

export function getCenterInfo(): CenterInfo {
  const center = store.get("center");
  if (typeof center !== "string") {
    return center;
  }

  try {
    const plain = crypt.decrypt(center, kUserConfigKey);
    return JSON.parse(plain);
  } catch (_e) {
    console.error("GetCenterInfo error:", _e)
    return {} as any;
  }
}


export function setUserConfig(cfg: Record<string,any> | string, value?: any) {
  console.log('setUserConfig:', cfg, value)
  if (typeof cfg === "string") {
    updateStore(cfg, value);
  } else {
    Object.keys(cfg).forEach((k) => {
      updateStore(k, cfg[k]);
    });
  }

  broadcastevents(IPC.IPC_USER_CONFIG_CHANGED, getUserConfig());
  store.saveSync();
}

export function getUserConfig(): any {
  const conf: any = { ...store.getData() };
  conf.network = getNetworkConfig();
  return conf;
}

function updateStore(name: string, value?: any) {
  if (name === "network") {
    setNetworkConfig(value as NetworkConfig);
  } else if (typeof value === "undefined") {
    store.unset(name);
  } else {
    store.set(name, value);
  }
}

function broadcastevents(channel: string, ...args: any[]) {
  BrowserWindow.getAllWindows().forEach((win) => {
    if (win.webContents) {
      win.webContents.send(channel, ...args);
    }
  });
}


export function save_config(): Promise<boolean> {
  return new Promise<boolean>((resolve, reject) => {
    store.save((err) => {
      if (err) {
        reject(err);
      } else {
        resolve(true);
      }
    });
  });
}
