import { ElogType, logTypeArray, logTypeMap } from '@share-types/center.types';
import { CommonModule } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { FormsModule } from '@angular/forms';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { LogHttpService } from '../../core/http/log.service';
import { CustomDatePipe } from '../../shared/pipes/custom-date.pipe';
import {
  DateFilterPipe,
  FilterLogPipe,
  TypeFilterPipe,
} from './log-filter.pipe';

interface Log {
  // module_name: string;
  content: string;
  time: number;
  type: ElogType;
}
@Component({
    selector: 'app-operation-log',
    imports: [
        CommonModule,
        NzTableModule,
        FormsModule,
        NzInputModule,
        NzIconModule,
        NzDatePickerModule,
        NzSelectModule,
        CustomDatePipe,
        FilterLogPipe,
        DateFilterPipe,
        TypeFilterPipe,
    ],
    templateUrl: './operation-log.component.html',
    styleUrls: ['./operation-log.component.scss']
})
export class OperationLogComponent implements OnInit {
  logs: Log[] = [];

  search = '';

  filter_date: [Date, Date] | null = null;

  typeSelected: ElogType | null = null;

  logType = ElogType;
  logTypeMap = logTypeMap;
  logTypeArray = logTypeArray;

  constructor(private logHttp: LogHttpService) {}
  ngOnInit(): void {
    this.logHttp.getLogs().subscribe((res) => {
      if (res.status === 'success') {
        this.logs = res.data
          .sort((a, b) => b.created_at - a.created_at)
          .map((item) => {
            return {
              module_name:
                item.type < 100
                  ? '系统操作'
                  : item.type >= 100 && item.type <= 200
                  ? '考场管理'
                  : '考试管理',
              content: item.content,
              time: item.created_at * 1000,
              type: item.type,
            };
          });
      }
    });
  }

}
