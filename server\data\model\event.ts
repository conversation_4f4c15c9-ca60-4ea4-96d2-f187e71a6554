import {
  Sequelize,
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
} from 'sequelize';

export default function (sequelize: Sequelize) {
  const Event = sequelize.define<EventModelFields>(
    'event',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      session_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      type: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      content: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.INTEGER,
      },
      is_deleted: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      version: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
    },
    {
      timestamps: false,
      underscored: true,
      freezeTableName: true,
      hooks: {
        beforeUpdate: (event: EventModelFields, opt) => {},
        beforeCreate: (event: EventModelFields, opt) => {},
      },
    },
  );
  return Event;
}

export interface EventModelFields
  extends Model<
    InferAttributes<EventModelFields>,
    InferCreationAttributes<EventModelFields>
  > {
  id: string;
  session_id: string;
  type: string;
  content: string;
  created_at: number;
  is_deleted: 0 | 1;
  version: number;
}
