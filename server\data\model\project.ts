import {
  Sequelize,
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';

export default function (sequelize: Sequelize) {
  const Project = sequelize.define<ProjectModelFields>(
    'project',
    {
      id: {
        type: DataTypes.STRING,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
      },
      start: {
        type: DataTypes.STRING,
      },
      end: {
        type: DataTypes.STRING,
      },
      test: {
        type: DataTypes.INTEGER,
      },
      created_at: {
        type: DataTypes.INTEGER,
      },
    },
    {
      timestamps: false,
      underscored: true,
      freezeTableName: true,
      hooks: {
        beforeUpdate: (attachment: ProjectModelFields, opt) => {},
        beforeCreate: (attachment: ProjectModelFields, opt) => {},
      },
    },
  );
  return Project;
}

export interface ProjectModelFields
  extends Model<
    InferAttributes<ProjectModelFields>,
    InferCreationAttributes<ProjectModelFields>
  > {
  id: string;
  name: string;
  start: string;
  end: string;
  test: 0 | 1;
  created_at: CreationOptional<number>;
}
