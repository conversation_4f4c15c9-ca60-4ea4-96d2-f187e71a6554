

import type { BuildOptions } from "esbuild";
import { build, context } from "esbuild";
import {copy} from "esbuild-plugin-copy";
import path from "path";

const isDev = process.env.NODE_ENV === "development";
const buildTargetDir = path.join(__dirname, "..","build", "app");

const common: BuildOptions = {
  outdir: buildTargetDir,
  bundle: true,
  minify: !isDev,
  sourcemap: isDev,
  logLevel: 'verbose',
  define: {
    DEBUG: isDev ? "true" : "false",
  },
  tsconfig: "tsconfig.json",
  
};

const main: BuildOptions = {
  ...common,
  entryPoints: ["src/main.ts", "src/preload.ts"],
  platform: "node",
  external: ["electron"],
  plugins: [
    copy({
      verbose:true,
      resolveFrom: 'cwd',
      assets: [
        {
          from: "./src/server/server.html",
          to: path.join(buildTargetDir, "server", "server.html"),
        },
        {
          from: "./src/assets/*",
          to: path.join(buildTargetDir, "assets"),
        }
      ],
    }),
  ],
};

const renderer: BuildOptions = {
  ...common,
  outdir: path.join(buildTargetDir, "server"),
  entryPoints: ["src/server/init.ts"],
  platform: "browser",
  format: "iife",
};

export const build_watch = async () => {
  const mainCtx = await context({ ...main });
  const rendererCtx = await context({ ...renderer });
  await mainCtx.watch();
  await rendererCtx.watch();
};

export const build_prod = async () => {
  await build({ ...main });
  await build({ ...renderer });
};

console.log("esbuild: isDev:", isDev);
const fn = isDev ? build_watch : build_prod;
fn().then(() => {
  console.log("esbuild: " + (isDev ? "watching" : "done") + "...");
}).catch((err) => {
  console.error(err);
  process.exit(1);
})