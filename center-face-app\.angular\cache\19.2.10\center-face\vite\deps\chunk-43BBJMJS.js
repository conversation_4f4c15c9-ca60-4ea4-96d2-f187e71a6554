import {
  Directive,
  ElementRef,
  Input,
  NgModule,
  Renderer2,
  setClassMetadata,
  ɵɵNgOnChangesFeature,
  ɵɵdefineDirective,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject
} from "./chunk-WG6BS3HR.js";
import {
  __publicField
} from "./chunk-R2YX2K3C.js";

// node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-transition-patch.mjs
var _NzTransitionPatchDirective = class _NzTransitionPatchDirective {
  elementRef;
  renderer;
  hidden = null;
  setHiddenAttribute() {
    if (this.hidden) {
      if (typeof this.hidden === "string") {
        this.renderer.setAttribute(this.elementRef.nativeElement, "hidden", this.hidden);
      } else {
        this.renderer.setAttribute(this.elementRef.nativeElement, "hidden", "");
      }
    } else {
      this.renderer.removeAttribute(this.elementRef.nativeElement, "hidden");
    }
  }
  constructor(elementRef, renderer) {
    this.elementRef = elementRef;
    this.renderer = renderer;
    this.renderer.setAttribute(this.elementRef.nativeElement, "hidden", "");
  }
  ngOnChanges() {
    this.setHiddenAttribute();
  }
  ngAfterViewInit() {
    this.setHiddenAttribute();
  }
};
__publicField(_NzTransitionPatchDirective, "ɵfac", function NzTransitionPatchDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _NzTransitionPatchDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Renderer2));
});
__publicField(_NzTransitionPatchDirective, "ɵdir", ɵɵdefineDirective({
  type: _NzTransitionPatchDirective,
  selectors: [["", "nz-button", ""], ["nz-button-group"], ["", "nz-icon", ""], ["nz-icon"], ["", "nz-menu-item", ""], ["", "nz-submenu", ""], ["nz-select-top-control"], ["nz-select-placeholder"], ["nz-input-group"]],
  inputs: {
    hidden: "hidden"
  },
  features: [ɵɵNgOnChangesFeature]
}));
var NzTransitionPatchDirective = _NzTransitionPatchDirective;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzTransitionPatchDirective, [{
    type: Directive,
    args: [{
      selector: "[nz-button], nz-button-group, [nz-icon], nz-icon, [nz-menu-item], [nz-submenu], nz-select-top-control, nz-select-placeholder, nz-input-group"
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: Renderer2
  }], {
    hidden: [{
      type: Input
    }]
  });
})();
var _NzTransitionPatchModule = class _NzTransitionPatchModule {
};
__publicField(_NzTransitionPatchModule, "ɵfac", function NzTransitionPatchModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || _NzTransitionPatchModule)();
});
__publicField(_NzTransitionPatchModule, "ɵmod", ɵɵdefineNgModule({
  type: _NzTransitionPatchModule,
  imports: [NzTransitionPatchDirective],
  exports: [NzTransitionPatchDirective]
}));
__publicField(_NzTransitionPatchModule, "ɵinj", ɵɵdefineInjector({}));
var NzTransitionPatchModule = _NzTransitionPatchModule;
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzTransitionPatchModule, [{
    type: NgModule,
    args: [{
      imports: [NzTransitionPatchDirective],
      exports: [NzTransitionPatchDirective]
    }]
  }], null, null);
})();

export {
  NzTransitionPatchDirective,
  NzTransitionPatchModule
};
//# sourceMappingURL=chunk-43BBJMJS.js.map
