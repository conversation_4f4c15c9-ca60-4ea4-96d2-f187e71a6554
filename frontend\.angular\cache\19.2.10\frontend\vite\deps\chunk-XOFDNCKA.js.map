{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@ngneat+elf@2.5.1_rxjs@7.8.1/node_modules/@ngneat/elf/index.esm.js"], "sourcesContent": ["import { BehaviorSubject, isObservable, firstValueFrom, Subject, Observable, pipe, asapScheduler } from 'rxjs';\nimport { filter, take, map, distinctUntilChanged, debounceTime } from 'rxjs/operators';\nfunction createState(...propsFactories) {\n  const result = {\n    config: {},\n    state: {}\n  };\n  for (const {\n    config,\n    props\n  } of propsFactories) {\n    Object.assign(result.config, config);\n    Object.assign(result.state, props);\n  }\n  return result;\n}\nlet asyncBatchesInProgress = 0;\nconst batchInProgress = new BehaviorSubject(false);\nconst batchDone$ = batchInProgress.asObservable().pipe(filter(inProgress => !inProgress), take(1));\nfunction emitOnce(cb) {\n  if (!batchInProgress.getValue()) {\n    batchInProgress.next(true);\n    const value = cb();\n    if (asyncBatchesInProgress === 0) {\n      batchInProgress.next(false);\n    }\n    return value;\n  }\n  return cb();\n}\nasync function emitOnceAsync(cb) {\n  asyncBatchesInProgress++;\n  if (!batchInProgress.getValue()) {\n    batchInProgress.next(true);\n  }\n  const callbackReturnValue = cb();\n  const value = await (isObservable(callbackReturnValue) ? firstValueFrom(callbackReturnValue) : callbackReturnValue);\n  if (--asyncBatchesInProgress === 0) {\n    batchInProgress.next(false);\n  }\n  return value;\n}\n\n// this is internal object that's not exported to public API\nconst elfHooksRegistry = {};\nclass ElfHooks {\n  registerPreStoreUpdate(fn) {\n    elfHooksRegistry.preStoreUpdate = fn;\n  }\n  registerPreStateInit(fn) {\n    elfHooksRegistry.preStateInit = fn;\n  }\n}\nconst elfHooks = new ElfHooks();\nconst registry = new Map();\nconst registryActions = new Subject();\nconst registry$ = registryActions.asObservable();\n\n// @internal\nfunction addStore(store) {\n  registry.set(store.name, store);\n  registryActions.next({\n    type: 'add',\n    store\n  });\n}\n\n// @internal\nfunction removeStore(store) {\n  registry.delete(store.name);\n  registryActions.next({\n    type: 'remove',\n    store\n  });\n}\nfunction getStore(name) {\n  return registry.get(name);\n}\nfunction getRegistry() {\n  return registry;\n}\nfunction getStoresSnapshot() {\n  const stores = {};\n  registry.forEach((store, key) => {\n    stores[key] = store.getValue();\n  });\n  return stores;\n}\nlet events = [];\n\n/**\n *\n * @private function don't use\n *\n */\nfunction _setEvent(e) {\n  events.push(e);\n}\nfunction emitEvents(source) {\n  if (events.length) {\n    events.forEach(e => source.next(e));\n  }\n  events = [];\n}\nclass Store extends BehaviorSubject {\n  constructor(storeDef) {\n    super(storeDef.state);\n    this.storeDef = storeDef;\n    this.initialState = void 0;\n    this.state = void 0;\n    this.batchInProgress = false;\n    this.events = new Subject();\n    this.context = {\n      config: this.getConfig(),\n      setEvent: action => {\n        _setEvent(action);\n      }\n    };\n    this.events$ = this.events.asObservable();\n    this.state = this.getInitialState(storeDef.state);\n    this.initialState = this.getValue();\n    addStore(this);\n  }\n  get name() {\n    return this.storeDef.name;\n  }\n  getInitialState(state) {\n    if (elfHooksRegistry.preStateInit) {\n      return elfHooksRegistry.preStateInit(state, this.name);\n    }\n    return state;\n  }\n  getConfig() {\n    return this.storeDef.config;\n  }\n  query(selector) {\n    return selector(this.getValue());\n  }\n  update(...reducers) {\n    const currentState = this.getValue();\n    let nextState = reducers.reduce((value, reducer) => {\n      value = reducer(value, this.context);\n      return value;\n    }, currentState);\n    if (elfHooksRegistry.preStoreUpdate) {\n      nextState = elfHooksRegistry.preStoreUpdate(currentState, nextState, this.name);\n    }\n    if (nextState !== currentState) {\n      this.state = nextState;\n      if (batchInProgress.getValue()) {\n        if (!this.batchInProgress) {\n          this.batchInProgress = true;\n          batchDone$.subscribe(() => {\n            super.next(this.state);\n            emitEvents(this.events);\n            this.batchInProgress = false;\n          });\n        }\n      } else {\n        super.next(this.state);\n        emitEvents(this.events);\n      }\n    }\n  }\n  getValue() {\n    return this.state;\n  }\n  reset() {\n    this.update(() => this.initialState);\n  }\n  combine(observables) {\n    let hasChange = true;\n    const buffer = {};\n    return new Observable(observer => {\n      for (const [key, query] of Object.entries(observables)) {\n        observer.add(query.subscribe(value => {\n          buffer[key] = value;\n          hasChange = true;\n        }));\n      }\n      return this.subscribe({\n        next() {\n          if (hasChange) {\n            observer.next({\n              ...buffer\n            });\n            hasChange = false;\n          }\n        },\n        error(e) {\n          observer.error(e);\n        },\n        complete() {\n          observer.complete();\n        }\n      });\n    });\n  }\n  destroy() {\n    removeStore(this);\n    this.reset();\n  }\n  next(value) {\n    this.update(() => value);\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  error() {}\n\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  complete() {}\n}\nfunction createStore(storeConfig, ...propsFactories) {\n  const {\n    state,\n    config\n  } = createState(...propsFactories);\n  const {\n    name\n  } = storeConfig;\n  return new Store({\n    name,\n    state,\n    config\n  });\n}\nfunction coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nfunction isUndefined(value) {\n  return value === undefined;\n}\nfunction isString(value) {\n  return typeof value === 'string';\n}\nfunction capitalize(key) {\n  return key.charAt(0).toUpperCase() + key.slice(1);\n}\nfunction isObject(item) {\n  return typeof item === 'object' && !Array.isArray(item) && item !== null;\n}\nfunction deepFreeze(o) {\n  Object.freeze(o);\n  const oIsFunction = typeof o === 'function';\n  const hasOwnProp = Object.prototype.hasOwnProperty;\n  Object.getOwnPropertyNames(o).forEach(function (prop) {\n    if (hasOwnProp.call(o, prop) && (oIsFunction ? prop !== 'caller' && prop !== 'callee' && prop !== 'arguments' : true) && o[prop] !== null && (typeof o[prop] === 'object' || typeof o[prop] === 'function') && !Object.isFrozen(o[prop])) {\n      deepFreeze(o[prop]);\n    }\n  });\n  return o;\n}\n\n/**\n *\n * Update a root property of the state\n *\n * @example\n *\n * store.update(setProp('foo', 'bar'))\n *\n * @example\n *\n * store.update(setProp('count', count => count + 1))\n *\n */\nfunction setProp(key, value) {\n  return function (state) {\n    return {\n      ...state,\n      [key]: isFunction(value) ? value(state[key]) : value\n    };\n  };\n}\n\n/**\n *\n * Update a root property of the state\n *\n * @example\n *\n * store.update(setProps({ count: 1, bar: 'baz'}))\n *\n * @example\n *\n * store.update(setProps(state => ({\n *   count: 1,\n *   nested: {\n *     ...state.nested,\n *     foo: 'bar'\n *   }\n * })))\n *\n */\nfunction setProps(props) {\n  return function (state) {\n    return {\n      ...state,\n      ...(isFunction(props) ? props(state) : props)\n    };\n  };\n}\nfunction select(mapFn) {\n  return pipe(map(mapFn), distinctUntilChanged());\n}\nfunction head() {\n  return map(arr => arr[0]);\n}\nfunction distinctUntilArrayItemChanged() {\n  return distinctUntilChanged((prevCollection, currentCollection) => {\n    if (prevCollection === currentCollection) {\n      return true;\n    }\n    if (prevCollection.length !== currentCollection.length) {\n      return false;\n    }\n    const isOneOfItemReferenceChanged = currentCollection.some((item, i) => {\n      return prevCollection[i] !== item;\n    });\n\n    // return false means there is a change and we want to call next()\n    return !isOneOfItemReferenceChanged;\n  });\n}\nconst asap = () => debounceTime(0, asapScheduler);\nfunction filterNil() {\n  return filter(value => value !== null && value !== undefined);\n}\nfunction propsFactory(key, {\n  initialValue: propsFactoryInitialValue,\n  config\n}) {\n  let initialValue = propsFactoryInitialValue;\n  const normalizedKey = capitalize(key);\n  return {\n    [`with${normalizedKey}`](value = initialValue) {\n      return {\n        props: {\n          [key]: value\n        },\n        config\n      };\n    },\n    [`set${normalizedKey}InitialValue`](value) {\n      initialValue = value;\n    },\n    [`set${normalizedKey}`](value) {\n      return function (state) {\n        const newVal = isFunction(value) ? value(state) : value;\n        if (newVal === state[key]) {\n          return state;\n        }\n        return {\n          ...state,\n          [key]: newVal\n        };\n      };\n    },\n    [`update${normalizedKey}`](value) {\n      return function (state) {\n        const newVal = isFunction(value) ? value(state) : value;\n        if (newVal === state[key]) {\n          return state;\n        }\n        return {\n          ...state,\n          [key]: isObject(newVal) ? {\n            ...state[key],\n            ...newVal\n          } : newVal\n        };\n      };\n    },\n    [`reset${normalizedKey}`]() {\n      return function (state) {\n        return {\n          ...state,\n          [key]: initialValue\n        };\n      };\n    },\n    [`select${normalizedKey}`]() {\n      return select(state => state[key]);\n    },\n    [`get${normalizedKey}`](state) {\n      return state[key];\n    }\n  };\n}\nfunction propsArrayFactory(key, options) {\n  const normalizedKey = capitalize(key);\n  const base = propsFactory(key, options);\n  return {\n    ...base,\n    [`add${normalizedKey}`](items) {\n      return function (state) {\n        return {\n          ...state,\n          [key]: arrayAdd(state[key], items)\n        };\n      };\n    },\n    [`remove${normalizedKey}`](items) {\n      return function (state) {\n        return {\n          ...state,\n          [key]: arrayRemove(state[key], items)\n        };\n      };\n    },\n    [`toggle${normalizedKey}`](items) {\n      return function (state) {\n        return {\n          ...state,\n          [key]: arrayToggle(state[key], items)\n        };\n      };\n    },\n    [`update${normalizedKey}`](predicateOrIds, obj) {\n      return function (state) {\n        return {\n          ...state,\n          [key]: arrayUpdate(state[key], predicateOrIds, obj)\n        };\n      };\n    },\n    [`in${normalizedKey}`](item) {\n      return state => inArray(state[key], item);\n    }\n  };\n}\nfunction arrayAdd(arr, items) {\n  return [...arr, ...coerceArray(items)];\n}\nfunction arrayRemove(arr, items) {\n  const toArray = coerceArray(items);\n  return arr.filter(current => !toArray.includes(current));\n}\nfunction arrayToggle(arr, items) {\n  const toArray = coerceArray(items);\n  const result = [...arr];\n  toArray.forEach(item => {\n    const i = result.indexOf(item);\n    i > -1 ? result.splice(i, 1) : result.push(item);\n  });\n  return result;\n}\nfunction inArray(arr, item) {\n  return arr.includes(item);\n}\nfunction arrayUpdate(arr, item, newItem) {\n  return arr.map(current => {\n    return current === item ? newItem : current;\n  });\n}\nfunction withProps(props) {\n  return {\n    props,\n    config: undefined\n  };\n}\nlet __DEV__ = true;\nfunction enableElfProdMode() {\n  __DEV__ = false;\n}\n\n// @internal\nfunction isDev() {\n  return __DEV__;\n}\nexport { Store, _setEvent, asap, capitalize, coerceArray, createState, createStore, deepFreeze, distinctUntilArrayItemChanged, elfHooks, emitOnce, emitOnceAsync, enableElfProdMode, filterNil, getRegistry, getStore, getStoresSnapshot, head, isDev, isFunction, isObject, isString, isUndefined, propsArrayFactory, propsFactory, registry$, select, setProp, setProps, withProps };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAS,eAAe,gBAAgB;AACtC,QAAM,SAAS;AAAA,IACb,QAAQ,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,EACV;AACA,aAAW;AAAA,IACT;AAAA,IACA;AAAA,EACF,KAAK,gBAAgB;AACnB,WAAO,OAAO,OAAO,QAAQ,MAAM;AACnC,WAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACnC;AACA,SAAO;AACT;AACA,IAAI,yBAAyB;AAC7B,IAAM,kBAAkB,IAAI,gBAAgB,KAAK;AACjD,IAAM,aAAa,gBAAgB,aAAa,EAAE,KAAK,OAAO,gBAAc,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;AACjG,SAAS,SAAS,IAAI;AACpB,MAAI,CAAC,gBAAgB,SAAS,GAAG;AAC/B,oBAAgB,KAAK,IAAI;AACzB,UAAM,QAAQ,GAAG;AACjB,QAAI,2BAA2B,GAAG;AAChC,sBAAgB,KAAK,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACA,SAAO,GAAG;AACZ;AACA,SAAe,cAAc,IAAI;AAAA;AAC/B;AACA,QAAI,CAAC,gBAAgB,SAAS,GAAG;AAC/B,sBAAgB,KAAK,IAAI;AAAA,IAC3B;AACA,UAAM,sBAAsB,GAAG;AAC/B,UAAM,QAAQ,MAAO,aAAa,mBAAmB,IAAI,eAAe,mBAAmB,IAAI;AAC/F,QAAI,EAAE,2BAA2B,GAAG;AAClC,sBAAgB,KAAK,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA;AAGA,IAAM,mBAAmB,CAAC;AAC1B,IAAM,WAAN,MAAe;AAAA,EACb,uBAAuB,IAAI;AACzB,qBAAiB,iBAAiB;AAAA,EACpC;AAAA,EACA,qBAAqB,IAAI;AACvB,qBAAiB,eAAe;AAAA,EAClC;AACF;AACA,IAAM,WAAW,IAAI,SAAS;AAC9B,IAAM,WAAW,oBAAI,IAAI;AACzB,IAAM,kBAAkB,IAAI,QAAQ;AACpC,IAAM,YAAY,gBAAgB,aAAa;AAG/C,SAAS,SAAS,OAAO;AACvB,WAAS,IAAI,MAAM,MAAM,KAAK;AAC9B,kBAAgB,KAAK;AAAA,IACnB,MAAM;AAAA,IACN;AAAA,EACF,CAAC;AACH;AAGA,SAAS,YAAY,OAAO;AAC1B,WAAS,OAAO,MAAM,IAAI;AAC1B,kBAAgB,KAAK;AAAA,IACnB,MAAM;AAAA,IACN;AAAA,EACF,CAAC;AACH;AACA,SAAS,SAAS,MAAM;AACtB,SAAO,SAAS,IAAI,IAAI;AAC1B;AACA,SAAS,cAAc;AACrB,SAAO;AACT;AACA,SAAS,oBAAoB;AAC3B,QAAM,SAAS,CAAC;AAChB,WAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,WAAO,GAAG,IAAI,MAAM,SAAS;AAAA,EAC/B,CAAC;AACD,SAAO;AACT;AACA,IAAI,SAAS,CAAC;AAOd,SAAS,UAAU,GAAG;AACpB,SAAO,KAAK,CAAC;AACf;AACA,SAAS,WAAW,QAAQ;AAC1B,MAAI,OAAO,QAAQ;AACjB,WAAO,QAAQ,OAAK,OAAO,KAAK,CAAC,CAAC;AAAA,EACpC;AACA,WAAS,CAAC;AACZ;AACA,IAAM,QAAN,cAAoB,gBAAgB;AAAA,EAClC,YAAY,UAAU;AACpB,UAAM,SAAS,KAAK;AACpB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,SAAS,IAAI,QAAQ;AAC1B,SAAK,UAAU;AAAA,MACb,QAAQ,KAAK,UAAU;AAAA,MACvB,UAAU,YAAU;AAClB,kBAAU,MAAM;AAAA,MAClB;AAAA,IACF;AACA,SAAK,UAAU,KAAK,OAAO,aAAa;AACxC,SAAK,QAAQ,KAAK,gBAAgB,SAAS,KAAK;AAChD,SAAK,eAAe,KAAK,SAAS;AAClC,aAAS,IAAI;AAAA,EACf;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,gBAAgB,OAAO;AACrB,QAAI,iBAAiB,cAAc;AACjC,aAAO,iBAAiB,aAAa,OAAO,KAAK,IAAI;AAAA,IACvD;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,MAAM,UAAU;AACd,WAAO,SAAS,KAAK,SAAS,CAAC;AAAA,EACjC;AAAA,EACA,UAAU,UAAU;AAClB,UAAM,eAAe,KAAK,SAAS;AACnC,QAAI,YAAY,SAAS,OAAO,CAAC,OAAO,YAAY;AAClD,cAAQ,QAAQ,OAAO,KAAK,OAAO;AACnC,aAAO;AAAA,IACT,GAAG,YAAY;AACf,QAAI,iBAAiB,gBAAgB;AACnC,kBAAY,iBAAiB,eAAe,cAAc,WAAW,KAAK,IAAI;AAAA,IAChF;AACA,QAAI,cAAc,cAAc;AAC9B,WAAK,QAAQ;AACb,UAAI,gBAAgB,SAAS,GAAG;AAC9B,YAAI,CAAC,KAAK,iBAAiB;AACzB,eAAK,kBAAkB;AACvB,qBAAW,UAAU,MAAM;AACzB,kBAAM,KAAK,KAAK,KAAK;AACrB,uBAAW,KAAK,MAAM;AACtB,iBAAK,kBAAkB;AAAA,UACzB,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,cAAM,KAAK,KAAK,KAAK;AACrB,mBAAW,KAAK,MAAM;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,QAAQ;AACN,SAAK,OAAO,MAAM,KAAK,YAAY;AAAA,EACrC;AAAA,EACA,QAAQ,aAAa;AACnB,QAAI,YAAY;AAChB,UAAM,SAAS,CAAC;AAChB,WAAO,IAAI,WAAW,cAAY;AAChC,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,WAAW,GAAG;AACtD,iBAAS,IAAI,MAAM,UAAU,WAAS;AACpC,iBAAO,GAAG,IAAI;AACd,sBAAY;AAAA,QACd,CAAC,CAAC;AAAA,MACJ;AACA,aAAO,KAAK,UAAU;AAAA,QACpB,OAAO;AACL,cAAI,WAAW;AACb,qBAAS,KAAK,mBACT,OACJ;AACD,wBAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,MAAM,GAAG;AACP,mBAAS,MAAM,CAAC;AAAA,QAClB;AAAA,QACA,WAAW;AACT,mBAAS,SAAS;AAAA,QACpB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,gBAAY,IAAI;AAChB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,KAAK,OAAO;AACV,SAAK,OAAO,MAAM,KAAK;AAAA,EACzB;AAAA;AAAA,EAGA,QAAQ;AAAA,EAAC;AAAA;AAAA,EAGT,WAAW;AAAA,EAAC;AACd;AACA,SAAS,YAAY,gBAAgB,gBAAgB;AACnD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,YAAY,GAAG,cAAc;AACjC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,IAAI,MAAM;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,UAAU;AACnB;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC;AAClD;AACA,SAAS,SAAS,MAAM;AACtB,SAAO,OAAO,SAAS,YAAY,CAAC,MAAM,QAAQ,IAAI,KAAK,SAAS;AACtE;AACA,SAAS,WAAW,GAAG;AACrB,SAAO,OAAO,CAAC;AACf,QAAM,cAAc,OAAO,MAAM;AACjC,QAAM,aAAa,OAAO,UAAU;AACpC,SAAO,oBAAoB,CAAC,EAAE,QAAQ,SAAU,MAAM;AACpD,QAAI,WAAW,KAAK,GAAG,IAAI,MAAM,cAAc,SAAS,YAAY,SAAS,YAAY,SAAS,cAAc,SAAS,EAAE,IAAI,MAAM,SAAS,OAAO,EAAE,IAAI,MAAM,YAAY,OAAO,EAAE,IAAI,MAAM,eAAe,CAAC,OAAO,SAAS,EAAE,IAAI,CAAC,GAAG;AACxO,iBAAW,EAAE,IAAI,CAAC;AAAA,IACpB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAeA,SAAS,QAAQ,KAAK,OAAO;AAC3B,SAAO,SAAU,OAAO;AACtB,WAAO,iCACF,QADE;AAAA,MAEL,CAAC,GAAG,GAAG,WAAW,KAAK,IAAI,MAAM,MAAM,GAAG,CAAC,IAAI;AAAA,IACjD;AAAA,EACF;AACF;AAqBA,SAAS,SAAS,OAAO;AACvB,SAAO,SAAU,OAAO;AACtB,WAAO,kCACF,QACC,WAAW,KAAK,IAAI,MAAM,KAAK,IAAI;AAAA,EAE3C;AACF;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,KAAK,IAAI,KAAK,GAAG,qBAAqB,CAAC;AAChD;AACA,SAAS,OAAO;AACd,SAAO,IAAI,SAAO,IAAI,CAAC,CAAC;AAC1B;AACA,SAAS,gCAAgC;AACvC,SAAO,qBAAqB,CAAC,gBAAgB,sBAAsB;AACjE,QAAI,mBAAmB,mBAAmB;AACxC,aAAO;AAAA,IACT;AACA,QAAI,eAAe,WAAW,kBAAkB,QAAQ;AACtD,aAAO;AAAA,IACT;AACA,UAAM,8BAA8B,kBAAkB,KAAK,CAAC,MAAM,MAAM;AACtE,aAAO,eAAe,CAAC,MAAM;AAAA,IAC/B,CAAC;AAGD,WAAO,CAAC;AAAA,EACV,CAAC;AACH;AACA,IAAM,OAAO,MAAM,aAAa,GAAG,aAAa;AAChD,SAAS,YAAY;AACnB,SAAO,OAAO,WAAS,UAAU,QAAQ,UAAU,MAAS;AAC9D;AACA,SAAS,aAAa,KAAK;AAAA,EACzB,cAAc;AAAA,EACd;AACF,GAAG;AACD,MAAI,eAAe;AACnB,QAAM,gBAAgB,WAAW,GAAG;AACpC,SAAO;AAAA,IACL,CAAC,OAAO,aAAa,EAAE,EAAE,QAAQ,cAAc;AAC7C,aAAO;AAAA,QACL,OAAO;AAAA,UACL,CAAC,GAAG,GAAG;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,MAAM,aAAa,cAAc,EAAE,OAAO;AACzC,qBAAe;AAAA,IACjB;AAAA,IACA,CAAC,MAAM,aAAa,EAAE,EAAE,OAAO;AAC7B,aAAO,SAAU,OAAO;AACtB,cAAM,SAAS,WAAW,KAAK,IAAI,MAAM,KAAK,IAAI;AAClD,YAAI,WAAW,MAAM,GAAG,GAAG;AACzB,iBAAO;AAAA,QACT;AACA,eAAO,iCACF,QADE;AAAA,UAEL,CAAC,GAAG,GAAG;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,SAAS,aAAa,EAAE,EAAE,OAAO;AAChC,aAAO,SAAU,OAAO;AACtB,cAAM,SAAS,WAAW,KAAK,IAAI,MAAM,KAAK,IAAI;AAClD,YAAI,WAAW,MAAM,GAAG,GAAG;AACzB,iBAAO;AAAA,QACT;AACA,eAAO,iCACF,QADE;AAAA,UAEL,CAAC,GAAG,GAAG,SAAS,MAAM,IAAI,kCACrB,MAAM,GAAG,IACT,UACD;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,QAAQ,aAAa,EAAE,IAAI;AAC1B,aAAO,SAAU,OAAO;AACtB,eAAO,iCACF,QADE;AAAA,UAEL,CAAC,GAAG,GAAG;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,SAAS,aAAa,EAAE,IAAI;AAC3B,aAAO,OAAO,WAAS,MAAM,GAAG,CAAC;AAAA,IACnC;AAAA,IACA,CAAC,MAAM,aAAa,EAAE,EAAE,OAAO;AAC7B,aAAO,MAAM,GAAG;AAAA,IAClB;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,KAAK,SAAS;AACvC,QAAM,gBAAgB,WAAW,GAAG;AACpC,QAAM,OAAO,aAAa,KAAK,OAAO;AACtC,SAAO,iCACF,OADE;AAAA,IAEL,CAAC,MAAM,aAAa,EAAE,EAAE,OAAO;AAC7B,aAAO,SAAU,OAAO;AACtB,eAAO,iCACF,QADE;AAAA,UAEL,CAAC,GAAG,GAAG,SAAS,MAAM,GAAG,GAAG,KAAK;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,SAAS,aAAa,EAAE,EAAE,OAAO;AAChC,aAAO,SAAU,OAAO;AACtB,eAAO,iCACF,QADE;AAAA,UAEL,CAAC,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,KAAK;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,SAAS,aAAa,EAAE,EAAE,OAAO;AAChC,aAAO,SAAU,OAAO;AACtB,eAAO,iCACF,QADE;AAAA,UAEL,CAAC,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,KAAK;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,SAAS,aAAa,EAAE,EAAE,gBAAgB,KAAK;AAC9C,aAAO,SAAU,OAAO;AACtB,eAAO,iCACF,QADE;AAAA,UAEL,CAAC,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,gBAAgB,GAAG;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,KAAK,aAAa,EAAE,EAAE,MAAM;AAC3B,aAAO,WAAS,QAAQ,MAAM,GAAG,GAAG,IAAI;AAAA,IAC1C;AAAA,EACF;AACF;AACA,SAAS,SAAS,KAAK,OAAO;AAC5B,SAAO,CAAC,GAAG,KAAK,GAAG,YAAY,KAAK,CAAC;AACvC;AACA,SAAS,YAAY,KAAK,OAAO;AAC/B,QAAM,UAAU,YAAY,KAAK;AACjC,SAAO,IAAI,OAAO,aAAW,CAAC,QAAQ,SAAS,OAAO,CAAC;AACzD;AACA,SAAS,YAAY,KAAK,OAAO;AAC/B,QAAM,UAAU,YAAY,KAAK;AACjC,QAAM,SAAS,CAAC,GAAG,GAAG;AACtB,UAAQ,QAAQ,UAAQ;AACtB,UAAM,IAAI,OAAO,QAAQ,IAAI;AAC7B,QAAI,KAAK,OAAO,OAAO,GAAG,CAAC,IAAI,OAAO,KAAK,IAAI;AAAA,EACjD,CAAC;AACD,SAAO;AACT;AACA,SAAS,QAAQ,KAAK,MAAM;AAC1B,SAAO,IAAI,SAAS,IAAI;AAC1B;AACA,SAAS,YAAY,KAAK,MAAM,SAAS;AACvC,SAAO,IAAI,IAAI,aAAW;AACxB,WAAO,YAAY,OAAO,UAAU;AAAA,EACtC,CAAC;AACH;AACA,SAAS,UAAU,OAAO;AACxB,SAAO;AAAA,IACL;AAAA,IACA,QAAQ;AAAA,EACV;AACF;AACA,IAAI,UAAU;AACd,SAAS,oBAAoB;AAC3B,YAAU;AACZ;AAGA,SAAS,QAAQ;AACf,SAAO;AACT;", "names": []}