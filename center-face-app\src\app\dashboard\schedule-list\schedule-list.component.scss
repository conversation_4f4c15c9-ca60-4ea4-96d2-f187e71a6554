:host {
  height: 100%;
  display: flex;
}

.exam-schedule {
  flex: 1;
  padding: 16px;
  background-color: #f5f5f5;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 16px;

    .title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }

    .exam-number {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #666;

      .settings-btn {
        border: none;
        background: none;
        padding: 4px;
        cursor: pointer;
        
        .settings-icon {
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  .date-group {
    margin-bottom: 24px;

    .date {
      font-size: 16px;
      font-weight: 500;
      margin: 16px 0;
      color: #333;
    }
  }

  .schedule-card {
    cursor: pointer;
    opacity: 100%;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    &.active {
      background:  linear-gradient( 90deg, rgba(65,156,248,0) 0%, rgb(238 246 254) 100%);
      border-radius: 9px;
      box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1), 0px 6px 6px 0px rgba(0,0,0,0.09), 0px 13px 8px 0px rgba(0,0,0,0.05), 0px 23px 9px 0px rgba(0,0,0,0.01), 0px 36px 10px 0px rgba(0,0,0,0);
    }
    &.not-start {
      background:  linear-gradient( 90deg, rgba(65,156,248,0) 0%, rgb(238 246 254) 100%);
      background: #fff;
    }
    .schedule-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .subject-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .status {
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 14px;
        min-width: fit-content;

        &.completed {
          background: #f0f0f0;
          color: #999;
        }

        &.not-start {
          background: #f0f0f0;
          color: #999;
        }

        &.active {
          background: #e6f7ff;
          color: #1890ff;
        }
      }
    }

    .schedule-info {
      display: flex;
      gap: 24px;
      color: #666;
      font-size: 14px;

      > div {
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          width: 16px;
          height: 16px;
          display: inline-block;
        }

      }
    }
  }
}

// 响应式布局
@media screen and (max-width: 768px) {
  .exam-schedule {
    padding: 12px;

    .subject-info {
      flex-direction: column;
      gap: 12px;
    }
  }
}

@media screen and (max-width: 500px) {
  .label {
    display: none;
  }
}
