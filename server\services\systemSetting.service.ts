import * as fs from 'node:fs';
import * as path from 'node:path';
import { getIPAddresses } from '@server/utils/utils';
import { NetworkConfig } from '@share-types/settings.types';

export interface SystemSettingInfo {
  ip: {
    binding_ip: string;
    ipList: string[];
  };
}
export interface SystemSetting {
  binding_ip: string;
}
interface SystemSettingEnv {
  data_path: string;
}

export class SystemSettingService {
  env: SystemSettingEnv;
  constructor(env: SystemSettingEnv) {
    this.env = env;
  }

  getSetting(): SystemSettingInfo {
    const ipList = getIPAddresses();
    const config = this.getNetWorkInfo();
    const binding_ip =
      config?.binding_ip && ipList.includes(config.binding_ip)
        ? config.binding_ip
        : ipList[0];
    return {
      ip: {
        binding_ip,
        ipList,
      },
    };
  }

  setSetting(setting: Partial<SystemSetting>) {
    const { binding_ip } = setting;
    if (binding_ip) {
      this.setNetWorkInfo({ binding_ip });
    }
  }

  // 获取网络配置文件信息
  getNetWorkInfo(): NetworkConfig {
    if (joyshell) {
      return joyshell.GetNetworkConfig();
    }

    // Development mode
    const file_path = path.join(this.env.data_path, 'network.json');
    console.log('SystemSetting: read network config');
    try {
      const file = fs.readFileSync(file_path, 'utf8');
      if (typeof file === 'string') {
        return JSON.parse(file) as NetworkConfig;
      }
      return {} as NetworkConfig;
    } catch (err: any) {
      if (err.code === 'ENOENT') {
        console.log('SystemSetting: no network file');
        return {} as NetworkConfig;
      } else {
        console.error('SystemSetting: getNetWorkFile error', err);
        throw err;
      }
    }
  }
  // 设置网络配置信息
  setNetWorkInfo(info: NetworkConfig) {
    if (joyshell) {
      joyshell.SetNetworkConfig(info);
      return;
    }

    const network_path = path.join(this.env.data_path, 'network.json');
    // Development mode
    try {
      if (fs.existsSync(network_path)) {
        const file_content = JSON.parse(fs.readFileSync(network_path, 'utf8'));
        const new_file = JSON.stringify(
          Object.assign(file_content, info),
          null,
          2,
        );
        fs.writeFileSync(network_path, new_file, 'utf8');
      } else {
        fs.writeFileSync(network_path, JSON.stringify(info, null, 2), 'utf8');
      }
    } catch (err) {
      console.error('SystemSetting: setNetWorkInfo error', err);
      throw err;
    }
  }
}
