import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';
import { environment } from "./environments/environment";
import { enableProdMode } from '@angular/core';
import { devTools } from '@ngneat/elf-devtools';

if (environment.production) {
  enableProdMode();
}
if (environment.statusDevTools) {
  devTools();
}
bootstrapApplication(AppComponent, appConfig).catch((err) =>
  console.error(err)
);
