import { BrowserWindow, dialog, ipc<PERSON>ain, shell } from "electron";
import * as config from "./config";
import * as IPC from "@share-types/ipc.constants";

ipcMain.on("ipc-open-devtools", (event /* arg */) => {
  console.log("ipc-open-devtools");
  event.sender.openDevTools({ mode: "detach" });
});

ipcMain.handle(IPC.IPC_GET_SETTINGS, () => {
  return {
    appName: config.APP_NAME,
    appVersion: config.APP_VERSION,
    settings: config.settings,
    userSettings: config.getUserConfig(),
    roomInfo: config.getCenterInfo(),
    mainArgv: process.argv.slice(1),
    serverUrl: config.getServerUrl()
  }
})

ipcMain.handle(IPC.IPC_UPDATE_USER_SETTINGS, function (_event, cfg, value) {
  // if (cfg === "remote") {
  //   if (cfg?.HOTFIXJS) {
  //     log.info("remote config:", Object.assign({}, value, { HOTFIXJS: "..." }));
  //   } else {
  //     log.info("remote config:", value);
  //   }
  // }
  config.setUserConfig(cfg, value);
});

ipcMain.handle(IPC.IPC_OPEN_URL, function (_event, url) {
  shell.openExternal(url);
});

ipcMain.handle("ipc-show-save-dialog", async (event, args) => {
  if (event.sender) {
    const win = BrowserWindow.fromWebContents(event.sender);
    if (!win) {
      return { canceled: true };
    }
    return dialog.showSaveDialog(win, args);
  }
  return { canceled: true };
});

ipcMain.handle("ipc-show-open-dialog", async (event, args) => {
  if (event.sender) {
    const win = BrowserWindow.fromWebContents(event.sender);
    if (!win) {
      return { canceled: true };
    }
    return dialog.showOpenDialog(win, args);
  }
  return { canceled: true };
});
