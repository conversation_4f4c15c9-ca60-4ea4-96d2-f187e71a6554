import { inject } from '@angular/core';
import {
  HttpRequest,
  HttpEvent,
  HttpHeaders,
  HttpHandlerFn,
  HttpResponseBase,
} from '@angular/common/http';
import { Observable, from, map, switchMap } from 'rxjs';
import { AuthService } from '../service/auth.service';
import { AUTH_DISABLE } from './cloud.http';
import { ServerTimeService } from '../service/server-time.service';
import { environment } from '../../../environments/environment';
import { randomString } from '@app/utils/randomString';
import { ModalService } from '../service/modal.service';
import { Router } from '@angular/router';

export function apiPrefixInterceptor(req: HttpRequest<unknown>, next: HttpHandlerFn): Observable<HttpEvent<unknown>> {
  let opt: { url?: string; headers: HttpHeaders } = { headers: req.headers };
  const apiUrl = environment.apiUrl || window.location.origin;

  if (!req.url.includes('http')) {
    opt.url = `${apiUrl}${req.url}`;
  }

  if (req.context.get(AUTH_DISABLE)) {
    return next(req.clone(opt));
  }

  const serverTime = Math.floor((inject(ServerTimeService).getServerTimeValue() || new Date().getTime()) / 1000);
  
  const authService = inject(AuthService)
  const authToken = authService.authToken;
  const xVid = authToken;
  const xRandom = `${serverTime}-${randomString(5)}`;
  const xDtype = 'venue_signin';
  const secret = authService.hardwareId;

  return from(hmacSha256(secret, `${xVid}${xRandom}${xDtype}`)).pipe(
    switchMap(xSign => {
      opt.headers = opt.headers
        .set('X-VID', xVid)
        .set('X-RANDOM', xRandom)
        .set('X-DTYPE', xDtype)
        .set('X-SIGN', xSign);

      return next(req.clone(opt));
    })
  );

}

export function errorInterceptor(req: HttpRequest<unknown>, next: HttpHandlerFn): Observable<HttpEvent<unknown>> {
  const authDisable = req.context.get(AUTH_DISABLE);
  if (authDisable) {
    return next(req);
  }
  const modal = inject(ModalService);
  const router = inject(Router);
  return next(req).pipe(
    map((res) => {
      if (res instanceof HttpResponseBase) {
        if (res.status === 403) {
          modal.error('提示', '登录已失效，请重新登录');
          router.navigate(['/login'], { queryParams: { relogin: true } });
        }
      }
      return res;
    })
  );
}


async function hmacSha256(secret: string, data: string) {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: { name: 'SHA-256' } },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
  return Array.from(new Uint8Array(signature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

