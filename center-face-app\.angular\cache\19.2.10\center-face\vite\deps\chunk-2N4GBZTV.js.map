{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/util.js", "../../../../../../node_modules/.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/conversion.js", "../../../../../../node_modules/.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/css-color-names.js", "../../../../../../node_modules/.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/format-input.js", "../../../../../../node_modules/.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/index.js", "../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-color.mjs", "../../../../../../node_modules/.pnpm/ng-zorro-antd@19.2.2_aloxaa5nsw4iupj3sqdeenrxmm/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-config.mjs"], "sourcesContent": ["/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nexport function bound01(n, max) {\n  if (isOnePointZero(n)) {\n    n = '100%';\n  }\n  var isPercent = isPercentage(n);\n  n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n  // Automatically convert percentage into number\n  if (isPercent) {\n    n = parseInt(String(n * max), 10) / 100;\n  }\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n  // Convert into [0, 1] range if it isn't already\n  if (max === 360) {\n    // If n is a hue given in degrees,\n    // wrap around out-of-range values into [0, 360] range\n    // then convert into [0, 1].\n    n = (n < 0 ? n % max + max : n % max) / parseFloat(String(max));\n  } else {\n    // If n not a hue given in degrees\n    // Convert into [0, 1] range if it isn't already.\n    n = n % max / parseFloat(String(max));\n  }\n  return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nexport function clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nexport function isOnePointZero(n) {\n  return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nexport function isPercentage(n) {\n  return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nexport function boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nexport function convertToPercentage(n) {\n  if (n <= 1) {\n    return \"\".concat(Number(n) * 100, \"%\");\n  }\n  return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nexport function pad2(c) {\n  return c.length === 1 ? '0' + c : String(c);\n}", "import { bound01, pad2 } from './util.js';\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */\nexport function rgbToRgb(r, g, b) {\n  return {\n    r: bound01(r, 255) * 255,\n    g: bound01(g, 255) * 255,\n    b: bound01(b, 255) * 255\n  };\n}\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */\nexport function rgbToHsl(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b);\n  var min = Math.min(r, g, b);\n  var h = 0;\n  var s = 0;\n  var l = (max + min) / 2;\n  if (max === min) {\n    s = 0;\n    h = 0; // achromatic\n  } else {\n    var d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n      default:\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    l: l\n  };\n}\nfunction hue2rgb(p, q, t) {\n  if (t < 0) {\n    t += 1;\n  }\n  if (t > 1) {\n    t -= 1;\n  }\n  if (t < 1 / 6) {\n    return p + (q - p) * (6 * t);\n  }\n  if (t < 1 / 2) {\n    return q;\n  }\n  if (t < 2 / 3) {\n    return p + (q - p) * (2 / 3 - t) * 6;\n  }\n  return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hslToRgb(h, s, l) {\n  var r;\n  var g;\n  var b;\n  h = bound01(h, 360);\n  s = bound01(s, 100);\n  l = bound01(l, 100);\n  if (s === 0) {\n    // achromatic\n    g = l;\n    b = l;\n    r = l;\n  } else {\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */\nexport function rgbToHsv(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b);\n  var min = Math.min(r, g, b);\n  var h = 0;\n  var v = max;\n  var d = max - min;\n  var s = max === 0 ? 0 : d / max;\n  if (max === min) {\n    h = 0; // achromatic\n  } else {\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n      default:\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    v: v\n  };\n}\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hsvToRgb(h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  var i = Math.floor(h);\n  var f = h - i;\n  var p = v * (1 - s);\n  var q = v * (1 - f * s);\n  var t = v * (1 - (1 - f) * s);\n  var mod = i % 6;\n  var r = [v, q, p, p, t, v][mod];\n  var g = [t, v, v, q, p, p][mod];\n  var b = [p, p, t, v, v, q][mod];\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n/**\n * Converts an RGB color to hex\n *\n * Assumes r, g, and b are contained in the set [0, 255]\n * Returns a 3 or 6 character hex\n */\nexport function rgbToHex(r, g, b, allow3Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  // Return a 3 character hex if possible\n  if (allow3Char && hex[0].startsWith(hex[0].charAt(1)) && hex[1].startsWith(hex[1].charAt(1)) && hex[2].startsWith(hex[2].charAt(1))) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n  }\n  return hex.join('');\n}\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * Assumes r, g, b are contained in the set [0, 255] and\n * a in [0, 1]. Returns a 4 or 8 character rgba hex\n */\n// eslint-disable-next-line max-params\nexport function rgbaToHex(r, g, b, a, allow4Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n  // Return a 4 character hex if possible\n  if (allow4Char && hex[0].startsWith(hex[0].charAt(1)) && hex[1].startsWith(hex[1].charAt(1)) && hex[2].startsWith(hex[2].charAt(1)) && hex[3].startsWith(hex[3].charAt(1))) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n  }\n  return hex.join('');\n}\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n */\nexport function rgbaToArgbHex(r, g, b, a) {\n  var hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  return hex.join('');\n}\n/** Converts a decimal to a hex value */\nexport function convertDecimalToHex(d) {\n  return Math.round(parseFloat(d) * 255).toString(16);\n}\n/** Converts a hex value to a decimal */\nexport function convertHexToDecimal(h) {\n  return parseIntFromHex(h) / 255;\n}\n/** Parse a base-16 hex value into a base-10 integer */\nexport function parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\nexport function numberInputToObject(color) {\n  return {\n    r: color >> 16,\n    g: (color & 0xff00) >> 8,\n    b: color & 0xff\n  };\n}", "// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nexport var names = {\n  aliceblue: '#f0f8ff',\n  antiquewhite: '#faebd7',\n  aqua: '#00ffff',\n  aquamarine: '#7fffd4',\n  azure: '#f0ffff',\n  beige: '#f5f5dc',\n  bisque: '#ffe4c4',\n  black: '#000000',\n  blanchedalmond: '#ffebcd',\n  blue: '#0000ff',\n  blueviolet: '#8a2be2',\n  brown: '#a52a2a',\n  burlywood: '#deb887',\n  cadetblue: '#5f9ea0',\n  chartreuse: '#7fff00',\n  chocolate: '#d2691e',\n  coral: '#ff7f50',\n  cornflowerblue: '#6495ed',\n  cornsilk: '#fff8dc',\n  crimson: '#dc143c',\n  cyan: '#00ffff',\n  darkblue: '#00008b',\n  darkcyan: '#008b8b',\n  darkgoldenrod: '#b8860b',\n  darkgray: '#a9a9a9',\n  darkgreen: '#006400',\n  darkgrey: '#a9a9a9',\n  darkkhaki: '#bdb76b',\n  darkmagenta: '#8b008b',\n  darkolivegreen: '#556b2f',\n  darkorange: '#ff8c00',\n  darkorchid: '#9932cc',\n  darkred: '#8b0000',\n  darksalmon: '#e9967a',\n  darkseagreen: '#8fbc8f',\n  darkslateblue: '#483d8b',\n  darkslategray: '#2f4f4f',\n  darkslategrey: '#2f4f4f',\n  darkturquoise: '#00ced1',\n  darkviolet: '#9400d3',\n  deeppink: '#ff1493',\n  deepskyblue: '#00bfff',\n  dimgray: '#696969',\n  dimgrey: '#696969',\n  dodgerblue: '#1e90ff',\n  firebrick: '#b22222',\n  floralwhite: '#fffaf0',\n  forestgreen: '#228b22',\n  fuchsia: '#ff00ff',\n  gainsboro: '#dcdcdc',\n  ghostwhite: '#f8f8ff',\n  goldenrod: '#daa520',\n  gold: '#ffd700',\n  gray: '#808080',\n  green: '#008000',\n  greenyellow: '#adff2f',\n  grey: '#808080',\n  honeydew: '#f0fff0',\n  hotpink: '#ff69b4',\n  indianred: '#cd5c5c',\n  indigo: '#4b0082',\n  ivory: '#fffff0',\n  khaki: '#f0e68c',\n  lavenderblush: '#fff0f5',\n  lavender: '#e6e6fa',\n  lawngreen: '#7cfc00',\n  lemonchiffon: '#fffacd',\n  lightblue: '#add8e6',\n  lightcoral: '#f08080',\n  lightcyan: '#e0ffff',\n  lightgoldenrodyellow: '#fafad2',\n  lightgray: '#d3d3d3',\n  lightgreen: '#90ee90',\n  lightgrey: '#d3d3d3',\n  lightpink: '#ffb6c1',\n  lightsalmon: '#ffa07a',\n  lightseagreen: '#20b2aa',\n  lightskyblue: '#87cefa',\n  lightslategray: '#778899',\n  lightslategrey: '#778899',\n  lightsteelblue: '#b0c4de',\n  lightyellow: '#ffffe0',\n  lime: '#00ff00',\n  limegreen: '#32cd32',\n  linen: '#faf0e6',\n  magenta: '#ff00ff',\n  maroon: '#800000',\n  mediumaquamarine: '#66cdaa',\n  mediumblue: '#0000cd',\n  mediumorchid: '#ba55d3',\n  mediumpurple: '#9370db',\n  mediumseagreen: '#3cb371',\n  mediumslateblue: '#7b68ee',\n  mediumspringgreen: '#00fa9a',\n  mediumturquoise: '#48d1cc',\n  mediumvioletred: '#c71585',\n  midnightblue: '#191970',\n  mintcream: '#f5fffa',\n  mistyrose: '#ffe4e1',\n  moccasin: '#ffe4b5',\n  navajowhite: '#ffdead',\n  navy: '#000080',\n  oldlace: '#fdf5e6',\n  olive: '#808000',\n  olivedrab: '#6b8e23',\n  orange: '#ffa500',\n  orangered: '#ff4500',\n  orchid: '#da70d6',\n  palegoldenrod: '#eee8aa',\n  palegreen: '#98fb98',\n  paleturquoise: '#afeeee',\n  palevioletred: '#db7093',\n  papayawhip: '#ffefd5',\n  peachpuff: '#ffdab9',\n  peru: '#cd853f',\n  pink: '#ffc0cb',\n  plum: '#dda0dd',\n  powderblue: '#b0e0e6',\n  purple: '#800080',\n  rebeccapurple: '#663399',\n  red: '#ff0000',\n  rosybrown: '#bc8f8f',\n  royalblue: '#4169e1',\n  saddlebrown: '#8b4513',\n  salmon: '#fa8072',\n  sandybrown: '#f4a460',\n  seagreen: '#2e8b57',\n  seashell: '#fff5ee',\n  sienna: '#a0522d',\n  silver: '#c0c0c0',\n  skyblue: '#87ceeb',\n  slateblue: '#6a5acd',\n  slategray: '#708090',\n  slategrey: '#708090',\n  snow: '#fffafa',\n  springgreen: '#00ff7f',\n  steelblue: '#4682b4',\n  tan: '#d2b48c',\n  teal: '#008080',\n  thistle: '#d8bfd8',\n  tomato: '#ff6347',\n  turquoise: '#40e0d0',\n  violet: '#ee82ee',\n  wheat: '#f5deb3',\n  white: '#ffffff',\n  whitesmoke: '#f5f5f5',\n  yellow: '#ffff00',\n  yellowgreen: '#9acd32'\n};", "/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\nimport { convertHexToDecimal, hslToRgb, hsvToRgb, parseIntFromHex, rgbToRgb } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { boundAlpha, convertToPercentage } from './util.js';\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nexport function inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color === 'string') {\n    color = stringInputToObject(color);\n  }\n  if (typeof color === 'object') {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = 'hsv';\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = 'hsl';\n    }\n    if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok: ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a: a\n  };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n  CSS_UNIT: new RegExp(CSS_UNIT),\n  rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n  rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n  hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n  hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n  hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n  hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n  hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n  hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n  hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n  hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nexport function stringInputToObject(color) {\n  color = color.trim().toLowerCase();\n  if (color.length === 0) {\n    return false;\n  }\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color === 'transparent') {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: 'name'\n    };\n  }\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  var match = matchers.rgb.exec(color);\n  if (match) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  match = matchers.rgba.exec(color);\n  if (match) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hsl.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  match = matchers.hsla.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hsv.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  match = matchers.hsva.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hex8.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? 'name' : 'hex8'\n    };\n  }\n  match = matchers.hex6.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? 'name' : 'hex'\n    };\n  }\n  match = matchers.hex4.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1] + match[1]),\n      g: parseIntFromHex(match[2] + match[2]),\n      b: parseIntFromHex(match[3] + match[3]),\n      a: convertHexToDecimal(match[4] + match[4]),\n      format: named ? 'name' : 'hex8'\n    };\n  }\n  match = matchers.hex3.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1] + match[1]),\n      g: parseIntFromHex(match[2] + match[2]),\n      b: parseIntFromHex(match[3] + match[3]),\n      format: named ? 'name' : 'hex'\n    };\n  }\n  return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nexport function isValidCSSUnit(color) {\n  return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}", "import { numberInputToObject, rgbaToHex, rgbToHex, rgbToHsl, rgbToHsv } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nvar TinyColor = /** @class */function () {\n  function TinyColor(color, opts) {\n    if (color === void 0) {\n      color = '';\n    }\n    if (opts === void 0) {\n      opts = {};\n    }\n    var _a;\n    // If input is already a tinycolor, return itself\n    if (color instanceof TinyColor) {\n      // eslint-disable-next-line no-constructor-return\n      return color;\n    }\n    if (typeof color === 'number') {\n      color = numberInputToObject(color);\n    }\n    this.originalInput = color;\n    var rgb = inputToRGB(color);\n    this.originalInput = color;\n    this.r = rgb.r;\n    this.g = rgb.g;\n    this.b = rgb.b;\n    this.a = rgb.a;\n    this.roundA = Math.round(100 * this.a) / 100;\n    this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;\n    this.gradientType = opts.gradientType;\n    // Don't let the range of [0,255] come back in [0,1].\n    // Potentially lose a little bit of precision here, but will fix issues where\n    // .5 gets interpreted as half of the total, instead of half of 1\n    // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n    if (this.r < 1) {\n      this.r = Math.round(this.r);\n    }\n    if (this.g < 1) {\n      this.g = Math.round(this.g);\n    }\n    if (this.b < 1) {\n      this.b = Math.round(this.b);\n    }\n    this.isValid = rgb.ok;\n  }\n  TinyColor.prototype.isDark = function () {\n    return this.getBrightness() < 128;\n  };\n  TinyColor.prototype.isLight = function () {\n    return !this.isDark();\n  };\n  /**\n   * Returns the perceived brightness of the color, from 0-255.\n   */\n  TinyColor.prototype.getBrightness = function () {\n    // http://www.w3.org/TR/AERT#color-contrast\n    var rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  };\n  /**\n   * Returns the perceived luminance of a color, from 0-1.\n   */\n  TinyColor.prototype.getLuminance = function () {\n    // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    var rgb = this.toRgb();\n    var R;\n    var G;\n    var B;\n    var RsRGB = rgb.r / 255;\n    var GsRGB = rgb.g / 255;\n    var BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) {\n      R = RsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    }\n    if (GsRGB <= 0.03928) {\n      G = GsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    }\n    if (BsRGB <= 0.03928) {\n      B = BsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    }\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  };\n  /**\n   * Returns the alpha value of a color, from 0-1.\n   */\n  TinyColor.prototype.getAlpha = function () {\n    return this.a;\n  };\n  /**\n   * Sets the alpha value on the current color.\n   *\n   * @param alpha - The new alpha value. The accepted range is 0-1.\n   */\n  TinyColor.prototype.setAlpha = function (alpha) {\n    this.a = boundAlpha(alpha);\n    this.roundA = Math.round(100 * this.a) / 100;\n    return this;\n  };\n  /**\n   * Returns whether the color is monochrome.\n   */\n  TinyColor.prototype.isMonochrome = function () {\n    var s = this.toHsl().s;\n    return s === 0;\n  };\n  /**\n   * Returns the object as a HSVA object.\n   */\n  TinyColor.prototype.toHsv = function () {\n    var hsv = rgbToHsv(this.r, this.g, this.b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this.a\n    };\n  };\n  /**\n   * Returns the hsva values interpolated into a string with the following format:\n   * \"hsva(xxx, xxx, xxx, xx)\".\n   */\n  TinyColor.prototype.toHsvString = function () {\n    var hsv = rgbToHsv(this.r, this.g, this.b);\n    var h = Math.round(hsv.h * 360);\n    var s = Math.round(hsv.s * 100);\n    var v = Math.round(hsv.v * 100);\n    return this.a === 1 ? \"hsv(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%)\") : \"hsva(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%, \").concat(this.roundA, \")\");\n  };\n  /**\n   * Returns the object as a HSLA object.\n   */\n  TinyColor.prototype.toHsl = function () {\n    var hsl = rgbToHsl(this.r, this.g, this.b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this.a\n    };\n  };\n  /**\n   * Returns the hsla values interpolated into a string with the following format:\n   * \"hsla(xxx, xxx, xxx, xx)\".\n   */\n  TinyColor.prototype.toHslString = function () {\n    var hsl = rgbToHsl(this.r, this.g, this.b);\n    var h = Math.round(hsl.h * 360);\n    var s = Math.round(hsl.s * 100);\n    var l = Math.round(hsl.l * 100);\n    return this.a === 1 ? \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\") : \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(this.roundA, \")\");\n  };\n  /**\n   * Returns the hex value of the color.\n   * @param allow3Char will shorten hex value to 3 char if possible\n   */\n  TinyColor.prototype.toHex = function (allow3Char) {\n    if (allow3Char === void 0) {\n      allow3Char = false;\n    }\n    return rgbToHex(this.r, this.g, this.b, allow3Char);\n  };\n  /**\n   * Returns the hex value of the color -with a # prefixed.\n   * @param allow3Char will shorten hex value to 3 char if possible\n   */\n  TinyColor.prototype.toHexString = function (allow3Char) {\n    if (allow3Char === void 0) {\n      allow3Char = false;\n    }\n    return '#' + this.toHex(allow3Char);\n  };\n  /**\n   * Returns the hex 8 value of the color.\n   * @param allow4Char will shorten hex value to 4 char if possible\n   */\n  TinyColor.prototype.toHex8 = function (allow4Char) {\n    if (allow4Char === void 0) {\n      allow4Char = false;\n    }\n    return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n  };\n  /**\n   * Returns the hex 8 value of the color -with a # prefixed.\n   * @param allow4Char will shorten hex value to 4 char if possible\n   */\n  TinyColor.prototype.toHex8String = function (allow4Char) {\n    if (allow4Char === void 0) {\n      allow4Char = false;\n    }\n    return '#' + this.toHex8(allow4Char);\n  };\n  /**\n   * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n   * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n   */\n  TinyColor.prototype.toHexShortString = function (allowShortChar) {\n    if (allowShortChar === void 0) {\n      allowShortChar = false;\n    }\n    return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n  };\n  /**\n   * Returns the object as a RGBA object.\n   */\n  TinyColor.prototype.toRgb = function () {\n    return {\n      r: Math.round(this.r),\n      g: Math.round(this.g),\n      b: Math.round(this.b),\n      a: this.a\n    };\n  };\n  /**\n   * Returns the RGBA values interpolated into a string with the following format:\n   * \"RGBA(xxx, xxx, xxx, xx)\".\n   */\n  TinyColor.prototype.toRgbString = function () {\n    var r = Math.round(this.r);\n    var g = Math.round(this.g);\n    var b = Math.round(this.b);\n    return this.a === 1 ? \"rgb(\".concat(r, \", \").concat(g, \", \").concat(b, \")\") : \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(this.roundA, \")\");\n  };\n  /**\n   * Returns the object as a RGBA object.\n   */\n  TinyColor.prototype.toPercentageRgb = function () {\n    var fmt = function (x) {\n      return \"\".concat(Math.round(bound01(x, 255) * 100), \"%\");\n    };\n    return {\n      r: fmt(this.r),\n      g: fmt(this.g),\n      b: fmt(this.b),\n      a: this.a\n    };\n  };\n  /**\n   * Returns the RGBA relative values interpolated into a string\n   */\n  TinyColor.prototype.toPercentageRgbString = function () {\n    var rnd = function (x) {\n      return Math.round(bound01(x, 255) * 100);\n    };\n    return this.a === 1 ? \"rgb(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%)\") : \"rgba(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%, \").concat(this.roundA, \")\");\n  };\n  /**\n   * The 'real' name of the color -if there is one.\n   */\n  TinyColor.prototype.toName = function () {\n    if (this.a === 0) {\n      return 'transparent';\n    }\n    if (this.a < 1) {\n      return false;\n    }\n    var hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n    for (var _i = 0, _a = Object.entries(names); _i < _a.length; _i++) {\n      var _b = _a[_i],\n        key = _b[0],\n        value = _b[1];\n      if (hex === value) {\n        return key;\n      }\n    }\n    return false;\n  };\n  TinyColor.prototype.toString = function (format) {\n    var formatSet = Boolean(format);\n    format = format !== null && format !== void 0 ? format : this.format;\n    var formattedString = false;\n    var hasAlpha = this.a < 1 && this.a >= 0;\n    var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === 'name' && this.a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === 'rgb') {\n      formattedString = this.toRgbString();\n    }\n    if (format === 'prgb') {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === 'hex' || format === 'hex6') {\n      formattedString = this.toHexString();\n    }\n    if (format === 'hex3') {\n      formattedString = this.toHexString(true);\n    }\n    if (format === 'hex4') {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === 'hex8') {\n      formattedString = this.toHex8String();\n    }\n    if (format === 'name') {\n      formattedString = this.toName();\n    }\n    if (format === 'hsl') {\n      formattedString = this.toHslString();\n    }\n    if (format === 'hsv') {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  };\n  TinyColor.prototype.toNumber = function () {\n    return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n  };\n  TinyColor.prototype.clone = function () {\n    return new TinyColor(this.toString());\n  };\n  /**\n   * Lighten the color a given amount. Providing 100 will always return white.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.lighten = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    var hsl = this.toHsl();\n    hsl.l += amount / 100;\n    hsl.l = clamp01(hsl.l);\n    return new TinyColor(hsl);\n  };\n  /**\n   * Brighten the color a given amount, from 0 to 100.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.brighten = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    var rgb = this.toRgb();\n    rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n    rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n    rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n    return new TinyColor(rgb);\n  };\n  /**\n   * Darken the color a given amount, from 0 to 100.\n   * Providing 100 will always return black.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.darken = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    var hsl = this.toHsl();\n    hsl.l -= amount / 100;\n    hsl.l = clamp01(hsl.l);\n    return new TinyColor(hsl);\n  };\n  /**\n   * Mix the color with pure white, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return white.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.tint = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    return this.mix('white', amount);\n  };\n  /**\n   * Mix the color with pure black, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return black.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.shade = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    return this.mix('black', amount);\n  };\n  /**\n   * Desaturate the color a given amount, from 0 to 100.\n   * Providing 100 will is the same as calling greyscale\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.desaturate = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    var hsl = this.toHsl();\n    hsl.s -= amount / 100;\n    hsl.s = clamp01(hsl.s);\n    return new TinyColor(hsl);\n  };\n  /**\n   * Saturate the color a given amount, from 0 to 100.\n   * @param amount - valid between 1-100\n   */\n  TinyColor.prototype.saturate = function (amount) {\n    if (amount === void 0) {\n      amount = 10;\n    }\n    var hsl = this.toHsl();\n    hsl.s += amount / 100;\n    hsl.s = clamp01(hsl.s);\n    return new TinyColor(hsl);\n  };\n  /**\n   * Completely desaturates a color into greyscale.\n   * Same as calling `desaturate(100)`\n   */\n  TinyColor.prototype.greyscale = function () {\n    return this.desaturate(100);\n  };\n  /**\n   * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n   * Values outside of this range will be wrapped into this range.\n   */\n  TinyColor.prototype.spin = function (amount) {\n    var hsl = this.toHsl();\n    var hue = (hsl.h + amount) % 360;\n    hsl.h = hue < 0 ? 360 + hue : hue;\n    return new TinyColor(hsl);\n  };\n  /**\n   * Mix the current color a given amount with another color, from 0 to 100.\n   * 0 means no mixing (return current color).\n   */\n  TinyColor.prototype.mix = function (color, amount) {\n    if (amount === void 0) {\n      amount = 50;\n    }\n    var rgb1 = this.toRgb();\n    var rgb2 = new TinyColor(color).toRgb();\n    var p = amount / 100;\n    var rgba = {\n      r: (rgb2.r - rgb1.r) * p + rgb1.r,\n      g: (rgb2.g - rgb1.g) * p + rgb1.g,\n      b: (rgb2.b - rgb1.b) * p + rgb1.b,\n      a: (rgb2.a - rgb1.a) * p + rgb1.a\n    };\n    return new TinyColor(rgba);\n  };\n  TinyColor.prototype.analogous = function (results, slices) {\n    if (results === void 0) {\n      results = 6;\n    }\n    if (slices === void 0) {\n      slices = 30;\n    }\n    var hsl = this.toHsl();\n    var part = 360 / slices;\n    var ret = [this];\n    for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n      hsl.h = (hsl.h + part) % 360;\n      ret.push(new TinyColor(hsl));\n    }\n    return ret;\n  };\n  /**\n   * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n   */\n  TinyColor.prototype.complement = function () {\n    var hsl = this.toHsl();\n    hsl.h = (hsl.h + 180) % 360;\n    return new TinyColor(hsl);\n  };\n  TinyColor.prototype.monochromatic = function (results) {\n    if (results === void 0) {\n      results = 6;\n    }\n    var hsv = this.toHsv();\n    var h = hsv.h;\n    var s = hsv.s;\n    var v = hsv.v;\n    var res = [];\n    var modification = 1 / results;\n    while (results--) {\n      res.push(new TinyColor({\n        h: h,\n        s: s,\n        v: v\n      }));\n      v = (v + modification) % 1;\n    }\n    return res;\n  };\n  TinyColor.prototype.splitcomplement = function () {\n    var hsl = this.toHsl();\n    var h = hsl.h;\n    return [this, new TinyColor({\n      h: (h + 72) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }), new TinyColor({\n      h: (h + 216) % 360,\n      s: hsl.s,\n      l: hsl.l\n    })];\n  };\n  /**\n   * Compute how the color would appear on a background\n   */\n  TinyColor.prototype.onBackground = function (background) {\n    var fg = this.toRgb();\n    var bg = new TinyColor(background).toRgb();\n    var alpha = fg.a + bg.a * (1 - fg.a);\n    return new TinyColor({\n      r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n      g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n      b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n      a: alpha\n    });\n  };\n  /**\n   * Alias for `polyad(3)`\n   */\n  TinyColor.prototype.triad = function () {\n    return this.polyad(3);\n  };\n  /**\n   * Alias for `polyad(4)`\n   */\n  TinyColor.prototype.tetrad = function () {\n    return this.polyad(4);\n  };\n  /**\n   * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n   * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n   */\n  TinyColor.prototype.polyad = function (n) {\n    var hsl = this.toHsl();\n    var h = hsl.h;\n    var result = [this];\n    var increment = 360 / n;\n    for (var i = 1; i < n; i++) {\n      result.push(new TinyColor({\n        h: (h + i * increment) % 360,\n        s: hsl.s,\n        l: hsl.l\n      }));\n    }\n    return result;\n  };\n  /**\n   * compare color vs current color\n   */\n  TinyColor.prototype.equals = function (color) {\n    return this.toRgbString() === new TinyColor(color).toRgbString();\n  };\n  return TinyColor;\n}();\nexport { TinyColor };\n// kept for backwards compatability with v1\nexport function tinycolor(color, opts) {\n  if (color === void 0) {\n    color = '';\n  }\n  if (opts === void 0) {\n    opts = {};\n  }\n  return new TinyColor(color, opts);\n}", "import { rgbToHsv, rgbToHex, inputToRGB } from '@ctrl/tinycolor';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst statusColors = ['success', 'processing', 'error', 'default', 'warning'];\nconst presetColors = ['pink', 'red', 'yellow', 'orange', 'cyan', 'green', 'blue', 'purple', 'geekblue', 'magenta', 'volcano', 'gold', 'lime'];\nfunction isPresetColor(color) {\n  return presetColors.indexOf(color) !== -1;\n}\nfunction isStatusColor(color) {\n  return statusColors.indexOf(color) !== -1;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from @ant-design/colors(https://github.com/ant-design/ant-design-colors)\n */\nconst hueStep = 2; // 色相阶梯\nconst saturationStep = 0.16; // 饱和度阶梯，浅色部分\nconst saturationStep2 = 0.05; // 饱和度阶梯，深色部分\nconst brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\nconst brightnessStep2 = 0.15; // 亮度阶梯，深色部分\nconst lightColorCount = 5; // 浅色数量，主色上\nconst darkColorCount = 4; // 深色数量，主色下\n// 暗色主题颜色映射关系表\nconst darkColorMap = [{\n  index: 7,\n  opacity: 0.15\n}, {\n  index: 6,\n  opacity: 0.25\n}, {\n  index: 5,\n  opacity: 0.3\n}, {\n  index: 5,\n  opacity: 0.45\n}, {\n  index: 5,\n  opacity: 0.65\n}, {\n  index: 5,\n  opacity: 0.85\n}, {\n  index: 4,\n  opacity: 0.9\n}, {\n  index: 3,\n  opacity: 0.95\n}, {\n  index: 2,\n  opacity: 0.97\n}, {\n  index: 1,\n  opacity: 0.98\n}];\n// Wrapper function ported from TinyColor.prototype.toHsv\n// Keep it here because of `hsv.h * 360`\nfunction toHsv({\n  r,\n  g,\n  b\n}) {\n  const hsv = rgbToHsv(r, g, b);\n  return {\n    h: hsv.h * 360,\n    s: hsv.s,\n    v: hsv.v\n  };\n}\n// Wrapper function ported from TinyColor.prototype.toHexString\n// Keep it here because of the prefix `#`\nfunction toHex({\n  r,\n  g,\n  b\n}) {\n  return `#${rgbToHex(r, g, b, false)}`;\n}\n// Wrapper function ported from TinyColor.prototype.mix, not treeshakable.\n// Amount in range [0, 1]\n// Assume color1 & color2 has no alpha, since the following src code did so.\nfunction mix(rgb1, rgb2, amount) {\n  const p = amount / 100;\n  const rgb = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b\n  };\n  return rgb;\n}\nfunction getHue(hsv, i, light) {\n  let hue;\n  // 根据色相不同，色相转向不同\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n  return hue;\n}\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n  let saturation;\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  }\n  // 边界值修正\n  if (saturation > 1) {\n    saturation = 1;\n  }\n  // 第一格的 s 限制在 0.06-0.1 之间\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n  return Number(saturation.toFixed(2));\n}\nfunction getValue(hsv, i, light) {\n  let value;\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n  if (value > 1) {\n    value = 1;\n  }\n  return Number(value.toFixed(2));\n}\nfunction generate(color, opts = {}) {\n  const patterns = [];\n  const pColor = inputToRGB(color);\n  for (let i = lightColorCount; i > 0; i -= 1) {\n    const hsv = toHsv(pColor);\n    const colorString = toHex(inputToRGB({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    }));\n    patterns.push(colorString);\n  }\n  patterns.push(toHex(pColor));\n  for (let i = 1; i <= darkColorCount; i += 1) {\n    const hsv = toHsv(pColor);\n    const colorString = toHex(inputToRGB({\n      h: getHue(hsv, i),\n      s: getSaturation(hsv, i),\n      v: getValue(hsv, i)\n    }));\n    patterns.push(colorString);\n  }\n  // dark theme patterns\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(({\n      index,\n      opacity\n    }) => {\n      const darkColorString = toHex(mix(inputToRGB(opts.backgroundColor || '#141414'), inputToRGB(patterns[index]), opacity * 100));\n      return darkColorString;\n    });\n  }\n  return patterns;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { generate, isPresetColor, isStatusColor, presetColors, statusColors };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, makeEnvironmentProviders, inject, CSP_NONCE, Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { filter, map } from 'rxjs/operators';\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { generate } from 'ng-zorro-antd/core/color';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { canUseDom, updateCSS } from 'ng-zorro-antd/core/util';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * User should provide an object implements this interface to set global configurations.\n */\nconst NZ_CONFIG = new InjectionToken('nz-config');\nfunction provideNzConfig(config) {\n  return makeEnvironmentProviders([{\n    provide: NZ_CONFIG,\n    useValue: config\n  }]);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from @ant-design/colors(https://github.com/ant-design/ant-design-colors)\n */\nconst dynamicStyleMark = `-ant-${Date.now()}-${Math.random()}`;\nfunction getStyle(globalPrefixCls, theme) {\n  const variables = {};\n  const formatColor = (color, updater) => {\n    let clone = color.clone();\n    clone = updater?.(clone) || clone;\n    return clone.toRgbString();\n  };\n  const fillColor = (colorVal, type) => {\n    const baseColor = new TinyColor(colorVal);\n    const colorPalettes = generate(baseColor.toRgbString());\n    variables[`${type}-color`] = formatColor(baseColor);\n    variables[`${type}-color-disabled`] = colorPalettes[1];\n    variables[`${type}-color-hover`] = colorPalettes[4];\n    variables[`${type}-color-active`] = colorPalettes[7];\n    variables[`${type}-color-outline`] = baseColor.clone().setAlpha(0.2).toRgbString();\n    variables[`${type}-color-deprecated-bg`] = colorPalettes[1];\n    variables[`${type}-color-deprecated-border`] = colorPalettes[3];\n  };\n  // ================ Primary Color ================\n  if (theme.primaryColor) {\n    fillColor(theme.primaryColor, 'primary');\n    const primaryColor = new TinyColor(theme.primaryColor);\n    const primaryColors = generate(primaryColor.toRgbString());\n    // Legacy - We should use semantic naming standard\n    primaryColors.forEach((color, index) => {\n      variables[`primary-${index + 1}`] = color;\n    });\n    // Deprecated\n    variables['primary-color-deprecated-l-35'] = formatColor(primaryColor, c => c.lighten(35));\n    variables['primary-color-deprecated-l-20'] = formatColor(primaryColor, c => c.lighten(20));\n    variables['primary-color-deprecated-t-20'] = formatColor(primaryColor, c => c.tint(20));\n    variables['primary-color-deprecated-t-50'] = formatColor(primaryColor, c => c.tint(50));\n    variables['primary-color-deprecated-f-12'] = formatColor(primaryColor, c => c.setAlpha(c.getAlpha() * 0.12));\n    const primaryActiveColor = new TinyColor(primaryColors[0]);\n    variables['primary-color-active-deprecated-f-30'] = formatColor(primaryActiveColor, c => c.setAlpha(c.getAlpha() * 0.3));\n    variables['primary-color-active-deprecated-d-02'] = formatColor(primaryActiveColor, c => c.darken(2));\n  }\n  // ================ Success Color ================\n  if (theme.successColor) {\n    fillColor(theme.successColor, 'success');\n  }\n  // ================ Warning Color ================\n  if (theme.warningColor) {\n    fillColor(theme.warningColor, 'warning');\n  }\n  // ================= Error Color =================\n  if (theme.errorColor) {\n    fillColor(theme.errorColor, 'error');\n  }\n  // ================= Info Color ==================\n  if (theme.infoColor) {\n    fillColor(theme.infoColor, 'info');\n  }\n  // Convert to css variables\n  const cssList = Object.keys(variables).map(key => `--${globalPrefixCls}-${key}: ${variables[key]};`);\n  return `\n  :root {\n    ${cssList.join('\\n')}\n  }\n  `.trim();\n}\nfunction registerTheme(globalPrefixCls, theme, cspNonce) {\n  const style = getStyle(globalPrefixCls, theme);\n  if (canUseDom()) {\n    updateCSS(style, `${dynamicStyleMark}-dynamic-theme`, {\n      cspNonce\n    });\n  } else {\n    warn(`NzConfigService: SSR do not support dynamic theme with css variables.`);\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst isDefined = function (value) {\n  return value !== undefined;\n};\nconst defaultPrefixCls = 'ant';\nclass NzConfigService {\n  configUpdated$ = new Subject();\n  /** Global config holding property. */\n  config = inject(NZ_CONFIG, {\n    optional: true\n  }) || {};\n  cspNonce = inject(CSP_NONCE, {\n    optional: true\n  });\n  constructor() {\n    if (this.config.theme) {\n      // If theme is set with NZ_CONFIG, register theme to make sure css variables work\n      registerTheme(this.getConfig().prefixCls?.prefixCls || defaultPrefixCls, this.config.theme, this.cspNonce);\n    }\n  }\n  getConfig() {\n    return this.config;\n  }\n  getConfigForComponent(componentName) {\n    return this.config[componentName];\n  }\n  getConfigChangeEventForComponent(componentName) {\n    return this.configUpdated$.pipe(filter(n => n === componentName), map(() => undefined));\n  }\n  set(componentName, value) {\n    this.config[componentName] = {\n      ...this.config[componentName],\n      ...value\n    };\n    if (componentName === 'theme' && this.config.theme) {\n      registerTheme(this.getConfig().prefixCls?.prefixCls || defaultPrefixCls, this.config.theme, this.cspNonce);\n    }\n    this.configUpdated$.next(componentName);\n  }\n  static ɵfac = function NzConfigService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzConfigService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NzConfigService,\n    factory: NzConfigService.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzConfigService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * This decorator is used to decorate class field. If a class field is decorated and unassigned, it would try to load default value from `NZ_CONFIG`\n *\n * @note that the class must have `_nzModuleName`({@link NzConfigKey}) property.\n * @example\n * ```ts\n * class ExampleComponent {\n *   private readonly _nzModuleName: NzConfigKey = 'button';\n *   @WithConfig() size: string = 'default';\n * }\n * ```\n */\nfunction WithConfig() {\n  return function (_value, context) {\n    context.addInitializer(function () {\n      const nzConfigService = inject(NzConfigService);\n      const originalValue = this[context.name];\n      let value;\n      let assignedByUser = false;\n      Object.defineProperty(this, context.name, {\n        get: () => {\n          const configValue = nzConfigService.getConfigForComponent(this['_nzModuleName'])?.[context.name];\n          if (assignedByUser) {\n            return value;\n          }\n          if (isDefined(configValue)) {\n            return configValue;\n          }\n          return originalValue;\n        },\n        set: newValue => {\n          // if the newValue is undefined, we also consider it as not assigned by user\n          assignedByUser = isDefined(newValue);\n          value = newValue;\n        },\n        enumerable: true,\n        configurable: true\n      });\n    });\n  };\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_CONFIG, NzConfigService, WithConfig, getStyle, provideNzConfig, registerTheme };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAIO,SAAS,QAAQ,GAAG,KAAK;AAC9B,MAAI,eAAe,CAAC,GAAG;AACrB,QAAI;AAAA,EACN;AACA,MAAI,YAAY,aAAa,CAAC;AAC9B,MAAI,QAAQ,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC;AAE9D,MAAI,WAAW;AACb,QAAI,SAAS,OAAO,IAAI,GAAG,GAAG,EAAE,IAAI;AAAA,EACtC;AAEA,MAAI,KAAK,IAAI,IAAI,GAAG,IAAI,MAAU;AAChC,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,KAAK;AAIf,SAAK,IAAI,IAAI,IAAI,MAAM,MAAM,IAAI,OAAO,WAAW,OAAO,GAAG,CAAC;AAAA,EAChE,OAAO;AAGL,QAAI,IAAI,MAAM,WAAW,OAAO,GAAG,CAAC;AAAA,EACtC;AACA,SAAO;AACT;AAKO,SAAS,QAAQ,KAAK;AAC3B,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC;AACrC;AAMO,SAAS,eAAe,GAAG;AAChC,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM,MAAM,WAAW,CAAC,MAAM;AAC7E;AAKO,SAAS,aAAa,GAAG;AAC9B,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM;AACrD;AAKO,SAAS,WAAW,GAAG;AAC5B,MAAI,WAAW,CAAC;AAChB,MAAI,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG;AAC9B,QAAI;AAAA,EACN;AACA,SAAO;AACT;AAKO,SAAS,oBAAoB,GAAG;AACrC,MAAI,KAAK,GAAG;AACV,WAAO,GAAG,OAAO,OAAO,CAAC,IAAI,KAAK,GAAG;AAAA,EACvC;AACA,SAAO;AACT;AAKO,SAAS,KAAK,GAAG;AACtB,SAAO,EAAE,WAAW,IAAI,MAAM,IAAI,OAAO,CAAC;AAC5C;;;ACvEO,SAAS,SAAS,GAAG,GAAG,GAAG;AAChC,SAAO;AAAA,IACL,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,EACvB;AACF;AAMO,SAAS,SAAS,GAAG,GAAG,GAAG;AAChC,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK,MAAM,OAAO;AACtB,MAAI,QAAQ,KAAK;AACf,QAAI;AACJ,QAAI;AAAA,EACN,OAAO;AACL,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,YAAQ,KAAK;AAAA,MACX,KAAK;AACH,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACF,KAAK;AACH,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACF,KAAK;AACH,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACF;AACE;AAAA,IACJ;AACA,SAAK;AAAA,EACP;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACxB,MAAI,IAAI,GAAG;AACT,SAAK;AAAA,EACP;AACA,MAAI,IAAI,GAAG;AACT,SAAK;AAAA,EACP;AACA,MAAI,IAAI,IAAI,GAAG;AACb,WAAO,KAAK,IAAI,MAAM,IAAI;AAAA,EAC5B;AACA,MAAI,IAAI,IAAI,GAAG;AACb,WAAO;AAAA,EACT;AACA,MAAI,IAAI,IAAI,GAAG;AACb,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAAA,EACrC;AACA,SAAO;AACT;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG;AAChC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,GAAG;AAEX,QAAI;AACJ,QAAI;AACJ,QAAI;AAAA,EACN,OAAO;AACL,QAAI,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC5C,QAAI,IAAI,IAAI,IAAI;AAChB,QAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAC3B,QAAI,QAAQ,GAAG,GAAG,CAAC;AACnB,QAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAAA,EAC7B;AACA,SAAO;AAAA,IACL,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG;AAChC,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI,MAAM;AACd,MAAI,IAAI,QAAQ,IAAI,IAAI,IAAI;AAC5B,MAAI,QAAQ,KAAK;AACf,QAAI;AAAA,EACN,OAAO;AACL,YAAQ,KAAK;AAAA,MACX,KAAK;AACH,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACF,KAAK;AACH,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACF,KAAK;AACH,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACF;AACE;AAAA,IACJ;AACA,SAAK;AAAA,EACP;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG;AAChC,MAAI,QAAQ,GAAG,GAAG,IAAI;AACtB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,IAAI,KAAK,MAAM,CAAC;AACpB,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,KAAK,IAAI;AACjB,MAAI,IAAI,KAAK,IAAI,IAAI;AACrB,MAAI,IAAI,KAAK,KAAK,IAAI,KAAK;AAC3B,MAAI,MAAM,IAAI;AACd,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAC9B,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAC9B,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAC9B,SAAO;AAAA,IACL,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG,YAAY;AAC5C,MAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;AAE/G,MAAI,cAAc,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AACnI,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EAC9D;AACA,SAAO,IAAI,KAAK,EAAE;AACpB;AAQO,SAAS,UAAU,GAAG,GAAG,GAAG,GAAG,YAAY;AAChD,MAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,oBAAoB,CAAC,CAAC,CAAC;AAE7I,MAAI,cAAc,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AAC1K,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EACjF;AACA,SAAO,IAAI,KAAK,EAAE;AACpB;AAUO,SAAS,oBAAoB,GAAG;AACrC,SAAO,KAAK,MAAM,WAAW,CAAC,IAAI,GAAG,EAAE,SAAS,EAAE;AACpD;AAEO,SAAS,oBAAoB,GAAG;AACrC,SAAO,gBAAgB,CAAC,IAAI;AAC9B;AAEO,SAAS,gBAAgB,KAAK;AACnC,SAAO,SAAS,KAAK,EAAE;AACzB;AACO,SAAS,oBAAoB,OAAO;AACzC,SAAO;AAAA,IACL,GAAG,SAAS;AAAA,IACZ,IAAI,QAAQ,UAAW;AAAA,IACvB,GAAG,QAAQ;AAAA,EACb;AACF;;;AC9NO,IAAI,QAAQ;AAAA,EACjB,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACf;;;ACnIO,SAAS,WAAW,OAAO;AAChC,MAAI,MAAM;AAAA,IACR,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,SAAS;AACb,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAQ,oBAAoB,KAAK;AAAA,EACnC;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACjF,YAAM,SAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC,WAAK;AACL,eAAS,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,MAAM,SAAS;AAAA,IACzD,WAAW,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACxF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACX,WAAW,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACxF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACX;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,GAAG;AACpD,UAAI,MAAM;AAAA,IACZ;AAAA,EACF;AACA,MAAI,WAAW,CAAC;AAChB,SAAO;AAAA,IACL;AAAA,IACA,QAAQ,MAAM,UAAU;AAAA,IACxB,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC;AAAA,EACF;AACF;AAEA,IAAI,cAAc;AAElB,IAAI,aAAa;AAEjB,IAAI,WAAW,MAAM,OAAO,YAAY,OAAO,EAAE,OAAO,aAAa,GAAG;AAIxE,IAAI,oBAAoB,cAAc,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,WAAW;AAChI,IAAI,oBAAoB,cAAc,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,WAAW;AAC/J,IAAI,WAAW;AAAA,EACb,UAAU,IAAI,OAAO,QAAQ;AAAA,EAC7B,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AAKO,SAAS,oBAAoB,OAAO;AACzC,UAAQ,MAAM,KAAK,EAAE,YAAY;AACjC,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,MAAI,QAAQ;AACZ,MAAI,MAAM,KAAK,GAAG;AAChB,YAAQ,MAAM,KAAK;AACnB,YAAQ;AAAA,EACV,WAAW,UAAU,eAAe;AAClC,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,QAAQ;AAAA,IACV;AAAA,EACF;AAKA,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK;AACnC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,oBAAoB,MAAM,CAAC,CAAC;AAAA,MAC/B,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,oBAAoB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MAC1C,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,SAAO;AACT;AAKO,SAAS,eAAe,OAAO;AACpC,SAAO,QAAQ,SAAS,SAAS,KAAK,OAAO,KAAK,CAAC,CAAC;AACtD;;;ACpNA,IAAI;AAAA;AAAA,EAAyB,WAAY;AACvC,aAASA,WAAU,OAAO,MAAM;AAC9B,UAAI,UAAU,QAAQ;AACpB,gBAAQ;AAAA,MACV;AACA,UAAI,SAAS,QAAQ;AACnB,eAAO,CAAC;AAAA,MACV;AACA,UAAI;AAEJ,UAAI,iBAAiBA,YAAW;AAE9B,eAAO;AAAA,MACT;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,oBAAoB,KAAK;AAAA,MACnC;AACA,WAAK,gBAAgB;AACrB,UAAI,MAAM,WAAW,KAAK;AAC1B,WAAK,gBAAgB;AACrB,WAAK,IAAI,IAAI;AACb,WAAK,IAAI,IAAI;AACb,WAAK,IAAI,IAAI;AACb,WAAK,IAAI,IAAI;AACb,WAAK,SAAS,KAAK,MAAM,MAAM,KAAK,CAAC,IAAI;AACzC,WAAK,UAAU,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK,IAAI;AACtE,WAAK,eAAe,KAAK;AAKzB,UAAI,KAAK,IAAI,GAAG;AACd,aAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,MAC5B;AACA,UAAI,KAAK,IAAI,GAAG;AACd,aAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,MAC5B;AACA,UAAI,KAAK,IAAI,GAAG;AACd,aAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,MAC5B;AACA,WAAK,UAAU,IAAI;AAAA,IACrB;AACA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACvC,aAAO,KAAK,cAAc,IAAI;AAAA,IAChC;AACA,IAAAA,WAAU,UAAU,UAAU,WAAY;AACxC,aAAO,CAAC,KAAK,OAAO;AAAA,IACtB;AAIA,IAAAA,WAAU,UAAU,gBAAgB,WAAY;AAE9C,UAAI,MAAM,KAAK,MAAM;AACrB,cAAQ,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OAAO;AAAA,IACrD;AAIA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAE7C,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,QAAQ,IAAI,IAAI;AACpB,UAAI,QAAQ,IAAI,IAAI;AACpB,UAAI,QAAQ,IAAI,IAAI;AACpB,UAAI,SAAS,SAAS;AACpB,YAAI,QAAQ;AAAA,MACd,OAAO;AAEL,YAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,MAC3C;AACA,UAAI,SAAS,SAAS;AACpB,YAAI,QAAQ;AAAA,MACd,OAAO;AAEL,YAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,MAC3C;AACA,UAAI,SAAS,SAAS;AACpB,YAAI,QAAQ;AAAA,MACd,OAAO;AAEL,YAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,MAC3C;AACA,aAAO,SAAS,IAAI,SAAS,IAAI,SAAS;AAAA,IAC5C;AAIA,IAAAA,WAAU,UAAU,WAAW,WAAY;AACzC,aAAO,KAAK;AAAA,IACd;AAMA,IAAAA,WAAU,UAAU,WAAW,SAAU,OAAO;AAC9C,WAAK,IAAI,WAAW,KAAK;AACzB,WAAK,SAAS,KAAK,MAAM,MAAM,KAAK,CAAC,IAAI;AACzC,aAAO;AAAA,IACT;AAIA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAC7C,UAAI,IAAI,KAAK,MAAM,EAAE;AACrB,aAAO,MAAM;AAAA,IACf;AAIA,IAAAA,WAAU,UAAU,QAAQ,WAAY;AACtC,UAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,aAAO;AAAA,QACL,GAAG,IAAI,IAAI;AAAA,QACX,GAAG,IAAI;AAAA,QACP,GAAG,IAAI;AAAA,QACP,GAAG,KAAK;AAAA,MACV;AAAA,IACF;AAKA,IAAAA,WAAU,UAAU,cAAc,WAAY;AAC5C,UAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,aAAO,KAAK,MAAM,IAAI,OAAO,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,IAAI,IAAI,QAAQ,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,IACnK;AAIA,IAAAA,WAAU,UAAU,QAAQ,WAAY;AACtC,UAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,aAAO;AAAA,QACL,GAAG,IAAI,IAAI;AAAA,QACX,GAAG,IAAI;AAAA,QACP,GAAG,IAAI;AAAA,QACP,GAAG,KAAK;AAAA,MACV;AAAA,IACF;AAKA,IAAAA,WAAU,UAAU,cAAc,WAAY;AAC5C,UAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,aAAO,KAAK,MAAM,IAAI,OAAO,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,IAAI,IAAI,QAAQ,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,IACnK;AAKA,IAAAA,WAAU,UAAU,QAAQ,SAAU,YAAY;AAChD,UAAI,eAAe,QAAQ;AACzB,qBAAa;AAAA,MACf;AACA,aAAO,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU;AAAA,IACpD;AAKA,IAAAA,WAAU,UAAU,cAAc,SAAU,YAAY;AACtD,UAAI,eAAe,QAAQ;AACzB,qBAAa;AAAA,MACf;AACA,aAAO,MAAM,KAAK,MAAM,UAAU;AAAA,IACpC;AAKA,IAAAA,WAAU,UAAU,SAAS,SAAU,YAAY;AACjD,UAAI,eAAe,QAAQ;AACzB,qBAAa;AAAA,MACf;AACA,aAAO,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU;AAAA,IAC7D;AAKA,IAAAA,WAAU,UAAU,eAAe,SAAU,YAAY;AACvD,UAAI,eAAe,QAAQ;AACzB,qBAAa;AAAA,MACf;AACA,aAAO,MAAM,KAAK,OAAO,UAAU;AAAA,IACrC;AAKA,IAAAA,WAAU,UAAU,mBAAmB,SAAU,gBAAgB;AAC/D,UAAI,mBAAmB,QAAQ;AAC7B,yBAAiB;AAAA,MACnB;AACA,aAAO,KAAK,MAAM,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,aAAa,cAAc;AAAA,IAC3F;AAIA,IAAAA,WAAU,UAAU,QAAQ,WAAY;AACtC,aAAO;AAAA,QACL,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,QACpB,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,QACpB,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,QACpB,GAAG,KAAK;AAAA,MACV;AAAA,IACF;AAKA,IAAAA,WAAU,UAAU,cAAc,WAAY;AAC5C,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,aAAO,KAAK,MAAM,IAAI,OAAO,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,GAAG,IAAI,QAAQ,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,IAC/J;AAIA,IAAAA,WAAU,UAAU,kBAAkB,WAAY;AAChD,UAAI,MAAM,SAAU,GAAG;AACrB,eAAO,GAAG,OAAO,KAAK,MAAM,QAAQ,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG;AAAA,MACzD;AACA,aAAO;AAAA,QACL,GAAG,IAAI,KAAK,CAAC;AAAA,QACb,GAAG,IAAI,KAAK,CAAC;AAAA,QACb,GAAG,IAAI,KAAK,CAAC;AAAA,QACb,GAAG,KAAK;AAAA,MACV;AAAA,IACF;AAIA,IAAAA,WAAU,UAAU,wBAAwB,WAAY;AACtD,UAAI,MAAM,SAAU,GAAG;AACrB,eAAO,KAAK,MAAM,QAAQ,GAAG,GAAG,IAAI,GAAG;AAAA,MACzC;AACA,aAAO,KAAK,MAAM,IAAI,OAAO,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,IACjO;AAIA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACvC,UAAI,KAAK,MAAM,GAAG;AAChB,eAAO;AAAA,MACT;AACA,UAAI,KAAK,IAAI,GAAG;AACd,eAAO;AAAA,MACT;AACA,UAAI,MAAM,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AACtD,eAAS,KAAK,GAAG,KAAK,OAAO,QAAQ,KAAK,GAAG,KAAK,GAAG,QAAQ,MAAM;AACjE,YAAI,KAAK,GAAG,EAAE,GACZ,MAAM,GAAG,CAAC,GACV,QAAQ,GAAG,CAAC;AACd,YAAI,QAAQ,OAAO;AACjB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC/C,UAAI,YAAY,QAAQ,MAAM;AAC9B,eAAS,WAAW,QAAQ,WAAW,SAAS,SAAS,KAAK;AAC9D,UAAI,kBAAkB;AACtB,UAAI,WAAW,KAAK,IAAI,KAAK,KAAK,KAAK;AACvC,UAAI,mBAAmB,CAAC,aAAa,aAAa,OAAO,WAAW,KAAK,KAAK,WAAW;AACzF,UAAI,kBAAkB;AAGpB,YAAI,WAAW,UAAU,KAAK,MAAM,GAAG;AACrC,iBAAO,KAAK,OAAO;AAAA,QACrB;AACA,eAAO,KAAK,YAAY;AAAA,MAC1B;AACA,UAAI,WAAW,OAAO;AACpB,0BAAkB,KAAK,YAAY;AAAA,MACrC;AACA,UAAI,WAAW,QAAQ;AACrB,0BAAkB,KAAK,sBAAsB;AAAA,MAC/C;AACA,UAAI,WAAW,SAAS,WAAW,QAAQ;AACzC,0BAAkB,KAAK,YAAY;AAAA,MACrC;AACA,UAAI,WAAW,QAAQ;AACrB,0BAAkB,KAAK,YAAY,IAAI;AAAA,MACzC;AACA,UAAI,WAAW,QAAQ;AACrB,0BAAkB,KAAK,aAAa,IAAI;AAAA,MAC1C;AACA,UAAI,WAAW,QAAQ;AACrB,0BAAkB,KAAK,aAAa;AAAA,MACtC;AACA,UAAI,WAAW,QAAQ;AACrB,0BAAkB,KAAK,OAAO;AAAA,MAChC;AACA,UAAI,WAAW,OAAO;AACpB,0BAAkB,KAAK,YAAY;AAAA,MACrC;AACA,UAAI,WAAW,OAAO;AACpB,0BAAkB,KAAK,YAAY;AAAA,MACrC;AACA,aAAO,mBAAmB,KAAK,YAAY;AAAA,IAC7C;AACA,IAAAA,WAAU,UAAU,WAAW,WAAY;AACzC,cAAQ,KAAK,MAAM,KAAK,CAAC,KAAK,OAAO,KAAK,MAAM,KAAK,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,IACnF;AACA,IAAAA,WAAU,UAAU,QAAQ,WAAY;AACtC,aAAO,IAAIA,WAAU,KAAK,SAAS,CAAC;AAAA,IACtC;AAKA,IAAAA,WAAU,UAAU,UAAU,SAAU,QAAQ;AAC9C,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,SAAS;AAClB,UAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC1B;AAKA,IAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC/C,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC1B;AAMA,IAAAA,WAAU,UAAU,SAAS,SAAU,QAAQ;AAC7C,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,SAAS;AAClB,UAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC1B;AAMA,IAAAA,WAAU,UAAU,OAAO,SAAU,QAAQ;AAC3C,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,aAAO,KAAK,IAAI,SAAS,MAAM;AAAA,IACjC;AAMA,IAAAA,WAAU,UAAU,QAAQ,SAAU,QAAQ;AAC5C,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,aAAO,KAAK,IAAI,SAAS,MAAM;AAAA,IACjC;AAMA,IAAAA,WAAU,UAAU,aAAa,SAAU,QAAQ;AACjD,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,SAAS;AAClB,UAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC1B;AAKA,IAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC/C,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,SAAS;AAClB,UAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC1B;AAKA,IAAAA,WAAU,UAAU,YAAY,WAAY;AAC1C,aAAO,KAAK,WAAW,GAAG;AAAA,IAC5B;AAKA,IAAAA,WAAU,UAAU,OAAO,SAAU,QAAQ;AAC3C,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,OAAO,IAAI,IAAI,UAAU;AAC7B,UAAI,IAAI,MAAM,IAAI,MAAM,MAAM;AAC9B,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC1B;AAKA,IAAAA,WAAU,UAAU,MAAM,SAAU,OAAO,QAAQ;AACjD,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,UAAI,OAAO,KAAK,MAAM;AACtB,UAAI,OAAO,IAAIA,WAAU,KAAK,EAAE,MAAM;AACtC,UAAI,IAAI,SAAS;AACjB,UAAI,OAAO;AAAA,QACT,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,QAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,QAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,QAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,MAClC;AACA,aAAO,IAAIA,WAAU,IAAI;AAAA,IAC3B;AACA,IAAAA,WAAU,UAAU,YAAY,SAAU,SAAS,QAAQ;AACzD,UAAI,YAAY,QAAQ;AACtB,kBAAU;AAAA,MACZ;AACA,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,OAAO,MAAM;AACjB,UAAI,MAAM,CAAC,IAAI;AACf,WAAK,IAAI,KAAK,IAAI,KAAK,OAAO,WAAW,KAAK,OAAO,KAAK,EAAE,WAAU;AACpE,YAAI,KAAK,IAAI,IAAI,QAAQ;AACzB,YAAI,KAAK,IAAIA,WAAU,GAAG,CAAC;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,WAAU,UAAU,aAAa,WAAY;AAC3C,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,IAAI,IAAI,OAAO;AACxB,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC1B;AACA,IAAAA,WAAU,UAAU,gBAAgB,SAAU,SAAS;AACrD,UAAI,YAAY,QAAQ;AACtB,kBAAU;AAAA,MACZ;AACA,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI,IAAI;AACZ,UAAI,MAAM,CAAC;AACX,UAAI,eAAe,IAAI;AACvB,aAAO,WAAW;AAChB,YAAI,KAAK,IAAIA,WAAU;AAAA,UACrB;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AACF,aAAK,IAAI,gBAAgB;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AACA,IAAAA,WAAU,UAAU,kBAAkB,WAAY;AAChD,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,IAAI,IAAI;AACZ,aAAO,CAAC,MAAM,IAAIA,WAAU;AAAA,QAC1B,IAAI,IAAI,MAAM;AAAA,QACd,GAAG,IAAI;AAAA,QACP,GAAG,IAAI;AAAA,MACT,CAAC,GAAG,IAAIA,WAAU;AAAA,QAChB,IAAI,IAAI,OAAO;AAAA,QACf,GAAG,IAAI;AAAA,QACP,GAAG,IAAI;AAAA,MACT,CAAC,CAAC;AAAA,IACJ;AAIA,IAAAA,WAAU,UAAU,eAAe,SAAU,YAAY;AACvD,UAAI,KAAK,KAAK,MAAM;AACpB,UAAI,KAAK,IAAIA,WAAU,UAAU,EAAE,MAAM;AACzC,UAAI,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG;AAClC,aAAO,IAAIA,WAAU;AAAA,QACnB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,QAC9C,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,QAC9C,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,QAC9C,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AAIA,IAAAA,WAAU,UAAU,QAAQ,WAAY;AACtC,aAAO,KAAK,OAAO,CAAC;AAAA,IACtB;AAIA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACvC,aAAO,KAAK,OAAO,CAAC;AAAA,IACtB;AAKA,IAAAA,WAAU,UAAU,SAAS,SAAU,GAAG;AACxC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,IAAI,IAAI;AACZ,UAAI,SAAS,CAAC,IAAI;AAClB,UAAI,YAAY,MAAM;AACtB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAO,KAAK,IAAIA,WAAU;AAAA,UACxB,IAAI,IAAI,IAAI,aAAa;AAAA,UACzB,GAAG,IAAI;AAAA,UACP,GAAG,IAAI;AAAA,QACT,CAAC,CAAC;AAAA,MACJ;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,WAAU,UAAU,SAAS,SAAU,OAAO;AAC5C,aAAO,KAAK,YAAY,MAAM,IAAIA,WAAU,KAAK,EAAE,YAAY;AAAA,IACjE;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;;;ACzhBF,IAAM,UAAU;AAChB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,iBAAiB;AAEvB,IAAM,eAAe,CAAC;AAAA,EACpB,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,CAAC;AAGD,SAAS,MAAM;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,MAAM,SAAS,GAAG,GAAG,CAAC;AAC5B,SAAO;AAAA,IACL,GAAG,IAAI,IAAI;AAAA,IACX,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AAGA,SAAS,MAAM;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,IAAI,SAAS,GAAG,GAAG,GAAG,KAAK,CAAC;AACrC;AAIA,SAAS,IAAI,MAAM,MAAM,QAAQ;AAC/B,QAAM,IAAI,SAAS;AACnB,QAAM,MAAM;AAAA,IACV,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,EAClC;AACA,SAAO;AACT;AACA,SAAS,OAAO,KAAK,GAAG,OAAO;AAC7B,MAAI;AAEJ,MAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK;AACvD,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;AAAA,EAChF,OAAO;AACL,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;AAAA,EAChF;AACA,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT,WAAW,OAAO,KAAK;AACrB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,cAAc,KAAK,GAAG,OAAO;AAEpC,MAAI,IAAI,MAAM,KAAK,IAAI,MAAM,GAAG;AAC9B,WAAO,IAAI;AAAA,EACb;AACA,MAAI;AACJ,MAAI,OAAO;AACT,iBAAa,IAAI,IAAI,iBAAiB;AAAA,EACxC,WAAW,MAAM,gBAAgB;AAC/B,iBAAa,IAAI,IAAI;AAAA,EACvB,OAAO;AACL,iBAAa,IAAI,IAAI,kBAAkB;AAAA,EACzC;AAEA,MAAI,aAAa,GAAG;AAClB,iBAAa;AAAA,EACf;AAEA,MAAI,SAAS,MAAM,mBAAmB,aAAa,KAAK;AACtD,iBAAa;AAAA,EACf;AACA,MAAI,aAAa,MAAM;AACrB,iBAAa;AAAA,EACf;AACA,SAAO,OAAO,WAAW,QAAQ,CAAC,CAAC;AACrC;AACA,SAAS,SAAS,KAAK,GAAG,OAAO;AAC/B,MAAI;AACJ,MAAI,OAAO;AACT,YAAQ,IAAI,IAAI,kBAAkB;AAAA,EACpC,OAAO;AACL,YAAQ,IAAI,IAAI,kBAAkB;AAAA,EACpC;AACA,MAAI,QAAQ,GAAG;AACb,YAAQ;AAAA,EACV;AACA,SAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAChC;AACA,SAAS,SAAS,OAAO,OAAO,CAAC,GAAG;AAClC,QAAM,WAAW,CAAC;AAClB,QAAM,SAAS,WAAW,KAAK;AAC/B,WAAS,IAAI,iBAAiB,IAAI,GAAG,KAAK,GAAG;AAC3C,UAAM,MAAM,MAAM,MAAM;AACxB,UAAM,cAAc,MAAM,WAAW;AAAA,MACnC,GAAG,OAAO,KAAK,GAAG,IAAI;AAAA,MACtB,GAAG,cAAc,KAAK,GAAG,IAAI;AAAA,MAC7B,GAAG,SAAS,KAAK,GAAG,IAAI;AAAA,IAC1B,CAAC,CAAC;AACF,aAAS,KAAK,WAAW;AAAA,EAC3B;AACA,WAAS,KAAK,MAAM,MAAM,CAAC;AAC3B,WAAS,IAAI,GAAG,KAAK,gBAAgB,KAAK,GAAG;AAC3C,UAAM,MAAM,MAAM,MAAM;AACxB,UAAM,cAAc,MAAM,WAAW;AAAA,MACnC,GAAG,OAAO,KAAK,CAAC;AAAA,MAChB,GAAG,cAAc,KAAK,CAAC;AAAA,MACvB,GAAG,SAAS,KAAK,CAAC;AAAA,IACpB,CAAC,CAAC;AACF,aAAS,KAAK,WAAW;AAAA,EAC3B;AAEA,MAAI,KAAK,UAAU,QAAQ;AACzB,WAAO,aAAa,IAAI,CAAC;AAAA,MACvB;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,kBAAkB,MAAM,IAAI,WAAW,KAAK,mBAAmB,SAAS,GAAG,WAAW,SAAS,KAAK,CAAC,GAAG,UAAU,GAAG,CAAC;AAC5H,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,SAAO;AACT;;;ACtKA,IAAM,YAAY,IAAI,eAAe,WAAW;AAehD,IAAM,mBAAmB,QAAQ,KAAK,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC;AAC5D,SAAS,SAAS,iBAAiB,OAAO;AACxC,QAAM,YAAY,CAAC;AACnB,QAAM,cAAc,CAAC,OAAO,YAAY;AACtC,QAAI,QAAQ,MAAM,MAAM;AACxB,YAAQ,UAAU,KAAK,KAAK;AAC5B,WAAO,MAAM,YAAY;AAAA,EAC3B;AACA,QAAM,YAAY,CAAC,UAAU,SAAS;AACpC,UAAM,YAAY,IAAI,UAAU,QAAQ;AACxC,UAAM,gBAAgB,SAAS,UAAU,YAAY,CAAC;AACtD,cAAU,GAAG,IAAI,QAAQ,IAAI,YAAY,SAAS;AAClD,cAAU,GAAG,IAAI,iBAAiB,IAAI,cAAc,CAAC;AACrD,cAAU,GAAG,IAAI,cAAc,IAAI,cAAc,CAAC;AAClD,cAAU,GAAG,IAAI,eAAe,IAAI,cAAc,CAAC;AACnD,cAAU,GAAG,IAAI,gBAAgB,IAAI,UAAU,MAAM,EAAE,SAAS,GAAG,EAAE,YAAY;AACjF,cAAU,GAAG,IAAI,sBAAsB,IAAI,cAAc,CAAC;AAC1D,cAAU,GAAG,IAAI,0BAA0B,IAAI,cAAc,CAAC;AAAA,EAChE;AAEA,MAAI,MAAM,cAAc;AACtB,cAAU,MAAM,cAAc,SAAS;AACvC,UAAM,eAAe,IAAI,UAAU,MAAM,YAAY;AACrD,UAAM,gBAAgB,SAAS,aAAa,YAAY,CAAC;AAEzD,kBAAc,QAAQ,CAAC,OAAO,UAAU;AACtC,gBAAU,WAAW,QAAQ,CAAC,EAAE,IAAI;AAAA,IACtC,CAAC;AAED,cAAU,+BAA+B,IAAI,YAAY,cAAc,OAAK,EAAE,QAAQ,EAAE,CAAC;AACzF,cAAU,+BAA+B,IAAI,YAAY,cAAc,OAAK,EAAE,QAAQ,EAAE,CAAC;AACzF,cAAU,+BAA+B,IAAI,YAAY,cAAc,OAAK,EAAE,KAAK,EAAE,CAAC;AACtF,cAAU,+BAA+B,IAAI,YAAY,cAAc,OAAK,EAAE,KAAK,EAAE,CAAC;AACtF,cAAU,+BAA+B,IAAI,YAAY,cAAc,OAAK,EAAE,SAAS,EAAE,SAAS,IAAI,IAAI,CAAC;AAC3G,UAAM,qBAAqB,IAAI,UAAU,cAAc,CAAC,CAAC;AACzD,cAAU,sCAAsC,IAAI,YAAY,oBAAoB,OAAK,EAAE,SAAS,EAAE,SAAS,IAAI,GAAG,CAAC;AACvH,cAAU,sCAAsC,IAAI,YAAY,oBAAoB,OAAK,EAAE,OAAO,CAAC,CAAC;AAAA,EACtG;AAEA,MAAI,MAAM,cAAc;AACtB,cAAU,MAAM,cAAc,SAAS;AAAA,EACzC;AAEA,MAAI,MAAM,cAAc;AACtB,cAAU,MAAM,cAAc,SAAS;AAAA,EACzC;AAEA,MAAI,MAAM,YAAY;AACpB,cAAU,MAAM,YAAY,OAAO;AAAA,EACrC;AAEA,MAAI,MAAM,WAAW;AACnB,cAAU,MAAM,WAAW,MAAM;AAAA,EACnC;AAEA,QAAM,UAAU,OAAO,KAAK,SAAS,EAAE,IAAI,SAAO,KAAK,eAAe,IAAI,GAAG,KAAK,UAAU,GAAG,CAAC,GAAG;AACnG,SAAO;AAAA;AAAA,MAEH,QAAQ,KAAK,IAAI,CAAC;AAAA;AAAA,IAEpB,KAAK;AACT;AACA,SAAS,cAAc,iBAAiB,OAAO,UAAU;AACvD,QAAM,QAAQ,SAAS,iBAAiB,KAAK;AAC7C,MAAI,UAAU,GAAG;AACf,cAAU,OAAO,GAAG,gBAAgB,kBAAkB;AAAA,MACpD;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,SAAK,uEAAuE;AAAA,EAC9E;AACF;AAMA,IAAM,YAAY,SAAU,OAAO;AACjC,SAAO,UAAU;AACnB;AACA,IAAM,mBAAmB;AACzB,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EACpB,iBAAiB,IAAI,QAAQ;AAAA;AAAA,EAE7B,SAAS,OAAO,WAAW;AAAA,IACzB,UAAU;AAAA,EACZ,CAAC,KAAK,CAAC;AAAA,EACP,WAAW,OAAO,WAAW;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc;AACZ,QAAI,KAAK,OAAO,OAAO;AAErB,oBAAc,KAAK,UAAU,EAAE,WAAW,aAAa,kBAAkB,KAAK,OAAO,OAAO,KAAK,QAAQ;AAAA,IAC3G;AAAA,EACF;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA,EACA,iCAAiC,eAAe;AAC9C,WAAO,KAAK,eAAe,KAAK,OAAO,OAAK,MAAM,aAAa,GAAG,IAAI,MAAM,MAAS,CAAC;AAAA,EACxF;AAAA,EACA,IAAI,eAAe,OAAO;AACxB,SAAK,OAAO,aAAa,IAAI,kCACxB,KAAK,OAAO,aAAa,IACzB;AAEL,QAAI,kBAAkB,WAAW,KAAK,OAAO,OAAO;AAClD,oBAAc,KAAK,UAAU,EAAE,WAAW,aAAa,kBAAkB,KAAK,OAAO,OAAO,KAAK,QAAQ;AAAA,IAC3G;AACA,SAAK,eAAe,KAAK,aAAa;AAAA,EACxC;AASF;AARE,cAlCI,kBAkCG,QAAO,SAAS,wBAAwB,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,kBAAiB;AACpD;AACA,cArCI,kBAqCG,SAA0B,mBAAmB;AAAA,EAClD,OAAO;AAAA,EACP,SAAS,iBAAgB;AAAA,EACzB,YAAY;AACd,CAAC;AAzCH,IAAM,kBAAN;AAAA,CA2CC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAaH,SAAS,aAAa;AACpB,SAAO,SAAU,QAAQ,SAAS;AAChC,YAAQ,eAAe,WAAY;AACjC,YAAM,kBAAkB,OAAO,eAAe;AAC9C,YAAM,gBAAgB,KAAK,QAAQ,IAAI;AACvC,UAAI;AACJ,UAAI,iBAAiB;AACrB,aAAO,eAAe,MAAM,QAAQ,MAAM;AAAA,QACxC,KAAK,MAAM;AACT,gBAAM,cAAc,gBAAgB,sBAAsB,KAAK,eAAe,CAAC,IAAI,QAAQ,IAAI;AAC/F,cAAI,gBAAgB;AAClB,mBAAO;AAAA,UACT;AACA,cAAI,UAAU,WAAW,GAAG;AAC1B,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,KAAK,cAAY;AAEf,2BAAiB,UAAU,QAAQ;AACnC,kBAAQ;AAAA,QACV;AAAA,QACA,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;", "names": ["TinyColor"]}