import * as path from 'path';
import * as fse from 'fs-extra';
import * as IPackage from './package.type';

import { DataAccess } from '@server/data/types';
import { FormPackageStrategy } from './form.package';
import { ExternalFormPackageStrategy } from './external.package';
import { ExamPackageStrategy } from './exam.package';
import { PackageFileManager } from '../file/package-file.manager';
import { PackageType } from './package.type';
import { MZip, ZipEntry, ZipReader } from '@share-types/addon.types';
import { getAddon } from '@server/utils/addon';

interface PackageStat {
  form: number;
  password: number;
}
export interface IPackageStrategy {
  handlePackage(dataPackage: DataPackage): Promise<PackageStat>;
}

export interface BaseDir {
  package: string;
  form: string;
}

export async function readZipFile<T>(zip: ZipReader, file_name: string) {
  try {
    const text = await zip.read(file_name);
    return JSON.parse(text) as T;
  } catch (error: any) {
    console.error('Package: read zip file error: ', error);
    throw new PackageError(
      'Package: read zip file error: ' + error.message,
      PackageErrorCode.READ_ERROR,
    );
  }
}

export function getZipFileList(zip: ZipReader) {
  const count = zip.count;
  const fileList: ZipEntry[] = [];
  for (let i = 0; i < count; i++) {
    const zip_file = zip.item(i);
    if (zip_file) {
      fileList.push(zip_file);
    }
  }
  return fileList;
}

export class PackageManager {
  dataAccess: DataAccess;
  baseDir: BaseDir;
  strategies: Map<PackageType, IPackageStrategy>;

  packageFileManager: PackageFileManager;
  mzip: MZip;
  constructor(
    dataAccess: DataAccess,
    packageFileManager: PackageFileManager,
    baseDir: BaseDir,
  ) {
    this.baseDir = baseDir;
    this.dataAccess = dataAccess;
    this.packageFileManager = packageFileManager;
    this.strategies = new Map<PackageType, IPackageStrategy>([
      ['exam_forms', new FormPackageStrategy(dataAccess, this.baseDir.form)],
      ['exam_package', new ExamPackageStrategy(dataAccess, this.baseDir.form)],
      [
        'formpackages',
        new ExternalFormPackageStrategy(dataAccess, this.baseDir.form),
      ],
    ]);
    try {
      this.mzip = getAddon('mzip');
    } catch (error) {
      console.error('Package: get mzip error: ', error);
    }
  }

  async importPackage(
    schedule_id: string,
    file_path: string,
    password: string,
  ) {
    let zip: ZipReader;
    try {
      const mzip = this.mzip || getAddon('mzip');
      zip = await mzip.open(file_path, password);
    } catch (error: any) {
      console.error('Package open error: ', error);
      throw new PackageError(
        'Package: open error' + error.message,
        PackageErrorCode.UNZIP_ERROR,
      );
    }
    const data_package = await this.validatePackage(
      file_path,
      password,
      schedule_id,
      zip,
    ).catch((error) => {
      console.error('Package validate error: ', error);
      if (error instanceof PackageError) {
        throw error;
      } else {
        throw new PackageError(
          'Package: validate error: ' + error.message,
          PackageErrorCode.INVALID_PACKAGE_TYPE,
        );
      }
    });
    if (!data_package) {
      return;
    }
    const stat = await data_package.handlePackage().finally(() => {
      data_package.zip.close();
    });
    const name = data_package.savePackage();
    this.packageFileManager.create(name, {
      schedule_id: data_package.schedule_id,
      type: data_package.type,
    });
    return stat;
  }

  async validatePackage(
    file_path: string,
    password: string,
    schedule_id: string,
    zip: ZipReader,
  ) {
    const manifest = await readZipFile<IPackage.Manifest>(zip, 'manifest.json');

    const type = manifest?.type;
    const strategy = this.strategies.get(type);
    if (!strategy) {
      throw new PackageError(
        'Package: Invalid package type: ' + type,
        PackageErrorCode.INVALID_PACKAGE_TYPE,
      );
    }

    if (type === 'formpackages') {
      const forms = manifest.forms;
      if (!forms) {
        throw new PackageError(
          'Package: form not found',
          PackageErrorCode.DATA_ERROR,
        );
      }
      const subjects =
        await this.dataAccess.subject.getScheduleSubjects(schedule_id);
      const formNotMatch = forms.every(
        (form) => !subjects.find((s) => s.code === form.subject_code),
      );
      if (formNotMatch) {
        throw new PackageError(
          'Package: form not match subject',
          PackageErrorCode.NOT_MATCH_ERROR,
        );
      }
    } else {
      const forms = await readZipFile<IPackage.Form[]>(zip, 'form.json');
      const form_schedule_id = forms[0].schedule_id;
      if (schedule_id !== form_schedule_id) {
        throw new PackageError(
          'Package: schedule_id not match: ' +
            schedule_id +
            ' <=> ' +
            form_schedule_id,
          PackageErrorCode.NOT_MATCH_ERROR,
        );
      }
    }

    return new DataPackage(
      file_path,
      password,
      strategy,
      zip,
      schedule_id,
      this.baseDir.package,
      type,
    );
  }
}

export class DataPackage {
  file_path: string;
  password: string;
  strategy: IPackageStrategy;
  type: PackageType;
  schedule_id: string;
  zip: ZipReader;
  packageBasePath: string;
  constructor(
    path: string,
    password: string,
    strategy: IPackageStrategy,
    zip: ZipReader,
    schedule_id: string,
    packageBasePath: string,
    type: PackageType,
  ) {
    this.file_path = path;
    this.password = password;
    this.strategy = strategy;
    this.zip = zip;
    this.schedule_id = schedule_id;
    this.type = type;
    this.packageBasePath = packageBasePath;
  }

  async handlePackage() {
    return this.strategy.handlePackage(this);
  }

  savePackage() {
    const name = path.basename(this.file_path);
    const target = path.join(this.packageBasePath, this.schedule_id, name);
    try {
      fse.copySync(this.file_path, target);
      return name;
    } catch (error: any) {
      throw new PackageError(
        'Package: save package error: ' + error.message,
        PackageErrorCode.SAVE_ERROR,
      );
    }
  }
}

export enum PackageErrorCode {
  INVALID_PACKAGE_TYPE = 1001,
  UNZIP_ERROR = 1002,
  DATA_ERROR = 1003,
  NOT_MATCH_ERROR = 1004,
  SAVE_ERROR = 1005,
  READ_ERROR = 1006,
}
export class PackageError extends Error {
  name = 'PackageError';
  code: PackageErrorCode;
  constructor(message: string, code: PackageErrorCode) {
    super(message);
    this.code = code;
  }

  toJSON() {
    return {
      name: this.name,
      msg: this.message,
      code: this.code,
    };
  }
}
