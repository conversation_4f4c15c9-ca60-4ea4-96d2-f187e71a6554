import { getUserConfig } from '@server/config/env';
import { ICenterInfo } from '@services/center.service';
import { DbConnection } from './types';
import globalWriteQueue from '../utils/writeQueue';

interface IProjectData {
  id: string;
  name: string;
  start: string;
  end: string;
  test: 0 | 1;
}
export interface IProjectDataAccess {
  getProjects(): IProjectData[];
  create(project: IProjectData): Promise<void>;
  delete(project_id: string): Promise<void>;
}

export class ProjectDataAccess implements IProjectDataAccess {
  db: DbConnection;
  projects: IProjectData[];
  formal_project: IProjectData;
  test_project: IProjectData | null;
  centerInfo: ICenterInfo;

  constructor(db: DbConnection) {
    this.db = db;
    this.projects = [] as IProjectData[];
  }

  getCenterInfo(): ICenterInfo {
    return this.centerInfo;
  }

  async loadProjects() {
    this.projects = await this.db.project.findAll({ raw: true });
  }

  getProjects() {
    const center = getUserConfig<ICenterInfo>('center');
    if (!center) {
      return [];
    }
    this.projects = [] as IProjectData[];
    this.formal_project = {
      id: center.project_id,
      name: center.project_name,
      start: center.start,
      end: center.end,
      test: 0,
    };
    this.projects.push(this.formal_project);

    if (center.test) {
      this.test_project = {
        id: center.test.project_id,
        name: center.test.name,
        start: center.test.start,
        end: center.test.end,
        test: 1,
      };
      this.projects.push(this.test_project);
    }
    return this.projects;
  }

  async getProject(project_id: string): Promise<IProjectData | null> {
    return this.db.project.findByPk(project_id);
  }

  async create(project: IProjectData): Promise<void> {
    await globalWriteQueue.enqueue(() => this.db.project.create(project));
    const p = this.projects.find((p) => p.id === project.id);
    if (!p) {
      this.projects.push(project);
    } else {
      p.name = project.name;
      p.start = project.start;
      p.end = project.end;
      p.test = project.test;
    }
  }

  async delete(project_id: string): Promise<void> {
    await globalWriteQueue.enqueue(() =>
      this.db.project.destroy({ where: { id: project_id } }),
    );
    this.projects = this.projects.filter((p) => p.id !== project_id);
  }
}
