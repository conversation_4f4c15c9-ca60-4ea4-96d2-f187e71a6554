import { Injectable } from '@angular/core';
import { NetworkStatusService } from '../network-status.service';

export interface ModelInfo {
  name: string;
  url: string;
  localPath: string;
  size: number;
  required: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class HumanModelManagerService {
  
  // Human.js 人脸检测所需的核心模型文件
  private readonly requiredModels: ModelInfo[] = [
    {
      name: 'Face Detection Model',
      url: 'https://cdn.jsdelivr.net/npm/@vladmandic/human/models/blazeface.json',
      localPath: '/assets/human-models/blazeface.json',
      size: 0.1, // MB
      required: true
    },
    {
      name: 'Face Detection Weights',
      url: 'https://cdn.jsdelivr.net/npm/@vladmandic/human/models/blazeface.bin',
      localPath: '/assets/human-models/blazeface.bin',
      size: 0.4, // MB
      required: true
    }
  ];

  private readonly optionalModels: ModelInfo[] = [
    {
      name: 'Face Mesh Model',
      url: 'https://cdn.jsdelivr.net/npm/@vladmandic/human/models/facemesh.json',
      localPath: '/assets/human-models/facemesh.json',
      size: 0.1, // MB
      required: false
    },
    {
      name: 'Face Mesh Weights',
      url: 'https://cdn.jsdelivr.net/npm/@vladmandic/human/models/facemesh.bin',
      localPath: '/assets/human-models/facemesh.bin',
      size: 2.8, // MB
      required: false
    }
  ];

  constructor(private networkStatus: NetworkStatusService) {}

  /**
   * 获取所有模型信息
   */
  getAllModels(): ModelInfo[] {
    return [...this.requiredModels, ...this.optionalModels];
  }

  /**
   * 获取必需的模型
   */
  getRequiredModels(): ModelInfo[] {
    return this.requiredModels;
  }

  /**
   * 检查本地模型是否可用
   */
  async checkLocalModelsAvailability(): Promise<{ available: boolean; missingModels: string[] }> {
    const missingModels: string[] = [];

    for (const model of this.requiredModels) {
      const isAvailable = await this.checkModelFile(model.localPath);
      if (!isAvailable) {
        missingModels.push(model.name);
      } else {
        // 进一步验证文件内容
        const isValid = await this.validateModelFile(model.localPath);
        if (!isValid) {
          missingModels.push(model.name);
        }
      }
    }

    return {
      available: missingModels.length === 0,
      missingModels
    };
  }

  /**
   * 检查单个模型文件是否存在且有效
   */
  private async checkModelFile(path: string): Promise<boolean> {
    try {
      const response = await fetch(path, { method: 'HEAD' });
      if (!response.ok) {
        return false;
      }

      // 检查文件大小，确保不是空文件或占位符
      const contentLength = response.headers.get('content-length');
      if (contentLength && parseInt(contentLength) < 100) {
        // 文件太小，可能是占位符
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 验证模型文件内容
   */
  private async validateModelFile(path: string): Promise<boolean> {
    try {
      const response = await fetch(path);
      if (!response.ok) {
        return false;
      }

      const text = await response.text();

      // 检查是否是占位符文件
      if (text.includes('"placeholder": true')) {
        return false;
      }

      // 对于 JSON 文件，验证是否是有效的 JSON
      if (path.endsWith('.json')) {
        try {
          JSON.parse(text);
          return true;
        } catch {
          return false;
        }
      }

      // 对于二进制文件，检查大小
      return text.length > 1000; // 至少 1KB
    } catch (error) {
      return false;
    }
  }

  /**
   * 下载模型到本地（开发时使用）
   */
  async downloadModels(onProgress?: (progress: number, modelName: string) => void): Promise<boolean> {
    if (!this.networkStatus.isOnline) {
      console.warn('Cannot download models: offline');
      return false;
    }

    try {
      let completedModels = 0;
      const totalModels = this.requiredModels.length;

      for (const model of this.requiredModels) {
        onProgress?.(completedModels / totalModels * 100, model.name);
        
        const success = await this.downloadSingleModel(model);
        if (!success) {
          console.error(`Failed to download model: ${model.name}`);
          return false;
        }
        
        completedModels++;
      }

      onProgress?.(100, 'Complete');
      return true;
    } catch (error) {
      console.error('Error downloading models:', error);
      return false;
    }
  }

  /**
   * 下载单个模型文件
   */
  private async downloadSingleModel(model: ModelInfo): Promise<boolean> {
    try {
      const response = await fetch(model.url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const blob = await response.blob();
      
      // 在实际应用中，这里需要将文件保存到服务器的 assets 目录
      // 这个方法主要用于开发时的模型准备
      console.log(`Downloaded ${model.name}: ${blob.size} bytes`);
      return true;
    } catch (error) {
      console.error(`Failed to download ${model.name}:`, error);
      return false;
    }
  }

  /**
   * 获取模型配置路径（本地优先）
   */
  async getModelBasePath(): Promise<string> {
    const localCheck = await this.checkLocalModelsAvailability();
    
    if (localCheck.available) {
      console.log('Using local Human.js models');
      return '/assets/human-models/';
    }
    
    if (this.networkStatus.isOnline) {
      const cdnAccess = await this.networkStatus.checkCDNAccess();
      if (cdnAccess) {
        console.log('Using CDN Human.js models');
        return 'https://cdn.jsdelivr.net/npm/@vladmandic/human/models/';
      }
    }
    
    console.warn('No model source available');
    throw new Error('No model source available (offline and no local models)');
  }

  /**
   * 生成模型下载脚本（用于部署准备）
   */
  generateDownloadScript(): string {
    const script = `#!/bin/bash
# Human.js Models Download Script
# Run this script to download required models for offline use

mkdir -p src/assets/human-models

echo "Downloading Human.js models for offline use..."

${this.requiredModels.map(model => 
  `echo "Downloading ${model.name}..."
curl -L "${model.url}" -o "src/assets/human-models/${model.url.split('/').pop()}"
`).join('\n')}

echo "Download complete!"
echo "Models saved to src/assets/human-models/"
`;

    return script;
  }

  /**
   * 获取模型总大小
   */
  getTotalModelSize(): number {
    return this.requiredModels.reduce((total, model) => total + model.size, 0);
  }
}
