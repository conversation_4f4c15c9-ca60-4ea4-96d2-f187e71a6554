import * as path from "path";
import * as fs from "fs";
import { getAddon } from "./addon";


/* eslint-disable  @typescript-eslint/no-explicit-any */

function formatParams(data: any[]) {
  const msg: any[] = [];
  data.forEach(function (obj) {
    if (obj instanceof Error) {
      obj = obj.stack || obj;
    } else if (typeof obj === "object") {
      msg.push(dump(obj));
    } else {
      msg.push(obj);
    }
  });
  return msg.join(" ");
}

function dump(o: any) {
  const cache = new Set<any>();
  return stringify(o, cache, 1);
}

function stringify(obj: any, cache: Set<any>, level: number) {
  if (typeof obj !== "object" || obj === null || obj instanceof Array) {
    return serialize(obj, cache, level);
  }

  if (cache.has(obj)) {
    return "{...}";
  }
  if (level > 2) {
    return "{ ... }";
  }

  cache.add(obj);

  return (
    "{" +
    Object.keys(obj)
      .map(function (k) {
        return typeof obj[k] === "function" ? null : '"' + k + '":' + serialize(obj[k], cache, level + 1);
      })
      .filter(function (i) {
        return i;
      }) +
    "}"
  );
}

function serialize(val: any, cache: Set<any>, level: number) {
  switch (typeof val) {
    case "string":
      return '"' + val.replace(/\\/g, "\\\\").replace('"', '\\"') + '"';
    case "number":
    case "boolean":
      return "" + val;
    case "function":
      return "<function>";
    case "object":
      if (val instanceof Date) {
        return '"' + val.toISOString() + '"';
      }
      if (val instanceof Array) {
        return (
          "[" +
          val
            .map(function (v) {
              return serialize(v, cache, level);
            })
            .join(",") +
          "]"
        );
      }
      if (val === null) {
        return "null";
      }
      return stringify(val, cache, level);
    default:
      return val;
  }
}

declare interface ILog {
  error(...params: any[]): void;
  warn(...params: any[]): void;
  info(...params: any[]): void;
  debug(...params: any[]): void;
  log(...params: any[]): void;
}

export class Logger implements ILog {
  constructor(public inner: any) {}
  error(...params: any[]): void {
    if (this.inner) {
      this.inner.error(formatParams(params));
    }
  }
  warn(...params: any[]): void {
    if (this.inner) {
      this.inner.warn(formatParams(params));
    }
  }
  info(...params: any[]): void {
    if (this.inner) {
      this.inner.info(formatParams(params));
    }
  }
  debug(...params: any[]): void {
    if (this.inner) {
      this.inner.debug(formatParams(params));
    }
  }
  log(...params: any[]): void {
    if (this.inner) {
      this.inner.info(formatParams(params));
    }
  }
}

const addon = getAddon("osext");

export async function createRotatingLoggerAsync(name: string, filepath: string, maxFileSize: number, maxFiles: number) {
  const dirname = path.dirname(filepath);
  await fs.promises.mkdir(dirname, { recursive: true });
  return createRotatingLogger(name, filepath, maxFileSize, maxFiles);
}

export function createRotatingLogger(name: string, filepath: string, maxFileSize: number, maxFiles: number) {
  const logger = new addon.Logger("rotating", name, filepath, maxFileSize, maxFiles);
  logger.setPattern("[%Y-%m-%dT%H:%M:%S.%e%z] [%l] %v");
  return logger;
}

const log = new Logger(console);

export function initLog(name: string, filepath: string, maxFileSize: number, maxFiles: number) {
  log.inner = createRotatingLogger(name, filepath, maxFileSize, maxFiles);
}

function closeAllLogs() {
  log.inner = null;
  addon.Logger.shutdown();
}

if (process.versions["electron"]) {
  // eslint:disable-next-line
  const electron = require("electron");
  if (electron.app) {
    electron.app.once("quit", () => {
      closeAllLogs();
    });
  }
}

export { log, closeAllLogs };
