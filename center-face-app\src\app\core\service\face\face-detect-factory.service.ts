import { Injectable } from '@angular/core';
import { FaceDetectBaseService, FaceDetectEngine } from './face-detect-base.service';
import { FaceDetectService } from './face-detect.service';
import { HumanFaceDetectService } from './human-face-detect.service';
import { SettingsService } from '../settings.service';

@Injectable({
  providedIn: 'root',
})
export class FaceDetectFactoryService {
  private currentEngine: FaceDetectEngine | null = null;
  private currentService: FaceDetectBaseService | null = null;

  constructor(
    private settingsService: SettingsService,
    private mediapipeService: FaceDetectService,
    private humanService: HumanFaceDetectService
  ) {}

  /**
   * 获取当前配置的人脸检测服务
   */
  getCurrentService(): FaceDetectBaseService {
    const configuredEngine = this.settingsService.getFaceEngine();
    
    // 如果引擎没有变化，返回当前服务
    if (this.currentEngine === configuredEngine && this.currentService) {
      return this.currentService;
    }

    // 切换引擎
    this.switchEngine(configuredEngine);
    return this.currentService!;
  }

  /**
   * 切换人脸检测引擎
   */
  async switchEngine(engine: FaceDetectEngine): Promise<FaceDetectBaseService> {
    // 如果已经是当前引擎，直接返回
    if (this.currentEngine === engine && this.currentService) {
      return this.currentService;
    }

    // 清理当前服务
    if (this.currentService) {
      this.currentService.destroy();
    }

    // 切换到新引擎
    switch (engine) {
      case 'human':
        this.currentService = this.humanService;
        break;
      case 'mediapipe':
      default:
        this.currentService = this.mediapipeService;
        break;
    }

    // 初始化新服务
    await this.currentService.initialize();
    this.currentEngine = engine;

    // 保存设置
    this.settingsService.setFaceEngine(engine);

    console.log(`Switched to ${engine} face detection engine`);
    return this.currentService;
  }

  /**
   * 获取当前引擎名称
   */
  getCurrentEngine(): FaceDetectEngine {
    return this.currentEngine || this.settingsService.getFaceEngine();
  }

  /**
   * 获取所有可用的引擎
   */
  getAvailableEngines(): { value: FaceDetectEngine; label: string }[] {
    return [
      { value: 'mediapipe', label: 'MediaPipe (OpenCV)' },
      { value: 'human', label: 'Human.js' }
    ];
  }

  /**
   * 检查指定引擎是否可用
   */
  async isEngineAvailable(engine: FaceDetectEngine): Promise<boolean> {
    try {
      switch (engine) {
        case 'human':
          return this.humanService.isReady() || await this.checkHumanAvailability();
        case 'mediapipe':
          return true; // MediaPipe 总是可用的
        default:
          return false;
      }
    } catch (error) {
      console.error(`Engine ${engine} availability check failed:`, error);
      return false;
    }
  }

  private async checkHumanAvailability(): Promise<boolean> {
    try {
      await this.humanService.initialize();
      return this.humanService.isReady();
    } catch (error) {
      console.error('Human engine initialization failed:', error);
      return false;
    }
  }

  /**
   * 销毁所有服务
   */
  destroy(): void {
    if (this.currentService) {
      this.currentService.destroy();
      this.currentService = null;
      this.currentEngine = null;
    }
  }
}
