import { Injectable } from '@angular/core';
import { FaceDetectBaseService, FaceDetectEngine } from './face-detect-base.service';
import { FaceDetectService } from './face-detect.service';
import { HumanFaceDetectService, HumanInitializationResult } from './human-face-detect.service';
import { SettingsService } from '../settings.service';
import { NetworkStatusService } from '../network-status.service';

@Injectable({
  providedIn: 'root',
})
export class FaceDetectFactoryService {
  private currentEngine: FaceDetectEngine | null = null;
  private currentService: FaceDetectBaseService | null = null;

  constructor(
    private settingsService: SettingsService,
    private mediapipeService: FaceDetectService,
    private humanService: HumanFaceDetectService,
    private networkStatus: NetworkStatusService
  ) {}

  /**
   * 获取当前配置的人脸检测服务
   */
  getCurrentService(): FaceDetectBaseService {
    const configuredEngine = this.settingsService.getFaceEngine();
    
    // 如果引擎没有变化，返回当前服务
    if (this.currentEngine === configuredEngine && this.currentService) {
      return this.currentService;
    }

    // 切换引擎
    this.switchEngine(configuredEngine);
    return this.currentService!;
  }

  /**
   * 切换人脸检测引擎（支持智能降级）
   */
  async switchEngine(engine: FaceDetectEngine): Promise<FaceDetectBaseService> {
    // 如果已经是当前引擎，直接返回
    if (this.currentEngine === engine && this.currentService) {
      return this.currentService;
    }

    // 清理当前服务
    if (this.currentService) {
      this.currentService.destroy();
    }

    let targetService: FaceDetectBaseService;
    let actualEngine = engine;

    // 选择目标服务
    if (engine === 'human') {
      targetService = this.humanService;

      // 尝试初始化 Human.js 引擎
      try {
        const result = await targetService.initialize() as HumanInitializationResult;
        if (!result.success) {
          console.warn('Human.js initialization failed, falling back to MediaPipe:', result.error);
          targetService = this.mediapipeService;
          actualEngine = 'mediapipe';
          await targetService.initialize();
        }
      } catch (error) {
        console.warn('Human.js initialization error, falling back to MediaPipe:', error);
        targetService = this.mediapipeService;
        actualEngine = 'mediapipe';
        await targetService.initialize();
      }
    } else {
      // MediaPipe 引擎
      targetService = this.mediapipeService;
      await targetService.initialize();
    }

    this.currentService = targetService;
    this.currentEngine = actualEngine;

    // 保存实际使用的引擎设置
    this.settingsService.setFaceEngine(actualEngine);

    console.log(`Switched to ${actualEngine} face detection engine`);
    return this.currentService;
  }

  /**
   * 获取当前引擎名称
   */
  getCurrentEngine(): FaceDetectEngine {
    return this.currentEngine || this.settingsService.getFaceEngine();
  }

  /**
   * 获取所有可用的引擎（包含离线支持信息）
   */
  async getAvailableEngines(): Promise<{
    value: FaceDetectEngine;
    label: string;
    supportsOffline: boolean;
    status: string;
  }[]> {
    const humanSupportsOffline = await this.humanService.supportsOfflineMode();

    return [
      {
        value: 'mediapipe',
        label: 'MediaPipe (OpenCV)',
        supportsOffline: true,
        status: 'Always available'
      },
      {
        value: 'human',
        label: 'Human.js',
        supportsOffline: humanSupportsOffline,
        status: humanSupportsOffline ? 'Offline ready' :
                this.networkStatus.isOnline ? 'Online only' : 'Unavailable'
      }
    ];
  }

  /**
   * 获取当前引擎的详细状态
   */
  async getCurrentEngineStatus(): Promise<{
    engine: FaceDetectEngine;
    ready: boolean;
    mode: string;
    supportsOffline: boolean;
    networkStatus: boolean;
  }> {
    const currentEngine = this.getCurrentEngine();
    const isReady = this.currentService?.isReady() || false;

    let mode = 'unknown';
    let supportsOffline = false;

    if (currentEngine === 'human' && this.currentService) {
      const humanService = this.currentService as HumanFaceDetectService;
      mode = humanService.getInitializationMode();
      supportsOffline = await humanService.supportsOfflineMode();
    } else if (currentEngine === 'mediapipe') {
      mode = 'local';
      supportsOffline = true;
    }

    return {
      engine: currentEngine,
      ready: isReady,
      mode,
      supportsOffline,
      networkStatus: this.networkStatus.isOnline
    };
  }

  /**
   * 检查指定引擎是否可用
   */
  async isEngineAvailable(engine: FaceDetectEngine): Promise<boolean> {
    try {
      switch (engine) {
        case 'human':
          return this.humanService.isReady() || await this.checkHumanAvailability();
        case 'mediapipe':
          return true; // MediaPipe 总是可用的
        default:
          return false;
      }
    } catch (error) {
      console.error(`Engine ${engine} availability check failed:`, error);
      return false;
    }
  }

  private async checkHumanAvailability(): Promise<boolean> {
    try {
      await this.humanService.initialize();
      return this.humanService.isReady();
    } catch (error) {
      console.error('Human engine initialization failed:', error);
      return false;
    }
  }

  /**
   * 销毁所有服务
   */
  destroy(): void {
    if (this.currentService) {
      this.currentService.destroy();
      this.currentService = null;
      this.currentEngine = null;
    }
  }
}
