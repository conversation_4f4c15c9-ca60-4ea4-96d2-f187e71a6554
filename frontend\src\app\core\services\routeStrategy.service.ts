import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  DetachedRouteHandle,
  RouteReuseStrategy,
} from '@angular/router';
import { Subject } from 'rxjs/internal/Subject';

export class RouteMsg {
  constructor(
    public type: string,
    public url: string,
    public route: ActivatedRouteSnapshot
  ) {}
}

@Injectable({
  providedIn: 'root',
})
export class RouteStrategyService implements RouteReuseStrategy {
  public static handlers: { [key: string]: DetachedRouteHandle } = {};
  private static routeText$ = new Subject<RouteMsg>();
  public static routeReuseEvent =
    RouteStrategyService.routeText$.asObservable();
  constructor() {}

  // 判断路由是否允许复用
  shouldDetach(route: ActivatedRouteSnapshot): boolean {
    if (route.data['keep']) {
      RouteStrategyService.routeText$.next(
        new RouteMsg('detach', this.getUrl(route), route)
      );
    }
    return route.data['keep'];
  }

  // 判断是否允许还原路由对象及其子对象
  shouldAttach(route: ActivatedRouteSnapshot): boolean {
    console.debug('shouleAttach', route as any);
    const is_attach = !!RouteStrategyService.handlers[this.getPath(route)];
    if (is_attach) {
      RouteStrategyService.routeText$.next(
        new RouteMsg('attach', this.getUrl(route), route)
      );
    }
    return is_attach;
  }

  // 获取实例对象，决定是否实例化还是使用缓存
  retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle | null {
    return RouteStrategyService.handlers[this.getPath(route)] || null;
  }

  // 判断是否复用路由
  shouldReuseRoute(
    future: ActivatedRouteSnapshot,
    curr: ActivatedRouteSnapshot
  ): boolean {
    return (
      future.routeConfig === curr.routeConfig &&
      JSON.stringify(future.params) === JSON.stringify(curr.params)
    );
  }

  // 存储路由快照&组件当前实例对象
  store(route: ActivatedRouteSnapshot, handle: DetachedRouteHandle): void {
    // if (Object.keys(RouteStrategyService.handlers).length >= 1) {
    //   console.log("handlers", RouteStrategyService.handlers);
    //   RouteStrategyService.handlers = {};
    // }
    RouteStrategyService.handlers[this.getPath(route)] = handle;
  }

  private getPath(route: ActivatedRouteSnapshot): string {
    if (route.routeConfig !== null && route.routeConfig.path !== null) {
      return route.routeConfig.path == undefined ? '' : route.routeConfig.path;
    }
    return '';
  }

  getTruthRoute(route: ActivatedRouteSnapshot): ActivatedRouteSnapshot {
    let next = route;
    while (next.firstChild) next = next.firstChild;
    return next;
  }
  /**
   * 根据快照获取URL地址
   */
  getUrl(route: ActivatedRouteSnapshot): string {
    let next = this.getTruthRoute(route);
    const segments: string[] = [];
    while (next) {
      segments.push(next.url.join('/'));
      next = next.parent!;
    }
    const url = `/${segments
      .filter((i) => i)
      .reverse()
      .join('/')}`;
    return url;
  }
}
