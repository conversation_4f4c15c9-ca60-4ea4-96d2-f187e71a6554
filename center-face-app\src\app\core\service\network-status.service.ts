import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, fromEvent, merge } from 'rxjs';
import { map, startWith } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class NetworkStatusService {
  private isOnlineSubject = new BehaviorSubject<boolean>(navigator.onLine);
  
  public isOnline$: Observable<boolean> = merge(
    fromEvent(window, 'online').pipe(map(() => true)),
    fromEvent(window, 'offline').pipe(map(() => false))
  ).pipe(
    startWith(navigator.onLine)
  );

  constructor() {
    this.isOnline$.subscribe(status => {
      this.isOnlineSubject.next(status);
      console.log('Network status changed:', status ? 'online' : 'offline');
    });
  }

  /**
   * 获取当前网络状态
   */
  get isOnline(): boolean {
    return this.isOnlineSubject.value;
  }

  /**
   * 检测网络连接性（通过尝试加载资源）
   */
  async checkConnectivity(url: string = '/favicon.ico', timeout: number = 3000): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      const response = await fetch(url, {
        method: 'HEAD',
        cache: 'no-cache',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      console.warn('Connectivity check failed:', error);
      return false;
    }
  }

  /**
   * 检测是否能访问外部CDN
   */
  async checkCDNAccess(cdnUrl: string = 'https://cdn.jsdelivr.net', timeout: number = 5000): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      const response = await fetch(cdnUrl, {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      return true;
    } catch (error) {
      console.warn('CDN access check failed:', error);
      return false;
    }
  }
}
