<div class="card">
    <div class="header">
        <div class="title">
            <span class="dot" [ngClass]="{'bg-green': roomData.is_online, 'bg-red': !roomData.is_online}"></span>
            {{roomData.room_name}}
        </div>
        <div class="more">
            <span nz-icon nzType="ellipsis" nzTheme="outline" nz-dropdown [nzDropdownMenu]="menu" nzTrigger="hover"
                [nzPlacement]="'bottomRight'"></span>
            <nz-dropdown-menu #menu="nzDropdownMenu">
                <ul nz-menu>
                    <li nz-menu-item (click)="exportResult()">导出考生答题数据</li>
                    <li nz-menu-item (click)="checkRoomPkg()">导出考场单机包</li>
                </ul>
            </nz-dropdown-menu>
        </div>
    </div>
    <div class="content">
        <div class="info">
            <div class="row">
                <span>考试科目：{{roomData.subjects.toString()}}</span>
            </div>
            <div class="row">
                <span class="item">试卷：{{roomData.form_status ? '已同步': '未同步'}}</span>
                <span class="item">密码：{{roomData.password_status ? '已同步': '未同步'}}</span>
            </div>
            <div class="row">
                <span class="item">总人数：{{entryCount.total}}</span>
                <span class="item">参考：{{entryCount.test}}</span>
                <span class="item">缺考：{{entryCount.absent}}</span>
            </div>
            <div class="row">
                <span class="item">已完成：{{roomData.entry_status.finished}}</span>
                <span class="item">违纪：{{breakRuleCount}}</span>
            </div>
        </div>
        <div class="chart">
            <app-chart-pie [data]="entryCount" [height]="150"></app-chart-pie>
        </div>
    </div>
    <nz-divider></nz-divider>
    <div class="footer">
        <nz-steps [nzCurrent]="currentStatus" nzSize="small" [nzProgressDot]="progressTemplate">
            <nz-step nzTitle="进场中"></nz-step>
            <nz-step nzTitle="考试中"></nz-step>
            <nz-step nzTitle="考试结束"></nz-step>
            <nz-step nzTitle="上传结果"></nz-step>
        </nz-steps>
        <ng-template #progressTemplate let-dot let-status="status" let-index="index">
            <ng-template [ngTemplateOutlet]="dot"></ng-template>

            <!-- <span nz-popover nzPopoverContent="steps {{ index }} status: {{ status }}" style="margin-left: -100%;">
              <ng-template [ngTemplateOutlet]="dot"></ng-template>
            </span> -->
        </ng-template>
    </div>
</div>

<ng-template #tplTitle>
    <span>导出考场数据包</span>
</ng-template>
<ng-template #tplContent let-params>
    <div class="body">
        <p class="explain">请选择导出需要的考场数据包</p>
        <nz-table #basicTable [nzData]="roomPkgList"
        [nzShowPagination]="false"
        [nzFrontPagination]="false"
        nzSize="small"
        [nzScroll]="{ y: '500px' }"
        >
          <thead>
            <tr>
              <th nzWidth="50px">序号</th>
              <th>文件名</th>
              <th nzWidth="200px">创建时间</th>
              <th nzWidth="100px">文件大小</th>
              <th nzWidth="100px">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of basicTable.data; let i = index">
              <td>{{i + 1}}</td>
              <td>{{data.name}}</td>
              <td>{{data.created_at | customDate: "date"}}</td>
              <td>{{data.size}}</td>
              <td>
                <button nz-button nzType="link" (click)="exportRoomPkg(data.id)">导出</button>
            </tr>
          </tbody>
        </nz-table>
      </div>
</ng-template>
<ng-template #tplFooter let-ref="modalRef">
    <button nz-button (click)="ref.destroy()">确定</button>
</ng-template>