import { CommonModule } from '@angular/common';
import {
  Component,
  Output,
  type OnInit,
  EventEmitter,
  Input,
} from '@angular/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
    selector: 'app-expand',
    imports: [CommonModule, NzButtonModule, NzIconModule],
    template: ` @if (expanded) {
    <a nz-button nzType="link" (click)="expand(false)">
      收起
      <span nz-icon nzType="up-square" nzTheme="fill"></span>
    </a>
    } @else {
    <a nz-button nzType="link" (click)="expand(true)">
      展开
      <span nz-icon nzType="down-square" nzTheme="fill"></span>
    </a>
    }`,
    styles: [
        `
      :host {
        display: inline-block;
        min-width: 82px;
      }
    `,
    ]
})
export class ExpandComponent implements OnInit {
  @Input({ required: true }) public expanded = false;
  @Output() expandedChange = new EventEmitter<boolean>();
  ngOnInit(): void {}

  expand(expand: boolean) {
    this.expandedChange.emit(expand);
  }
}
