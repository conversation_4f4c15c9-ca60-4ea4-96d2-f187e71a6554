import { ApplicationConfig, provideZoneChangeDetection, importProvidersFrom, isDevMode } from '@angular/core';
import { provideRouter, withComponentInputBinding } from '@angular/router';

import { routes } from './app.routes';
import { zh_CN, provideNzI18n } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import zh from '@angular/common/locales/zh';
import { FormsModule } from '@angular/forms';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { provideNzIcons } from 'ng-zorro-antd/icon';
import { SettingOutline, LeftOutline, ExclamationOutline } from '@ant-design/icons-angular/icons';
import { apiPrefixInterceptor, errorInterceptor } from './core/http/http.interceptor';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { ClockCircleOutline, PieChartOutline, UserOutline, CheckOutline } from '@ant-design/icons-angular/icons';
import { provideServiceWorker } from '@angular/service-worker';

const icons = [SettingOutline, LeftOutline, ClockCircleOutline, PieChartOutline, UserOutline, CheckOutline, ExclamationOutline];

registerLocaleData(zh);

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withComponentInputBinding()),
    provideNzI18n(zh_CN),
    importProvidersFrom(FormsModule, NzModalModule),
    provideAnimationsAsync(), 
    provideHttpClient(withInterceptors([apiPrefixInterceptor, errorInterceptor])),
    provideNzIcons(icons), provideServiceWorker('ngsw-worker.js', {
            enabled: !isDevMode(),
            registrationStrategy: 'registerWhenStable:30000'
          }),
  ]
};
