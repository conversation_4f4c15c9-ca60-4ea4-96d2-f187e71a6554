import { Sequelize, Options } from 'sequelize';
import * as path from 'path';
import * as env from '@server/config/env';
import { ICenterInfo } from '@services/center.service';
import { DbConnection } from '@data/types';

const DB_KEY = 'AtaJoyTest2018';
const db: DbConnection = {} as DbConnection;

function loadModels(sequelize: Sequelize) {
  const modelDefiners = [
    require('../model/project'),
    require('../model/schedule'),
    require('../model/session'),
    require('../model/room'),
    require('../model/subject'),
    require('../model/form'),
    require('../model/event'),
    require('../model/log'),
  ];

  for (const modelDefiner of modelDefiners) {
    const model = modelDefiner.default(sequelize);
    db[model.name] = model;
  }
}

function newDb(file: string) {
  const options: Options = {
    dialect: 'sqlite',
    storage: file,
    logging: false,
  };
  const sequelize = new Sequelize(file, '', DB_KEY, options);
  return sequelize;
}

db.open = async function () {
  const center = env.getUserConfig<ICenterInfo>('center');
  if (!center) {
    return false;
  }
  const db_name = `2025_${center.center_id}_${center.project_id}.db`;
  const db_path = path.join(env.data_path, db_name);
  if (db.sequelize) {
    await db.sequelize.close();
  }
  db.db_name = db_name;
  console.log('DB: open db', db_name);
  const sequelize = newDb(db_path);
  db.sequelize = sequelize;
  loadModels(sequelize);

  const integrity_check = await sequelize
    .query('PRAGMA integrity_check')
    .then(([result]) => {
      return result[0] && (result[0] as any).integrity_check;
    });

  if (integrity_check !== 'ok') {
    console.log(integrity_check.substr(0, 100));
    throw new Error('integrity checking failed');
  }

  const pragmas = ['journal_mode = WAL', 'synchronous = NORMAL'];
  for (const param of pragmas) {
    await sequelize.query(`PRAGMA ${param};`);
  }
  return !!(await sequelize.sync());
};

db.close = async function () {
  if (this.sequelize) {
    await this.sequelize.query('PRAGMA wal_checkpoint;');
    await this.sequelize.close();
    return true;
  }
  return false;
};

export default db;
