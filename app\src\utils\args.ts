export function getArg(name: string, defVal?: string, argv?: string[]): string | undefined {
  argv = argv || process.argv.slice(1);
  for (let i = 0; i < argv.length; ++i) {
    const arg = argv[i];
    if (arg.startsWith("-")) {
      const m = arg.match(/--?([^=]*)(?:=(.*))?/);
      if (m && m[1] === name) {
        if (typeof m[2] !== "undefined") {
          return m[2].replace(/^"(.+(?="$))"$/, "$1");
        } else {
          return argv[i + 1] || defVal;
        }
      }
    }
  }
  return defVal;
}

export function hasSwitch(name: string, argv?: string[]): boolean {
  argv = argv || process.argv.slice(1);
  for (let i = 0; i < argv.length; ++i) {
    const arg = argv[i];
    if (arg.startsWith("-")) {
      const m = arg.match(/--?([^=]*)?/);
      if (m && m[1] === name) {
        return true;
      }
    }
  }
  return false;
}
