import { DataAccess } from '@data/types';
import { ElogType, logTypeMap } from '@share-types/center.types';

export class LogService {
  dataAccess: DataAccess;

  constructor(dataAccess: DataAccess) {
    this.dataAccess = dataAccess;
  }

  async createLog(type: ElogType, detail: string) {
    await this.dataAccess.log.create({
      type,
      content: `${logTypeMap[type]} ${detail}`,
    });
  }

  getLogs() {
    return this.dataAccess.log.getLogs();
  }
}
